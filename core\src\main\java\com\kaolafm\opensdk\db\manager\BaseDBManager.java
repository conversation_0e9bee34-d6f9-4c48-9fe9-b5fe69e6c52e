package com.kaolafm.opensdk.db.manager;

import com.kaolafm.opensdk.db.OnQueryListener;
import com.kaolafm.opensdk.db.helper.GreenDaoHelper;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.log.Logging.Log;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.AbstractDaoSession;
import org.greenrobot.greendao.async.AsyncSession;

import java.util.List;
import java.util.concurrent.Callable;

import io.reactivex.Single;
import io.reactivex.SingleObserver;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

/**
 * 将所有创建的表格相同的部分封装到这个BaseDBManager中
 *
 * <AUTHOR>
 * @date 2018/5/14
 */

public abstract class BaseDBManager<T, D extends AbstractDao<T, ?>> implements DBOperation<T> {

    public AbstractDaoSession mDaoSession;

    public AsyncSession mAsyncSession;

    public Class mEntityClass;

    public D mDao;

    private GreenDaoManager mBind;

    protected BaseDBManager() {
        mBind = GreenDaoHelper.bind(this);
    }

    public void updateTable(int oldVersion, int newVersion) {
//        mOperation.updateTable(oldVersion, newVersion);
    }
    //======================插入==========================

    /**
     * 插入单个对象
     */
    public void insert(T t) {
        try {
            runInNewThread(() -> mDao.insertOrReplace(t) >= 0, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 插入一个对象集合
     */
    public void insert(final List<T> list) {
        if (list == null || list.isEmpty()) {
            return;
        }
        try {
            runInNewThread(() -> {
                mDao.insertOrReplaceInTx(list);
                return true;
            }, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 同步插入单个对象
     */
    public boolean insertSync(T t) {
        try {
            return mDao.insertOrReplace(t) != -1;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 同步插入一个对象集合
     */
    public boolean insertSync(final List<T> list) {
        if (list == null || list.isEmpty()) {
            return false;
        }
        try {
            mDao.insertOrReplaceInTx(list);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

//======================保存==========================

    /**
     * 如果主键相同就更新，不相同就插入
     */
    public void save(T t) {
        runInNewThread(() -> {
            mDao.save(t);
            return true;
        }, null);
    }

    /**
     * 保存一个对象集合。如果主键相同就更新，不相同就插入
     */
    public void save(List<T> list) {
        runInNewThread(() -> {
            mDao.saveInTx(list);
            return true;
        }, null);
    }

    public void saveSync(List<T> list) {
        mDao.saveInTx(list);
    }

    public void saveSync( T t) {
        mDao.save(t);
    }
//======================更新==========================

    /**
     * 以对象形式进行数据修改
     * 其中必须要知道对象的主键ID
     */
    public void update(T t) {
        if (t == null) {
            return;
        }
        try {
            mAsyncSession.update(t);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 批量更新数据
     */
    public void update(final List<T> list) {
        try {
            mAsyncSession.updateInTx(mEntityClass, list);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

//======================删除==========================

    /**
     * 删除所有数据，指定类class
     */
    public void deleteAll(Class<T> clazz) {
        try {
            mAsyncSession.deleteAll(clazz);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 删除所有数据，反射获取当前泛型的class
     */
    public void deleteAll() {
        mDao.deleteAll();
    }

    /**
     * 删除某个数据
     */
    public void delete(T t) {
        try {
            if (t != null) {
                mAsyncSession.delete(t);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 批量删除数据
     */
    public void delete(final List<T> list, Class clazz) {
        if (null == list || list.isEmpty()) {
            return;
        }
        try {
            mAsyncSession.deleteInTx(clazz, list);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 批量删除数据
     */
    public void delete(final List<T> list) {
        delete(list, mEntityClass);
    }

//======================查询==========================

    /**
     * 获得某个表名
     */
    public String getTableName(Class clazz) {
        return mDaoSession.getDao(clazz).getTablename();
    }

    /**
     * 获得表名
     */
    public String getTableName() {
        return mDao.getTablename();
    }

    /**
     * 根据主键ID来查询，结果以回调方式显示
     */
    public void queryById(long id, OnQueryListener<T> listener) {
        runInNewThread(() -> {
            T t = (T) mDaoSession.getDao(mEntityClass).load(id);
            if (t == null) {
                throw new ApiException(String.format("数据库中没有查到id=%s的数据", id));
            }
            return t;
        }, listener);
    }

    /**
     * 查询某条件下的对象，结果以回调方式显示
     */
    public void queryRaw(String where, OnQueryListener<List<T>> listener, Object... params) {
        runInNewThread(() -> (List<T>) mDao.queryRawCreate(where, params), listener);
    }

    /**
     * 同步查询某条件下的对象
     */
    public List<T> queryRawSync(String where, String... params) {
        List<T> objects = null;
        try {
            objects = mDao.queryRaw(where, params);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return objects;
    }

    //-------------查询所有-----------

    /**
     * 查询所有对象
     */
    public Single<List<T>> queryAll() {
        return Single.fromCallable(() -> mDao.loadAll())
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 查询所有对象， 结果以回调方式显示。
     */
    public void queryAll(OnQueryListener<List<T>> listener) {
        try {
            runInNewThread(() -> mDao.loadAll(), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 查询数据库数量
     */
    public void queryAllCount(OnQueryListener<Long> listener) {
        runInNewThread(() -> mDao.queryBuilder().buildCount().count(), listener);
    }

    /**
     * 同步查询所有对象
     */
    public List<T> queryAllSync() {
        List<T> list = null;
        try {
            list = mDao.loadAll();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

    /**
     * 关闭数据库一般在OnDestroy中使用
     */
    public void closeDataBase() {
        mBind.close();
    }

    protected <C> void runInNewThread(final Callable<? extends C> callable, OnQueryListener<C> listener) {
        Single.fromCallable(callable)
                .subscribeOn(Schedulers.io())
                .unsubscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new SingleObserver<C>() {
                    private Disposable mDisposable;

                    @Override
                    public void onSubscribe(Disposable d) {
                        mDisposable = d;
                    }

                    @Override
                    public void onSuccess(C c) {
                        if (listener != null) {
                            listener.onQuery(c);
                        }
                        dispose(mDisposable);
                    }

                    @Override
                    public void onError(Throwable e) {
                        Log.i("BaseDBManager", "onError: " + e);
                        if (listener != null) {
                            listener.onQuery(null);
                        }
                        dispose(mDisposable);
                    }
                });
    }

    /**
     * 注销rxjava，防止内存泄露
     */
    private void dispose(Disposable disposable) {
        if (disposable != null && !disposable.isDisposed()) {
            disposable.dispose();
        }
    }

}
