package com.kaolafm.opensdk.demo;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ProgressBar;

import com.kaolafm.ad.api.model.Advert;
import com.kaolafm.opensdk.http.download.DownloadListener;
import com.kaolafm.opensdk.http.download.DownloadManager;
import com.kaolafm.opensdk.http.download.DownloadProgress;
import com.kaolafm.opensdk.http.error.ApiException;

import java.io.File;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * <AUTHOR>
 * @date 2020-02-19
 */
public class DownloadActivity extends BaseActivity {

    private static final String URL = "http://vod.cntv.lxdns.com/flash/mp4video1/qgds/2009/12/27/qgds_h264418000nero_aac32_20091227_1261891653006.mp4";

    @BindView(R.id.pb_download_progress)
    ProgressBar pbDownloadProgress;

    @Override
    public int getLayoutId() {
        return R.layout.activity_download;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        Intent intent = getIntent();
        Advert advert = intent.getParcelableExtra("ssss");
        Log.e("DownloadActivity", "initView: "+advert);
    }

    @Override
    public void initData() {

    }

    @OnClick({R.id.btn_download_start, R.id.btn_download_pause, R.id.btn_download_cancel})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.btn_download_start:
                DownloadManager.getInstance().download(URL, new DownloadListener() {
                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onProgress(DownloadProgress progress) {
                        pbDownloadProgress.setMax((int) progress.getTotalSize());
                        pbDownloadProgress.setProgress((int) progress.getDownloadedSize());
                    }

                    @Override
                    public void onSuccess(File file) {
                        Log.e("DownloadActivity", "onSuccess: ");
                    }

                    @Override
                    public void onError(ApiException exception) {
                        Log.e("DownloadActivity", "onError: "+exception);
                    }
                });
                break;
            case R.id.btn_download_pause:
                DownloadManager.getInstance().pause(URL);
                break;
            case R.id.btn_download_cancel:
                DownloadManager.getInstance().cancel(URL);
                break;
            default:
        }
    }
}
