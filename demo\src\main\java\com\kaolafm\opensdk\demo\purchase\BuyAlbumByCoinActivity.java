package com.kaolafm.opensdk.demo.purchase;

import com.kaolafm.opensdk.api.purchase.PurchaseRequest;
import com.kaolafm.opensdk.api.purchase.model.PurchaseSucess;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.ArrayList;
import java.util.List;

/**
 * 通用
 *
 */
public class BuyAlbumByCoinActivity extends CommonActivity {



    @Override
    public void initData() {
        new PurchaseRequest().buyAlbumByCoin(1100001230l,100l,  new HttpCallback<PurchaseSucess>() {
            @Override
            public void onSuccess(PurchaseSucess status) {
                if (adapter != null) {
                    List<Object> list = new ArrayList<>();
                    list.add(status);
                    adapter.setDataList(list);
                }
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取失败", exception);
            }
        });
    }
}
