//package com.kaolafm.opensdk.player;
//
//
//import android.app.Application;
//import android.os.RemoteException;
//import android.os.SystemClock;
//import android.text.TextUtils;
//import android.util.Log;
//
//import com.kaolafm.base.utils.ListUtil;
//import com.kaolafm.base.utils.NetworkUtil;
//import com.kaolafm.base.utils.StringUtil;
//import com.kaolafm.opensdk.ResType;
//import com.kaolafm.opensdk.api.music.qq.QQMusicRequest;
//import com.kaolafm.opensdk.api.music.qq.model.MusicInfo;
//import com.kaolafm.opensdk.api.music.qq.model.Song;
//import com.kaolafm.opensdk.api.music.qq.model.SongChartsResult;
//import com.kaolafm.opensdk.api.music.qq.model.SongMenu;
//import com.kaolafm.opensdk.di.scope.AppScope;
//import com.kaolafm.opensdk.http.core.HttpCallback;
//import com.kaolafm.opensdk.http.error.ApiException;
//import com.kaolafm.report.model.PlayReportParameter;
//import com.kaolafm.report.util.ReportConstants;
//import com.kaolafm.sdk.core.mediaplayer.BroadcastRadioPlayerManager;
//import com.kaolafm.sdk.core.mediaplayer.IPlayerOptions;
//import com.kaolafm.sdk.core.mediaplayer.IPlayerStateListener;
//import com.kaolafm.sdk.core.mediaplayer.PlayItem;
//import com.kaolafm.sdk.core.mediaplayer.PlayerManager;
//import com.kaolafm.sdk.core.mediaplayer.PlayerService;
//import com.kaolafm.sdk.core.mediaplayer.PlayerService.PlayerBinder;
//import com.kaolafm.sdk.core.util.ReportUtil;
//import com.kaolafm.sdk.vehicle.GeneralCallback;
//
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.Collections;
//import java.util.Iterator;
//import java.util.List;
//
//import javax.inject.Inject;
//
///**
// * 音乐播放管理类
// *
// * <AUTHOR>
// * @date 2018/4/17
// */
//public class MusicPlayerManager implements IPlayerOptions<PlayItem> {
//
//    private static final int SEC_DIVISOR = 1000;
//
//    private static final int PAGE_SIZE = 5;
//
//    /**
//     * 歌曲分页，每次拉取的歌曲数
//     */
//    private static final int PAGE_NUM = PAGE_SIZE;
//
//    /**
//     * 每次拉取最大歌曲数量
//     */
//    private static final int MAX_FETCH_SONGS_NUM = 50;
//
//    /**
//     * 是否清空播放列表
//     */
//    private boolean isClearList = true;
//
//    /**
//     * 上次退出时正在播放歌曲的songId
//     */
//    private long mLatestSongId;
//
//    /**
//     * 上次退出时正在播放歌曲的位置
//     */
//    private int mLatestPosition = 0;
//
//    private final MusicListManager mListManager;
//
//    private MusicInfo mMusicInfo;
//
//    @Inject
//    QQMusicRequest mMusicRequest;
//
//    @Inject
//    @AppScope
//    Application mApplication;
//
//    private PlayerBinder mPlayerBinder;
//
//    private List<IPlayerStateListener> mUnAddedPlayerStateListeners = new ArrayList<>();
//
////    private List<OnDownloadProgressListener> mUnAddedOnDownloadProgressListener = new ArrayList<>();
//
//    private ArrayList<GeneralCallback> mGetContentGeneralCallbacks;
//
////    private ArrayList<IPlayChangedListener> mIPlayChangedListenerList;
//
//    /**
//     * 记录上次播放的单曲信息
//     */
//    private PlayItem mPrePlayItem;
//
//    /**
//     * 记录QQ音乐上次播放专辑id
//     */
//    private String mPreRadioId;
//
//    /**
//     * 音乐播放器是否可用
//     */
//    private boolean isMusicPlayerEnable;
//
//    /**
//     * 当前播放的位置
//     */
//    private int mCurrentPosition = 0;
//
//    /**
//     * 已经播放的时长，上报数据用
//     */
//    private int mHavePlayedTime;
//
//    /**
//     * 是否触发了快进快退，true为触发
//     */
//    private boolean isSeekEvent = false;
//
//    /**
//     * 上一个播放位置的时间
//     */
//    private long mPrePositionTime;
//
//    /**
//     * 记录当前单曲播放总时长 单位ms
//     */
//    private long mAudioStartPlayTime;
//
//    /**
//     * 是否首次启动播放 true为是，false为否
//     */
//    private boolean isFirstPlay = true;
//
//    private boolean mCurPlayStartReported = false;
//
//
//    private MusicPlayerManager() {
//        mListManager = MusicListManager.getInstance();
//        mMusicInfo = new MusicInfo();
////        SessionComponent subcomponent = ComponentKit.getInstance().getSubcomponent();
////        subcomponent.getMusicPlayerComponent().inject(this);
//    }
//
//    private static class MusicPlayerManagerHolder {
//
//        private static final MusicPlayerManager INSTANCE = new MusicPlayerManager();
//    }
//
//    public static MusicPlayerManager getInstance() {
//        return MusicPlayerManagerHolder.INSTANCE;
//    }
//
//    public void init(PlayerBinder playerBinder) {
//        mPlayerBinder = playerBinder;
//        try {
//            mPlayerBinder.addPlayerStateListener(mPlayerStateListener);
//            for (int i = 0; i < mUnAddedPlayerStateListeners.size(); i++) {
//                IPlayerStateListener playerStateListener = mUnAddedPlayerStateListeners.get(i);
//                if (playerStateListener != null) {
//                    mPlayerBinder.addPlayerStateListener(playerStateListener);
//                }
//            }
//            mUnAddedPlayerStateListeners.clear();
//
//        } catch (Throwable e) {
//            e.printStackTrace();
//        }
//    }
//
//
//    private IPlayerStateListener mPlayerStateListener = new IPlayerStateListener() {
//        @Override
//        public void onIdle(PlayItem playItem) {
//            if (isMusicPlayerEnable) {
//                mCurrentPosition = 0;
//            }
//        }
//
//        @Override
//        public void onPlayerPreparing(PlayItem playItem) {
//            if (isMusicPlayerEnable) {
//                mCurrentPosition = 0;
//            }
//        }
//
//        @Override
//        public void onPlayerPlaying(PlayItem playItem) {
//            if (isMusicPlayerEnable) {
//                if (!mCurPlayStartReported) {
//                    mAudioStartPlayTime = System.currentTimeMillis();
//                    mCurPlayStartReported = true;
//                }
//                //存入收听历史数据库
////                mDaoManager.save(mListManager.getCurrentSong());
////                MusicResumePlayUtil.saveSongId(playItem.getAudioId());
//            }
//        }
//
//        @Override
//        public void onPlayerPaused(PlayItem playItem) {
//            if (isMusicPlayerEnable) {
//                mCurrentPosition = 0;
//            }
//        }
//
//        @Override
//        public void onProgress(String url, int position, int duration, boolean isPreDownloadComplete) {
//            if (isMusicPlayerEnable) {
//                if (mCurrentPosition != 0) {
//                    int playTime = position - mCurrentPosition;
//                    if (playTime > 0) {
//                        mHavePlayedTime += playTime;
//                    }
//                    ReportUtil.reportSetPlayPosition(mHavePlayedTime, duration);
//                }
//                mCurrentPosition = position;
//            }
//        }
//
//        @Override
//        public void onPlayerFailed(PlayItem playItem, int i, int i1) {
//            if (isMusicPlayerEnable) {
//                mCurrentPosition = 0;
//            }
//        }
//
//        @Override
//        public void onPlayerEnd(PlayItem playItem) {
//            if (isMusicPlayerEnable) {
//                mCurrentPosition = 0;
//                if (!NetworkUtil.isNetworkAvailable(mApplication)) {
//                    playItem.setPosition(0);
//                    return;
//                }
//                //上报播放结束
//                //   reportListenToEnd(playItem);
//                ReportUtil.reportEndPlay(ReportConstants.PLAY_CHANGE_BY_AUTO);
//                mHavePlayedTime = 0;
//                playNext(true);
//            }
//        }
//
//        @Override
//        public void onSeekStart(String s) {
//            if (isMusicPlayerEnable) {
//                isSeekEvent = true;
//                mCurrentPosition = 0;
//            }
//        }
//
//        @Override
//        public void onSeekComplete(String s) {
//
//        }
//
//        @Override
//        public void onBufferingStart(PlayItem playItem) {
//            if (isMusicPlayerEnable) {
//                mCurrentPosition = 0;
//                mPrePositionTime = SystemClock.elapsedRealtime();
//                //    reportBuffering(true);
//                ReportUtil.reportBufferingStart(playItem, isSeekEvent);
//            }
//        }
//
//        @Override
//        public void onBufferingEnd(PlayItem playItem) {
//            if (isMusicPlayerEnable) {
//                ReportUtil.reportBufferingEnd(playItem, isSeekEvent, mPrePositionTime);
//                isSeekEvent = false;
//                mPrePositionTime = 0;
//            }
//        }
//    };
//
//
//    private void clearPlayerList() {
//        mListManager.clearPlayList();
//        mPrePlayItem = null;
//    }
//
////    @Override
//    public boolean isState(int state) {
//        try {
//            return mPlayerBinder != null && mPlayerBinder.isState(state);
//        } catch (RemoteException e) {
//            e.printStackTrace();
//            return false;
//        }
//    }
//
//    @Override
//    public void play() {
////        initPlayPosition();
//        if (mPlayerBinder != null) {
//            try {
//                mPlayerBinder.play();
//            } catch (RemoteException e) {
//                e.printStackTrace();
//            }
//        }
//    }
//
//    public void play(ArrayList<PlayItem> playItems) {
//        play(playItems, Collections.emptyList());
//    }
//
//    private void play(ArrayList<PlayItem> playItems, List<Song> songList) {
//        if (playItems != null && playItems.size() > 0) {
//            enablePlayer();
//            //私人FM第一次需要清空，后面不需要清空。
//            if (isClearList || (mMusicInfo.getType() != ResType.MUSIC_MINE_PRIVATE_FM
//                    && mMusicInfo.getType() != ResType.MUSIC_MINE_LIKE
//                    && mMusicInfo.getType() != ResType.TYPE_SONGMENU
//                    && mMusicInfo.getType() != ResType.TYPE_MUSIC_RADIO_LABEL
//                    && mMusicInfo.getType() != ResType.TYPE_MUSIC_RADIO_SCENE)) {
//                clearPlayerList();
//            }
//            mListManager.addPlayList(playItems);
//            mListManager.addSongList(songList);
//            play(playItems.get(mLatestPosition));
//            //复位。防止切换时崩溃或不从头开始播放
//            mLatestPosition = 0;
//            mLatestSongId = 0;
//        } else {
//            notifyGetContent(false);
//        }
//    }
//
//    /**
//     * 播放指定的PlayItem，并自动播放下一首
//     *
//     * @param playItem 要播放的PlayItem
//     */
////    @Override
//    public void play(PlayItem playItem) {
//        MusicListManager.getInstance().setCurPlayItemIndex(playItem);
//        start(playItem, true);
//    }
//
////    @Override
//    public void play(int position) {
//        PlayItem playItem = getPlayItemByPosition(position);
//        if (playItem == null) {
//            return;
//        }
//        play(playItem);
//    }
////===================================自定义播放各种类型的方法=================================================================
//
//    public void play(Song song) {
////        play(song.transform2PlayItem());
//    }
//
//    public void play(List<Song> songList) {
//        play(getPlayItems(songList), songList);
//    }
//
//    private ArrayList<PlayItem> getPlayItems(List<Song> songList) {
//        ArrayList<PlayItem> playItemList = new ArrayList<>();
//        if (songList != null && songList.size() > 0) {
//            //不能使用for循环，一个是遵循规范，一个是防止出现角标越界或ConcurrentModificationException
//            Iterator<Song> iterator = songList.iterator();
//            while (iterator.hasNext()) {
//                Song song = iterator.next();
//                //过滤没有播放地址的歌曲。
//                if (TextUtils.isEmpty(song.getSongPlayUrlStandard())) {
//                    iterator.remove();
//                    continue;
//                }
//                if (song.getSongId() == mLatestSongId) {
//                    //不能用i，因为前面有判断，会对不上号。
//                    mLatestPosition = playItemList.size();
//                }
////                playItemList.add(song.transform2PlayItem());
//            }
//            return playItemList;
//        }
//        return playItemList;
//    }
//
//    public void play(String... songMids) {
//        if (songMids == null || songMids.length == 0) {
//            return;
//        }
//        mMusicRequest.getSongListBatch(StringUtil.array2String(songMids),
//                new HttpCallback<List<Song>>() {
//                    @Override
//                    public void onSuccess(List<Song> songList) {
//                        play(songList);
//                    }
//
//                    @Override
//                    public void onError(ApiException exception) {
//
//                    }
//                });
//    }
//
//    /**
//     * 根据类型播放歌曲。歌单、榜单、电台 {@link }
//     *
//     * @param id      播放id
//     * @param resType 要播放的类型
//     * @param source  音频来源
//     */
//    private void play(long id, int resType, String source) {
//        switch (resType) {
//            case ResType.TYPE_QQ_MUSIC:
//            case ResType.TYPE_SONGMENU:
//                playSongMenu(id, source);
//                break;
//            case ResType.TYPE_SONG_CHARTS:
//                playCharts(id);
//                break;
//            case ResType.TYPE_MUSIC_RADIO_SCENE:
//                playSceneRadio(id, source);
//                break;
//            case ResType.TYPE_MUSIC_RADIO_LABEL:
//                playLabelRadio(id, source);
//                break;
//            case ResType.MUSIC_MINE_DAY:
//                playRecommendSongs(source);
//                break;
//            case ResType.MUSIC_MINE_LIKE:
//                playMyLike(source);
//                break;
//            case ResType.MUSIC_MINE_PRIVATE_FM:
//                playPrivateFM();
//                break;
//            case ResType.MUSIC_MINE_HISTORY:
//                playHistory();
//                break;
//            default:
//        }
//    }
//
//    /**
//     * 播放语音搜索结果
//     */
//    public void playVoiceResult(String sourceName, String... songMids) {
//        if (songMids == null || songMids.length == 0) {
//            return;
//        }
//        mMusicRequest.getSongListBatch(StringUtil.array2String( songMids), new HttpCallback<List<Song>>() {
//            @Override
//            public void onSuccess(List<Song> songList) {
//                if (songList != null) {
//                    int size = songList.size();
//                    //语音搜索结果续播时播放收听历史, 要单独处理，不影响当前的显示
////                    MusicResumePlayUtil.saveInfo(ResType.MUSIC_MINE_HISTORY, ResType.MUSIC_MINE_HISTORY,
////                            ResUtil.getString(R.string.listen_history));
//                    //当前显示信息
//                    mMusicInfo.clear();
////                    mMusicInfo.setName(ResUtil.getString(R.string.music));
//                    //如果是一个列表就替换原来的播单
//                    if (size > 1) {
//                        mMusicInfo.setType(ResType.TYPE_MUSIC_VOICE_RESULT);
//                        play(songList);
//                    } else {
//                        //如果是一个单曲，就分情况处理
//                        processPlaySingleSong(songList);
//                    }
//                }
//            }
//
//            @Override
//            public void onError(ApiException exception) {
//
//            }
//        });
//    }
//
//    public void playByMids(String... songMids) {
//        playVoiceResult("", songMids);
//    }
//
//    /**
//     * 处理播单语音搜索结果单曲情况
//     */
//    private void processPlaySingleSong(List<Song> songList) {
//        Song song = songList.get(0);
//        ArrayList<PlayItem> playList = mListManager.getPlayList();
//        //正处于播放音乐的播放器
//        if (playList != null && !playList.isEmpty()) {
//            PlayItem curPlayItem = mListManager.getCurPlayItem();
//            if (curPlayItem != null && TextUtils.equals(song.getSongMid(), curPlayItem.getMid())) {
//                if (isPaused()) {
//                    play();
//                }
//                return;
//            }
//            PlayItem playItem = null;
//            for (int i = 0, size = playList.size(); i < size; i++) {
//                PlayItem tempItem = playList.get(i);
//                //播单有该歌曲就直接跳过去播放。
//                if (TextUtils.equals(tempItem.getMid(), song.getSongMid())) {
//                    playItem = tempItem;
//                }
//            }
//            if (playItem != null) {
//                play(playItem);
//                return;
//            }
//            int index = mListManager.getCurPosition() + 1;
//            ArrayList<PlayItem> playItemList = getPlayItems(songList);
//            mListManager.addPlayList(index, playItemList);
//            mListManager.addSongList(index, songList);
//            play(mListManager.getPlayList().get(index));
//        } else {//不是在播放音乐。
//            /*mDaoManager.querySpecifiedCount(historyList -> {
//                mMusicInfo.clear();
//                mMusicInfo.setName(ResUtil.getString(R.string.listen_history));
//                mMusicInfo.setType(ResType.MUSIC_MINE_HISTORY);
//                if (historyList != null) {
//                    long equalId = 0;
//                    for (int i = 0, size = historyList.size(); i < size; i++) {
//                        Song temp = historyList.get(i);
//                        if (TextUtils.equals(temp.getSongMid(), song.getSongMid())) {
//                            equalId = temp.getSongId();
//                        }
//                    }
//                    if (equalId != 0) {
//                        mLatestSongId = equalId;
//                    } else {
//                        historyList.add(0, song);
//                    }
//                    play(historyList);
//                } else {
//                    play(songList);
//                }
//            });*/
//        }
//    }
//
//    /***
//     * 根据榜单id播放榜单(排行榜歌单)
//     * @param chartId
//     */
//    public void playCharts(long chartId) {
//        if (!isPlayThis(chartId, ResType.TYPE_SONG_CHARTS)) {
//            mMusicRequest.getSongListOfSongCharts(chartId, new HttpCallback<SongChartsResult>() {
//                @Override
//                public void onSuccess(SongChartsResult songChartsResult) {
//                    saveInfo(songChartsResult.getTopId(), ResType.TYPE_SONG_CHARTS,
//                            songChartsResult.getTopName());
////                    mMusicInfo.setCover(songChartsResult.getTopDesc());
////                    mMusicInfo.setListenNum(songChartsResult.getListenNum());
////                    mMusicInfo.setDesc(songChartsResult.getTopDesc());
//                    isClearList = true;
//                    play(songChartsResult.getResult());
//                }
//
//                @Override
//                public void onError(ApiException exception) {
//
//                }
//            });
//        }
//    }
//
//    /**
//     * 根据歌单id播放普通的歌单
//     */
//    public void playSongMenu(long songMenuId, String source) {
//        playSongMenu(songMenuId, source, ResType.TYPE_SONGMENU);
//    }
//
//    /**
//     * 播放歌单，歌单有多个类型。
//     */
//    private void playSongMenu(long songMenuId, String source, int type) {
//        if (!isPlayThis(songMenuId, type)) {
//            mMusicRequest.getSongListOfSongMenu(songMenuId,
//                    new HttpCallback<List<Song>>() {
//                        @Override
//                        public void onSuccess(List<Song> songList) {
//                            saveInfo(songMenuId, type, source);
//                            //第一次请求要清空播单，不加该项在更换歌单时会不清空播单
//                            isClearList = true;
//                            int position = 0;
//                            if (songList != null && songList.size() > 0) {
//                                int size = songList.size();
//                                String[] mids = new String[size];
//                                for (int i = 0; i < size; i++) {
//                                    String songMid = songList.get(i).getSongMid();
//                                    if (!TextUtils.isEmpty(songMid)) {
//                                        mids[i] = songMid;
//                                    }
//                                    if (songList.get(i).getSongId() == mLatestSongId) {
//                                        position = i;
//                                    }
//                                }
//                                mMusicInfo.setSongMids(mids);
//
//                                //小于每页数量，拉取一页
//                                int pageNum = PAGE_NUM;
//                                if (position + 1 > PAGE_NUM && position + 1 < MAX_FETCH_SONGS_NUM) {
//                                    pageNum = position + 1;
//                                }
//                                play(getPlaySongMids(pageNum));
//                            }
//                        }
//
//                        @Override
//                        public void onError(ApiException exception) {
//
//                        }
//                    });
//        }
//    }
//
//    private void batchSongList(int position) {
//        int num = (int) Math.ceil(((double) position) / MAX_FETCH_SONGS_NUM);
//        for (int i = 0; i < num; i++) {
//            String[] playSongMids = getPlaySongMids(MAX_FETCH_SONGS_NUM);
//            int page = i;
//            mMusicRequest.getSongListBatch(StringUtil.array2String(playSongMids),
//                    new HttpCallback<List<Song>>() {
//                        @Override
//                        public void onSuccess(List<Song> songList) {
//                            Log.e("MusicPlayerManager", "onSuccess: i=" + page + ", songList=" + songList.size());
//                        }
//
//                        @Override
//                        public void onError(ApiException exception) {
//
//                        }
//                    });
//        }
//    }
//
//    /**
//     * 播放我喜欢的/我的红心，其实也是歌单的一种
//     */
//    public void playMyLike(long songMenuId, String source) {
//        playSongMenu(songMenuId, source, ResType.MUSIC_MINE_LIKE);
//    }
//
//    /**
//     * 播放我喜欢的/我的红心，其实也是歌单的一种
//     */
//    public void playMyLike(String source) {
//        mMusicRequest.getSelfSongMenuList(new HttpCallback<List<SongMenu>>() {
//            @Override
//            public void onSuccess(List<SongMenu> songMenus) {
//                if (songMenus != null && songMenus.size() > 0) {
//                    SongMenu songMenu = songMenus.get(0);
//                    if (TextUtils.equals(songMenu.getDissName(), "我喜欢的")) {
//                        playMyLike(songMenu.getDissId(), source);
//                    }
//                }
//            }
//
//            @Override
//            public void onError(ApiException exception) {
//
//            }
//        });
//    }
//
//    /**
//     * 播放当前正在播放的场景电台的下一页
//     */
//    private void playSceneRadio() {
//        if (mMusicInfo.getType() == ResType.TYPE_MUSIC_RADIO_SCENE) {
//            playSceneRadio(mMusicInfo.getId(), mMusicInfo.getName(), false);
//        }
//    }
//
//    /**
//     * 根据电台id播放场景电台歌曲
//     */
//    public void playSceneRadio(long radioId, String source) {
//        playSceneRadio(radioId, source, true);
//    }
//
//    public void playSceneRadio(long radioId, String source, boolean isFirstPlay) {
//        boolean canPlay = (!isPlayThis(radioId, ResType.TYPE_MUSIC_RADIO_SCENE) && isFirstPlay)
//                || !isFirstPlay;
//        if (canPlay) {
//            mMusicRequest.getSongListOfRadio(radioId, new HttpCallback<List<Song>>() {
//                @Override
//                public void onSuccess(List<Song> songList) {
//                    isClearList = isFirstPlay;
//                    if (isFirstPlay) {
//                        saveInfo(radioId, ResType.TYPE_MUSIC_RADIO_SCENE, source);
//                    }
//                    play(songList);
//                }
//
//                @Override
//                public void onError(ApiException exception) {
//                    Log.e("MusicPlayerManager", "onError: throwable=" + exception);
//                }
//            });
//        }
//    }
//
//    /**
//     * 播放下一页标签电台
//     */
//    public void playLabelRadio() {
//        if (mMusicInfo.getType() == ResType.TYPE_MUSIC_RADIO_LABEL) {
//            playLabelRadio(mMusicInfo.getId(), mMusicInfo.getName(), false);
//        }
//    }
//
//    /**
//     * 根据电台id播放标签电台
//     */
//    public void playLabelRadio(long radioId, String source) {
//        playLabelRadio(radioId, source, true);
//    }
//
//    /**
//     * 根据电台id播放标签电台
//     *
//     * @param isFirstPlay 是否第一次播放
//     */
//    public void playLabelRadio(long radioId, String source, boolean isFirstPlay) {
//        boolean canPlay = (!isPlayThis(radioId, ResType.TYPE_MUSIC_RADIO_LABEL) && isFirstPlay)
//                || !isFirstPlay;
//        if (canPlay) {
//            mMusicRequest.getSongListOfCategoryLabel(radioId, new HttpCallback<List<Song>>() {
//                @Override
//                public void onSuccess(List<Song> songList) {
//                    //第一次播放需要清播单
//                    isClearList = isFirstPlay;
//                    if (isFirstPlay) {
//                        saveInfo(radioId, ResType.TYPE_MUSIC_RADIO_LABEL, source);
//                    }
//                    play(songList);
//                }
//
//                @Override
//                public void onError(ApiException exception) {
//
//                }
//            });
//        }
//    }
//
//
//    /**
//     * 播放每日推荐30首
//     */
//    public void playRecommendSongs(String name) {
//        if (!isPlayThis(ResType.MUSIC_MINE_DAY, ResType.MUSIC_MINE_DAY)) {
//            mMusicRequest.getDayRecommendSongs(new HttpCallback<List<Song>>() {
//                @Override
//                public void onSuccess(List<Song> songList) {
//                    saveInfo(ResType.MUSIC_MINE_DAY, ResType.MUSIC_MINE_DAY, name);
//                    play(songList);
//                }
//
//                @Override
//                public void onError(ApiException exception) {
//
//                }
//            });
//        }
//    }
//
//    /**
//     * 播放私人FM
//     */
//    public void playPrivateFM() {
//        playPrivateFM(true);
//    }
//
//    public void playPrivateFM(boolean isFirstPlay) {
//        boolean canPlay = !isPlayThis(ResType.MUSIC_MINE_PRIVATE_FM, ResType.MUSIC_MINE_PRIVATE_FM) || !isFirstPlay;
//        if (canPlay) {
//            mMusicRequest.getIndividualSongsOfRadio(new HttpCallback<List<Song>>() {
//                @Override
//                public void onSuccess(List<Song> songList) {
//                    isClearList = isFirstPlay;
//                    saveInfo(ResType.MUSIC_MINE_PRIVATE_FM, ResType.MUSIC_MINE_PRIVATE_FM,
//                            "私人fm");
//                    play(songList);
//                    if (!isFirstPlay) {
////                        reportPlayNextSong(songList.get(0).transform2PlayItem());
//                    }
//                }
//
//                @Override
//                public void onError(ApiException exception) {
//
//                }
//            });
//        }
//    }
//
//    /**
//     * 播放最近收听
//     */
//    public void playHistory() {
//        if (!isPlayThis(ResType.MUSIC_MINE_HISTORY, ResType.MUSIC_MINE_HISTORY)) {
////            mDaoManager.querySpecifiedCount(songList -> {
////                //自定义id用于显示播放状态
////                saveInfo(ResType.MUSIC_MINE_HISTORY, ResType.MUSIC_MINE_HISTORY,
////                        ResUtil.getString(R.string.listen_history));
////                play(songList);
////            });
//        }
//    }
//
//    /**
//     * App退出再次打开继续播放上一次播放的内容
//     */
//    public void playLatest() {
//        if (!isPlayerEnable()) {
////            MusicInfo musicInfo = MusicResumePlayUtil.getInfo();
////            mLatestSongId = musicInfo.getLatestSongMid();
////            play(musicInfo.getId(), musicInfo.getType(), musicInfo.getName());
//        }
//    }
////=========================================================================================================
//
//    @Override
//    public void pause() {
//        initPlayPosition();
//        if (mPlayerBinder != null && isPlaying()) {
//            try {
//                mPlayerBinder.pause();
//            } catch (RemoteException e) {
//                e.printStackTrace();
//            }
//        }
//    }
//
//
//    @Override
//    public void stop() {
//        initPlayPosition();
//        if (mPlayerBinder != null) {
//            mPlayerBinder.stop();
//        }
//    }
//
//    @Override
//    public void reset() {
//        initPlayPosition();
//        mMusicInfo.clear();
//        if (mPlayerBinder != null) {
//            try {
//                mPlayerBinder.reset();
//            } catch (RemoteException e) {
//                e.printStackTrace();
//            }
//        }
//    }
//
//    @Override
//    public void release() {
//
//    }
//
//    @Override
//    public void switchPlayerStatus() {
//        if (isPlaying()) {
//            pause();
//        } else {
//            isMusicPlayerEnable = true;
//            addIPlayerStateListener(mPlayerStateListener);
//
//            if (isPaused()) {
//                play();
//            } else {
//                playStart();
//            }
//        }
//    }
//
//    /**
//     * 针对断点续播功能设计（启动客户端，根据历史初始化播单但没有自动播放时调用）
//     */
//    private void playStart() {
//        PlayItem playItem = MusicListManager.getInstance()
//                .getCurPlayItem();
//        mCurrentPosition = 0;
//        if (playItem == null) {
//            if (hasNext()) {
//                playNext(false);
//            }
//            return;
//        }
//        playItem.setPosition(0);
//        start(playItem);
//    }
//
//    private void start(PlayItem playItem) {
//        start(playItem, false);
//    }
//
//    /**
//     * 检查是否正在播放当前歌曲集合
//     *
//     * @return true 正在播放当前的
//     */
//    private boolean isPlayThis(long songMenuId, int type) {
//        if (songMenuId == mMusicInfo.getId() && type == mMusicInfo.getType()) {
//            if (isPaused()) {
//                play();
//            }
//            return true;
//        }
//        return false;
//    }
//
//    @Override
//    public boolean isPlaying() {
//        return mPlayerBinder != null && mPlayerBinder.isPlaying() && isMusicPlayerEnable;
//    }
//
//    @Override
//    public void seek(int position) {
//        if (mPlayerBinder != null && NetworkUtil.isNetworkAvailable(mApplication)) {
//            try {
//                mPlayerBinder.seek(position);
//            } catch (RemoteException e) {
//                e.printStackTrace();
//            }
//        }
//    }
//
//    @Override
//    public void playPre() {
//        start(MusicListManager.getInstance().getPrePlayItem(), false);
//    }
//
//    /**
//     * 手动播放下一首。
//     */
//    @Override
//    public void playNext() {
//        playNext(false);
//    }
//
//    /**
//     * 是否自动播放下一首，true自动播放。
//     */
//    private void playNext(boolean isAutoPlay) {
//        PlayItem nextPlayItem = mListManager.getNextPlayItem(isAutoPlay);
//        if (nextPlayItem == null) {
//            //如果是在播放私人fm，没有下一首就再次请求接口
//            if (mMusicInfo.getType() == ResType.MUSIC_MINE_PRIVATE_FM) {
//                isClearList = false;
//                playPrivateFM(false);
//                //电台每次进入都不一样，下一页会继续调接口
//            } else if (mMusicInfo.getType() == ResType.TYPE_MUSIC_RADIO_SCENE) {
//                isClearList = false;
//                playSceneRadio();
//            } else if (mMusicInfo.getType() == ResType.TYPE_MUSIC_RADIO_LABEL) {
//                isClearList = false;
//                playLabelRadio();
//            } else {
//                playNextPage();
//            }
//        } else {
//            reportPlayNextSong(nextPlayItem);
//            start(nextPlayItem, isAutoPlay);
//        }
//    }
//
//    /**
//     * 当前播放音乐类型参考
//     */
//    private int mCurrentMusicType = -1;
//
//    private void start(PlayItem playItem, boolean isAutoPlay) {
//        if (mPlayerBinder == null
//                || playItem == null
//                || TextUtils.isEmpty(playItem.getPlayUrl())) {
//            return;
//        }
//        if (mPrePlayItem != null) {
//            //如果是播放的同一个就继续播放
//            if (TextUtils.equals(mPrePlayItem.getMid(), playItem.getMid()) && (isPlaying()
//                    || isPaused())) { // 解决31231问题
//                if (isPaused()) {
//                    play();
//                }
//                return;
//            }
//        }
//        mListManager.setCurPlayItemIndex(playItem);
//        enablePlayer();
////        reset();
//        playItem.setPosition(0);
//        try {
//            mPlayerBinder.start(playItem);
//        } catch (RemoteException e) {
//            e.printStackTrace();
//        }
//        notifyIPlayChangedListener(playItem);
//
//        reportStartListen(playItem, mMusicInfo.getType(), String.valueOf(mMusicInfo.getId()));
//        if (mPrePlayItem != null) {
//            //   reportPlayEnd(mPrePlayItem);
//        } else {
//            mHavePlayedTime = 0;
//        }
//        mPreRadioId = String.valueOf(mMusicInfo.getId());
//        mPrePlayItem = playItem;
//        mCurPlayStartReported = false;
//        int musicType = mMusicInfo.getType();
//        if (mCurrentMusicType != musicType) {
//            mCurrentMusicType = musicType;
////            EventBus.getDefault().post(new PlayerMusicRadioChangedEBData());
//        }
//    }
//
//    /**
//     * 判断播放状态是否处于paused状态
//     *
//     * @return true 为暂停状态 false 为其他状态
//     */
//    public boolean isPaused() {
//        if (mPlayerBinder == null || !isMusicPlayerEnable) {
//            return false;
//        }
//        try {
//            if (mPlayerBinder.isState(PlayerService.STATE_ON_PAUSED)) {
//                return true;
//            }
//        } catch (RemoteException e) {
//            e.printStackTrace();
//        }
//        return false;
//    }
//
//
//    @Override
//    public void destroy() {
////        //保存当前正在播放的歌曲集合id、类型、第几首等信息，用于断点续播
////        mMusicInfo.setLatestSongMid(mListManager.getCurPlayItem().getAudioId());
////        MusicResumePlayUtil.saveInfo(mMusicInfo);
//        removePlayerStateListener();
//        if (isMusicPlayerEnable) {
//            //   reportPlayEnd(MusicListManager.getInstance().getCurPlayItem());
//        }
//    }
//
//    @Override
//    public void enablePlayer() {
//        if (!isMusicPlayerEnable) {
//            isMusicPlayerEnable = true;
////            KLAutoPlayerManager.getInstance().setIPlayerOptions(this);
//            addIPlayerStateListener(mPlayerStateListener);
//            disableOtherPlayer();
////            EventBus.getDefault().post(new PlayerChangedEBData());
//        }
//    }
//
//    /**
//     * 时音乐播放器不可用
//     */
//    public void disablePlayer() {
//        if (isMusicPlayerEnable) {
//            //   reportPlayEnd(mListManager.getCurPlayItem());
//            ReportUtil.reportEndPlay(ReportConstants.PLAY_CHANGE_BY_CLICK);
//        }
//
//        clearPlayerList();
//        removePlayerStateListener();
//        isMusicPlayerEnable = false;
//        mPrePlayItem = null;
//        mPreRadioId = null;
//        mMusicInfo.clear();
//        isClearList = true;
//        mLatestSongId = 0;
//        mLatestPosition = 0;
//        mHavePlayedTime = 0;
//    }
//
//    /**
//     * @param playerStateListener
//     */
//    public void addPlayerStateListener(IPlayerStateListener playerStateListener) {
//        if (mPlayerBinder != null) {
//            try {
//                mPlayerBinder.addPlayerStateListener(playerStateListener);
//            } catch (RemoteException e) {
//                e.printStackTrace();
//            }
//        } else {
//            if (!mUnAddedPlayerStateListeners.contains(playerStateListener)) {
//                mUnAddedPlayerStateListeners.add(playerStateListener);
//            }
//        }
//    }
//
//    @Override
//    public void disableOtherPlayer() {
//        PlayerManager playerManager = PlayerManager.getInstance(mApplication);
//        if (playerManager.isPlayerEnable()) {
//            playerManager.disablePlayer();
//        }
//        BroadcastRadioPlayerManager radioPlayerManager = BroadcastRadioPlayerManager.getInstance();
//        if (radioPlayerManager.isBroadcastPlayerEnable()) {
//            radioPlayerManager.disableBroadcastPlayer();
//        }
//    }
//
//    @Override
//    public void setVolume(float leftVolume, float rightVolume) {
//        if (mPlayerBinder != null) {
//            mPlayerBinder.setVolume(leftVolume, rightVolume);
//        }
//    }
//
//    @Override
//    public boolean hasNext() {
//        //私人fm, 场景电台，标签电台一直有下一首
//        if (mMusicInfo.getType() == ResType.MUSIC_MINE_PRIVATE_FM
//                || mMusicInfo.getType() == ResType.TYPE_MUSIC_RADIO_LABEL
//                || mMusicInfo.getType() == ResType.TYPE_MUSIC_RADIO_SCENE) {
//            return true;
//        }
//        //歌单有分页，如果当前播单中没有下一页，判断是否还有下一页。如果有就还有下一首
//        return haveNextPage() || MusicListManager
//                .getInstance().hasNext();
//    }
//
//    @Override
//    public boolean hasPre() {
//        if (mMusicInfo.getType() == ResType.MUSIC_MINE_PRIVATE_FM) {
//            return MusicListManager.getInstance().getCurPosition() > 0;
//        }
//        return mListManager.hasPre();
//    }
//
//    /**
//     * 添加播放状态监听{@link IPlayerStateListener}
//     * 如果播放器已经连上服务，即{@link PlayerBinder}不为空，就添加到{@link PlayerBinder}中,<br/>
//     * 否则就先将监听存储起来，等连上服务会自动添加到{@link PlayerBinder}中。
//     */
////    @Override
//    public void addIPlayerStateListener(IPlayerStateListener playerStateListener) {
//        if (mPlayerBinder != null) {
//            try {
//                mPlayerBinder.addPlayerStateListener(playerStateListener);
//            } catch (RemoteException e) {
//                e.printStackTrace();
//            }
//        } else {
//            if (!mUnAddedPlayerStateListeners.contains(playerStateListener)) {
//                mUnAddedPlayerStateListeners.add(playerStateListener);
//            }
//        }
//    }
//
////    @Override
//    public void removeIPlayerStateListener(IPlayerStateListener playerStateListener) {
//        if (mPlayerBinder != null) {
//            try {
//                mPlayerBinder.removePlayerStateListener(playerStateListener);
//            } catch (RemoteException e) {
//                e.printStackTrace();
//            }
//        } else {
//            if (mUnAddedPlayerStateListeners.contains(playerStateListener)) {
//                mUnAddedPlayerStateListeners.remove(playerStateListener);
//            }
//        }
//    }
//
//    public void removePlayerStateListener() {
//        removeIPlayerStateListener(mPlayerStateListener);
//    }
//
//
//    /**
//     * 添加播放单曲切换行为监听
//     */
////    @Override
////    public void addIPlayChangedListener(IPlayChangedListener iPlayChangedListener) {
////        PlayerManager.getInstance(mApplication).addIPlayChangedListener(iPlayChangedListener);
////    }
//
//    /**
//     * 移除播放单曲切换行为监听
//     */
////    @Override
////    public void removeIPlayChangedListener(IPlayChangedListener iPlayChangedListener) {
////        PlayerManager.getInstance(mApplication).removeIPlayChangedListener(iPlayChangedListener);
////    }
//
//    private void notifyIPlayChangedListener(PlayItem playItem) {
////        PlayerManager.getInstance(mApplication).notifyIPlayChangedListener(playItem);
//    }
//
//
////    @Override
//    public void updatePlayListPosition(int position) {
//        MusicListManager.getInstance().setCurPosition(position);
//    }
//
//    /**
//     * 是否有下一页
//     */
//    public boolean haveNextPage() {
//        String[] songMids = mMusicInfo.getSongMids();
//        return songMids != null && songMids.length > 0;
//    }
//
//    /**
//     * 播放下一页
//     */
//    public void playNextPage() {
//        if (haveNextPage()) {
//            play(getPlaySongMids(PAGE_NUM));
//            isClearList = false;
//        }
//    }
//
//    /**
//     * 加载下一页歌曲
//     */
//    public void loadNextPage(GeneralCallback<Boolean> generalCallback) {
//        if (haveNextPage()) {
//            mMusicRequest.getSongListBatch(StringUtil.array2String(getPlaySongMids(PAGE_NUM)),
//                    new HttpCallback<List<Song>>() {
//                        @Override
//                        public void onSuccess(List<Song> songList) {
//                            if (songList != null && songList.size() > 0) {
//                                mListManager.addPlayList(getPlayItems(songList));
//                                mListManager.addSongList(songList);
//                                generalCallback.onResult(true);
//                            } else {
//                                generalCallback.onResult(false);
//                            }
//                        }
//
//                        @Override
//                        public void onError(ApiException exception) {
//                            generalCallback.onResult(false);
//                        }
//                    });
//        }
//    }
//
//    /**
//     * 获取要加载或播放的歌曲mid数组
//     */
//    private String[] getPlaySongMids(int pageNum) {
//        String[] songMids = mMusicInfo.getSongMids();
//        String[] playSongMids;
//        if (songMids.length > pageNum) {
//            playSongMids = Arrays.copyOf(songMids, pageNum);
//            mMusicInfo.setSongMids(Arrays.copyOfRange(songMids, pageNum, songMids.length));
//        } else {
//            playSongMids = songMids;
//            mMusicInfo.setSongMids(null);
//        }
//        return playSongMids;
//    }
//
//    /**
//     * 初始化播放位置等
//     */
//    private void initPlayPosition() {
//        isSeekEvent = false;
//        mPrePositionTime = 0;
//        mCurrentPosition = 0;
//    }
//
//    /**
//     * 音乐播放器是否可用
//     */
//    public boolean isPlayerEnable() {
//        return isMusicPlayerEnable;
//    }
//
//    /**
//     * 获取当前播单列表
//     */
////    @Override
//    public ArrayList<PlayItem> getPlayList() {
//        return MusicListManager.getInstance().getPlayList();
//    }
//
////    @Override
//    public int getCurrentPlayListPosition() {
//        return MusicListManager.getInstance().getCurPosition();
//    }
//
////    @Override
//    public PlayItem getCurrentPlayItem() {
//        return MusicListManager.getInstance().getCurPlayItem();
//    }
//
////    @Override
//    public PlayItem getPlayItemByPosition(int position) {
//        ArrayList<PlayItem> playItemArrayList = getPlayList();
//        if (ListUtil.isEmpty(playItemArrayList)) {
//            return null;
//        }
//        int size = playItemArrayList.size();
//        if (size <= position) {
//            return null;
//        }
//        PlayItem playItem = playItemArrayList.get(position);
//        return playItem;
//    }
//
////    @Override
//    public String getRadioType() {
//        return String.valueOf(ResType.TYPE_QQ_MUSIC);
//    }
//
////    @Override
//    public String getRadioId() {
//        if (mMusicInfo == null) {
//            return null;
//        }
//        return String.valueOf(mMusicInfo.getId());
//    }
//
//    /**
//     * 获取播放歌曲的信息
//     */
//    public MusicInfo getMusicInfo() {
//        return mMusicInfo;
//    }
//
//    /**
//     * 保存当前信息
//     */
//    private void saveInfo(long id, int type, String name) {
//        mMusicInfo.clear();
//        mMusicInfo.setId(id);
//        mMusicInfo.setType(type);
//        if (!TextUtils.isEmpty(name)) {
//            mMusicInfo.setName(name);
//        }
//        //保存当前正在播放的歌曲集合id、类型、第几首等信息，用于断点续播
////        MusicResumePlayUtil.saveInfo(mMusicInfo);
//    }
//
//    /**
//     * 添加播放一个音频获取内容结果监听事件
//     */
//    public void addGetContentListener(GeneralCallback<Boolean> callback) {
//        if (mGetContentGeneralCallbacks == null) {
//            mGetContentGeneralCallbacks = new ArrayList<>();
//        }
//        if (mGetContentGeneralCallbacks.contains(callback)) {
//            return;
//        }
//        mGetContentGeneralCallbacks.add(callback);
//    }
//
//    /**
//     * 移除播放一个音频获取内容结果监听事件
//     */
//    public void removeGetContentListener(GeneralCallback<Boolean> callback) {
//        if (mGetContentGeneralCallbacks == null) {
//            return;
//        }
//        if (mGetContentGeneralCallbacks.contains(callback)) {
//            mGetContentGeneralCallbacks.remove(callback);
//        }
//    }
//
//    private void notifyGetContent(boolean result) {
//        if (mGetContentGeneralCallbacks == null) {
//            return;
//        }
//        ArrayList<GeneralCallback> generalCallbacks = (ArrayList<GeneralCallback>) mGetContentGeneralCallbacks
//                .clone();
//        for (int i = 0, size = generalCallbacks.size(); i < size; i++) {
//            GeneralCallback callback = generalCallbacks.get(i);
//            if (callback == null) {
//                continue;
//            }
//            callback.onResult(result);
//        }
//    }
//    //===============================数据上报=================================
//
////    /**
////     * 上报播放结束事件
////     *
////     * @param playItem 单曲对象
////     */
////    private void reportPlayEnd(PlayItem playItem) {
////        if (playItem == null) {
////            return;
////        }
////        if (isFirstPlay) {
////            isFirstPlay = PlayerManager.getInstance(mApplication).getIsFirstPlay();
////        }
////        CommonEvent event = new CommonEvent();
////        event.setEventCode(StatisticsManager.CommonEventCode.PLAY_FINISH);
////        event.setRadioid(mPreRadioId);
////        event.setAudioid(String.valueOf(playItem.getAudioId()));
////        event.setPlaytime(String.valueOf(mHavePlayedTime / SEC_DIVISOR + 1)); // 加1S误差
////        event.setEventType(playItem.getIsOffline() ? "1" : "0");
//////        event.setRemarks1(HostConstants.PLAYER_DEFAULT_REMARKS);
////        event.setRemarks2(String.valueOf(mAudioStartPlayTime / SEC_DIVISOR));
////        event.setRemarks4(isFirstPlay ? "1" : "0");
////        StatisticsManager.getInstance().reportEventToServer(event);
////        mHavePlayedTime = 0;
////        if (isFirstPlay) {
////            isFirstPlay = false;
////            PlayerManager.getInstance(mApplication).setIsFirstPlay(false);
////        }
////    }
//
//    /**
//     * 上报缓冲。服务器以此来判断是否卡顿
//     */
//    //  private void reportBuffering(boolean isBufferingStart) {
//
////    }
////
////    /**
////     * 上报完整收听
////     */
////    private void reportListenToEnd(PlayItem playItem) {
////        //只有私人fm进行上报
////        if (mMusicInfo.getType() == ResType.MUSIC_MINE_PRIVATE_FM) {
//////            mMusicRequest.reportListenToEnd(playItem.getPosition() / 1000, playItem.getAudioId(),
//////                    playItem.getRecommendReason());
////        }
////    }
//
//    /**
//     * 上报播放下一首
//     */
//    private void reportPlayNextSong(PlayItem playItem) {
//        if (mMusicInfo.getType() == ResType.MUSIC_MINE_PRIVATE_FM) {
////            mMusicRequest.reportPlayNextSong(playItem.getPosition() / 1000, playItem.getAudioId(),
////                    playItem.getRecommendReason());
//        }
//    }
//
//    private void reportStartListen(PlayItem playItem, int type, String radioId) {
//        if (playItem == null || StringUtil.isEmpty(playItem.getPlayUrl())) {
//            return;
//        }
//        PlayReportParameter playReportParameter = new PlayReportParameter();
//        String audioId = getAudioId(type);
//        if (!audioId.equals(ReportConstants.QQ_API_AUDIO_ID)) {
//            playReportParameter.setRadioid(radioId);
//        }
//        playReportParameter.setAudioid(audioId);
//        if (playItem.getIsOffline()) {
//            playReportParameter.setType("0");
//        }
//        playReportParameter.setTotalLength(playItem.getTotalDuration());
//        ReportUtil.reportQQMusicStartPlay(playReportParameter);
//    }
//
//    private static String getAudioId(int resType) {
//        switch (resType) {
//            case ResType.TYPE_MUSIC_RADIO_SCENE:
//            case ResType.TYPE_MUSIC_RADIO_LABEL:
//                return ReportConstants.QQ_RADIO_AUDIO_ID;
//            default:
//                break;
//        }
//        return ReportConstants.QQ_API_AUDIO_ID;
//    }
//}
