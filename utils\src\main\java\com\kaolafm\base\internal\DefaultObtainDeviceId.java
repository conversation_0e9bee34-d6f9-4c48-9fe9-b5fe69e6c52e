package com.kaolafm.base.internal;

import android.content.Context;
import android.os.Build;
import com.kaolafm.base.utils.MD5;
import java.util.UUID;

/**
 * 默认方式获取uuid
 * <AUTHOR>
 * @date 2019-06-17
 */
public class DefaultObtainDeviceId extends BaseObtainDeviceId {

    public DefaultObtainDeviceId() {
    }

    @Override
    protected String createUUID(Context context) {
        // If all else fails, if the user does have lower than API 9 (lower
        // than Gingerbread), has reset their device or 'Secure.ANDROID_ID'
        // returns 'null', then simply the ID returned will be solely based
        // off their Android device information. This is where the collisions
        // can happen.
        // Thanks http://www.pocketmagic.net/?p=1662!
        // Try not to use DISPLAY, HOST or ID - these items could change.
        // If there are collisions, there will be overlapping data
        int abiLen = 0;
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.KITKAT_WATCH) {
            String[] abis = Build.SUPPORTED_ABIS;
            for (String abi : abis) {
                if (abi != null) {
                    abiLen += abi.length();
                }
            }
        } else {
            abiLen = Build.CPU_ABI.length();
        }
        String devIdShort = "35" +
                (Build.BOARD.length() % 10) +
                (Build.BRAND.length() % 10) +
                (abiLen % 10) +
                (Build.DEVICE.length() % 10) +
                (Build.MANUFACTURER.length() % 10) +
                (Build.MODEL.length() % 10) +
                (Build.PRODUCT.length() % 10);

        // Thanks to @Roman SL!
        // http://stackoverflow.com/a/4789483/950427
        // Only devices with API >= 9 have android.os.Build.SERIAL
        // http://developer.android.com/reference/android/os/Build.html#SERIAL
        // If a user upgrades software or roots their device, there will be a duplicate entry
        String serial = null;
        String uuid;
        try {
            serial = Build.class.getField("SERIAL").get(null).toString();
            if (serial == null) {
                serial = "";
            }
            // Go ahead and return the serial for api => 9
            uuid = new UUID(devIdShort.hashCode(), serial.hashCode()).toString();
            return MD5.getMD5Str(uuid);
        } catch (Exception exception) {
            // String needs to be initialized
            serial = "serial";
        }

        // Thanks @Joe!
        // http://stackoverflow.com/a/2853253/950427
        // Finally, combine the values we have found by using the UUID class to create a unique identifier
        uuid = new UUID(devIdShort.hashCode(), serial.hashCode()).toString();
        return MD5.getMD5Str(uuid);
    }
}
