package com.kaolafm.opensdk.player.logic.model.item.model;

/**
 * 播放url- 数据类
 */
public class PlayUrlData {

    /**
     * 默认url
     */
    private String defaultPlayUrl;

    /**
     * AAC 32kbp/s码率播放地址
     */
    private String aacPlayUrl32;

    /**
     * AAC 64kbp/s码率播放地址
     */
    private String aacPlayUrl64;

    /**
     * AAC 128kbp/s码率播放地址
     */
    private String aacPlayUrl128;

    /**
     * AAC 320kbp/s码率播放地址
     */
    private String aacPlayUrl320;

    /**
     * mp3 32kbp/s码率播放地址
     */
    private String mp3PlayUrl32;

    /**
     * mp3 64kbp/s码率播放地址
     */
    private String mp3PlayUrl64;

    /**
     * mp3 128kbp/s码率播放地址
     */
    private String mp3PlayUrl128;

    /**
     * mp3 320kbp/s码率播放地址
     */
    private String mp3PlayUrl320;


    /**
     * 播放url
     */
    private String mp3PlayUrl;

    /**
     * 播放url
     */
    private String m3u8PlayUrl;

    public String getAacPlayUrl32() {
        return aacPlayUrl32;
    }

    public void setAacPlayUrl32(String aacPlayUrl32) {
        this.aacPlayUrl32 = aacPlayUrl32;
    }

    public String getAacPlayUrl64() {
        return aacPlayUrl64;
    }

    public void setAacPlayUrl64(String aacPlayUrl64) {
        this.aacPlayUrl64 = aacPlayUrl64;
    }

    public String getAacPlayUrl128() {
        return aacPlayUrl128;
    }

    public void setAacPlayUrl128(String aacPlayUrl128) {
        this.aacPlayUrl128 = aacPlayUrl128;
    }

    public String getMp3PlayUrl() {
        return mp3PlayUrl;
    }

    public void setMp3PlayUrl(String mp3PlayUrl) {
        this.mp3PlayUrl = mp3PlayUrl;
    }

    public String getM3u8PlayUrl() {
        return m3u8PlayUrl;
    }

    public void setM3u8PlayUrl(String m3u8PlayUrl) {
        this.m3u8PlayUrl = m3u8PlayUrl;
    }

    public String getAacPlayUrl320() {
        return aacPlayUrl320;
    }

    public void setAacPlayUrl320(String aacPlayUrl320) {
        this.aacPlayUrl320 = aacPlayUrl320;
    }

    public String getDefaultPlayUrl() {
        return defaultPlayUrl;
    }

    public void setDefaultPlayUrl(String defaultPlayUrl) {
        this.defaultPlayUrl = defaultPlayUrl;
    }

    public String getMp3PlayUrl32() {
        return mp3PlayUrl32;
    }

    public void setMp3PlayUrl32(String mp3PlayUrl32) {
        this.mp3PlayUrl32 = mp3PlayUrl32;
    }

    public String getMp3PlayUrl64() {
        return mp3PlayUrl64;
    }

    public void setMp3PlayUrl64(String mp3PlayUrl64) {
        this.mp3PlayUrl64 = mp3PlayUrl64;
    }

    public String getMp3PlayUrl128() {
        return mp3PlayUrl128;
    }

    public void setMp3PlayUrl128(String mp3PlayUrl128) {
        this.mp3PlayUrl128 = mp3PlayUrl128;
    }

    public String getMp3PlayUrl320() {
        return mp3PlayUrl320;
    }

    public void setMp3PlayUrl320(String mp3PlayUrl320) {
        this.mp3PlayUrl320 = mp3PlayUrl320;
    }
}
