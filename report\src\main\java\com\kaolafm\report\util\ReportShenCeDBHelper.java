package com.kaolafm.report.util;

import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.database.ReportData;
import com.kaolafm.report.database.greendao.DaoMaster;
import com.kaolafm.report.database.greendao.DaoSession;
import com.kaolafm.report.model.ReportBean;

import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.query.QueryBuilder;

import java.util.List;
import java.util.concurrent.Callable;

import io.reactivex.Single;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

/**
 * <AUTHOR> on 2019/1/9.
 */

public class ReportShenCeDBHelper {
    private static final String DATA_BASE_NAME = "reportData.db";
    private static ReportShenCeDBHelper reportShenCeDBHelper;
    private DaoSession daoSession;

    private ReportShenCeDBHelper() {
    }


    public static ReportShenCeDBHelper getInstance() {
        if (reportShenCeDBHelper == null) {
            synchronized (ReportShenCeDBHelper.class) {
                if (reportShenCeDBHelper == null) {
                    reportShenCeDBHelper = new ReportShenCeDBHelper();
                }
            }
        }
        return reportShenCeDBHelper;
    }

    public void init() {
        DaoMaster.DevOpenHelper devOpenHelper = new DaoMaster.DevOpenHelper(ReportHelper.getInstance().getContext(), DATA_BASE_NAME);
        Database database;
        try {
            database = devOpenHelper.getWritableDb();
        } catch (Exception e) {
            e.printStackTrace();
            return;
        }
        DaoMaster daoMaster = new DaoMaster(database);
        daoSession = daoMaster.newSession();
    }

    public Single<Long> insertData(String str) {
        if (isDaoSessionUnavailable()) {
            return null;
        }
        ReportData reportData = new ReportData();
        reportData.setSendStr(str);
        return Single.fromCallable(() -> {
            daoSession.insert(reportData);
            return daoSession.getReportDataDao().count();
        }).observeOn(AndroidSchedulers.mainThread())
                .subscribeOn(Schedulers.io());
    }

    public Single<Long> deleteDataList(List<Long> idList) {
        if (isDaoSessionUnavailable()) {
            return null;
        }
        return Single.fromCallable(() -> {
            daoSession.getReportDataDao().deleteByKeyInTx(idList);
            return daoSession.getReportDataDao().count();
        }).observeOn(AndroidSchedulers.mainThread()).subscribeOn(Schedulers.io());
    }

    public Single<ReportBean> read() {
        if (isDaoSessionUnavailable()) {
            return null;
        }
        return Single.fromCallable((Callable<List<ReportData>>) () -> {
            QueryBuilder queryBuilder = daoSession.queryBuilder(ReportData.class);
            return queryBuilder.limit(ReportConstants.READ_DATA_BASE_MAX_COUNT).list();
        }).map(reportData -> {
            ReportBean reportBean = new ReportBean();
            reportBean.setType(ReportConstants.UPLOAD_TASK_TYPE_BY_DATA_BASE);
            for (int i = 0; i < reportData.size(); i++) {
                reportBean.addData(reportData.get(i).getId(), reportData.get(i).getSendStr());
            }
            return reportBean;
        }).observeOn(AndroidSchedulers.mainThread()).subscribeOn(Schedulers.io());
    }

    private boolean isDaoSessionUnavailable() {
        return daoSession == null;
    }

    public void release() {
        if (isDaoSessionUnavailable()) {
            return;
        }
        daoSession.getDatabase().close();
    }
}
