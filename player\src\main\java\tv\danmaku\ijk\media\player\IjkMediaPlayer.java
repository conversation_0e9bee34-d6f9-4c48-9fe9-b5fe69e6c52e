/*
 * Copyright (C) 2006 The Android Open Source Project
 * Copyright (C) 2013 <PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package tv.danmaku.ijk.media.player;

import android.annotation.SuppressLint;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.view.Surface;
import android.view.SurfaceHolder;

import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;

import java.io.IOException;
import java.lang.ref.WeakReference;

import io.reactivex.Single;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import tv.danmaku.ijk.media.player.annotations.AccessedByNative;
import tv.danmaku.ijk.media.player.annotations.CalledByNative;


/**
 * <AUTHOR>
 * <p/>
 * Java wrapper of ffplay.
 */
public final class IjkMediaPlayer implements IJKConstants {
    private final static String TAG = "IjkMediaPlayer";

    private int mVideoWidth;
    private int mVideoHeight;

    private ijkPlayerCallBack mijkPlayerCallBack;

    @AccessedByNative
    private long mNativeMediaPlayer;

    @AccessedByNative
    private int mNativeSurfaceTexture;

    @AccessedByNative
    private int mListenerContext;

    @AccessedByNative
    private long mNativeMediaDataSource;
    @AccessedByNative
    private long mNativeAndroidIO;

    private SurfaceHolder mSurfaceHolder;
    private boolean mScreenOnWhilePlaying;
    private boolean mStayAwake;

    private static final IjkLibLoader sLocalLibLoader = System::loadLibrary;

    private static volatile boolean mIsLibLoaded = false;
    private Disposable disposable;

    public static void loadLibrariesOnce(IjkLibLoader libLoader, boolean force) {
        synchronized (IjkMediaPlayer.class) {
            if (!mIsLibLoaded || force) {
                try {
//                    libLoader.loadLibrary("kaolafmffmpeg");
                    libLoader.loadLibrary("kaolafmsdl");
                    libLoader.loadLibrary("kaolafmplayer");
                    libLoader.loadLibrary("kaolafmsoundtouch");
                    mIsLibLoaded = true;
                } catch (Throwable t) {
                    PlayerLogUtil.log(TAG, "load lib failure:" + t.getMessage());
                }
            }
        }
    }

    private static volatile boolean mIsNativeInitialized = false;

    private static void initNativeOnce(boolean force) {
        synchronized (IjkMediaPlayer.class) {
            if (!mIsNativeInitialized || force) {
                native_init();
                mIsNativeInitialized = true;
            }
        }
    }

    public IjkMediaPlayer() {
        this(sLocalLibLoader);
    }


    /**
     * do not loadLibaray
     *
     * @param libLoader
     */
    public IjkMediaPlayer(IjkLibLoader libLoader) {
        initPlayer(libLoader);
    }

    @SuppressLint("CheckResult")
    private void initPlayer(final IjkLibLoader libLoader) {
        if (disposable != null && disposable.isDisposed()) {
            PlayerLogUtil.log(TAG, "initPlayer do, so cancel this init");
            return;
        }
        disposable = Single.fromCallable(() -> {
                    loadLibrariesOnce(sLocalLibLoader, false);
                    return 1; // 返回值可以是 null，或其他符合您逻辑的值
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                        integer -> {
                            initNativeOnce(false);
                            native_setup(new WeakReference<>(IjkMediaPlayer.this));

                            if (mijkPlayerCallBack != null) {
                                mijkPlayerCallBack.message(MEDIA_IJK_SO_INIT_SUCCESS, 0, 0, 0);
                            }
                        },
                        throwable -> {
                            PlayerLogUtil.log(TAG, "Initialization error", throwable.getMessage());
                        }
                );
    }

    public void initPlayerForce() {
        if (disposable != null && !disposable.isDisposed()) {
            disposable.dispose();
        }
        disposable = Single.fromCallable(() -> {
                    loadLibrariesOnce(sLocalLibLoader, true);
                    return 1; // 返回值可以是 null，或其他符合您逻辑的值
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                        integer -> {
                            initNativeOnce(true);
                            native_setup(new WeakReference<>(IjkMediaPlayer.this));

                            if (mijkPlayerCallBack != null) {
                                mijkPlayerCallBack.message(MEDIA_IJK_SO_INIT_SUCCESS, 0, 0, 0);
                            }
                        },
                        throwable -> {
                            PlayerLogUtil.log(TAG, "Initialization error", throwable.getMessage());
                        }
                );
    }

    // 下面都是内部调用的底层方法
    @FunctionalInterface
    private interface MediaPlayerFunction {
        void call() throws Exception;
    }

    private void performAction(MediaPlayerFunction function) {
        if (!mIsNativeInitialized) {
            PlayerLogUtil.log(TAG, "IjkMediaPlayer is not initialized");
            return;
        }
        try {
            function.call();
        } catch (Exception e) {
            PlayerLogUtil.log(TAG, "IjkMediaPlayer error", e.getMessage());
        }
    }

    public void setLogInValid() {
        performAction(this::_setLogInValid);
    }

    public void setPlaybackRate(float rate) {
        performAction(() -> _setPlaybackRate(rate));
    }

    public float getPlaybackRate() {
        if (!mIsNativeInitialized) {
            PlayerLogUtil.log(TAG, "IjkMediaPlayer is not initialized");
            return 0;
        }
        return _getPlaybackRate();
    }

    public void setDisplay(SurfaceHolder sh) {
        this.mSurfaceHolder = sh;
        Surface surface;
        if (sh != null) {
            surface = sh.getSurface();
        } else {
            surface = null;
        }
        performAction(() -> _setVideoSurface(surface));
        this.updateSurfaceScreenOn();
    }

    public void setSurface(Surface surface) {
        if (this.mScreenOnWhilePlaying && surface != null) {
            PlayerLogUtil.log(TAG, "setScreenOnWhilePlaying(true) is ineffective for Surface");
        }

        this.mSurfaceHolder = null;
        performAction(() -> _setVideoSurface(surface));
        this.updateSurfaceScreenOn();
    }

    private void updateSurfaceScreenOn() {
        if (this.mSurfaceHolder != null) {
            this.mSurfaceHolder.setKeepScreenOn(this.mScreenOnWhilePlaying && this.mStayAwake);
        }
    }

    public void setDataSource(String path) throws Exception {
        performAction(() -> _setDataSource(path, null, null));
    }

    public void prepare(int needSeek, int streamTypeChannel) throws IllegalStateException {
        performAction(() -> _prepareAsync(needSeek, streamTypeChannel));
    }

    public void seekAtStart(long msec) {
        performAction(() -> _setSeekAtStart(msec));
    }

    public void setDuration(long urlDuration, long totalDuration) throws IllegalStateException {
        performAction(() -> _setDuration((int) urlDuration, (int) totalDuration));
    }

    public void prepareAsync() throws IllegalStateException {
        performAction(() -> this._prepareAsync(0, STREAM_MUSIC));
    }

    public void setOption(int category, String name, String value) {
        performAction(() -> this._setOption(category, name, value));
    }

    public void setOption(int category, String name, long value) {
        performAction(() -> this._setOption(category, name, value));
    }

    public void play() throws IllegalStateException {
        performAction(this::_start);
    }

    public void stop() throws IllegalStateException {
        performAction(this::_stop);
    }


    public void pause() throws IllegalStateException {
        performAction(this::_pause);
    }


    public void seek(long msec) throws Exception {
        performAction(() -> seekTo(msec));
    }

    public boolean isPlay() {
        if (!mIsNativeInitialized) {
            PlayerLogUtil.log(TAG, " IjkMediaPlayer is not initialized");
            return false;
        }
        return isPlaying();
    }

    public void release() {
        if (disposable != null && !disposable.isDisposed()) {
            disposable.dispose();
        }
        if (!mIsNativeInitialized) {
            PlayerLogUtil.log(TAG, "IjkMediaPlayer is not initialized");
            return;
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "release", "release");
        _release();
    }


    public void reset() throws Exception {
        if (!mIsNativeInitialized) {
            PlayerLogUtil.log(TAG, "IjkMediaPlayer is not initialized");
            return;
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "reset", "reset ");
        _stop();
        _reset();
    }


    public String getDnsAddress() {
        if (!mIsNativeInitialized) {
            PlayerLogUtil.log(TAG, "IjkMediaPlayer is not initialized");
            return null;
        }
        return _getDnsAdress();
    }

    public void setAudioStreamType(int streamtype) {
    }

    public void setAutoPlayOnPrepared(boolean enabled) {
        performAction(() -> _setAutoPlayOnPrepared(enabled));
    }

    public void setMediaVolume(float leftVolume, float rightVolume) {
        performAction(() -> setVolume(leftVolume, rightVolume));
    }

    /**
     * 设置AudioAttributes的ContentType属性
     *
     * @param content_type:AudioAttributes类中CONTENT_TYPE值
     * @return 无
     * 备注：在prepare()函数之前调用
     */
    public void setAttributesContentType(int content_type) {
        performAction(() -> _setAttributesContentType(content_type));
    }

    /**
     * 设置AudioAttributes的Usage属性
     *
     * @param usage:AudioAttributes类中USAGE值
     * @return 无
     * 备注：在prepare()函数之前调用才能生效
     */
    public void setAttributesUsage(int usage) {
        performAction(() -> _setAttributesUsage(usage));
    }

    /**
     * 设置调整岚图输出声道，1、2、3、4、5、6、7、8声道调整为1、2、3、5、6、7、8、4
     *
     * @param enable 1:设置。0：不设置，ffmepeg声道输出,默认不设置
     * @return 无
     */
    public void setLTChannelLayout(int enable) {
        performAction(() -> _setLTChannelLayout(enable));
    }

    /**
     * 设置IJKPlayer 音量均衡
     *
     * @param active 1 enable loudness normalization  0 disable loudness normalization
     */
    public void setLoudnessNormalization(int active) {
        performAction(() -> _setLoudnessNormalization(active));
    }

    // 原来的接口，没有提供的必要
//    public void setAvOption(AvFormatOption option) {
//        setAvFormatOption(option.getName(), option.getValue());
//    }

    public void setAvFormatOption(String name, String value) {
        performAction(() -> _setAvFormatOption(name, value));
    }

    public void setAvCodecOption(String name, String value) {
        performAction(() -> _setAvCodecOption(name, value));
    }

    public void setSwScaleOption(String name, String value) {
        performAction(() -> _setSwScaleOption(name, value));
    }

    /**
     * @param chromaFourCC AvFourCC.SDL_FCC_RV16 AvFourCC.SDL_FCC_RV32
     *                     AvFourCC.SDL_FCC_YV12
     */
    public void setOverlayFormat(int chromaFourCC) {
        performAction(() -> _setOverlayFormat(chromaFourCC));
    }

    /**
     * @param frameDrop =0 do not drop any frame <0 drop as many frames as possible >0
     *                  display 1 frame per `frameDrop` continuous dropped frames,
     */
    public void setFrameDrop(int frameDrop) {
        performAction(() -> _setFrameDrop(frameDrop));
    }

    public void setMediaCodecEnabled(boolean enabled) {
        performAction(() -> _setMediaCodecEnabled(enabled));
    }

    public void setOpenSLESEnabled(boolean enabled) {
        performAction(() -> _setOpenSLESEnabled(enabled));
    }

    public void setAudioFadeEnabled(boolean enabled) {
        performAction(() -> _setAudioFadeEnable(enabled ? 1 : 0));
    }

    /**
     * 设置音频淡入时长
     *
     * @param type 淡入类型（或类型的组合）0x11, 0x12, 0x13：播放开始淡入、Seek起播淡入、Pause起播淡入
     * @param msec 淡入时长，单位为毫秒（ms）
     */
    public void setAudioFadeInDuration(int type, long msec) {
        performAction(() -> _setAudioFadeInDuration(type, msec));
    }

    /**
     * 设置音频淡出时长
     *
     * @param type 淡出类型（或类型的组合）0x21, 0x22, 0x23：播放结束淡出、Stop起播淡出、Pause停播淡出
     * @param msec 淡出时长，单位为毫秒（ms）
     */
    public void setAudioFadeOutDuration(int type, long msec) {
        performAction(() -> _setAudioFadeOutDuration(type, msec));
    }

    public void setDnsAddress(String[] values) throws IOException, IllegalArgumentException,
            SecurityException, IllegalStateException {
        performAction(() -> _setDnsAddress(values));
    }

    public void clearDnsAddress() throws IOException, IllegalArgumentException, SecurityException, IllegalStateException {
        performAction(this::_clearDnsAddress);
    }

    /**
     * 2016.10.21
     * 清除代理方法
     */
    public void clearProxyAddress() {
        performAction(this::_clearProxyAdress);
    }

    /**
     * 2016.10.21
     * 设置代理方法
     */
    public void setProxyAddress(String values) throws IOException, IllegalArgumentException,
            SecurityException, IllegalStateException {
        performAction(() -> _setProxyAddress(values));
    }

    /**
     * 2021.1.5
     * 设置清除dns缓存方法
     */
    public void clearDnsCache(String values) throws IOException, IllegalArgumentException,
            SecurityException, IllegalStateException {
        performAction(() -> _clearDnsCache(values));
    }


    public Bundle getMediaMeta() {
        if (!mIsNativeInitialized) {
            PlayerLogUtil.log(TAG, "IjkMediaPlayer is not initialized");
            return null;
        }
        return _getMediaMeta();
    }

    public String getVideoCodecInfo() {
        if (!mIsNativeInitialized) {
            PlayerLogUtil.log(TAG, "IjkMediaPlayer is not initialized");
            return null;
        }
        return _getVideoCodecInfo();
    }

    public String getAudioCodecInfo() {
        if (!mIsNativeInitialized) {
            PlayerLogUtil.log(TAG, "IjkMediaPlayer is not initialized");
            return null;
        }
        return _getAudioCodecInfo();
    }

    protected void finalize() {
        native_finalize();
    }

    public void setIjkPlayerCallBack(ijkPlayerCallBack callBack) {
        this.mijkPlayerCallBack = callBack;
    }

    public ijkPlayerCallBack getIjkPlayerCallBack() {
        return mijkPlayerCallBack;
    }

    public int getVideoWidth() {
        return mVideoWidth;
    }

    public void setVideoWidth(int mVideoWidth) {
        this.mVideoWidth = mVideoWidth;
    }

    public int getVideoHeight() {
        return mVideoHeight;
    }

    public void setVideoHeight(int mVideoHeight) {
        this.mVideoHeight = mVideoHeight;
    }

    public boolean getCurrentFrame(Bitmap bitmap) {
        if (!mIsNativeInitialized) {
            PlayerLogUtil.log(TAG, "IjkMediaPlayer is not initialized");
            return false;
        }
        return _getCurrentFrame(bitmap);
    }

    public long getCurrentPos() {
        if (!mIsNativeInitialized) {
            PlayerLogUtil.log(TAG, "IjkMediaPlayer is not initialized");
            return 0;
        }
        return getCurrentPosition();
    }

    public long getDur() {
        if (!mIsNativeInitialized) {
            PlayerLogUtil.log(TAG, "IjkMediaPlayer is not initialized");
            return 0;
        }
        return getDuration();
    }

    /**
     * 本地方法调用上层方法.....
     */

    @CalledByNative
    private static void postDataFromNative(Object weakThiz, byte buffer[], int length) {
    }

    @CalledByNative
    private static void postEventFromNative(Object weakThiz, int what,
                                            int arg1, int arg2, Object obj) {
        if (weakThiz == null)
            return;

        @SuppressWarnings("rawtypes")
        IjkMediaPlayer mp = (IjkMediaPlayer) ((WeakReference) weakThiz).get();
        if (mp == null) {
            return;
        }

        if (what == MEDIA_INFO && arg1 == MEDIA_INFO_STARTED_AS_NEXT) {
            try {
                mp.play();
            } catch (IllegalStateException ill) {
                ill.printStackTrace();
            }
        }
        if (mp.getIjkPlayerCallBack() != null) {
            mp.getIjkPlayerCallBack().message(what, arg1, arg2, obj);
        }
    }

    @CalledByNative
    private static String onSelectCodec(Object weakThiz, String mimeType,
                                        int profile, int level) {
        return null;
    }


    @CalledByNative
    private static String onControlResolveSegmentUrl(Object weakThiz,
                                                     int segment) {
        return null;
    }

    @CalledByNative
    private static String onControlResolveSegmentOfflineMrl(Object weakThiz,
                                                            int segment) {
        return null;
    }

    @CalledByNative
    private static int onControlResolveSegmentDuration(Object weakThiz, int segment) {
        return 0;
    }

    @CalledByNative
    private static int onControlResolveSegmentCount(Object weakThiz) {
        return 0;
    }

    @CalledByNative
    private static boolean onNativeInvoke(Object weakThiz, int what, Bundle args) {
        return true;
    }

    // 下面是native方法，全部都应该是private方法，不应该被外部调用.

    /**
     * @throws IOException
     */
    private native void _clearDnsAddress() throws IOException, IllegalArgumentException,
            SecurityException, IllegalStateException;

    /**
     * 1,片花控制有效，0，片花控制无效
     *
     * @param active 是否有片花(广告)
     */
    private native void _setTitileActive(int active) throws IllegalStateException;

    private native long _getAudioFormat();

    private native long _getAudioSampleRate();

    private native long _getAudioChannels();

    private native void _setDataCallBack(long flag) throws IOException,
            IllegalArgumentException, SecurityException, IllegalStateException;

    /*
     * Update the IjkMediaPlayer SurfaceTexture. Call after setting a new
     * display surface.
     */
    private native void _setVideoSurface(Surface surface);

    private native boolean _getCurrentFrame(Bitmap bitmap);

    /**
     * 1 enable loudness normalization  0 disable loudness normalization
     *
     * @param active disable or enable loudness normalization
     */
    private native void _setLoudnessNormalization(int active) throws IllegalStateException;

    private static native String _getColorFormatName(
            int mediaCodecColorFormat);

    private static native void native_init();

    private native void native_setup(Object IjkMediaPlayer_this);

    private native void native_finalize();

    private native void native_message_loop(Object IjkMediaPlayer_this);

    private native void _setDnsAddress(String[] values) throws IOException, IllegalArgumentException,
            SecurityException, IllegalStateException;

    /**
     * 2016.07.22新加方法
     *
     * @return
     */
    private native String _getDnsAdress();

    /**
     * 2016.10.21
     * 清除代理方法
     */
    private native void _clearProxyAdress();

    /**
     * 2016.10.21
     * 设置代理方法
     */
    private native void _setProxyAddress(String values);

    /**
     * 2021.1.5
     * 设置清除dns缓存方法
     */
    private native void _clearDnsCache(String values);


    /**
     * @param urlduration 参数单位是以毫秒为精确度的时长
     */
    private native void _setDuration(int urlduration, int urlduraion_all) throws IllegalStateException;

    private native void _start() throws IllegalStateException;

    private native void _setDataSource(String path, String[] keys,
                                       String[] values) throws IOException, IllegalArgumentException,
            SecurityException, IllegalStateException;

    private native void _stop() throws IllegalStateException;

    private native void _setAvFormatOption(String name, String value);

    private native void _setAvCodecOption(String name, String value);

    private native void _setSwScaleOption(String name, String value);

    private native void _setOverlayFormat(int chromaFourCC);

    private native void _setFrameDrop(int frameDrop);

    private native void _setMediaCodecEnabled(boolean enabled);

    private native void _setOpenSLESEnabled(boolean enabled);

    private native Bundle _getMediaMeta();

    private native String _getVideoCodecInfo();

    private native String _getAudioCodecInfo();

    private native void _pause() throws IllegalStateException;

    private native void _prepareAsync(int needSeek, int stream_type_channel) throws IllegalStateException;

    private native void _setOption(int category, String name, String value);

    private native void _setOption(int category, String name, long value);

    private native void seekTo(long msec) throws IllegalStateException;

    private native boolean isPlaying();

    private native long getCurrentPosition();

    private native long getDuration();

    private native void _release();

    private native void _reset();

    private native void setVolume(float leftVolume, float rightVolume);

    /**
     * 设置播放速率的接口
     *
     * @param rate
     */
    private native void _setPlaybackRate(float rate);

    /**
     * 获取播放速率的接口
     */
    private native float _getPlaybackRate();

    /**
     * 起播前Seek（设置播放位置，默认从0开始播放）
     *
     * @param msec: 起播位置，单位毫秒
     */
    private native void _setSeekAtStart(long msec);

    /**
     * 自动播放（准备完后自动播放，默认自动播放）
     *
     * @param enable: True  功能开启
     *                False 功能关闭
     */
    private native void _setAutoPlayOnPrepared(boolean enable);

    /**
     * 控制IJK动态库中log信息是否可以输出如果调用则不可以输出，默认情况为输出
     */
    private native void _setLogInValid();

    /**
     * // 设置日志上报（使 FFmpeg 用 IJKPlayer 的日志函数进行日志输出，默认使用 brief 方式）
     *
     * @param useReport: 0 使用 brief  的方式（） 1 使用 report 的方式（使用 FFmpeg 定义的格式）
     */
    private native void _setLogReport(int useReport);

    /**
     * 设置日志等级（分模块）
     * 默认 LOG_MOUDLE_IJK_MEDIA 的日志等级为 IJK_LOG_DEFAULT
     * 默认 LOG_MOUDLE_IJK_LIBAV 的日志等级为 IJK_LOG_INFO
     *
     * @param moudle: LOG_MOUDLE_IJK_MEDIA、LOG_MOUDLE_IJK_LIBAV
     * @param level   : IJK_LOG_XXX
     */
    private native void _setLogLevel(int moudle, int level);

    /**
     * ijk sdk 版本
     *
     * @return
     */
    private native String _getLibVersion();

    /**
     * 是否启用音频淡入淡出功能
     *
     * @param enable: 0-不启用（默认）、1-开启
     */
    private native void _setAudioFadeEnable(int enable);

    /**
     * 设置音频淡入时长
     *
     * @param type 淡入类型（或类型的组合）0x11, 0x12, 0x13：播放开始淡入、Seek起播淡入、Pause起播淡入
     * @param msec 淡入时长，单位为毫秒（ms）
     */
    private native void _setAudioFadeInDuration(int type, long msec);

    /**
     * 设置音频淡出时长
     *
     * @param type 淡出类型（或类型的组合）0x21, 0x22, 0x23：播放结束淡出、Stop起播淡出、Pause停播淡出
     * @param msec 淡出时长，单位为毫秒（ms）
     */
    private native void _setAudioFadeOutDuration(int type, long msec);

    /**
     * 设置AudioAttributes的ContentType属性
     *
     * @param content_type:AudioAttributes类中CONTENT_TYPE值
     * @return 无
     */
    private native void _setAttributesContentType(int content_type);

    /**
     * 设置AudioAttributes的Usage属性
     *
     * @param usage:AudioAttributes类中USAGE值
     * @return 无
     */
    private native void _setAttributesUsage(int usage);

    /**
     * 设置调整岚图输出声道，1、2、3、4、5、6、7、8声道调整为1、2、3、5、6、7、8、4
     *
     * @param enable 1:设置, 0：不设置，ffmepeg声道输出,默认不设置
     * @return 无
     */
    private native void _setLTChannelLayout(int enable);


    /*
     * 下面这些Listeners并没有在使用，实际上相关接口定义在IMediaPlayer里面而非这里
     */
//    public interface OnPreparedListener {
//        void onPrepared(IjkMediaPlayer mp);
//    }
//
//    public interface OnCompletionListener {
//        void onCompletion(IjkMediaPlayer mp);
//    }
//
//    public interface OnBufferingUpdateListener {
//        void onBufferingUpdate(IjkMediaPlayer mp, int position, int duration);
//    }
//
//    public interface OnSeekCompleteListener {
//        void onSeekComplete(IjkMediaPlayer mp);
//    }
//
//    public interface OnVideoSizeChangedListener {
//        void onVideoSizeChanged(IjkMediaPlayer mp, int width, int height,
//                                int sar_num, int sar_den);
//    }
//
//    public interface OnErrorListener {
//        boolean onError(IjkMediaPlayer mp, int what, int extra);
//    }
//
//    public interface OnInfoListener {
//        boolean onInfo(IjkMediaPlayer mp, int what, int extra);
//    }

    public interface ijkPlayerCallBack {
        void message(int what, int arg1, int arg2, Object obj);
    }
}
