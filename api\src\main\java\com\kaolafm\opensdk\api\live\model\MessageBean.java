package com.kaolafm.opensdk.api.live.model;

/**
 * 客户端对发送和接收消息的统一封装，保存一条消息的关键信息，UI可以根据这些信息进行展示
 *
 * <AUTHOR>
 */
public class MessageBean {
    /**
     * 消息发送中
     */
    public static final int MSG_SENDING = 1;
    /**
     * 消息发送成功
     */
    public static final int MSG_SEND_SUCCESS = 2;
    /**
     * 消息发失败
     */
    public static final int MSG_SEND_FAILED = 3;

    public SendChatMsgData sendChatMsgData;
    /**
     * 消息唯一id
     */
    public String id;
    /**
     * 消息类型
     */
    public int type;
    public boolean isForbidden;
    /**
     * 是否被加入黑名单 true为是，false为否
     */
    public boolean isInBlackList;
    public int sendStatus;

    public String account;
    public String chatTime;
    public String userIconUrl;
    public String contentString;
    public String nickName;
    public String localThumbPath;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }


    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }


}