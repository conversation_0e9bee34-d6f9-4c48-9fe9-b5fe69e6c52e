package com.kaolafm.opensdk.account.profile;

import javax.inject.Inject;

/**
 * 考拉相关的配置信息，包括AppId, AppKey, AppType, Channel, PackageName, VersionName,DeviceId等
 *
 * <AUTHOR>
 * @date 2018/7/29
 */
public class KaolaProfile extends Profile {

    private String appKey;

    private String channel;

    private String packageName;

    private String deviceId;

    private String sdkVersionName;
    /**
     * 车型
     */
    private String carType;

    @Inject
    public KaolaProfile() {
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public String getAppKey() {
        return appKey;
    }

    public String getChannel() {
        return channel;
    }

    public String getPackageName() {
        return packageName;
    }

    public String getSdkVersionName() {
        return sdkVersionName;
    }

    public void setSdkVersionName(String sdkVersionName) {
        this.sdkVersionName = sdkVersionName;
    }

    public void setCarType(String carType) {
        this.carType = carType;
    }

    public String getCarType() {
        return carType;
    }
}
