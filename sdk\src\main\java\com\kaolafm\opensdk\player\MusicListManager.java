//package com.kaolafm.opensdk.player;
//
//import static com.kaolafm.sdk.core.mediaplayer.PlayerListManager.PLAY_MODE_LOOP_AUDIO;
//import static com.kaolafm.sdk.core.mediaplayer.PlayerListManager.PLAY_MODE_RANDOM_ALL;
//import static com.kaolafm.sdk.core.mediaplayer.PlayerListManager.PLAY_MODE_SEQUENCE_ALL;
//
//import android.text.TextUtils;
//import android.util.Log;
//import com.kaolafm.base.utils.ListUtil;
//import com.kaolafm.opensdk.api.music.qq.model.MusicInfo;
//import com.kaolafm.opensdk.api.music.qq.model.Song;
//import com.kaolafm.sdk.core.mediaplayer.IPlayerListChangedListener;
//import com.kaolafm.sdk.core.mediaplayer.PlayItem;
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 音乐播单管理
// *
// * <AUTHOR>
// * @date 2018/4/17
// */
//
//public class MusicListManager {
//    private static final String TAG = "MusicListManager";
//
//    private static final int INVALID_NUM = -1;
//
//    /**
//     * 播放列表
//     */
//    private ArrayList<PlayItem> mPlayList;
//
//    /**
//     * 用于存储播放历史列表
//     */
//    private List<Song> mSongList;
//
//    /**
//     * 当前播放索引
//     */
//    private int mCurPosition = INVALID_NUM;
//
//    /**
//     * 已经播放过的音频池针对随机播放设计
//     */
//    private ArrayList<PlayItem> mPlayedList = new ArrayList<>();
//    /**
//     * 未播放过的音频池针对随机播放设计
//     */
//    private ArrayList<PlayItem> mUnPlayedList = new ArrayList<>();
//    /**
//     * 当前音频播放模式
//     */
//    private int mCurrentPlayMode = PLAY_MODE_SEQUENCE_ALL;
//
//    /**
//     * 播单数据改变回调容器
//     */
//    private ArrayList<IPlayerListChangedListener> mIPlayerListChangedListener = new ArrayList<>();
//
//
//    private MusicListManager() {
//        mPlayList = new ArrayList<>();
//        mSongList = new ArrayList<>();
//    }
//
//    private static class MusicListManagerHolder {
//        private static final MusicListManager INSTANCE = new MusicListManager();
//    }
//
//    public static MusicListManager getInstance() {
//        return MusicListManagerHolder.INSTANCE;
//    }
//
//    /**
//     * 添加播放列表
//     */
//    public void addPlayList(ArrayList<PlayItem> playItems) {
//        mPlayList.addAll(new ArrayList<>(playItems));
//        notifyPlayerListChanged();
//    }
//
//    /**
//     * 添加到播放列表某个位置
//     *
//     * @param playItemList
//     */
//    public void addPlayList(int index, List<PlayItem> playItemList) {
//        if (index > mPlayList.size()) {
//            index = mPlayList.size();
//        }
//        mPlayList.addAll(index, new ArrayList<>(playItemList));
//        notifyPlayerListChanged();
//    }
//
//    /**
//     * 添加到song列表
//     *
//     * @param songList
//     */
//    public void addSongList(List<Song> songList) {
//        if (songList != null) {
//            mSongList.addAll(songList);
//        }
//    }
//
//    public void addSongList(int index, List<Song> songList) {
//        if (songList != null) {
//            if (index > mSongList.size()) {
//                index = mSongList.size();
//            }
//            mSongList.addAll(index, songList);
//        }
//    }
//
//
//    /**
//     * 重新设置播放列表
//     */
//    public void setPlayList(ArrayList<PlayItem> playList) {
//        this.mPlayList = playList;
//    }
//
//    public void setSongList(List<Song> songList) {
//        if (songList != null) {
//            mSongList = songList;
//        }
//    }
//
//    public void clearPlayList() {
//        mPlayList.clear();
//        mSongList.clear();
//
//        mPlayDirection = NO_DIRECTION;
//        mCurrentPlayMode = PLAY_MODE_SEQUENCE_ALL;
//    }
//
//    /**
//     * 删除播单中特定单曲对象
//     *
//     * @param playItem
//     * @param isCurrentPlayingItem 将要删除的单曲对象是否正在播放 true为是，false为否
//     * @return 当前被删除的单曲索引值
//     */
//    public int delPlayItem(PlayItem playItem, boolean isCurrentPlayingItem) {
//        if (playItem == null || ListUtil.isEmpty(mPlayList)) {
//            return INVALID_NUM;
//        }
//        for (int i = 0, size = mPlayList.size(); i < size; i++) {
//            if (mPlayList.get(i) != playItem) {
//                continue;
//            }
//            mPlayList.remove(i);
//            mPlayList.trimToSize();
//            deleteHistory(playItem.getAudioId());
//            int newSize = mPlayList.size();
//            if (newSize == 0) {
//                return INVALID_NUM;
//            }
//            if (mCurrentPlayMode == PLAY_MODE_RANDOM_ALL) {
//                if (mPlayedList.contains(playItem)) {
//                    mPlayedList.remove(playItem);
//                } else if (mUnPlayedList.contains(playItem)) {
//                    mUnPlayedList.remove(playItem);
//                }
//            } else {
//                if (isCurrentPlayingItem && i == newSize) {
////                    KLAutoPlayerManager.getInstance().reset();
//                    mCurPosition = INVALID_NUM;
//                    return 0;
//                }
//            }
//            Log.d(TAG, "delPlayItem-------->mCurPosition = " + mCurPosition + "---> i = " + i);
//            if (mCurPosition >= i) {
//                --mCurPosition;
//            }
//            return i;
//        }
//        return INVALID_NUM;
//    }
//
//    private void deleteHistory(long songId) {
////        mDaoManager.delete(songId);
//    }
//
//    /**
//     * 获取当前播放PlayItem
//     *
//     * @return
//     */
//    public PlayItem getCurPlayItem() {
//        if (mCurPosition >= 0 && mCurPosition < mPlayList.size()) {
//            return mPlayList.get(mCurPosition);
//        }
//        return null;
//    }
//
//    public Song getCurrentSong() {
//        if (mCurPosition >= 0 && mCurPosition < mSongList.size()) {
//            return mSongList.get(mCurPosition);
//        }
//        return null;
//    }
//
//    /**
//     * 获取当前播放播单位置
//     *
//     * @return
//     */
//    public int getCurPosition() {
//        return mCurPosition;
//    }
//
//    public PlayItem getPlayItemById(long id) {
//        if(this.mPlayList != null && this.mPlayList.size() != 0) {
//            for (int i = 0, size = mPlayList.size(); i < size; i++) {
//                PlayItem playItem = mPlayList.get(i);
//                if (playItem != null && id == playItem.getAudioId()) {
//                    return playItem;
//                }
//            }
//            return null;
//        } else {
//            return null;
//        }
//    }
//
//    /**
//     * 更新当前播单位置
//     *
//     * @param position
//     */
//    public void setCurPosition(int position) {
//        if (position < 0) {
//            mCurPosition = 0;
//        } else {
//            mCurPosition = position;
//        }
//    }
//
//    /**
//     * 是否存在上一曲目
//     *
//     * @return true为有上一曲目，false为没有上一曲目
//     */
//    boolean hasPre() {
//        if (mCurrentPlayMode == PLAY_MODE_RANDOM_ALL) {
//            return true;
//        }
//        if (mCurPosition == 0) {
//            if (canCarePlayModel()) {
//                return true;
//            }
//        }
//        boolean flag = mCurPosition > 0;
//        return flag;
//    }
//
//    /**
//     * 是否存在下一曲目
//     *
//     * @return true为有下一个曲目，false为没有下一个曲目
//     */
//    boolean hasNext() {
//        if (ListUtil.isEmpty(mPlayList)) {
//            return false;
//        }
//        if (mCurrentPlayMode == PLAY_MODE_RANDOM_ALL) {
//            return true;
//        }
//        int maxIndex = mPlayList.size() - 1;
//        if (mCurPosition == maxIndex) {
//            if (canCarePlayModel()) {
//                return true;
//            }
//        }
//        boolean flag = maxIndex > mCurPosition;
//        return flag;
//    }
//
//    /**
//     * 获取播放列表
//     *
//     * @return
//     */
//    public ArrayList<PlayItem> getPlayList() {
//        return mPlayList;
//    }
//
//    private static final int LEFT_DIRECTION = -1;
//    private static final int RIGHT_DIRECTION = 1;
//    private static final int NO_DIRECTION = 0;
//    /**
//     * -1 左向，1 右向，0 无方向
//     * 此变量针对随机模式起作用
//     */
//    private int mPlayDirection = NO_DIRECTION;
//
//    /**
//     * 获取下一个PlayItem
//     *
//     * @param bAutoPlayComplete 是否为自动播放完毕（true为是，false为否
//     *                          如果自动播放完毕且在单曲循环模式下会出处理单曲循环模式否则则视为用户触发，否则放弃此次播放模式处理）
//     * @return
//     */
//    public PlayItem getNextPlayItem(boolean bAutoPlayComplete) {
//        if (mCurrentPlayMode == PLAY_MODE_LOOP_AUDIO && bAutoPlayComplete) {
//            PlayItem playItem = getCurPlayItem();
//            if (playItem == null) {
//                return null;
//            }
//            if (!TextUtils.isEmpty(playItem.getPlayUrl())) {
//                playItem.setPosition(0);
//                return playItem;
//            }
//        } else if (mCurrentPlayMode == PLAY_MODE_RANDOM_ALL) {
////            int size = mUnPlayedList.size();
////            if (size == 0) {
////                initRandomPool();
////            }
////            PlayItem playItem = makeRandomPlayItem();
////            mPlayDirection = RIGHT_DIRECTION;
////            return playItem;
//            PlayItem playItem = null;
//            int size = mPlayedList.size();
//            if (size == 0) {
//                mPlayDirection = RIGHT_DIRECTION;
//            }
//            if (size > 0 && mPlayDirection != RIGHT_DIRECTION) {
//                PlayItem currentPlayItem = getCurPlayItem();
//                if (currentPlayItem != null && mPlayedList.contains(currentPlayItem)) {
//                    mPlayedList.remove(currentPlayItem);
//                }
//                size = mPlayedList.size();
//                if (size > 0) {
//                    playItem = mPlayedList.remove(size - 1);
//                } else {
//                    mPlayDirection = RIGHT_DIRECTION;
//                }
//            }
//            if (playItem == null) {
//                if (mUnPlayedList.size() == 0) {
//                    initRandomPool();
//                }
//                // 解决31243问题
//                playItem = makeRandomPlayItem();
//            }
//            return playItem;
//
//        }
//        if (!hasNext()) {
//            return null;
//        }
//        mCurPosition++;
//        int size = mPlayList.size();
//        if (mCurPosition >= size) {
//            if (canCarePlayModel()) {
//                mCurPosition = 0;
//            } else {
//                mCurPosition = size - 1;
//            }
//        }
//        return getCurPlayItem();
//    }
//
//    /**
//     * 获取上一个PlayItem
//     *
//     * @return
//     */
//    public PlayItem getPrePlayItem() {
//        if (mCurrentPlayMode == PLAY_MODE_RANDOM_ALL) {
//            PlayItem playItem = null;
//            int size = mPlayedList.size();
//            if (size == 0) {
//                mPlayDirection = LEFT_DIRECTION;
//            }
//            if (size > 0 && mPlayDirection != LEFT_DIRECTION) {
//                PlayItem currentPlayItem = getCurPlayItem();
//                if (currentPlayItem != null && mPlayedList.contains(currentPlayItem)) {
//                    mPlayedList.remove(currentPlayItem);
//                }
//                size = mPlayedList.size();
//                if (size > 0) {
//                    playItem = mPlayedList.remove(size - 1);
//                } else {
//                    mPlayDirection = LEFT_DIRECTION;
//                }
//            }
//            if (playItem == null) {
//                if (mUnPlayedList.size() == 0) {
//                    initRandomPool();
//                }
//                // 解决31243问题
//                playItem = makeRandomPlayItem();
//            }
//            return playItem;
//        }
////        Log.d(TAG, "getPrePlayItem-------->mCurPosition = " + mCurPosition);
//        if (mCurPosition > 0) {
//            --mCurPosition;
//        } else {
//            if (canCarePlayModel()) {
//                mCurPosition = mPlayList.size() - 1;
//            } else {
//                mCurPosition = 0;
//            }
//        }
//        return getCurPlayItem();
//    }
//
//    /**
//     * 根据当前播放音频同步原始播单游标
//     *
//     * @param playItem
//     */
//    public void setCurPlayItemIndex(PlayItem playItem) {
//        if (playItem == null) {
//            return;
//        }
//        ArrayList<PlayItem> tempArrayList = getPlayList();
//        for (int i = 0, size = tempArrayList.size(); i < size; i++) {
//            PlayItem orgPlayItem = tempArrayList.get(i);
//            if (orgPlayItem == null) {
//                continue;
//            }
//            if (orgPlayItem == playItem) {
//                mCurPosition = i;
//                break;
//            }
//        }
//    }
//
//    /**
//     * 获取当前播放模式（参考
//     * PlayerListManager.PLAY_MODE_SEQUENCE_ALL,
//     * PlayerListManager.PLAY_MODE_LOOP_AUDIO,
//     * PlayerListManager.PLAY_MODE_RANDOM_ALL
//     * ）
//     *
//     * @return
//     */
//    public int getCurrentPlayMode() {
//        return mCurrentPlayMode;
//    }
//
//    /**
//     * 是否为默认播放模式
//     *
//     * @return true为是，false为否
//     */
//    public boolean isDefaultMode() {
//        return mCurrentPlayMode == PLAY_MODE_SEQUENCE_ALL;
//    }
//
//    /**
//     * 设置播放模式
//     *
//     * @param currentMode
//     */
//    public void setCurrentPlayMode(int currentMode) {
//        if (currentMode == PLAY_MODE_RANDOM_ALL) {
//            mPlayDirection = NO_DIRECTION;
//            initRandomPool();
//        } else {
//            clearRandomPool();
//        }
//        this.mCurrentPlayMode = currentMode;
//    }
//
//    /**
//     * 初始化随机播放池
//     */
//    private void initRandomPool() {
//        clearRandomPool();
////        PlayItem playItem = getCurPlayItem();
//        mUnPlayedList.addAll(getPlayList());
////        if (mUnPlayedList.contains(playItem)) {
////            mUnPlayedList.remove(playItem);
////        }
//    }
//
////    /**
////     * 初始化随机播放池
////     */
////    private void reInitRandomPool() {
////        mPlayDirection = NO_DIRECTION;
////        clearRandomPool();
////        mUnPlayedList.addAll(getPlayList());
////    }
//
//    /**
//     * 初始化随机播放池
//     *
//     * @param playList 当前播单数据源
//     */
//    public void initRandomPool(List<PlayItem> playList) {
//        clearRandomPool();
//        PlayItem playItem = getCurPlayItem();
//        mUnPlayedList.addAll(playList);
//        if (mUnPlayedList.contains(playItem)) {
//            mUnPlayedList.remove(playItem);
//        }
//    }
//
//    /**
//     * 清除播放模式缓存列表
//     */
//    private void clearRandomPool() {
//        if (mPlayedList.size() > 0) {
//            mPlayedList.clear();
//        }
//        if (mUnPlayedList.size() > 0) {
//            mUnPlayedList.clear();
//        }
//    }
//
//    private PlayItem makeRandomPlayItem() {
//        int index = (int) (Math.random() * mUnPlayedList.size());
//        PlayItem playItem = null;
//        if (mUnPlayedList.size() > index) {
////            if (mPlayedList.size() == 0) {
////                PlayItem currentPlayItem = getCurPlayItem();
////                if (currentPlayItem != null) {
////                    mPlayedList.add(currentPlayItem);
////                }
////            }
//            playItem = mUnPlayedList.remove(index);
//            if (playItem != null) {
//                mPlayedList.add(playItem);
//            }
//        }
//        ArrayList<PlayItem> tempArrayList = getPlayList();
//        if (playItem == null) {
//            int size = tempArrayList.size();
//            if (size > mCurPosition && mCurPosition >= 0) {
//                playItem = tempArrayList.get(mCurPosition);
//            } else if (size == 1) {
//                playItem = tempArrayList.get(0);
//            }
//        }
//        if (playItem == null) {
//            return null;
//        }
//
//        setCurPlayItemIndex(playItem); // 同步总播单游标
//
//        return playItem;
//    }
//
//    public void registerPlayerListChangedListener(IPlayerListChangedListener iPlayerListChangedListener) {
//        if (mIPlayerListChangedListener.contains(iPlayerListChangedListener)) {
//            return;
//        }
//        mIPlayerListChangedListener.add(iPlayerListChangedListener);
//    }
//
//    public void unRegisterPlayerListChangedListener(IPlayerListChangedListener iPlayerListChangedListener) {
//        if (mIPlayerListChangedListener.contains(iPlayerListChangedListener)) {
//            mIPlayerListChangedListener.remove(iPlayerListChangedListener);
//        }
//    }
//
//    private void notifyPlayerListChanged() {
//        if (mIPlayerListChangedListener.size() == 0) {
//            return;
//        }
//        ArrayList<PlayItem> tempPlayList = getPlayList();
//        ArrayList<IPlayerListChangedListener> iPlayerListChangedListeners = (ArrayList<IPlayerListChangedListener>) mIPlayerListChangedListener.clone();
//        for (int i = 0, size = iPlayerListChangedListeners.size(); i < size; i++) {
//            IPlayerListChangedListener iPlayerListChangedListener = iPlayerListChangedListeners.get(i);
//            if (iPlayerListChangedListener == null) {
//                continue;
//            }
//            iPlayerListChangedListener.onPlayerListChanged(tempPlayList);
//        }
//    }
//
//    private boolean canCarePlayModel() {
//        if (MusicPlayerManager.getInstance().haveNextPage()) {
//            return false;
//        }
//        MusicInfo musicInfo = MusicPlayerManager.getInstance().getMusicInfo();
//        // 解决31226问题
//        if (musicInfo != null /*&& (
//                musicInfo.getType() == ResType.MUSIC_MINE_PRIVATE_FM
//                        ||musicInfo.getType() == ResType.TYPE_MUSIC_RADIO_SCENE
//                        ||musicInfo.getType() == ResType.TYPE_MUSIC_RADIO_LABEL)*/) {
//            return false;
//        } else if (mCurrentPlayMode == PLAY_MODE_SEQUENCE_ALL || mCurrentPlayMode == PLAY_MODE_LOOP_AUDIO) {
//            return true;
//        }
//        return false;
//    }
//
//}
