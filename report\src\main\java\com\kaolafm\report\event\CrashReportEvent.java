package com.kaolafm.report.event;


import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> on 2019/1/21.
 * 客户端异常上报
 */

public class CrashReportEvent extends BaseReportEventBean {

    /**
     * 状态码	 客户端可以自定义状态码，也可以不传
     */
    private String result;
    /**
     * 错误信息	程序抛出的Exception
     */
    private String message;

    public CrashReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_CRASH);
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
