package com.kaolafm.opensdk.api.operation.model.column;

import com.kaolafm.opensdk.api.operation.model.ImageFile;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 栏目成员父类。
 * <br></br>
 * 对于该类的子类一般操作可以使用{@link com.kaolafm.opensdk.utils.operation.OperationAssister}工具类。
 */
public abstract class ColumnMember implements Serializable {

    /**
     * 栏目成员的code值，用于获取子栏目成员。该值是可变的。
     */
    private String code;

    /**
     * 标题
     */
    private String title;

    /**
     * 副标题
     */
    private String subtitle;

    /**
     * 推荐理由
     */
    private String recommendReason;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否显示角标，1是，0否
     */
    private int cornerMark;

    /**
     * 图片信息集合
     */
    private Map<String, ImageFile> imageFiles;

    /**
     * 额外信息，用于一些定制需求
     */
    private Map<String, String> extInfo;

    /**
     * SDK内部使用，开发者不需要关心，会一直为空
     */
    private String type;

    /**
     * 组件类型
     * 1  上2下1组件
     * 2  轮播组件
     * 3  上2下3组件
     * 4  上1下1组件
     * 5  单内容大卡组件
     * 6  首页福利活动组件
     * 7  品牌入口组件
     * 10    圆形组件
     * 11    话题大卡组件
     * 12    话题小卡组件
     * 13    品牌主页大卡
     * 14    品牌主页 1+1
     * 15    活动类型组件
     */
    private int componentType;
    /**
     * 组件正文
     */
    private String mainBody;

    /**
     * 节目标题
     */
    private String programTitle;

    /**
     * 节目描述
     */
    private String programDesc;

    /**
     * 内容列表
     */
    private List<ColumnContent> contentList;
    /**
     * SDK内部使用，开发者不需要关心
     */
    private String callBack;
    /**
     * 成员标签
     */
    private String memberTag;

    /**
     * SDK内部使用，开发者不需要关心
     */
    private String outputMode;

    public String getMemberTag() {
        return memberTag;
    }

    public void setMemberTag(String memberTag) {
        this.memberTag = memberTag;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public String getRecommendReason() {
        return recommendReason;
    }

    public void setRecommendReason(String recommendReason) {
        this.recommendReason = recommendReason;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getCornerMark() {
        return cornerMark;
    }

    public void setCornerMark(int cornerMark) {
        this.cornerMark = cornerMark;
    }

    public Map<String, ImageFile> getImageFiles() {
        return imageFiles;
    }

    public void setImageFiles(Map<String, ImageFile> imageFiles) {
        this.imageFiles = imageFiles;
    }

    public Map<String, String> getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(Map<String, String> extInfo) {
        this.extInfo = extInfo;
    }

    public String getCallBack() {
        return callBack;
    }

    public void setCallBack(String callBack) {
        this.callBack = callBack;
    }

    public String getOutputMode() {
        return outputMode;
    }

    public void setOutputMode(String outputMode) {
        this.outputMode = outputMode;
    }

    public ImageFile getIcon() {
        if (imageFiles != null) {
            return imageFiles.get("icon");
        }
        return null;
    }

    public String getMainBody() {
        return mainBody;
    }

    public void setMainBody(String mainBody) {
        this.mainBody = mainBody;
    }

    public int getComponentType() {
        return componentType;
    }

    public void setComponentType(int componentType) {
        this.componentType = componentType;
    }

    public List<ColumnContent> getContentList() {
        return contentList;
    }

    public void setContentList(List<ColumnContent> contentList) {
        this.contentList = contentList;
    }

    public String getProgramTitle() {
        return programTitle;
    }

    public void setProgramTitle(String programTitle) {
        this.programTitle = programTitle;
    }

    public String getProgramDesc() {
        return programDesc;
    }

    public void setProgramDesc(String programDesc) {
        this.programDesc = programDesc;
    }

    @Override
    public String toString() {
        return "ColumnMember{" +
                "code='" + code + '\'' +
                ", title='" + title + '\'' +
                ", subtitle='" + subtitle + '\'' +
                ", recommendReason='" + recommendReason + '\'' +
                ", description='" + description + '\'' +
                ", cornerMark=" + cornerMark +
                ", imageFiles=" + imageFiles +
                ", extInfo=" + extInfo +
                ", type='" + type + '\'' +
                ", componentType=" + componentType +
                ", contentList=" + contentList +
                ", callBack='" + callBack + '\'' +
                ", outputMode='" + outputMode + '\'' +
                '}';
    }
}
