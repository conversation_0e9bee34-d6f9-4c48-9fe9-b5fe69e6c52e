package com.kaolafm.opensdk.api.operation.model.category;


/**
 * 人工运营分类成员:专题。
 */
public class FeatureCategoryMember extends CategoryMember {

    /** 专题id*/
    private long featureId;

    /** 专题的播放次数*/
    private int playTimes;

    public long getFeatureId() {
        return featureId;
    }

    public void setFeatureId(long albumId) {
        this.featureId = albumId;
    }

    public int getPlayTimes() {
        return playTimes;
    }

    public void setPlayTimes(int playTimes) {
        this.playTimes = playTimes;
    }

    @Override
    public String toString() {
        return "AlbumCategoryMember{" +
                "featureId=" + featureId +
                ", playTimes=" + playTimes +
                '}' + super.toString();
    }
}
