package com.kaolafm.opensdk.db.helper;

import com.kaolafm.opensdk.apt.DBOpt;
import com.kaolafm.opensdk.db.manager.BaseDBManager;
import com.kaolafm.opensdk.db.manager.GreenDaoManager;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.AbstractDaoSession;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;

/**
 * GreenDao工具类，用于获取实例
 * <AUTHOR>
 * @date 2020/8/24
 */
public class GreenDaoHelper {

//    private static final Map BINDINDS = new ConcurrentHashMap();

    public static <T, D extends AbstractDao<T, ?>> GreenDaoManager bind(BaseDBManager<T, D> manager) {
        Class<T> entryClass = (Class<T>) ((ParameterizedType) manager.getClass().getGenericSuperclass()).getActualTypeArguments()[0];
        Class<? extends BaseDBManager> aClass = manager.getClass();
        DBOpt annotation = aClass.getAnnotation(DBOpt.class);
        String name = annotation.name();
        GreenDaoManager m = null;
        try {
            Class<?> managerClass = aClass.getClassLoader().loadClass(aClass.getPackage().getName() + ".DaoManager");
            Method method = managerClass.getMethod("getInstance", String.class);
            m = (GreenDaoManager) method.invoke(null, name);
            AbstractDaoSession daoSession = m.getDaoSession();
            manager.mDaoSession = daoSession;
            manager.mDao = (D) daoSession.getDao(entryClass);
            manager.mAsyncSession = daoSession.startAsyncSession();
            manager.mEntityClass = entryClass;
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }
        return m;
    }

//    private static <D extends AbstractDao<T, ?>, T> void createBind(BaseDBManager<T, D> manager) {
//        Constructor constructor = findBindingConstructorForClass(manager.getClass());
//
//    }
//
//    private static Constructor findBindingConstructorForClass(Class<? extends BaseDBManager> aClass) {
//
//    }
//
//    public static AbstractDaoSession getDaoSession(BaseDBManager manager) {
//
//        return null;
//    }
}
