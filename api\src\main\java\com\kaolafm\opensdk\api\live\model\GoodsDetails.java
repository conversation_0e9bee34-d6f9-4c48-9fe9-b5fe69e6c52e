package com.kaolafm.opensdk.api.live.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.kaolafm.opensdk.api.media.model.PayMethod;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-02-22
 */
public class GoodsDetails implements Parcelable {
    private Long id; //商品序号
    private String name; // 商品名称
    private String picUrl; // 商品图片
    private String sellPoint; // 商品简介
    private Long marketPrice; // 商品原价.单位:分
    private Long salesPrice; // 商品秒杀/折扣/促销/跳楼价.单位:分
    private Integer shelf; // 状态.0-下架；1-上架
    private Integer pushType;//状态.是否讲解中：0-否；1-是
    private Integer stock; // 库存
    private String weight; // 重量
    private String volume; // 体积
    private Long shopId; // 店铺id
    private String description; // 详情介绍
    private List<PayMethod> payMethod; // 支付方法

    public GoodsDetails() {
    }

    protected GoodsDetails(Parcel in) {
        if (in.readByte() == 0) {
            id = null;
        } else {
            id = in.readLong();
        }
        name = in.readString();
        picUrl = in.readString();
        sellPoint = in.readString();
        if (in.readByte() == 0) {
            marketPrice = null;
        } else {
            marketPrice = in.readLong();
        }
        if (in.readByte() == 0) {
            salesPrice = null;
        } else {
            salesPrice = in.readLong();
        }
        if (in.readByte() == 0) {
            shelf = null;
        } else {
            shelf = in.readInt();
        }
        if (in.readByte() == 0) {
            pushType = null;
        } else {
            pushType = in.readInt();
        }
        if (in.readByte() == 0) {
            stock = null;
        } else {
            stock = in.readInt();
        }
        weight = in.readString();
        volume = in.readString();
        if (in.readByte() == 0) {
            shopId = null;
        } else {
            shopId = in.readLong();
        }
        description = in.readString();
        payMethod = in.createTypedArrayList(PayMethod.CREATOR);
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        if (id == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeLong(id);
        }
        dest.writeString(name);
        dest.writeString(picUrl);
        dest.writeString(sellPoint);
        if (marketPrice == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeLong(marketPrice);
        }
        if (salesPrice == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeLong(salesPrice);
        }
        if (shelf == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(shelf);
        }
        if (pushType == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(pushType);
        }
        if (stock == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(stock);
        }
        dest.writeString(weight);
        dest.writeString(volume);
        if (shopId == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeLong(shopId);
        }
        dest.writeString(description);
        dest.writeTypedList(payMethod);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<GoodsDetails> CREATOR = new Creator<GoodsDetails>() {
        @Override
        public GoodsDetails createFromParcel(Parcel in) {
            return new GoodsDetails(in);
        }

        @Override
        public GoodsDetails[] newArray(int size) {
            return new GoodsDetails[size];
        }
    };

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getSellPoint() {
        return sellPoint;
    }

    public void setSellPoint(String sellPoint) {
        this.sellPoint = sellPoint;
    }

    public Long getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(Long marketPrice) {
        this.marketPrice = marketPrice;
    }

    public Long getSalesPrice() {
        return salesPrice;
    }

    public void setSalesPrice(Long salesPrice) {
        this.salesPrice = salesPrice;
    }

    public Integer getShelf() {
        return shelf;
    }

    public void setShelf(Integer shelf) {
        this.shelf = shelf;
    }

    public Integer getPushType() {
        return pushType;
    }

    public void setPushType(Integer pushType) {
        this.pushType = pushType;
    }

    public Integer getStock() {
        return stock;
    }

    public void setStock(Integer stock) {
        this.stock = stock;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getVolume() {
        return volume;
    }

    public void setVolume(String volume) {
        this.volume = volume;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<PayMethod> getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(List<PayMethod> payMethod) {
        this.payMethod = payMethod;
    }
}
