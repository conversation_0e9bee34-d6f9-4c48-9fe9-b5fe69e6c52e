package com.kaolafm.opensdk.account.token;

import android.os.Parcel;
import android.text.TextUtils;

import com.kaolafm.opensdk.api.music.qq.LoginType;
import com.kaolafm.opensdk.api.music.qq.model.TencentLoginResult;
import com.kaolafm.base.utils.DateUtil;
import java.util.Date;
import javax.inject.Inject;

/**
 * QQ音乐的鉴权相关的数据，OpenId、UnionId、musicId、musicKey、refreshTime、expiresTime、LoginType
 *
 * <AUTHOR>
 * @date 2018/8/9
 */
public class QQMusicAccessToken implements AccessToken {

    @Inject
    public QQMusicAccessToken() {
    }

    public QQMusicAccessToken(long musicId, String musicKey, String token, String openId, String refreshToken,
            Date expiresTime, String unionId, String appId, int loginType) {
        this.musicId = musicId;
        this.musicKey = musicKey;
        this.token = token;
        this.openId = openId;
        this.refreshToken = refreshToken;
        this.expiresTime = expiresTime;
        this.unionId = unionId;
        this.appId = appId;
        this.loginType = loginType;
    }

    /**
     * 音乐账号id，微信登录使用
     */
    private long musicId;

    /**
     * 音乐登录访问凭证，微信登录使用
     */
    private String musicKey;

    /**
     * 微信用户接口调用凭证；微信登录不需要，QQ登录要用到
     */
    private String token;

    /**
     * 微信用户授权用户唯一标识；QQ用户登录标识
     */
    private String openId;

    /**
     * 微信用户填写通过wx_access_token获取到的wx_refresh_token参数； QQ用户登录刷新凭证
     */
    private String refreshToken;

    /**
     * 到期时间
     */
    private Date expiresTime;

    /**
     * 用户标识，对微信开发者帐号唯一
     */
    private String unionId;

    /**
     * 音乐open api在开放互联（open.qq.com）注册的appid
     */
    private String appId;

    /**
     * 登录方式，微信登录还是qq登录
     */
    @LoginType
    private int loginType;

    protected QQMusicAccessToken(Parcel in) {
        musicId = in.readLong();
        musicKey = in.readString();
        token = in.readString();
        openId = in.readString();
        refreshToken = in.readString();
        unionId = in.readString();
        appId = in.readString();
        loginType = in.readInt();
    }

    public static final Creator<QQMusicAccessToken> CREATOR = new Creator<QQMusicAccessToken>() {
        @Override
        public QQMusicAccessToken createFromParcel(Parcel in) {
            return new QQMusicAccessToken(in);
        }

        @Override
        public QQMusicAccessToken[] newArray(int size) {
            return new QQMusicAccessToken[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(musicId);
        dest.writeString(musicKey);
        dest.writeString(token);
        dest.writeString(openId);
        dest.writeString(refreshToken);
        dest.writeString(unionId);
        dest.writeString(appId);
        dest.writeInt(loginType);
    }

    @Override
    public boolean isLogin() {
        return musicId != 0 && !TextUtils.isEmpty(musicKey);
    }

    @Override
    public void logout() {
        clear();
    }

    @Override
    public void clear() {
        setMusicId(0);
        setMusicKey(null);
        loginType = 0;
    }

    @Override
    public boolean isExpires() {
        return new Date(DateUtil.getServerTime()).after(getExpiresTime());
    }

    public long getMusicId() {
        return musicId;
    }

    public void setMusicId(long musicId) {
        this.musicId = musicId;
    }

    public String getMusicKey() {
        return musicKey;
    }

    public void setMusicKey(String musicKey) {
        this.musicKey = musicKey;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public Date getExpiresTime() {
        return expiresTime;
    }

    public void setExpiresTime(Date expiresTime) {
        this.expiresTime = expiresTime;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public int getLoginType() {
        return loginType;
    }

    public void setLoginType(@LoginType int loginType) {
        this.loginType = loginType;
    }

    public QQMusicAccessToken setCurrentAccessToken(TencentLoginResult loginResult) {
        if (loginResult != null) {
            this.setAppId(loginResult.getAppId());
            this.setLoginType(loginResult.getLoginType());
            this.setMusicId(loginResult.getMusicId());
            this.setMusicKey(loginResult.getMusicKey());
            this.setOpenId(loginResult.getOpenId());
            this.setToken(loginResult.getToken());
            this.setRefreshToken(loginResult.getRefreshToken());
            this.setUnionId(loginResult.getUnionId());
            this.setExpiresTime(new Date(loginResult.getRefreshTime() * 1000 + DateUtil.getServerTime()));
        }
        return this;
    }

    public QQMusicAccessToken refreshToken(TencentLoginResult loginResult) {
        if (loginResult != null) {
            this.musicId = loginResult.getMusicId();
            this.musicKey = loginResult.getMusicKey();
            this.loginType = loginResult.getLoginType();
            this.expiresTime = new Date(loginResult.getRefreshTime() * 1000 + DateUtil.getServerTime());
        }
        return this;
    }
}
