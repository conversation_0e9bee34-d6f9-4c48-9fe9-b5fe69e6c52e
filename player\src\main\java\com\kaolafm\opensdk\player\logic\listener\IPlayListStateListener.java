package com.kaolafm.opensdk.player.logic.listener;

import com.kaolafm.opensdk.player.logic.model.item.InvalidPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.List;

/**
 * <AUTHOR> on 2019/3/19.
 */

public interface IPlayListStateListener {
    void onPlayListChange(List<PlayItem> playItemList);

    void onPlayListChangeError(PlayItem playItem, int errorCode, int errorExtra);
}
