package com.kaolafm.opensdk.player.logic.playcontrol;

import android.util.Log;

import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.media.model.AudioFileInfo;
import com.kaolafm.opensdk.api.media.model.ToneQuality;
import com.kaolafm.opensdk.player.core.ijk.VideoView;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.model.PlayItemConstants;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.item.TVPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.TimeInfoData;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.opensdk.player.logic.util.PlayerPreconditions;

import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.Flowable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;

/**
 * 听电视播放控制 do
 * <AUTHOR> shi qian
 */
public class TVPlayControl extends BasePlayControl {
    private final String TAG = TVPlayControl.class.getSimpleName();
    /**
     * 等待获取下一首广播时间
     */
    //private static final int WAIT_TIMER = 10000;
    private long progressTime;
    private long startTime;
    private long endTime;
    private long curServiceTime;
    private long livingTotalTime;
    private Disposable mDisposable;
    private IPlayerStateListener mIPlayerStateListener;

    //是否正在播放直播节目
    private boolean isPlayLiving = false;
    private boolean isPaused = false;


    @Override
    public void setPlayStateListener(BasePlayStateListener iPlayerStateListener) {
        super.setPlayStateListener(iPlayerStateListener);
        mIPlayerStateListener = iPlayerStateListener;
    }

    public TVPlayControl() {
        super();
    }

    @Override
    public void notifyPlayItemChangToOtherType() {
        super.notifyPlayItemChangToOtherType();
        Log.i(TAG, "notifyPlayItemChangToOtherType");
        mPlayItem = null;
        isPlayLiving = false;
        stopTimer();
    }

    @Override
    public void preStart(PlayItem playItem) {
        super.preStart(playItem);
        if (mPlayItem != null && playItem != null && !mPlayItem.getRadioId().equals(playItem.getRadioId())) {
            //如果调用start播放时与之前正播的广播不是同一个电台
            //用于解决播放检修台A的下一个电台B时手动切换到A由于检修又自动播放B时音频不能播放的问题
            mPlayItem = null;
            isPlayLiving = false;
            stopTimer();
        }
    }

    @Override
    public void start(int type, PlayItem playItem, VideoView videoView, boolean isPlayNow) {
        Log.i(TAG, "=== IPlayControl start()");
        boolean isIgnorePlay = mPlayItem instanceof TVPlayItem    //正在播放的是广播节目
                && playItem instanceof TVPlayItem    //想要播放的是广播节目
                && mPlayItem.getRadioId().equals(playItem.getRadioId())   //正在播放的是否是同一个广播节目
                && isPlayLiving //是否正在播放直播节目
                && playItem.isLiving()  //要播放的节目是不是直播
                ;
        if (isIgnorePlay) {
            //上面的验证通过，下面就应该验证播放地址是否相同，否则重新拉流
            String playUrl = getPlayUrl(mPlayItem, ((TVPlayItem) mPlayItem).getPlayInfoList());
            String newPlayUrl = getPlayUrl(playItem, ((TVPlayItem) playItem).getPlayInfoList());
            isIgnorePlay = playUrl != null && playUrl.equals(newPlayUrl);
        }
        mPlayItem = playItem;
        if (playItem.getType() != PlayerConstants.RESOURCES_TYPE_TV) {
            stopTimer();
            return;
        }
        TVPlayItem playItemTemp = (TVPlayItem) playItem;
        if (playItemTemp.getStatus() == PlayerConstants.BROADCAST_STATUS_LIVING) {
            isPlayLiving = true;
            TimeInfoData timeInfoData = playItemTemp.getTimeInfoData();
            curServiceTime = timeInfoData.getCurSystemTime();
            startTime = timeInfoData.getStartTime();
            endTime = timeInfoData.getFinishTime();
            livingTotalTime = endTime - startTime;
            progressTime = curServiceTime - startTime;
            if (progressTime < 0) {
                progressTime = 0;
            }
            PlayerLogUtil.log(getClass().getSimpleName(), "start", "live count time: curServiceTime" + curServiceTime + ", startTime = " + timeInfoData.getStartTime() + ", endTime = " + timeInfoData.getEndTime());
            startTimer();
        } else {
            isPlayLiving = false;
            stopTimer();
        }
        boolean tvAutoPlayToNextItem = isTvAutoPlayToNextItem();
        if (isInterceptTvAutoPlay()) {
            PlayerLogUtil.log(getClass().getSimpleName(), "start", "intercept normal play");
            setPlayUrl(mPlayItem.getPlayUrl(), 0, 0, getVideoView());
            mIPlayerStateListener.onPlayerPaused(mPlayItem);
        } else {
            if (tvAutoPlayToNextItem && isIgnorePlay){
                PlayerLogUtil.log(getClass().getSimpleName(), "start", "tv-living auto play next and ignore restart");
                mIPlayerStateListener.onPlayerPlaying(playItem);
                return;
            }
            if (playItemTemp.getTimeInfoData().getStartTime() > DateUtil.getServerTime()) {
                mIPlayerStateListener.onPlayerFailed(playItem, 404, 404);
                return;
            }
            super.start(type, playItem, videoView, isPlayNow);
        }
    }

    @Override
    ToneQuality getToneQuality(PlayItem playItem, List<AudioFileInfo> playListUrlInfos) {
        return null;
    }

    @Override
    void requestPlayUrl(PlayItem playItem, OnGetPlayUrlData callback) {
        TVPlayItem tvPlayItem = (TVPlayItem) playItem;
        if (tvPlayItem.isLiving()) {
            setPlayUrl(playItem, tvPlayItem.getPlayInfoList());
        } else {
            setPlayUrl(playItem, tvPlayItem.getBackPlayInfoList());
        }
        callback.onDataGet(tvPlayItem.getPlayUrl());
    }

    @Override
    String getPlayItemResourceType(PlayItem playItem) {
        return "m3u8";
    }

    private boolean isInterceptTvAutoPlay() {
        if (mPlayItem.getType() != PlayerConstants.RESOURCES_TYPE_TV) {
            return false;
        }
        String type = mPlayItem.getMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY);
        if (!StringUtil.isEmpty(type)) {
            mPlayItem.removeMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY);
            return true;
        }
        return false;
    }


    private boolean isTvAutoPlayToNextItem() {
        if (mPlayItem.getType() != PlayerConstants.RESOURCES_TYPE_BROADCAST) {
            return false;
        }
        String type = mPlayItem.getMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_AUTO_PLAY_NEXT_ITEM);
        if (!StringUtil.isEmpty(type)) {
            mPlayItem.removeMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_AUTO_PLAY_NEXT_ITEM);
            return true;
        }
        return false;
    }


    private void notifyProgress(long progressTime, long total) {
        if (PlayerPreconditions.checkNull(mIPlayerStateListener)) {
            return;
        }
        // Log.i(PlayerConstants.LOG_PROGRESS_TAG, "广播 直播通知 进度 : progressTime: " + progressTime + " total = "+ total);
        mIPlayerStateListener.onProgress(mPlayItem, (int) progressTime, (int) total);
    }

    private void notifyPause() {
        if (PlayerPreconditions.checkNull(mIPlayerStateListener)) {
            return;
        }
        mIPlayerStateListener.onPlayerPaused(mPlayItem);
    }

    private void notifyPlayEnd() {
        if (PlayerPreconditions.checkNull(mIPlayerStateListener)) {
            return;
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "notifyPlayEnd");
        mIPlayerStateListener.onPlayerEnd(mPlayItem);
    }

    private void startTimer() {
        stopTimer();
        mDisposable = Flowable.interval(1, TimeUnit.SECONDS).onBackpressureDrop()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(aLong -> onTimer());
    }

    @Override
    public void reset(boolean needResetLastPlaybackRateFlag) {
        super.reset(needResetLastPlaybackRateFlag);
        isPaused = false;
    }

    @Override
    public void rePlay() {
        super.rePlay();
        isPaused = false;
    }

    @Override
    public void play() {
        Log.i(TAG, "=== IPlayControl play()");
        if (isPaused) {
            rePlay();
        } else {
            super.play();
        }
        isPaused = false;
    }

    @Override
    public void pause() {
        Log.i(TAG, "=== IPlayControl pause()");
        super.pause();
        isPaused = true;
    }

    private void onTimer() {
        progressTime = progressTime + 1000;
        // Log.i(PlayerConstants.LOG_PROGRESS_TAG, "直播倒计时时间为: " + progressTime + " 总时间: " + livingTotalTime);
        if (progressTime > livingTotalTime) {
            ((TVPlayItem) mPlayItem).setStatus(PlayerConstants.BROADCAST_STATUS_PLAYBACK);
            // 清除过时的直播URL，避免数据不一致
            String oldUrl = mPlayItem.getPlayUrl();
            mPlayItem.setPlayUrl(null);
            PlayerLogUtil.log(getClass().getSimpleName(), "onTimer", "TV play end, status changed to PLAYBACK, clear old live url: " + oldUrl);
            mPlayItem.addMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_AUTO_PLAY_NEXT_ITEM, "1");
            if (isPlaying()) {
//                reset();
//                pause();
                mPlayItem.removeMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY);
            } else {
                PlayerLogUtil.log(getClass().getSimpleName(), "onTimer", "TV play end, is pause, loading data");
                mPlayItem.addMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY, "1");
            }
            notifyPlayEnd();
            stopTimer();
        } else {
            if (isPlaying()) {
                notifyProgress(progressTime, livingTotalTime);
            }
        }
    }


    public void stopTimer() {
        if (mDisposable != null && !mDisposable.isDisposed()) {
            mDisposable.dispose();
            mDisposable = null;
        }
    }


    @Override
    public void release() {
        super.release();
        isPaused = false;
        stopTimer();
    }
}
