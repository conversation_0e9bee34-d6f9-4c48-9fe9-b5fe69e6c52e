package com.kaolafm.opensdk.permission;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.location.Location;
import android.location.LocationManager;
import android.os.Build;
import android.os.Handler;
import android.os.Message;
import android.util.Log;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.kaolafm.opensdk.OpenSDK;
import com.kaolafm.opensdk.di.component.ComponentKit;

import java.util.List;

public class LocationUtil {
    public static LocationUtil instance = new LocationUtil();
    private boolean isSetLocation;//是否使用外部传入经纬度
    private LocationManager locationManager;
    private String locationProvider;
    private final int MESSAGE_CODE = 1100;//Handler消息code
    private final long MESSAGE_TIME = 10 * 60 * 1000;//Handler延时时间
    private Handler handler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (isSetLocation) {
                //如果外部传入，拦截不在执行
                handler.removeMessages(MESSAGE_CODE);
                return;
            }
            if (msg.what == MESSAGE_CODE && locationManager != null) {
                try {
                    if (ActivityCompat.checkSelfPermission(ComponentKit.getInstance().getApplication(), Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(ComponentKit.getInstance().getApplication(), Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                        // TODO: Consider calling
                        //    ActivityCompat#requestPermissions
                        // here to request the missing permissions, and then overriding
                        //   public void onRequestPermissionsResult(int requestCode, String[] permissions,
                        //                                          int[] grantResults)
                        // to handle the case where the user grants the permission. See the documentation
                        // for ActivityCompat#requestPermissions for more details.
                        return;
                    }
                    Location location = locationManager.getLastKnownLocation(locationProvider);
                    if (location != null) {
                        updateLocation(location);
                    }
                } catch (Exception e) {
                    Log.e("Location", e.getMessage());
                }
                handler.sendEmptyMessageDelayed(MESSAGE_CODE, MESSAGE_TIME);
            }
        }
    };
    /**
     * 外部传入经纬度
     *
     * @param longitude 经度
     * @param latitude  纬度
     */
    public void setLocation(String longitude, String latitude) {
        this.isSetLocation = true;
        if (handler != null) {
            handler.removeMessages(MESSAGE_CODE);
        }
        //手动设置过经纬度就不在更新位置信息，只能通过手动再设置
        OpenSDK.getInstance().getmEngine().setLocation(longitude, latitude);
    }

    /**
     * SDKd定位更新经纬度
     *
     * @param longitude 经度
     * @param latitude  纬度
     */
    private void setLocationDefault(String longitude, String latitude) {
        if (!isSetLocation)
            OpenSDK.getInstance().getmEngine().setLocation(longitude, latitude);

    }

    public void getLocation() {
        //1.获取位置管理器
        locationManager = (LocationManager) ComponentKit.getInstance().getApplication().getSystemService(Context.LOCATION_SERVICE);

        //2.获取位置提供器，GPS或是NetWork
        List<String> providers = locationManager.getProviders(true);

        if (providers.contains(LocationManager.GPS_PROVIDER)) {
            //如果是GPS
            locationProvider = LocationManager.GPS_PROVIDER;
            Log.v("Location", "定位方式GPS");
        } else if (providers.contains(LocationManager.NETWORK_PROVIDER)) {
            //如果是Network
            locationProvider = LocationManager.NETWORK_PROVIDER;
            Log.v("Location", "定位方式Network");
        } else {
            Log.v("Location", "没有可用的位置提供器");
            locationProvider = LocationManager.NETWORK_PROVIDER;
            if (!handler.hasMessages(MESSAGE_CODE))
                handler.sendEmptyMessage(MESSAGE_CODE);
            return;
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            //获取权限（如果没有开启权限，会弹出对话框，询问是否开启权限）
            if (ContextCompat.checkSelfPermission(ComponentKit.getInstance().getApplication(), Manifest.permission.ACCESS_FINE_LOCATION)
                    != PackageManager.PERMISSION_GRANTED ||
                    ContextCompat.checkSelfPermission(ComponentKit.getInstance().getApplication(), Manifest.permission.ACCESS_COARSE_LOCATION)
                            != PackageManager.PERMISSION_GRANTED) {
                Log.v("Location", "----------getLocation() 未开启权限----------");
                return;
            } else {
                //3.获取上次的位置，一般第一次运行，此值为null
                inLocation(locationManager);
            }
        } else {
            inLocation(locationManager);
        }
    }

    private void inLocation(LocationManager locationManager) {
        if (ContextCompat.checkSelfPermission(ComponentKit.getInstance().getApplication(), Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED
                && ContextCompat.checkSelfPermission(ComponentKit.getInstance().getApplication(), Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            // TODO: Consider calling
            //    ActivityCompat#requestPermissions
            // here to request the missing permissions, and then overriding
            //   public void onRequestPermissionsResult(int requestCode, String[] permissions,
            //                                          int[] grantResults)
            // to handle the case where the user grants the permission. See the documentation
            // for ActivityCompat#requestPermissions for more details.
            Log.v("Location", "----------inLocation() 未开启权限----------");
            return;
        }
        Location location = locationManager.getLastKnownLocation(locationProvider);
        if (location != null) {
//            Toast.makeText(getContext(), "定位成功--经纬度：" + location.getLongitude() + "   " + location.getLatitude() + "---" + locationProvider, Toast.LENGTH_LONG).show();
            updateLocation(location);

        } else {
            if (location == null && locationProvider.equals(LocationManager.GPS_PROVIDER)) {
                Log.v("location", "GPS定位失败，切换为网络定位");
                locationProvider = LocationManager.NETWORK_PROVIDER;
                inLocation(locationManager);
                return;
            }
        }
        try {
            Log.v("Location", "----------inLocation() 添加监听----------");
            //监视地理位置变化，第二个和第三个参数分别为更新的最短时间minTime和最短距离minDistace
//            minDistance参数还可用于控制位置更新的频率.如果它大于0,则位置提供者将仅在位置至少改变minDistance米时向您的应用程序发送更新,并且至少已经过了minTime毫秒
            if (!handler.hasMessages(MESSAGE_CODE))
                handler.sendEmptyMessage(MESSAGE_CODE);
        } catch (Exception e) {
            Log.e("location", "-------------------定位失败-----------------:" + locationProvider);
        }

    }

    /**
     * 更新位置刷新
     *
     * @param location 经纬度信息
     */
    private void updateLocation(Location location) {
        if (isSetLocation) {
            //使用外部传入经纬度，停止sdk默认定位
            Log.v("location", "---------------------updateLocation()----有外部位置传入，默认定位不在更新");
            if (handler != null) {
                handler.removeMessages(MESSAGE_CODE);
            }
            return;
        } else {
            Log.e("location", "-----updateLocation()---: " + location.getLatitude() + "--" + location.getLongitude() + "--" + locationProvider);
            double mLatitude = location.getLatitude();
            double mLongitude = location.getLongitude();
            setLocationDefault(String.valueOf(mLongitude), String.valueOf(mLatitude));
        }
    }
    public void getLocation(boolean isSetLocation){
        if (isSetLocation) {
            //使用外部传入经纬度，停止sdk默认定位
            Log.v("location", "---------------------updateLocation()----有外部位置传入，默认定位不在更新");
        } else {
            String[] arr = OpenSDK.getInstance().getmEngine().getLocation();
            Log.e("location", "-----updateLocation()---: " + arr[0] + "--" + arr[1] + "--" + locationProvider);
        }
    }
    public void release(){
        if (handler != null) {
            handler.removeMessages(MESSAGE_CODE);
            handler = null;
        }
    }
}
