package com.kaolafm.opensdk.demo.live.chat;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import com.kaolafm.opensdk.demo.DemoApplication;
import com.kaolafm.opensdk.demo.live.ui.LivePresenter;
import com.kaolafm.opensdk.demo.live.ui.UserInfoManager;
import com.kaolafm.opensdk.demo.live.util.AESUtil;
import com.netease.nimlib.sdk.NIMClient;
import com.netease.nimlib.sdk.Observer;
import com.netease.nimlib.sdk.RequestCallback;
import com.netease.nimlib.sdk.StatusCode;
import com.netease.nimlib.sdk.auth.AuthService;
import com.netease.nimlib.sdk.auth.AuthServiceObserver;
import com.netease.nimlib.sdk.auth.LoginInfo;
import com.netease.nimlib.sdk.chatroom.ChatRoomMessageBuilder;
import com.netease.nimlib.sdk.chatroom.ChatRoomService;
import com.netease.nimlib.sdk.chatroom.ChatRoomServiceObserver;
import com.netease.nimlib.sdk.chatroom.constant.MemberType;
import com.netease.nimlib.sdk.chatroom.model.ChatRoomKickOutEvent;
import com.netease.nimlib.sdk.chatroom.model.ChatRoomMember;
import com.netease.nimlib.sdk.chatroom.model.ChatRoomMessage;
import com.netease.nimlib.sdk.chatroom.model.ChatRoomNotificationAttachment;
import com.netease.nimlib.sdk.chatroom.model.ChatRoomStatusChangeData;
import com.netease.nimlib.sdk.chatroom.model.EnterChatRoomData;
import com.netease.nimlib.sdk.chatroom.model.EnterChatRoomResultData;
import com.netease.nimlib.sdk.msg.constant.MsgTypeEnum;
import com.netease.nimlib.sdk.msg.constant.NotificationType;
import com.netease.nimlib.sdk.msg.constant.SessionTypeEnum;
import com.netease.nimlib.sdk.msg.model.IMMessage;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class NimManager {

    public static final String TAG = "NimManager";

    private Context mContext;

    /**
     * 未登录即加入聊天室异常错误
     */
    public static final int UNLOGIN_TO_JOIN_CHATROOM_ERROR = 1000;

    /**
     * 服务异常错误码
     */
    public static final int SERVICE_ERROR_CODE = 50802;

    /**
     * 未知Host异常错误码
     */
    public static final int UNKNOWNHOST_ERROR_CODE = 50803;

    /**
     * 用户区分UDID、UID和TOKEN的分隔符
     */
    private static final String SPECIAL_DELIMITER_STR = "#";

    /**
     * 加入聊天室使用的扩展字段（昵称）key值
     */
    public static final String NICK_NAME_KEY = "nickName";
    /**
     * 加入聊天室使用的扩展字段（头像）key值
     */
    public static final String AVATAR_KEY = "avatar";


    /**
     * 黑名单操作相关数据类型
     */
    public final static int MSG_TYPE_BLACKLIST = 5;

    /**
     * 禁言操作相关数据类型
     */
    public final static int MSG_TYPE_MUTE = 6;

    /**
     * 接收普通文本消息
     */
    public final static int MSG_TYPE_RECEIVE = 0;

    /**
     * 发送普通文本消息
     */
    public final static int MSG_TYPE_SEND = 1;


    private static final int MEMBER_IN_MUTED_LIST_ERROR_CODE = 13004;

    /**
     * 当前聊天室ID
     */
    private String mRoomId;

    /**
     * 当前登录用户是否已经被踢出房间 true为是，false为否
     */
    private boolean isUserKickOut;

    private String mAccount;
    private String mNickName;

    /**
     * 当前用户是否被禁言
     */
    private boolean isVoiceRevoked;

    private boolean bChatRoomEntered;

    private boolean bNimInited;

    private LivePresenter mPresenter;

    /**
     * 当前用户被踢出房间监听
     */
    private OnUserKickOutChatRoomListener mOnUserKickOutChatRoomListener;

    private ArrayList<OnChatMessageReceivedListener> mOnChatMessageReceivedListenerList
            = new ArrayList<OnChatMessageReceivedListener>();
    private ArrayList<RoomMemberChangedObserver> mRoomMemberChangedObservers
            = new ArrayList<RoomMemberChangedObserver>();


    private NimManager() {
        mContext = DemoApplication.appContext;
    }

    private static final class INSTANCE_HOLDER {
        private static final NimManager INSTANCE = new NimManager();
    }

    public static NimManager getInstance() {
        return INSTANCE_HOLDER.INSTANCE;
    }

    public void setLivePresenter(LivePresenter presenter) {
        mPresenter = presenter;
    }

    public void initNim() {
        if (!bNimInited) {
            NIMClient.initSDK();
            bNimInited = true;
        }
    }

    public void enterChatRoom(Context context, String rid, NimManager.EnterChatRoomListener listener) {
        String roomId = String.valueOf(rid);
        StatusCode statusCode = NIMClient.getStatus();
        boolean login = UserInfoManager.getInstance().isUserLogin();
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "enterChatRoom roomId: " + rid + ", mRoomId: " + mRoomId
                    + ", statusCode: "+ statusCode + ", login: " + login
                    + ", bChatRoomEntered: " + bChatRoomEntered);
        }
        if (bChatRoomEntered) {
            //如果已经在聊天室，并且要进入的是同一个聊天室，返回，如果不是，先退出前一个聊天室
            if (roomId.equals(mRoomId)) {
                return;
            } else {
                exitChatRoom(false);
            }
        }
        mRoomId = roomId;
        if (!login) {
            mAccount = null;
            mNickName = null;
            return;
        }
        if (isUserKickOut) {
            logoutNim();
        }
        if (statusCode != StatusCode.LOGINED || TextUtils.isEmpty(mAccount)) {
            UserInfoManager userSetting = UserInfoManager.getInstance();
            if (userSetting.isUserLogin()) {
                String[] values = getAccountAndTokenByUID();
                if (values != null && values.length == 4) {
                    mAccount = values[1];
                    mNickName = values[3];
                    LoginInfo loginInfo = new LoginInfo(values[1], values[2]);
                    startLoginToNim(loginInfo, roomId, listener);
                } else { // 联网获取account和token
                    registerToImAndAutoEnterRoom(roomId, listener);
                }
            }
        } else {
            listener.loginSuccess(mAccount);
            joinChatRoom(context, roomId, listener);
        }
    }

    private void registerToImAndAutoEnterRoom(String roomId, EnterChatRoomListener enterChatRoomListener) {
        mPresenter.registerToNim(roomId, enterChatRoomListener);
    }

    private String[] getAccountAndTokenByUID() {
        UserInfoManager userSetting = UserInfoManager.getInstance();
        if (!userSetting.isUserLogin()) {
            return null;
        }
        String value = getNimUIDAndTokenAccountValue();
        String uid = userSetting.getUserId();
        return getAccountAndTokenById(uid, value);
    }

    private String[] getAccountAndTokenById(String id, String value) {
        if (TextUtils.isEmpty(value) || id == null) {
            return null;
        }
        String[] values = value.split(SPECIAL_DELIMITER_STR);
        if (values == null || values.length != 4) {
            return null;
        }
        if (id.equals(values[0])) {
            return values;
        }
        return null;
    }

    private String getNimUIDAndTokenAccountValue() {

        String value = UserInfoManager.getInstance().getLivingUidToken();
        if (TextUtils.isEmpty(value)) {
            return null;
        }
        try {
            value = AESUtil.decode(value, AESUtil.AES_KEY);
        } catch (Exception e) {
        }
        return value;
    }

    public boolean isChatRoomEntered() {
        return bChatRoomEntered;
    }

    public void setChatRoomEntered(boolean chatRoomEntered) {
        this.bChatRoomEntered = chatRoomEntered;
    }

    /**
     * 退出Nim服务器（注销用户）
     * 注意：不要在 Activity(Fragment) 的 onDestroy 方法中调用
     */
    public void logoutNim() {
        if (TextUtils.isEmpty(mAccount)) {
            return;
        }
        StatusCode statusCode = NIMClient.getStatus();
        if (statusCode.shouldReLogin()) {
            return;
        }
        unRegisterOnLineStatus();
        NIMClient.getService(AuthService.class).logout();
        mAccount = null;
        mNickName = null;
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "logoutNim---------------->start");
        }
    }

    /**
     * 注销当前用户在线状态监听
     */
    private void unRegisterOnLineStatus() {
        NIMClient.getService(AuthServiceObserver.class).observeOnlineStatus(mStatusObserver, false);
    }

    private Observer<StatusCode> mStatusObserver = new Observer<StatusCode>() {
        @Override
        public void onEvent(StatusCode statusCode) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.d(TAG, "statusCode-------->statusCode: " + statusCode.toString());
            }
            if (statusCode == StatusCode.KICKOUT ||
                    statusCode == StatusCode.KICK_BY_OTHER_CLIENT ||
                    statusCode == StatusCode.FORBIDDEN) {
                managerUserKickOut();
            } else if (statusCode == StatusCode.PWD_ERROR) { // 密码错误
                clearNimUIDAndTokenAccountValue(mContext);
            }
        }
    };

    public void startLoginToNim(LoginInfo loginInfo, final String roomId,
                                final EnterChatRoomListener enterChatRoomListener) {
        RequestCallback<LoginInfo> callback = new RequestCallback<LoginInfo>() {
            @Override
            public void onSuccess(LoginInfo loginInfo) {
                if (LivePresenter.DEBUG_LIVE) {
                    Log.d(TAG, "startLoginToNim onSuccess "
                            + loginInfo == null ? null : loginInfo.getAccount());
                }
                if (UserInfoManager.getInstance().isUserLogin()) {
                    if (loginInfo != null) {
                        setNimUIDAndTokenAccountValue(loginInfo.getAccount(), loginInfo.getToken());
                    }
                }
                registerKickOutEvent();
                joinChatRoom(mContext, roomId, enterChatRoomListener);
                if (enterChatRoomListener != null) {
                    enterChatRoomListener.loginSuccess(mAccount);
                }
            }

            @Override
            public void onFailed(int code) {
                Log.d(TAG, "login onFailed code: " + code);
                if (code == 302 || code == 404) {
                    clearNimUIDAndTokenAccountValue(mContext);
                }
                if (enterChatRoomListener != null) {
                    enterChatRoomListener.loginFailed(code);
                }
            }

            @Override
            public void onException(Throwable throwable) {
                if (enterChatRoomListener != null) {
                    enterChatRoomListener.onException(throwable);
                }
            }
        };
        NIMClient.getService(AuthService.class).login(loginInfo).setCallback(callback);
    }

    public MessageBean sendTextChatMsg(SendChatMsgData sendChatMsgData, final SendMsgListener sendMsgListener) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "sendTextChatMsg");
        }
        ChatRoomMessage message = ChatRoomMessageBuilder.createChatRoomTextMessage(sendChatMsgData.sessionId, sendChatMsgData.content);
        final MessageBean messageBean = new MessageBean();
        messageBean.account = mAccount;
        messageBean.sendChatMsgData = sendChatMsgData;
        messageBean.sendStatus = MessageBean.MSG_SENDING;
        messageBean.contentString = sendChatMsgData.content;
        messageBean.type = MSG_TYPE_SEND;
        messageBean.chatTime = String.valueOf(System.currentTimeMillis());
        messageBean.isForbidden = isVoiceRevoked;
        messageBean.nickName = UserInfoManager.getInstance().getNickName();
        Map<String, Object> mapData = new HashMap<String, Object>();
        if (!TextUtils.isEmpty(messageBean.nickName)) {
            mapData.put(NICK_NAME_KEY, messageBean.nickName);
        }
        if (!TextUtils.isEmpty(messageBean.userIconUrl)) {
            mapData.put(AVATAR_KEY, messageBean.userIconUrl);
        }
        if (mapData.size() > 0) {
            message.setRemoteExtension(mapData);
        }

        NIMClient.getService(ChatRoomService.class).sendMessage(message, true).setCallback(new RequestCallback<Void>() {
            @Override
            public void onSuccess(Void aVoid) {
                messageBean.sendStatus = MessageBean.MSG_SEND_SUCCESS;
                if (sendMsgListener != null) {
                    sendMsgListener.onSendSuccess(messageBean);
                }
            }

            @Override
            public void onFailed(int i) {
                if (i == MEMBER_IN_MUTED_LIST_ERROR_CODE) { // 解决19377问题
                    messageBean.isForbidden = true;
                    isVoiceRevoked = true;
                }
                messageBean.sendStatus = MessageBean.MSG_SEND_FAILED;
                if (sendMsgListener != null) {
                    sendMsgListener.onSendFailed(messageBean);
                }
            }

            @Override
            public void onException(Throwable throwable) {
                messageBean.sendStatus = MessageBean.MSG_SEND_FAILED;
                if (sendMsgListener != null) {
                    sendMsgListener.onSendFailed(messageBean);
                }
            }
        });
        return messageBean;
    }

    /**
     * 进入聊天室
     */
    private void joinChatRoom(final Context context, String roomId, final EnterChatRoomListener enterChatRoomListener) {
        EnterChatRoomData data = new EnterChatRoomData(roomId);
        Log.d(TAG, "joinChatRoom------------------>roomId: " + roomId);
        NIMClient.getService(ChatRoomService.class).enterChatRoom(data).setCallback(
                new RequestCallback<EnterChatRoomResultData>() {
                    @Override
                    public void onSuccess(EnterChatRoomResultData o) {
                        Log.d(TAG, "enterChatRoom success");
                        registerMsgReceiveListener();
                        registerChatRoomOnlineStatus();
                        NimUserInfoCache.getInstance().registerObservers();
                        if (enterChatRoomListener != null) {
                            enterChatRoomListener.enterChatRoomSuccess(o);
                        }
                    }

                    @Override
                    public void onFailed(int code) {
                        Log.d(TAG, "enterChatRoom onFailed code:" + code);
                        if (UNLOGIN_TO_JOIN_CHATROOM_ERROR == code) {
                            logoutNim();
                            clearNimUIDAndTokenAccountValue(context);
                        }
                        if (enterChatRoomListener != null) {
                            enterChatRoomListener.enterChatRoomFailed(code);
                        }
                    }

                    @Override
                    public void onException(Throwable throwable) {
                        if (enterChatRoomListener != null) {
                            enterChatRoomListener.onException(throwable);
                        }
                    }
                }
        );
    }

    private void setNimUIDAndTokenAccountValue(String account, String token) {
        UserInfoManager userSetting = UserInfoManager.getInstance();
        if (userSetting.isUserLogin()) {
            String uid = userSetting.getUserId();
            String tempValue = uid + SPECIAL_DELIMITER_STR + account
                    + SPECIAL_DELIMITER_STR + token + SPECIAL_DELIMITER_STR + mNickName;
            try {
                tempValue = AESUtil.encode(tempValue, AESUtil.AES_KEY);
            } catch (Exception e) {
                Log.e(TAG, "getNimUDIDAndTokenAccountValue---------->error", e);
            }
            UserInfoManager.getInstance().setLivingUidToken(tempValue);
            return;
        }
        UserInfoManager.getInstance().setLivingUidToken("");
    }

    private void managerUserKickOut() {
        if (!isUserKickOut) {
            notifyUserKickOut();
        }
        isUserKickOut = true;
        onUserInfoInvalid(mContext);
    }

    /**
     * 处理用户token失效问题
     */
    public void onUserInfoInvalid(Context context) {
        if (mRoomId != null) {
            exitChatRoom();
        }
        logoutNim();
        clearNimUIDAndTokenAccountValue(context);
    }

    /**
     * 针对登录用户切换处理逻辑
     *
     * @param context
     */
    private void clearNimUIDAndTokenAccountValue(Context context) {
        UserInfoManager.getInstance().setLivingUidToken("");
    }

    private void notifyUserKickOut() {
        if (mOnUserKickOutChatRoomListener != null) {
            mOnUserKickOutChatRoomListener.onKickOut();
        }
    }


    public void exitChatRoom() {
        exitChatRoom(true);
    }

    public void exitChatRoom(boolean clearObserver) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "exitChatRoom");
        }
        isUserKickOut = false;
        bChatRoomEntered = false;
        NIMClient.getService(ChatRoomService.class).exitChatRoom(mRoomId);
        mRoomId = null;
        if (clearObserver) {
            if (mRoomMemberChangedObservers.size() > 0) {
                mRoomMemberChangedObservers.clear();
            }
            if (mOnChatMessageReceivedListenerList.size() > 0) {
                mOnChatMessageReceivedListenerList.clear();
            }
        }
        unRegisterChatRoomOnlineStatus();
        unRegisterMsgReceiveListener();
        unRegisterKickOutEvent();
        ChatRoomMemberCache.getInstance().clear();
        NimUserInfoCache nimUserInfoCache = NimUserInfoCache.getInstance();
        nimUserInfoCache.clear();
        nimUserInfoCache.unRegisterObservers();
    }

    private void unRegisterMsgReceiveListener() {
        NIMClient.getService(ChatRoomServiceObserver.class).observeReceiveMessage(mIncomingMessageObserver, false);
    }

    private void registerKickOutEvent() {
        NIMClient.getService(ChatRoomServiceObserver.class).observeKickOutEvent(kickOutObserver, true);
    }

    private void unRegisterKickOutEvent() {
        NIMClient.getService(ChatRoomServiceObserver.class).observeKickOutEvent(kickOutObserver, false);
    }

    private void registerChatRoomOnlineStatus() {
        NIMClient.getService(ChatRoomServiceObserver.class).observeOnlineStatus(mChatRoomChangeDataObserver, true);
    }

    private void unRegisterChatRoomOnlineStatus() {
        NIMClient.getService(ChatRoomServiceObserver.class).observeOnlineStatus(mChatRoomChangeDataObserver, false);
    }

    private Observer<ChatRoomStatusChangeData> mChatRoomChangeDataObserver = new Observer<ChatRoomStatusChangeData>() {

        @Override
        public void onEvent(ChatRoomStatusChangeData chatRoomStatusChangeData) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.d(TAG, "statusCode: " + chatRoomStatusChangeData == null
                        ? null : chatRoomStatusChangeData.status == null
                        ? null : chatRoomStatusChangeData.status.toString());
            }
        }
    };

    private Observer<ChatRoomKickOutEvent> kickOutObserver = new Observer<ChatRoomKickOutEvent>() {
        @Override
        public void onEvent(ChatRoomKickOutEvent chatRoomKickOutEvent) {
            if (!TextUtils.isEmpty(mRoomId)) {
                Toast.makeText(mContext, chatRoomKickOutEvent.getReason().getValue(),
                        Toast.LENGTH_SHORT).show();
            }
            managerUserKickOut();
        }
    };

    /**
     * 注册聊天室消息及通知监听
     */
    private void registerMsgReceiveListener() {
        NIMClient.getService(ChatRoomServiceObserver.class).observeReceiveMessage(mIncomingMessageObserver, true);
    }

    private Observer<List<ChatRoomMessage>> mIncomingMessageObserver = new Observer<List<ChatRoomMessage>>() {
        @Override
        public void onEvent(List<ChatRoomMessage> messages) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.d(TAG, "mIncomingMessageObserver onEvent");
            }
            if (messages == null || messages.isEmpty()) {
                return;
            }

            for (int i = 0, size = messages.size(); i < size; i++) {
                ChatRoomMessage msg = messages.get(i);
                if (msg == null) {
                    continue;
                }
                MsgTypeEnum msgTypeEnum = msg.getMsgType();
                if (LivePresenter.DEBUG_LIVE) {
                    Log.d(TAG, "mIncomingMessageObserver msgTypeEnum: " + msgTypeEnum);
                }
                if (msgTypeEnum == MsgTypeEnum.notification) { // 通知消息
                    handleNotification(msg);
                } else if (msgTypeEnum == MsgTypeEnum.text) { // 文本消息
                    MessageBean data = new MessageBean();
                    data.account = msg.getFromAccount();
                    data.contentString = msg.getContent();
                    data.chatTime = String.valueOf(msg.getTime());
                    data.type = MSG_TYPE_RECEIVE;
                    Map<String, Object> extension = msg.getRemoteExtension();
                    if (extension != null) {
                        if (extension.containsKey(AVATAR_KEY)) {
                            data.userIconUrl = (String) extension.get(AVATAR_KEY);
                        }
                        if (extension.containsKey(NICK_NAME_KEY)) {
                            data.nickName = (String) extension.get(NICK_NAME_KEY);
                        }
                    }
                    updateMsgListData(data);
                } else if (msgTypeEnum == MsgTypeEnum.image) { // 图片消息
                } else if (msgTypeEnum == MsgTypeEnum.custom) { // 自定义消息
                } else {
                }
            }
        }
    };

    private void handleNotification(IMMessage message) {
        ChatRoomNotificationAttachment attachment = (ChatRoomNotificationAttachment) message.getAttachment();
        if (attachment == null) {
            return;
        }

        ArrayList<String> targets = attachment.getTargets();
        if (null == targets || targets.isEmpty()) {
            return;
        }
        final String roomId = message.getSessionId();
        final NimUserInfoCache nimUserInfoCache = NimUserInfoCache.getInstance();
        final NotificationType attachmentType = attachment.getType();
        for (int i = 0, size = targets.size(); i < size; i++) {
            final String target = targets.get(i);
            if (target == null || isAnonymouslyUser(target)) {
                continue;
            }
            NimUserInfoCache.NimUserInfoCustomer nimUserInfo = nimUserInfoCache.getUserInfo(target);
            if (nimUserInfo == null) {
                Map<String, Object> extension = attachment.getExtension();
                nimUserInfo = nimUserInfoCache.translateExtensionToNimUserInfoCustomer(target, extension);
                if (nimUserInfo == null) {
                    nimUserInfoCache.getUserInfoFromRemote(target, new RequestCallback<NimUserInfoCache.NimUserInfoCustomer>() {
                        @Override
                        public void onSuccess(NimUserInfoCache.NimUserInfoCustomer nimUserInfo) {
                            notifyChatRoomMemberChanged(attachmentType, nimUserInfoCache.translateNimUserInfoCustomerToChatRoomMember(nimUserInfo), roomId, target);
                        }

                        @Override
                        public void onFailed(int i) {
                        }

                        @Override
                        public void onException(Throwable throwable) {
                        }
                    });
                } else {
                    notifyChatRoomMemberChanged(attachmentType, nimUserInfoCache.translateNimUserInfoCustomerToChatRoomMember(nimUserInfo), roomId, target);
                }
            } else {
                notifyChatRoomMemberChanged(attachmentType, nimUserInfoCache.translateNimUserInfoCustomerToChatRoomMember(nimUserInfo), roomId, target);
            }
        }
    }

    /**
     * 检测当前账号是否为匿名用户
     *
     * @param account
     * @return true为是，false为否
     */
    public boolean isAnonymouslyUser(String account) {
        return false;
    }

    private void notifyChatRoomMemberChanged(NotificationType type, ChatRoomMember member, String roomId, String target) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "notifyChatRoomMemberChanged member: "
                    + member == null ? null : member.getNick());
        }
        if (member == null) {
            return;
        }
        switch (type) {
            case ChatRoomMemberIn:
                for (int i = 0, size = mRoomMemberChangedObservers.size(); i < size; i++) {
                    RoomMemberChangedObserver roomMemberChangedObserver = mRoomMemberChangedObservers.get(i);
                    if (roomMemberChangedObserver == null) {
                        continue;
                    }
                    if (!isAnonymouslyUser(member.getAccount())) {
                        roomMemberChangedObserver.onRoomMemberIn(ChatUserInfo.chatRoomMember2ChatUserInfo(member));
                    }
                }
                break;
            case ChatRoomMemberExit:
                for (int i = 0, size = mRoomMemberChangedObservers.size(); i < size; i++) {
                    RoomMemberChangedObserver roomMemberChangedObserver = mRoomMemberChangedObservers.get(i);
                    ChatRoomMemberCache.getInstance().deleteRoomMemberCache(roomId, target);
                    if (roomMemberChangedObserver == null) {
                        continue;
                    }
                    if (!isAnonymouslyUser(member.getAccount())) {
                        roomMemberChangedObserver.onRoomMemberExit(ChatUserInfo.chatRoomMember2ChatUserInfo(member));
                    }
                }
                return;
            case ChatRoomManagerAdd:
                member.setMemberType(MemberType.ADMIN);
                break;
            case ChatRoomManagerRemove:
                member.setMemberType(MemberType.NORMAL);
                break;
            case ChatRoomMemberBlackAdd: {
                if (mAccount == null) {
                    break;
                }
                member.setInBlackList(true);
                String account = member.getAccount();
                if (!isAnonymouslyUser(account) && mAccount.equals(account)) {
                    MessageBean data = new MessageBean();
                    data.type = MSG_TYPE_BLACKLIST;
                    data.isInBlackList = true;
                    data.account = member.getAccount();
                    updateMsgListData(data);
                }
                break;
            }
            case ChatRoomMemberBlackRemove: {
                if (mAccount == null) {
                    break;
                }
                member.setInBlackList(false);
                String account = member.getAccount();
                if (!isAnonymouslyUser(account) && mAccount.equals(account)) {
                    MessageBean data = new MessageBean();
                    data.type = MSG_TYPE_BLACKLIST;
                    data.isInBlackList = false;
                    data.account = member.getAccount();
                    updateMsgListData(data);
                }
                break;
            }
            case ChatRoomMemberMuteAdd:
                if (mAccount == null) {
                    break;
                }
                if (mAccount.equals(mAccount)) {
                    isVoiceRevoked = true;
                    MessageBean data = new MessageBean();
                    data.type = MSG_TYPE_MUTE;
                    data.isForbidden = true;
                    data.account = member.getAccount();
                    updateMsgListData(data);
                }
                member.setMuted(true);
                break;
            case ChatRoomMemberMuteRemove:
                if (mAccount == null) {
                    break;
                }
                if (mAccount.equals(mAccount)) {
                    isVoiceRevoked = false;
                    MessageBean data = new MessageBean();
                    data.type = MSG_TYPE_MUTE;
                    data.isForbidden = false;
                    data.account = member.getAccount();
                    updateMsgListData(data);
                }
                member.setMuted(false);
                member.setMemberType(MemberType.GUEST);
                break;
            case ChatRoomCommonAdd:
                member.setMemberType(MemberType.NORMAL);
                break;
            case ChatRoomCommonRemove:
                member.setMemberType(MemberType.GUEST);
                break;
            default:
                break;
        }
        ChatRoomMemberCache.getInstance().saveMember(member);
    }

    private void updateMsgListData(final MessageBean data) {
        if (TextUtils.isEmpty(data.userIconUrl) || TextUtils.isEmpty(data.nickName)) {
            NimUserInfoCache.getInstance().getUserInfoFromRemote(data.account, new RequestCallback<NimUserInfoCache.NimUserInfoCustomer>() {
                @Override
                public void onSuccess(NimUserInfoCache.NimUserInfoCustomer nimUserInfo) {
                    ArrayList<MessageBean> messageBeanArrayList = new ArrayList<>(1);
                    if (nimUserInfo != null) {
                        data.nickName = nimUserInfo.name;
                        data.userIconUrl = nimUserInfo.avatar;
                    }
                    messageBeanArrayList.add(data);
                    notifyMessageReceived(messageBeanArrayList);
                }

                @Override
                public void onFailed(int i) {

                }

                @Override
                public void onException(Throwable throwable) {

                }
            });
        } else {
            ArrayList<MessageBean> messageBeanArrayList = new ArrayList<>(1);
            messageBeanArrayList.add(data);
            notifyMessageReceived(messageBeanArrayList);
        }
    }

    private void notifyMessageReceived(ArrayList<MessageBean> messageData) {
        ArrayList<OnChatMessageReceivedListener> onChatMessageReceivedListeners =
                (ArrayList<OnChatMessageReceivedListener>) mOnChatMessageReceivedListenerList.clone();
        for (int i = 0, size = onChatMessageReceivedListeners.size(); i < size; i++) {
            OnChatMessageReceivedListener onChatMessageReceivedListener = onChatMessageReceivedListeners.get(i);
            if (onChatMessageReceivedListener == null) {
                continue;
            }
            onChatMessageReceivedListener.onChatMessageReceived(messageData);
        }
    }

    /**
     * 获取当前聊天室登录账号
     *
     * @return
     */
    public String getAccount() {
        return mAccount;
    }

    public String getNickName() {
        return mNickName;
    }

    public void setAccount(String account) {
        mAccount = account;
    }

    public void setNickName(String nickName) {
        mNickName = nickName;
    }

    /**
     * 获取当前聊天室ID
     *
     * @return
     */
    public String getRoomId() {
        return mRoomId;
    }

    public void setRoomId(String roomId) {
        mRoomId = roomId;
    }

    public static class SendChatMsgData {
        public SessionTypeEnum sessionType;
        /**
         * 聊天内容
         */
        public String content;
        /**
         * 房间ID
         */
        public String sessionId;
        /**
         * 图片文件对象
         */
        public File file;
        /**
         * 是否启用自家图片服务器true为是，false为否
         */
        public boolean canUserSelfImageServer;
    }


    public interface OnUserKickOutChatRoomListener {
        void onKickOut();
    }

    public interface OnChatMessageReceivedListener {
        void onChatMessageReceived(ArrayList<MessageBean> messageData);
    }

    public interface RoomMemberChangedObserver {
        void onRoomMemberIn(ChatUserInfo chatUserInfo);

        void onRoomMemberExit(ChatUserInfo chatUserInfo);
    }

    public interface EnterChatRoomListener {
        /**
         * 登录直播聊天室或加入直播聊天室出现异常
         *
         * @param throwable
         */
        void onException(Throwable throwable);

        /**
         * 登录失败
         *
         * @param code
         */
        void loginFailed(int code);

        /**
         * 登录聊天室成功
         */
        void loginSuccess(String account);

        void enterChatRoomSuccess(EnterChatRoomResultData enterChatRoomResultData);

        /**
         * 加入聊天室失败
         * com.netease.nimlib.sdk.ResponseCode
         *
         * @param code
         */
        void enterChatRoomFailed(int code);

        /**
         * 获取云信账号失败
         */
        void getAccountFailed(int code);
    }

    public interface SendMsgListener {
        void onSendSuccess(MessageBean messageBean);

        void onSendFailed(MessageBean messageBean);
    }

    public void addRoomMemberChangedObserver(RoomMemberChangedObserver o) {
        if (!mRoomMemberChangedObservers.contains(o)) {
            mRoomMemberChangedObservers.add(o);
        }
    }

    public void addChatMessageReceivedListener(OnChatMessageReceivedListener o) {
        if (!mOnChatMessageReceivedListenerList.contains(o)) {
            mOnChatMessageReceivedListenerList.add(o);
        }
    }

}
