package com.kaolafm.opensdk.http.download.engine;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.http.download.DownloadListener;
import com.kaolafm.opensdk.http.download.DownloadProgress;
import com.kaolafm.opensdk.http.download.DownloadRequest;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.http.urlmanager.UrlManager;
import com.kaolafm.opensdk.utils.HttpUtil;

import java.io.File;

import io.reactivex.Flowable;
import okhttp3.ResponseBody;
import retrofit2.Response;
import retrofit2.http.GET;
import retrofit2.http.Header;
import retrofit2.http.Streaming;
import retrofit2.http.Url;

/**
 * 下载网络请求
 * <AUTHOR>
 * @date 2020-02-07
 */
public class RealDownloadRequest extends BaseRequest {

    private DownloadService mDownloadService;

    public RealDownloadRequest() {
        mDownloadService = obtainRetrofitService(DownloadService.class);
    }

    public Flowable<Response<ResponseBody>> download(long current, long end, String url) {
        //url添加表示防止切换https或http
        return mDownloadService.download("bytes=" + current + "-" + end, url + UrlManager.IDENTIFICATION_IGNORE);
    }

    public void download(DownloadRequest request, DownloadListener listener) {
        setTag(request.tag());
        String url = request.url + UrlManager.IDENTIFICATION_IGNORE;
        Flowable<DownloadProgress> downloadProgressFlowable = mDownloadService
                .download("bytes=0-", url)
                .doOnNext(response -> {
                    if (!response.isSuccessful()) {
                        throw new IllegalArgumentException(String.format("The url %s is illegal", url));
                    }
                    saveFileInfo(url, response);
                })
                .flatMap(response -> dispatch(request, response));
        doHttpDeal(downloadProgressFlowable, new DownloadListener() {
            @Override
            public void onStart() {
                if (listener != null) {
                    listener.onStart();
                }
            }

            @Override
            public void onProgress(DownloadProgress progress) {
                if (listener != null) {
                    listener.onProgress(progress);
                }
            }

            @Override
            public void onSuccess(File file) {
                if (listener != null) {
                    listener.onSuccess(request.getFile());
                }
            }

            @Override
            public void onError(ApiException exception) {
                if (listener != null) {
                    listener.onError(exception);
                }
            }
        });
    }

    private Flowable<DownloadProgress> dispatch(DownloadRequest request, Response<ResponseBody> response) {
        DownloadEngine engine = HttpUtil.isSupportRange(response) ? new RangeDownloadEngine(this) : new NormalDownloadEngine();
        return engine.download(request, response);
    }

    private void saveFileInfo(String url, Response<ResponseBody> response) {
        String fileName = HttpUtil.getFileName(response);
    }

    private interface DownloadService {

        /**
         * 下载。可支持断点下载
         * @param range 断点下载范围
         * @param url 下载地址
         * @return
         */
        @Streaming
        @GET
        Flowable<Response<ResponseBody>> download(@Header("Range") String range, @Url String url);

    }
}
