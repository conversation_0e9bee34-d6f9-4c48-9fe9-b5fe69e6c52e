package com.kaolafm.opensdk.api.login;

import com.kaolafm.opensdk.account.token.KaolaAccessToken;
import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.login.model.Success;

import io.reactivex.Observable;
import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.POST;
import retrofit2.http.Query;

interface KRadioLoginService {

    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER, ApiHostConstants.HTTPS_PROTOCOL_DOMAIN_HEADER})
    @GET(KRadioApiConstants.REQUEST_VERIFY_CODE_URL)
    Single<BaseResult<Success>> getVerificationCode(@Query("phoneNumber") String phoneNum);

    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER, ApiHostConstants.HTTPS_PROTOCOL_DOMAIN_HEADER})
    @POST(KRadioApiConstants.REQUEST_LOGIN_URL)
    Single<BaseResult<KaolaAccessToken>> login(@Query("phoneNumber") String phoneNum, @Query("code") String code);

    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER, ApiHostConstants.HTTPS_PROTOCOL_DOMAIN_HEADER})
    @POST(KRadioApiConstants.REQUEST_TOKEN_URL)
    Observable<BaseResult<KaolaAccessToken>> getToken(@Query("secretkey") String secretkey, @Query("uuid") String uuid);

    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER, ApiHostConstants.HTTPS_PROTOCOL_DOMAIN_HEADER})
    @POST(KRadioApiConstants.REQUEST_LOGOUT_URL)
    Observable<BaseResult<Success>> logout(@Query("secretkey") String secretkey);

}
