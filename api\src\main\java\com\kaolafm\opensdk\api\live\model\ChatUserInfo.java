package com.kaolafm.opensdk.api.live.model;

public class ChatUserInfo extends UserCenterUserInfoData /*implements Parcelable*/ {

    /**
     * bundle携带数据的键名称
     */
    private static final String CHAT_USER_INFO = "chatUserInfo";

    /**
     * 用户的角色状态
     */
    private String role;


    public ChatUserInfo() {
    }


    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

//    // todo chuanlive import com.netease.nimlib.sdk.chatroom.model.ChatRoomMember;
//    public static ChatUserInfo chatRoomMember2ChatUserInfo(ChatRoomMember chatRoomMember) {
//        ChatUserInfo userInfo = new ChatUserInfo();
//
//        if (chatRoomMember != null) {
//            userInfo.setAvatar(chatRoomMember.getAvatar());
//            userInfo.setNickName(chatRoomMember.getNick());
//            userInfo.setUid(chatRoomMember.getAccount());
//        }
//        return userInfo;
//    }

    // 替换掉云信sdk后的成员信息类型
    public static ChatUserInfo chatRoomMember2ChatUserInfo(LiveChatRoomMemberInfo chatRoomMember) {
        ChatUserInfo userInfo = new ChatUserInfo();

        if (chatRoomMember != null) {
            userInfo.setAvatar(chatRoomMember.getAvatar());
            userInfo.setNickName(chatRoomMember.getNickName());
            userInfo.setUid(chatRoomMember.getAccount());
        }
        return userInfo;
    }

    public static ChatUserInfo messageBean2ChatUserInfo(MessageBean messageBean) {
        ChatUserInfo userInfo = new ChatUserInfo();

        if (messageBean != null) {
            userInfo.setAvatar(messageBean.userIconUrl);
            userInfo.setNickName(messageBean.nickName);
            userInfo.setUid(messageBean.account);
        }
        return userInfo;
    }

}