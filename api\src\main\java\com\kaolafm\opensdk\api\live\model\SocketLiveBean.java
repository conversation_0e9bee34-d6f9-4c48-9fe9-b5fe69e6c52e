package com.kaolafm.opensdk.api.live.model;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 **/
public class SocketLiveBean {

    /**
     * notice : 有一场新直播开始了,邀您一起参与直播互动啦
     * noticeUrl : http://vod.kaolafm.net/mz/audios/201907/f878f078-a0a1-4241-9b1c-50900773f62f9.mp3
     * icon : http://pic.kaolafm.net/icon/    10:51:18live_icon.png
     * liveStart : 1545580800000
     * status : began
     * playBackStatus : false
     * livePlayDetailDto : {"liveName":"每天·一点声音","liveId":1445805850,"livePic":"http://img.kaolafm.net/mz/images/201812/8ed3c42a-37de-44a7-aaf4-11c0a93e17ca/default.jpg","liveDesc":"test","programId":1514563012,"programName":"奥迪朝圣之旅","programPic":"http://img.kaolafm.net/mz/images/201812/ad0e48f2-6fb2-44ea-bfdd-dea65d74e446/default.jpg","programDesc":"奥迪朝圣之旅","begintime":"2018-12-24 00:00:00","endtime":"2020-01-30 00:00:00","status":1,"showStartTime":"直播中","period":1,"comperes":"恒宇、依言","guests":"","albumId":0,"liveUrl":"http://play.c.l.kaolafm.net/ugc/1445805850_1514563012/playlist.m3u8","shareUrl":"http://m.kaolafm.com/share/liveplay/index.html","onLineNum":0,"isCanSubscribe":0,"isAlreadySubscribe":0,"subscribeNum":0,"bgColor":"","programLikedNum":0,"programSharedNum":0,"programFollowedNum":6,"uid":2758502,"avatar":"http://img.kaolafm.net/mz/images/201609/96f43582-5bb3-4859-b722-624cc848e11b/default.jpg","isVanchor":1,"gender":0,"backLiveUrl":"","timeLength":"","startTime":1545580800000,"finshTime":1580313600000,"serveTime":1563850278232,"duration":0,"canPlayBack":0,"pushHost":"pub.c.l.kaolafm.net","accessKey":"ugc","lockType":0,"isAlreadyFollowed":0,"roomId":59814518,"rtmpUrl":"rtmp://play.c.l.kaolafm.net/ugc/1445805850_1514563012"}
     */

    @SerializedName("notice")
    public String notice;

    @SerializedName("radioId")
    public long radioId;

    @SerializedName("liveId")
    public long liveId;

    @SerializedName("liveProgramId")
    public long programId;

    @SerializedName("noticeUrl")
    public String noticeUrl;

    @SerializedName("icon")
    public String icon;

    @SerializedName("liveStart")
    public long liveStart;

    @SerializedName("status")
    public String status;

    @SerializedName("playBackEnd")
    public long playBackEnd;

    @SerializedName("playBackStatus")
    public boolean playBackStatus;

    @SerializedName("livePlayDetailDto")
    public LiveDetails livePlayDetailDto;

    public static class ResultBean {



    }
}
