package com.kaolafm.opensdk.api.music.qq.model;

import com.google.gson.annotations.SerializedName;
import com.kaolafm.opensdk.http.core.Response;

/**
 * QQ音乐返回数据基类。
 *
 * <AUTHOR>
 * @date 2018/4/19
 */

public class BaseMusicResult<RESULT> extends Response {

    @SerializedName("sub_ret")
    private int subRet;

    @SerializedName(value = "songlist", alternate = {"data", "order_list", "group_list", "album_list", "singer_list",
            "groups", "category_detail", "category_ablum", "song_list", "song_lyric", "vip_info"})
    private RESULT result;

    public int getSubRet() {
        return subRet;
    }

    public void setSubRet(int subRet) {
        this.subRet = subRet;
    }

    public RESULT getResult() {
        return result;
    }

    public void setResult(RESULT data) {
        this.result = data;
    }
}
