package com.kaolafm.opensdk.demo.purchase;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.text.method.ScrollingMovementMethod;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.kaolafm.opensdk.api.purchase.PurchaseRequest;
import com.kaolafm.opensdk.api.purchase.model.PurchaseSucess;
import com.kaolafm.opensdk.api.purchase.model.QRCodeInfo;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;


import java.util.concurrent.TimeUnit;

import butterknife.BindView;
import butterknife.OnClick;
import io.reactivex.Observable;
import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

/**
 * 通用
 *
 */
public class VipQRCodeActivity extends BaseActivity {
    public static final String KEY_MEAL_ID = "mealId";
    public static final String KEY_PRICE = "price";

    @BindView(R.id.et_vip_meal_id)
    EditText vipMealIdET;

    @BindView(R.id.et_vip_price)
    EditText vipPriceET;

    @BindView(R.id.iv_vip_qr_code)
    ImageView qrImageView;

    @BindView(R.id.info_view)
    TextView infoView;

    @BindView(R.id.btn_qr_code_check)
    Button qrCodeCheck;

//    @BindView(R.id.tv_qr_status_history)
//    TextView mTvQrStatusHistory;

    private Disposable mDisposable;
    private PurchaseRequest mPurchaseRequest;
    private String mQrCodeId;

    @Override
    public int getLayoutId() {
        return R.layout.activity_vip_qr_code;
    }

    @Override
    public void initArgs() {
        Intent intent = getIntent();
        if (intent != null) {
            String mealId = intent.getStringExtra(KEY_MEAL_ID);
            String price = intent.getStringExtra(KEY_PRICE);
            if(!TextUtils.isEmpty(mealId)){
                vipMealIdET.setText(mealId);
            }
            if(!TextUtils.isEmpty(price)){
                vipPriceET.setText(price);
            }
        }
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        setTitle("Vip二维码");
    }

    @Override
    public void initData() {
        mPurchaseRequest = new PurchaseRequest().setTag(this.toString());
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mPurchaseRequest != null) {
            mPurchaseRequest.cancel(this.toString());
        }
//        dispose();
    }

    @OnClick({
            R.id.btn_vip_qr_code_get,R.id.btn_qr_code_check
//            , R.id.btn_kaola_start_check
//            , R.id.btn_kaola_stop_check
    })
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.btn_vip_qr_code_get:
                getVipQrCode();
                break;
            case R.id.btn_qr_code_check:
                skipToCheck();
                break;
//            case R.id.btn_kaola_start_check:
//                checkStatusInterval();
//                break;
//            case R.id.btn_kaola_stop_check:
//                dispose();
//                break;
        }
    }

    private void getVipQrCode(){
        Long mealId = Long.valueOf(vipMealIdET.getText().toString().trim());
        Long price = Long.valueOf(vipPriceET.getText().toString().trim());
        mPurchaseRequest.getVipQRCode(mealId, price, new HttpCallback<QRCodeInfo>() {
            @Override
            public void onSuccess(QRCodeInfo qrCodeInfo) {
                mQrCodeId = qrCodeInfo.getQrCodeId();
                infoView.setText(qrCodeInfo.toString());
                Glide.with(VipQRCodeActivity.this)
                        .load(qrCodeInfo.getQrCodeImg())
                        .into(qrImageView);
                qrCodeCheck.setVisibility(View.VISIBLE);
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取失败", exception);
            }
        });
    }

    private void skipToCheck(){
        Intent intent = new Intent(VipQRCodeActivity.this, QRCodeStatusActivity.class);
        intent.putExtra(QRCodeStatusActivity.KEY_QRCODE_ID, mQrCodeId);
        startActivity(intent);
    }

    /**
     * 循环检查二维码状态
     */
//    private void checkStatusInterval() {
//        if (mDisposable == null || mDisposable.isDisposed()) {
//            Observable.interval(1, TimeUnit.SECONDS)
//                    .subscribe(new Observer<Long>() {
//                        @Override
//                        public void onSubscribe(Disposable d) {
//                            mDisposable = d;
//                        }
//
//                        @Override
//                        public void onNext(Long aLong) {
//                            checkStatus();
//                        }
//
//                        @Override
//                        public void onError(Throwable e) {
//                            dispose();
//                        }
//
//                        @Override
//                        public void onComplete() {
//                            dispose();
//                        }
//                    });
//        }
//    }

//    private void dispose() {
//        if (mDisposable != null && !mDisposable.isDisposed()) {
//            mDisposable.dispose();
//            mDisposable = null;
//        }
//    }
//
//    private void checkStatus() {
//        if (!TextUtils.isEmpty(mQrCodeId)) {
//            mPurchaseRequest.qrCodeStatus(mQrCodeId, new HttpCallback<PurchaseSucess>() {
//
//                @Override
//                public void onSuccess(PurchaseSucess purchaseSucess) {
//                    switch (purchaseSucess.getStatus()){
//                        case 0:
//                            showStatus("未支付");
//                            break;
//                        case 1:
//                            showStatus("已支付");
//                            dispose();
//                            break;
//                        case 2:
//                            showStatus("过期");
//                            dispose();
//                            break;
//                    }
//                }
//
//                @Override
//                public void onError(ApiException exception) {
//                    showError("获取失败", exception);
//                }
//            });
//        }
//    }
//
//    private void showStatus(String msg) {
//        String historyText = mTvQrStatusHistory.getText().toString();
//        mTvQrStatusHistory.setText(msg + "\n" + historyText);
//    }

}
