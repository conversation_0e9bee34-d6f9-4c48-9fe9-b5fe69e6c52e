package com.kaolafm.opensdk.demo;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import butterknife.BindView;

/**
 * <AUTHOR>
 * @date 2018/7/20
 */

public class CategoryAdapter extends BaseAdapter<FunctionItem> {

    @Override
    protected BaseHolder<FunctionItem> getViewHolder(View view, int viewType) {
        if (viewType == FunctionItem.TYPE_CHILD) {
            return new FunctionViewHolder(view);
        }
        return new TitleViewHolder(view);
    }

    @Override
    protected int getLayoutId(int viewType) {
        if (viewType == FunctionItem.TYPE_CHILD) {
            return R.layout.item_demo_funtion;
        }
        return R.layout.item_main;
    }

    @Override
    public int getItemViewType(int position) {
        FunctionItem functionItem = mDataList.get(position);
        if (functionItem != null) {
            return functionItem.getType();
        }
        return 0;
    }

    static class TitleViewHolder extends BaseHolder<FunctionItem> {

        private TextView mTvItemName;

        public TitleViewHolder(View itemView) {
            super(itemView);
            mTvItemName = itemView.findViewById(R.id.tv_item_name);
        }

        @Override
        public void setupData(FunctionItem functionItem, int position) {
            mTvItemName.setText(functionItem.getName());
        }
    }

    static class FunctionViewHolder extends BaseHolder<FunctionItem> {

        @BindView(R.id.iv_function_ic)
        ImageView mIvFunctionIc;

        @BindView(R.id.tv_function_name)
        TextView mTvFunctionName;

        public FunctionViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        public void setupData(FunctionItem functionItem, int position) {
            mTvFunctionName.setText(functionItem.getName());
        }
    }
}
