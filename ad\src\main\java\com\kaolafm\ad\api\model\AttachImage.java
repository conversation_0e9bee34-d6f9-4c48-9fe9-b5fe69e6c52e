package com.kaolafm.ad.api.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 图片广告中的附图。
 * <AUTHOR>
 * @date 2020/4/1
 */
public class AttachImage implements Parcelable {
    /**
     * 图片地址
     */
    private String url;

    /**
     * 图本地缓存路径
     */
    private String localPath;

    /**
     * 曝光时长.
     */
    private int exposeDuration;

    /**
     * 宽
     */
    private int width;

    /**
     * 高
     */
    private int height;

    public AttachImage() {
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getLocalPath() {
        return localPath;
    }

    public void setLocalPath(String localPath) {
        this.localPath = localPath;
    }

    public int getExposeDuration() {
        return exposeDuration;
    }

    public void setExposeDuration(int exposeDuration) {
        this.exposeDuration = exposeDuration;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    protected AttachImage(Parcel in) {
        url = in.readString();
        localPath = in.readString();
        exposeDuration = in.readInt();
        width = in.readInt();
        height = in.readInt();
    }

    public static final Creator<AttachImage> CREATOR = new Creator<AttachImage>() {
        @Override
        public AttachImage createFromParcel(Parcel in) {
            return new AttachImage(in);
        }

        @Override
        public AttachImage[] newArray(int size) {
            return new AttachImage[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(url);
        dest.writeString(localPath);
        dest.writeInt(exposeDuration);
        dest.writeInt(width);
        dest.writeInt(height);
    }
}
