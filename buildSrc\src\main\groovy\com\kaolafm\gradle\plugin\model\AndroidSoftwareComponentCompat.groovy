package com.kaolafm.gradle.plugin.model

import org.gradle.api.artifacts.ConfigurationContainer
import org.gradle.api.artifacts.PublishArtifact
import org.gradle.api.attributes.Usage
import org.gradle.api.internal.attributes.ImmutableAttributes
import org.gradle.api.internal.attributes.ImmutableAttributesFactory
import org.gradle.api.internal.component.SoftwareComponentInternal
import org.gradle.api.internal.component.UsageContext
import org.gradle.api.internal.java.usagecontext.LazyConfigurationUsageContext
import org.gradle.api.model.ObjectFactory

import javax.inject.Inject

class AndroidSoftwareComponentCompat implements SoftwareComponentInternal {

    private final Set<PublishArtifact> artifacts = new LinkedHashSet<PublishArtifact>()
    private final UsageContext runtimeUsage
    private final UsageContext compileUsage
    protected final ConfigurationContainer configurations
    protected final ObjectFactory objectFactory
    protected final ImmutableAttributesFactory attributesFactory

    @Inject
    AndroidSoftwareComponentCompat(ObjectFactory objectFactory, ConfigurationContainer configurations, ImmutableAttributesFactory attributesFactory) {
        this.objectFactory = objectFactory
        this.configurations = configurations
        this.attributesFactory = attributesFactory
        runtimeUsage = createRuntimeUsageContext()
        compileUsage = createCompileUsageContext()

    }

    @Override
    Set<? extends UsageContext> getUsages() {
        return ([runtimeUsage, compileUsage] as Set).asImmutable()
    }

    @Override
    String getName() {
        return "android"
    }

    private UsageContext createRuntimeUsageContext() {
        ImmutableAttributes attributes = attributesFactory.of(Usage.USAGE_ATTRIBUTE, objectFactory.named(Usage.class, Usage.JAVA_RUNTIME))
        return new LazyConfigurationUsageContext("runtime", "implementation", artifacts, configurations, attributes)

    }

    private UsageContext createCompileUsageContext() {
        ImmutableAttributes attributes = attributesFactory.of(Usage.USAGE_ATTRIBUTE, objectFactory.named(Usage.class, Usage.JAVA_API))
        return new LazyConfigurationUsageContext("api", "api", artifacts, configurations, attributes)

    }

}