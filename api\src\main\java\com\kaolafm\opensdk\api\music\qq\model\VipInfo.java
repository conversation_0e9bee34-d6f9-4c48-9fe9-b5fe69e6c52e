package com.kaolafm.opensdk.api.music.qq.model;

import com.google.gson.annotations.SerializedName;

/**
 * 会员信息
 * <AUTHOR>
 * @date 2018/5/4
 */

public class VipInfo {

    /**
     * end_time : 2019-10-28 21:32:45
     * start_time : 2017-05-11 21:32:45
     *  vip_pay_page  : “xxx”
     *  vip_name  : “xxx”
     * vip_flag : 1
     */

    @SerializedName("end_time")
    private String endTime;

    @SerializedName("start_time")
    private String startTime;

    @SerializedName("vip_flag")
    private int vipFlag;

    @SerializedName("vip_name")
    private String vipName;

    @SerializedName("vip_pay_page")
    private String vipPayPage;

    /** vip剩余时间 */
    private String vipTimeLeft;

    public String getEndTime() {
        return endTime;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public int getVipFlag() {
        return vipFlag;
    }

    public void setVipFlag(int vipFlag) {
        this.vipFlag = vipFlag;
    }

    public String getVipName() {
        return vipName;
    }

    public void setVipName(String vipName) {
        this.vipName = vipName;
    }

    public String getVipPayPage() {
        return vipPayPage;
    }

    public void setVipPayPage(String vipPayPage) {
        this.vipPayPage = vipPayPage;
    }

    public String getVipTimeLeft() {
        return vipTimeLeft;
    }

    public void setVipTimeLeft(String vipTimeLeft) {
        this.vipTimeLeft = vipTimeLeft;
    }
}
