package com.kaolafm.opensdk.api.operation.model.category;

import com.kaolafm.opensdk.api.operation.model.ImageFile;
import java.io.Serializable;
import java.util.Map;

/**
 * 人工运营分类成员（父类）。
 * <br></br>
 * 对于该类的子类一般操作可以使用{@link com.kaolafm.opensdk.utils.operation.OperationAssister}工具类。
 */
public class CategoryMember implements Serializable {

    /** 分类成员的code值，用于获取子分类成员。该值是可变的。*/
    private String code;

    /** 标题名*/
    private String title;

    /** 副标题名*/
    private String subtitle;

    /** 描述*/
    private String description;

    /** SDK内部使用，开发者不需要关心，会一直为空*/
    private String type;

    /** 图片信息集合*/
    private Map<String, ImageFile> imageFiles;

    /** 额外信息，用于一些定制需求*/
    private Map<String, String> extInfo;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Map<String, ImageFile> getImageFiles() {
        return imageFiles;
    }

    public void setImageFiles(Map<String, ImageFile> imageFiles) {
        this.imageFiles = imageFiles;
    }

    public Map<String, String> getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(Map<String, String> extInfo) {
        this.extInfo = extInfo;
    }

    @Override
    public String toString() {
        return "CategoryMember{" +
                "code='" + code + '\'' +
                ", title='" + title + '\'' +
                ", subtitle='" + subtitle + '\'' +
                ", description='" + description + '\'' +
                ", type='" + type + '\'' +
                ", imageFiles=" + imageFiles +
                ", extInfo=" + extInfo +
                '}';
    }
}
