package com.kaolafm.opensdk.player.logic.playlist;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.player.logic.listener.IPlayListControl;
import com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener;
import com.kaolafm.opensdk.player.logic.model.CustomPlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.PlaylistInfo;
import com.kaolafm.opensdk.player.logic.model.item.InvalidPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.opensdk.player.logic.util.PlayerPreconditions;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> on 2019/3/18.
 */

public abstract class BasePlayListControl implements IPlayListControl {
    protected ArrayList<PlayItem> mPlayItemArrayList;
    protected PlaylistInfo mPlaylistInfo;
    protected int mPosition;
    protected PlayItem mCurPlayItem;
    protected PlayerBuilder mPlayerBuilder;
    protected IPlayListStateListener mIPlayListControlListener;

    public BasePlayListControl() {
        mPlayItemArrayList = new ArrayList<>();
        mPlaylistInfo = new PlaylistInfo();
        mPosition = 0;
        PlayerLogUtil.log(getClass().getSimpleName(), "BasePlayListControl构造器", "修改mPosition = " + mPosition);
    }

    @Override
    public void initPlayList(PlayerBuilder playerBuilder, IPlayListGetListener iPlayListGetListener) {
        if (PlayerPreconditions.checkNull(playerBuilder)) {
            return;
        }
        mPosition = 0;
        PlayerLogUtil.log(getClass().getSimpleName(), "initPlayList", "修改mPosition = " + mPosition);
        mPlayerBuilder = playerBuilder;
        initPlayListInfo(playerBuilder);
    }

    @Override
    public boolean hasNextPage() {
        return !PlayerPreconditions.checkNull(mPlaylistInfo) && mPlaylistInfo.isHasNextPage();
    }

    @Override
    public boolean hasPrePage() {
        return !PlayerPreconditions.checkNull(mPlaylistInfo) && mPlaylistInfo.isHasPrePage();
    }

    @Override
    public void clearPlayList() {
        release();
    }

    @Override
    public void getNextPlayItem(IPlayListGetListener iPlayListGetListener) {
        if (PlayerPreconditions.checkNull(mPlayItemArrayList)) {
            notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_ITEM_NULL, -1);
            return;
        }
        if (mPosition + 1 < 0) {
            notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_INDEX_OUT_OF_BOUNDS, -1);
            return;
        }
        if (mPosition + 1 >= mPlayItemArrayList.size()) {
            PlayerLogUtil.log(getClass().getSimpleName(), "getNextPlayItem", "current list end");

            if (hasNextPage()) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getNextPlayItem", "has next page");
                loadNextPage(iPlayListGetListener);
            } else {
                notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_IS_LAST_ONE, -1);
            }
            return;
        }
        int index = mPosition + 1;
        notifyPlayListGet(iPlayListGetListener, index, mPlayItemArrayList.get(index), null);
    }

    @Override
    public void getPrePlayItem(IPlayListGetListener iPlayListGetListener) {
        if (PlayerPreconditions.checkNull(mPlayItemArrayList)) {
            notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_ITEM_NULL, -1);
            return;
        }
        if (mPosition < 0) {
            notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_INDEX_OUT_OF_BOUNDS, -1);
            return;
        }
        if (mPosition == 0) {
            if (hasPrePage()) {
                loadPrePage(iPlayListGetListener);
            } else {
                notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_IS_FIRST_ONE, -1);
            }
            return;
        }
        int index = mPosition - 1;
        notifyPlayListGet(iPlayListGetListener, index, mPlayItemArrayList.get(index), null);
    }

    @Override
    public PlayItem getCurPlayItem() {
        if (PlayerPreconditions.checkNull(mPlayItemArrayList)) {
            return new InvalidPlayItem();
        }
        if (mPosition < 0) {
            return new InvalidPlayItem();
        }
        if (mPosition >= mPlayItemArrayList.size()) {
            return new InvalidPlayItem();
        }
        return mPlayItemArrayList.get(mPosition);
    }

    @Override
    public PlayItem getPlayItem(PlayerBuilder playerBuilder) {
        if (PlayerPreconditions.checkNull(mPlayItemArrayList)) {
            return null;
        }
        if (PlayerPreconditions.checkNull(mPlayerBuilder)) {
            return null;
        }
        if (PlayerPreconditions.checkNull(playerBuilder)) {
            return null;
        }
        if (playerBuilder.getType() != mPlayerBuilder.getType()) {
            return null;
        }
        String id = playerBuilder.getId();
        if (playerBuilder instanceof CustomPlayerBuilder) {
            if (!StringUtil.isEmpty(((CustomPlayerBuilder) playerBuilder).getChildId())) {
                id = ((CustomPlayerBuilder) playerBuilder).getChildId();
            }
        }
        for (int i = 0; i < mPlayItemArrayList.size(); i++) {
            PlayItem playItem = mPlayItemArrayList.get(i);
            if (PlayerPreconditions.checkNull(playItem)) {
                continue;
            }
            if (String.valueOf(playItem.getAudioId()).equals(id)) {
                return playItem;
            }
        }
        return null;
    }

    @Override
    public int getCurPosition() {
        return mPosition;
    }

    @Override
    public void setCurPosition(int position) {
        mPosition = position;
        PlayerLogUtil.log(getClass().getSimpleName(), "setCurPosition(I)", "修改mPosition = " + mPosition);
        mCurPlayItem = mPlayItemArrayList.get(mPosition);
    }

    @Override
    public void setCurPosition(PlayItem playItem) {
        if (PlayerPreconditions.checkNull(playItem)) {
            return;
        }
        if (playItem instanceof InvalidPlayItem) {
            mPosition = 0;
            PlayerLogUtil.log(getClass().getSimpleName(), "setCurPosition(O)", "修改mPosition = " + mPosition);
            mCurPlayItem = playItem;
            return;
        }
        if (PlayerPreconditions.checkNull(mPlayItemArrayList)) {
            return;
        }

        for (int i = 0; i < mPlayItemArrayList.size(); i++) {
            PlayItem playItemTemp = mPlayItemArrayList.get(i);
            if (PlayerPreconditions.checkNull(playItemTemp)) {
                continue;
            }
            if (playItemTemp.getAudioId() == playItem.getAudioId()) {
                mPosition = i;
                PlayerLogUtil.log(getClass().getSimpleName(), "setCurPosition(O)", "修改mPosition = " + mPosition);
                mCurPlayItem = playItem;
                PlayerLogUtil.log(getClass().getSimpleName(), "setCurPosition", " position = " + mPosition);
                return;
            }
        }
        mPosition = -1;
        PlayerLogUtil.log(getClass().getSimpleName(), "setCurPosition(O)", "修改mPosition = " + mPosition);
        mCurPlayItem = null;
    }

    @Override
    public boolean hasPre() {
        if (PlayerPreconditions.checkNull(mPlayItemArrayList)) {
            return false;
        }
        if (mPosition - 1 >= 0) {
            return true;
        }
        return hasPrePage();
    }

    @Override
    public boolean hasNext() {
        if (PlayerPreconditions.checkNull(mPlayItemArrayList)) {
            return false;
        }
        if (mPosition + 1 < 0) {
            return false;
        }
        if (mPosition + 1 < mPlayItemArrayList.size()) {
            return true;
        }
        return hasNextPage();
    }

    @Override
    public List<PlayItem> getPlayList() {
        return mPlayItemArrayList;
    }

    @Override
    public PlaylistInfo getPlayListInfo() {
        return mPlaylistInfo;
    }

    @Override
    public void loadNextPage(IPlayListGetListener iPlayListGetListener) {

    }

    @Override
    public void loadPrePage(IPlayListGetListener iPlayListGetListener) {

    }

    @Override
    public void loadPageData(int type, long audioId, int pageNum, IPlayListGetListener iPlayListGetListener) {

    }

    @Override
    public void setCallback(IPlayListStateListener iPlayListControlListener) {
        mIPlayListControlListener = iPlayListControlListener;
    }

    @Override
    public void release() {
        mPosition = 0;
        PlayerLogUtil.log(getClass().getSimpleName(), "release", "修改mPosition = " + mPosition);
        mCurPlayItem = null;
        if (!ListUtil.isEmpty(mPlayItemArrayList)) {
            mPlayItemArrayList.clear();
        }
    }

    private void initPlayListInfo(PlayerBuilder playerBuilder) {
        mPlaylistInfo = new PlaylistInfo();
        mPlaylistInfo.setType(playerBuilder.getType());
        mPlaylistInfo.setId(playerBuilder.getId());
        mPlaylistInfo.setNoSubscribe(playerBuilder.getNoSubscribe());
        mPlaylistInfo.setSort(playerBuilder.getSort());
        if (playerBuilder instanceof CustomPlayerBuilder) {
            mPlaylistInfo.setChildId(((CustomPlayerBuilder) playerBuilder).getChildId());
        }
        mPlaylistInfo.setPageIndex("1");
        if (!ListUtil.isEmpty(mPlayItemArrayList)) {
            mPlayItemArrayList.clear();
        }
    }

    protected void notifyPlayListGetError(IPlayListGetListener iPlayListGetListener, PlayItem playItem, int errorCode, int errorExtra) {
        if (PlayerPreconditions.checkNull(iPlayListGetListener)) {
            return;
        }
        iPlayListGetListener.onDataGetError(playItem, errorCode, errorExtra);
    }

    protected void notifyPlayListGet(IPlayListGetListener iPlayListGetListener, int position, PlayItem playItem, List<PlayItem> playItemArrayList) {
        addPlayItemParameter(position, playItem);
        notifyPlayListGet(iPlayListGetListener, playItem, playItemArrayList);
    }

    protected void notifyPlayListGet(IPlayListGetListener iPlayListGetListener, PlayItem playItem, List<PlayItem> playItemArrayList) {
        if (PlayerPreconditions.checkNull(iPlayListGetListener)) {
            return;
        }
        iPlayListGetListener.onDataGet(playItem, playItemArrayList);
    }

    protected void notifyPlayListChangeError(PlayItem playItem, int errorCode, int errorExtra) {
        if (PlayerPreconditions.checkNull(mIPlayListControlListener)) {
            return;
        }
        mIPlayListControlListener.onPlayListChangeError(playItem, errorCode, errorExtra);
    }

    protected void notifyPlayListChange(ArrayList<PlayItem> playItemArrayList) {
        if (PlayerPreconditions.checkNull(mIPlayListControlListener)) {
            return;
        }
        mIPlayListControlListener.onPlayListChange(playItemArrayList);
    }


    @Override
    public boolean isExistPlayItem(long id) {
        if (PlayerPreconditions.checkNull(mPlayItemArrayList)) {
            return false;
        }
        for (int i = 0; i < mPlayItemArrayList.size(); i++) {
            PlayItem playItem = mPlayItemArrayList.get(i);
            if (PlayerPreconditions.checkNull(playItem)) {
                continue;
            }
            if (playItem.getAudioId() == id) {
                addPlayItemParameter(i, playItem);
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean isExistPlayItem(PlayItem playItem) {
        if (playItem == null) return false;
        return isExistPlayItem(playItem.getAudioId());
    }

    /**
     * 为当前的playitem添加参数.
     *
     * @param position
     * @param playItem
     */
    protected void addPlayItemParameter(int position, PlayItem playItem) {
    }
}
