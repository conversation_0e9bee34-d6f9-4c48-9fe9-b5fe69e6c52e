package com.kaolafm.opensdk.http.urlmanager;

import java.util.Map;

import okhttp3.Request;

/**
 * 动态修改url相关参数的接口
 * <AUTHOR>
 * @date 2020/5/19
 */
public interface UrlManager {

    String DOMAIN_NAME = "Domain-Name";

    String DOMAIN_NAME_HEADER = DOMAIN_NAME + ":";

    String HTTPS_PROTOCOL = "Protocol-Type";

    /**
     * 在url中加入该字段可以不对url进行任何处理，直接使用传入的url进行网络请求
     */
    String IDENTIFICATION_IGNORE = "#url_ignore";

    /**
     * 处理网络请求的url
     * @param oldRequest 原来的请求
     * @return 处理后的请求
     */
    Request processRequest(Request oldRequest);

    /**
     * 添加域名
     * @param domainName host的命名标识，一一对应host。一定要与@Headers中一样，<br/>
     *                   如@Headers{{@link #DOMAIN_NAME_HEADER} + {@code domainName}}
     * @param domainUrl  域名host
     */
    void putDomain(String domainName, String domainUrl);

    /**
     * 添加实时回调参数，用于添加那些需要在请求时才传入的参数
     */
    void addRealtimeParamListener(String domainName, RealtimeParamListener listener);

    /**
     * 是否使用https
     * @return
     */
    boolean isUseHttps();

    /**
     * 添加实时参数回调
     */
    interface RealtimeParamListener {

        /**
         * 获取实时参数集合
         * @return 实时参数集合
         */
        Map<String, String> getParam();
    }

}
