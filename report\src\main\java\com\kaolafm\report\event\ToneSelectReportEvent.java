package com.kaolafm.report.event;

import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> on 2019-07-23.
 */

public class ToneSelectReportEvent extends BaseReportEventBean {
    public static final String TONE_TYPE_LOW = "1";
    public static final String TONE_TYPE_NOMAL = "2";
    public static final String TONE_TYPE_HIGH = "3";
    public static final String TONE_TYPE_HIGHER = "4";

    /**
     * 1：流畅音质；2：标准音质；3：高品音质；4：超高音质；
     */
    private String type;

    public ToneSelectReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_TONE_SELECT);
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
