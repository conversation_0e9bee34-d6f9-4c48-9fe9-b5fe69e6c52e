package com.kaolafm.opensdk.api;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.account.profile.KaolaProfile;
import com.kaolafm.opensdk.di.qualifier.ProfileQualifier;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.core.FlowableCallback;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.core.IRepositoryManager;
import com.trello.rxlifecycle3.LifecycleTransformer;

import java.util.Map;

import javax.inject.Inject;

import dagger.Lazy;
import io.reactivex.Flowable;
import io.reactivex.Observable;
import io.reactivex.Single;
import io.reactivex.functions.Function;
import retrofit2.Call;
import retrofit2.Response;

/**
 * <AUTHOR>
 * @date 2020-01-14
 */
public abstract class AbstractRequest {
    /**
     * 该实例是在{@link com.kaolafm.opensdk.di.module.AppModule}中提供
     */
    @Inject
    @AppScope
    protected IRepositoryManager mRepositoryManager;

    /**
     * 通过限定符由{@link com.kaolafm.opensdk.di.module.CommonParamModule}中提供，
     * 由单例{@link KaolaProfileManager}控制其提供的{@link KaolaProfile}
     */
    @Inject
    @ProfileQualifier
    protected Lazy<KaolaProfile> mProfileLazy;

    @Inject
    @AppScope
    protected Lazy<Gson> mGsonLazy;

    private LifecycleTransformer mLifecycleTransformer;

    private Object mTag;

    protected <T> T obtainRetrofitService(Class<T> service) {
        if(mRepositoryManager == null){
            ComponentKit.getInstance().inject((BaseRequest) this);
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        return mRepositoryManager.obtainRetrofitService(service);
    }

    protected void putNullParam(Map<String, Object> map, String key, Object object) {
        if (map != null && !TextUtils.isEmpty(key) && object != null && !TextUtils.isEmpty(object.toString())) {
            map.put(key, object);
        }
    }

    /**
     * 设置tag用于网络请求
     *
     * @param tag tag标记
     */
    public <T extends AbstractRequest> T setTag(Object tag) {
        mTag = tag;
        return (T) this;
    }

    /**
     * 根据tag取消网络请求，会取消该tag标记的所有网络请求
     */
    public void cancel(Object tag) {
        mRepositoryManager.cancel(tag);
    }

    /**********************************Observable****************************************/


    protected <T, E> void doHttpDeal(Observable<T> observable, Function<T, E> function, HttpCallback<E> callback) {
        if (canBind()) {
            mRepositoryManager.doHttpDeal(mLifecycleTransformer, observable, function, callback);
        } else {
            mRepositoryManager.doHttpDeal(mTag, observable, function, callback);
        }
    }

    protected <T> void doHttpDeal(Observable<T> observable, HttpCallback<T> callback) {
        if (canBind()) {
            mRepositoryManager.doHttpDeal(mLifecycleTransformer, observable, callback);
        } else{
            mRepositoryManager.doHttpDeal(mTag, observable, callback);
        }
    }

    /**********************************Single****************************************/

    protected <T, E> void doHttpDeal(Single<T> single, Function<T, E> function, HttpCallback<E> callback) {
        if (canBind()) {
            mRepositoryManager.doHttpDeal(mLifecycleTransformer, single, function, callback);
        } else if (mTag != null) {
            mRepositoryManager.doHttpDeal(mTag, single, function, callback);
        } else {
            mRepositoryManager.doHttpDeal(single, function, callback);
        }
    }

    protected <T> void doHttpDeal(Single<T> single, HttpCallback<T> callback) {
        if (canBind()) {
            mRepositoryManager.doHttpDeal(mLifecycleTransformer, single, callback);
        } else if (mTag != null) {
            mRepositoryManager.doHttpDeal(mTag, single, callback);
        } else {
            mRepositoryManager.doHttpDeal(single, callback);
        }
    }

    protected <T> void doHttpDeal(Flowable<T> flowable, FlowableCallback<T> callback) {
        if (canBind()) {
            mRepositoryManager.doHttpDeal(mLifecycleTransformer, flowable, callback);
        } else if (mTag != null) {
            mRepositoryManager.doHttpDeal(mTag, flowable, callback);
        } else {
            mRepositoryManager.doHttpDeal(flowable, callback);
        }
    }

    /**
     * 判断是可以绑定生命周期
     */
    private boolean canBind() {
        return mLifecycleTransformer != null /*&& LIFECYCLE_TRANSFORMER_CLASS_NAME
                .equals(mSingleTransformer.getClass().getName())*/;
    }

    /**********************************Call****************************************/

    protected <T> T doHttpDealSync(Call<T> call) {
        return mRepositoryManager.doHttpDealSync(call);
    }

    protected <T> Response<T> doHttpDealSyncResponse(Call<T> call) {
        return mRepositoryManager.doHttpDealSyncResponse(call);
    }

    /**
     * 将网络请求绑定到生命周期
     * @param lifecycleTransformer
     */
    public <T extends AbstractRequest> T bindLifecycle(LifecycleTransformer lifecycleTransformer) {
        mLifecycleTransformer = lifecycleTransformer;
        return (T) this;
    }
}
