package com.kaolafm.ad;

import com.kaolafm.opensdk.Options;
import com.kaolafm.opensdk.utils.BaseHttpsStrategy;

import okhttp3.Interceptor;

/**
 * 广告配置项
 * <AUTHOR>
 * @date 2020-03-03
 */
public class AdvertOptions extends Options {

    public static final AdvertOptions DEFAULT = new AdvertOptions();
    /**
     * 品牌
     */
    private String brand;

    /**设备类型,0:不存在;1:APP(手机);2:后视镜;3:中控台;*/
    private int deviceType;

    public AdvertOptions() {
        this(new Builder());
    }

    public String brand() {
        return brand;
    }

    public int deviceType() {
        return deviceType;
    }

    private AdvertOptions(Builder builder) {
        super(builder);
        this.brand = builder.brand;
        this.deviceType = builder.deviceType;
    }

    public static class Builder extends Options.Builder {
        /**
         * 品牌
         */
        private String brand;
        /**
         *
         */
        private int deviceType;

        /**
         * 品牌
         */
        public Builder brand(String brand) {
            this.brand = brand;
            return this;
        }

        /**
         * 设备类型,0:不存在;1:APP(手机);2:后视镜;3:中控台;
         */
        public Builder deviceType(int deviceType) {
            this.deviceType = deviceType;
            return this;
        }

        @Override
        public Builder versionName(String versionName) {
            super.versionName(versionName);
            return this;
        }

        @Override
        public Builder httpTimeout(long timeout) {
            super.httpTimeout(timeout);
            return this;
        }

        @Override
        public Builder interceptor(Interceptor interceptor) {
            super.interceptor(interceptor);
            return this;
        }

        @Override
        public Builder carType(String carType) {
            super.carType(carType);
            return this;
        }

        @Override
        public Builder useHttps(BaseHttpsStrategy strategy) {
            super.useHttps(strategy);
            return this;
        }

        @Override
        public AdvertOptions build() {
            return new AdvertOptions(this);
        }
    }
}
