package com.kaolafm.ad.profile;

import android.os.Build;

import com.kaolafm.opensdk.account.profile.Profile;

/**
 * 与系统相关的配置属性
 * <AUTHOR>
 * @date 2020-01-13
 */
public class OSProfile extends Profile {

    /**0-不存在，1-Android，2-IOS*/
    private String osType = "1";

    /**设备类型,0:不存在;1:APP(手机);2:后视镜;3:中控台;*/
    private int deviceType = 0;

    /**设备型号*/
    private String deviceModel = Build.MODEL;

    /**操作系统版本, 例如：9.0*/
    private String osVersion = Build.VERSION.RELEASE;

    /**设备制造商，例如：Rockchip、alps、ATC*/
    private String deviceManufacturer = Build.MANUFACTURER;

    /**设备分辨率*/
    private String resolution;

    private String ip;

    public String getOsType() {
        return osType;
    }

    public void setOsType(String osType) {
        this.osType = osType;
    }

    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    public int getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(int deviceType) {
        this.deviceType = deviceType;
    }

    public String getDeviceManufacturer() {
        return deviceManufacturer;
    }

    public void setDeviceManufacturer(String deviceManufacturer) {
        this.deviceManufacturer = deviceManufacturer;
    }

    public String getResolution() {
        return resolution;
    }

    public void setResolution(String resolution) {
        this.resolution = resolution;
    }

    public String getDeviceModel() {
        return deviceModel;
    }

    public void setDeviceModel(String deviceModel) {
        this.deviceModel = deviceModel;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }
}
