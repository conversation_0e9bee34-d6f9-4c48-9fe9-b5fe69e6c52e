package com.kaolafm.opensdk.api.login.model;

import com.google.gson.annotations.SerializedName;

/**
 * 二维码相关
 *
 * <AUTHOR>
 * @date 2018/9/13
 */

public class QRCodeInfo {

    /** 正常状态. 等待扫描*/
    public static final int STATUS_NORMAL = 0;

    /** 二维码过期. 需要重新请求二维码*/
    public static final int STATUS_LOSE_EFFICACY = 1;

    /** 二维码已授权*/
    public static final int STATUS_AUTHORIZATION = 2;

    /** 新增状态3，已被扫描，还未登录 */
    public static final int STATUS_SCANED = 3;

    /**
     * code : irrIB3
     * state : 0
     */

    /** 用户绑定K-radio的code值。*/
    @SerializedName("code")
    private String code;

    /** 二维码url*/
    @SerializedName("qrcode_path")
    private String qrCodePath;

    /** 获取code的状态码*/
    @SerializedName("state")
    private String state;

    /** 二维码状态*/
    @SerializedName("status")
    private int status;

    /**
     * uuid : 482b4246-7a58-41fc-a5cc-a57950086862
     * qrcode_path : https://img.kaolafm.net/QRCode/20180913/482b4246-7a58-41fc-a5cc-a57950086862.jpg
     * status : 1
     */
    /**uuid获取用于绑定K-radio的code*/
    @SerializedName("uuid")
    private String uuid;

    /**用户昵称*/
    @SerializedName("nickname")
    private String nickname;

    /**用户头像*/
    @SerializedName("avatar")
    private String avatar;

    public String getCode() {
        return code;
    }

    public String getQRCodePath() {
        return qrCodePath;
    }

    public String getState() {
        return state;
    }

    public int getStatus() {
        return status;
    }

    public String getUuid() {
        return uuid;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setQRCodePath(String qrCodePath) {
        this.qrCodePath = qrCodePath;
    }

    public void setState(String state) {
        this.state = state;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    @Override
    public String toString() {
        return "QRCodeInfo{" +
                "code='" + code + '\'' +
                ", qrCodePath='" + qrCodePath + '\'' +
                ", state='" + state + '\'' +
                ", status=" + status +
                ", uuid='" + uuid + '\'' +
                ", nickname='" + nickname + '\'' +
                ", avatar='" + avatar + '\'' +
                '}';
    }
}
