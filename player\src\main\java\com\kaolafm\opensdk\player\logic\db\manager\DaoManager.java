package com.kaolafm.opensdk.player.logic.db.manager;

import android.app.Application;
import android.content.Context;

import com.kaolafm.opensdk.db.helper.MigrationHelper;
import com.kaolafm.opensdk.db.manager.GreenDaoManager;
import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.opensdk.player.logic.db.greendao.DaoMaster;
import com.kaolafm.opensdk.player.logic.db.greendao.DaoSession;

import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.query.QueryBuilder;

/**
 * 进行数据库的管理
 * <AUTHOR>
 * @date 2018/5/14
 */

public class DaoManager implements GreenDaoManager {

    private static volatile DaoManager mInstance;

    private Application mContext;

    private DaoMaster mDaoMaster;

    private DaoSession mDaoSession;

    private DaoMaster.OpenHelper mHelper;

    private DaoManager(String name) {
        QueryBuilder.LOG_SQL = Logging.isDebug();
        QueryBuilder.LOG_VALUES = Logging.isDebug();
        mContext = ComponentKit.getInstance().getApplication();
        mHelper = new SQLiteUpdateOpenHelper(mContext, name);
        mDaoMaster = new DaoMaster(mHelper.getWritableDatabase());
    }

    public static DaoManager getInstance(String name) {
        if (mInstance == null) {
            synchronized (DaoManager.class) {
                if (mInstance == null) {
                    mInstance = new DaoManager(name);
                }
            }
        }
        return mInstance;
    }


    @Override
    public DaoSession getDaoSession() {
        if (mDaoSession == null){
            mDaoSession = mDaoMaster.newSession();
        }
        return mDaoSession;
    }

    @Override
    public void close(){
        closeHelper();
        closeDaoSession();
    }

    public void closeDaoSession() {
        if (mDaoSession != null){
            mDaoSession.clear();
            mDaoSession = null;
        }
    }

    public void closeHelper() {
        if (mHelper != null){
            mHelper.close();
            mHelper = null;
        }
    }


    private class SQLiteUpdateOpenHelper extends DaoMaster.OpenHelper {
        SQLiteUpdateOpenHelper(Context context, String dbName) {
            super(context, dbName);
        }

        @Override
        public void onUpgrade(Database db, int oldVersion, int newVersion) {
            super.onUpgrade(db, oldVersion, newVersion);
            //数据库版本发生变化时候，回调这里。
            MigrationHelper.migrate(db, mDaoSession.getAllDaos().toArray(new Class[0]));
        }
    }
}
