<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="CircleProgressImageView">
        <attr name="progress_color" format="color|reference"></attr>
        <attr name="back_color" format="color|reference"></attr>
        <attr name="progress_width" format="dimension|reference"></attr>
    </declare-styleable>

    <declare-styleable name="PlayingIndicator">
        <attr name="bar_num" format="integer" />
        <attr name="duration" format="integer" />
        <attr name="bar_color" format="color|reference" />
        <attr name="step_num" format="integer" />
        <attr name="is_two_way" format="boolean" />
        <attr name="min_height" format="dimension|reference" />
    </declare-styleable>
</resources>
