package com.kaolafm.opensdk.api.yunxin.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.kaolafm.opensdk.api.live.LiveApiConstant;

/**
 * 云信自定义消息-用户送出礼物的消息
 *
 * <AUTHOR>
 * @date 2023-02-16
 */
public class GiftMsg implements Parcelable {

    private Integer giftId;//打赏礼物id
    private Integer giftCount;//打赏礼物数量
    private String giftName;//打赏礼物名称
    private String giftImg;//打赏礼物图片

    // ***********此处字段替换************
    private String nickname;//用户昵称
    private String avatar;//头像
    private String openUid;//用户id
    private Long sendGiftTime;//发送礼物的时间

    private String senderNickName;//用户昵称
    private String senderAvatar;//头像
    private String senderAccount;//用户id
    private Long sendTime;//发送礼物的时间
    // ***********此处字段替换************

    private int hitCombo;//上一次要连击的礼物数
    private boolean currentStart;//是否从当前数开始连击
    private int jumpCombo;//跳到指定连击数，例如：从1直接显示3，这里的值就是2

    public Integer getGiftId() {
        return giftId;
    }

    public void setGiftId(Integer giftId) {
        this.giftId = giftId;
    }

    public Integer getGiftCount() {
        return giftCount;
    }

    public void setGiftCount(Integer giftCount) {
        this.giftCount = giftCount;
    }

    public String getGiftName() {
        return giftName == null ? "" : giftName;
    }

    public void setGiftName(String giftName) {
        this.giftName = giftName;
    }

    public String getGiftImg() {
        return giftImg == null ? "" : giftImg;
    }

    public void setGiftImg(String giftImg) {
        this.giftImg = giftImg;
    }

    public String getNickname() {
        if (LiveApiConstant.IS_USE_NEW_SOCKET) {
            return getSenderNickName();
        }
        return nickname == null ? "" : nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getAvatar() {
        if (LiveApiConstant.IS_USE_NEW_SOCKET) {
            return getSenderAvatar();
        }
        return avatar == null ? "" : avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getOpenUid() {
        if (LiveApiConstant.IS_USE_NEW_SOCKET) {
            return getSenderAccount();
        }
        return openUid == null ? "" : openUid;
    }

    public void setOpenUid(String openUid) {
        this.openUid = openUid;
    }

    public Long getSendGiftTime() {
        if (LiveApiConstant.IS_USE_NEW_SOCKET) {
            return getSendTime();
        }
        return sendGiftTime;
    }

    public void setSendGiftTime(Long sendGiftTime) {
        this.sendGiftTime = sendGiftTime;
    }

    public String getSenderNickName() {
        return senderNickName == null ? "" : senderNickName;
    }

    public void setSenderNickName(String senderNickName) {
        this.senderNickName = senderNickName;
    }

    public String getSenderAvatar() {
        return senderAvatar == null ? "" : senderAvatar;
    }

    public void setSenderAvatar(String senderAvatar) {
        this.senderAvatar = senderAvatar;
    }

    public String getSenderAccount() {
        return senderAccount == null ? "" : senderAccount;
    }

    public void setSenderAccount(String senderAccount) {
        this.senderAccount = senderAccount;
    }

    public Long getSendTime() {
        return sendTime;
    }

    public void setSendTime(Long sendTime) {
        this.sendTime = sendTime;
    }

    public int getHitCombo() {
        return hitCombo;
    }

    public void setHitCombo(int hitCombo) {
        this.hitCombo = hitCombo;
    }

    public boolean isCurrentStart() {
        return currentStart;
    }

    public void setCurrentStart(boolean currentStart) {
        this.currentStart = currentStart;
    }

    public int getJumpCombo() {
        return jumpCombo;
    }

    public void setJumpCombo(int jumpCombo) {
        this.jumpCombo = jumpCombo;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeValue(this.giftId);
        dest.writeValue(this.giftCount);
        dest.writeString(this.giftName);
        dest.writeString(this.giftImg);
        dest.writeString(this.nickname);
        dest.writeString(this.avatar);
        dest.writeString(this.openUid);
        dest.writeValue(this.sendGiftTime);
        dest.writeString(this.senderNickName);
        dest.writeString(this.senderAvatar);
        dest.writeString(this.senderAccount);
        dest.writeValue(this.sendTime);
        dest.writeInt(this.hitCombo);
        dest.writeByte(this.currentStart ? (byte) 1 : (byte) 0);
        dest.writeInt(this.jumpCombo);
    }

    public GiftMsg() {
    }

    protected GiftMsg(Parcel in) {
        this.giftId = (Integer) in.readValue(Integer.class.getClassLoader());
        this.giftCount = (Integer) in.readValue(Integer.class.getClassLoader());
        this.giftName = in.readString();
        this.giftImg = in.readString();
        this.nickname = in.readString();
        this.avatar = in.readString();
        this.openUid = in.readString();
        this.sendGiftTime = (Long) in.readValue(Long.class.getClassLoader());
        this.senderNickName = in.readString();
        this.senderAvatar = in.readString();
        this.senderAccount = in.readString();
        this.sendTime = (Long) in.readValue(Long.class.getClassLoader());
        this.hitCombo = in.readInt();
        this.currentStart = in.readByte() != 0;
        this.jumpCombo = in.readInt();
    }

    public static final Parcelable.Creator<GiftMsg> CREATOR = new Parcelable.Creator<GiftMsg>() {
        @Override
        public GiftMsg createFromParcel(Parcel source) {
            return new GiftMsg(source);
        }

        @Override
        public GiftMsg[] newArray(int size) {
            return new GiftMsg[size];
        }
    };

    @Override
    public String toString() {
        return "GiftMsg{" +
                "giftId=" + giftId +
                ", giftCount=" + giftCount +
                ", giftName='" + giftName + '\'' +
                ", giftImg='" + giftImg + '\'' +
                ", nickname='" + nickname + '\'' +
                ", avatar='" + avatar + '\'' +
                ", openUid='" + openUid + '\'' +
                ", sendGiftTime=" + sendGiftTime +
                ", senderNickName='" + senderNickName + '\'' +
                ", senderAvatar='" + senderAvatar + '\'' +
                ", senderAccount='" + senderAccount + '\'' +
                ", sendTime=" + sendTime +
                ", hitCombo=" + hitCombo +
                ", currentStart=" + currentStart +
                ", jumpCombo=" + jumpCombo +
                '}';
    }
}
