package com.kaolafm.opensdk.player.core.utils;

import android.content.Context;
import android.media.AudioAttributes;
import android.media.AudioFocusRequest;
import android.media.AudioManager;
import android.os.Build;

import androidx.annotation.RequiresApi;

import com.kaolafm.opensdk.player.core.model.AAudioFocus;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;

/**
 * <AUTHOR> on 2019-06-11.
 */

public class AudioFocusManager extends AAudioFocus {
    private AudioManager mAudioManager;
    private String TAG = "AudioFocusManager";

    public AudioFocusManager(Context context) {
        mAudioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
    }

    private AudioAttributes mCustomAudioAttributes;

    public void setCustomAudioAttributes(AudioAttributes customAudioAttributes) {
        PlayerLogUtil.log(TAG, "setCustomAudioAttributes", "mCustomAudioAttributes = " + mCustomAudioAttributes);

        this.mCustomAudioAttributes = customAudioAttributes;
    }

    /**
     * 请求焦点
     *
     */
    @Override
    public boolean requestAudioFocus() {
        if (mAudioManager == null) {
            return false;
        }
        boolean request = false;
        //通知客户端
        //mKLAudioFocusOperationListener
        PlayerCustomizeManager.getInstance().beforeRequestAudioFocus(mAudioManager);
        int result = 0;

        PlayerLogUtil.log(TAG, "CustomAudioAttributes", ":" + mCustomAudioAttributes);
        if(mCustomAudioAttributes == null){

            result = mAudioManager.requestAudioFocus(mOnAudioFocusChangeListener, AudioManager.STREAM_MUSIC, AudioManager.AUDIOFOCUS_GAIN);

        } else {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {

                result = requestAudioFocusWithCustomAudioAttributes();

            } else {
                PlayerLogUtil.log(TAG, "requestAudioFocus", "SDK_INT <= O");

                result = mAudioManager.requestAudioFocus(mOnAudioFocusChangeListener, AudioManager.STREAM_MUSIC, AudioManager.AUDIOFOCUS_GAIN);
            }
        }

        PlayerLogUtil.log(TAG, "requestAudioFocus", "result = " + result);
        // result返回1是指申请成功，不是音频焦点返回1
        if(result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED){
            request = true;
            notifyAudioFocusChange(false, AudioManager.AUDIOFOCUS_GAIN);
        }
        return request;
    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    private int requestAudioFocusWithCustomAudioAttributes(){
        AudioFocusRequest afr = new AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN)
                .setOnAudioFocusChangeListener(mOnAudioFocusChangeListener)
                .setAudioAttributes(mCustomAudioAttributes)
                .build();
        PlayerLogUtil.log(TAG, "requestAudioFocusWithCustomAudioAttributes-SDK_INT > O", "afr = " + afr);
        int result = mAudioManager.requestAudioFocus(afr);
        PlayerLogUtil.log(TAG, "requestAudioFocusWithCustomAudioAttributes-SDK_INT > O", "result = " + result);
        return result;
    }


    @Override
    public boolean abandonAudioFocus() {
        if (mAudioManager == null) {
            return false;
        }
        boolean abandon = false;
        int rect = mAudioManager.abandonAudioFocus(mOnAudioFocusChangeListener);
        PlayerLogUtil.log(TAG, "abandonAudioFocus", "result = " + rect);
        // rect返回1是指释放成功，不是音频焦点返回1
        if(rect == AudioManager.AUDIOFOCUS_REQUEST_GRANTED){
            abandon = true;
            notifyAudioFocusChange(false, AudioManager.AUDIOFOCUS_LOSS);
        }
        return abandon;
    }


    private AudioManager.OnAudioFocusChangeListener mOnAudioFocusChangeListener = focusChange -> {
        notifyAudioFocusChange(true, focusChange);
    };

}
