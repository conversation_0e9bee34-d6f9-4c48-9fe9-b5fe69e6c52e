package com.kaolafm.ad.di.module;

import android.text.TextUtils;

import com.kaolafm.ad.AdConstant;
import com.kaolafm.ad.api.internal.OrderedPair;
import com.kaolafm.ad.di.qualifier.AdInitParam;
import com.kaolafm.ad.di.qualifier.AdvertHostQualifier;
import com.kaolafm.ad.di.qualifier.BasicParam;
import com.kaolafm.ad.di.qualifier.CommonParam;
import com.kaolafm.ad.profile.AdvertisingProfile;
import com.kaolafm.ad.util.AdParamsUtil;
import com.kaolafm.opensdk.HostConstants;
import com.kaolafm.opensdk.Options;
import com.kaolafm.opensdk.account.profile.KaolaProfile;
import com.kaolafm.opensdk.account.token.KaolaAccessToken;
import com.kaolafm.opensdk.di.qualifier.AccessTokenQualifier;
import com.kaolafm.opensdk.di.qualifier.DomainQualifier;
import com.kaolafm.opensdk.di.qualifier.ParamQualifier;
import com.kaolafm.opensdk.di.qualifier.ProfileQualifier;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.utils.BaseHttpsStrategy;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import dagger.Module;
import dagger.Provides;
import dagger.multibindings.IntoMap;
import dagger.multibindings.StringKey;

/**
 * 公共参数提供类
 * <AUTHOR> Yan
 * @date 2020-01-13
 */
@Module
public class AdCommonParamModule {

    /**
     * 提供激活
     * @param profile
     * @param kaolaProfile
     * @param accessToken
     * @return
     */
    @Provides
    @AdInitParam
    String provideActiveParam(@ProfileQualifier AdvertisingProfile profile, @ProfileQualifier KaolaProfile kaolaProfile, @AccessTokenQualifier KaolaAccessToken accessToken) {
        ArrayList<Object> params = new ArrayList<Object>() {{
            add(AdParamsUtil.getTime());
            add(profile.getAppId());
            add(kaolaProfile.getDeviceId());
            add(accessToken.getUserId());
            //K-radio SDK的appId是广告SDK的渠道id
            add(kaolaProfile.getAppId());
            add(profile.getOsType());
            add(profile.getDeviceType());
            add(profile.getOsVersion());
            add(profile.getDeviceManufacturer());
            add(profile.getResolution());
            add(profile.getVersionName());
            add(profile.getIp());
            add(kaolaProfile.getCarType());
            add(profile.getBrand());
        }};
        return AdParamsUtil.createParamData(params);
    }

    /**
     * 提供一般的公共参数
     * @param profile
     * @param kaolaProfile
     * @param basicParams
     * @return
     */
    @Provides
    @CommonParam
    Set<OrderedPair> provideCommonParam(@ProfileQualifier AdvertisingProfile profile, @ProfileQualifier KaolaProfile kaolaProfile, @BasicParam Set<OrderedPair> basicParams) {
        Set<OrderedPair> orderedPairs = new HashSet<>(basicParams);
        orderedPairs.add(new OrderedPair(60, "device_type", profile.getDeviceType()));
        orderedPairs.add(new OrderedPair(70, "os_type", profile.getOsType()));
        orderedPairs.add(new OrderedPair(180, "os_version", profile.getOsVersion()));
        orderedPairs.add(new OrderedPair(190, "app_version", kaolaProfile.getVersionName()));
        orderedPairs.add(new OrderedPair(200, "sdk_version", profile.getVersionName()));
        return orderedPairs;
    }

    /**
     * 提供最基础的公共参数
     * @param profile
     * @param kaolaProfile
     * @param accessToken
     * @return
     */
    @Provides
    @BasicParam
    Set<OrderedPair> provideBasicCommonParam(@ProfileQualifier AdvertisingProfile profile, @ProfileQualifier KaolaProfile kaolaProfile, @AccessTokenQualifier KaolaAccessToken accessToken) {
        Set<OrderedPair> orderedPairs = new HashSet<>();
        orderedPairs.add(new OrderedPair(30, "appId", profile.getAppId()));
        orderedPairs.add(new OrderedPair(40, "udid", kaolaProfile.getDeviceId()));
        orderedPairs.add(new OrderedPair(50, "uid", accessToken.getUserId()));
        orderedPairs.add(new OrderedPair(220, "channel_id", kaolaProfile.getAppId()));
        orderedPairs.add(new OrderedPair(230, "car_type", kaolaProfile.getCarType()));
        orderedPairs.add(new OrderedPair(240, "brand", profile.getBrand()));
        return orderedPairs;
    }

    @Provides
    @AppScope
    Map<String, String> provideParams(@ProfileQualifier AdvertisingProfile profile, @ProfileQualifier KaolaProfile kaolaProfile, @AccessTokenQualifier KaolaAccessToken accessToken) {
        return new HashMap<String, String>() {{
            put("deviceId", kaolaProfile.getDeviceId());
            put("userId", accessToken.getUserId());
            put("deviceType", profile.getDeviceType()+"");
            put("osType", profile.getOsType());
            put("appId", profile.getAppId());
            put("channelId", kaolaProfile.getAppId());
            put("carType", kaolaProfile.getCarType());
            put("brand", profile.getBrand());
            put("appStartupSession", UUID.randomUUID().toString());
            put("lat", kaolaProfile.getLat());
            put("lng", kaolaProfile.getLng());
            String capabilities = profile.getCapabilities();
            if (!TextUtils.isEmpty(capabilities)) {
                put("capabilities", capabilities);
            }
        }};
    }

    @Provides
    @ParamQualifier
    @IntoMap
    @StringKey(AdConstant.DOMAIN_NAME_AD)
    Map<String, String> provideEngineParams(@AppScope Map<String, String> params) {
        return params;
    }

    @Provides
    boolean isUseHttps(Options options) {
        return options.isUseHttps(BaseHttpsStrategy.AD_PARAM);
    }

    @Provides
    @DomainQualifier
    @IntoMap
    @StringKey(AdConstant.DOMAIN_NAME_AD)
    String provideEngineHost(boolean useHttps) {
        return useHttps? HostConstants.AD_ENGINE_HOST : AdConstant.AD_ENGINE_HOST_HTTP;
    }

    @Provides
    @DomainQualifier
    @IntoMap
    @StringKey(AdConstant.DOMAIN_NAME_AD_REPORT)
    String provideReportHost(boolean useHttps) {
        return useHttps? HostConstants.AD_REPROT_HOST : AdConstant.AD_REPROT_HOST_HTTP;
    }

    @Provides
    @AdvertHostQualifier
    String provideSocketHost(boolean useHttps) {
        return useHttps? HostConstants.AD_SOCKET_HOST : AdConstant.AD_SOCKET_HOST_HTTP;
    }

}
