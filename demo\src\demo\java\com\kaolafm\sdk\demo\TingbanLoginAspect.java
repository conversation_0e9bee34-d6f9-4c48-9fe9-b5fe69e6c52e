package com.kaolafm.sdk.demo;

import android.content.Context;
import android.content.SharedPreferences;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import android.widget.Button;
import android.widget.ImageView;

import com.kaolafm.opensdk.demo.DemoApplication;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.demo.login.KaolaLoginActivity;
import com.kaolafm.opensdk.log.Logging;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

/**
 * <AUTHOR>
 * @date 2019-06-14
 */
@Aspect
public class TingbanLoginAspect {

    private boolean useHttps = true;
    private SharedPreferences sp;

    @Around("execution(* com.kaolafm.opensdk.http.core.o.b())")
    public Object setUseHttp(ProceedingJoinPoint point) throws Throwable {
//        useHttps = isHttps();
        Logging.Log.e("TingbanLoginAspect", "setUseHttps: " + useHttps);
        return useHttps;
    }

    @After("execution(* com.kaolafm.opensdk.demo.login.KaolaLoginActivity.initView(..))")
    public void switchHttp(JoinPoint point) throws Throwable {
        KaolaLoginActivity activity = (KaolaLoginActivity) point.getThis();
        if (activity != null) {
            ConstraintLayout clKaolaRoot = activity.clKaolaRoot;

            Button button = new Button(activity);
            button.setId(R.id.btn_switch_http);
            button.setText("切换到" + (useHttps ? "http" : "https"));
            button.setLayoutParams(new ConstraintLayout.LayoutParams(ConstraintLayout.LayoutParams.WRAP_CONTENT, ConstraintLayout.LayoutParams.WRAP_CONTENT));
            button.setOnClickListener(v -> {
                switchHttp(!useHttps);
                button.setText("切换到" + (useHttps ? "http" : "https"));
            });
            clKaolaRoot.addView(button);

            ImageView ivKaolaAvatar = activity.mIvKaolaAvatar;
            ConstraintSet constraintSet = new ConstraintSet();
            constraintSet.clone(clKaolaRoot);
            constraintSet.connect(button.getId(), ConstraintSet.LEFT, ivKaolaAvatar.getId(), ConstraintSet.LEFT);
            constraintSet.connect(button.getId(), ConstraintSet.RIGHT, ivKaolaAvatar.getId(), ConstraintSet.RIGHT);
            constraintSet.connect(button.getId(), ConstraintSet.TOP, ivKaolaAvatar.getId(), ConstraintSet.BOTTOM);
            constraintSet.applyTo(clKaolaRoot);
        }
    }

    private boolean isHttps() {
        if (sp == null) {
            Context context = DemoApplication.getContext();
            sp = context.getSharedPreferences("http_https", Context.MODE_PRIVATE);
        }
        return sp.getBoolean("isHttps", true);
    }

    private void switchHttp(boolean isHttps) {
        useHttps = isHttps;
        Context context = DemoApplication.getContext();
        SharedPreferences.Editor edit = context.getSharedPreferences("http_https", Context.MODE_PRIVATE).edit();
        edit.putBoolean("isHttps", isHttps);
        edit.apply();
    }

}
