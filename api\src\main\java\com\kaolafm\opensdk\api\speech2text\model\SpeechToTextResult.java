package com.kaolafm.opensdk.api.speech2text.model;

/**
 * 语音转换结果
 */
public class SpeechToTextResult extends BaseSpeechSocketMessage {
    /**
     * 句子开始
     */
    public static final int SENTENCE_START = 0;
    /**
     * 句子返回中
     */
    public static final int SENTENCE_CENTER = 1;
    /**
     * 句子结束
     */
    public static final int SENTENCE_END = 2;


    /**
     * 在音频流中的序号
     * 相同序号的返回，后返回的text包含之前的内容。比如“北京的天气。今天是周日。”这段话的返回过程。
     * 返回过程：
     * 1：{“taskId”:"xxx","index":1,"text":"北"}
     * 2:
     * {“taskId”:"xxx","index":1,"text":"北京"}
     * 3:{“taskId”:"xxx","index":1,"text":"北京的"}
     * 4:
     * {“taskId”:"xxx","index":1,"text":"北京的天气。"}
     * 5:{“taskId”:"xxx","index":2,"text":"今"}
     * 6:{“taskId”:"xxx","index":2,"text":"今天"}
     * 7:{“taskId”:"xxx","index":2,"text":"今天是"}
     * 8：{“taskId”:"xxx","index":2,"text":"今天是周日"}
     */
    private int index;
    /**
     * 当前句子开始的时长
     */
    private int beginTime;
    /**
     * 当前翻译结果距离开始时长
     */
    private int time;
    /**
     * 转换结果
     */
    private String text;
    /**
     * 返回句子状态
     * 0:句子开始
     * 1:句子返回中
     * 2::句子结束
     */
    private int state = SENTENCE_START;

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public int getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(int beginTime) {
        this.beginTime = beginTime;
    }

    public int getTime() {
        return time;
    }

    public void setTime(int time) {
        this.time = time;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    @Override
    public String toString() {
        return "SpeechVoiceResult{" +
                "taskId='" + getTaskId() + '\'' +
                ", index=" + index +
                ", beginTime=" + beginTime +
                ", time=" + time +
                ", text='" + text + '\'' +
                ", state=" + state +
                '}';
    }
}