package com.kaolafm.opensdk.di.module;

import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.di.scope.AppScope;

import dagger.Module;
import dagger.Provides;

/**
 * 全局实例
 * <AUTHOR>
 * @date 2018/7/23
 */
@Module
public class AppModule {

    @Provides
    @AppScope
    AccessTokenManager provideAccessTokenManager(){
        return AccessTokenManager.getInstance();
    }
}
