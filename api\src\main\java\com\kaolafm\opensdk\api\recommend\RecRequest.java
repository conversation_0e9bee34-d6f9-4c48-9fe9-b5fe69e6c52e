package com.kaolafm.opensdk.api.recommend;


import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.recommend.model.BaseSceneListData;
import com.kaolafm.opensdk.api.recommend.model.SceneDataList;
import com.kaolafm.opensdk.http.core.HttpCallback;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> on 2019-07-18.
 * 负反馈请求, 使用opensdk网络请求.
 */

public class RecRequest extends BaseRequest {

    private RecApiService mRecApiService;

    public RecRequest() {
        mUrlManager.putDomain(ApiHostConstants.RECOMMEND_DOMAIN_NAME, ApiHostConstants.RECOMMEND_HOST);
        mRecApiService = obtainRetrofitService(RecApiService.class);
    }

    /**
     * 获取场景电台列表
     *
     * @param code
     * @param callback
     */
    public void getSceneRadioList(String code
            , String size
            , Map<String, String> extraParams //防止特殊情况，扩展
            , HttpCallback<BaseSceneListData<List<SceneDataList>>> callback) {
        HashMap<String, String> params = new HashMap<>();
        if (code != null) {
            params.put("code", code);
        }
        if (size != null) {
            params.put("size", size);
        }
        if (extraParams != null && extraParams.size() > 0) {
            params.putAll(extraParams);
        }
        doHttpDeal(mRecApiService.getSceneRadioList(params), callback);
    }


    /**
     * car_type_code	车型场景代码
     * time_code	时间场景代码
     * sex_code	性别场景代码
     * age_code	年龄场景代码
     * weather_code	天气场景代码
     * speed_code	车速场景代码
     * special_scenes_code	特殊场景代码
     */
    public void getPresentRadioList(String carTypeCode
            , String timeCode
            , String sexCode
            , String ageCode
            , String weatherCode
            , String speedCode
            , String specialScenesCode
            , String size
            , Map<String, String> extraParams //防止特殊情况扩展
            , HttpCallback<BaseSceneListData<List<SceneDataList>>> callback) {

        String jsonString = "";
        try {
            JSONObject jsonObject = new JSONObject();
            if (!TextUtils.isEmpty(carTypeCode)) {
                jsonObject.put("car_type_code", carTypeCode);
            }
            if (!TextUtils.isEmpty(timeCode)) {
                jsonObject.put("time_code", timeCode);
            }
            if (!TextUtils.isEmpty(sexCode)) {
                jsonObject.put("sex_code", sexCode);
            }
            if (!TextUtils.isEmpty(ageCode)) {
                jsonObject.put("age_code", ageCode);
            }
            if (!TextUtils.isEmpty(weatherCode)) {
                jsonObject.put("weather_code", weatherCode);
            }
            if (!TextUtils.isEmpty(speedCode)) {
                jsonObject.put("speed_code", speedCode);
            }
            if (!TextUtils.isEmpty(specialScenesCode)) {
                jsonObject.put("special_scenes_code", specialScenesCode);
            }
            if(jsonObject.length() != 0) {
                jsonString = jsonObject.toString();
            } else {
                jsonString = null;
            }
            Log.i("RecRequest", jsonString);
        } catch (Exception e) {
            Log.i("RecRequest", e.toString());
        }

        HashMap<String, String> params = new HashMap<>();
        if (!TextUtils.isEmpty(jsonString)) {
            params.put("conditions", jsonString);
        }
        if (size != null) {
            params.put("size", size);
        }
        if (extraParams != null && extraParams.size() > 0) {
            params.putAll(extraParams);
        }
        doHttpDeal(mRecApiService.getPresentRadioList(params), callback);
    }
}