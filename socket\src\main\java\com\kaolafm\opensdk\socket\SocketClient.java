package com.kaolafm.opensdk.socket;

import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.operation.model.column.ActivityDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.AlbumDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.AudioDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.BroadcastDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.CategoryColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.Column;
import com.kaolafm.opensdk.api.operation.model.column.ColumnGrp;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.LiveProgramDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.RadioDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.RadioQQMusicDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.SearchResultColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.TVDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.TopicDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.WebViewColumnMember;
import com.kaolafm.opensdk.api.speech2text.model.BaseSpeechSocketMessage;
import com.kaolafm.opensdk.api.speech2text.model.SpeechSocketMessageSender;
import com.kaolafm.opensdk.api.speech2text.model.SpeechToTextError;
import com.kaolafm.opensdk.api.speech2text.model.SpeechToTextResult;
import com.kaolafm.opensdk.http.core.RuntimeTypeAdapterFactory;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.log.Logging;

import org.greenrobot.greendao.annotation.NotNull;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import io.socket.client.IO;
import io.socket.client.Manager;
import io.socket.client.Socket;
import io.socket.emitter.Emitter;
import io.socket.emitter.Emitter.Listener;
import io.socket.engineio.client.Transport;
import io.socket.engineio.parser.Packet;

/**
 * Created by kaolafm on 2019/3/11.
 */
class SocketClient implements Client {
    private static final String TAG = "SocketClient";

    private Socket mSocket;

    private String mToken;

    private String socketHost;

    private Map<String, String> map;

    private TransportListener mTransportListener = new TransportListener();

    private Emitter.Listener mConnectListener, mDisConnectListener, mConnectTimeOutListener, mConnectErrorListener;

    private ConnectErrorListener mBusinessConnectErrorListener;
    private ConnectLostListener mBusinessConnectLostListener;
    private IConnectListener mBusinessConnectListener;

    private Map<Listener, SocketListener> mListeners = new ConcurrentHashMap<>();

    private static final class SocketClientHolder {
        private static final SocketClient INSTANCE = new SocketClient();
    }

    public static SocketClient getInstance() {
        return SocketClientHolder.INSTANCE;
    }

    @Override
    public void create() {
        init(createLinkStringByGet(map));
    }

    @Override
    public void setSocketHost(String socketHost) {
        this.socketHost = socketHost;
    }

    @Override
    public void setMap(Map<String, String> map) {
        this.map = map;
    }

    private void initListener() {
        mConnectListener = (Object... args) -> {
            Log.i(TAG, "connect");
            request();
            ThreadUtil.runOnUI(() -> {
                if (mBusinessConnectListener != null) {
                    mBusinessConnectListener.onConnected();
                }
            });
        };

        mDisConnectListener = (Object... args) -> {
            Log.i(TAG, "socket---------->DisConnect");
            ThreadUtil.runOnUI(() -> {
                if (mBusinessConnectLostListener != null) {
                    mBusinessConnectLostListener.onConnectLost(args);
                }
            });
        };

        mConnectTimeOutListener = (Object... args) -> {
            Log.i(TAG, "socket---------->ConnectTimeOut");
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001198038112?userId=1229522问题
            reset();
        };

        mConnectErrorListener = (Object... args) -> {
            String result = "";
            if (args.length > 0) {
                result = args[0].toString();
            }
            Log.i(TAG, "connecterror------------->" + result);

            ThreadUtil.runOnUI(() -> {
                if (null != mBusinessConnectErrorListener) {
                    mBusinessConnectErrorListener.onConnectError(args);
                }
            });
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300002119691512?userId=1229522问题
            reset();
        };

        setMessageListener(Socket.EVENT_CONNECT, mConnectListener);
        setMessageListener(Socket.EVENT_DISCONNECT, mDisConnectListener);
        setMessageListener(Socket.EVENT_CONNECT_TIMEOUT, mConnectTimeOutListener);
        setMessageListener(Socket.EVENT_CONNECT_ERROR, mConnectErrorListener);
    }

    /**
     * 长连接连接上就直接进行请求，第一次请求需要添加上公共参数。
     */
    private void request() {
        if (canEmit()) {
            int index = 0;
            for (SocketListener listener : mListeners.values()) {
                //只第一个请求添加公共参数
                if (listener.isNeedRequest()) {
                    request(listener, listener.isNeedParams());
                }
                index++;
            }
        }
    }

    public void init(String token) {
        Log.i(TAG, "socket---------->init = " + mSocket);
        if (mSocket != null) {
            if (!mToken.equals(token)) {
                newSocket(token);
                initListener();
            }
        } else {
            newSocket(token);
            initListener();
        }
    }

    @Override
    public void reset() {
        if (mSocket != null) {
            mSocket.off();
            mSocket.disconnect();
            mSocket = null;
        }
        init(mToken);
    }

    @Override
    public void open() {
        if (mSocket != null) {
            Log.i(TAG, "socket---------->open");
            mSocket.connect();
        }
    }

    @Override
    public void release() {
        mTransportListener.transportOff();
        removeMessageListener(Manager.EVENT_TRANSPORT, mTransportListener);
        removeMessageListener(Socket.EVENT_CONNECT, mConnectListener);
        removeMessageListener(Socket.EVENT_DISCONNECT, mDisConnectListener);
        removeMessageListener(Socket.EVENT_CONNECT_TIMEOUT, mConnectTimeOutListener);
        removeMessageListener(Socket.EVENT_CONNECT_ERROR, mConnectErrorListener);
        removeBusinessListener();
        mListeners.clear();
        if (mSocket != null) {
            mSocket.disconnect();
            mSocket = null;
        }
    }

    /**
     * 移除外部添加的监听。
     */
    private void removeBusinessListener() {
        Set<Entry<Listener, SocketListener>> entries = mListeners.entrySet();
        for (Entry<Listener, SocketListener> next : entries) {
            removeMessageListener(next.getValue().getEvent(), next.getKey());
        }
        mListeners.clear();
    }

    /**
     * 添加监听
     *
     * @param socketListener 监听/回调
     */
    @Override
    public <T> void addListener(SocketListener<T> socketListener) {
        if (!mListeners.containsValue(socketListener)) {
            Listener listener = getEmitterListener(socketListener);
            mListeners.put(listener, socketListener);
            setMessageListener(socketListener.getEvent(), listener);
        }
    }

    @Override
    public void removeListener(SocketListener listener) {
        Iterator<Entry<Listener, SocketListener>> iterator = mListeners.entrySet().iterator();
        while (iterator.hasNext()) {
            Entry<Listener, SocketListener> entry = iterator.next();
            if (entry.getValue() == listener) {
                removeMessageListener(listener.getEvent(), entry.getKey());
                iterator.remove();
            }
        }
    }

    /**
     * 获取针对某个event的socket框架的监听，
     *
     * @param listener 回调
     */
    private <T> Listener getEmitterListener(final SocketListener<T> listener) {
        return args -> {
            Logging.i(TAG, "listener is " + listener);
            if (listener == null) {
                return;
            }

            if (!ListUtil.isEmpty(args)) {
                Object msg = args[0];
                Logging.i(TAG, "getEmitterListener args[0] is " + msg);
                if (args.length > 1) { //io.socket.client.Socket类 onevent是remove(0) emitBuffered是get(0)
                    msg = args[1];
                    Logging.i(TAG, "getEmitterListener args[1] is " + msg);
                }
                BaseResult<T> baseResult = (BaseResult<T>) getResult(msg, listener.getClass());
                if (baseResult != null) {
                    Log.i(TAG, "data  = " + baseResult.toString());
                    if (SocketEvent.LOCATION_UPDATE_RESPONSE.equals(listener.getEvent())) { //需要直接用code做区分
                        if (20000 == baseResult.getCode()) {
                            ThreadUtil.runOnUI(() -> listener.onSuccess(((T) "位置上传成功")));
                        } else {
                            ThreadUtil.runOnUI(() -> listener.onError(new ApiException("位置上传失败")));
                        }
                        return;
                    }
                    if (baseResult.getResult() != null) {
                        ThreadUtil.runOnUI(() -> listener.onSuccess(baseResult.getResult()));
                        return;
                    } else {
                        Log.i(TAG, "getEmitterListener data is null!");
                    }
                } else {
                    Log.i(TAG, "baseResult is null");
                }
            }
            ThreadUtil.runOnUI(() -> listener.onError(new ApiException("数据为空")));
        };
    }

    /**
     * 解析结果，返回泛型的bean。
     * 这里可以抽出一个工具类，目前只有Socket用到，后面其他有用到在抽个工具类
     *
     * @param msg   原始json数据
     * @param clazz
     * @return 解析后的结果。
     */
    private <T> BaseResult<T> getResult(Object msg, Class<T> clazz) {
        if (msg == null) {
            return null;
        }
        Log.i(TAG, "msg = " + msg.toString() + " clazz:" + clazz.toString());
        Gson gson = new GsonBuilder()
                .registerTypeAdapterFactory(RuntimeTypeAdapterFactory.of(ColumnGrp.class)
                        .registerSubtype(ColumnGrp.class)
                        .registerSubtype(Column.class))
                .registerTypeAdapterFactory(RuntimeTypeAdapterFactory.of(ColumnMember.class)
                        .registerSubtype(AlbumDetailColumnMember.class)
                        .registerSubtype(AudioDetailColumnMember.class)
                        .registerSubtype(BroadcastDetailColumnMember.class)
                        .registerSubtype(TVDetailColumnMember.class)
                        .registerSubtype(CategoryColumnMember.class)
                        .registerSubtype(TopicDetailColumnMember.class)
                        .registerSubtype(ActivityDetailColumnMember.class)
                        .registerSubtype(LiveProgramDetailColumnMember.class)
                        .registerSubtype(RadioDetailColumnMember.class)
                        .registerSubtype(RadioQQMusicDetailColumnMember.class)
                        .registerSubtype(SearchResultColumnMember.class)
                        .registerSubtype(WebViewColumnMember.class))
                .registerTypeAdapterFactory(RuntimeTypeAdapterFactory.of(BaseSpeechSocketMessage.class)
                        .registerSubtype(SpeechSocketMessageSender.class)
                        .registerSubtype(SpeechToTextError.class)
                        .registerSubtype(SpeechToTextResult.class))
                .create();

        Type[] genericInterfaces = clazz.getGenericInterfaces();
        if (genericInterfaces.length == 0) {
            Log.i(TAG, "genericInterfaces is empty, try super");
            genericInterfaces = clazz.getSuperclass().getGenericInterfaces();
        }
        if (genericInterfaces.length == 0) {
            Log.i(TAG, "genericInterfaces still empty");
            return null;
        }
        Type typeArgument = ((ParameterizedType) genericInterfaces[0]).getActualTypeArguments()[0];
        Log.i(TAG, "type argument:" + typeArgument);
        Type type = new ParameterizedTypeImpl(BaseResult.class, new Type[]{typeArgument});
        Log.i(TAG, "gson type:" + type.toString());
        BaseResult<T> result = gson.fromJson(msg.toString(), type);
        Log.i(TAG, "result:" + result.toString());
        return result;
    }

    public void sendMsg(String event, Object msg) {
        if (canEmit()) {
            mSocket.emit(event, msg);
        }
    }

    /**
     * 长连接请求，用于非第一次请求，不添加公共参数
     *
     * @param listener 长连接监听/回调
     */
    @Override
    public <T> void request(SocketListener<T> listener) {
        request(listener, listener.isNeedParams());
    }

    /**
     * 请求socket
     *
     * @param listener        长连接监听
     * @param addCommonParams 是否添加公共参数，只是第一次请求添加，后面请求就不在添加。
     */
    public <T> void request(SocketListener<T> listener, boolean addCommonParams) {
        Map<String, Object> params = new HashMap<>();
        if (addCommonParams) {
            params.putAll(map);
        }
        params = listener.getParams(params);
        Gson gson = new GsonBuilder().enableComplexMapKeySerialization().create();
        JSONObject jsonObject = null;
        try {
            jsonObject = new JSONObject(gson.toJson(params));
            Log.i(TAG, "请求event：" + listener.getEvent() + " request = " + jsonObject.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        request(jsonObject, listener);
    }

    /**
     * 直接发送带有信息的长连接请求
     *
     * @param msg      发送的信息
     * @param callback 回调
     */
    @Override
    public <T> void request(Object msg, SocketListener<T> callback) {
        if (callback != null) {
            addListener(callback);
            if (canEmit()) {
                sendMsg(callback.getEvent(), msg);
            } else {
                Logging.e("socket没有连接成功");
            }
        }
    }

    /**
     * 添加对应事件的长连接监听到socket框架
     *
     * @param event    事件
     * @param listener 监听
     */
    public SocketClient setMessageListener(String event, Emitter.Listener listener) {
        if (mSocket != null) {
            mSocket.on(event, listener);
        }
        return this;
    }

    /**
     * 移除对应事件的长连接监听
     *
     * @param event    事件
     * @param listener 监听
     */
    public SocketClient removeMessageListener(String event, Emitter.Listener listener) {
        if (mSocket != null) {
            mSocket.off(event, listener);
        }
        return this;
    }

    /**
     * 检查能否发送Socket请求信息
     *
     * @return true 可以发送
     */
    @Override
    public boolean canEmit() {
        return mSocket != null && mSocket.connected();
    }

    @Override
    public void registerConnectErrorListener(ConnectErrorListener connectErrorListener) {
        this.mBusinessConnectErrorListener = connectErrorListener;
    }

    @Override
    public void registerConnectLostListener(ConnectLostListener connectLostListener) {
        this.mBusinessConnectLostListener = connectLostListener;
    }

    public void registerConnectListener(IConnectListener connectListener) {
        this.mBusinessConnectListener = connectListener;
    }

    private class TransportListener implements Emitter.Listener {

        private Transport transport;

        public void transportOff() {
            if (transport != null) {
                transport.off();
            }
        }

        @Override
        public void call(Object... args) {
            transport = (Transport) args[0];
            Log.i(TAG, "EVENT_TRANSPORT----->" + transport);
            transport.off();
            transport.on(Transport.EVENT_PACKET, mPackListener);
        }
    }

    private Emitter.Listener mPackListener = args -> {
        Packet packet = (Packet) args[0];
        Log.i(TAG, "EVENT_PACKET----->type = " + packet.type + "---->data = " + packet.data);
    };

    /**
     * 创建新的Socket对象
     */
    private void newSocket(String token) {
        mToken = token;
        IO.Options options = new IO.Options();
        options.reconnection = true;
        options.forceNew = true;
        options.reconnectionAttempts = 10;
        options.reconnectionDelay = 3000;
        options.reconnectionDelayMax = 30000;
        options.timeout = 10000;
        options.transports = new String[]{"websocket", "polling"};
        options.query = token;
        try {
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001145260719?userId=1917386问题
            if (mSocket != null) {
                mSocket.disconnect();
            }
            Log.i(TAG, "->socketHost:" + socketHost);
            mSocket = IO.socket(socketHost, options);

            mSocket.io().on(Manager.EVENT_TRANSPORT, mTransportListener);
            Log.e(TAG, "===socket Create WebSocket success socketHost:" + socketHost);
        } catch (Exception e) {
            Log.e(TAG, "===socket Create WebSocket failed:", e);
        }
        Log.i(TAG, "newSocket---------->token = " + token);
    }

    /**
     * 解析泛型的类。
     */
    private class ParameterizedTypeImpl implements ParameterizedType {

        private final Class raw;
        private final Type[] args;

        public ParameterizedTypeImpl(Class raw, Type[] args) {
            this.raw = raw;
            this.args = args != null ? args : new Type[0];
        }

        @NotNull
        @Override
        public Type[] getActualTypeArguments() {
            return args;
        }

        @NotNull
        @Override
        public Type getRawType() {
            return raw;
        }

        @Override
        public Type getOwnerType() {
            return null;
        }
    }

    /**
     * 　　* 把数组所有元素排序，并按照“参数=参数值”的模式用“&”字符拼接成字符串
     * 　　* @param params 需要排序并参与字符拼接的参数组
     * 　　* @return 拼接后字符串
     * 　　* @throws UnsupportedEncodingException
     */
    public String createLinkStringByGet(Map<String, String> params) {
        List<String> keys = new ArrayList<>(params.keySet());
        String prestr = "";
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = params.get(key);
            if (i == keys.size() - 1) {//拼接时，不包括最后一个&字符
                prestr = prestr + key + "=" + value;
            } else {
                prestr = prestr + key + "=" + value + "&";
            }
        }
        return prestr;
    }
}
