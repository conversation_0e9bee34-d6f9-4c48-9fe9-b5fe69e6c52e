package com.kaolafm.opensdk.demo.history;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import butterknife.BindView;

import com.bumptech.glide.Glide;
import com.kaolafm.opensdk.api.history.model.ListeningHistory;
import com.kaolafm.opensdk.demo.BaseAdapter;
import com.kaolafm.opensdk.demo.BaseHolder;
import com.kaolafm.opensdk.demo.R;

/**
 * <AUTHOR>
 * @date 2019/3/11
 */
public class HistoryAdapter extends BaseAdapter<ListeningHistory> {

    @Override
    protected BaseHolder<ListeningHistory> getViewHolder(View view, int viewType) {
        return new HistoryHolder(view);
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_history;
    }

    static class HistoryHolder extends BaseHolder<ListeningHistory> {

        @BindView(R.id.iv_history_item_icon)
        ImageView mIvHistoryItemIcon;

        @BindView(R.id.tv_history_item_title)
        TextView mTvHistoryItemTitle;

        @BindView(R.id.tv_history_item_listen)
        TextView tv_history_item_listen;

        @BindView(R.id.item_tag)
        TextView item_tag;


        public HistoryHolder(View itemView) {
            super(itemView);
        }

        @Override
        public void setupData(ListeningHistory listeningHistory, int position) {
            Glide.with(itemView).load(listeningHistory.getPicUrl()).into(mIvHistoryItemIcon);
            mTvHistoryItemTitle.setText(listeningHistory.getRadioTitle());
            tv_history_item_listen.setText("收听量：" + listeningHistory.getListenCount());
            if (listeningHistory.getType() == 0||listeningHistory.getType() == 1) {
                item_tag.setVisibility(View.VISIBLE);
                if (listeningHistory.getVip() == 1) {
                    item_tag.setText("VIP");
                } else if (listeningHistory.getFine() == 1) {
                    item_tag.setText("精品");
                }else {
                    item_tag.setText("无");
                }
                item_tag.setVisibility(View.VISIBLE);
            }else {
                item_tag.setVisibility(View.GONE);
            }
        }
    }
}
