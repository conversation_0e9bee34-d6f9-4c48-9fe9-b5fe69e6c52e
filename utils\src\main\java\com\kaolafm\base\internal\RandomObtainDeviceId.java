package com.kaolafm.base.internal;

import android.content.Context;
import com.kaolafm.base.utils.MD5;
import java.util.UUID;

/**
 * 通过随机数生成uuid。新的生成方式，k-radio使用的是这种
 * <AUTHOR>
 * md5(uuid运算(当前的时间*1000+ (1000以内的随机数)))
 * @date 2019-06-17
 */
public class RandomObtainDeviceId extends BaseObtainDeviceId {

    private static final String CHARSET_NAME = "UTF-8";

    public RandomObtainDeviceId() {
    }

    @Override
    protected String createUUID(Context context) {
        String uuid;
        long time = System.currentTimeMillis();
        int index = (int) (Math.random() * 1000);
        time = time * 1000 + index;
        String timeStr = String.valueOf(time);
        try {
            uuid = MD5.getMD5Str(UUID.nameUUIDFromBytes(timeStr.getBytes(CHARSET_NAME)).toString());
        } catch (Exception e) {
            uuid = null;
            e.printStackTrace();
        }
        return uuid;
    }
}
