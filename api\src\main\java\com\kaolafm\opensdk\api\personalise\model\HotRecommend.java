package com.kaolafm.opensdk.api.personalise.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

/**
 * 热门推荐
 * 用于搜索、订阅等场景无内容时显示推荐内容
 */
public class HotRecommend implements Parcelable {
    /**
     * 推荐内容id
     */
    @SerializedName("contentId")
    private long contentId;
    /**
     * 名称
     */
    @SerializedName("name")
    private String name;
    /**
     * 推荐类型
     * 0-专辑 1-单曲 3-AI电台 11-广播 12-听电视 13-专题
     */
    @SerializedName("type")
    private int type;

    public HotRecommend() {
    }

    protected HotRecommend(Parcel in) {
        contentId = in.readLong();
        name = in.readString();
        type = in.readInt();
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(contentId);
        dest.writeString(name);
        dest.writeInt(type);
    }

    public static final Creator<HotRecommend> CREATOR = new Creator<HotRecommend>() {
        @Override
        public HotRecommend createFromParcel(Parcel in) {
            return new HotRecommend(in);
        }

        @Override
        public HotRecommend[] newArray(int size) {
            return new HotRecommend[size];
        }
    };

    public long getContentId() {
        return contentId;
    }

    public void setContentId(long contentId) {
        this.contentId = contentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "HotRecommend{" +
                "contentId=" + contentId +
                ", name='" + name + '\'' +
                ", type=" + type +
                '}';
    }
}
