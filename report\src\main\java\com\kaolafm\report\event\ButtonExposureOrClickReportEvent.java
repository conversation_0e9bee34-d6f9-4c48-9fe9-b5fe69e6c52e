package com.kaolafm.report.event;

import com.kaolafm.report.util.ReportConstants;

import static com.kaolafm.report.util.ReportConstants.CONTROL_TYPE_SCREEN;

/**
 * 按钮曝光/点击事件
 */
public class ButtonExposureOrClickReportEvent extends BaseReportEventBean {

    /**
     * 曝光或点击
     */
    public static final String MODE_CLICK = "click";    //点击
    public static final String MODE_EXPOSURE = "exposure";      //曝光

    /**
     * 按钮id
     */
    public static final String BUTTON_ID_USAGE_NOTICE_AGREE = "04 start";   //使用提示-开始使用/同意
    public static final String BUTTON_ID_USAGE_NOTICE_DISAGREE = "05 disagree";     //使用提示-不同意，退出
    public static final String BUTTON_ID_PRIVACY_POLICY_AGREE = "14";       //隐私协议-继续使用/同意
    public static final String BUTTON_ID_PRIVACY_POLICY_DISAGREE = "08 disagree";       //隐私协议-不同意，退出
    public static final String BUTTON_ID_HOME_PAGE_NAVIGATION = "15";       //首页导航
    public static final String BUTTON_ID_LOGOUT = "16";       //退出登录
    public static final String BUTTON_ID_MINE = "09";       //首页我的
    public static final String BUTTON_ID_CATEGORIES = "10";       //首页全部/分类
    public static final String BUTTON_ID_SEARCH = "11";       //首页搜索
    public static final String BUTTON_ID_NETWORK_RETRY = "12";       //网络错误点击重试
    public static final String BUTTON_ID_MESSAGE_BUBBLE_DETAIL_BUTTON_1 = "16";       //消息泡泡大图 按钮1
    public static final String BUTTON_ID_MESSAGE_BUBBLE_DETAIL_BUTTON_2 = "17";       //消息泡泡大图 按钮2
    public static final String BUTTON_ID_MESSAGE_BUBBLE_DETAIL_VOICE_BROADCAST = "18";       //消息泡泡大图 语音播报
    public static final String BUTTON_ID_MESSAGE_BUBBLE_SEE_DETAILS = "19";       //消息泡泡小卡 查看详情
    public static final String BUTTON_ID_MESSAGE_BUBBLE_CLOSE = "20";       //消息泡泡小卡 关闭
    public static final String BUTTON_ID_MESSAGE_BUBBLE_REPLAY = "21";       //消息泡泡小卡 重播
    public static final String BUTTON_ID_MESSAGE_BOX_HINT = "22";       //消息盒子 叹号/提示
    public static final String BUTTON_ID_SEARCH_CATEGORY_TAGS_LABEL = "23";       //搜索 分类标签
    public static final String BUTTON_ID_SEARCH_CATEGORY_LIST_ITEM = "24";       //搜索 分类列表中的Item
    public static final String BUTTON_ID_SEARCH_HOT_KEYWORDS_ITEM = "25";       //搜索 热词列表Item
    public static final String BUTTON_ID_SEARCH_ASSOCIATIONAL_WORD = "26";       //搜索 联想词
    public static final String BUTTON_ID_SEARCH_INPUT_KEYWORD_CLEAR = "27";       //搜索 输入框内容清空按钮
    public static final String BUTTON_ID_SEARCH_BUTTON = "28";       //搜索 搜索按钮
    public static final String BUTTON_ID_All_CLASSIFICATION_LEVEL_NAVIGATION = "29";       //全部/分类一级导航
    public static final String BUTTON_ID_All_CLASSIFICATION_SECONDARY_NAVIGATION = "30";       //全部/分类二级导航
    public static final String BUTTON_ID_BROADCAST_AREA_LABEL = "31";       //广播播放页 区域标签
    public static final String BUTTON_ID_BROADCAST_AREA_LIST_ITEM = "32";       //广播播放页 区域列表Item
    public static final String BUTTON_ID_BROADCAST_PROGRAM_DATE_TAB = "33";       //广播播放页 昨天/今天/明天日期tab
    public static final String BUTTON_ID_PLAYLIST_ITEM_SUBSCRIBE_BUTTON = "34";       //播单碎片订阅按钮
    public static final String BUTTON_ID_PLAYLIST_REFRESH = "35";       //播单下拉刷新
    public static final String BUTTON_ID_VIP_ORDER_PURCHASE_NOTES = "36";       //vip购买-购买提示
    public static final String BUTTON_ID_VIP_ORDER_MEAL_ITEM = "37";       //vip购买-套餐
    public static final String BUTTON_ID_AUDIO_ORDER_PURCHASE_NOTES = "38";       //单集、多集支付-购买须知
    public static final String BUTTON_ID_AUDIO_ORDER_TYPE = "39";       //单集、多集购买切换按钮
    public static final String BUTTON_ID_AUDIO_ORDER_PURCHASE_BUTTON = "40";       //多集购买-支付按钮
    public static final String BUTTON_ID_AUDIO_ORDER_SELECT_CURRENT_PAGE = "41";       //多集购买-全选本页按钮
    public static final String BUTTON_ID_AD_SKIP = "42";       //跳过广告
    public static final String BUTTON_ID_AD_FOLD = "43";       //广告-收起按钮
    public static final String BUTTON_ID_AD_AUDIO_SKIP = "101";       //音频广告-跳过按钮
    public static final String BUTTON_ID_AD_PIC_CLOSE = "102";       //图片广告-关闭按钮
    public static final String BUTTON_ID_PLAYLIST_SORT_TYPE_ASC = "44";       //播单-正序
    public static final String BUTTON_ID_PLAYLIST_SORT_TYPE_DESC = "45";       //播单-倒序
    public static final String BUTTON_ID_SKIN_DAY = "46";       //主题换肤-白天
    public static final String BUTTON_ID_SKIN_NIGHT = "47";       //主题换肤-黑夜
    public static final String BUTTON_ID_MESSAGE_VOICE_BROADCAST_SWITCH_ON = "103";       //消息语音播报开关-开
    public static final String BUTTON_ID_MESSAGE_VOICE_BROADCAST_SWITCH_OFF = "104";       //消息语音播报开关-关
    public static final String BUTTON_ID_TONE_QUALITY_NORMAL = "49";       //音质选择-普通
    public static final String BUTTON_ID_TONE_QUALITY_HIGH = "50";       //音质选择-高品质
    public static final String BUTTON_ID_ABOUT_US = "51";       //关于我们
    public static final String BUTTON_ID_ENABLING_CLAUSE = "52";       //用户授权条款
    public static final String BUTTON_ID_SERVICE_AGREEMENT = "53";       //服务协议
    public static final String BUTTON_ID_PRIVACY_POLICY = "54";       //隐私政策
    public static final String BUTTON_ID_LOGIN_BUTTON = "55";       //登录按钮
    public static final String BUTTON_ID_MY_SUBSCRIBE_BUTTON = "56";       //我的订阅
    public static final String BUTTON_ID_MY_LISTEN_HISTORY = "57";       //收听历史
    public static final String BUTTON_ID_MY_PURCHASED = "58";       //已购
    public static final String BUTTON_ID_PERSONAL_CENTER = "59";       //个人中心
    public static final String BUTTON_ID_SUBSCRIBE_TYPE = "60";       //我的订阅-节目类型
    public static final String BUTTON_ID_SEND_VERIFICATION_CODE = "61";       //发送/重发验证码
    public static final String BUTTON_ID_JOIN_VIP = "62";       //立即加入vip
    public static final String BUTTON_ID_VIP_RENEW = "63";       //vip续费
    public static final String BUTTON_ID_RELOGIN = "64";       //返回重新登陆
    public static final String BUTTON_ID_DELETE_BUTTON = "03  click clear";       //我的/收听历史-删除/一键清空按钮
    public static final String BUTTON_ID_SETTING_BUTTON = "65";       //设置按钮
    public static final String BUTTON_ID_PLAYER_MESSAGE_BUTTON = "66";       //播放器-消息按钮
    public static final String BUTTON_ID_LIVE_PAGE_SEND_MESSAGE_UNLOGIN = "67";       //直播间-未登录状态下的留言按钮
    public static final String BUTTON_ID_LIVE_PAGE_SEND_MESSAGE_LOGIN = "68";       //直播间-登录状态下的留言按钮
    public static final String BUTTON_ID_LIVE_PAGE_LISTEN = "69";       //直播间-留言试听
    public static final String BUTTON_ID_LIVE_PAGE_SEND_BUTTON = "70";       //直播间-留言发送
    public static final String BUTTON_ID_LIVE_PAGE_CANCEL_SEND_MESSAGE = "71";       //直播间-留言取消
    public static final String BUTTON_ID_PLAYER_SUBSCRIBE_BUTTON = "72";       //播放器-订阅按钮
    public static final String BUTTON_ID_MESSAGE_BUBBLE_DETAIL_COVER = "73";       //消息泡泡活动详情-封面图
    public static final String BUTTON_ID_AD_INTERACTION_CLOSE = "74";       //二次互动广告关闭按钮
    public static final String BUTTON_ID_ALBUM_ORDER_PAY_METHOD = "75";       //专辑购买-支付方式按钮（人民币/其他）
    public static final String BUTTON_ID_LIVE_ROOM_NOTICE = "76";       //直播间公告
    public static final String BUTTON_ID_LIVE_ROOM_START_RECORD_MESSAGE = "77";       //留言互动
    public static final String BUTTON_ID_LIVE_ROOM_GIFT_SPAN_CONTROLLER = "78";       //礼物面板折叠控制器
    public static final String BUTTON_ID_LIVE_ROOM_SHOPPING_CART_SPAN_CONTROLLER = "79"; //       //购物车面板折叠控制器
    public static final String BUTTON_ID_LIVE_ROOM_MERCHANDISE_PUSH_PURCHASE = "80";       //商品推送面板上的去抢购按钮
    public static final String BUTTON_ID_LIVE_ROOM_MERCHANDISE_LIST_ITEM_PURCHASE = "81";       //商品列表item的去抢购按钮
    public static final String BUTTON_ID_LIVE_ROOM_GIFT_ITEM_SEND = "82";       //礼物列表item的“赠送”按钮
    public static final String BUTTON_ID_LIVE_ROOM_MERCHANDISE_PURCHASE_NOTICE = "83";       //直播间商品购买弹窗-购买须知
    public static final String BUTTON_ID_BRAND_PAGE_NAVIGATION = "84";       //品牌主页-导航
    public static final String BUTTON_ID_BRAND_PAGE_BACK_BUTTON = "85";       //品牌主页-返回按钮
    public static final String BUTTON_ID_BRAND_PAGE_ARROW_BUTTON = "86";       //品牌主页-快捷跳转按钮（翻页按钮）
    public static final String BUTTON_ID_TOPIC_PAGE_PUBLISH = "87";       //话题详情-发布按钮
    public static final String BUTTON_ID_TOPIC_PAGE_VOICE_BROADCAST = "88";       //话题详情-语音播报
    public static final String BUTTON_ID_TOPIC_PAGE_SORT_WAY_BUTTON_POPULARITY = "89";       //话题详情-帖子排序切换按钮-热度
    public static final String BUTTON_ID_TOPIC_PAGE_SORT_WAY_BUTTON_TIME = "90";       //话题详情-帖子排序切换按钮-时间
    public static final String BUTTON_ID_TOPIC_PAGE_POSTS_LIKE = "91";       //话题详情-帖子点赞
    public static final String BUTTON_ID_TOPIC_PAGE_POSTS_UNLIKE = "92";       //话题详情-帖子取消点赞
    public static final String BUTTON_ID_POSTS_PUBLISH_PAGE_PUBLISH = "93";       //帖子输入弹窗-发布按钮
    public static final String BUTTON_ID_TOPIC_PAGE_BACK_BUTTON = "94";       //话题详情-返回按钮
    public static final String BUTTON_ID_ALBUM_ORDER_PURCHASE_NOTES = "95";       //专辑购买-购买须知
    public static final String BUTTON_ID_SEARCH_PAGE_HISTORY_ITEM = "96";       //搜索页-历史搜索词item
    public static final String BUTTON_ID_SEARCH_PAGE_HISTORY_CLEAR = "97";       //搜索页-清空历史
    public static final String BUTTON_ID_ACTIVITY_CARD_OPEN_DETAIL = "98";       //活动大卡组件-查看详情

    private String mode = MODE_CLICK;       // 曝光、点击
    private String buttonid;    //按钮的编码id，id唯一
    private String buttonname;  //按钮的中文名，可以重复
    private String pageId;  //所在页面的编码id
    private String controltype = CONTROL_TYPE_SCREEN;   //交互方式 1. 屏幕 2. 语音 3. 方控 4. 其他
    private String dialogid;       //如果按钮在弹窗上，报所在的弹窗id
    private String radioid;     //如果按钮有内容对象，则上报对象的合集id
    private String audioid;     //如果按钮有内容对象，则上报对象的单曲
    private String aidsid;      //如果对象是广告，则上报广告id,否则传null
    /**
     * 播放详情页
     * 专辑id
     * 专题id
     * 智能电台id
     * 听广播详情页上报传统广播id
     * 听电视详情页上报电台id
     * 直播间上报直播间id
     */
    private String pagecontentid;       //如果在页面有具体内容，则上报内容唯一id


    private String contentid;   //通用参数，当buttonid中备注时，启用这个参数，其他时候为空即可

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getButtonid() {
        return buttonid;
    }

    public void setButtonid(String buttonid) {
        this.buttonid = buttonid;
    }

    public String getButtonname() {
        return buttonname;
    }

    public void setButtonname(String buttonname) {
        this.buttonname = buttonname;
    }

    public String getPageId() {
        return pageId;
    }

    public void setPageId(String pageId) {
        this.pageId = pageId;
    }

    public String getControltype() {
        return controltype;
    }

    public void setControltype(String controltype) {
        this.controltype = controltype;
    }

    public String getDialogid() {
        return dialogid;
    }

    public void setDialogid(String dialogid) {
        this.dialogid = dialogid;
    }

    public String getRadioid() {
        return radioid;
    }

    public void setRadioid(String radioid) {
        this.radioid = radioid;
    }

    public String getAudioid() {
        return audioid;
    }

    public void setAudioid(String audioid) {
        this.audioid = audioid;
    }

    public String getPagecontentid() {
        return pagecontentid;
    }

    public void setPagecontentid(String pagecontentid) {
        this.pagecontentid = pagecontentid;
    }

    public String getAidsid() {
        return aidsid;
    }

    public void setAidsid(String aidsid) {
        this.aidsid = aidsid;
    }

    public String getContentid() {
        return contentid;
    }

    public void setContentid(String contentid) {
        this.contentid = contentid;
    }

    public ButtonExposureOrClickReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_BUTTON_EXPOSURE_CLICK);
    }

    public ButtonExposureOrClickReportEvent(String mode, String buttonid, String buttonname, String pageId, String controltype) {
        this(mode, buttonid, buttonname, pageId, controltype, null);
    }

    public ButtonExposureOrClickReportEvent(String mode, String buttonid, String buttonname, String pageId, String controltype, String dialogid) {
        this(mode, buttonid, buttonname, pageId, controltype, dialogid, null, null, null);
    }

    public ButtonExposureOrClickReportEvent(String mode, String buttonid, String buttonname, String pageId, String controltype, String dialogid, String radioid, String audioid, String pagecontentid) {
        this(mode, buttonid, buttonname, pageId, controltype, dialogid, radioid, audioid, pagecontentid, null);
    }

    public ButtonExposureOrClickReportEvent(String mode, String buttonid, String buttonname, String pageId, String controltype, String dialogid, String radioid, String audioid, String pagecontentid, String aidsid) {
        setEventcode(ReportConstants.EVENT_ID_BUTTON_EXPOSURE_CLICK);
        this.mode = mode;
        this.buttonid = buttonid;
        this.buttonname = buttonname;
        this.pageId = pageId;
        this.controltype = controltype;
        this.dialogid = dialogid;
        this.radioid = radioid;
        this.audioid = audioid;
        this.pagecontentid = pagecontentid;
        this.aidsid = aidsid;
    }

    public ButtonExposureOrClickReportEvent(String mode, String buttonid, String buttonname, String pageId, String controltype, String dialogid, String radioid, String audioid, String pagecontentid, String aidsid, String contentid) {
        setEventcode(ReportConstants.EVENT_ID_BUTTON_EXPOSURE_CLICK);
        this.mode = mode;
        this.buttonid = buttonid;
        this.buttonname = buttonname;
        this.pageId = pageId;
        this.controltype = controltype;
        this.dialogid = dialogid;
        this.radioid = radioid;
        this.audioid = audioid;
        this.pagecontentid = pagecontentid;
        this.aidsid = aidsid;
        this.contentid = contentid;
    }
}

