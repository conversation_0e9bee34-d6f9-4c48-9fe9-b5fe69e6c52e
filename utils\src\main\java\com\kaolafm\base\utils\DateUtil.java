package com.kaolafm.base.utils;

import android.os.SystemClock;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Locale;
import java.util.TimeZone;

/**
 * 处理时间相关的工具类
 *
 * <AUTHOR>
 * @date 2018/5/6
 */

public class DateUtil {

    private static final String ONE_SECOND_AGO = "秒前";

    private static final String ONE_MINUTE_AGO = "分钟前";

    private static final String ONE_HOUR_AGO = "小时前";

    private static final String ONE_DAY_AGO = "天前";

    private static final String ONE_MONTH_AGO = "月前";

    private static final String ONE_YEAR_AGO = "年前";

    private static long mServerTime;

    private static long mResponseTime;

    /**
     * 时间字符串转时间戳， 格式化格式为yyyy-MM-dd HH:mm:ss
     */
    public static long string2Millis(String time) {
        return string2Millis("yyyy-MM-dd HH:mm:ss", time);
    }

    /**
     * 按照给定的格式将时间字符串转时间戳
     *
     * @param patten 格式化格式
     * @param time   毫秒值
     */
    public static long string2Millis(String patten, String time) {
        return string2Millis(time, patten, Locale.CHINA);
    }

    public static long string2Millis(String time, String patten, Locale locale) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(patten, locale);
        try {
            return dateFormat.parse(time).getTime();
        } catch (ParseException | NullPointerException e) {
            e.printStackTrace();
        }
        return 0;
    }

    public static String formatMillis(String patten, long time) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(patten, Locale.CHINA);
        return dateFormat.format(time);
    }

    public static String formatMillis(long time) {
        return formatMillis("yyyy-MM-dd HH:mm:ss", time);
    }

    /**
     * 将毫秒值转为时间间隔字符串，如，1天，6个月。
     */
    public static String millis2Day(long duration) {
        long second = duration / 1000;
        long years = second / (60 * 60 * 24 * 365);
        long months = (second % years) / (60 * 60 * 24 * 30);
        long days = (second % months) / (60 * 60 * 24);
        StringBuilder sb = new StringBuilder();
        if (years > 0) {
            sb.append(years).append("年");
        }
        if (months > 0) {
            sb.append(months).append("个月");
        }
        if (days > 0) {
            sb.append(days).append("天");
        }
        return sb.toString();
    }

    /**
     * 给的时间距现在时间的间隔，以几天前/几月前等形式显示
     */
    public static String getDisTimeStr(long time) {
        long second = (System.currentTimeMillis() - time) / 1000;
        if (second > 0) {
            //小于1分钟
            if (second < 60) {
                return second + "秒前";
                //小于一小时
            } else if (second < 60 * 60) {
                return second / 60 + "分前";
                //小于1天
            } else if (second < (60 * 60 * 24)) {
                return second / (60 * 60) + "小时前";
                //小于一个月
            } else if (second < (60 * 60 * 24 * 30)) {
                long days = second / (60 * 60 * 24);
                if (days == 7) {
                    return "一周前";
                }
                return days + "天前";
            } else {
                return formatMillis(time);
            }
        }
        return null;
    }

    /**
     * 保存服务器时间
     *
     * @param date
     */
    public static void setServerTime(String date) {
        TimeZone.setDefault(TimeZone.getTimeZone("GMT+8"));
        mServerTime = string2Millis(date, "EEE, d MMM yyyy HH:mm:ss z", Locale.ENGLISH);
        mResponseTime = SystemClock.elapsedRealtime();
    }

    /**
     * 获取服务器时间
     *
     * @return 毫秒
     */
    public static long getServerTime() {
        long time = mServerTime + (SystemClock.elapsedRealtime() - mResponseTime);
        //判断是不是13位的数字，毫秒时间戳都是13位。
        if (String.valueOf(time).length() != 13) {
            return System.currentTimeMillis();
        }
        return time;
    }

}
