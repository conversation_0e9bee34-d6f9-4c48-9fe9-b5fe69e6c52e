<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <RadioGroup
        android:id="@+id/rg_live_account_type"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        >
        <RadioButton
            android:id="@+id/rb_live_third"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            android:text="UID账号"
            />
        <RadioButton
            android:id="@+id/rb_live_kradio"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="使用手机号"
            />

    </RadioGroup>
    <EditText
        android:id="@+id/et_live_uid"
        app:layout_constraintTop_toBottomOf="@id/rg_live_account_type"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="请输入用户唯一标识"
        />
    <EditText
        android:id="@+id/et_live_nickname"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/et_live_uid"
        android:hint="请输入昵称"
        />
    <EditText
        android:id="@+id/et_live_avatar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/et_live_nickname"
        android:hint="请输入用户头像url"
        />

</androidx.constraintlayout.widget.ConstraintLayout>