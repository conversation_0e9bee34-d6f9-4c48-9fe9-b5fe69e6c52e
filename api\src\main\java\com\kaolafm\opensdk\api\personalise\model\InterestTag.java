package com.kaolafm.opensdk.api.personalise.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * <AUTHOR>
 * @date 2019/4/29
 */
public class InterestTag implements Parcelable {
    /**
     * 标签名
     */
    private String name;

    /**
     * 是否是当前用户选中的标签，true表示是。
     */
    private boolean selected;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    protected InterestTag(Parcel in) {
        name = in.readString();
        selected = in.readInt() == 1;
    }

    public static final Creator<InterestTag> CREATOR = new Creator<InterestTag>() {
        @Override
        public InterestTag createFromParcel(Parcel in) {
            return new InterestTag(in);
        }

        @Override
        public InterestTag[] newArray(int size) {
            return new InterestTag[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(name);
        dest.writeInt(selected ? 1 : 0);
    }
}
