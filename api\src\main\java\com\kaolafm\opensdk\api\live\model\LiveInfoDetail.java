package com.kaolafm.opensdk.api.live.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;
import com.kaolafm.opensdk.api.media.model.AudioFileInfo;

import java.util.List;

/**
 * 直播节目详信息
 * <AUTHOR>
 */
public class LiveInfoDetail implements Parcelable {


    /** 直播已经结束*/
    public static final int STATUS_FINISHED = 0;

    /** 正在直播*/
    public static final int STATUS_LIVING = 1;

    /** 即将开始*/
    public static final int STATUS_COMING = 2;

    /** 今天开播, 今天的某个时间*/
    public static final int STATUS_START_TODAY = 3;

    /** 明天开播*/
    public static final int STATUS_START_TOMORROW = 4;

    /** 后天开播*/
    public static final int STATUS_START_AFTER_TOMORROW = 5;

    /** 未开播*/
    public static final int STATUS_NOT_START = 6;

    /** 已延期*/
    public static final int STATUS_DELAYED = 7;

    /** 直播转录播*/
    public static final int STATUS_LIVE_TO_RECORDING = 8;

    /**
     {
     "liveName": "在线电台测试直播",
     "liveId": 1413989903,
     "livePic": "https://iovimg.radio.cn/mz/images/202207/43f48b8f-34b4-4bf7-b60c-691a8dc1226f/default.jpg",
     "liveDesc": "直播简介测试",
     "programId": 1517397714,
     "programName": "在线电台测试直播",
     "programPic": "https://iovimg.radio.cn/mz/images/202207/aea62f22-7638-4bf6-8e6c-bea079fae1aa/default.png",
     "programDesc": "直播简介测试",
     "begintime": "2022-07-04 18:36:31",
     "endtime": "2023-08-04 00:00:00",
     "status": 1,
     "showStartTime": "直播中",
     "period": 12,
     "comperes": "yl",
     "guests": "",
     "albumId": 0,
     "liveUrl": "https://ytlive.radio.cn/45/radios/21720/index_21720.m3u8?type=1&key=1c2faf50dd454e748c587ca59cc0ec95&time=62ff0683",
     "shareUrl": "http://m.kaolafm.com/share/liveplay/index.html",
     "onLineNum": 0,
     "isCanSubscribe": 0,
     "isAlreadySubscribe": 0,
     "subscribeNum": 0,
     "bgColor": "",
     "programLikedNum": 0,
     "programSharedNum": 0,
     "programFollowedNum": 0,
     "uid": 5480092,
     "avatar": "https://iovimg.radio.cn/mz/images/201605/00ecbe21-e547-4e96-aafa-239a46027a43/default.png",
     "isVanchor": 0,
     "gender": 1,
     "backLiveUrl": null,
     "timeLength": "",
     "startTime": 1656930991000,
     "finshTime": 1691078400000,
     "serveTime": 1660880515837,
     "duration": 0,
     "canPlayBack": 0,
     "pushHost": "pub.c.l.kaolafm.net",
     "accessKey": "ugc",
     "lockType": 0,
     "isAlreadyFollowed": 0,
     "roomId": 928859243,
     "userType": null,
     "rtmpUrl": "rtmp://play.a.l.kaolafm.net/ugc/1413989903_1517397714",
     "mainLiveId": null,
     "isLite": 0,
     "playInfoList": [{
     "fileType": "m3u8",
     "playUrl": "https://ytlive.radio.cn/45/radios/21720/index_21720.m3u8?type=1&key=1c2faf50dd454e748c587ca59cc0ec95&time=62ff0683",
     "bitrateNew": 48
     }, {
     "fileType": "m3u8",
     "playUrl": "https://ytlive.radio.cn/45/radios/21720/index_21720.m3u8?type=1&key=1c2faf50dd454e748c587ca59cc0ec95&time=62ff0683",
     "bitrateNew": 196
     }
     */

    /** 直播间名称*/
    private String liveName;

    /** 直播间id*/
    private long liveId;

    /** 直播间描述*/
    private String liveDesc;
    /** 嘉宾*/
    private String guests;
    /** 当前直播节目id*/
    private long programId;

    /** 当前直播节目名称*/
    private String programName;

    /** 当前直播节目描述*/
    private String programDesc;

    /** 开始时间。格式化的时间，2018-12-12 00:00:00*/
    @SerializedName("begintime")
    private String beginTime;

    /** 结束时间。格式化的时间，2020-01-30 00:00:00*/
    @SerializedName("endTime")
    private String endTime;

    /** 节目状态。@see {@link #STATUS_LIVING}等*/
    private int status;

    /** 节目期号*/
    private int period;

    /** 主播姓名*/
    private String comperes;

    /** 直播地址*/
    private String liveUrl;

    /** 节目图片url*/
    private String programPic;

    /** 直播间图片url*/
    private String livePic;

    /** 直播时长*/
    private String timeLength;

    /** 开始时间，时间戳，单位毫秒*/
    private long startTime;

    /** 结束时间，时间戳，单位毫秒*/
    @SerializedName("finshTime")
    private long finishTime;

    /** */
    @SerializedName("serveTime")
    private long serverTime;

    /** 直播时长*/
    private int duration;

    private String roomId;

    /** 时间描述，如直播中*/
    private String showStartTime;
    /**
     * 主播id
     */
    @SerializedName("uid")
    private long comperesId;

    private String announcement;//直播公告内容,空白则不展示直播公告按钮

    private Integer showOnlineNum;//在线人数是否开启字段

    private Long onLineNum;//在线人数

    private Long totalOnlineNum;//累计在线人数

    private String liveBgPic;//背景图

    @SerializedName("playInfoList")
    private List<AudioFileInfo> playInfoList;

    @SerializedName("abilityList")
    private List<LiveAbility> liveAbilityList;

    public LiveInfoDetail() {
    }

    protected LiveInfoDetail(Parcel in) {
        liveName = in.readString();
        liveId = in.readLong();
        liveDesc = in.readString();
        guests = in.readString();
        programId = in.readLong();
        programName = in.readString();
        programDesc = in.readString();
        beginTime = in.readString();
        endTime = in.readString();
        status = in.readInt();
        period = in.readInt();
        comperes = in.readString();
        liveUrl = in.readString();
        programPic = in.readString();
        livePic = in.readString();
        timeLength = in.readString();
        startTime = in.readLong();
        finishTime = in.readLong();
        serverTime = in.readLong();
        duration = in.readInt();
        roomId = in.readString();
        showStartTime = in.readString();
        comperesId = in.readLong();
        announcement = in.readString();
        if (in.readByte() == 0) {
            showOnlineNum = null;
        } else {
            showOnlineNum = in.readInt();
        }
        if (in.readByte() == 0) {
            onLineNum = null;
        } else {
            onLineNum = in.readLong();
        }
        if (in.readByte() == 0) {
            totalOnlineNum = null;
        } else {
            totalOnlineNum = in.readLong();
        }
        liveBgPic = in.readString();
        playInfoList = in.createTypedArrayList(AudioFileInfo.CREATOR);
        liveAbilityList = in.createTypedArrayList(LiveAbility.CREATOR);
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(liveName);
        dest.writeLong(liveId);
        dest.writeString(liveDesc);
        dest.writeString(guests);
        dest.writeLong(programId);
        dest.writeString(programName);
        dest.writeString(programDesc);
        dest.writeString(beginTime);
        dest.writeString(endTime);
        dest.writeInt(status);
        dest.writeInt(period);
        dest.writeString(comperes);
        dest.writeString(liveUrl);
        dest.writeString(programPic);
        dest.writeString(livePic);
        dest.writeString(timeLength);
        dest.writeLong(startTime);
        dest.writeLong(finishTime);
        dest.writeLong(serverTime);
        dest.writeInt(duration);
        dest.writeString(roomId);
        dest.writeString(showStartTime);
        dest.writeLong(comperesId);
        dest.writeString(announcement);
        if (showOnlineNum == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(showOnlineNum);
        }
        if (onLineNum == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeLong(onLineNum);
        }
        if (totalOnlineNum == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeLong(totalOnlineNum);
        }
        dest.writeString(liveBgPic);
        dest.writeTypedList(playInfoList);
        dest.writeTypedList(liveAbilityList);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<LiveInfoDetail> CREATOR = new Creator<LiveInfoDetail>() {
        @Override
        public LiveInfoDetail createFromParcel(Parcel in) {
            return new LiveInfoDetail(in);
        }

        @Override
        public LiveInfoDetail[] newArray(int size) {
            return new LiveInfoDetail[size];
        }
    };

    public String getLiveName() {
        return liveName;
    }

    public void setLiveName(String liveName) {
        this.liveName = liveName;
    }

    public long getLiveId() {
        return liveId;
    }

    public void setLiveId(long liveId) {
        this.liveId = liveId;
    }

    public String getLiveDesc() {
        return liveDesc;
    }

    public void setLiveDesc(String liveDesc) {
        this.liveDesc = liveDesc;
    }

    public String getGuests() {
        return guests;
    }

    public void setGuests(String guests) {
        this.guests = guests;
    }

    public long getProgramId() {
        return programId;
    }

    public void setProgramId(long programId) {
        this.programId = programId;
    }

    public String getProgramName() {
        return programName;
    }

    public void setProgramName(String programName) {
        this.programName = programName;
    }

    public String getProgramDesc() {
        return programDesc;
    }

    public void setProgramDesc(String programDesc) {
        this.programDesc = programDesc;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getPeriod() {
        return period;
    }

    public void setPeriod(int period) {
        this.period = period;
    }

    public String getComperes() {
        return comperes;
    }

    public void setComperes(String comperes) {
        this.comperes = comperes;
    }

    public String getLiveUrl() {
        return liveUrl;
    }

    public void setLiveUrl(String liveUrl) {
        this.liveUrl = liveUrl;
    }

    public String getProgramPic() {
        return programPic;
    }

    public void setProgramPic(String programPic) {
        this.programPic = programPic;
    }

    public String getLivePic() {
        return livePic;
    }

    public void setLivePic(String livePic) {
        this.livePic = livePic;
    }

    public String getTimeLength() {
        return timeLength;
    }

    public void setTimeLength(String timeLength) {
        this.timeLength = timeLength;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(long finishTime) {
        this.finishTime = finishTime;
    }

    public long getServerTime() {
        return serverTime;
    }

    public void setServerTime(long serverTime) {
        this.serverTime = serverTime;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getShowStartTime() {
        return showStartTime;
    }

    public void setShowStartTime(String showStartTime) {
        this.showStartTime = showStartTime;
    }

    public long getComperesId() {
        return comperesId;
    }

    public void setComperesId(long comperesId) {
        this.comperesId = comperesId;
    }

    public String getAnnouncement() {
        return announcement;
    }

    public void setAnnouncement(String announcement) {
        this.announcement = announcement;
    }

    public Integer getShowOnlineNum() {
        return showOnlineNum;
    }

    public void setShowOnlineNum(Integer showOnlineNum) {
        this.showOnlineNum = showOnlineNum;
    }

    public Long getOnLineNum() {
        return onLineNum;
    }

    public void setOnLineNum(Long onLineNum) {
        this.onLineNum = onLineNum;
    }

    public Long getTotalOnlineNum() {
        return totalOnlineNum;
    }

    public void setTotalOnlineNum(Long totalOnlineNum) {
        this.totalOnlineNum = totalOnlineNum;
    }

    public String getLiveBgPic() {
        return liveBgPic;
    }

    public void setLiveBgPic(String liveBgPic) {
        this.liveBgPic = liveBgPic;
    }

    public List<AudioFileInfo> getPlayInfoList() {
        return playInfoList;
    }

    public void setPlayInfoList(List<AudioFileInfo> playInfoList) {
        this.playInfoList = playInfoList;
    }

    public List<LiveAbility> getLiveAbilityList() {
        return liveAbilityList;
    }

    public void setLiveAbilityList(List<LiveAbility> liveAbilityList) {
        this.liveAbilityList = liveAbilityList;
    }

    @Override
    public String toString() {
        return "LiveInfoDetail{" +
                "liveName='" + liveName + '\'' +
                ", liveId=" + liveId +
                ", liveDesc='" + liveDesc + '\'' +
                ", guests='" + guests + '\'' +
                ", programId=" + programId +
                ", programName='" + programName + '\'' +
                ", programDesc='" + programDesc + '\'' +
                ", beginTime='" + beginTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", status=" + status +
                ", period=" + period +
                ", comperes='" + comperes + '\'' +
                ", liveUrl='" + liveUrl + '\'' +
                ", programPic='" + programPic + '\'' +
                ", livePic='" + livePic + '\'' +
                ", timeLength='" + timeLength + '\'' +
                ", startTime=" + startTime +
                ", finishTime=" + finishTime +
                ", serverTime=" + serverTime +
                ", duration=" + duration +
                ", roomId='" + roomId + '\'' +
                ", showStartTime='" + showStartTime + '\'' +
                ", comperesId=" + comperesId +
                ", announcement='" + announcement + '\'' +
                ", showOnlineNum=" + showOnlineNum +
                ", onLineNum=" + onLineNum +
                ", totalOnlineNum=" + totalOnlineNum +
                ", liveBgPic='" + liveBgPic + '\'' +
                ", playInfoList=" + playInfoList +
                ", liveAbilityList=" + liveAbilityList +
                '}';
    }
}