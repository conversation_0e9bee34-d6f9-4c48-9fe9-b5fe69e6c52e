package com.kaolafm.opensdk.api.live.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * <AUTHOR>
 * @date 2023-02-16
 */
public class GiftGivingResult implements Parcelable {

    private Integer status; //返回状态(0-服务器异常,1-成功,2-错误的打赏次数,3-礼物不存在,4-直播不存在,5-用户不存在,6-主播不在直播中,7-账户余额不足,8-打赏失败)

    private String msg; //返回状态描述

    private Long coins; //用户最新云币余额

    public GiftGivingResult() {
    }

    public GiftGivingResult(Integer status, String msg, Long coins) {
        this.status = status;
        this.msg = msg;
        this.coins = coins;
    }

    protected GiftGivingResult(Parcel in) {
        if (in.readByte() == 0) {
            status = null;
        } else {
            status = in.readInt();
        }
        msg = in.readString();
        if (in.readByte() == 0) {
            coins = null;
        } else {
            coins = in.readLong();
        }
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        if (status == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(status);
        }
        dest.writeString(msg);
        if (coins == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeLong(coins);
        }
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<GiftGivingResult> CREATOR = new Creator<GiftGivingResult>() {
        @Override
        public GiftGivingResult createFromParcel(Parcel in) {
            return new GiftGivingResult(in);
        }

        @Override
        public GiftGivingResult[] newArray(int size) {
            return new GiftGivingResult[size];
        }
    };

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Long getCoins() {
        return coins;
    }

    public void setCoins(Long coins) {
        this.coins = coins;
    }
}
