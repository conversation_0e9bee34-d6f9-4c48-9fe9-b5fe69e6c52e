package com.kaolafm.opensdk.api.music.qq.model;

import java.util.Arrays;

/**
 * 播放歌曲的信息，相当于一个歌单/榜单/电台/专辑的信息
 *
 * <AUTHOR>
 * @date 2018/5/6
 */

public class MusicInfo {


    /**
     * 用于操作的id，比如获取歌曲列表，收藏等
     */
    private long id;

    /**
     * 名称，如歌单名/电台名
     */
    private String name;

    /**
     * 封面
     */
    private String cover;

    /**
     * 收听数
     */
    private long listenNum;

    /**
     * 介绍
     */
    private String desc;

    /**
     * 类型
     */
    private int type;

    /**
     * 还没有加载的歌曲mid集合
     */
    private String[] songMids;

    /**
     * 当前正在播放的歌曲的songMid
     */
    private long latestSongMid;

    /**
     * 歌词是否打开true为是，false为否
     */
    private boolean isLyricOpen;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public long getListenNum() {
        return listenNum;
    }

    public void setListenNum(long listenNum) {
        this.listenNum = listenNum;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String[] getSongMids() {
        return songMids;
    }

    public void setSongMids(String[] songMids) {
        this.songMids = songMids;
    }

    public long getLatestSongMid() {
        return latestSongMid;
    }

    public void setLatestSongMid(long latestSongMid) {
        this.latestSongMid = latestSongMid;
    }

    public boolean isLyricOpen() {
        return isLyricOpen;
    }

    public void setLyricOpen(boolean lyricOpen) {
        isLyricOpen = lyricOpen;
    }

    public void clear() {
        id = 0;
        name = "";
        cover = "";
        listenNum = 0;
        desc = "";
        type = 0;
        songMids = null;
        latestSongMid = 0;
        isLyricOpen = false;
    }

    @Override
    public String toString() {
        return "MusicInfo{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", cover='" + cover + '\'' +
                ", listenNum=" + listenNum +
                ", desc='" + desc + '\'' +
                ", type=" + type +
                ", songMids=" + Arrays.toString(songMids) +
                ", latestSongMid=" + latestSongMid +
                ", isLyricOpen=" + isLyricOpen +
                '}';
    }
}
