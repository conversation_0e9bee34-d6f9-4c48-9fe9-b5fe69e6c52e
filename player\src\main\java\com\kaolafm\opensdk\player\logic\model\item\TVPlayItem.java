package com.kaolafm.opensdk.player.logic.model.item;

import android.os.Parcel;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.media.model.AudioFileInfo;
import com.kaolafm.opensdk.player.logic.model.item.model.InfoData;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.TimeInfoData;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import java.util.ArrayList;
import java.util.List;

/**
 * 听电视-播放对象
 */
public class TVPlayItem extends PlayItem {

    /**
     * 信息相关数据
     */
    private InfoData mInfoData;

    /**
     * 播放时间相关数据
     */
    private TimeInfoData mTimeInfoData;

    /**
     * 广播频段
     */
    private String frequencyChannel;

    /**
     * 在线广播定制状态
     */
    private int status;

    /**
     * 直播分音质播放地址
     */
    private List<AudioFileInfo> playInfoList;

    /**
     * 回放分音质播放地址
     */
    private List<AudioFileInfo> backPlayInfoList;

    public TVPlayItem() {
        mInfoData = new InfoData();
        mTimeInfoData = new TimeInfoData();
        playInfoList = new ArrayList<>();
        backPlayInfoList = new ArrayList<>();
    }

    @Override
    public String getRadioId() {
        return String.valueOf(mInfoData.getAlbumId());
    }


    @Override
    public String getAlbumId() {
        return String.valueOf(mInfoData.getAlbumId());
    }

    @Override
    public String getTitle() {
        String title = mInfoData.getTitle();
        if (StringUtil.isEmpty(title)) {
            title = mInfoData.getAlbumName();
        }
        return title;
    }

    @Override
    public String getPicUrl() {
        String picUrl = mInfoData.getAudioPic();
        if (StringUtil.isEmpty(picUrl)) {
            picUrl = mInfoData.getAlbumPic();
        }
        return picUrl;
    }

    @Override
    public String getHost() {
        return mInfoData.getHosts();
    }

    @Override
    public String getUpdateTime() {
        return mInfoData.getUpdateTime();
    }

    @Override
    public String getAlbumTitle() {
        return mInfoData.getAlbumName();
    }

    @Override
    public String getBeginTime() {
        return mTimeInfoData.getBeginTime();
    }

    @Override
    public String getEndTime() {
        return mTimeInfoData.getEndTime();
    }

    @Override
    public long getStartTime() {
        return mTimeInfoData.getStartTime();
    }

    @Override
    public int getType() {
        return PlayerConstants.RESOURCES_TYPE_TV;
    }

    public String getFrequencyChannel() {
        return frequencyChannel;
    }

    public void setFrequencyChannel(String frequencyChannel) {
        this.frequencyChannel = frequencyChannel;
    }

    @Override
    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public List<AudioFileInfo> getPlayInfoList() {
        return playInfoList;
    }

    public void setPlayInfoList(List<AudioFileInfo> playInfoList) {
        this.playInfoList = playInfoList;
    }

    public List<AudioFileInfo> getBackPlayInfoList() {
        return backPlayInfoList;
    }

    public void setBackPlayInfoList(List<AudioFileInfo> backPlayInfoList) {
        this.backPlayInfoList = backPlayInfoList;
    }

    public boolean isLivingUrl() {
        return status == PlayerConstants.BROADCAST_STATUS_LIVING;
    }

    @Override
    public boolean isLiving() {
        return isLivingUrl();
    }

    public InfoData getInfoData() {
        return mInfoData;
    }

    public void setInfoData(InfoData infoData) {
        this.mInfoData = infoData;
    }

    public TimeInfoData getTimeInfoData() {
        return mTimeInfoData;
    }

    public void setTimeInfoData(TimeInfoData timeInfoData) {
        this.mTimeInfoData = timeInfoData;
    }

    private TVPlayItem(Parcel parcel) {

    }

    public static final Creator<TVPlayItem> CREATOR = new Creator<TVPlayItem>() {

        @Override
        public TVPlayItem createFromParcel(Parcel source) {
            return new TVPlayItem(source);
        }

        @Override
        public TVPlayItem[] newArray(int size) {
            return new TVPlayItem[size];
        }
    };

    @Override
    public long getFinishTime() {
        return mTimeInfoData.getFinishTime();
    }
}
