package com.kaolafm.opensdk.api.music.qq.model;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2018/4/19
 */

public class SongMenu {

    public static class Creator {

        /**
         * avatar_url :
         * is_vip : 0
         * name : 唯有、努力
         * uin : 2166375611
         */

        @SerializedName("avatar_url")
        private String avatarUrl;

        @SerializedName("is_vip")
        private int isVip;

        @SerializedName("name")
        private String name;

        @SerializedName("uin")
        private long uin;

        public String getAvatarUrl() {
            return avatarUrl;
        }

        public int getIsVip() {
            return isVip;
        }

        public String getName() {
            return name;
        }

        public long getUin() {
            return uin;
        }

        public void setAvatarUrl(String avatarUrl) {
            this.avatarUrl = avatarUrl;
        }

        public void setIsVip(int isVip) {
            this.isVip = isVip;
        }

        public void setName(String name) {
            this.name = name;
        }

        public void setUin(long uin) {
            this.uin = uin;
        }
    }

    /**
     * commit_time : 1492655476
     * create_time : 1492501956
     * creator : {"avatar_url":"","is_vip":0,"name":"唯有、努力","uin":2166375611}
     * diss_id : 1159137608
     * diss_name : 你是让我充满着期待的盛夏
     * introduction :
     * listen_num : 17965
     * pic_url : http://p.qpic.cn/music_cover/aNTAHHoAL4SKTAppfXDqibTEmk99zia8hvyd5lUDBJHvNXCbv3FiaUQRg/600?n=1
     */

    @SerializedName("commit_time")
    private long commitTime;

    @SerializedName("create_time")
    private long createTime;

    @SerializedName("creator")
    private Creator creator;

    @SerializedName("diss_id")
    private long dissId;

    @SerializedName("diss_name")
    private String dissName;

    @SerializedName("introduction")
    private String introduction;

    @SerializedName("listen_num")
    private long listenNum;

    @SerializedName("song_num")
    private long songNum;

    @SerializedName("update_time")
    private long updateTime;

    @SerializedName(value = "pic_url", alternate = {"diss_pic"})
    private String picUrl;

    @SerializedName("diss_type")
    private int dissType;

    public long getCommitTime() {
        return commitTime;
    }

    public long getCreateTime() {
        return createTime;
    }

    public Creator getCreator() {
        return creator;
    }

    public long getDissId() {
        return dissId;
    }

    public String getDissName() {
        return dissName;
    }

    public String getIntroduction() {
        return introduction;
    }

    public long getListenNum() {
        return listenNum;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setCommitTime(long commitTime) {
        this.commitTime = commitTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public void setCreator(Creator creator) {
        this.creator = creator;
    }

    public void setDissId(long dissId) {
        this.dissId = dissId;
    }

    public void setDissName(String dissName) {
        this.dissName = dissName;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }

    public void setListenNum(long listenNum) {
        this.listenNum = listenNum;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public int getDissType() {
        return dissType;
    }

    public void setDissType(int dissType) {
        this.dissType = dissType;
    }

    public long getSongNum() {
        return songNum;
    }

    public void setSongNum(long songNum) {
        this.songNum = songNum;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }
}
