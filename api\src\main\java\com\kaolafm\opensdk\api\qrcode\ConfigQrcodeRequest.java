package com.kaolafm.opensdk.api.qrcode;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.qrcode.model.ConfigQrcode;
import com.kaolafm.opensdk.http.core.HttpCallback;

/**
 * 二维码相关请求
 */
public class ConfigQrcodeRequest extends BaseRequest {

    private final ConfigQrcodeService mService;

    public ConfigQrcodeRequest() {
        this.mService = obtainRetrofitService(ConfigQrcodeService.class);
    }


    public void getQrcode(Integer type, HttpCallback<ConfigQrcode> callback){
        doHttpDeal(mService.getQrcode(type), BaseResult::getResult, callback);
    }
}
