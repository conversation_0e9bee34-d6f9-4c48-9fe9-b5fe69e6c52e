package com.kaolafm.opensdk.api.goods;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.live.model.GoodsDetails;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Query;


/**
 *
 */
public interface GoodsService {


//    /**
//     * 获取商品列表
//     * @param liveId    直播id
//     * @return
//     */
//    @Headers(ApiHostConstants.MALL_DOMAIN_HEADER)
//    @GET(KaolaApiConstant.REQUEST_GOODS_LIST)
//    Single<BaseResult<GoodsResult>> getGoods(@Query("liveId") Integer liveId);


    /**
     * 获取商品详情
     * @param goodsId    商品id
     * @return
     */
    @Headers(ApiHostConstants.MALL_DOMAIN_HEADER)
    @GET(KaolaApiConstant.REQUEST_GOODS_DETAIL)
    Single<BaseResult<GoodsDetails>> getGoodsDetails(@Query("goodsId") Long goodsId);


}
