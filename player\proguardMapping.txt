com.kaolafm.opensdk.player.core.BuildConfig -> com.kaolafm.opensdk.player.core.BuildConfig:
    boolean DEBUG -> DEBUG
    java.lang.String APPLICATION_ID -> APPLICATION_ID
    java.lang.String BUILD_TYPE -> BUILD_TYPE
    java.lang.String FLAVOR -> FLAVOR
    int VERSION_CODE -> VERSION_CODE
    java.lang.String VERSION_NAME -> VERSION_NAME
    6:6:void <init>() -> <init>
com.kaolafm.opensdk.player.core.PlayerService -> com.kaolafm.opensdk.player.core.PlayerService:
    com.kaolafm.opensdk.player.core.utils.ContextMediaPlayer mContextMediaPlayer -> mContextMediaPlayer
    java.lang.String mPlayingUri -> mPlayingUri
    com.kaolafm.opensdk.player.core.model.AAudioFocus mAudioFocusManager -> mAudioFocusManager
    com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter mIAudioFocusListener -> mIAudioFocusListener
    boolean isLoseAudioFocus -> isLoseAudioFocus
    int mPrePlayStatus -> mPrePlayStatus
    int mPreFocusChange -> mPreFocusChange
    com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter iAudioFocusListener -> iAudioFocusListener
    31:319:void <init>() -> <init>
    55:55:android.os.IBinder onBind(android.content.Intent) -> onBind
    60:62:void onCreate() -> onCreate
    65:66:void initContextMediaPlayer() -> initContextMediaPlayer
    168:171:void initPlayerInner() -> initPlayerInner
    174:188:void startInner(java.lang.String,long,long,boolean) -> startInner
    191:192:void setPositionInner(long) -> setPositionInner
    195:196:void pauseInner() -> pauseInner
    199:206:void playInner() -> playInner
    209:210:void stopInner() -> stopInner
    213:214:void resetInner() -> resetInner
    217:218:void releaseInner() -> releaseInner
    221:222:void seekInner(long) -> seekInner
    225:226:void setPlayerStateListenerInner(com.kaolafm.opensdk.player.core.listener.IPlayerStateCoreListener) -> setPlayerStateListenerInner
    229:230:void setPlayerBufferProgressListenerInner(com.kaolafm.opensdk.player.core.listener.IPlayerBufferProgressListener) -> setPlayerBufferProgressListenerInner
    233:234:void setInitCompleteListenerInner(com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener) -> setInitCompleteListenerInner
    237:238:void setLoudnessNormalizationInner(int) -> setLoudnessNormalizationInner
    241:241:boolean isPlayingInner() -> isPlayingInner
    245:250:void setCustomAudioFocusInner(com.kaolafm.opensdk.player.core.model.AAudioFocus) -> setCustomAudioFocusInner
    253:254:void setAudioFocusListenerInner(com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter) -> setAudioFocusListenerInner
    257:257:boolean requestAudioFocusInner() -> requestAudioFocusInner
    261:262:void abandonAudioFocusInner() -> abandonAudioFocusInner
    265:285:void managerAudioFocusChange(int) -> managerAudioFocusChange
    288:296:void manageGainFocus() -> manageGainFocus
    299:305:void manageLossFocus() -> manageLossFocus
    313:316:boolean checkAudioFocus() -> checkAudioFocus
    333:333:int getPlayStatusInner() -> getPlayStatusInner
    342:342:long getCurrentPositionInner() -> getCurrentPositionInner
    352:353:void setMediaVolumeInner(float,float) -> setMediaVolumeInner
    320:325:void lambda$new$0(int) -> lambda$new$0
    31:31:void access$000(com.kaolafm.opensdk.player.core.PlayerService) -> access$000
    31:31:void access$100(com.kaolafm.opensdk.player.core.PlayerService,java.lang.String,long,long,boolean) -> access$100
    31:31:void access$200(com.kaolafm.opensdk.player.core.PlayerService,long) -> access$200
    31:31:void access$300(com.kaolafm.opensdk.player.core.PlayerService) -> access$300
    31:31:void access$400(com.kaolafm.opensdk.player.core.PlayerService) -> access$400
    31:31:void access$500(com.kaolafm.opensdk.player.core.PlayerService) -> access$500
    31:31:void access$600(com.kaolafm.opensdk.player.core.PlayerService) -> access$600
    31:31:void access$700(com.kaolafm.opensdk.player.core.PlayerService) -> access$700
    31:31:void access$800(com.kaolafm.opensdk.player.core.PlayerService,long) -> access$800
    31:31:void access$900(com.kaolafm.opensdk.player.core.PlayerService,com.kaolafm.opensdk.player.core.listener.IPlayerStateCoreListener) -> access$900
    31:31:void access$1000(com.kaolafm.opensdk.player.core.PlayerService,com.kaolafm.opensdk.player.core.listener.IPlayerBufferProgressListener) -> access$1000
    31:31:void access$1100(com.kaolafm.opensdk.player.core.PlayerService,com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener) -> access$1100
    31:31:void access$1200(com.kaolafm.opensdk.player.core.PlayerService,int) -> access$1200
    31:31:boolean access$1300(com.kaolafm.opensdk.player.core.PlayerService) -> access$1300
    31:31:int access$1400(com.kaolafm.opensdk.player.core.PlayerService) -> access$1400
    31:31:boolean access$1500(com.kaolafm.opensdk.player.core.PlayerService) -> access$1500
    31:31:void access$1600(com.kaolafm.opensdk.player.core.PlayerService) -> access$1600
    31:31:void access$1700(com.kaolafm.opensdk.player.core.PlayerService,com.kaolafm.opensdk.player.core.model.AAudioFocus) -> access$1700
    31:31:void access$1800(com.kaolafm.opensdk.player.core.PlayerService,com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter) -> access$1800
    31:31:long access$1900(com.kaolafm.opensdk.player.core.PlayerService) -> access$1900
    31:31:void access$2000(com.kaolafm.opensdk.player.core.PlayerService,float,float) -> access$2000
com.kaolafm.opensdk.player.core.PlayerService$PlayerServiceBinder -> com.kaolafm.opensdk.player.core.PlayerService$PlayerServiceBinder:
    com.kaolafm.opensdk.player.core.PlayerService this$0 -> this$0
    69:69:void <init>(com.kaolafm.opensdk.player.core.PlayerService) -> <init>
    71:72:void initPlayer() -> initPlayer
    75:76:void start(java.lang.String,long) -> start
    79:80:void start(java.lang.String,long,boolean) -> start
    83:84:void start(java.lang.String,long,long) -> start
    87:88:void start(java.lang.String,long,long,boolean) -> start
    91:92:void setPosition(long) -> setPosition
    95:96:void pause() -> pause
    99:100:void play() -> play
    103:104:void stop() -> stop
    107:108:void reset() -> reset
    111:112:void release() -> release
    115:116:void seek(long) -> seek
    119:120:void setPlayerStateListener(com.kaolafm.opensdk.player.core.listener.IPlayerStateCoreListener) -> setPlayerStateListener
    123:124:void setPlayerBufferProgressListener(com.kaolafm.opensdk.player.core.listener.IPlayerBufferProgressListener) -> setPlayerBufferProgressListener
    127:128:void setInitCompleteListener(com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener) -> setInitCompleteListener
    131:132:void setLoudnessNormalization(int) -> setLoudnessNormalization
    135:135:boolean isPlaying() -> isPlaying
    139:139:int getPlayStatus() -> getPlayStatus
    143:143:boolean requestAudioFocus() -> requestAudioFocus
    147:148:void abandonAudioFocus() -> abandonAudioFocus
    151:152:void setCustomAudioFocus(com.kaolafm.opensdk.player.core.model.AAudioFocus) -> setCustomAudioFocus
    155:156:void setAudioFocusListener(com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter) -> setAudioFocusListener
    159:159:long getCurrentPosition() -> getCurrentPosition
    163:164:void setMediaVolume(float,float) -> setMediaVolume
com.kaolafm.opensdk.player.core.R -> com.kaolafm.opensdk.player.core.R:
    9:9:void <init>() -> <init>
com.kaolafm.opensdk.player.core.R$string -> com.kaolafm.opensdk.player.core.R$string:
    int app_name -> app_name
    10:10:void <init>() -> <init>
    11:11:void <clinit>() -> <clinit>
com.kaolafm.opensdk.player.core.ijk.IJKMediaPlayerAdapter -> com.kaolafm.opensdk.player.core.ijk.IJKMediaPlayerAdapter:
    com.kaolafm.opensdk.player.core.listener.IPlayerStateCoreListener mIPlayerState -> mIPlayerState
    com.kaolafm.opensdk.player.core.listener.IPlayerBufferProgressListener mIPlayerBufferProgressListener -> mIPlayerBufferProgressListener
    com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener mIPlayerInitCompleteListener -> mIPlayerInitCompleteListener
    tv.danmaku.ijk.media.player.IjkMediaPlayer mIjkMediaPlayer -> mIjkMediaPlayer
    java.lang.String mDataSource -> mDataSource
    int mPlayStatus -> mPlayStatus
    51:54:void <init>() -> <init>
    58:58:long getDuration() -> getDuration
    63:63:long getCurrentPosition() -> getCurrentPosition
    68:68:boolean isPlaying() -> isPlaying
    74:79:void pause() -> pause
    84:89:void play() -> play
    94:94:void preload(java.lang.String) -> preload
    99:105:void reset() -> reset
    110:114:void release() -> release
    118:119:void prepare() -> prepare
    123:124:void prepare(int) -> prepare
    128:130:void prepare(int,int) -> prepare
    135:140:void seek(long) -> seek
    144:150:void setDataSource(java.lang.String) -> setDataSource
    154:160:void stop() -> stop
    165:178:void start(java.lang.String,long,long,int) -> start
    182:182:java.lang.String getDnsAddress() -> getDnsAddress
    187:188:void setMediaVolume(float,float) -> setMediaVolume
    192:193:void setPlayerStateListener(com.kaolafm.opensdk.player.core.listener.IPlayerStateCoreListener) -> setPlayerStateListener
    197:198:void setBufferProgressListener(com.kaolafm.opensdk.player.core.listener.IPlayerBufferProgressListener) -> setBufferProgressListener
    202:203:void setInitPlayerInitCompleteListener(com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener) -> setInitPlayerInitCompleteListener
    208:212:void setDuration(long,long) -> setDuration
    217:217:void setPlayRatio(float) -> setPlayRatio
    222:226:void seekAtStart(long) -> seekAtStart
    230:230:int getPlayStatus() -> getPlayStatus
    235:236:void setPlayStatus(int) -> setPlayStatus
    342:342:void setWakeMode(android.content.Context,int) -> setWakeMode
    345:349:void notifyPlayerPreparing() -> notifyPlayerPreparing
    352:356:void notifyPlayerIdle() -> notifyPlayerIdle
    359:363:void notifyPlayerPlaying() -> notifyPlayerPlaying
    366:370:void notifyPlayerPaused() -> notifyPlayerPaused
    373:376:void notifyProgress(long,long) -> notifyProgress
    379:383:void notifyPlayerFailed(int,int) -> notifyPlayerFailed
    386:390:void notifyPlayerEnd() -> notifyPlayerEnd
    393:397:void notifySeekStart() -> notifySeekStart
    400:404:void notifySeekComplete() -> notifySeekComplete
    407:411:void notifyBufferingStart() -> notifyBufferingStart
    414:418:void notifyBufferingEnd() -> notifyBufferingEnd
    421:424:void notifyBufferProgress(long,long) -> notifyBufferProgress
    427:430:void notifyInitComplete() -> notifyInitComplete
    23:23:java.lang.String access$000(com.kaolafm.opensdk.player.core.ijk.IJKMediaPlayerAdapter) -> access$000
    23:23:void access$100(com.kaolafm.opensdk.player.core.ijk.IJKMediaPlayerAdapter) -> access$100
    23:23:void access$200(com.kaolafm.opensdk.player.core.ijk.IJKMediaPlayerAdapter) -> access$200
    23:23:void access$300(com.kaolafm.opensdk.player.core.ijk.IJKMediaPlayerAdapter,long,long) -> access$300
    23:23:void access$400(com.kaolafm.opensdk.player.core.ijk.IJKMediaPlayerAdapter,long,long) -> access$400
    23:23:void access$500(com.kaolafm.opensdk.player.core.ijk.IJKMediaPlayerAdapter) -> access$500
    23:23:void access$600(com.kaolafm.opensdk.player.core.ijk.IJKMediaPlayerAdapter,int,int) -> access$600
    23:23:void access$700(com.kaolafm.opensdk.player.core.ijk.IJKMediaPlayerAdapter) -> access$700
    23:23:void access$800(com.kaolafm.opensdk.player.core.ijk.IJKMediaPlayerAdapter) -> access$800
    23:23:void access$900(com.kaolafm.opensdk.player.core.ijk.IJKMediaPlayerAdapter) -> access$900
com.kaolafm.opensdk.player.core.ijk.IJKMediaPlayerAdapter$IJKCallBack -> com.kaolafm.opensdk.player.core.ijk.IJKMediaPlayerAdapter$IJKCallBack:
    int mArg1 -> mArg1
    int mArg2 -> mArg2
    java.lang.ref.WeakReference ijkMediaPlayerAdapterWeakReference -> ijkMediaPlayerAdapterWeakReference
    243:245:void <init>(com.kaolafm.opensdk.player.core.ijk.IJKMediaPlayerAdapter) -> <init>
    248:260:void rePlay() -> rePlay
    265:337:void message(int,int,int,java.lang.Object) -> message
com.kaolafm.opensdk.player.core.listener.IAudioFocusListener -> com.kaolafm.opensdk.player.core.listener.IAudioFocusListener:
    void onFocusChange(int) -> onFocusChange
com.kaolafm.opensdk.player.core.listener.IPlayerBufferProgressListener -> com.kaolafm.opensdk.player.core.listener.IPlayerBufferProgressListener:
    void onBufferProgress(long,long) -> onBufferProgress
com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener -> com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener:
    void onPlayerInitComplete(boolean) -> onPlayerInitComplete
com.kaolafm.opensdk.player.core.listener.IPlayerStateCoreListener -> com.kaolafm.opensdk.player.core.listener.IPlayerStateCoreListener:
    void onIdle(java.lang.String) -> onIdle
    void onPlayerPreparing(java.lang.String) -> onPlayerPreparing
    void onPlayerPlaying(java.lang.String) -> onPlayerPlaying
    void onPlayerPaused(java.lang.String) -> onPlayerPaused
    void onProgress(java.lang.String,long,long) -> onProgress
    void onPlayerFailed(java.lang.String,int,int) -> onPlayerFailed
    void onPlayerEnd(java.lang.String) -> onPlayerEnd
    void onSeekStart(java.lang.String) -> onSeekStart
    void onSeekComplete(java.lang.String) -> onSeekComplete
    void onBufferingStart(java.lang.String) -> onBufferingStart
    void onBufferingEnd(java.lang.String) -> onBufferingEnd
com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter -> com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter:
    void onAudioFocusChange(int) -> onAudioFocusChange
com.kaolafm.opensdk.player.core.listener.OnHandleAudioFocusListener -> com.kaolafm.opensdk.player.core.listener.OnHandleAudioFocusListener:
    boolean onAudioFocusDuck(java.lang.Object[]) -> onAudioFocusDuck
    boolean onAudioFocusGain(java.lang.Object[]) -> onAudioFocusGain
    boolean onAudioFocusLoss(java.lang.Object[]) -> onAudioFocusLoss
com.kaolafm.opensdk.player.core.listener.OnPlayLogicListener -> com.kaolafm.opensdk.player.core.listener.OnPlayLogicListener:
    boolean onPlayLogicDispose() -> onPlayLogicDispose
com.kaolafm.opensdk.player.core.listener.OnPlayerConfigListener -> com.kaolafm.opensdk.player.core.listener.OnPlayerConfigListener:
    int getMusicStreamType() -> getMusicStreamType
com.kaolafm.opensdk.player.core.model.AAudioFocus -> com.kaolafm.opensdk.player.core.model.AAudioFocus:
    com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter mIAudioFocusListener -> mIAudioFocusListener
    int mFocusState -> mFocusState
    10:10:void <init>() -> <init>
    boolean requestAudioFocus() -> requestAudioFocus
    boolean abandonAudioFocus() -> abandonAudioFocus
    30:30:int getAudioFocusState() -> getAudioFocusState
    39:40:void setAudioFocusListener(com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter) -> setAudioFocusListener
    48:52:void notifyAudioFocusChange(int) -> notifyAudioFocusChange
com.kaolafm.opensdk.player.core.model.AMediaPlayer -> com.kaolafm.opensdk.player.core.model.AMediaPlayer:
    11:11:void <init>() -> <init>
    void play() -> play
    void pause() -> pause
    void stop() -> stop
    void start(java.lang.String,long,long,int) -> start
    void seek(long) -> seek
    boolean isPlaying() -> isPlaying
    void setPlayerStateListener(com.kaolafm.opensdk.player.core.listener.IPlayerStateCoreListener) -> setPlayerStateListener
    void setBufferProgressListener(com.kaolafm.opensdk.player.core.listener.IPlayerBufferProgressListener) -> setBufferProgressListener
    void setInitPlayerInitCompleteListener(com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener) -> setInitPlayerInitCompleteListener
    void setDuration(long,long) -> setDuration
    void setPlayRatio(float) -> setPlayRatio
    void seekAtStart(long) -> seekAtStart
    int getPlayStatus() -> getPlayStatus
    long getDuration() -> getDuration
    long getCurrentPosition() -> getCurrentPosition
    void preload(java.lang.String) -> preload
    void reset() -> reset
    void release() -> release
    void prepare() -> prepare
    void prepare(int) -> prepare
    void prepare(int,int) -> prepare
    void setDataSource(java.lang.String) -> setDataSource
    69:69:java.lang.String getDnsAddress() -> getDnsAddress
    74:74:void setMediaVolume(float,float) -> setMediaVolume
    78:78:void setLoudnessNormalization(int) -> setLoudnessNormalization
com.kaolafm.opensdk.player.core.utils.AudioFocusManager -> com.kaolafm.opensdk.player.core.utils.AudioFocusManager:
    android.media.AudioManager mAudioManager -> mAudioManager
    android.media.AudioManager$OnAudioFocusChangeListener mOnAudioFocusChangeListener -> mOnAudioFocusChangeListener
    15:46:void <init>(android.content.Context) -> <init>
    26:31:boolean requestAudioFocus() -> requestAudioFocus
    37:42:boolean abandonAudioFocus() -> abandonAudioFocus
    47:48:void lambda$new$0(int) -> lambda$new$0
com.kaolafm.opensdk.player.core.utils.ContextMediaPlayer -> com.kaolafm.opensdk.player.core.utils.ContextMediaPlayer:
    int TYPE_IJK_MEDIA_PLAYER -> TYPE_IJK_MEDIA_PLAYER
    int TYPE_EXO_MEDIA_PLAYER -> TYPE_EXO_MEDIA_PLAYER
    com.kaolafm.opensdk.player.core.model.AMediaPlayer mediaPlayer -> mediaPlayer
    12:12:void <init>() -> <init>
    19:24:void initPlayer(int,android.content.Context) -> initPlayer
    27:27:com.kaolafm.opensdk.player.core.model.AMediaPlayer getMediaPlayer() -> getMediaPlayer
com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager -> com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager:
    com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager playerCustomizeManager -> playerCustomizeManager
    com.kaolafm.opensdk.player.core.listener.OnPlayerConfigListener mPlayerConfigListener -> mPlayerConfigListener
    com.kaolafm.opensdk.player.core.listener.OnHandleAudioFocusListener mHandleAudioFocusListener -> mHandleAudioFocusListener
    com.kaolafm.opensdk.player.core.listener.OnPlayLogicListener mPlayLogicListener -> mPlayLogicListener
    22:24:void <init>() -> <init>
    27:34:com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager getInstance() -> getInstance
    38:38:com.kaolafm.opensdk.player.core.listener.OnPlayerConfigListener getPlayerConfigListener() -> getPlayerConfigListener
    42:43:void setPlayerConfigListener(com.kaolafm.opensdk.player.core.listener.OnPlayerConfigListener) -> setPlayerConfigListener
    46:46:com.kaolafm.opensdk.player.core.listener.OnHandleAudioFocusListener getHandleAudioFocusListener() -> getHandleAudioFocusListener
    50:51:void setOnHandleAudioFocusListener(com.kaolafm.opensdk.player.core.listener.OnHandleAudioFocusListener) -> setOnHandleAudioFocusListener
    54:54:com.kaolafm.opensdk.player.core.listener.OnPlayLogicListener getPlayLogicListener() -> getPlayLogicListener
    58:59:void setPlayLogicListener(com.kaolafm.opensdk.player.core.listener.OnPlayLogicListener) -> setPlayLogicListener
    67:67:boolean disposePlay() -> disposePlay
    76:80:int getStreamChannel() -> getStreamChannel
    89:94:boolean disposeAudioFocusChangeDuck() -> disposeAudioFocusChangeDuck
com.kaolafm.opensdk.player.logic.PlayControlFactory -> com.kaolafm.opensdk.player.logic.PlayControlFactory:
    android.util.SparseArray PLAY_CONTROL_ARRAY -> PLAY_CONTROL_ARRAY
    25:26:void <init>() -> <init>
    29:33:com.kaolafm.opensdk.player.logic.playcontrol.IPlayControl getPlayControl(int) -> getPlayControl
    17:23:void <clinit>() -> <clinit>
com.kaolafm.opensdk.player.logic.PlayerManager -> com.kaolafm.opensdk.player.logic.PlayerManager:
    com.kaolafm.opensdk.player.logic.listener.IPlayListControl mIPlayListControl -> mIPlayListControl
    com.kaolafm.opensdk.player.logic.playcontrol.PlayControl mIPlayControl -> mIPlayControl
    int mCustomType -> mCustomType
    com.kaolafm.opensdk.player.logic.model.PlayerBuilder mBuilder -> mBuilder
    android.content.Context mContext -> mContext
    boolean isPlayerInitSuccess -> isPlayerInitSuccess
    java.util.ArrayList mPlayControlListenerArrayList -> mPlayControlListenerArrayList
    java.util.ArrayList mPlayListControlListenerArrayList -> mPlayListControlListenerArrayList
    com.kaolafm.opensdk.player.logic.PlayerManager$MIPlayListStateListener miPlayListStateListener -> miPlayListStateListener
    com.kaolafm.opensdk.player.logic.PlayerManager$MBasePlayStateListener mBasePlayStateListener -> mBasePlayStateListener
    com.kaolafm.opensdk.player.logic.listener.IPlayListControl mKradioInnerPlayListControl -> mKradioInnerPlayListControl
    java.util.HashMap mCustomIPlayListControlHashMap -> mCustomIPlayListControlHashMap
    boolean isFromUser -> isFromUser
    java.util.ArrayList mPlayerInitCompleteListenerArrayList -> mPlayerInitCompleteListenerArrayList
    java.util.ArrayList mAudioFocusListenerArrayList -> mAudioFocusListenerArrayList
    int mAudioFocusStatus -> mAudioFocusStatus
    com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener iPlayerInitCompleteListener -> iPlayerInitCompleteListener
    com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter iAudioFocusListener -> iAudioFocusListener
    40:844:void <init>() -> <init>
    112:112:com.kaolafm.opensdk.player.logic.PlayerManager getInstance() -> getInstance
    116:118:void init(android.content.Context) -> init
    127:130:void registerKRadioPlayList(java.lang.Class) -> registerKRadioPlayList
    139:147:void registerCustomPlayList(int,java.lang.Class) -> registerCustomPlayList
    153:154:void play() -> play
    162:167:void play(boolean) -> play
    173:174:void pause() -> pause
    182:187:void pause(java.lang.Boolean) -> pause
    193:194:void stop() -> stop
    202:207:void stop(boolean) -> stop
    210:211:void reset() -> reset
    214:219:void reset(boolean) -> reset
    225:226:void switchPlayerStatus() -> switchPlayerStatus
    234:239:void switchPlayerStatus(boolean) -> switchPlayerStatus
    248:252:void seek(int) -> seek
    258:273:void playPre() -> playPre
    279:296:void playNext() -> playNext
    304:359:void start(com.kaolafm.opensdk.player.logic.model.PlayerBuilder) -> start
    367:370:boolean hasNextPage() -> hasNextPage
    379:382:boolean hasPrePage() -> hasPrePage
    391:394:com.kaolafm.opensdk.player.logic.model.PlayItem getCurPlayItem() -> getCurPlayItem
    403:406:int getCurPosition() -> getCurPosition
    415:418:boolean hasPre() -> hasPre
    427:430:boolean hasNext() -> hasNext
    439:442:java.util.ArrayList getPlayList() -> getPlayList
    451:455:void loadNextPage(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> loadNextPage
    463:467:void loadPrePage(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> loadPrePage
    475:483:void addPlayListControlStateCallback(com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener) -> addPlayListControlStateCallback
    491:498:void removePlayListControlStateCallback(com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener) -> removePlayListControlStateCallback
    506:514:void addPlayControlStateCallback(com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener) -> addPlayControlStateCallback
    522:529:void removePlayControlStateCallback(com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener) -> removePlayControlStateCallback
    537:540:com.kaolafm.opensdk.player.logic.model.PlaylistInfo getPlayListInfo() -> getPlayListInfo
    549:553:void setLoudnessNormalization(int) -> setLoudnessNormalization
    561:561:boolean isFromUser() -> isFromUser
    566:577:void notifyListChange(java.util.ArrayList) -> notifyListChange
    580:591:void notifyPlayListError(int) -> notifyPlayListError
    594:595:void notifyPlayControl(int,com.kaolafm.opensdk.player.logic.model.PlayItem) -> notifyPlayControl
    598:648:void notifyPlayControl(int,com.kaolafm.opensdk.player.logic.model.PlayItem,long,long) -> notifyPlayControl
    748:757:void initBuilder(com.kaolafm.opensdk.player.logic.model.PlayerBuilder) -> initBuilder
    760:771:void initCustomPlayListControl(int) -> initCustomPlayListControl
    774:778:long getSeekPosition(com.kaolafm.opensdk.player.logic.model.PlayerBuilder) -> getSeekPosition
    784:791:void addPlayerInitComplete(com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener) -> addPlayerInitComplete
    795:801:void removePlayerInitComplete(com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener) -> removePlayerInitComplete
    804:817:void notifyPlayerInitComplete() -> notifyPlayerInitComplete
    820:820:boolean isPlayerInitSuccess() -> isPlayerInitSuccess
    824:827:int getPlayStatus() -> getPlayStatus
    831:834:long getCurrentPosition() -> getCurrentPosition
    838:842:void setMediaVolume(float,float) -> setMediaVolume
    850:862:void notifyAudioFocus(int) -> notifyAudioFocus
    865:872:void addAudioFocusListener(com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter) -> addAudioFocusListener
    875:881:void removeAudioFocusListener(com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter) -> removeAudioFocusListener
    884:884:int getCurrentAudioFocusStatus() -> getCurrentAudioFocusStatus
    920:943:void runUIThread(com.kaolafm.opensdk.player.logic.PlayerManager$UiThread) -> runUIThread
    953:969:void destroy() -> destroy
    845:847:void lambda$new$1(int) -> lambda$new$1
    781:781:void lambda$new$0(boolean) -> lambda$new$0
    34:34:void <init>(com.kaolafm.opensdk.player.logic.PlayerManager$1) -> <init>
    34:34:com.kaolafm.opensdk.player.logic.listener.IPlayListControl access$200(com.kaolafm.opensdk.player.logic.PlayerManager) -> access$200
    34:34:com.kaolafm.opensdk.player.logic.model.PlayerBuilder access$300(com.kaolafm.opensdk.player.logic.PlayerManager) -> access$300
    34:34:com.kaolafm.opensdk.player.logic.playcontrol.PlayControl access$400(com.kaolafm.opensdk.player.logic.PlayerManager) -> access$400
    34:34:long access$500(com.kaolafm.opensdk.player.logic.PlayerManager,com.kaolafm.opensdk.player.logic.model.PlayerBuilder) -> access$500
    34:34:void access$600(com.kaolafm.opensdk.player.logic.PlayerManager$UiThread) -> access$600
    34:34:void access$700(com.kaolafm.opensdk.player.logic.PlayerManager,int,com.kaolafm.opensdk.player.logic.model.PlayItem,long,long) -> access$700
    34:34:void access$800(com.kaolafm.opensdk.player.logic.PlayerManager,int,com.kaolafm.opensdk.player.logic.model.PlayItem) -> access$800
    34:34:void access$900(com.kaolafm.opensdk.player.logic.PlayerManager,java.util.ArrayList) -> access$900
    34:34:void access$1000(com.kaolafm.opensdk.player.logic.PlayerManager,int) -> access$1000
com.kaolafm.opensdk.player.logic.PlayerManager$1 -> com.kaolafm.opensdk.player.logic.PlayerManager$1:
    com.kaolafm.opensdk.player.logic.PlayerManager this$0 -> this$0
    261:261:void <init>(com.kaolafm.opensdk.player.logic.PlayerManager) -> <init>
    264:266:void onDataGet(com.kaolafm.opensdk.player.logic.model.PlayItem,java.util.ArrayList) -> onDataGet
    271:271:void onDataGetError(int) -> onDataGetError
com.kaolafm.opensdk.player.logic.PlayerManager$2 -> com.kaolafm.opensdk.player.logic.PlayerManager$2:
    com.kaolafm.opensdk.player.logic.PlayerManager this$0 -> this$0
    283:283:void <init>(com.kaolafm.opensdk.player.logic.PlayerManager) -> <init>
    286:289:void onDataGet(com.kaolafm.opensdk.player.logic.model.PlayItem,java.util.ArrayList) -> onDataGet
    294:294:void onDataGetError(int) -> onDataGetError
com.kaolafm.opensdk.player.logic.PlayerManager$3 -> com.kaolafm.opensdk.player.logic.PlayerManager$3:
    com.kaolafm.opensdk.player.logic.PlayerManager this$0 -> this$0
    340:340:void <init>(com.kaolafm.opensdk.player.logic.PlayerManager) -> <init>
    344:352:void onDataGet(com.kaolafm.opensdk.player.logic.model.PlayItem,java.util.ArrayList) -> onDataGet
    357:357:void onDataGetError(int) -> onDataGetError
com.kaolafm.opensdk.player.logic.PlayerManager$4 -> com.kaolafm.opensdk.player.logic.PlayerManager$4:
    com.kaolafm.opensdk.player.logic.PlayerManager$UiThread val$mUiThread -> val$mUiThread
    923:923:void <init>(com.kaolafm.opensdk.player.logic.PlayerManager$UiThread) -> <init>
    926:926:void onSubscribe(io.reactivex.disposables.Disposable) -> onSubscribe
    931:931:void onNext(java.lang.String) -> onNext
    936:936:void onError(java.lang.Throwable) -> onError
    940:941:void onComplete() -> onComplete
    923:923:void onNext(java.lang.Object) -> onNext
com.kaolafm.opensdk.player.logic.PlayerManager$MBasePlayStateListener -> com.kaolafm.opensdk.player.logic.PlayerManager$MBasePlayStateListener:
    com.kaolafm.opensdk.player.logic.PlayerManager mPlayerManager -> mPlayerManager
    656:658:void <init>(com.kaolafm.opensdk.player.logic.PlayerManager) -> <init>
    662:663:void onIdle(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onIdle
    667:668:void onPlayerPreparing(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onPlayerPreparing
    672:673:void onPlayerPlaying(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onPlayerPlaying
    677:678:void onPlayerPaused(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onPlayerPaused
    682:684:void onProgress(com.kaolafm.opensdk.player.logic.model.PlayItem,long,long) -> onProgress
    688:690:void onPlayerFailed(com.kaolafm.opensdk.player.logic.model.PlayItem,int,int) -> onPlayerFailed
    694:696:void onPlayerEnd(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onPlayerEnd
    700:702:void onSeekStart(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onSeekStart
    706:707:void onSeekComplete(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onSeekComplete
    711:712:void onBufferingStart(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onBufferingStart
    716:717:void onBufferingEnd(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onBufferingEnd
    721:722:void OnDownloadProgress(com.kaolafm.opensdk.player.logic.model.PlayItem,long,long) -> OnDownloadProgress
    721:721:void lambda$OnDownloadProgress$11(com.kaolafm.opensdk.player.logic.model.PlayItem,long,long) -> lambda$OnDownloadProgress$11
    716:716:void lambda$onBufferingEnd$10(com.kaolafm.opensdk.player.logic.model.PlayItem) -> lambda$onBufferingEnd$10
    711:711:void lambda$onBufferingStart$9(com.kaolafm.opensdk.player.logic.model.PlayItem) -> lambda$onBufferingStart$9
    706:706:void lambda$onSeekComplete$8(com.kaolafm.opensdk.player.logic.model.PlayItem) -> lambda$onSeekComplete$8
    700:700:void lambda$onSeekStart$7(com.kaolafm.opensdk.player.logic.model.PlayItem) -> lambda$onSeekStart$7
    694:694:void lambda$onPlayerEnd$6(com.kaolafm.opensdk.player.logic.model.PlayItem) -> lambda$onPlayerEnd$6
    688:688:void lambda$onPlayerFailed$5(com.kaolafm.opensdk.player.logic.model.PlayItem,int,int) -> lambda$onPlayerFailed$5
    682:682:void lambda$onProgress$4(com.kaolafm.opensdk.player.logic.model.PlayItem,long,long) -> lambda$onProgress$4
    677:677:void lambda$onPlayerPaused$3(com.kaolafm.opensdk.player.logic.model.PlayItem) -> lambda$onPlayerPaused$3
    672:672:void lambda$onPlayerPlaying$2(com.kaolafm.opensdk.player.logic.model.PlayItem) -> lambda$onPlayerPlaying$2
    667:667:void lambda$onPlayerPreparing$1(com.kaolafm.opensdk.player.logic.model.PlayItem) -> lambda$onPlayerPreparing$1
    662:662:void lambda$onIdle$0(com.kaolafm.opensdk.player.logic.model.PlayItem) -> lambda$onIdle$0
com.kaolafm.opensdk.player.logic.PlayerManager$MIPlayListStateListener -> com.kaolafm.opensdk.player.logic.PlayerManager$MIPlayListStateListener:
    com.kaolafm.opensdk.player.logic.PlayerManager mPlayerManager -> mPlayerManager
    731:733:void <init>(com.kaolafm.opensdk.player.logic.PlayerManager) -> <init>
    737:738:void onPlayListChange(java.util.ArrayList) -> onPlayListChange
    742:743:void onPlayListChangeError(int) -> onPlayListChangeError
com.kaolafm.opensdk.player.logic.PlayerManager$PlayManagerInstance -> com.kaolafm.opensdk.player.logic.PlayerManager$PlayManagerInstance:
    com.kaolafm.opensdk.player.logic.PlayerManager PLAYERMANAGER -> PLAYERMANAGER
    107:107:void <init>() -> <init>
    107:107:com.kaolafm.opensdk.player.logic.PlayerManager access$100() -> access$100
    108:108:void <clinit>() -> <clinit>
com.kaolafm.opensdk.player.logic.PlayerManager$UiThread -> com.kaolafm.opensdk.player.logic.PlayerManager$UiThread:
    void onSuccess() -> onSuccess
com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener -> com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener:
    9:9:void <init>() -> <init>
    14:14:void onIdle(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onIdle
    19:19:void onPlayerPreparing(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onPlayerPreparing
    24:24:void onPlayerPlaying(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onPlayerPlaying
    29:29:void onPlayerPaused(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onPlayerPaused
    34:34:void onProgress(com.kaolafm.opensdk.player.logic.model.PlayItem,long,long) -> onProgress
    39:39:void onPlayerFailed(com.kaolafm.opensdk.player.logic.model.PlayItem,int,int) -> onPlayerFailed
    44:44:void onPlayerEnd(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onPlayerEnd
    49:49:void onSeekStart(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onSeekStart
    54:54:void onSeekComplete(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onSeekComplete
    59:59:void onBufferingStart(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onBufferingStart
    64:64:void onBufferingEnd(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onBufferingEnd
    69:69:void OnDownloadProgress(com.kaolafm.opensdk.player.logic.model.PlayItem,long,long) -> OnDownloadProgress
com.kaolafm.opensdk.player.logic.listener.IPlayListControl -> com.kaolafm.opensdk.player.logic.listener.IPlayListControl:
    void initPlayList(com.kaolafm.opensdk.player.logic.model.PlayerBuilder,com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> initPlayList
    boolean hasNextPage() -> hasNextPage
    boolean hasPrePage() -> hasPrePage
    void getNextPlayItem(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> getNextPlayItem
    void getPrePlayItem(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> getPrePlayItem
    com.kaolafm.opensdk.player.logic.model.PlayItem getCurPlayItem() -> getCurPlayItem
    com.kaolafm.opensdk.player.logic.model.PlayItem getPlayItem(com.kaolafm.opensdk.player.logic.model.PlayerBuilder) -> getPlayItem
    int getCurPosition() -> getCurPosition
    void setCurPosition(int) -> setCurPosition
    void setCurPosition(com.kaolafm.opensdk.player.logic.model.PlayItem) -> setCurPosition
    boolean hasPre() -> hasPre
    boolean hasNext() -> hasNext
    java.util.ArrayList getPlayList() -> getPlayList
    com.kaolafm.opensdk.player.logic.model.PlaylistInfo getPlayListInfo() -> getPlayListInfo
    void loadNextPage(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> loadNextPage
    void loadPrePage(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> loadPrePage
    void setCallback(com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener) -> setCallback
    void release() -> release
    void clearPlayList() -> clearPlayList
com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener -> com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener:
    void onDataGet(com.kaolafm.opensdk.player.logic.model.PlayItem,java.util.ArrayList) -> onDataGet
    void onDataGetError(int) -> onDataGetError
com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener -> com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener:
    void onPlayListChange(java.util.ArrayList) -> onPlayListChange
    void onPlayListChangeError(int) -> onPlayListChangeError
com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener -> com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener:
    void onIdle(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onIdle
    void onPlayerPreparing(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onPlayerPreparing
    void onPlayerPlaying(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onPlayerPlaying
    void onPlayerPaused(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onPlayerPaused
    void onProgress(com.kaolafm.opensdk.player.logic.model.PlayItem,long,long) -> onProgress
    void onPlayerFailed(com.kaolafm.opensdk.player.logic.model.PlayItem,int,int) -> onPlayerFailed
    void onPlayerEnd(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onPlayerEnd
    void onSeekStart(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onSeekStart
    void onSeekComplete(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onSeekComplete
    void onBufferingStart(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onBufferingStart
    void onBufferingEnd(com.kaolafm.opensdk.player.logic.model.PlayItem) -> onBufferingEnd
com.kaolafm.opensdk.player.logic.listener.IReleaseAble -> com.kaolafm.opensdk.player.logic.listener.IReleaseAble:
    void release() -> release
com.kaolafm.opensdk.player.logic.model.AlbumPlayerBuilder -> com.kaolafm.opensdk.player.logic.model.AlbumPlayerBuilder:
    10:12:void <init>() -> <init>
com.kaolafm.opensdk.player.logic.model.BroadcastPlayerBuilder -> com.kaolafm.opensdk.player.logic.model.BroadcastPlayerBuilder:
    10:12:void <init>() -> <init>
com.kaolafm.opensdk.player.logic.model.CustomPlayerBuilder -> com.kaolafm.opensdk.player.logic.model.CustomPlayerBuilder:
    java.lang.String mChildId -> mChildId
    long mSeekPosition -> mSeekPosition
    int mSort -> mSort
    23:27:void <init>() -> <init>
    30:30:java.lang.String getChildId() -> getChildId
    34:35:com.kaolafm.opensdk.player.logic.model.CustomPlayerBuilder setChildId(java.lang.String) -> setChildId
    39:39:long getSeekPosition() -> getSeekPosition
    43:44:com.kaolafm.opensdk.player.logic.model.CustomPlayerBuilder setSeekPosition(long) -> setSeekPosition
    48:48:int getSort() -> getSort
    52:53:com.kaolafm.opensdk.player.logic.model.CustomPlayerBuilder setSort(int) -> setSort
com.kaolafm.opensdk.player.logic.model.LivePlayerBuilder -> com.kaolafm.opensdk.player.logic.model.LivePlayerBuilder:
    10:12:void <init>() -> <init>
com.kaolafm.opensdk.player.logic.model.PlayItem -> com.kaolafm.opensdk.player.logic.model.PlayItem:
    int CTG_TYPE_TX -> CTG_TYPE_TX
    long audioId -> audioId
    java.lang.String title -> title
    java.lang.String playUrl -> playUrl
    java.lang.String offlineUrl -> offlineUrl
    boolean isOffline -> isOffline
    java.lang.String offlinePlayUrl -> offlinePlayUrl
    int position -> position
    int duration -> duration
    int totalDuration -> totalDuration
    java.lang.String audioDes -> audioDes
    long albumId -> albumId
    java.lang.String albumPic -> albumPic
    java.lang.String albumOfflinePic -> albumOfflinePic
    java.lang.String albumName -> albumName
    int orderNum -> orderNum
    java.lang.String mp3PlayUrl -> mp3PlayUrl
    java.lang.String m3u8PlayUrl -> m3u8PlayUrl
    java.lang.String shareUrl -> shareUrl
    long categoryId -> categoryId
    java.lang.String hosts -> hosts
    long fileSize -> fileSize
    int isLiked -> isLiked
    java.lang.String updateTime -> updateTime
    long createTime -> createTime
    java.lang.String clockId -> clockId
    boolean isInterrupted -> isInterrupted
    int DATA_SRC_HISTORY -> DATA_SRC_HISTORY
    int dataSrc -> dataSrc
    boolean isLivingUrl -> isLivingUrl
    long startTime -> startTime
    long finishTime -> finishTime
    java.lang.String beginTime -> beginTime
    java.lang.String endTime -> endTime
    long liveId -> liveId
    java.lang.String audioPic -> audioPic
    com.kaolafm.opensdk.player.logic.model.PlayItemType type -> type
    int status -> status
    java.lang.String dnsAddress -> dnsAddress
    java.lang.String aacPlayUrl32 -> aacPlayUrl32
    java.lang.String aacPlayUrl64 -> aacPlayUrl64
    java.lang.String aacPlayUrl128 -> aacPlayUrl128
    java.lang.String icon -> icon
    boolean canAutoDel -> canAutoDel
    java.lang.String genre -> genre
    java.lang.String mid -> mid
    java.lang.String frequencyChannel -> frequencyChannel
    java.lang.String recommendReason -> recommendReason
    int isThirdParty -> isThirdParty
    long curSystemTime -> curSystemTime
    android.os.Parcelable$Creator CREATOR -> CREATOR
    69:170:void <init>() -> <init>
    173:173:com.kaolafm.opensdk.player.logic.model.PlayItemType getType() -> getType
    177:178:void setType(com.kaolafm.opensdk.player.logic.model.PlayItemType) -> setType
    181:181:long getLiveId() -> getLiveId
    185:186:void setLiveId(long) -> setLiveId
    189:190:void setAudioId(long) -> setAudioId
    193:193:long getAudioId() -> getAudioId
    197:197:java.lang.String getAudioPic() -> getAudioPic
    201:202:void setAudioPic(java.lang.String) -> setAudioPic
    205:206:void setTitle(java.lang.String) -> setTitle
    209:209:java.lang.String getTitle() -> getTitle
    213:214:void setPlayUrl(java.lang.String) -> setPlayUrl
    217:217:java.lang.String getPlayUrl() -> getPlayUrl
    221:222:void setIsOffline(boolean) -> setIsOffline
    225:225:boolean getIsOffline() -> getIsOffline
    229:230:void setOfflinePlayUrl(java.lang.String) -> setOfflinePlayUrl
    233:233:java.lang.String getOfflinePlayUrl() -> getOfflinePlayUrl
    237:238:void setPosition(int) -> setPosition
    241:241:int getPosition() -> getPosition
    245:246:void setDuration(int) -> setDuration
    249:249:int getDuration() -> getDuration
    253:254:void setAudioDes(java.lang.String) -> setAudioDes
    257:258:void setAlbumId(long) -> setAlbumId
    261:262:void setAlbumPic(java.lang.String) -> setAlbumPic
    265:266:void setAlbumName(java.lang.String) -> setAlbumName
    269:270:void setOrderNum(int) -> setOrderNum
    273:274:void setMp3PlayUrl(java.lang.String) -> setMp3PlayUrl
    277:278:void setShareUrl(java.lang.String) -> setShareUrl
    281:282:void setCategoryId(long) -> setCategoryId
    285:286:void setHosts(java.lang.String) -> setHosts
    289:290:void setFileSize(long) -> setFileSize
    293:294:void setIsLiked(int) -> setIsLiked
    297:298:void setUpdateTime(java.lang.String) -> setUpdateTime
    301:302:void setClockId(java.lang.String) -> setClockId
    305:305:java.lang.String getAudioDes() -> getAudioDes
    309:309:long getAlbumId() -> getAlbumId
    313:313:java.lang.String getAlbumPic() -> getAlbumPic
    317:317:java.lang.String getAlbumName() -> getAlbumName
    321:321:int getOrderNum() -> getOrderNum
    325:325:java.lang.String getMp3PlayUrl() -> getMp3PlayUrl
    329:329:java.lang.String getShareUrl() -> getShareUrl
    333:333:long getCategoryId() -> getCategoryId
    337:337:java.lang.String getHosts() -> getHosts
    341:341:long getFileSize() -> getFileSize
    345:345:int getIsLiked() -> getIsLiked
    349:349:java.lang.String getUpdateTime() -> getUpdateTime
    353:353:java.lang.String getClockId() -> getClockId
    357:357:long getStartTime() -> getStartTime
    361:362:void setStartTime(long) -> setStartTime
    365:365:long getFinishTime() -> getFinishTime
    369:370:void setFinishTime(long) -> setFinishTime
    373:373:java.lang.String getBeginTime() -> getBeginTime
    377:378:void setBeginTime(java.lang.String) -> setBeginTime
    381:381:java.lang.String getEndTime() -> getEndTime
    385:386:void setEndTime(java.lang.String) -> setEndTime
    389:390:void setIsInterrupted(boolean) -> setIsInterrupted
    393:393:boolean getIsInterrupted() -> getIsInterrupted
    397:397:long getCreateTime() -> getCreateTime
    401:402:void setCreateTime(long) -> setCreateTime
    405:405:int getDataSrc() -> getDataSrc
    409:410:void setDataSrc(int) -> setDataSrc
    413:413:java.lang.String getAlbumOfflinePic() -> getAlbumOfflinePic
    417:417:java.lang.String getM3u8PlayUrl() -> getM3u8PlayUrl
    421:422:void setM3u8PlayUrl(java.lang.String) -> setM3u8PlayUrl
    425:426:void setAlbumOfflinePic(java.lang.String) -> setAlbumOfflinePic
    429:429:boolean isLivingUrl() -> isLivingUrl
    433:434:void setIsLivingUrl(boolean) -> setIsLivingUrl
    437:437:java.lang.String getOfflineUrl() -> getOfflineUrl
    441:442:void setOfflineUrl(java.lang.String) -> setOfflineUrl
    445:445:int getTotalDuration() -> getTotalDuration
    449:450:void setTotalDuration(int) -> setTotalDuration
    453:453:int getStatus() -> getStatus
    457:458:void setStatus(int) -> setStatus
    461:461:java.lang.String getDnsAddress() -> getDnsAddress
    465:466:void setDnsAddress(java.lang.String) -> setDnsAddress
    469:469:java.lang.String getAacPlayUrl32() -> getAacPlayUrl32
    473:474:void setAacPlayUrl32(java.lang.String) -> setAacPlayUrl32
    477:477:java.lang.String getAacPlayUrl64() -> getAacPlayUrl64
    481:482:void setAacPlayUrl64(java.lang.String) -> setAacPlayUrl64
    485:485:java.lang.String getAacPlayUrl128() -> getAacPlayUrl128
    489:490:void setAacPlayUrl128(java.lang.String) -> setAacPlayUrl128
    493:494:void setIcon(java.lang.String) -> setIcon
    497:497:java.lang.String getIcon() -> getIcon
    501:501:boolean getCanAutoDel() -> getCanAutoDel
    505:506:void setCanAutoDel(boolean) -> setCanAutoDel
    509:509:java.lang.String getGenre() -> getGenre
    513:514:void setGenre(java.lang.String) -> setGenre
    517:517:java.lang.String getMid() -> getMid
    521:522:void setMid(java.lang.String) -> setMid
    525:525:java.lang.String getFrequencyChannel() -> getFrequencyChannel
    529:530:void setFrequencyChannel(java.lang.String) -> setFrequencyChannel
    533:536:java.lang.String getPic() -> getPic
    540:540:java.lang.String getRecommendReason() -> getRecommendReason
    544:545:void setRecommendReason(java.lang.String) -> setRecommendReason
    548:548:int getIsThirdParty() -> getIsThirdParty
    552:553:void setIsThirdParty(int) -> setIsThirdParty
    557:557:int describeContents() -> describeContents
    561:561:long getCurSystemTime() -> getCurSystemTime
    565:566:void setCurSystemTime(long) -> setCurSystemTime
    570:611:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    69:655:void <init>(android.os.Parcel) -> <init>
    9:9:void <init>(android.os.Parcel,com.kaolafm.opensdk.player.logic.model.PlayItem$1) -> <init>
    657:657:void <clinit>() -> <clinit>
com.kaolafm.opensdk.player.logic.model.PlayItem$1 -> com.kaolafm.opensdk.player.logic.model.PlayItem$1:
    657:657:void <init>() -> <init>
    659:659:com.kaolafm.opensdk.player.logic.model.PlayItem createFromParcel(android.os.Parcel) -> createFromParcel
    663:663:com.kaolafm.opensdk.player.logic.model.PlayItem[] newArray(int) -> newArray
    657:657:java.lang.Object[] newArray(int) -> newArray
    657:657:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
com.kaolafm.opensdk.player.logic.model.PlayItemType -> com.kaolafm.opensdk.player.logic.model.PlayItemType:
    com.kaolafm.opensdk.player.logic.model.PlayItemType DEFAULT -> DEFAULT
    com.kaolafm.opensdk.player.logic.model.PlayItemType LIVE_PLAYBACK -> LIVE_PLAYBACK
    com.kaolafm.opensdk.player.logic.model.PlayItemType LIVE_AUDITION_EDIT -> LIVE_AUDITION_EDIT
    com.kaolafm.opensdk.player.logic.model.PlayItemType LIVE_AUDITION -> LIVE_AUDITION
    com.kaolafm.opensdk.player.logic.model.PlayItemType LIVING -> LIVING
    com.kaolafm.opensdk.player.logic.model.PlayItemType BROADCAST_LIVING -> BROADCAST_LIVING
    com.kaolafm.opensdk.player.logic.model.PlayItemType BROADCAST_PLAYBACK -> BROADCAST_PLAYBACK
    com.kaolafm.opensdk.player.logic.model.PlayItemType[] $VALUES -> $VALUES
    10:10:com.kaolafm.opensdk.player.logic.model.PlayItemType[] values() -> values
    10:10:com.kaolafm.opensdk.player.logic.model.PlayItemType valueOf(java.lang.String) -> valueOf
    10:10:void <init>(java.lang.String,int) -> <init>
    10:24:void <clinit>() -> <clinit>
com.kaolafm.opensdk.player.logic.model.PlayerBuilder -> com.kaolafm.opensdk.player.logic.model.PlayerBuilder:
    java.lang.String mId -> mId
    int mType -> mType
    9:12:void <init>() -> <init>
    15:16:com.kaolafm.opensdk.player.logic.model.PlayerBuilder setId(java.lang.String) -> setId
    20:20:java.lang.String getId() -> getId
    24:24:int getType() -> getType
    28:29:com.kaolafm.opensdk.player.logic.model.PlayerBuilder setType(int) -> setType
com.kaolafm.opensdk.player.logic.model.PlaylistInfo -> com.kaolafm.opensdk.player.logic.model.PlaylistInfo:
    java.lang.String mAlbumName -> mAlbumName
    java.lang.String mAlbumPic -> mAlbumPic
    java.lang.String mPageIndex -> mPageIndex
    int mAllSize -> mAllSize
    boolean hasNextPage -> hasNextPage
    boolean hasPrePage -> hasPrePage
    7:17:void <init>() -> <init>
    20:20:java.lang.String getPageIndex() -> getPageIndex
    24:25:void setPageIndex(java.lang.String) -> setPageIndex
    28:28:boolean isHasNextPage() -> isHasNextPage
    32:33:void setHasNextPage(boolean) -> setHasNextPage
    36:36:boolean isHasPrePage() -> isHasPrePage
    40:41:void setHasPrePage(boolean) -> setHasPrePage
    44:44:int getAllSize() -> getAllSize
    48:49:void setAllSize(int) -> setAllSize
    52:52:java.lang.String getAlbumName() -> getAlbumName
    56:57:void setAlbumName(java.lang.String) -> setAlbumName
    60:60:java.lang.String getAlbumPic() -> getAlbumPic
    64:65:void setAlbumPic(java.lang.String) -> setAlbumPic
com.kaolafm.opensdk.player.logic.model.RadioPlayerBuilder -> com.kaolafm.opensdk.player.logic.model.RadioPlayerBuilder:
    10:12:void <init>() -> <init>
com.kaolafm.opensdk.player.logic.playcontrol.AlbumPlayControl -> com.kaolafm.opensdk.player.logic.playcontrol.AlbumPlayControl:
    7:7:void <init>() -> <init>
com.kaolafm.opensdk.player.logic.playcontrol.BasePlayControl -> com.kaolafm.opensdk.player.logic.playcontrol.BasePlayControl:
    com.kaolafm.opensdk.player.core.PlayerService$PlayerServiceBinder mPlayerBinder -> mPlayerBinder
    com.kaolafm.opensdk.player.logic.model.PlayItem mPlayItem -> mPlayItem
    com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener mBasePlayControlListener -> mBasePlayControlListener
    boolean isSeek -> isSeek
    java.lang.Long mLoseTime -> mLoseTime
    long mCurrentPosition -> mCurrentPosition
    long mAudioPlayedTime -> mAudioPlayedTime
    com.kaolafm.opensdk.player.core.listener.IPlayerBufferProgressListener mBufferProgressListener -> mBufferProgressListener
    com.kaolafm.opensdk.player.core.listener.IPlayerStateCoreListener mPlayerStateListener -> mPlayerStateListener
    29:220:void <init>() -> <init>
    50:51:void setPlayStateListener(com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener) -> setPlayStateListener
    55:58:void setBind(com.kaolafm.opensdk.player.core.PlayerService$PlayerServiceBinder) -> setBind
    62:66:void play() -> play
    70:75:void pause() -> pause
    79:83:void stop() -> stop
    87:92:void reset() -> reset
    96:101:void release() -> release
    105:110:void seek(int) -> seek
    114:118:void playTempTask(java.lang.String) -> playTempTask
    122:138:void start(int,com.kaolafm.opensdk.player.logic.model.PlayItem) -> start
    142:146:void setLoudnessNormalization(int) -> setLoudnessNormalization
    150:153:boolean isPlaying() -> isPlaying
    158:162:void requestAudioFocus() -> requestAudioFocus
    166:170:void setCustomAudioFocus(com.kaolafm.opensdk.player.core.model.AAudioFocus) -> setCustomAudioFocus
    175:179:void setAudioFocusListener(com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter) -> setAudioFocusListener
    183:186:long getCurrentPosition() -> getCurrentPosition
    191:195:void setMediaVolume(float,float) -> setMediaVolume
    199:202:int getPlayStatus() -> getPlayStatus
    207:211:void destroy() -> destroy
    332:335:void clearPlayArgument() -> clearPlayArgument
    214:218:void lambda$new$0(long,long) -> lambda$new$0
    20:20:com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener access$000(com.kaolafm.opensdk.player.logic.playcontrol.BasePlayControl) -> access$000
    20:20:long access$100(com.kaolafm.opensdk.player.logic.playcontrol.BasePlayControl) -> access$100
    20:20:long access$200(com.kaolafm.opensdk.player.logic.playcontrol.BasePlayControl) -> access$200
    20:20:long access$202(com.kaolafm.opensdk.player.logic.playcontrol.BasePlayControl,long) -> access$202
    20:20:long access$102(com.kaolafm.opensdk.player.logic.playcontrol.BasePlayControl,long) -> access$102
    20:20:void access$300(com.kaolafm.opensdk.player.logic.playcontrol.BasePlayControl) -> access$300
    20:20:boolean access$402(com.kaolafm.opensdk.player.logic.playcontrol.BasePlayControl,boolean) -> access$402
    20:20:java.lang.Long access$502(com.kaolafm.opensdk.player.logic.playcontrol.BasePlayControl,java.lang.Long) -> access$502
com.kaolafm.opensdk.player.logic.playcontrol.BasePlayControl$1 -> com.kaolafm.opensdk.player.logic.playcontrol.BasePlayControl$1:
    com.kaolafm.opensdk.player.logic.playcontrol.BasePlayControl this$0 -> this$0
    220:220:void <init>(com.kaolafm.opensdk.player.logic.playcontrol.BasePlayControl) -> <init>
    224:228:void onIdle(java.lang.String) -> onIdle
    232:236:void onPlayerPreparing(java.lang.String) -> onPlayerPreparing
    240:244:void onPlayerPlaying(java.lang.String) -> onPlayerPlaying
    248:253:void onPlayerPaused(java.lang.String) -> onPlayerPaused
    257:274:void onProgress(java.lang.String,long,long) -> onProgress
    278:283:void onPlayerFailed(java.lang.String,int,int) -> onPlayerFailed
    287:292:void onPlayerEnd(java.lang.String) -> onPlayerEnd
    296:301:void onSeekStart(java.lang.String) -> onSeekStart
    305:309:void onSeekComplete(java.lang.String) -> onSeekComplete
    313:318:void onBufferingStart(java.lang.String) -> onBufferingStart
    322:328:void onBufferingEnd(java.lang.String) -> onBufferingEnd
com.kaolafm.opensdk.player.logic.playcontrol.BroadcastPlayControl -> com.kaolafm.opensdk.player.logic.playcontrol.BroadcastPlayControl:
    int WAIT_TIMER -> WAIT_TIMER
    long progressTime -> progressTime
    long startTime -> startTime
    long endTime -> endTime
    long curServiceTime -> curServiceTime
    long livingTotalTime -> livingTotalTime
    io.reactivex.Observable mObservable -> mObservable
    io.reactivex.disposables.Disposable mDisposable -> mDisposable
    com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener mIPlayerStateListener -> mIPlayerStateListener
    41:42:void setPlayStateListener(com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener) -> setPlayStateListener
    45:47:void <init>() -> <init>
    51:66:void start(int,com.kaolafm.opensdk.player.logic.model.PlayItem) -> start
    69:70:void initTimer() -> initTimer
    73:107:void startTimer() -> startTimer
    110:114:void stopTimer() -> stopTimer
    117:122:void notifyProgress(long,long) -> notifyProgress
    125:130:void notifyPlayEnd() -> notifyPlayEnd
    134:135:void destroy() -> destroy
    25:25:io.reactivex.disposables.Disposable access$002(com.kaolafm.opensdk.player.logic.playcontrol.BroadcastPlayControl,io.reactivex.disposables.Disposable) -> access$002
    25:25:long access$102(com.kaolafm.opensdk.player.logic.playcontrol.BroadcastPlayControl,long) -> access$102
    25:25:long access$100(com.kaolafm.opensdk.player.logic.playcontrol.BroadcastPlayControl) -> access$100
    25:25:long access$200(com.kaolafm.opensdk.player.logic.playcontrol.BroadcastPlayControl) -> access$200
    25:25:void access$300(com.kaolafm.opensdk.player.logic.playcontrol.BroadcastPlayControl) -> access$300
    25:25:void access$400(com.kaolafm.opensdk.player.logic.playcontrol.BroadcastPlayControl,long,long) -> access$400
    25:25:void access$500(com.kaolafm.opensdk.player.logic.playcontrol.BroadcastPlayControl) -> access$500
com.kaolafm.opensdk.player.logic.playcontrol.BroadcastPlayControl$1 -> com.kaolafm.opensdk.player.logic.playcontrol.BroadcastPlayControl$1:
    com.kaolafm.opensdk.player.logic.playcontrol.BroadcastPlayControl this$0 -> this$0
    75:75:void <init>(com.kaolafm.opensdk.player.logic.playcontrol.BroadcastPlayControl) -> <init>
    78:79:void onSubscribe(io.reactivex.disposables.Disposable) -> onSubscribe
    83:94:void onNext(java.lang.Object) -> onNext
    99:99:void onError(java.lang.Throwable) -> onError
    103:104:void onComplete() -> onComplete
com.kaolafm.opensdk.player.logic.playcontrol.IPlayControl -> com.kaolafm.opensdk.player.logic.playcontrol.IPlayControl:
    void setPlayStateListener(com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener) -> setPlayStateListener
    void setBind(com.kaolafm.opensdk.player.core.PlayerService$PlayerServiceBinder) -> setBind
    void play() -> play
    void pause() -> pause
    void stop() -> stop
    void reset() -> reset
    void release() -> release
    void seek(int) -> seek
    void destroy() -> destroy
    void playTempTask(java.lang.String) -> playTempTask
    void start(int,com.kaolafm.opensdk.player.logic.model.PlayItem) -> start
    void setLoudnessNormalization(int) -> setLoudnessNormalization
    boolean isPlaying() -> isPlaying
    void requestAudioFocus() -> requestAudioFocus
    void setCustomAudioFocus(com.kaolafm.opensdk.player.core.model.AAudioFocus) -> setCustomAudioFocus
    void setAudioFocusListener(com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter) -> setAudioFocusListener
    long getCurrentPosition() -> getCurrentPosition
    void setMediaVolume(float,float) -> setMediaVolume
    int getPlayStatus() -> getPlayStatus
com.kaolafm.opensdk.player.logic.playcontrol.PGCPlayControl -> com.kaolafm.opensdk.player.logic.playcontrol.PGCPlayControl:
    7:7:void <init>() -> <init>
com.kaolafm.opensdk.player.logic.playcontrol.PlayControl -> com.kaolafm.opensdk.player.logic.playcontrol.PlayControl:
    com.kaolafm.opensdk.player.core.PlayerService$PlayerServiceBinder mPlayerBinder -> mPlayerBinder
    com.kaolafm.opensdk.player.logic.model.PlayItem mPlayItem -> mPlayItem
    com.kaolafm.opensdk.player.logic.playcontrol.IPlayControl mIPlayControl -> mIPlayControl
    com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener mIPlayControlListener -> mIPlayControlListener
    com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener mInitCompleteListener -> mInitCompleteListener
    android.content.Context mContext -> mContext
    int mType -> mType
    android.content.ServiceConnection mPlayerServiceConnection -> mPlayerServiceConnection
    com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener iPlayerInitCompleteListener -> iPlayerInitCompleteListener
    33:217:void <init>() -> <init>
    41:41:com.kaolafm.opensdk.player.logic.playcontrol.PlayControl getInstance() -> getInstance
    45:51:void init(android.content.Context,com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener,com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener) -> init
    55:59:void play() -> play
    62:66:void pause() -> pause
    69:73:void stop() -> stop
    76:80:void reset() -> reset
    83:87:void release() -> release
    90:98:void switchPlayerStatus() -> switchPlayerStatus
    101:105:void seek(int) -> seek
    108:111:long getCurrentPosition() -> getCurrentPosition
    116:121:void destroy() -> destroy
    125:129:void playTempTask(java.lang.String) -> playTempTask
    132:136:void setCustomAudioFocus(com.kaolafm.opensdk.player.core.model.AAudioFocus) -> setCustomAudioFocus
    139:146:void start(int,com.kaolafm.opensdk.player.logic.model.PlayItem) -> start
    149:150:void setCallback(com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener) -> setCallback
    153:157:void setLoudnessNormalization(int) -> setLoudnessNormalization
    161:164:boolean isPlaying() -> isPlaying
    173:176:int getPlayStatus() -> getPlayStatus
    180:185:void setMediaVolume(float,float) -> setMediaVolume
    188:192:void setAudioFocusListener(com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter) -> setAudioFocusListener
    226:234:void unBindService() -> unBindService
    218:221:void lambda$new$0(boolean) -> lambda$new$0
    24:24:void <init>(com.kaolafm.opensdk.player.logic.playcontrol.PlayControl$1) -> <init>
    24:24:com.kaolafm.opensdk.player.core.PlayerService$PlayerServiceBinder access$202(com.kaolafm.opensdk.player.logic.playcontrol.PlayControl,com.kaolafm.opensdk.player.core.PlayerService$PlayerServiceBinder) -> access$202
    24:24:com.kaolafm.opensdk.player.core.PlayerService$PlayerServiceBinder access$200(com.kaolafm.opensdk.player.logic.playcontrol.PlayControl) -> access$200
    24:24:com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener access$300(com.kaolafm.opensdk.player.logic.playcontrol.PlayControl) -> access$300
com.kaolafm.opensdk.player.logic.playcontrol.PlayControl$1 -> com.kaolafm.opensdk.player.logic.playcontrol.PlayControl$1:
    com.kaolafm.opensdk.player.logic.playcontrol.PlayControl this$0 -> this$0
    194:194:void <init>(com.kaolafm.opensdk.player.logic.playcontrol.PlayControl) -> <init>
    198:203:void onServiceConnected(android.content.ComponentName,android.os.IBinder) -> onServiceConnected
    207:214:void onServiceDisconnected(android.content.ComponentName) -> onServiceDisconnected
com.kaolafm.opensdk.player.logic.playcontrol.PlayControl$PlayInstance -> com.kaolafm.opensdk.player.logic.playcontrol.PlayControl$PlayInstance:
    com.kaolafm.opensdk.player.logic.playcontrol.PlayControl PLAYER -> PLAYER
    36:36:void <init>() -> <init>
    36:36:com.kaolafm.opensdk.player.logic.playcontrol.PlayControl access$100() -> access$100
    37:37:void <clinit>() -> <clinit>
com.kaolafm.opensdk.player.logic.playlist.AlbumPlayListControl -> com.kaolafm.opensdk.player.logic.playlist.AlbumPlayListControl:
    26:26:void <init>() -> <init>
    29:41:void initPlayList(com.kaolafm.opensdk.player.logic.model.PlayerBuilder,com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> initPlayList
    45:47:void loadNextPage(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> loadNextPage
    51:53:void loadPrePage(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> loadPrePage
    56:97:void loadData(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> loadData
    114:128:void getAudioInfo(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> getAudioInfo
    131:137:void updateListInfo(com.kaolafm.opensdk.api.BasePageResult,com.kaolafm.opensdk.player.logic.model.PlayItem) -> updateListInfo
    26:26:void access$000(com.kaolafm.opensdk.player.logic.playlist.AlbumPlayListControl,com.kaolafm.opensdk.api.BasePageResult,com.kaolafm.opensdk.player.logic.model.PlayItem) -> access$000
    26:26:void access$100(com.kaolafm.opensdk.player.logic.playlist.AlbumPlayListControl,com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> access$100
com.kaolafm.opensdk.player.logic.playlist.AlbumPlayListControl$1 -> com.kaolafm.opensdk.player.logic.playlist.AlbumPlayListControl$1:
    com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener val$iPlayListGetListener -> val$iPlayListGetListener
    com.kaolafm.opensdk.player.logic.playlist.AlbumPlayListControl this$0 -> this$0
    70:70:void <init>(com.kaolafm.opensdk.player.logic.playlist.AlbumPlayListControl,com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> <init>
    73:88:void onSuccess(com.kaolafm.opensdk.api.BasePageResult) -> onSuccess
    92:95:void onError(com.kaolafm.opensdk.http.error.ApiException) -> onError
    70:70:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.player.logic.playlist.AlbumPlayListControl$2 -> com.kaolafm.opensdk.player.logic.playlist.AlbumPlayListControl$2:
    com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener val$iPlayListGetListener -> val$iPlayListGetListener
    com.kaolafm.opensdk.player.logic.playlist.AlbumPlayListControl this$0 -> this$0
    114:114:void <init>(com.kaolafm.opensdk.player.logic.playlist.AlbumPlayListControl,com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> <init>
    117:121:void onSuccess(com.kaolafm.opensdk.api.media.model.AudioDetails) -> onSuccess
    126:126:void onError(com.kaolafm.opensdk.http.error.ApiException) -> onError
    114:114:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.player.logic.playlist.BasePlayListControl -> com.kaolafm.opensdk.player.logic.playlist.BasePlayListControl:
    java.util.ArrayList mPlayItemArrayList -> mPlayItemArrayList
    com.kaolafm.opensdk.player.logic.model.PlaylistInfo mPlaylistInfo -> mPlaylistInfo
    int mPosition -> mPosition
    com.kaolafm.opensdk.player.logic.model.PlayerBuilder mPlayerBuilder -> mPlayerBuilder
    com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener mIPlayListControlListener -> mIPlayListControlListener
    30:35:void <init>() -> <init>
    39:44:void initPlayList(com.kaolafm.opensdk.player.logic.model.PlayerBuilder,com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> initPlayList
    48:48:boolean hasNextPage() -> hasNextPage
    53:53:boolean hasPrePage() -> hasPrePage
    58:59:void clearPlayList() -> clearPlayList
    63:82:void getNextPlayItem(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> getNextPlayItem
    86:104:void getPrePlayItem(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> getPrePlayItem
    108:117:com.kaolafm.opensdk.player.logic.model.PlayItem getCurPlayItem() -> getCurPlayItem
    122:143:com.kaolafm.opensdk.player.logic.model.PlayItem getPlayItem(com.kaolafm.opensdk.player.logic.model.PlayerBuilder) -> getPlayItem
    148:148:int getCurPosition() -> getCurPosition
    153:154:void setCurPosition(int) -> setCurPosition
    158:176:void setCurPosition(com.kaolafm.opensdk.player.logic.model.PlayItem) -> setCurPosition
    180:186:boolean hasPre() -> hasPre
    191:200:boolean hasNext() -> hasNext
    205:205:java.util.ArrayList getPlayList() -> getPlayList
    210:210:com.kaolafm.opensdk.player.logic.model.PlaylistInfo getPlayListInfo() -> getPlayListInfo
    216:216:void loadNextPage(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> loadNextPage
    221:221:void loadPrePage(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> loadPrePage
    225:227:void setCallback(com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener) -> setCallback
    231:237:void release() -> release
    240:250:void initPlayListInfo(com.kaolafm.opensdk.player.logic.model.PlayerBuilder) -> initPlayListInfo
    253:257:void notifyPlayListGetError(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener,int) -> notifyPlayListGetError
    260:264:void notifyPlayListGet(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener,com.kaolafm.opensdk.player.logic.model.PlayItem,java.util.ArrayList) -> notifyPlayListGet
    267:271:void notifyPlayListChangeError(int) -> notifyPlayListChangeError
    274:278:void notifyPlayListChange(java.util.ArrayList) -> notifyPlayListChange
com.kaolafm.opensdk.player.logic.playlist.BroadcastPlayListControl -> com.kaolafm.opensdk.player.logic.playlist.BroadcastPlayListControl:
    25:25:void <init>() -> <init>
    28:30:void initPlayList(com.kaolafm.opensdk.player.logic.model.PlayerBuilder,com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> initPlayList
    34:36:void loadPrePage(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> loadPrePage
    40:42:void loadNextPage(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> loadNextPage
    45:92:void loadData(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> loadData
    96:126:void getNextPlayItem(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> getNextPlayItem
    129:159:void initLiving(com.kaolafm.opensdk.player.logic.model.PlayItem,com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> initLiving
    163:171:com.kaolafm.opensdk.player.logic.model.PlayItem getPlayItem(com.kaolafm.opensdk.player.logic.model.PlayerBuilder) -> getPlayItem
com.kaolafm.opensdk.player.logic.playlist.BroadcastPlayListControl$1 -> com.kaolafm.opensdk.player.logic.playlist.BroadcastPlayListControl$1:
    com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener val$iPlayListGetListener -> val$iPlayListGetListener
    com.kaolafm.opensdk.player.logic.playlist.BroadcastPlayListControl this$0 -> this$0
    59:59:void <init>(com.kaolafm.opensdk.player.logic.playlist.BroadcastPlayListControl,com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> <init>
    62:84:void onSuccess(java.util.List) -> onSuccess
    88:90:void onError(com.kaolafm.opensdk.http.error.ApiException) -> onError
    59:59:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.player.logic.playlist.BroadcastPlayListControl$2 -> com.kaolafm.opensdk.player.logic.playlist.BroadcastPlayListControl$2:
    com.kaolafm.opensdk.player.logic.model.PlayItem val$playItem -> val$playItem
    com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener val$iPlayListGetListener -> val$iPlayListGetListener
    com.kaolafm.opensdk.player.logic.playlist.BroadcastPlayListControl this$0 -> this$0
    130:130:void <init>(com.kaolafm.opensdk.player.logic.playlist.BroadcastPlayListControl,com.kaolafm.opensdk.player.logic.model.PlayItem,com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> <init>
    133:151:void onSuccess(com.kaolafm.opensdk.api.broadcast.ProgramDetails) -> onSuccess
    155:157:void onError(com.kaolafm.opensdk.http.error.ApiException) -> onError
    130:130:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.player.logic.playlist.LivePlayListControl -> com.kaolafm.opensdk.player.logic.playlist.LivePlayListControl:
    16:16:void <init>() -> <init>
    19:41:void initPlayList(com.kaolafm.opensdk.player.logic.model.PlayerBuilder,com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> initPlayList
com.kaolafm.opensdk.player.logic.playlist.LivePlayListControl$1 -> com.kaolafm.opensdk.player.logic.playlist.LivePlayListControl$1:
    com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener val$iPlayListGetListener -> val$iPlayListGetListener
    com.kaolafm.opensdk.player.logic.playlist.LivePlayListControl this$0 -> this$0
    20:20:void <init>(com.kaolafm.opensdk.player.logic.playlist.LivePlayListControl,com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> <init>
    23:33:void onSuccess(com.kaolafm.opensdk.api.live.model.LiveInfoDetail) -> onSuccess
    37:39:void onError(com.kaolafm.opensdk.http.error.ApiException) -> onError
    20:20:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.player.logic.playlist.OneKeyListenPlayListControl -> com.kaolafm.opensdk.player.logic.playlist.OneKeyListenPlayListControl:
    24:24:void <init>() -> <init>
    28:30:void initPlayList(com.kaolafm.opensdk.player.logic.model.PlayerBuilder,com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> initPlayList
    34:36:void loadPrePage(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> loadPrePage
    40:42:void loadNextPage(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> loadNextPage
    45:78:void loadData(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> loadData
    81:91:void updateListInfo(java.util.List) -> updateListInfo
    95:95:boolean hasNextPage() -> hasNextPage
    24:24:void access$000(com.kaolafm.opensdk.player.logic.playlist.OneKeyListenPlayListControl,java.util.List) -> access$000
com.kaolafm.opensdk.player.logic.playlist.OneKeyListenPlayListControl$1 -> com.kaolafm.opensdk.player.logic.playlist.OneKeyListenPlayListControl$1:
    com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener val$iPlayListGetListener -> val$iPlayListGetListener
    com.kaolafm.opensdk.player.logic.playlist.OneKeyListenPlayListControl this$0 -> this$0
    53:53:void <init>(com.kaolafm.opensdk.player.logic.playlist.OneKeyListenPlayListControl,com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> <init>
    56:70:void onSuccess(java.util.List) -> onSuccess
    74:76:void onError(com.kaolafm.opensdk.http.error.ApiException) -> onError
    53:53:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.player.logic.playlist.PGCPlayListControl -> com.kaolafm.opensdk.player.logic.playlist.PGCPlayListControl:
    25:25:void <init>() -> <init>
    29:31:void initPlayList(com.kaolafm.opensdk.player.logic.model.PlayerBuilder,com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> initPlayList
    35:37:void loadPrePage(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> loadPrePage
    41:43:void loadNextPage(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> loadNextPage
    46:80:void loadData(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> loadData
    83:109:void initRadioInfo(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> initRadioInfo
    112:122:void updateListInfo(java.util.List) -> updateListInfo
    126:126:boolean hasNextPage() -> hasNextPage
    25:25:void access$000(com.kaolafm.opensdk.player.logic.playlist.PGCPlayListControl,java.util.List) -> access$000
    25:25:void access$100(com.kaolafm.opensdk.player.logic.playlist.PGCPlayListControl,com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> access$100
com.kaolafm.opensdk.player.logic.playlist.PGCPlayListControl$1 -> com.kaolafm.opensdk.player.logic.playlist.PGCPlayListControl$1:
    com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener val$iPlayListGetListener -> val$iPlayListGetListener
    com.kaolafm.opensdk.player.logic.playlist.PGCPlayListControl this$0 -> this$0
    55:55:void <init>(com.kaolafm.opensdk.player.logic.playlist.PGCPlayListControl,com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> <init>
    58:72:void onSuccess(java.util.List) -> onSuccess
    76:78:void onError(com.kaolafm.opensdk.http.error.ApiException) -> onError
    55:55:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.player.logic.playlist.PGCPlayListControl$2 -> com.kaolafm.opensdk.player.logic.playlist.PGCPlayListControl$2:
    com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener val$iPlayListGetListener -> val$iPlayListGetListener
    com.kaolafm.opensdk.player.logic.playlist.PGCPlayListControl this$0 -> this$0
    91:91:void <init>(com.kaolafm.opensdk.player.logic.playlist.PGCPlayListControl,com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> <init>
    94:102:void onSuccess(com.kaolafm.opensdk.api.media.model.RadioDetails) -> onSuccess
    106:107:void onError(com.kaolafm.opensdk.http.error.ApiException) -> onError
    91:91:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.player.logic.playlist.PlayListControl -> com.kaolafm.opensdk.player.logic.playlist.PlayListControl:
    com.kaolafm.opensdk.player.logic.listener.IPlayListControl mIPlayListControl -> mIPlayListControl
    com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener mIPlayListControlListener -> mIPlayListControlListener
    int mType -> mType
    25:26:void <init>() -> <init>
    30:37:void initPlayList(com.kaolafm.opensdk.player.logic.model.PlayerBuilder,com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> initPlayList
    41:44:boolean hasNextPage() -> hasNextPage
    49:52:boolean hasPrePage() -> hasPrePage
    57:61:void clearPlayList() -> clearPlayList
    65:69:void getNextPlayItem(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> getNextPlayItem
    73:77:void getPrePlayItem(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> getPrePlayItem
    81:84:com.kaolafm.opensdk.player.logic.model.PlayItem getCurPlayItem() -> getCurPlayItem
    89:92:com.kaolafm.opensdk.player.logic.model.PlayItem getPlayItem(com.kaolafm.opensdk.player.logic.model.PlayerBuilder) -> getPlayItem
    97:100:int getCurPosition() -> getCurPosition
    105:109:void setCurPosition(int) -> setCurPosition
    113:117:void setCurPosition(com.kaolafm.opensdk.player.logic.model.PlayItem) -> setCurPosition
    121:124:boolean hasPre() -> hasPre
    129:132:boolean hasNext() -> hasNext
    137:141:java.util.ArrayList getPlayList() -> getPlayList
    146:150:com.kaolafm.opensdk.player.logic.model.PlaylistInfo getPlayListInfo() -> getPlayListInfo
    155:159:void loadNextPage(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> loadNextPage
    163:167:void loadPrePage(com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener) -> loadPrePage
    171:177:void setCallback(com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener) -> setCallback
    181:185:void release() -> release
    188:218:void initPlayListControl(int) -> initPlayListControl
com.kaolafm.opensdk.player.logic.playlist.PlayListUtils -> com.kaolafm.opensdk.player.logic.playlist.PlayListUtils:
    21:21:void <init>() -> <init>
    24:46:java.util.ArrayList audioDetail2PlayItem(java.util.List,java.lang.String,int) -> audioDetail2PlayItem
    50:80:com.kaolafm.opensdk.player.logic.model.PlayItem translateToPlayItem(com.kaolafm.opensdk.api.media.model.AudioDetails) -> translateToPlayItem
    85:96:java.util.ArrayList programDetails2PlayItem(java.util.List) -> programDetails2PlayItem
    100:113:int getLivingBroadcastPlayItem(java.util.ArrayList) -> getLivingBroadcastPlayItem
    117:161:com.kaolafm.opensdk.player.logic.model.PlayItem translateToPlayItem(com.kaolafm.opensdk.api.broadcast.ProgramDetails) -> translateToPlayItem
    165:180:com.kaolafm.opensdk.player.logic.model.PlayItem liveInfoToPlayItem(com.kaolafm.opensdk.api.live.model.LiveInfoDetail) -> liveInfoToPlayItem
com.kaolafm.opensdk.player.logic.playlist.PlayerBuilderUtil -> com.kaolafm.opensdk.player.logic.playlist.PlayerBuilderUtil:
    22:22:void <init>() -> <init>
    25:30:com.kaolafm.opensdk.player.logic.model.AlbumPlayerBuilder toAlbumPlayerBuilder(com.kaolafm.opensdk.api.media.model.AlbumDetails) -> toAlbumPlayerBuilder
    34:40:com.kaolafm.opensdk.player.logic.model.AlbumPlayerBuilder toAlbumPlayerBuilder(com.kaolafm.opensdk.api.media.model.AudioDetails) -> toAlbumPlayerBuilder
    44:49:com.kaolafm.opensdk.player.logic.model.RadioPlayerBuilder toRadioPlayerBuilder(com.kaolafm.opensdk.api.media.model.RadioDetails) -> toRadioPlayerBuilder
    53:59:com.kaolafm.opensdk.player.logic.model.RadioPlayerBuilder toRadioPlayerBuilder(long,com.kaolafm.opensdk.api.media.model.AudioDetails) -> toRadioPlayerBuilder
    64:69:com.kaolafm.opensdk.player.logic.model.BroadcastPlayerBuilder toBroadcastPlayBuilder(com.kaolafm.opensdk.api.broadcast.BroadcastDetails) -> toBroadcastPlayBuilder
    73:80:com.kaolafm.opensdk.player.logic.model.BroadcastPlayerBuilder toBroadcastPlayBuilder(com.kaolafm.opensdk.api.broadcast.ProgramDetails) -> toBroadcastPlayBuilder
    84:91:com.kaolafm.opensdk.player.logic.model.LivePlayerBuilder toLivePlayBuilder(com.kaolafm.opensdk.api.live.model.LiveInfoDetail) -> toLivePlayBuilder
    95:122:com.kaolafm.opensdk.player.logic.model.PlayerBuilder toPlayBuilder(com.kaolafm.opensdk.player.logic.model.PlayItem) -> toPlayBuilder
com.kaolafm.opensdk.player.logic.util.PlayerConstants -> com.kaolafm.opensdk.player.logic.util.PlayerConstants:
    java.lang.String LOG_TAG -> LOG_TAG
    java.lang.String LOG_PROGRESS_TAG -> LOG_PROGRESS_TAG
    int PAGE_NUMBER_10 -> PAGE_NUMBER_10
    int RESOURCES_TYPE_INVALID -> RESOURCES_TYPE_INVALID
    int RESOURCES_TYPE_ALBUM -> RESOURCES_TYPE_ALBUM
    int RESOURCES_TYPE_AUDIO -> RESOURCES_TYPE_AUDIO
    int RESOURCES_TYPE_PGC -> RESOURCES_TYPE_PGC
    int RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE -> RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE
    int RESOURCES_TYPE_BROADCAST -> RESOURCES_TYPE_BROADCAST
    int RESOURCES_TYPE_LIVING -> RESOURCES_TYPE_LIVING
    int RESOURCES_TYPE_QQ_MUSIC -> RESOURCES_TYPE_QQ_MUSIC
    int RESOURCES_TYPE_MIN_SIZE -> RESOURCES_TYPE_MIN_SIZE
    int TYPE_PLAYER_IDLE -> TYPE_PLAYER_IDLE
    int TYPE_PLAYER_PREPARING -> TYPE_PLAYER_PREPARING
    int TYPE_PLAYER_PLAYING -> TYPE_PLAYER_PLAYING
    int TYPE_PLAYER_PROGRESS -> TYPE_PLAYER_PROGRESS
    int TYPE_PLAYER_PAUSED -> TYPE_PLAYER_PAUSED
    int TYPE_SEEK_START -> TYPE_SEEK_START
    int TYPE_SEEK_COMPLETE -> TYPE_SEEK_COMPLETE
    int TYPE_BUFFERING_START -> TYPE_BUFFERING_START
    int TYPE_BUFFERING_END -> TYPE_BUFFERING_END
    int TYPE_PLAYER_END -> TYPE_PLAYER_END
    int TYPE_PLAYER_DOWNLOAD_PROGRESS -> TYPE_PLAYER_DOWNLOAD_PROGRESS
    int TYPE_PLAYER_FAILED -> TYPE_PLAYER_FAILED
    int BROADCAST_STATUS_DEFAULT -> BROADCAST_STATUS_DEFAULT
    int BROADCAST_STATUS_LIVING -> BROADCAST_STATUS_LIVING
    int BROADCAST_STATUS_PLAYBACK -> BROADCAST_STATUS_PLAYBACK
    int BROADCAST_STATUS_NOT_ON_AIR -> BROADCAST_STATUS_NOT_ON_AIR
    int SORT_ACS -> SORT_ACS
    int SORT_DESC -> SORT_DESC
    float LEFT_VOLUME_MAX -> LEFT_VOLUME_MAX
    float RIGHT_VOLUME_MAX -> RIGHT_VOLUME_MAX
    float LEFT_VOLUME_MIN -> LEFT_VOLUME_MIN
    float RIGHT_VOLUME_MIN -> RIGHT_VOLUME_MIN
    7:7:void <init>() -> <init>
com.kaolafm.opensdk.player.logic.util.PlayerPreconditions -> com.kaolafm.opensdk.player.logic.util.PlayerPreconditions:
    13:13:void <init>() -> <init>
    16:19:void checkArgument(boolean,java.lang.String) -> checkArgument
    22:22:boolean checkNull(java.lang.Object) -> checkNull
    27:27:java.lang.Object checkNotNull(java.lang.Object) -> checkNotNull
    32:35:java.lang.Object checkNotNull(java.lang.Object,java.lang.String) -> checkNotNull
    40:43:java.lang.String checkNotEmpty(java.lang.String) -> checkNotEmpty
    48:51:java.util.Collection checkNotEmpty(java.util.Collection) -> checkNotEmpty
com.kaolafm.opensdk.player.logic.util.ReportUtil -> com.kaolafm.opensdk.player.logic.util.ReportUtil:
    26:26:void <init>() -> <init>
    33:56:void reportStartPlay(com.kaolafm.opensdk.player.logic.model.PlayItem) -> reportStartPlay
    59:62:void reportSetPlayPosition(com.kaolafm.opensdk.player.logic.model.PlayItem,long,long) -> reportSetPlayPosition
    68:69:void reportEndPlay(java.lang.String) -> reportEndPlay
    75:76:void reportEndPlay(java.lang.String,boolean) -> reportEndPlay
    86:105:void reportBufferingStart(com.kaolafm.opensdk.player.logic.model.PlayItem,boolean) -> reportBufferingStart
    117:146:void reportBufferingEnd(com.kaolafm.opensdk.player.logic.model.PlayItem,boolean,long) -> reportBufferingEnd
    156:196:void reportRequestError(com.kaolafm.opensdk.player.logic.model.PlayItem,int,int,java.lang.String) -> reportRequestError
    195:195:void lambda$reportRequestError$5(com.kaolafm.report.event.RequetErrorReportEvent,java.lang.Throwable) -> lambda$reportRequestError$5
    193:195:void lambda$reportRequestError$4(com.kaolafm.report.event.RequetErrorReportEvent,java.lang.String) -> lambda$reportRequestError$4
    190:190:java.lang.String lambda$reportRequestError$3() -> lambda$reportRequestError$3
    145:145:void lambda$reportBufferingEnd$2(com.kaolafm.report.event.BufferEndReportEvent,java.lang.Throwable) -> lambda$reportBufferingEnd$2
    143:145:void lambda$reportBufferingEnd$1(com.kaolafm.report.event.BufferEndReportEvent,java.lang.String) -> lambda$reportBufferingEnd$1
    140:140:java.lang.String lambda$reportBufferingEnd$0() -> lambda$reportBufferingEnd$0
tv.danmaku.ijk.media.player.AndroidMediaPlayer -> tv.danmaku.ijk.media.player.AndroidMediaPlayer:
    android.media.MediaPlayer mInternalMediaPlayer -> mInternalMediaPlayer
    tv.danmaku.ijk.media.player.AndroidMediaPlayer$AndroidMediaPlayerListenerHolder mInternalListenerAdapter -> mInternalListenerAdapter
    java.lang.String mDataSource -> mDataSource
    java.lang.Object mInitLock -> mInitLock
    boolean mIsReleased -> mIsReleased
    boolean mKeepInBackground -> mKeepInBackground
    tv.danmaku.ijk.media.player.MediaInfo sMediaInfo -> sMediaInfo
    android.view.SurfaceHolder$Callback mSurfaceCallback -> mSurfaceCallback
    39:66:void <init>() -> <init>
    57:64:void setDisplay(android.view.SurfaceHolder) -> setDisplay
    85:86:void setSurface(android.view.Surface) -> setSurface
    91:100:void setDataSource(java.lang.String) -> setDataSource
    104:104:java.lang.String getDataSource() -> getDataSource
    110:110:void prepare() -> prepare
    114:115:void prepareAsync() -> prepareAsync
    119:120:void start() -> start
    124:125:void stop() -> stop
    129:130:void pause() -> pause
    134:135:void setScreenOnWhilePlaying(boolean) -> setScreenOnWhilePlaying
    139:139:int getVideoWidth() -> getVideoWidth
    144:144:int getVideoHeight() -> getVideoHeight
    149:149:int getVideoSarNum() -> getVideoSarNum
    154:154:int getVideoSarDen() -> getVideoSarDen
    160:163:boolean isPlaying() -> isPlaying
    169:170:void seekTo(long) -> seekTo
    175:178:long getCurrentPosition() -> getCurrentPosition
    185:188:long getDuration() -> getDuration
    194:199:void release() -> release
    203:207:void reset() -> reset
    211:212:void setVolume(float,float) -> setVolume
    216:228:tv.danmaku.ijk.media.player.MediaInfo getMediaInfo() -> getMediaInfo
    236:237:void setWakeMode(android.content.Context,int) -> setWakeMode
    241:242:void setAudioStreamType(int) -> setAudioStreamType
    246:247:void setKeepInBackground(boolean) -> setKeepInBackground
    253:263:void attachInternalListeners() -> attachInternalListeners
    34:34:android.media.MediaPlayer access$000(tv.danmaku.ijk.media.player.AndroidMediaPlayer) -> access$000
    34:34:boolean access$100(tv.danmaku.ijk.media.player.AndroidMediaPlayer) -> access$100
tv.danmaku.ijk.media.player.AndroidMediaPlayer$1 -> tv.danmaku.ijk.media.player.AndroidMediaPlayer$1:
    tv.danmaku.ijk.media.player.AndroidMediaPlayer this$0 -> this$0
    66:66:void <init>(tv.danmaku.ijk.media.player.AndroidMediaPlayer) -> <init>
    69:69:void surfaceChanged(android.view.SurfaceHolder,int,int,int) -> surfaceChanged
    72:72:void surfaceCreated(android.view.SurfaceHolder) -> surfaceCreated
    75:80:void surfaceDestroyed(android.view.SurfaceHolder) -> surfaceDestroyed
tv.danmaku.ijk.media.player.AndroidMediaPlayer$AndroidMediaPlayerListenerHolder -> tv.danmaku.ijk.media.player.AndroidMediaPlayer$AndroidMediaPlayerListenerHolder:
    java.lang.ref.WeakReference mWeakMediaPlayer -> mWeakMediaPlayer
    tv.danmaku.ijk.media.player.AndroidMediaPlayer this$0 -> this$0
    273:275:void <init>(tv.danmaku.ijk.media.player.AndroidMediaPlayer,tv.danmaku.ijk.media.player.AndroidMediaPlayer) -> <init>
    279:283:boolean onInfo(android.media.MediaPlayer,int,int) -> onInfo
    288:292:boolean onError(android.media.MediaPlayer,int,int) -> onError
    297:302:void onVideoSizeChanged(android.media.MediaPlayer,int,int) -> onVideoSizeChanged
    306:311:void onSeekComplete(android.media.MediaPlayer) -> onSeekComplete
    315:320:void onBufferingUpdate(android.media.MediaPlayer,int) -> onBufferingUpdate
    324:329:void onCompletion(android.media.MediaPlayer) -> onCompletion
    333:338:void onPrepared(android.media.MediaPlayer) -> onPrepared
tv.danmaku.ijk.media.player.BaseMediaPlayer -> tv.danmaku.ijk.media.player.BaseMediaPlayer:
    boolean mIsLogEnabled -> mIsLogEnabled
    29:29:void <init>() -> <init>
    33:33:boolean isLogEnabled() -> isLogEnabled
    38:39:void setLogEnabled(boolean) -> setLogEnabled
    43:43:boolean isPlayable() -> isPlayable
    48:48:void setAudioStreamType(int) -> setAudioStreamType
    52:52:void setKeepInBackground(boolean) -> setKeepInBackground
    56:56:int getVideoSarNum() -> getVideoSarNum
    61:61:int getVideoSarDen() -> getVideoSarDen
    67:67:void setWakeMode(android.content.Context,int) -> setWakeMode
    73:73:void setSurface(android.view.Surface) -> setSurface
tv.danmaku.ijk.media.player.IMediaPlayer -> tv.danmaku.ijk.media.player.IMediaPlayer:
    int MEDIA_INFO_UNKNOWN -> MEDIA_INFO_UNKNOWN
    int MEDIA_INFO_STARTED_AS_NEXT -> MEDIA_INFO_STARTED_AS_NEXT
    int MEDIA_INFO_VIDEO_RENDERING_START -> MEDIA_INFO_VIDEO_RENDERING_START
    int MEDIA_INFO_VIDEO_TRACK_LAGGING -> MEDIA_INFO_VIDEO_TRACK_LAGGING
    int MEDIA_INFO_BUFFERING_START -> MEDIA_INFO_BUFFERING_START
    int MEDIA_INFO_BUFFERING_END -> MEDIA_INFO_BUFFERING_END
    int MEDIA_INFO_BAD_INTERLEAVING -> MEDIA_INFO_BAD_INTERLEAVING
    int MEDIA_INFO_NOT_SEEKABLE -> MEDIA_INFO_NOT_SEEKABLE
    int MEDIA_INFO_METADATA_UPDATE -> MEDIA_INFO_METADATA_UPDATE
    int MEDIA_INFO_TIMED_TEXT_ERROR -> MEDIA_INFO_TIMED_TEXT_ERROR
    int MEDIA_ERROR_UNKNOWN -> MEDIA_ERROR_UNKNOWN
    int MEDIA_ERROR_SERVER_DIED -> MEDIA_ERROR_SERVER_DIED
    int MEDIA_ERROR_NOT_VALID_FOR_PROGRESSIVE_PLAYBACK -> MEDIA_ERROR_NOT_VALID_FOR_PROGRESSIVE_PLAYBACK
    int MEDIA_ERROR_IO -> MEDIA_ERROR_IO
    int MEDIA_ERROR_MALFORMED -> MEDIA_ERROR_MALFORMED
    int MEDIA_ERROR_UNSUPPORTED -> MEDIA_ERROR_UNSUPPORTED
    int MEDIA_ERROR_TIMED_OUT -> MEDIA_ERROR_TIMED_OUT
    void setDisplay(android.view.SurfaceHolder) -> setDisplay
    void setDataSource(java.lang.String) -> setDataSource
    java.lang.String getDataSource() -> getDataSource
    void prepare() -> prepare
    void prepareAsync() -> prepareAsync
    void start() -> start
    void stop() -> stop
    void pause() -> pause
    void setScreenOnWhilePlaying(boolean) -> setScreenOnWhilePlaying
    int getVideoWidth() -> getVideoWidth
    int getVideoHeight() -> getVideoHeight
    boolean isPlaying() -> isPlaying
    void seekTo(long) -> seekTo
    long getCurrentPosition() -> getCurrentPosition
    long getDuration() -> getDuration
    void release() -> release
    void reset() -> reset
    void setVolume(float,float) -> setVolume
    tv.danmaku.ijk.media.player.MediaInfo getMediaInfo() -> getMediaInfo
    void setLogEnabled(boolean) -> setLogEnabled
    boolean isPlayable() -> isPlayable
    void setOnPreparedListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnPreparedListener) -> setOnPreparedListener
    void setOnCompletionListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnCompletionListener) -> setOnCompletionListener
    void setOnBufferingUpdateListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnBufferingUpdateListener) -> setOnBufferingUpdateListener
    void setOnSeekCompleteListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnSeekCompleteListener) -> setOnSeekCompleteListener
    void setOnVideoSizeChangedListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnVideoSizeChangedListener) -> setOnVideoSizeChangedListener
    void setOnErrorListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnErrorListener) -> setOnErrorListener
    void setOnInfoListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnInfoListener) -> setOnInfoListener
    void setAudioStreamType(int) -> setAudioStreamType
    void setKeepInBackground(boolean) -> setKeepInBackground
    int getVideoSarNum() -> getVideoSarNum
    int getVideoSarDen() -> getVideoSarDen
    void setWakeMode(android.content.Context,int) -> setWakeMode
    void setSurface(android.view.Surface) -> setSurface
tv.danmaku.ijk.media.player.IMediaPlayer$OnBufferingUpdateListener -> tv.danmaku.ijk.media.player.IMediaPlayer$OnBufferingUpdateListener:
    void onBufferingUpdate(tv.danmaku.ijk.media.player.IMediaPlayer,int,int) -> onBufferingUpdate
tv.danmaku.ijk.media.player.IMediaPlayer$OnCompletionListener -> tv.danmaku.ijk.media.player.IMediaPlayer$OnCompletionListener:
    void onCompletion(tv.danmaku.ijk.media.player.IMediaPlayer) -> onCompletion
tv.danmaku.ijk.media.player.IMediaPlayer$OnErrorListener -> tv.danmaku.ijk.media.player.IMediaPlayer$OnErrorListener:
    boolean onError(tv.danmaku.ijk.media.player.IMediaPlayer,int,int) -> onError
tv.danmaku.ijk.media.player.IMediaPlayer$OnInfoListener -> tv.danmaku.ijk.media.player.IMediaPlayer$OnInfoListener:
    boolean onInfo(tv.danmaku.ijk.media.player.IMediaPlayer,int,int) -> onInfo
tv.danmaku.ijk.media.player.IMediaPlayer$OnPreparedListener -> tv.danmaku.ijk.media.player.IMediaPlayer$OnPreparedListener:
    void onPrepared(tv.danmaku.ijk.media.player.IMediaPlayer) -> onPrepared
tv.danmaku.ijk.media.player.IMediaPlayer$OnSeekCompleteListener -> tv.danmaku.ijk.media.player.IMediaPlayer$OnSeekCompleteListener:
    void onSeekComplete(tv.danmaku.ijk.media.player.IMediaPlayer) -> onSeekComplete
tv.danmaku.ijk.media.player.IMediaPlayer$OnVideoSizeChangedListener -> tv.danmaku.ijk.media.player.IMediaPlayer$OnVideoSizeChangedListener:
    void onVideoSizeChanged(tv.danmaku.ijk.media.player.IMediaPlayer,int,int,int,int) -> onVideoSizeChanged
tv.danmaku.ijk.media.player.IjkLibLoader -> tv.danmaku.ijk.media.player.IjkLibLoader:
    void loadLibrary(java.lang.String) -> loadLibrary
tv.danmaku.ijk.media.player.IjkMediaCodecInfo -> tv.danmaku.ijk.media.player.IjkMediaCodecInfo:
    int RANK_MAX -> RANK_MAX
    int RANK_TESTED -> RANK_TESTED
    int RANK_ACCEPTABLE -> RANK_ACCEPTABLE
    int RANK_LAST_CHANCE -> RANK_LAST_CHANCE
    int RANK_SECURE -> RANK_SECURE
    int RANK_SOFTWARE -> RANK_SOFTWARE
    int RANK_NON_STANDARD -> RANK_NON_STANDARD
    int RANK_NO_SENSE -> RANK_NO_SENSE
    android.media.MediaCodecInfo mCodecInfo -> mCodecInfo
    int mRank -> mRank
    java.lang.String mMimeType -> mMimeType
    java.util.Map sKnownCodecList -> sKnownCodecList
    14:25:void <init>() -> <init>
    31:121:java.util.Map getKnownCodecList() -> getKnownCodecList
    127:181:tv.danmaku.ijk.media.player.IjkMediaCodecInfo setupCandidate(android.media.MediaCodecInfo,java.lang.String) -> setupCandidate
    186:207:void dumpProfileLevels(java.lang.String) -> dumpProfileLevels
    210:226:java.lang.String getProfileName(int) -> getProfileName
    231:267:java.lang.String getLevelName(int) -> getLevelName
    15:22:void <clinit>() -> <clinit>
tv.danmaku.ijk.media.player.IjkMediaMeta -> tv.danmaku.ijk.media.player.IjkMediaMeta:
    java.lang.String IJKM_KEY_FORMAT -> IJKM_KEY_FORMAT
    java.lang.String IJKM_KEY_DURATION_US -> IJKM_KEY_DURATION_US
    java.lang.String IJKM_KEY_START_US -> IJKM_KEY_START_US
    java.lang.String IJKM_KEY_BITRATE -> IJKM_KEY_BITRATE
    java.lang.String IJKM_KEY_VIDEO_STREAM -> IJKM_KEY_VIDEO_STREAM
    java.lang.String IJKM_KEY_AUDIO_STREAM -> IJKM_KEY_AUDIO_STREAM
    java.lang.String IJKM_KEY_TYPE -> IJKM_KEY_TYPE
    java.lang.String IJKM_VAL_TYPE__VIDEO -> IJKM_VAL_TYPE__VIDEO
    java.lang.String IJKM_VAL_TYPE__AUDIO -> IJKM_VAL_TYPE__AUDIO
    java.lang.String IJKM_VAL_TYPE__UNKNOWN -> IJKM_VAL_TYPE__UNKNOWN
    java.lang.String IJKM_KEY_CODEC_NAME -> IJKM_KEY_CODEC_NAME
    java.lang.String IJKM_KEY_CODEC_PROFILE -> IJKM_KEY_CODEC_PROFILE
    java.lang.String IJKM_KEY_CODEC_LONG_NAME -> IJKM_KEY_CODEC_LONG_NAME
    java.lang.String IJKM_KEY_WIDTH -> IJKM_KEY_WIDTH
    java.lang.String IJKM_KEY_HEIGHT -> IJKM_KEY_HEIGHT
    java.lang.String IJKM_KEY_FPS_NUM -> IJKM_KEY_FPS_NUM
    java.lang.String IJKM_KEY_FPS_DEN -> IJKM_KEY_FPS_DEN
    java.lang.String IJKM_KEY_TBR_NUM -> IJKM_KEY_TBR_NUM
    java.lang.String IJKM_KEY_TBR_DEN -> IJKM_KEY_TBR_DEN
    java.lang.String IJKM_KEY_SAR_NUM -> IJKM_KEY_SAR_NUM
    java.lang.String IJKM_KEY_SAR_DEN -> IJKM_KEY_SAR_DEN
    java.lang.String IJKM_KEY_SAMPLE_RATE -> IJKM_KEY_SAMPLE_RATE
    java.lang.String IJKM_KEY_CHANNEL_LAYOUT -> IJKM_KEY_CHANNEL_LAYOUT
    java.lang.String IJKM_KEY_STREAMS -> IJKM_KEY_STREAMS
    long AV_CH_FRONT_LEFT -> AV_CH_FRONT_LEFT
    long AV_CH_FRONT_RIGHT -> AV_CH_FRONT_RIGHT
    long AV_CH_FRONT_CENTER -> AV_CH_FRONT_CENTER
    long AV_CH_LOW_FREQUENCY -> AV_CH_LOW_FREQUENCY
    long AV_CH_BACK_LEFT -> AV_CH_BACK_LEFT
    long AV_CH_BACK_RIGHT -> AV_CH_BACK_RIGHT
    long AV_CH_FRONT_LEFT_OF_CENTER -> AV_CH_FRONT_LEFT_OF_CENTER
    long AV_CH_FRONT_RIGHT_OF_CENTER -> AV_CH_FRONT_RIGHT_OF_CENTER
    long AV_CH_BACK_CENTER -> AV_CH_BACK_CENTER
    long AV_CH_SIDE_LEFT -> AV_CH_SIDE_LEFT
    long AV_CH_SIDE_RIGHT -> AV_CH_SIDE_RIGHT
    long AV_CH_TOP_CENTER -> AV_CH_TOP_CENTER
    long AV_CH_TOP_FRONT_LEFT -> AV_CH_TOP_FRONT_LEFT
    long AV_CH_TOP_FRONT_CENTER -> AV_CH_TOP_FRONT_CENTER
    long AV_CH_TOP_FRONT_RIGHT -> AV_CH_TOP_FRONT_RIGHT
    long AV_CH_TOP_BACK_LEFT -> AV_CH_TOP_BACK_LEFT
    long AV_CH_TOP_BACK_CENTER -> AV_CH_TOP_BACK_CENTER
    long AV_CH_TOP_BACK_RIGHT -> AV_CH_TOP_BACK_RIGHT
    long AV_CH_STEREO_LEFT -> AV_CH_STEREO_LEFT
    long AV_CH_STEREO_RIGHT -> AV_CH_STEREO_RIGHT
    long AV_CH_WIDE_LEFT -> AV_CH_WIDE_LEFT
    long AV_CH_WIDE_RIGHT -> AV_CH_WIDE_RIGHT
    long AV_CH_SURROUND_DIRECT_LEFT -> AV_CH_SURROUND_DIRECT_LEFT
    long AV_CH_SURROUND_DIRECT_RIGHT -> AV_CH_SURROUND_DIRECT_RIGHT
    long AV_CH_LOW_FREQUENCY_2 -> AV_CH_LOW_FREQUENCY_2
    long AV_CH_LAYOUT_MONO -> AV_CH_LAYOUT_MONO
    long AV_CH_LAYOUT_STEREO -> AV_CH_LAYOUT_STEREO
    long AV_CH_LAYOUT_2POINT1 -> AV_CH_LAYOUT_2POINT1
    long AV_CH_LAYOUT_2_1 -> AV_CH_LAYOUT_2_1
    long AV_CH_LAYOUT_SURROUND -> AV_CH_LAYOUT_SURROUND
    long AV_CH_LAYOUT_3POINT1 -> AV_CH_LAYOUT_3POINT1
    long AV_CH_LAYOUT_4POINT0 -> AV_CH_LAYOUT_4POINT0
    long AV_CH_LAYOUT_4POINT1 -> AV_CH_LAYOUT_4POINT1
    long AV_CH_LAYOUT_2_2 -> AV_CH_LAYOUT_2_2
    long AV_CH_LAYOUT_QUAD -> AV_CH_LAYOUT_QUAD
    long AV_CH_LAYOUT_5POINT0 -> AV_CH_LAYOUT_5POINT0
    long AV_CH_LAYOUT_5POINT1 -> AV_CH_LAYOUT_5POINT1
    long AV_CH_LAYOUT_5POINT0_BACK -> AV_CH_LAYOUT_5POINT0_BACK
    long AV_CH_LAYOUT_5POINT1_BACK -> AV_CH_LAYOUT_5POINT1_BACK
    long AV_CH_LAYOUT_6POINT0 -> AV_CH_LAYOUT_6POINT0
    long AV_CH_LAYOUT_6POINT0_FRONT -> AV_CH_LAYOUT_6POINT0_FRONT
    long AV_CH_LAYOUT_HEXAGONAL -> AV_CH_LAYOUT_HEXAGONAL
    long AV_CH_LAYOUT_6POINT1 -> AV_CH_LAYOUT_6POINT1
    long AV_CH_LAYOUT_6POINT1_BACK -> AV_CH_LAYOUT_6POINT1_BACK
    long AV_CH_LAYOUT_6POINT1_FRONT -> AV_CH_LAYOUT_6POINT1_FRONT
    long AV_CH_LAYOUT_7POINT0 -> AV_CH_LAYOUT_7POINT0
    long AV_CH_LAYOUT_7POINT0_FRONT -> AV_CH_LAYOUT_7POINT0_FRONT
    long AV_CH_LAYOUT_7POINT1 -> AV_CH_LAYOUT_7POINT1
    long AV_CH_LAYOUT_7POINT1_WIDE -> AV_CH_LAYOUT_7POINT1_WIDE
    long AV_CH_LAYOUT_7POINT1_WIDE_BACK -> AV_CH_LAYOUT_7POINT1_WIDE_BACK
    long AV_CH_LAYOUT_OCTAGONAL -> AV_CH_LAYOUT_OCTAGONAL
    long AV_CH_LAYOUT_STEREO_DOWNMIX -> AV_CH_LAYOUT_STEREO_DOWNMIX
    android.os.Bundle mMediaMeta -> mMediaMeta
    java.lang.String mFormat -> mFormat
    long mDurationUS -> mDurationUS
    long mStartUS -> mStartUS
    long mBitrate -> mBitrate
    java.util.ArrayList mStreams -> mStreams
    tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta mVideoStream -> mVideoStream
    tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta mAudioStream -> mAudioStream
    9:9:void <init>() -> <init>
    120:120:java.lang.String getString(java.lang.String) -> getString
    124:124:int getInt(java.lang.String) -> getInt
    128:135:int getInt(java.lang.String,int) -> getInt
    140:140:long getLong(java.lang.String) -> getLong
    144:151:long getLong(java.lang.String,long) -> getLong
    156:156:java.util.ArrayList getParcelableArrayList(java.lang.String) -> getParcelableArrayList
    160:166:java.lang.String getDurationInline() -> getDurationInline
    170:235:tv.danmaku.ijk.media.player.IjkMediaMeta parse(android.os.Bundle) -> parse
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta -> tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta:
    android.os.Bundle mMeta -> mMeta
    int mIndex -> mIndex
    java.lang.String mType -> mType
    java.lang.String mCodecName -> mCodecName
    java.lang.String mCodecProfile -> mCodecProfile
    java.lang.String mCodecLongName -> mCodecLongName
    long mBitrate -> mBitrate
    int mWidth -> mWidth
    int mHeight -> mHeight
    int mFpsNum -> mFpsNum
    int mFpsDen -> mFpsDen
    int mTbrNum -> mTbrNum
    int mTbrDen -> mTbrDen
    int mSarNum -> mSarNum
    int mSarDen -> mSarDen
    int mSampleRate -> mSampleRate
    long mChannelLayout -> mChannelLayout
    264:266:void <init>(int) -> <init>
    269:269:java.lang.String getString(java.lang.String) -> getString
    273:273:int getInt(java.lang.String) -> getInt
    277:284:int getInt(java.lang.String,int) -> getInt
    289:289:long getLong(java.lang.String) -> getLong
    293:300:long getLong(java.lang.String,long) -> getLong
    305:310:java.lang.String getCodecLongNameInline() -> getCodecLongNameInline
    315:321:java.lang.String getResolutionInline() -> getResolutionInline
    326:329:java.lang.String getFpsInline() -> getFpsInline
    334:339:java.lang.String getBitrateInline() -> getBitrateInline
    344:347:java.lang.String getSampleRateInline() -> getSampleRateInline
    352:360:java.lang.String getChannelLayoutInline() -> getChannelLayoutInline
tv.danmaku.ijk.media.player.IjkMediaPlayer -> tv.danmaku.ijk.media.player.IjkMediaPlayer:
    java.lang.String TAG -> TAG
    tv.danmaku.ijk.media.player.IjkMediaPlayer$ijkPlayerCallBack mijkPlayerCallBack -> mijkPlayerCallBack
    long mNativeMediaPlayer -> mNativeMediaPlayer
    int mNativeSurfaceTexture -> mNativeSurfaceTexture
    int mListenerContext -> mListenerContext
    long mNativeMediaDataSource -> mNativeMediaDataSource
    long mNativeAndroidIO -> mNativeAndroidIO
    tv.danmaku.ijk.media.player.IjkLibLoader sLocalLibLoader -> sLocalLibLoader
    boolean mIsLibLoaded -> mIsLibLoaded
    boolean mIsNativeInitialized -> mIsNativeInitialized
    64:79:void loadLibrariesOnce(tv.danmaku.ijk.media.player.IjkLibLoader) -> loadLibrariesOnce
    84:90:void initNativeOnce() -> initNativeOnce
    93:94:void <init>() -> <init>
    102:104:void <init>(tv.danmaku.ijk.media.player.IjkLibLoader) -> <init>
    108:120:void initPlayer(tv.danmaku.ijk.media.player.IjkLibLoader) -> initPlayer
    123:127:void setDataSource(java.lang.String) -> setDataSource
    130:134:void prepare(int,int) -> prepare
    137:141:void seekAtStart(long) -> seekAtStart
    144:148:void setDuration(long,long) -> setDuration
    151:155:void play() -> play
    158:162:void stop() -> stop
    166:170:void pause() -> pause
    174:178:void seek(long) -> seek
    181:185:void release() -> release
    189:193:void reset() -> reset
    197:200:java.lang.String getDnsAddress() -> getDnsAddress
    204:208:void setMediaVolume(float,float) -> setMediaVolume
    217:221:void setLoudnessNormalization(int) -> setLoudnessNormalization
    224:225:void setAvOption(tv.danmaku.ijk.media.player.option.AvFormatOption) -> setAvOption
    228:229:void setAvFormatOption(java.lang.String,java.lang.String) -> setAvFormatOption
    232:233:void setAvCodecOption(java.lang.String,java.lang.String) -> setAvCodecOption
    236:237:void setSwScaleOption(java.lang.String,java.lang.String) -> setSwScaleOption
    244:245:void setOverlayFormat(int) -> setOverlayFormat
    252:253:void setFrameDrop(int) -> setFrameDrop
    256:257:void setMediaCodecEnabled(boolean) -> setMediaCodecEnabled
    260:261:void setOpenSLESEnabled(boolean) -> setOpenSLESEnabled
    264:265:void setAutoPlayOnPrepared(boolean) -> setAutoPlayOnPrepared
    269:273:void setDnsAddress(java.lang.String[]) -> setDnsAddress
    276:280:void clearDnsAddress() -> clearDnsAddress
    287:291:void clearProxyAddress() -> clearProxyAddress
    299:303:void setProxyAddress(java.lang.String) -> setProxyAddress
    307:307:android.os.Bundle getMediaMeta() -> getMediaMeta
    311:311:java.lang.String getVideoCodecInfo() -> getVideoCodecInfo
    315:315:java.lang.String getAudioCodecInfo() -> getAudioCodecInfo
    319:320:void finalize() -> finalize
    323:324:void setIjkPlayerCallBack(tv.danmaku.ijk.media.player.IjkMediaPlayer$ijkPlayerCallBack) -> setIjkPlayerCallBack
    327:327:tv.danmaku.ijk.media.player.IjkMediaPlayer$ijkPlayerCallBack getIjkPlayerCallBack() -> getIjkPlayerCallBack
    336:336:void postDataFromNative(java.lang.Object,byte[],int) -> postDataFromNative
    341:360:void postEventFromNative(java.lang.Object,int,int,int,java.lang.Object) -> postEventFromNative
    365:365:java.lang.String onSelectCodec(java.lang.Object,java.lang.String,int,int) -> onSelectCodec
    372:372:java.lang.String onControlResolveSegmentUrl(java.lang.Object,int) -> onControlResolveSegmentUrl
    378:378:java.lang.String onControlResolveSegmentOfflineMrl(java.lang.Object,int) -> onControlResolveSegmentOfflineMrl
    383:383:int onControlResolveSegmentDuration(java.lang.Object,int) -> onControlResolveSegmentDuration
    388:388:int onControlResolveSegmentCount(java.lang.Object) -> onControlResolveSegmentCount
    void _clearDnsAddress() -> _clearDnsAddress
    void _setTitileActive(int) -> _setTitileActive
    long _getAudioFormat() -> _getAudioFormat
    long _getAudioSampleRate() -> _getAudioSampleRate
    long _getAudioChannels() -> _getAudioChannels
    void _setDataCallBack(long) -> _setDataCallBack
    void _setVideoSurface(android.view.Surface) -> _setVideoSurface
    void _setLoudnessNormalization(int) -> _setLoudnessNormalization
    java.lang.String _getColorFormatName(int) -> _getColorFormatName
    void native_init() -> native_init
    void native_setup(java.lang.Object) -> native_setup
    void native_finalize() -> native_finalize
    void native_message_loop(java.lang.Object) -> native_message_loop
    void _setDnsAddress(java.lang.String[]) -> _setDnsAddress
    java.lang.String _getDnsAdress() -> _getDnsAdress
    void _clearProxyAdress() -> _clearProxyAdress
    void _setProxyAddress(java.lang.String) -> _setProxyAddress
    void _setDuration(int,int) -> _setDuration
    void _start() -> _start
    void _setDataSource(java.lang.String,java.lang.String[],java.lang.String[]) -> _setDataSource
    void _stop() -> _stop
    void _setAvFormatOption(java.lang.String,java.lang.String) -> _setAvFormatOption
    void _setAvCodecOption(java.lang.String,java.lang.String) -> _setAvCodecOption
    void _setSwScaleOption(java.lang.String,java.lang.String) -> _setSwScaleOption
    void _setOverlayFormat(int) -> _setOverlayFormat
    void _setFrameDrop(int) -> _setFrameDrop
    void _setMediaCodecEnabled(boolean) -> _setMediaCodecEnabled
    void _setOpenSLESEnabled(boolean) -> _setOpenSLESEnabled
    android.os.Bundle _getMediaMeta() -> _getMediaMeta
    java.lang.String _getVideoCodecInfo() -> _getVideoCodecInfo
    java.lang.String _getAudioCodecInfo() -> _getAudioCodecInfo
    void _pause() -> _pause
    void _prepareAsync(int,int) -> _prepareAsync
    void seekTo(long) -> seekTo
    boolean isPlaying() -> isPlaying
    long getCurrentPosition() -> getCurrentPosition
    long getDuration() -> getDuration
    void _release() -> _release
    void _reset() -> _reset
    void setVolume(float,float) -> setVolume
    void _setPlaybackRate(float) -> _setPlaybackRate
    float _getPlaybackRate() -> _getPlaybackRate
    void _setSeekAtStart(long) -> _setSeekAtStart
    void _setAutoPlayOnPrepared(boolean) -> _setAutoPlayOnPrepared
    void _setLogInValid() -> _setLogInValid
    void _setLogReport(int) -> _setLogReport
    void _setLogLevel(int,int) -> _setLogLevel
    571:571:boolean onNativeInvoke(java.lang.Object,int,android.os.Bundle) -> onNativeInvoke
    java.lang.String _getLibVersion() -> _getLibVersion
    113:119:void lambda$initPlayer$2(java.lang.Integer) -> lambda$initPlayer$2
    109:110:java.lang.Integer lambda$initPlayer$1(tv.danmaku.ijk.media.player.IjkLibLoader) -> lambda$initPlayer$1
    59:59:void lambda$static$0(java.lang.String) -> lambda$static$0
    41:81:void <clinit>() -> <clinit>
tv.danmaku.ijk.media.player.IjkMediaPlayer$ijkPlayerCallBack -> tv.danmaku.ijk.media.player.IjkMediaPlayer$ijkPlayerCallBack:
    void message(int,int,int,java.lang.Object) -> message
tv.danmaku.ijk.media.player.IjkMediaPlayerConstants -> tv.danmaku.ijk.media.player.IjkMediaPlayerConstants:
    int MEDIA_INFO_STARTED_AS_NEXT -> MEDIA_INFO_STARTED_AS_NEXT
    int MEDIA_INFO_VIDEO_TRACK_LAGGING -> MEDIA_INFO_VIDEO_TRACK_LAGGING
    int MEDIA_NOP -> MEDIA_NOP
    int MEDIA_PREPARED -> MEDIA_PREPARED
    int MEDIA_PLAYBACK_COMPLETE -> MEDIA_PLAYBACK_COMPLETE
    int MEDIA_BUFFERING_UPDATE -> MEDIA_BUFFERING_UPDATE
    int MEDIA_SEEK_COMPLETE -> MEDIA_SEEK_COMPLETE
    int MEDIA_SET_VIDEO_SIZE -> MEDIA_SET_VIDEO_SIZE
    int MEDIA_PLAYER_PTS_UPDATE -> MEDIA_PLAYER_PTS_UPDATE
    int MEDIA_PLAYER_VOD_PTS_PRELOAD_UPDATE -> MEDIA_PLAYER_VOD_PTS_PRELOAD_UPDATE
    int MEDIA_TIMED_TEXT -> MEDIA_TIMED_TEXT
    int MEDIA_ERROR -> MEDIA_ERROR
    int MEDIA_INFO -> MEDIA_INFO
    int MEDIA_SET_VIDEO_SAR -> MEDIA_SET_VIDEO_SAR
    int MEDIA_ERROR_IJK_PLAYER -> MEDIA_ERROR_IJK_PLAYER
    int MEDIA_ERROR_IJK_PLAYER_ZERO -> MEDIA_ERROR_IJK_PLAYER_ZERO
    int MEDIA_CONNECTION_TIMEOUT_ERROR -> MEDIA_CONNECTION_TIMEOUT_ERROR
    int NO_TS_FILE_ERROR_IJK_PLAYER -> NO_TS_FILE_ERROR_IJK_PLAYER
    int NO_M3U8_FILE_IJK_PLAYER -> NO_M3U8_FILE_IJK_PLAYER
    int NO_ID_SUB_ERROR_IJK_PLAYER -> NO_ID_SUB_ERROR_IJK_PLAYER
    int NO_ID_SUB_ERROR_UNKHNOWN_IJK_PLAYER -> NO_ID_SUB_ERROR_UNKHNOWN_IJK_PLAYER
    int BAD_GATEWAY_SUB_ERROR_IJK_PLAYER -> BAD_GATEWAY_SUB_ERROR_IJK_PLAYER
    int NO_FILE_SUB_ERROR_IJK_PLAYER -> NO_FILE_SUB_ERROR_IJK_PLAYER
    int DOMAIN_SUB_ERROR_IJK_PLAYER -> DOMAIN_SUB_ERROR_IJK_PLAYER
    int NO_UPDATE_SUB_ERROR_IJK_PLAYER -> NO_UPDATE_SUB_ERROR_IJK_PLAYER
    int GET_STREAM_FAILED_SUB_ERROR_IJK_PLAYER -> GET_STREAM_FAILED_SUB_ERROR_IJK_PLAYER
    int SEEK_GET_STREAM_FAILED_SUB_ERROR_IJK_PLAYER -> SEEK_GET_STREAM_FAILED_SUB_ERROR_IJK_PLAYER
    int GET_STREAM_CONNECTION_SUB_ERROR_IJK_PLAYER -> GET_STREAM_CONNECTION_SUB_ERROR_IJK_PLAYER
    int GET_STREAM_CONNECTED_SUB_ERROR_IJK_PLAYER -> GET_STREAM_CONNECTED_SUB_ERROR_IJK_PLAYER
    int STREAM_MUSIC -> STREAM_MUSIC
    int MEDIA_IJK_SO_INIT_SUCCESS -> MEDIA_IJK_SO_INIT_SUCCESS
    int LOG_MOUDLE_IJK_MEDIA -> LOG_MOUDLE_IJK_MEDIA
    int LOG_MOUDLE_IJK_LIBAV -> LOG_MOUDLE_IJK_LIBAV
tv.danmaku.ijk.media.player.MediaInfo -> tv.danmaku.ijk.media.player.MediaInfo:
    java.lang.String mMediaPlayerName -> mMediaPlayerName
    java.lang.String mVideoDecoder -> mVideoDecoder
    java.lang.String mVideoDecoderImpl -> mVideoDecoderImpl
    java.lang.String mAudioDecoder -> mAudioDecoder
    java.lang.String mAudioDecoderImpl -> mAudioDecoderImpl
    tv.danmaku.ijk.media.player.IjkMediaMeta mMeta -> mMeta
    19:19:void <init>() -> <init>
tv.danmaku.ijk.media.player.SimpleMediaPlayer -> tv.danmaku.ijk.media.player.SimpleMediaPlayer:
    tv.danmaku.ijk.media.player.IMediaPlayer$OnPreparedListener mOnPreparedListener -> mOnPreparedListener
    tv.danmaku.ijk.media.player.IMediaPlayer$OnCompletionListener mOnCompletionListener -> mOnCompletionListener
    tv.danmaku.ijk.media.player.IMediaPlayer$OnBufferingUpdateListener mOnBufferingUpdateListener -> mOnBufferingUpdateListener
    tv.danmaku.ijk.media.player.IMediaPlayer$OnSeekCompleteListener mOnSeekCompleteListener -> mOnSeekCompleteListener
    tv.danmaku.ijk.media.player.IMediaPlayer$OnVideoSizeChangedListener mOnVideoSizeChangedListener -> mOnVideoSizeChangedListener
    tv.danmaku.ijk.media.player.IMediaPlayer$OnErrorListener mOnErrorListener -> mOnErrorListener
    tv.danmaku.ijk.media.player.IMediaPlayer$OnInfoListener mOnInfoListener -> mOnInfoListener
    24:24:void <init>() -> <init>
    35:36:void setOnPreparedListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnPreparedListener) -> setOnPreparedListener
    39:40:void setOnCompletionListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnCompletionListener) -> setOnCompletionListener
    44:45:void setOnBufferingUpdateListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnBufferingUpdateListener) -> setOnBufferingUpdateListener
    48:49:void setOnSeekCompleteListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnSeekCompleteListener) -> setOnSeekCompleteListener
    53:54:void setOnVideoSizeChangedListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnVideoSizeChangedListener) -> setOnVideoSizeChangedListener
    57:58:void setOnErrorListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnErrorListener) -> setOnErrorListener
    61:62:void setOnInfoListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnInfoListener) -> setOnInfoListener
    65:72:void resetListeners() -> resetListeners
    75:82:void attachListeners(tv.danmaku.ijk.media.player.IMediaPlayer) -> attachListeners
    85:87:void notifyOnPrepared() -> notifyOnPrepared
    90:92:void notifyOnCompletion() -> notifyOnCompletion
    95:97:void notifyOnBufferingUpdate(int,int) -> notifyOnBufferingUpdate
    100:102:void notifyOnSeekComplete() -> notifyOnSeekComplete
    106:109:void notifyOnVideoSizeChanged(int,int,int,int) -> notifyOnVideoSizeChanged
    112:114:boolean notifyOnError(int,int) -> notifyOnError
    118:120:boolean notifyOnInfo(int,int) -> notifyOnInfo
tv.danmaku.ijk.media.player.annotations.AccessedByNative -> tv.danmaku.ijk.media.player.annotations.AccessedByNative:
tv.danmaku.ijk.media.player.annotations.CalledByNative -> tv.danmaku.ijk.media.player.annotations.CalledByNative:
    java.lang.String value() -> value
tv.danmaku.ijk.media.player.exceptions.IjkMediaException -> tv.danmaku.ijk.media.player.exceptions.IjkMediaException:
    long serialVersionUID -> serialVersionUID
    19:19:void <init>() -> <init>
tv.danmaku.ijk.media.player.ffmpeg.FFmpegApi -> tv.danmaku.ijk.media.player.ffmpeg.FFmpegApi:
    3:3:void <init>() -> <init>
    java.lang.String av_base64_encode(byte[]) -> av_base64_encode
tv.danmaku.ijk.media.player.misc.IAndroidIO -> tv.danmaku.ijk.media.player.misc.IAndroidIO:
    int open(java.lang.String) -> open
    int read(byte[],int) -> read
    long seek(long,int) -> seek
    int close() -> close
tv.danmaku.ijk.media.player.misc.IMediaDataSource -> tv.danmaku.ijk.media.player.misc.IMediaDataSource:
    int readAt(long,byte[],int,int) -> readAt
    long getSize() -> getSize
    void close() -> close
tv.danmaku.ijk.media.player.option.AvFormatOption -> tv.danmaku.ijk.media.player.option.AvFormatOption:
    java.lang.String getName() -> getName
    java.lang.String getValue() -> getValue
tv.danmaku.ijk.media.player.option.AvFourCC -> tv.danmaku.ijk.media.player.option.AvFourCC:
    int SDL_FCC_YV12 -> SDL_FCC_YV12
    int SDL_FCC_RV16 -> SDL_FCC_RV16
    int SDL_FCC_RV32 -> SDL_FCC_RV32
    19:19:void <init>() -> <init>
    20:22:void <clinit>() -> <clinit>
tv.danmaku.ijk.media.player.option.format.AvFormatOption_HttpDetectRangeSupport -> tv.danmaku.ijk.media.player.option.format.AvFormatOption_HttpDetectRangeSupport:
    tv.danmaku.ijk.media.player.option.format.AvFormatOption_HttpDetectRangeSupport Enable -> Enable
    tv.danmaku.ijk.media.player.option.format.AvFormatOption_HttpDetectRangeSupport Disable -> Disable
    java.lang.String mValue -> mValue
    30:32:void <init>(java.lang.String) -> <init>
    35:35:java.lang.String getName() -> getName
    39:39:java.lang.String getValue() -> getValue
    24:26:void <clinit>() -> <clinit>
tv.danmaku.ijk.media.player.pragma.DebugLog -> tv.danmaku.ijk.media.player.pragma.DebugLog:
    boolean ENABLE_ERROR -> ENABLE_ERROR
    boolean ENABLE_INFO -> ENABLE_INFO
    boolean ENABLE_WARN -> ENABLE_WARN
    boolean ENABLE_DEBUG -> ENABLE_DEBUG
    boolean ENABLE_VERBOSE -> ENABLE_VERBOSE
    24:24:void <init>() -> <init>
    33:33:int e(java.lang.String,java.lang.String) -> e
    41:41:int e(java.lang.String,java.lang.String,java.lang.Throwable) -> e
    49:50:int efmt(java.lang.String,java.lang.String,java.lang.Object[]) -> efmt
    58:58:int i(java.lang.String,java.lang.String) -> i
    66:66:int i(java.lang.String,java.lang.String,java.lang.Throwable) -> i
    74:75:int ifmt(java.lang.String,java.lang.String,java.lang.Object[]) -> ifmt
    83:83:int w(java.lang.String,java.lang.String) -> w
    91:91:int w(java.lang.String,java.lang.String,java.lang.Throwable) -> w
    99:100:int wfmt(java.lang.String,java.lang.String,java.lang.Object[]) -> wfmt
    108:108:int d(java.lang.String,java.lang.String) -> d
    116:116:int d(java.lang.String,java.lang.String,java.lang.Throwable) -> d
    124:125:int dfmt(java.lang.String,java.lang.String,java.lang.Object[]) -> dfmt
    133:133:int v(java.lang.String,java.lang.String) -> v
    141:141:int v(java.lang.String,java.lang.String,java.lang.Throwable) -> v
    149:150:int vfmt(java.lang.String,java.lang.String,java.lang.Object[]) -> vfmt
    158:160:void printStackTrace(java.lang.Throwable) -> printStackTrace
    164:170:void printCause(java.lang.Throwable) -> printCause
tv.danmaku.ijk.media.player.pragma.Pragma -> tv.danmaku.ijk.media.player.pragma.Pragma:
    boolean ENABLE_VERBOSE -> ENABLE_VERBOSE
    21:21:void <init>() -> <init>
