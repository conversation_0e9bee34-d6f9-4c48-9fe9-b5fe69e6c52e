package com.kaolafm.opensdk.utils.operation;

import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.operation.model.category.AlbumCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.CategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.FeatureCategoryMember;
import com.kaolafm.opensdk.api.operation.model.column.AlbumDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.FeatureDetailColumnMember;

import javax.inject.Inject;

/**
 * 专辑 分类/栏目成员处理类
 *
 * <AUTHOR>
 * @date 2018/9/21
 */

public class FeatureProcessor implements IOperationProcessor {

    @Inject
    public FeatureProcessor() {
    }

    @Override
    public boolean accept(CategoryMember member) {
        return member instanceof FeatureCategoryMember;
    }

    @Override
    public boolean accept(ColumnMember member) {
        return member instanceof FeatureDetailColumnMember;
    }

    @Override
    public long getId(CategoryMember member) {
        return ((FeatureCategoryMember) member).getFeatureId();
    }

    @Override
    public long getId(ColumnMember member) {
        return ((FeatureDetailColumnMember) member).getFeatureId();
    }

    @Override
    public long getListenNum(CategoryMember member) {
        return ((FeatureCategoryMember) member).getPlayTimes();
    }

    @Override
    public int getType(CategoryMember member) {
        return ResType.TYPE_FEATURE;
    }

    @Override
    public int getType(ColumnMember member) {
        return ResType.TYPE_FEATURE;
    }

    @Override
    public void play(CategoryMember member) {
//        PlayerManager.getInstance(OpenSDK.getInstance().getContext()).playAlbum(getId(member));
    }

    @Override
    public void play(ColumnMember member) {
//        PlayerManager.getInstance(OpenSDK.getInstance().getContext()).playAlbum(getId(member));
    }
}
