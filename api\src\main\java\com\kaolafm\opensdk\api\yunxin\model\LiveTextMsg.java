package com.kaolafm.opensdk.api.yunxin.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 如果msgType = 1 or msgType = 2，msgInfo字段如下
 */
public class LiveTextMsg implements Parcelable {

    // 消息唯一id
    private String id;
    // 发送时间,时间戳
    private Long sendTime;
    // 1文本。2语音
    private String msgContentType;
    // 文本消息内容
    private String msgText;
    // 语音消息内容
    private String msgVoice;
    // 设备号
    private String deviceId;
    // 发送方用户账号
    private String senderAccount;
    // 发送方用户头像
    private String senderAvatar;
    // 发送方用户昵称
    private String senderNickName;

    protected LiveTextMsg(Parcel in) {
        id = in.readString();
        if (in.readByte() == 0) {
            sendTime = null;
        } else {
            sendTime = in.readLong();
        }
        msgContentType = in.readString();
        msgText = in.readString();
        msgVoice = in.readString();
        deviceId = in.readString();
        senderAccount = in.readString();
        senderAvatar = in.readString();
        senderNickName = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(id);
        if (sendTime == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeLong(sendTime);
        }
        dest.writeString(msgContentType);
        dest.writeString(msgText);
        dest.writeString(msgVoice);
        dest.writeString(deviceId);
        dest.writeString(senderAccount);
        dest.writeString(senderAvatar);
        dest.writeString(senderNickName);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<LiveTextMsg> CREATOR = new Creator<LiveTextMsg>() {
        @Override
        public LiveTextMsg createFromParcel(Parcel in) {
            return new LiveTextMsg(in);
        }

        @Override
        public LiveTextMsg[] newArray(int size) {
            return new LiveTextMsg[size];
        }
    };

    public String getId() {
        return id == null ? "" : id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getSendTime() {
        return sendTime;
    }

    public void setSendTime(Long sendTime) {
        this.sendTime = sendTime;
    }

    public String getMsgContentType() {
        return msgContentType == null ? "" : msgContentType;
    }

    public void setMsgContentType(String msgContentType) {
        this.msgContentType = msgContentType;
    }

    public String getMsgText() {
        return msgText == null ? "" : msgText;
    }

    public void setMsgText(String msgText) {
        this.msgText = msgText;
    }

    public String getMsgVoice() {
        return msgVoice == null ? "" : msgVoice;
    }

    public void setMsgVoice(String msgVoice) {
        this.msgVoice = msgVoice;
    }

    public String getDeviceId() {
        return deviceId == null ? "" : deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getSenderAccount() {
        return senderAccount == null ? "" : senderAccount;
    }

    public void setSenderAccount(String senderAccount) {
        this.senderAccount = senderAccount;
    }

    public String getSenderAvatar() {
        return senderAvatar == null ? "" : senderAvatar;
    }

    public void setSenderAvatar(String senderAvatar) {
        this.senderAvatar = senderAvatar;
    }

    public String getSenderNickName() {
        return senderNickName == null ? "" : senderNickName;
    }

    public void setSenderNickName(String senderNickName) {
        this.senderNickName = senderNickName;
    }

    @Override
    public String toString() {
        return "LiveTextMsg{" +
                "id='" + id + '\'' +
                ", sendTime=" + sendTime +
                ", msgContentType='" + msgContentType + '\'' +
                ", msgText='" + msgText + '\'' +
                ", msgVoice='" + msgVoice + '\'' +
                ", deviceId='" + deviceId + '\'' +
                ", senderAccount='" + senderAccount + '\'' +
                ", senderAvatar='" + senderAvatar + '\'' +
                ", senderNickName='" + senderNickName + '\'' +
                '}';
    }
}
