package com.kaolafm.opensdk.api.emergency;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.emergency.model.EmergencyBroadcast;

import java.util.HashMap;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.QueryMap;

interface EmergencyService {

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_EMERGENCY_MESSAGE)
    Single<BaseResult<EmergencyBroadcast>> getEmergencyMessage();

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_EMERGENCY_POLLING)
    Single<BaseResult<EmergencyBroadcast>> getEmergencyPolling(@QueryMap HashMap<String, String> params);
}
