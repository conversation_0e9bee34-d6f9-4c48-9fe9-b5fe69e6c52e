package com.kaolafm.opensdk.demo;

import android.content.pm.ActivityInfo;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.trello.rxlifecycle3.LifecycleProvider;
import com.trello.rxlifecycle3.LifecycleTransformer;
import com.trello.rxlifecycle3.RxLifecycle;
import com.trello.rxlifecycle3.android.ActivityEvent;
import com.trello.rxlifecycle3.android.RxLifecycleAndroid;

import butterknife.ButterKnife;
import butterknife.Unbinder;
import io.reactivex.Observable;
import io.reactivex.subjects.BehaviorSubject;

/**
 * <AUTHOR>
 * @date 2018/7/26
 */

public abstract class BaseActivity extends AppCompatActivity implements IActivity, LifecycleProvider<ActivityEvent> {

    public ImageView mBackView;
    private TextView mTitleView;
    private Unbinder mUnbinder;
    private final BehaviorSubject<ActivityEvent> mLifecycleSubject = BehaviorSubject.create();

    private IPlayerInitCompleteListener iPlayerInitCompleteListener = new IPlayerInitCompleteListener() {
        @Override
        public void onPlayerInitComplete(boolean b) {
            showToast("播放器初始化结果:"+b);
        }
    };


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        super.setContentView(R.layout.base_layout);
        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);

        PlayerManager.getInstance().addPlayerInitComplete(iPlayerInitCompleteListener);

        int layoutId = getLayoutId();
        if (layoutId != 0) {
            setContentView(layoutId);
            mUnbinder = ButterKnife.bind(this);
        }
        mTitleView = findViewById(R.id.tv_title);
        mBackView = findViewById(R.id.iv_back);
        mBackView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        initArgs();
        initView(savedInstanceState);
        initData();
    }

    @Override
    public void setContentView(int layoutResID) {
        setContentView(View.inflate(this, layoutResID, null));
    }

    @Override
    public void setContentView(View view) {
        LinearLayout rootLayout = findViewById(R.id.root_layout);
        rootLayout.addView(view, new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
    }

    public void setTitle(CharSequence charSequence){
        if(mTitleView == null){
            return;
        }
        mTitleView.setText(charSequence);
    }

    @Override
    public void initArgs() {

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        PlayerManager.getInstance().removePlayerInitComplete(iPlayerInitCompleteListener);
        if (mUnbinder != null) {
            mUnbinder.unbind();
        }
        mUnbinder = null;
    }

    @Override
    public void showLoading() {

    }

    @Override
    public void hideLoading() {

    }

    public void showToast(String msg) {
        Toast.makeText(this, msg, Toast.LENGTH_SHORT).show();
    }
    public void showError(String tag, ApiException e) {
        showToast(tag+", 错误码="+e.getCode()+", 错误信息="+e.getMessage());

    }

    @Override
    public Observable<ActivityEvent> lifecycle() {
        return mLifecycleSubject;
    }

    @Override
    public <T> LifecycleTransformer<T> bindUntilEvent(ActivityEvent event) {
        return RxLifecycle.bindUntilEvent(mLifecycleSubject, event);
    }

    @Override
    public <T> LifecycleTransformer<T> bindToLifecycle() {
        return RxLifecycleAndroid.bindActivity(mLifecycleSubject);
    }
}
