package com.kaolafm.opensdk.player.logic.playcontrol;

import com.kaolafm.opensdk.api.media.AudioRequest;
import com.kaolafm.opensdk.api.media.model.AudioFileInfo;
import com.kaolafm.opensdk.api.media.model.AudioPlayInfo;
import com.kaolafm.opensdk.api.media.model.ToneQuality;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.core.ijk.VideoView;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import java.util.List;

/**
 * 专辑播放控制
 *
 * <AUTHOR> shi qian
 */
public class AlbumPlayControl extends BasePlayControl {

    @Override
    public void start(int type, PlayItem playItem, VideoView videoView, boolean isPlayNow) {
        super.start(type, playItem, videoView, isPlayNow);
    }

    @Override
    ToneQuality getToneQuality(PlayItem playItem, List<AudioFileInfo> playListUrlInfos) {
        return null;
    }

    @Override
    String getPlayItemResourceType(PlayItem playItem) {
        return "mp3";
    }

    @Override
    void requestPlayUrl(PlayItem playItem, OnGetPlayUrlData callback) {
        new AudioRequest().getAudioPlayInfo(playItem.getPlayUrlId(), new HttpCallback<AudioPlayInfo>() {
            @Override
            public void onSuccess(AudioPlayInfo audioPlayInfo) {
                List<AudioFileInfo> playList = audioPlayInfo.getPlayInfoList();
                if (playList == null) {
                    onError(new ApiException(PlayerConstants.ERROR_CODE_PLAY_LIST_URL_NULL, "playList is null"));
                    return;
                }
                //设置并回调播放地址
                setPlayUrl(playItem, playList);
                callback.onDataGet(playItem.getPlayUrl());
            }

            @Override
            public void onError(ApiException e) {
                stop();
                if (e.getCode() == PlayerConstants.ERROR_CODE_NO_COPYRIGHT) {
                    // code - 50816, message - 因版权原因，暂时无法播放
                    if (mBasePlayControlListener != null) {
                        mBasePlayControlListener.onPlayerFailed(playItem, PlayerConstants.ERROR_CODE_NO_COPYRIGHT, e.getCode());
                    }
                    return;
                }
                if (e.getCode() == PlayerConstants.ERROR_CODE_PLAY_LIST_URL_NULL) {
                    if (mBasePlayControlListener != null) {
                        mBasePlayControlListener.onPlayerFailed(playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_URL_NULL, 404);
                    }
                    return;
                }
                if (mBasePlayControlListener != null) {
                    mBasePlayControlListener.onPlayerFailed(playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_URL_DECODE, e.getCode());
                }
            }
        });
    }
}
