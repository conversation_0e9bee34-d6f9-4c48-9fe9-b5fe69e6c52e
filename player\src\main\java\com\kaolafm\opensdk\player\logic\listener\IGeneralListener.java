package com.kaolafm.opensdk.player.logic.listener;

import com.kaolafm.opensdk.player.logic.model.item.InvalidPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;

/**
 * @ClassName IGeneralListener
 * @Description 通用回调
 * <AUTHOR>
 * @Date 2020-02-18 09:15
 * @Version 1.0
 */
public interface IGeneralListener {
    void getPlayListError(PlayItem playItem, int errorCode, int errorExtra);
    void playUrlError(int code);
    default void playerInitError(int code){
        PlayerLogUtil.log("IGeneralListener", "Player init failed with code: " + code);
    }
}
