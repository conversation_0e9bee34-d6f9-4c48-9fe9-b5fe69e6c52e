package com.kaolafm.opensdk.api.common;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.common.model.AboutInfoBean;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;

/**
 * 一些公共的请求
 */
public interface CommonService {
    /**
     * 关于我们
     * @return
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_ABOUT_INFO)
    Single<BaseResult<AboutInfoBean>> getAboutInfo();
}
