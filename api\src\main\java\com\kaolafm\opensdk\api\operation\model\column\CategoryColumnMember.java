package com.kaolafm.opensdk.api.operation.model.column;

import com.kaolafm.opensdk.ResType;

/**
 * 栏目成员：分类
 */
public class CategoryColumnMember extends ColumnContent {

    /** 分类的code值*/
    private String categoryCode;

    /** 内容类型。1:专辑，2:在线广播，3:直播，4:AI电台，5:QQ音乐电台*/
    private int contentType;

    public String getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    public int getContentType() {
        return contentType;
    }

    public void setContentType(int contentType) {
        this.contentType = contentType;
    }

    /**
     * 获取ResType对应的类型
     */
    public int getContenResType() {
        switch (contentType) {
            case 0:
                return ResType.TYPE_ALL;
            case 1:
                return ResType.TYPE_ALBUM;
            case 2:
                return ResType.TYPE_BROADCAST;
            case 3:
                return ResType.TYPE_LIVE;
            case 4:
                return ResType.TYPE_RADIO;
            case 5:
                return ResType.TYPE_QQ_MUSIC;
            case 6:
                return ResType.TYPE_NEWS;
            case 7:
                return ResType.TYPE_TV;
            case 8:
                return ResType.TYPE_FEATURE;
            default:
        }
        return contentType;
    }


    @Override
    public String toString() {
        return "CategoryColumnMember{" +
                "ids='" + categoryCode + '\'' +
                ", contentType=" + contentType +
                "} " + super.toString();
    }
}
