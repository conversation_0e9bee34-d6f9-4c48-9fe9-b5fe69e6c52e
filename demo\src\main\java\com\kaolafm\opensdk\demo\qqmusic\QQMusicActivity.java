//package com.kaolafm.opensdk.demo.qqmusic;
//
//import android.content.Intent;
//import android.content.res.TypedArray;
//import android.os.Bundle;
//import android.support.v7.app.AlertDialog.Builder;
//import android.support.v7.widget.DividerItemDecoration;
//import android.support.v7.widget.LinearLayoutManager;
//import android.support.v7.widget.RecyclerView;
//import android.text.Editable;
//import android.text.TextUtils;
//import android.view.ViewGroup.LayoutParams;
//import android.widget.EditText;
//import butterknife.BindView;
//import com.kaolafm.opensdk.ResType;
//import com.kaolafm.opensdk.account.token.AccessTokenManager;
//import com.kaolafm.opensdk.demo.BaseActivity;
//import com.kaolafm.opensdk.demo.CategoryAdapter;
//import com.kaolafm.opensdk.demo.FunctionItem;
//import com.kaolafm.opensdk.demo.R;
//import com.kaolafm.opensdk.demo.detail.DetailActivity;
//import com.kaolafm.opensdk.demo.player.BasePlayerActivity;
//import com.kaolafm.opensdk.demo.player.QQMusicPlayerActivity;
//import java.util.ArrayList;
//
///**
// * <AUTHOR> Yan
// * @date 2018/11/19
// */
//
//public class QQMusicActivity extends BaseActivity {
//
//    @BindView(R.id.rv_qqmusic_function)
//    RecyclerView mRvQqmusicFunction;
//
//    private CategoryAdapter mAdapter;
//
//    @Override
//    public int getLayoutId() {
//        return R.layout.activity_qqmusic;
//    }
//
//    @Override
//    public void initView(Bundle savedInstanceState) {
//        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
//        mRvQqmusicFunction.setLayoutManager(linearLayoutManager);
//        mAdapter = new CategoryAdapter();
//        mAdapter.setOnItemClickListener((view, viewType, s, position) -> {
//            if (viewType == FunctionItem.TYPE_CHILD) {
//                startPage(position);
//            }
//        });
//        mRvQqmusicFunction.setAdapter(mAdapter);
//        mRvQqmusicFunction.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));
//        setTitle("所有功能列表");
//    }
//
//    @Override
//    public void initData() {
//        String[] functions = getResources().getStringArray(R.array.qqmusic_functions);
//        TypedArray ta = getResources().obtainTypedArray(R.array.qqmusic_child_index);
//        ArrayList<FunctionItem> functionList = new ArrayList<>();
//        for (int i = 0; i < functions.length; i++){
//            FunctionItem functionItem = new FunctionItem(functions[i], FunctionItem.TYPE_TITLE, 0);
//            functionList.add(functionItem);
//            int resourceId = ta.getResourceId(i, 0);
//            String[] funcs = getResources().getStringArray(resourceId);
//            for (String func : funcs) {
//                functionList.add(new FunctionItem(func, FunctionItem.TYPE_CHILD, 1));
//            }
//        }
//        mAdapter.setDataList(functionList);
//        ta.recycle();
//    }
//
//    private void startPage(int position) {
//        if (!AccessTokenManager.getInstance().getQQMusicAccessToken().isLogin()) {
//            showToast("请先登录QQ音乐");
//            return;
//        }
//        Intent intent = new Intent(QQMusicActivity.this, QQMusicPlayerActivity.class);
//        switch (position){
//            //私人FM
//            case 1:
//                intent.putExtra(BasePlayerActivity.KEY_TYPE, ResType.MUSIC_MINE_PRIVATE_FM);
//                startActivity(intent);
//                break;
//            //我喜欢的
//            case 2:
//                intent.putExtra(BasePlayerActivity.KEY_TYPE, ResType.MUSIC_MINE_LIKE);
//                startActivity(intent);
//                break;
//            //每日30首
//            case 3:
//                intent.putExtra(BasePlayerActivity.KEY_TYPE, ResType.MUSIC_MINE_DAY);
//                startActivity(intent);
//                break;
//            //我的收藏
//            case 4:
//                startActivity(new Intent(QQMusicActivity.this, CollectionActivity.class));
//                break;
//            //场景电台
//            case 6:
//                showDetail(ResType.TYPE_MUSIC_RADIO_SCENE, 136);
//                break;
//            //标签电台
//            case 7:
//                showDetail(ResType.TYPE_MUSIC_RADIO_LABEL, 13);
//                break;
//            //排行榜
//            case 8:
//                break;
//            default:
//                break;
//        }
//    }
//
//    private void showDetail(int type, long defaultId) {
//        EditText editText = new EditText(this);
//        editText.setLayoutParams(new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT));
//        editText.setHint("请输入id，不输入则使用默认Id");
//        new Builder(this)
//                .setView(editText)
//                .setPositiveButton("确定", (dialog, which) -> {
//                    Editable text = editText.getText();
//                    String id = null;
//                    long playId;
//                    if (text != null) {
//                        id = text.toString().trim();
//                    }
//                    if (!TextUtils.isEmpty(id)) {
//                        playId = Long.valueOf(id);
//                    } else {
//                        playId = defaultId;
//                    }
//                    Intent intent = new Intent();
//                    intent.setClass(QQMusicActivity.this, QQMusicPlayerActivity.class);
//                    intent.putExtra(DetailActivity.KEY_ID, playId);
//                    intent.putExtra(DetailActivity.KEY_TYPE, type);
//                    startActivity(intent);
//                })
//                .setNegativeButton("取消", (dialog, which) -> dialog.dismiss())
//                .create().show();
//    }
//
//}
