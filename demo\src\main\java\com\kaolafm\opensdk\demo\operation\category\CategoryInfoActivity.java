package com.kaolafm.opensdk.demo.operation.category;

import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.util.Log;
import android.view.ViewGroup.LayoutParams;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.operation.model.ImageFile;
import com.kaolafm.opensdk.api.operation.model.category.AlbumCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.BroadcastCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.Category;
import com.kaolafm.opensdk.api.operation.model.category.CategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.LiveProgramCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.RadioCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.RadioQQMusicCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.TVCategoryMember;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.demo.player.AlbumPlayerActivity;
import com.kaolafm.opensdk.demo.player.BasePlayerActivity;
import com.kaolafm.opensdk.demo.player.BroadcastPlayerActivity;
import com.kaolafm.opensdk.demo.player.RadioPlayerActivity;
import com.kaolafm.opensdk.demo.player.TVPlayerActivity;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.utils.operation.OperationAssister;

import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import butterknife.BindView;

/**
 * 分类信息页面
 *
 * <AUTHOR> Yan
 * @date 2018/8/7
 */

public class CategoryInfoActivity extends BaseActivity {
    public static final String KEY_CATEGORY_MEMBER = "CategoryMember";

    @BindView(R.id.ll_category_info_root)
    LinearLayout mLlCategoryInfoRoot;

    @BindView(R.id.tv_category_info)
    TextView mTvCategoryInfo;

    private Category mCategory;

    private CategoryMember mCategoryMember;

    @Override
    public int getLayoutId() {
        return R.layout.activity_category_info;
    }

    @Override
    public void initArgs() {
        Intent intent = getIntent();
        if (intent != null) {
            mCategory = (Category) intent.getSerializableExtra("Category");
            mCategoryMember = (CategoryMember) intent.getSerializableExtra("CategoryMember");
        }
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        StringBuilder sb = new StringBuilder();
        String code = "";
        String id = "";
        String name = "";
        String description = "";
        String contentType = "";
        String extra = "";
        int fine = 0;
        int vip = 0;
        Map<String, ImageFile> imageFiles = null;
        if (mCategory != null) {
            setTitle("分类详情");
            code = mCategory.getCode();
            name = mCategory.getName();
            description = mCategory.getDescription();
            contentType = getContentType(mCategory.getContentType());
            extra = mCategory.getExtInfo().toString();
            imageFiles = mCategory.getImageFiles();
        } else if (mCategoryMember != null) {
            setTitle("分类成员详情");
            code = mCategoryMember.getCode();
            id = String.valueOf(OperationAssister.getId(mCategoryMember));
            name = mCategoryMember.getTitle();
            description = mCategoryMember.getDescription();
            contentType = getContentType(mCategoryMember);
            Map<String, String> extInfo = mCategoryMember.getExtInfo();
            extra = extInfo == null ? "" : extInfo.toString();
            imageFiles = mCategoryMember.getImageFiles();
            if (mCategoryMember instanceof AlbumCategoryMember) {
                AlbumCategoryMember albumCategoryMember = (AlbumCategoryMember) mCategoryMember;
                vip = albumCategoryMember.getVip();
                fine = albumCategoryMember.getFine();
            }

        }
        sb.append("ID：").append(id).append("\r\n")
//                .append("类型：").append(type).append("\r\n")
                .append("名称：").append(name).append("\r\n")
                .append("描述：").append(description).append("\r\n")
                .append("内容类型：").append(contentType).append("\r\n");
        sb.append("额外信息：").append(extra).append("\r\n");
        sb.append("会员：").append(vip == 1 ? "是" : "否").append("\r\n");
        sb.append("精品：").append(fine == 1 ? "是" : "否").append("\r\n");
        mTvCategoryInfo.setText(sb);

        if (imageFiles != null && !imageFiles.isEmpty()) {
            sb.delete(0, sb.length());
            Set<Entry<String, ImageFile>> entries = imageFiles.entrySet();
            for (Entry<String, ImageFile> entry : entries) {
                sb.append("图片类型：")
                        .append(entry.getKey()).append("\r\n");
                ImageFile imageFile = entry.getValue();
                sb.append("图片大小：").append(String.valueOf(imageFile.getWidth())).append("x")
                        .append(String.valueOf(imageFile.getHeight()))
                        .append("\r\n");
                TextView imageText = new TextView(this);
                LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(LayoutParams.WRAP_CONTENT,
                        LayoutParams.WRAP_CONTENT);
                imageText.setLayoutParams(layoutParams);
                imageText.setText(sb);
                mLlCategoryInfoRoot.addView(imageText);
                Glide.with(this).load(imageFile.getUrl()).into(new SimpleTarget<Drawable>() {
                    @Override
                    public void onResourceReady(@NonNull Drawable resource,
                                                @Nullable Transition<? super Drawable> transition) {
                        imageText.setCompoundDrawablesWithIntrinsicBounds(resource, null, null, null);
                    }
                });

            }
        }
        mTvCategoryInfo.setOnClickListener(v -> {
            Log.e("CategoryInfoActivity", "onClick: " + mCategoryMember);
            if (mCategoryMember != null) {
                if (mCategoryMember instanceof RadioQQMusicCategoryMember) {
//                    Integer radioQQMusicType = ((RadioQQMusicCategoryMember) mCategoryMember).getRadioQQMusicType();
//                    if (radioQQMusicType == 0) {
//                        MusicPlayerManager.getInstance()
//                                .playSceneRadio(((RadioQQMusicCategoryMember) mCategoryMember).getOldId(), "");
//                    } else if (radioQQMusicType == 1) {
//                        MusicPlayerManager.getInstance()
//                                .playLabelRadio(((RadioQQMusicCategoryMember) mCategoryMember).getOldId(), "");
//                    }
                } else if (mCategoryMember instanceof RadioCategoryMember) {
                    Intent intent = new Intent(CategoryInfoActivity.this, RadioPlayerActivity.class);
                    intent.putExtra(BasePlayerActivity.KEY_ID, String.valueOf(((RadioCategoryMember) mCategoryMember).getRadioId()));
                    intent.putExtra(BasePlayerActivity.KEY_TYPE, ResType.TYPE_RADIO);
                    startActivity(intent);
                } else if (mCategoryMember instanceof AlbumCategoryMember) {
                    Intent intent = new Intent(CategoryInfoActivity.this, AlbumPlayerActivity.class);
                    intent.putExtra(BasePlayerActivity.KEY_ID, String.valueOf(((AlbumCategoryMember) mCategoryMember).getAlbumId()));
                    intent.putExtra(BasePlayerActivity.KEY_TYPE, ResType.TYPE_ALBUM);
                    startActivity(intent);
                } else if (mCategoryMember instanceof BroadcastCategoryMember) {
                    Intent intent = new Intent(CategoryInfoActivity.this, BroadcastPlayerActivity.class);
                    intent.putExtra(BasePlayerActivity.KEY_ID, String.valueOf(((BroadcastCategoryMember) mCategoryMember).getBroadcastId()));
                    intent.putExtra(BasePlayerActivity.KEY_TYPE, ResType.TYPE_BROADCAST);
                    startActivity(intent);
                } else if (mCategoryMember instanceof TVCategoryMember) {
                    Intent intent = new Intent(CategoryInfoActivity.this, TVPlayerActivity.class);
                    intent.putExtra(BasePlayerActivity.KEY_ID, String.valueOf(((TVCategoryMember) mCategoryMember).getListenTVid()));
                    intent.putExtra(BasePlayerActivity.KEY_TYPE, ResType.TYPE_TV);
                    startActivity(intent);
                }
            }
        });
//        PlayerManager.getInstance().setCanUseDefaultAudioFocusLogic(true);

        PlayerManager.getInstance().addAudioFocusListener(new OnAudioFocusChangeInter() {
            @Override
            public void onAudioFocusChange(int i) {
                Log.e("CategoryInfoActivity", "onAudioFocusChange: " + i);
            }
        });
    }

    private String getContentType(CategoryMember member) {
        if (member instanceof AlbumCategoryMember) {
            return "专辑";
        } else if (member instanceof BroadcastCategoryMember) {
            return "在线广播";
        } else if (member instanceof LiveProgramCategoryMember) {
            return "直播";
        } else if (member instanceof RadioCategoryMember) {
            return "电台";
        } else if (member instanceof RadioQQMusicCategoryMember) {
            return "QQ音乐电台";
        } else if (member instanceof TVCategoryMember) {
            return "听电视";
        }
        return null;
    }

    /**
     * 分类的内容类型。0：综合1:专辑，2:在线广播，3:直播，4:AI电台，5:QQ音乐电台 6：新闻 7：听电视
     *
     * @param contentType
     * @return
     */
    private String getContentType(int contentType) {
        switch (contentType) {
            case 1:
                return "专辑";
            case 2:
                return "在线广播";
            case 3:
                return "直播";
            case 4:
                return "AI电台";
            case 5:
                return "QQ音乐电台";
            case 6:
                return "新闻";
            case 7:
                return "听电视";
            default:
        }
        return null;
    }

    @Override
    public void initData() {
    }

}
