package com.kaolafm.opensdk.http.download;

import java.io.File;

/**
 * <AUTHOR>
 * @date 2020-02-10
 */
public abstract class DownloadRequest {
    public String url;
    public String fileName;
    public String filePath;

    private File mSaveFile;

    private File mShadowFile;

    private File mTmpFile;

    public String tag() {
        return url;
    }

    public File getFile() {
        if (mSaveFile == null) {
            mSaveFile = new File(filePath, fileName);
        }
        return mSaveFile;
    }

    public File getShadow() {
        if (mShadowFile == null) {
            mShadowFile = new File(filePath, fileName + ".download");
        }
        return mShadowFile;
    }

    public File getTmp() {
        if (mTmpFile == null) {
            mTmpFile = new File(filePath, fileName + ".tmp");
        }
        return mTmpFile;
    }

    public File getDir() {
        return new File(filePath);
    }
}
