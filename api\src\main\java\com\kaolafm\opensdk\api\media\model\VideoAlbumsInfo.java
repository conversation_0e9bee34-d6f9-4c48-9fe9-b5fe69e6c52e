package com.kaolafm.opensdk.api.media.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

public class VideoAlbumsInfo implements Parcelable {

    private List<VideoAlbumDetails> localAlbums;

    protected VideoAlbumsInfo(Parcel in) {
        localAlbums = in.createTypedArrayList(VideoAlbumDetails.CREATOR);
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeTypedList(localAlbums);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<VideoAlbumsInfo> CREATOR = new Creator<VideoAlbumsInfo>() {
        @Override
        public VideoAlbumsInfo createFromParcel(Parcel in) {
            return new VideoAlbumsInfo(in);
        }

        @Override
        public VideoAlbumsInfo[] newArray(int size) {
            return new VideoAlbumsInfo[size];
        }
    };

    public List<VideoAlbumDetails> getLocalAlbums() {
        return localAlbums;
    }

    public void setLocalAlbums(List<VideoAlbumDetails> localAlbums) {
        this.localAlbums = localAlbums;
    }

    @Override
    public String toString() {
        return "VideoAlbumDetails{" +
                "albums=" + localAlbums +
                '}';
    }
}
