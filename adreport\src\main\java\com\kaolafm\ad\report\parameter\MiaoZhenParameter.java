package com.kaolafm.ad.report.parameter;

public class MiaoZhenParameter {
    /**
     * 操作系统
     * 0表示Android，1表示iOS，2表示Windows Phone，3表示其他
     */
    private String mo;
    /**
     * ip地址
     */
    private String ns;
    /**
     * IMEI 15位IMEI取MD5摘要
     */
    private String m2;
    /**
     * androidID AndroidID取MD5摘要
     */
    private String m1a;
    /**
     * AndroidID原始值
     */
    private String m1;
    /**
     * MAC地址 保留分隔符”:”的大写MAC地址取MD5摘要
     */
    private String m6;
    /**
     * MAC地址 去除分隔符”:”的大写MAC地址取MD5摘要
     */
    private String m6a;
    /**
     * APP名称
     */
    private String nn;


    public String getMo() {
        return mo;
    }

    public String getNs() {
        return ns;
    }

    public String getM2() {
        return m2;
    }

    public String getM1a() {
        return m1a;
    }

    public String getM1() {
        return m1;
    }

    public String getM6() {
        return m6;
    }

    public String getM6a() {
        return m6a;
    }

    public String getNn() {
        return nn;
    }

    public void setMo(String mo) {
        this.mo = mo;
    }

    public void setNs(String ns) {
        this.ns = ns;
    }

    public void setM2(String m2) {
        this.m2 = m2;
    }

    public void setM1a(String m1a) {
        this.m1a = m1a;
    }

    public void setM1(String m1) {
        this.m1 = m1;
    }

    public void setM6(String m6) {
        this.m6 = m6;
    }

    public void setM6a(String m6a) {
        this.m6a = m6a;
    }

    public void setNn(String nn) {
        this.nn = nn;
    }

    @Override
    public String toString() {
        return "MiaoZhenParameter{" +
                "mo='" + mo + '\'' +
                ", ns='" + ns + '\'' +
                ", m2='" + m2 + '\'' +
                ", m1a='" + m1a + '\'' +
                ", m1='" + m1 + '\'' +
                ", m6='" + m6 + '\'' +
                ", m6a='" + m6a + '\'' +
                ", nn='" + nn + '\'' +
                '}';
    }
}
