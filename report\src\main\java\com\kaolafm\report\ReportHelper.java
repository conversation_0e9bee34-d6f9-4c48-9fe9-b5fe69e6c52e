package com.kaolafm.report;

import android.content.Context;

import com.google.gson.Gson;
import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.report.api.report.ReportSwitchOption;
import com.kaolafm.report.api.report.ReportSwitchRequest;
import com.kaolafm.report.database.ConfigData;
import com.kaolafm.report.event.BaseReportEventBean;
import com.kaolafm.report.listener.IReportEventIntercept;
import com.kaolafm.report.listener.IReportInitListener;
import com.kaolafm.report.model.KaolaActivateData;
import com.kaolafm.report.model.PlayReportParameter;
import com.kaolafm.report.model.ReportBean;
import com.kaolafm.report.model.ReportBeanBigData;
import com.kaolafm.report.model.ReportCarParameter;
import com.kaolafm.report.model.ReportParameter;
import com.kaolafm.report.model.ReportPrivateParameter;
import com.kaolafm.report.model.ReportTask;
import com.kaolafm.report.util.ConfigConstant;
import com.kaolafm.report.util.ConfigDBHelper;
import com.kaolafm.report.util.ConfigDBHelperBigData;
import com.kaolafm.report.util.InitUtil;
import com.kaolafm.report.util.PlayReportManager;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportShenCeDBHelper;
import com.kaolafm.report.util.ReportBigDataDBHelper;
import com.kaolafm.report.util.ReportParameterManager;
import com.kaolafm.report.util.ReportShenCeTaskHelper;
import com.kaolafm.report.util.ReportBigDataTaskHelper;
import com.kaolafm.report.util.ReportShenCeTimerManager;
import com.kaolafm.report.util.ReportBigDataTimerManager;
import com.kaolafm.report.util.ReportUploadBigDataTask;
import com.kaolafm.report.util.ReportUploadShenCeTask;

import java.util.List;

import io.reactivex.Single;


/**
 * <AUTHOR> on 2019/1/10.
 */

public class ReportHelper {
    /**
     * 是否使用车载sdk
     */
    public boolean isUseBySDK = true;

    private IReportInitListener mIReportInitListener;

    /**
     * 是否初始化成功
     */
    public boolean isInitSuccess = false;

    private String mCarType = "";
    private String product_id = "";
    private String app_mode = "";

    private static ReportHelper reportHelper;
    private Context mContext;
    private PlayReportManager playReportManager;

    private ReportSwitchRequest mReportSwitchRequest;
    private boolean mIsUploadBigDatacenter = false;

    /**
     * 缓存第一次激活成功. 保存激活数据.
     */
    private KaolaActivateData mKaolaActivateData;

    private ReportHelper() {

    }

    public static ReportHelper getInstance() {
        if (reportHelper == null) {
            synchronized (ReportHelper.class) {
                if (reportHelper == null) {
                    reportHelper = new ReportHelper();
                }
            }
        }
        return reportHelper;
    }

    /**
     * 用来控制是opensdk使用
     */
    public void initBySdk() {
        isUseBySDK = true;
    }

    public void initByApk() {
        isUseBySDK = false;
    }

    public void init(Context context, ReportParameter reportParameter) {
        mContext = context;
        mReportSwitchRequest = new ReportSwitchRequest();
        // 判断上川到大数据还是神策
        mReportSwitchRequest.isUploadBigDatacenter(new HttpCallback<ReportSwitchOption>() {
            @Override
            public void onSuccess(ReportSwitchOption reportSwitchOption) {
                mIsUploadBigDatacenter = reportSwitchOption.isUploadBigDatacenter();
                if (mIsUploadBigDatacenter) {
                    ReportBigDataDBHelper.getInstance().init();
                    ConfigDBHelperBigData.getInstance().init();
                } else {
                    ReportShenCeDBHelper.getInstance().init();
                    ConfigDBHelper.getInstance().init();
                }
                syncInit(reportParameter);
            }

            @Override
            public void onError(ApiException exception) {
                mIsUploadBigDatacenter = false;
                ReportShenCeDBHelper.getInstance().init();
                ConfigDBHelper.getInstance().init();
                syncInit(reportParameter);
            }
        });
    }

    private void syncInit(ReportParameter reportParameter) {
        playReportManager = new PlayReportManager();
        ReportParameterManager.getInstance().init(InitUtil.init(reportParameter.getDeviceId(), reportParameter.getAppid(), reportParameter.getOpenid(), reportParameter.getUid(), reportParameter.getLib_version(), reportParameter.getChannel()), playReportManager);

        isInitSuccess = true;
        if (mIReportInitListener != null) {
            mIReportInitListener.initComplete();
        }
        if (mKaolaActivateData != null) {
            setCarParameter(mKaolaActivateData);
            mKaolaActivateData = null;
        }
        if (!StringUtil.isEmpty(mCarType)) {
            ReportParameterManager.getInstance().setCarType(mCarType);
        }
        if (!StringUtil.isEmpty(product_id)) {
            ReportParameterManager.getInstance().setProduct_id(product_id);
        }
        if (!StringUtil.isEmpty(app_mode)) {
            ReportParameterManager.getInstance().setApp_mode(app_mode);
        }
    }

//    public void init(Context context, String deviceId, String appId, String openId, String userId, String libVersion, String channel) {
//        mContext = context;
//        ReportShenCeDBHelper.getInstance().init();
//        ConfigDBHelper.getInstance().init();
//        playReportManager = new PlayReportManager();
//        ReportParameterManager.getInstance().init(InitUtil.init(deviceId, appId, openId, userId, libVersion, channel), playReportManager);
//        isInitSuccess = true;
//    }

    public void initUid(String uid) {
        if (!isInitSuccess) {
            return;
        }
        ReportParameterManager.getInstance().setUid(uid);
    }


    public void setCarParameter(ReportCarParameter reportCarParameter) {
        if (!isInitSuccess) {
            return;
        }
        ReportParameterManager.getInstance().setReportCarParameter(reportCarParameter);
    }

    public void setCarParameter(KaolaActivateData kaolaActivateData) {
        mKaolaActivateData = kaolaActivateData;
        if (!isInitSuccess) {
            Logging.i(ReportConstants.REPORT_TAG, "未初始化sdk, 缓存激活数据..");
            return;
        }
        if (kaolaActivateData == null) {
            return;
        }
        KaolaActivateData.CarConfigBean carConfigBean = kaolaActivateData.getCarConfig();
        KaolaActivateData.CarInfoBean carInfoBean = kaolaActivateData.getCarInfo();
        if (carConfigBean == null || carInfoBean == null) {
            return;
        }
        ReportCarParameter reportCarParameter = new ReportCarParameter();
        reportCarParameter.setAppIdType(carInfoBean.getAppIdType());
        reportCarParameter.setCarBrand(carInfoBean.getCarBrand());
        reportCarParameter.setFirstAppId(carInfoBean.getFirstAppId());
        reportCarParameter.setFirstAppIdName(carInfoBean.getFirstAppIdName());
        reportCarParameter.setMarketType(carInfoBean.getMarketType());
        reportCarParameter.setOem(carInfoBean.getOem());
        reportCarParameter.setTimer(carConfigBean.getReportInterval());
        reportCarParameter.setSystemTime(DateUtil.getServerTime());
        reportCarParameter.setDeveloper(carInfoBean.getDeveloper());
        ReportParameterManager.getInstance().setReportCarParameter(reportCarParameter);
    }

    /**
     * 设置播放器播放进度
     *
     * @param playPosition
     */
    public void setPlayPosition(long playPosition, long totalLength) {
        if (!isInitSuccess) {
            return;
        }
        if (playReportManager == null) {
            return;
        }
        playReportManager.setPosition(playPosition, totalLength);
    }

    public void setSearchAudioPlayCallBack(String playType, String callback) {
        if (!isInitSuccess) {
            return;
        }
        if (playReportManager == null) {
            return;
        }
        playReportManager.setSearchPlayCallBack(playType, callback);
    }

    public void setProductId(String product_id) {
        if (!this.isInitSuccess) {
            this.product_id = product_id;
        } else {
            ReportParameterManager.getInstance().setProduct_id(product_id);
        }
    }

    public void setAppMode(String app_mode) {
        if (!this.isInitSuccess) {
            this.app_mode = app_mode;
        } else {
            ReportParameterManager.getInstance().setApp_mode(app_mode);
        }
    }

    public void setLat(String lat) {
        if (!isInitSuccess) {
            return;
        }
        ReportParameterManager.getInstance().setLat(lat);
    }

    public void setCoordType(String coordType) {
        if (!isInitSuccess) {
            return;
        }
        ReportParameterManager.getInstance().setCoordType(coordType);
    }

    public void setLon(String lon) {
        if (!isInitSuccess) {
            return;
        }
        ReportParameterManager.getInstance().setLon(lon);
    }

    public void setPage(String page) {
        if (!isInitSuccess) {
            return;
        }
        ReportParameterManager.getInstance().setPage(page);
    }

    public void setLiveStatus(int status) {
        if (!isInitSuccess) {
            return;
        }
        if (playReportManager == null) {
            return;
        }
        playReportManager.setLiveStatus(status);
    }

    public Context getContext() {
        return mContext;
    }

    /**
     * 添加上报事件
     *
     * @param reportEventBean 上报事件
     */
    public void addEvent(BaseReportEventBean reportEventBean) {
        addEvent(reportEventBean, false);
    }

    /**
     * 添加上报事件
     *
     * @param reportEventBean  上报事件
     * @param isReportRightNow 立即上报
     */
    public void addEvent(BaseReportEventBean reportEventBean, boolean isReportRightNow) {
        if (!isInitSuccess) {
            return;
        }

        if (reportEventBean == null) {
            return;
        }
        String eventCode = reportEventBean.getEventcode();
        if (StringUtil.isEmpty(eventCode)) {
            return;
        }
        if (isReportRightNow) {
            reportEventBean.setReport_timely(ReportConstants.REPORT_EVENT_RIGHT_NOW + "");
        }

        String jsonStr = new Gson().toJson(reportEventBean);
        Logging.i(ReportConstants.REPORT_TAG, "添加数据上报事件.是否立即:" + isReportRightNow + "事件id = " + reportEventBean.getEventcode() + " json = " + jsonStr);


        // 判断上川到大数据还是神策
        if (mIsUploadBigDatacenter) {// 上传到大数据
            if (isReportRightNow) {
                ReportBeanBigData reportBean = new ReportBeanBigData();
                reportBean.setType(ReportConstants.UPLOAD_TASK_TYPE_NORMAL);
                reportBean.addData(jsonStr);
                ReportUploadBigDataTask reportUploadTask = new ReportUploadBigDataTask(reportBean);
                reportUploadTask.report();
            } else {
                ReportTask<Long> reportTask = new ReportTask();
                reportTask.setType(ReportConstants.TASK_TYPE_INSERT);
                reportTask.setSingleTask(ReportBigDataDBHelper.getInstance().insertData(jsonStr));
                ReportBigDataTaskHelper.getInstance().insertTask(reportTask);
            }
        } else {// 上传到神策
            if (isReportRightNow) {
                ReportBean reportBean = new ReportBean();
                reportBean.setType(ReportConstants.UPLOAD_TASK_TYPE_NORMAL);
                reportBean.addData(jsonStr);
                ReportUploadShenCeTask reportUploadShenCeTask = new ReportUploadShenCeTask(reportBean);
                reportUploadShenCeTask.report();
            } else {
                ReportTask<Long> reportTask = new ReportTask();
                reportTask.setType(ReportConstants.TASK_TYPE_INSERT);
                reportTask.setSingleTask(ReportShenCeDBHelper.getInstance().insertData(jsonStr));
                ReportShenCeTaskHelper.getInstance().insertTask(reportTask);
            }
        }
//        if (isReportRightNow) {
//            ReportBean reportBean = new ReportBean();
//            reportBean.setType(ReportConstants.UPLOAD_TASK_TYPE_NORMAL);
//            reportBean.addData(jsonStr);
//            ReportUploadShenCeTask reportUploadTask = new ReportUploadShenCeTask(reportBean);
//            reportUploadTask.report();
//        } else {
//            ReportTask<Long> reportTask = new ReportTask();
//            reportTask.setType(ReportConstants.TASK_TYPE_INSERT);
//            reportTask.setSingleTask(ReportShenCeDBHelper.getInstance().insertData(jsonStr));
//            ReportShenCeTaskHelper.getInstance().insertTask(reportTask);
//        }

    }

    /**
     * 添加播放开始事件
     *
     * @param playReportParameter
     */
    public void addStartListenReport(PlayReportParameter playReportParameter) {
        if (!isInitSuccess) {
            return;
        }
        if (playReportManager == null) {
            return;
        }
        playReportManager.setPlayReportParameter(playReportParameter);
    }

    /**
     * 添加播放结束事件
     *
     * @param reason       播放结束的原因
     * @param isNeedReport 是否需上报
     */
    public void addEndListenReport(String reason, boolean isNeedReport) {
        if (!isInitSuccess) {
            return;
        }
        if (playReportManager == null) {
            return;
        }
        playReportManager.addEndEvent(reason, isNeedReport);
    }

    /**
     * 添加播放结束事件
     *
     * @param reason           播放结束的原因
     * @param isNeedReport     是否需要上报
     * @param isClearParameter 是否清除Parameter
     */
    public void addEndListenReport(String reason, boolean isNeedReport, boolean isClearParameter) {
        if (this.isInitSuccess) {
            this.playReportManager.addEndEvent(reason, isNeedReport, isClearParameter);
        }
    }

    public void addBroadcastPlaying(boolean isClearParameter) {
        if (this.isInitSuccess) {
            this.playReportManager.addBroadcastPlaying(isClearParameter);
        }
    }


    public void addAppStart(String type) {
        if (!isInitSuccess) {
            return;
        }
        ReportParameterManager.getInstance().setAppStartType(type);
    }

    public void setCarType(String carType) {
        if (!isInitSuccess) {
            mCarType = carType;
        } else {
            ReportParameterManager.getInstance().setCarType(carType);
        }
    }

    /**
     * 设置横竖屏
     *
     * @param type
     */
    public void setLandOrPortrait(String type) {
        if (!isInitSuccess) {
            return;
        }

        ReportParameterManager.getInstance().setScreen_direction(type);
    }

    /**
     * 设置升级方式
     *
     * @param type
     */
    public void initUpdateEvent(String type) {
        if (!isInitSuccess) {
            return;
        }
        ReportParameterManager.getInstance().initUpdateEvent(type);
    }

    /**
     * 设置真实版本
     *
     * @param realVersionName
     * @param realVersionCode
     */
    public void setRealVersion(String realVersionName, String realVersionCode) {
        ReportParameterManager.getInstance().setRealVersion(realVersionName, realVersionCode);
    }


    public void setIReportInitListener(IReportInitListener listener) {
        mIReportInitListener = listener;
    }

    public void setReportEventIntercept(IReportEventIntercept reportEventIntercept) {
        if (!isInitSuccess) {
            return;
        }
        if (playReportManager == null) {
            return;
        }
        playReportManager.setReportEventIntercept(reportEventIntercept);
    }

    public void release() {
        if (!isInitSuccess) {
            return;
        }
        if (playReportManager != null) {
            playReportManager.release();
        }
        if (mIsUploadBigDatacenter) {
            ReportBigDataTaskHelper.getInstance().release();
            ReportBigDataTimerManager.getInstance().release();
            ConfigDBHelperBigData.getInstance().release();
            ReportBigDataDBHelper.getInstance().release();
        } else {
            ReportShenCeTaskHelper.getInstance().release();
            ReportShenCeTimerManager.getInstance().release();
            ConfigDBHelper.getInstance().release();
            ReportShenCeDBHelper.getInstance().release();
        }
    }

    public boolean isUploadBigDatacenter() {
        return mIsUploadBigDatacenter;
    }

    public void saveDB(String json) {
        if (mIsUploadBigDatacenter) {
            if (!StringUtil.isEmpty(json)) {
                ConfigData configData = new ConfigData();
                configData.setId(1L);
                configData.setType(ConfigDBHelperBigData.TYPE_PLAY_PARAMETER);
                configData.setJson(json);
                ConfigDBHelperBigData.getInstance().insertData(configData)
                        .subscribe(aLong -> {
                        }, throwable -> {
                        });
            }
        } else {
            if (!StringUtil.isEmpty(json)) {
                ConfigData configData = new ConfigData();
                configData.setId(1L);
                configData.setType(ConfigConstant.TYPE_PLAY_PARAMETER);
                configData.setJson(json);
                ConfigDBHelper.getInstance().insertData(configData)
                        .subscribe(aLong -> {
                        }, throwable -> {
                        });
            }
        }
    }

    public void savePrivateParameter(ReportPrivateParameter privateParameter) {
        ConfigData configData = new ConfigData();
        configData.setId(2L);
        configData.setType(ConfigConstant.TYPE_REPORT_PRIVATE_PARAMETER);
        configData.setJson(new Gson().toJson(privateParameter));
        Single<Long> single = insertDB(configData);
        if (single == null) {
            return;
        }
        single.subscribe(aLong -> {
        }, throwable -> {
        });
    }

    public void saveCarParameter(ReportCarParameter reportCarParameter) {
        ConfigData configData = new ConfigData();
        configData.setId(3L);
        configData.setType(ConfigConstant.TYPE_REPORT_CAR_PARAMETER);
        configData.setJson(new Gson().toJson(reportCarParameter));
        Single<Long> single = insertDB(configData);
        if (single == null) {
            return;
        }
        single.subscribe(aLong -> {
        }, throwable -> {
        });
    }

    public void deleteDB() {
        if (mIsUploadBigDatacenter) {
            ConfigDBHelperBigData.getInstance().deleteData(1L).subscribe(aLong -> {
            }, throwable -> {
            });
        } else {
            ConfigDBHelper.getInstance().deleteData(1L).subscribe(aLong -> {
            }, throwable -> {
            });
        }

    }

    public Single<List<ConfigData>> readDB() {
        if (mIsUploadBigDatacenter) {
            return ConfigDBHelperBigData.getInstance().readAll();
        } else {
            return ConfigDBHelper.getInstance().readAll();
        }
    }

    private Single<Long> insertDB(ConfigData configData) {
        if (mIsUploadBigDatacenter) {
            return ConfigDBHelperBigData.getInstance().insertData(configData);
        } else {
            return ConfigDBHelper.getInstance().insertData(configData);
        }
    }
}
