package com.kaolafm.opensdk.http.socket;

import okhttp3.WebSocket;

/**
 * <AUTHOR>
 * @date 2020-01-02
 */
class SocketInfo {

    WebSocket webSocket;

    boolean connect;

    boolean reconnect;

    String text;

    private SocketInfo() {
    }

    SocketInfo(WebSocket webSocket, boolean connect) {
        this.webSocket = webSocket;
        this.connect = connect;
    }

    public SocketInfo(WebSocket webSocket, String text) {
        this.webSocket = webSocket;
        this.text = text;
    }

    static SocketInfo createReconnect() {
        SocketInfo socketInfo = new SocketInfo();
        socketInfo.reconnect = true;
        return socketInfo;
    }

    boolean isConnect() {
        return false;
    }

    WebSocket getWebSocket() {
        return webSocket;
    }
}
