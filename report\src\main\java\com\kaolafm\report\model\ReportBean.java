package com.kaolafm.report.model;

import org.json.JSONArray;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> on 2019/1/10.
 */

public class ReportBean {
    private int mType;
    private List<Long> mIdList;
    JSONArray jsonArray;

    public ReportBean() {
        mIdList = new ArrayList<>();
        jsonArray = new JSONArray();
    }

    public void addData(Long id, String reportJson) {
        mIdList.add(id);
        jsonArray.put(reportJson);
    }

    public void addData(String reportJson) {
        jsonArray.put(reportJson);
    }

    public List getIdList() {
        return mIdList;
    }

    public String getReportValue() {
        return jsonArray.toString();
    }

    public int getType() {
        return mType;
    }

    public void setType(int type) {
        this.mType = type;
    }
}
