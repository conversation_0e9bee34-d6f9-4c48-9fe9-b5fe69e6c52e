package com.kaolafm.report.event;

import com.kaolafm.report.util.ReportConstants;

/**
 * @Package: com.kaolafm.report.event
 * @Description: 购买VIP
 * @Author: Maclay
 * @Date: 14:24
 */
public class BuyVIPEvent extends BaseReportEventBean {
    private int type;
    private String remarks1;

    public BuyVIPEvent() {
        setEventcode(ReportConstants.EVENT_ID_BUY_VIP);
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getRemarks1() {
        return remarks1;
    }

    public void setRemarks1(String remarks1) {
        this.remarks1 = remarks1;
    }


    @Override
    public String toString() {
        return "BuyAlbumEvent{" +
                "type='" + type + '\'' +
                ", remarks1='" + remarks1 + '\'' +
                '}';
    }
}
