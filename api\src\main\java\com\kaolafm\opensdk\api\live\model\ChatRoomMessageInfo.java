package com.kaolafm.opensdk.api.live.model;

import android.os.Parcel;
import android.os.Parcelable;

public class ChatRoomMessageInfo implements Parcelable {

    // 消息发送时间，时间戳格式
    private Long sendTime;
    // 消息发送人账号
    private String senderAccount;
    // 消息发送人头像
    private String senderAvatar;
    // 消息发送人昵称
    private String senderNickName;
    // 消息类型：0文本，1语音
    private Integer msgType;
    // 文本消息内容
    private String msgContent;
    // 语音消息内容
    private String msgAudioContent;
    // 是否本人发送
    private Boolean selfSend;

    public Long getSendTime() {
        return sendTime;
    }

    public void setSendTime(Long sendTime) {
        this.sendTime = sendTime;
    }

    public String getSenderAccount() {
        return senderAccount == null ? "" : senderAccount;
    }

    public void setSenderAccount(String senderAccount) {
        this.senderAccount = senderAccount;
    }

    public String getSenderAvatar() {
        return senderAvatar == null ? "" : senderAvatar;
    }

    public void setSenderAvatar(String senderAvatar) {
        this.senderAvatar = senderAvatar;
    }

    public String getSenderNickName() {
        return senderNickName == null ? "" : senderNickName;
    }

    public void setSenderNickName(String senderNickName) {
        this.senderNickName = senderNickName;
    }

    public Integer getMsgType() {
        return msgType;
    }

    public void setMsgType(Integer msgType) {
        this.msgType = msgType;
    }

    public String getMsgContent() {
        return msgContent == null ? "" : msgContent;
    }

    public void setMsgContent(String msgContent) {
        this.msgContent = msgContent;
    }

    public String getMsgAudioContent() {
        return msgAudioContent == null ? "" : msgAudioContent;
    }

    public void setMsgAudioContent(String msgAudioContent) {
        this.msgAudioContent = msgAudioContent;
    }

    public Boolean getSelfSend() {
        return selfSend;
    }

    public void setSelfSend(Boolean selfSend) {
        this.selfSend = selfSend;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeValue(this.sendTime);
        dest.writeString(this.senderAccount);
        dest.writeString(this.senderAvatar);
        dest.writeString(this.senderNickName);
        dest.writeValue(this.msgType);
        dest.writeString(this.msgContent);
        dest.writeString(this.msgAudioContent);
        dest.writeValue(this.selfSend);
    }

    public ChatRoomMessageInfo() {
    }

    protected ChatRoomMessageInfo(Parcel in) {
        this.sendTime = (Long) in.readValue(Long.class.getClassLoader());
        this.senderAccount = in.readString();
        this.senderAvatar = in.readString();
        this.senderNickName = in.readString();
        this.msgType = (Integer) in.readValue(Integer.class.getClassLoader());
        this.msgContent = in.readString();
        this.msgAudioContent = in.readString();
        this.selfSend = (Boolean) in.readValue(Boolean.class.getClassLoader());
    }

    public static final Parcelable.Creator<ChatRoomMessageInfo> CREATOR = new Parcelable.Creator<ChatRoomMessageInfo>() {
        @Override
        public ChatRoomMessageInfo createFromParcel(Parcel source) {
            return new ChatRoomMessageInfo(source);
        }

        @Override
        public ChatRoomMessageInfo[] newArray(int size) {
            return new ChatRoomMessageInfo[size];
        }
    };
}
