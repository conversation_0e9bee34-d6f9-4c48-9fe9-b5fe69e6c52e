package com.kaolafm.opensdk.log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * log。默认实现。
 * <AUTHOR>
 * @date 2018/12/19
 */

public final class Logging {

    @NonNull
    private static Printer printer = new DefaultPrinter();

    private static boolean isDebug = false;

    private static LogLevel.RequestLevel mRequestLevel = LogLevel.RequestLevel.NONE;

    private Logging() {
    }

    public static void printer(@NonNull Printer printer) {
        Logging.printer = printer;
    }
    /**
     * 本次打印使用指定的tag进行打印。
     */
    public static Printer tag(@Nullable String tag) {
        return printer.tag(tag);
    }

    /**
     * 打印log，不使用其他任意额外修饰，仅仅打印。相当于{@link android.util.Log}的方法。
     */
    public static void log(int priority, @Nullable String tag, @Nullable String message,
            @Nullable Throwable throwable) {
        if (isDebug()) {
            printer.println(priority, tag, message, throwable);
        }
    }

    /**
     * 打印log
     */
    public static void log(int priority, @Nullable String tag, @Nullable String message) {
        if (isDebug()) {
            log(priority, tag, message, null);
        }
    }

    public static void d(@NonNull String message, @Nullable Object... args) {
        if (isDebug()) {
            printer.d(message, args);
        }
    }

    public static void d(@Nullable Object object) {
        if (isDebug()) {
            printer.d(object);
        }
    }

    public static void e(@NonNull String message, @Nullable Object... args) {
        if (isDebug()) {
            printer.e(message, args);
        }
    }

    public static void e(@Nullable Throwable throwable, @NonNull String message, @Nullable Object... args) {
        if (isDebug()) {
            printer.e(throwable, message, args);
        }
    }

    public static void i(@NonNull String message, @Nullable Object... args) {
        if (isDebug()) {
            printer.i(message, args);
        }
    }

    public static void v(@NonNull String message, @Nullable Object... args) {
        if (isDebug()) {
            printer.v(message, args);
        }
    }

    public static void w(@NonNull String message, @Nullable Object... args) {
        if (isDebug()) {
            printer.w(message, args);
        }
    }

    /**
     *
     */
    public static void wtf(@NonNull String message, @Nullable Object... args) {
        if (isDebug()) {
            printer.wtf(message, args);
        }
    }

    public static void d(String tag, String message) {
        if (isDebug()) {
            log(LogLevel.DEBUG, tag, message);
        }
    }

    public static void e(String tag, String message) {
        if (isDebug()) {
            log(LogLevel.ERROR, tag, message);
        }
    }

    public static void i(String tag, String message) {
        if (isDebug()) {
            log(LogLevel.INFO, tag, message);
        }
    }

    public static void v(String tag, String message) {
        if (isDebug()) {
            log(LogLevel.VERBOSE, tag, message);
        }
    }

    public static void w(String tag, String message) {
        if (isDebug()) {
            log(LogLevel.WARN, tag, message);
        }
    }

    public static void wtf(String tag, String message) {
        if (isDebug()) {
            log(LogLevel.ASSERT, tag, message);
        }
    }

    /**
     * 格式化json并打印
     */
    public static void json(@Nullable String json) {
        if (isDebug()) {
            printer.json(json);
        }
    }

    /**
     * 格式化xml并打印
     */
    public static void xml(@Nullable String xml) {
        if (isDebug()) {
            printer.xml(xml);
        }
    }

    /**
     * 是否debug模式。debug模式下可以打印log
     * @param debug true是
     */
    public static void setDebug(boolean debug) {
        isDebug = debug;
    }

    public static boolean isDebug() {
        return isDebug;
    }

    public static LogLevel.RequestLevel getRequestLevel() {
        return mRequestLevel;
    }

    public static void setRequestLevel(LogLevel.RequestLevel level) {
        mRequestLevel = level;
    }

    public static class Log {

        /**
         * @deprecated compatible with {@link android.util.Log#v(String, String)}
         */
        public static void v(String tag, String msg) {
            Logging.v(tag, msg);
        }

        /**
         * @deprecated compatible with {@link android.util.Log#v(String, String, Throwable)}
         */
        public static void v(String tag, String msg, Throwable tr) {
            log(LogLevel.VERBOSE, tag, msg, tr);
        }

        /**
         * @deprecated compatible with {@link android.util.Log#d(String, String)}
         */
        public static void d(String tag, String msg) {
            Logging.d(tag, msg);
        }

        /**
         * @deprecated compatible with {@link android.util.Log#d(String, String, Throwable)}
         */
        public static void d(String tag, String msg, Throwable tr) {
            log(LogLevel.DEBUG, tag, msg, tr);
        }

        /**
         * @deprecated compatible with {@link android.util.Log#i(String, String)}
         */
        public static void i(String tag, String msg) {
            Logging.i(tag, msg);
        }

        /**
         * @deprecated compatible with {@link android.util.Log#i(String, String, Throwable)}
         */
        public static void i(String tag, String msg, Throwable tr) {
            log(LogLevel.INFO, tag, msg, tr);
        }

        /**
         * @deprecated compatible with {@link android.util.Log#w(String, String)}
         */
        public static void w(String tag, String msg) {
            Logging.w(tag, msg);
        }

        /**
         * @deprecated compatible with {@link android.util.Log#w(String, String, Throwable)}
         */
        public static void w(String tag, String msg, Throwable tr) {
            log(LogLevel.WARN, tag, msg, tr);
        }

        /**
         * @deprecated compatible with {@link android.util.Log#w(String, Throwable)}
         */
        public static void w(String tag, Throwable tr) {
            log(LogLevel.WARN, tag, "", tr);
        }

        /**
         * @deprecated compatible with {@link android.util.Log#e(String, String)}
         */
        public static void e(String tag, String msg) {
            Logging.e(tag, msg);
        }

        /**
         * @deprecated compatible with {@link android.util.Log#e(String, String, Throwable)}
         */
        public static void e(String tag, String msg, Throwable tr) {
            log(LogLevel.ERROR, tag, msg, tr);
        }

        /**
         * @deprecated compatible with {@link android.util.Log#wtf(String, String)}
         */
        public static void wtf(String tag, String msg) {
            Logging.wtf(tag, msg);
        }

        /**
         * @deprecated compatible with {@link android.util.Log#wtf(String, Throwable)}
         */
        public static void wtf(String tag, Throwable tr) {
            wtf(tag, "", tr);
        }

        /**
         * @deprecated compatible with {@link android.util.Log#wtf(String, String, Throwable)}
         */
        public static void wtf(String tag, String msg, Throwable tr) {
            log(LogLevel.ASSERT, tag, msg, tr);
        }

        /**
         * @deprecated compatible with {@link android.util.Log#println(int, String, String)}
         */
        public static void println(int logLevel, String tag, String msg) {
            Logging.log(logLevel,tag, msg);
        }
    }
}
