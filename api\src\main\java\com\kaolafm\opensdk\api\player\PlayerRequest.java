package com.kaolafm.opensdk.api.player;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.player.model.PlayUrlData;
import com.kaolafm.opensdk.http.core.HttpCallback;

/**
 * 播放器独有的接口类。目前只有更新碎片状态、获取广播日期接口
 *
 * <AUTHOR>
 * @date 2019-11-13
 */
public class PlayerRequest extends BaseRequest {

    private PlayerService mPlayerService;

    public PlayerRequest() {
        mUrlManager.putDomain(PlayerRequestConstant.DOMAIN_HEADER_API_KAOLA, PlayerRequestConstant.BASE_HOST_API_KAOLA);
        mPlayerService = obtainRetrofitService(PlayerService.class);
    }

    /**
     * 通过playId获取播放地址
     * @param playId
     * @param callback
     */
    public void getPlayUrlById(String playId, HttpCallback<PlayUrlData> callback){
        doHttpDeal(mPlayerService.getPlayUrl(playId), BaseResult::getResult, callback);
    }
}
