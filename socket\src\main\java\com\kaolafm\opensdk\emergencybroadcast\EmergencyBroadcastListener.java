package com.kaolafm.opensdk.emergencybroadcast;


import android.text.TextUtils;

import com.kaolafm.opensdk.api.emergency.model.EmergencyBroadcast;
import com.kaolafm.opensdk.socket.SocketEvent;
import com.kaolafm.opensdk.socket.SocketListener;

import java.util.Map;

public abstract class EmergencyBroadcastListener implements SocketListener<EmergencyBroadcast> {
    @Override
    public String getEvent() {
        return SocketEvent.API_MESSAGE;
    }

    @Override
    public Map<String, Object> getParams(Map<String, Object> params) {
        if (params != null) {
            String lat = (String) params.get("lat");
            String lng = (String) params.get("lng");
            if (!TextUtils.isEmpty(lat)) {
                try {
                    params.put("lat", Double.valueOf(lat));
                } catch (Exception e) {
                    params.put("lat", 0);
                }
            } else {
                params.put("lat", 0);
            }

            if (!TextUtils.isEmpty(lng)) {
                try {
                    params.put("lng", Double.valueOf(lng));
                } catch (Exception e) {
                    params.put("lng", 0);
                }
            } else {
                params.put("lng", 0);
            }
        }
        return params;
    }

    @Override
    public boolean isNeedParams() {
        return true;
    }

    @Override
    public boolean isNeedRequest() {
        return false;
    }
}
