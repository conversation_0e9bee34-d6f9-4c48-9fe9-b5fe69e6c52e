package com.kaolafm.gradle.plugin.model

import com.kaolafm.gradle.plugin.model.MavenConfig
import org.gradle.api.Action
/**
 * <AUTHOR>
 * @date 2019/4/15
 */
public class SDKFlavor {
    def includeSourcePackage = new HashSet<String>()
    def includeSourceJava = new HashSet<String>()
    def excludeSourcePackage = new HashSet<String>()
    def excludeSourceJava = new HashSet<String>()
    def includeClass = new HashSet<String>()
    def includePackage = new HashSet<String>()
    def excludeJar = new HashSet<String>()
    def excludeClass = new HashSet<String>()
    def excludePackage = new HashSet<String>()
    def outputFileDir = "build/releaseJar"
    def outputFileName = ""
    def versionName = ""
    def outputProguardFileName = ""
    def proguardConfigFile = new HashSet<String>()
    def applyMappingFile = ""
    def needDefaultProguard = false
    //是否包含旧SDK。默认包含。
    boolean includeOldSDK = true

    def mavenConfig = new MavenConfig()

    String name

    SDKFlavor(String name) {
        this.name = name
    }

    public void mavenConfig(Action<MavenConfig> action) {
        action.execute(mavenConfig)
    }

    def getIncludeSourcePackage() {
        return includeSourcePackage
    }

    void setIncludeSourcePackage(Set<String> includeSourcePackage) {
        this.includeSourcePackage = includeSourcePackage
    }

    def getIncludeSourceJava() {
        return includeSourceJava
    }

    void setIncludeSourceJava(Set<String> includeSourceJava) {
        this.includeSourceJava = includeSourceJava
    }

    def getExcludeSourcePackage() {
        return excludeSourcePackage
    }

    void setExcludeSourcePackage(Set<String> excludeSourcePackage) {
        this.excludeSourcePackage = excludeSourcePackage
    }

    def getExcludeSourceJava() {
        return excludeSourceJava
    }

    void setExcludeSourceJava(Set<String> excludeSourceJava) {
        this.excludeSourceJava = excludeSourceJava
    }

    def getIncludeClass() {
        return includeClass
    }

    void setIncludeClass(Set<String> includeClass) {
        this.includeClass = includeClass
    }

    def getIncludePackage() {
        return includePackage
    }

    void setIncludePackage(Set<String> includePackage) {
        this.includePackage = includePackage
    }

    def getExcludeJar() {
        return excludeJar
    }

    void setExcludeJar(Set<String> excludeJar) {
        this.excludeJar = excludeJar
    }

    def getExcludeClass() {
        return excludeClass
    }

    void setExcludeClass(Set<String> excludeClass) {
        this.excludeClass = excludeClass
    }

    def getExcludePackage() {
        return excludePackage
    }

    void setExcludePackage(Set<String> excludePackage) {
        this.excludePackage = excludePackage
    }

    def getOutputFileDir() {
        return outputFileDir
    }

    void setOutputFileDir(String outputFileDir) {
        this.outputFileDir = outputFileDir
    }

    def getOutputFileName() {
        return outputFileName
    }

    void setOutputFileName(String outputFileName) {
        this.outputFileName = outputFileName
    }

    def getVersionName() {
        return versionName
    }

    void setVersionName(String jarVersion) {
        this.versionName = jarVersion
        versionName(jarVersion)
    }

    def getOutputProguardFileName() {
        return outputProguardFileName
    }

    void setOutputProguardFileName(String outputProguardFileName) {
        this.outputProguardFileName = outputProguardFileName
    }

    def getProguardConfigFile() {
        return proguardConfigFile
    }

    void setProguardConfigFile(Set<String> proguardConfigFile) {
        this.proguardConfigFile = proguardConfigFile
    }

    def getApplyMappingFile() {
        return applyMappingFile
    }

    void setApplyMappingFile(String applyMappingFile) {
        this.applyMappingFile = applyMappingFile
    }

    def getNeedDefaultProguard() {
        return needDefaultProguard
    }

    void setNeedDefaultProguard(boolean needDefaultProguard) {
        this.needDefaultProguard = needDefaultProguard
    }

    def getMavenConfig() {
        return mavenConfig
    }

    void setMavenConfig(maven) {
        this.mavenConfig = maven
    }

    String getName() {
        return name
    }

    void setName(String name) {
        this.name = name
    }

    //----------------------------设置maven相关参数-------------------------

    public void mavenType(mavenType) {
        mavenConfig.mavenType(mavenType)
    }

    public void artifactId(artifactId) {
        mavenConfig.artifactId(artifactId)
    }

    public void groupId(groupId) {
        mavenConfig.groupId(groupId)
    }

    public void libType(libType) {
        mavenConfig.libType(libType)
    }
    public void libDescription(libDescription) {
        mavenConfig.description(libDescription)
    }

    public void repository(repository) {
        mavenConfig.repository(repository)
    }

    public void versionName(version) {
        mavenConfig.versionName(version)
    }

    public void userName (userName) {
        mavenConfig.setUserName(userName)
    }

    public void password(password) {
        mavenConfig.setPassword(password)
    }

    @Override
    public String toString() {
        return "SDKFlavor{" +
                "includeSourcePackage=" + includeSourcePackage +
                ", includeSourceJava=" + includeSourceJava +
                ", excludeSourcePackage=" + excludeSourcePackage +
                ", excludeSourceJava=" + excludeSourceJava +
                ", includeClass=" + includeClass +
                ", includePackage=" + includePackage +
                ", excludeJar=" + excludeJar +
                ", excludeClass=" + excludeClass +
                ", excludePackage=" + excludePackage +
                ", outputFileDir=" + outputFileDir +
                ", outputFileName=" + outputFileName +
                ", jarVersion=" + versionName +
                ", outputProguardFileName=" + outputProguardFileName +
                ", proguardConfigFile=" + proguardConfigFile +
                ", applyMappingFile=" + applyMappingFile +
                ", needDefaultProguard=" + needDefaultProguard +
                ", mavenConfig=" + mavenConfig +
                ", name='" + name + '\'' +
                '}'
    }
}