package com.kaolafm.gradle.plugin.processors

import com.android.build.gradle.api.LibraryVariant
import com.kaolafm.gradle.plugin.model.AndroidAttachments
import com.kaolafm.gradle.plugin.model.MavenConfig
import com.kaolafm.gradle.plugin.model.SDKFlavor
import com.kaolafm.gradle.plugin.utils.TaskFactory
import org.gradle.api.Project
import org.gradle.api.Task
import org.gradle.api.artifacts.Configuration
import org.gradle.api.artifacts.component.ModuleComponentSelector
import org.gradle.api.artifacts.component.ProjectComponentSelector
import org.gradle.api.artifacts.result.DependencyResult
import org.gradle.api.publish.PublicationContainer
import org.gradle.api.publish.PublishingExtension
import org.gradle.api.publish.maven.MavenPom
import org.gradle.api.publish.maven.MavenPublication

import java.util.function.Predicate

/**
 * 上传发布处理，配置各种发布的参数
 * <AUTHOR>
 * @date 2019-06-08
 */
class UploadProcessor {

    private Project mProject

    /**
     * 是否已经设置过配置，防止多次设置报错
     */
    private boolean attached = false

    UploadProcessor(Project project) {
        this.mProject = project
    }

    void upload(LibraryVariant variant, SDKFlavor flavor) {
        if (flavor != null) {
            Task uploadTask = TaskFactory.createUploadTask(mProject, flavor.name)
            Task proguardTask = TaskFactory.createBuildProguardTask(mProject, flavor.name)
            if (attached) {
                return
            }
            attached = true
            attachArtifact(variant, flavor)
            int mavenType = flavor.mavenConfig.mavenType
            String pluginTaskName
            if (mavenType == MavenConfig.TYPE_MAVEN_BINTRAY) {
                config(flavor)
                pluginTaskName = "bintrayUpload"
            } else {
                pluginTaskName = "publishReleasePublicationToMavenRepository"
            }
            Task pluginPublishTask = mProject.tasks.findByPath(pluginTaskName)
            pluginPublishTask.dependsOn proguardTask
            uploadTask.dependsOn pluginPublishTask
        }
    }

    private void attachArtifact(LibraryVariant variant, SDKFlavor flavor) {
        String publicationName = variant.name
        MavenPublication publication = createPublication(publicationName, flavor)
        new AndroidAttachments(mProject, flavor, variant).attachTo(publication)
    }

    private MavenPublication createPublication(String name, SDKFlavor flavor) {
        MavenConfig config = flavor.mavenConfig
        String groupId = config.groupId
        String artifactId = config.artifactId
        String version = config.versionName
        PublishingExtension publishingExtension = mProject.extensions.getByType(PublishingExtension)
        publishingExtension.repositories { artifactRepositories ->
            artifactRepositories.maven { mavenArtifactRepository ->
                String repository = config.repository
                mavenArtifactRepository.url = repository
                //file开头是本地路径，不设置用户名密码
                if (repository != null && !repository.startsWith("file://")) {
                    mavenArtifactRepository.credentials {
                        credentials ->
                            credentials.username = config.userName
                            credentials.password = config.password
                    }
                }
            }
        }
        PublicationContainer publicationContainer = publishingExtension.publications
        return publicationContainer.create(name, MavenPublication) { MavenPublication publication ->
            publication.groupId = groupId
            publication.artifactId = artifactId
            publication.version = version
            decoratePom(publication.pom, config)
        } as MavenPublication
    }

    private void decoratePom(MavenPom pom, MavenConfig config) {
        pom.name = config.artifactId
        pom.description = config.description
        pom.packaging = config.libType
        addDependencies(pom)
    }

    private void addDependencies(MavenPom pom) {
        pom.withXml {
            //获取pom中的依赖节点
            NodeList dependenciesNodeList = asNode().getByName("dependencies")
            Node dependenciesNode
            if (dependenciesNodeList != null && dependenciesNodeList.size() > 0) {
                dependenciesNode = dependenciesNodeList[0]
            } else {
                dependenciesNode = asNode().appendNode('dependencies')
            }
            //移除无用的依赖
            dependenciesNode.children().removeIf(new Predicate<Node>() {
                @Override
                boolean test(Node o) {
                    try {
                        return "unspecified" == o.artifactId.get(0).value().get(0) || "unspecified" == o.version.get(0).value().get(0)
                    } catch (NullPointerException e) {
                        return true
                    }
                }
            })

            def deps = new ArrayList<ModuleComponentSelector>()
            mProject.configurations.each { Configuration conf ->
                //  考虑多个Flavor的情况
                if (conf.name.toLowerCase().contains("releaseruntimeclasspath")) {
                    // 获取所有依赖信息
                    conf.incoming.resolutionResult.root.dependencies.each { dr ->
                        resolveDependencies(deps, dr)
                    }
                }
            }
            //添加依赖到pom中
            deps.each {
                def dependencyNode = dependenciesNode.appendNode('dependency')
                dependencyNode.appendNode('groupId', it.group)
                dependencyNode.appendNode('artifactId', it.module)
                dependencyNode.appendNode('version', it.version)
                dependencyNode.appendNode('scope', "compile")
            }
        }
    }

    /**
     * 递归的方式获取所有第三方依赖
     */
    private void resolveDependencies(ArrayList<ModuleComponentSelector> selectors, DependencyResult dr) {
        if (dr != null) {
            def requested = dr.requested
            if (requested instanceof ModuleComponentSelector) {
                selectors.add(requested)
            } else if (requested instanceof ProjectComponentSelector) {
                dr.selected.dependencies.each { subDr ->
                    resolveDependencies(selectors, subDr)
                }
            }
        }
    }

    /**
     * 配置bintray仓库的信息
     * @param flavor
     */
    void config(SDKFlavor flavor) {
        MavenConfig mavenConfig = flavor.mavenConfig
        mProject.bintray {
            user = mavenConfig.userName
            key = mavenConfig.password
            publish = true
            dryRun = false

            publications = ['release']

            pkg {
                userOrg = mavenConfig.userOrg
                repo = mavenConfig.repository
                name = mavenConfig.artifactId
                desc = mavenConfig.description
                websiteUrl = 'http://www.tingban.cn'
//                vcsUrl = mavenConfig.repository
                licenses = ["Apache-2.0"]
                version {
                    name = mavenConfig.versionName
                    gpg {
                        sign = true
                    }
                }
            }
        }
    }

}