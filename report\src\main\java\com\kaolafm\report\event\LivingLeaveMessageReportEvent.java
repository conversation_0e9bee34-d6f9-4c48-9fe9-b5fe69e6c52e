package com.kaolafm.report.event;

import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> on 2019-07-23.
 */

public class LivingLeaveMessageReportEvent extends BaseReportEventBean {
    /**
     * 直播标识id
     */
    private String live_id;
    /**
     * 直播计划id
     */
    private String plan_id;
    /**
     * 主持人id
     */
    private String live_manager_uid;

    private String radioid;

    public LivingLeaveMessageReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_LIVING_LEAVE_MESSAGE);
    }

    public String getLive_id() {
        return live_id;
    }

    public void setLive_id(String live_id) {
        this.live_id = live_id;
    }

    public String getPlan_id() {
        return plan_id;
    }

    public void setPlan_id(String plan_id) {
        this.plan_id = plan_id;
    }

    public String getLive_manager_uid() {
        return live_manager_uid;
    }

    public void setLive_manager_uid(String live_manager_uid) {
        this.live_manager_uid = live_manager_uid;
    }

    public String getRadioid() {
        return radioid;
    }

    public void setRadioid(String radioid) {
        this.radioid = radioid;
    }
}
