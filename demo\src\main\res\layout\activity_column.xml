<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true"
    >

    <EditText
        android:id="@+id/et_column_zone"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:hint="请输入分区"
        android:textSize="14sp"
        android:text="mainPage"/>

    <TextView
        android:id="@+id/tv_column_commit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/color_black_50_transparent"
        android:paddingBottom="5dp"
        android:paddingEnd="10dp"
        android:paddingStart="10dp"
        android:paddingTop="5dp"
        android:text="确定"
        android:textColor="@color/colorAccent"
        app:layout_constraintEnd_toEndOf="@id/et_column_zone" />
    <Switch
        android:id="@+id/switch_with_member"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="同时获取栏目成员"
        app:layout_constraintEnd_toEndOf="parent"
        />

    <com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout
        android:id="@+id/trf_column_refresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/et_column_zone"
        app:layout_constraintVertical_weight="1">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_column_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
