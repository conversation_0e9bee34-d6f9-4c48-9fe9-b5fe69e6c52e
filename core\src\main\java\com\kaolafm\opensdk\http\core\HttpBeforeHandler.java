package com.kaolafm.opensdk.http.core;

import okhttp3.Interceptor;
import okhttp3.Request;

/**
 * 提前处理网络请求的接口
 * <AUTHOR>
 * @date 2020-01-13
 */
public interface HttpBeforeHandler {
    /**
     * 提前处理http的请求
     * @param chain
     * @param request
     * @return
     */
    Request onHttpRequestBefore(Interceptor.Chain chain, Request request);

    /**
     * 优先级，越大越先执行。不设置，不保证顺序，dagger2 注入是使用的HashSet，根据hash值进行的排序。
     * @return
     */
    default int priority() {
        return 0;
    }
}
