package com.kaolafm.report.event;

import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> on 2019-07-23.
 */

public class PlusFeedbackReportEvent extends BaseReportEventBean{
    private String audioid;
    private String radioid;
    private String albumid;
    /**
     * 正反馈追踪号
     */
    private String remarks11;

    public PlusFeedbackReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_PLUS_FEEDBACK);
    }

    public String getAudioid() {
        return audioid;
    }

    public void setAudioid(String audioid) {
        this.audioid = audioid;
    }

    public String getRadioid() {
        return radioid;
    }

    public void setRadioid(String radioid) {
        this.radioid = radioid;
    }

    public String getAlbumid() {
        return albumid;
    }

    public void setAlbumid(String albumid) {
        this.albumid = albumid;
    }

    public String getRemarks11() {
        return remarks11;
    }

    public void setRemarks11(String remarks11) {
        this.remarks11 = remarks11;
    }
}
