package com.kaolafm.opensdk.socket;

/**
 * Created by kaolafm on 2019/3/12.
 */

public final class SocketEvent {
    public static final String HOME_PAGE_REFRESH = "KradioHomepageRefresh";

    public static final String RADIO_LIVE = "livePush";
    public static final String APP_MESSAGE = "appMessage";//消息泡泡
    public static final String API_MESSAGE = "EB_PUSH";//应急广播的消息事件
    public static final String LOCATION_UPDATE = "LOC_RPT";//上报地理位置
    public static final String LOCATION_UPDATE_RESPONSE = "LOC_RESP";//上报地理位置结果
    public static final String USER_LOGIN = "singleLogin";
    public static final String USER_LOGOUT = "singleLogout";

    /**
     * 积分
     */
    public static final String COIN_REFRESH = "integralRefresh";

    /**
     * 上传历史
     */
    public static final String SAVE_HISTORY = "saveUserHistory";

    /**
     * 开始语音识别
     */
    public static final String SPEECH_RECOGNITION_START = "speechStart";

    /**
     * 语音识别进行中
     */
    public static final String SPEECH_RECOGNITION_RUNNING = "speechVoice";

    /**
     * 结束语音识别
     */
    public static final String SPEECH_RECOGNITION_END = "speechEnd";

    /**
     * 语音识别结果
     */
    public static final String SPEECH_RECOGNITION_RESULT = "speechVoiceResult";

    /**
     * 语音识别出错
     */
    public static final String SPEECH_RECOGNITION_ERROR = "speechVoiceError";

    /**
     * 直播 - 长连接事件 - 进入聊天室 - 客户端推送事件
     * 用户进入聊天室，告知服务端
     */
    public static final String LIVE_ENTER_CHATROOM = "ENTER_CHATROOM";

    /**
     * 直播 - 长连接事件 - 退出聊天室 - 客户端推送事件
     * 用户离开聊天室，告知服务端
     */
    public static final String LIVE_EXIT_CHATROOM = "EXIT_CHATROOM";

    /**
     * 直播 - 长连接事件 - 赠礼通知 - 客户端推送事件
     * 用户进行赠礼，告知服务端
     * 送礼物会调用后台的http接口，请求成功后调用此事件通知后台
     */
    public static final String LIVE_SEND_GIFT = "SEND_GIFT";

    /**
     * 直播 - 推送聊天室成员排名 - 客户端监听事件
     * 推送某个聊天室里的成员排名信息
     * 合并在 LIVE_RECV_MSG,暂时不需要
     */
    public static final String LIVE_PUSH_RANK = "PUSH_RANK";

    /**
     * 直播 - 实时接收聊天室消息（用户消息以及直播间通知） - 客户端监听事件
     */
    public static final String LIVE_RECV_MSG = "RECV_MSG";

    /**
     * 直播 - 长连接事件 - 监听用户进入事件
     */
    public static final String LIVE_ENTER_CHATROOM_NOTIFY_OTHERS = "ENTER_CHATROOM_NOTIFY_OTHERS";

    /**
     * 直播 - 长连接事件 - 监听用户退出事件
     */
    public static final String LIVE_EXIT_CHATROOM_NOTIFY_OTHERS = "EXIT_CHATROOM_NOTIFY_OTHERS";

    /**
     * 直播 - 用户被禁言，被踢出直播间 - 客户端监听事件
     */
    public static final String LIVE_USER_FORBIDDEN = "USER_FORBIDDEN";

}
