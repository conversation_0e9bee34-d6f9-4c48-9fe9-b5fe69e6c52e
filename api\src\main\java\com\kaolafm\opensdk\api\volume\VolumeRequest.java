package com.kaolafm.opensdk.api.volume;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.volume.model.VolumeOption;
import com.kaolafm.opensdk.http.core.HttpCallback;

/**
 * 音量控制请求接口。目前只有音量均衡控制。
 * <AUTHOR>
 * @date 2019-11-12
 */
public class VolumeRequest extends BaseRequest {

    private VolumeServce mVolumeServce;

    public VolumeRequest() {
        mVolumeServce = obtainRetrofitService(VolumeServce.class);
    }

    /**
     * 检查音量均衡是否开启，true表示开启，false表示不开启。
     * @param callback
     */
    public void checkVolumeBalanceStatus(HttpCallback<Boolean> callback) {
        doHttpDeal(mVolumeServce.checkVolumeBalanceStatus(), baseResult -> {
            VolumeOption result = baseResult.getResult();
            return result != null && result.isOpenVolumeBalance();
        }, callback);
    }
}
