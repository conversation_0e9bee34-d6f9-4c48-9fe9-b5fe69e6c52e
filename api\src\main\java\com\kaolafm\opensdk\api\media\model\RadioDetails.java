package com.kaolafm.opensdk.api.media.model;

import android.os.Parcel;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: RadioDetails.java                                               
 *                                                                  *
 * Created in 2018/8/10 下午5:05                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public final class RadioDetails extends BaseMediaDetails {

    /**
     * AI电台类型：0，类型电台；1，艺人电台；2，场景电台；3，流派电台；4，年代电台；5，主题电台；6，品牌电台。
     */
    @Deprecated
    private int radioType = -1;

    /**
     * 1，表示广告位ID配置在AI电台详情中；2，表示广告位配置在编排位中。
     */
    @Deprecated
    private int adZoneChooseType = 0;

    /**
     * 表示广告位ID
     */
    @Deprecated
    private int adZoneId = -1;

    public int getRadioType() {
        return radioType;
    }

    public void setRadioType(int radioType) {
        this.radioType = radioType;
    }

    public int getAdZoneChooseType() {
        return adZoneChooseType;
    }

    public void setAdZoneChooseType(int adZoneChooseType) {
        this.adZoneChooseType = adZoneChooseType;
    }

    public int getAdZoneId() {
        return adZoneId;
    }

    public void setAdZoneId(int adZoneId) {
        this.adZoneId = adZoneId;
    }

    protected RadioDetails(Parcel in) {
        super(in);
        this.radioType = in.readInt();
        this.adZoneChooseType = in.readInt();
        this.adZoneId = in.readInt();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        super.writeToParcel(dest, flags);
        dest.writeInt(radioType);
        dest.writeInt(adZoneChooseType);
        dest.writeInt(adZoneId);
    }
}
