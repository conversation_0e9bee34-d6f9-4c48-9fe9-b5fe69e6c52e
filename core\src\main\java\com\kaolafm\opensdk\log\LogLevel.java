package com.kaolafm.opensdk.log;

/**
 * log的级别，与系统log及主流log框架一样。
 * <AUTHOR>
 * @date 2018/12/19
 */

public class LogLevel {

    /**
     * Log level for Logging.v.
     */
    public static final int VERBOSE = 2;

    /**
     * Log level for Logging.d.
     */
    public static final int DEBUG = 3;

    /**
     * Log level for Logging.i.
     */
    public static final int INFO = 4;

    /**
     * Log level for Logging.w.
     */
    public static final int WARN = 5;

    /**
     * Log level for Logging.e.
     */
    public static final int ERROR = 6;

    /**
     * Log level for Logging.wtf.
     */
    public static final int ASSERT = 7;

    /**
     * Log level for Logging#init, printing all logs.
     */
    public static final int ALL = Integer.MIN_VALUE;

    /**
     * Log level for Logging#init, printing no println.
     */
    public static final int NONE = Integer.MAX_VALUE;

    public enum RequestLevel {
        /**
         * 不打印log
         */
        NONE,
        /**
         * 只打印请求信息
         */
        REQUEST,
        /**
         * 只打印响应信息
         */
        RESPONSE,
        /**
         * 所有数据全部打印
         */
        ALL
    }
}
