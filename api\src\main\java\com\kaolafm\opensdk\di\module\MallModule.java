package com.kaolafm.opensdk.di.module;

import android.text.TextUtils;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.account.profile.KaolaProfile;
import com.kaolafm.opensdk.account.token.KaolaAccessToken;
import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.di.qualifier.AccessTokenQualifier;
import com.kaolafm.opensdk.di.qualifier.KaolaParam;
import com.kaolafm.opensdk.di.qualifier.MallParam;
import com.kaolafm.opensdk.di.qualifier.ParamQualifier;
import com.kaolafm.opensdk.di.qualifier.ProfileQualifier;

import java.util.HashMap;
import java.util.Map;

import dagger.Module;
import dagger.Provides;
import dagger.multibindings.IntoMap;
import dagger.multibindings.StringKey;

/**
 * 提供考拉、Kradio公共参数
 *
 * <AUTHOR>
 * @date 2018/7/25
 */
@Module
public class MallModule {

    /**
     * 商城接口公共参数
     */
    @Provides
    @MallParam
    Map<String, String> provideMallCommonParams(@ProfileQualifier KaolaProfile profile,
                                           @AccessTokenQualifier KaolaAccessToken accessToken) {
        HashMap<String, String> params = new HashMap<>();
        params.put(KaolaApiConstant.APP_ID, profile.getAppId());
        params.put(KaolaApiConstant.PACKAGE_NAME, profile.getPackageName());
        params.put(KaolaApiConstant.OS, KaolaApiConstant.OS_NAME);
        params.put(KaolaApiConstant.DEVICE_ID, profile.getDeviceId());

        String openId = accessToken.getOpenId();
        if (!TextUtils.isEmpty(openId)) {
            params.put(KaolaApiConstant.OPEN_ID, openId);
        }
        params.put(KaolaApiConstant.SIGN, StringUtil.createSign(params, profile.getAppKey()));
        //下面参数不需要加入MD5签名中。
        params.put(KaolaApiConstant.CHANNEL, profile.getChannel());
        params.put(KaolaApiConstant.VERSION, profile.getVersionName());

        String udid;
        String userId = accessToken.getUserId();
        if (!TextUtils.isEmpty(userId)) {
            params.put(KaolaApiConstant.OPEN_UID, userId);
            udid = userId;
        } else {
            udid = profile.getDeviceId();
        }
        params.put(KaolaApiConstant.UDID, udid);
        String token = accessToken.getAccessToken();
        if (!TextUtils.isEmpty(token)) {
            params.put(KaolaApiConstant.ACCESS_TOKEN, token);
        }

        String sdkVersionName = profile.getSdkVersionName();
        if (!TextUtils.isEmpty(sdkVersionName)) {
            params.put(KaolaApiConstant.SDK_VERSION, sdkVersionName);
        }
        String lat = profile.getLat();
        params.put(KaolaApiConstant.LAT, lat == null ? "" : lat);
        String lng = profile.getLng();
        params.put(KaolaApiConstant.LNG, lng == null ? "" : lng);

        String carType = profile.getCarType();
        if (!TextUtils.isEmpty(carType)) {
            params.put(KaolaApiConstant.CAR_TYPE, carType);
        }
        String capabilities = profile.getCapabilities();
        if (!TextUtils.isEmpty(capabilities)) {
            params.put("capabilities", capabilities);
        }
        return params;
    }

    @Provides
    @ParamQualifier
    @IntoMap
    @StringKey(ApiHostConstants.MALL_DOMAIN_NAME)
    Map<String, String> provideMallParams(@KaolaParam Map<String, String> params) {
        return params;
    }
}
