package com.kaolafm.ad.expose;

import com.kaolafm.opensdk.di.scope.AppScope;

import javax.inject.Inject;

/**
 * 组合图片、音频的执行类
 *
 * <AUTHOR>
 * @date 2020-02-17
 */
@AppScope
class CompositeExecutor {

    private AdvertisingImagerAdapter mImager;

    private AdvertisingPlayerAdapter mPlayer;

    @Inject
    CompositeExecutor(AdvertisingImagerAdapter imager, AdvertisingPlayerAdapter player) {
        mImager = imager;
        mPlayer = player;
    }

    public AdvertisingImagerAdapter getImager() {
        return mImager;
    }

    public void setImager(AdvertisingImagerAdapter imager) {
        mImager = imager;
    }

    public AdvertisingPlayerAdapter getPlayer() {
        return mPlayer;
    }

    public void setPlayer(AdvertisingPlayerAdapter player) {
        mPlayer = player;
    }
}
