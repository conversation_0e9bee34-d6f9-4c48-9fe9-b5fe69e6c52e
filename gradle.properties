# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx2048m

# When configured, <PERSON><PERSON><PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
#android.useAndroidX=true
#android.enableJetifier=true
android.useDepricatedNdk=true
android.useAndroidX=true
android.enableJetifier=true
android.enableD8.desugaring=true
android.databinding.enableV2=true
android.jetifier.blacklist = buildSrc.jar,26.6.4.jar,bundletool-0.10.3.jar