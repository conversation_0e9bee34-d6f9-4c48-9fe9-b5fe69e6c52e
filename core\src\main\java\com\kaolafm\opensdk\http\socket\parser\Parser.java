package com.kaolafm.opensdk.http.socket.parser;


import com.kaolafm.opensdk.http.socket.Packet;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.opensdk.http.error.UTF8Exception;

import org.json.JSONException;
import org.json.JSONTokener;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;


public class Parser {

    /**
     * Packet type `start`.
     */
    public static final int CONNECT = 0;

    /**
     * Packet type `disconnect`.
     */
    public static final int DISCONNECT = 1;

    /**
     * Packet type `event`.
     */
    public static final int EVENT = 2;

    /**
     * Packet type `ack`.
     */
    public static final int ACK = 3;

    /**
     * Packet type `error`.
     */
    public static final int ERROR = 4;

    /**
     * Packet type `binary event`.
     */
    public static final int BINARY_EVENT = 5;

    /**
     * Packet type `binary ack`.
     */
    public static final int BINARY_ACK = 6;

    /**
     * Packet types.
     */
    /*private static final String[] TYPES = new String[]{
            "CONNECT",
            "DISCONNECT",
            "EVENT",
            "ACK",
            "ERROR",
            "BINARY_EVENT",
            "BINARY_ACK"
    };*/

    private static final int MAX_INT_CHAR_LENGTH = String.valueOf(Integer.MAX_VALUE).length();

    public static final int PROTOCOL = 3;

    private static final Map<Integer, String> PACKETS = new HashMap<Integer, String>() {{
        put(Packet.OPEN, "open");
        put(Packet.CLOSE, "close");
        put(Packet.PING, "ping");
        put(Packet.PONG, "pong");
        put(Packet.MESSAGE, "message");
        put(Packet.UPGRADE, "upgrade");
        put(Packet.NOOP, "noop");
    }};

    private static Packet<String> err = new Packet<>(Packet.ERROR, "parser error");

    private static UTF8.Options utf8Options = new UTF8.Options();

    static {
        utf8Options.strict = false;
    }


    private Parser() {
    }

    public static void encodePacket(Packet packet, CodecCallback callback) throws UTF8Exception {
        encodePacket(packet, false, callback);
    }

    public static void encodePacket(Packet packet, boolean utf8encode, CodecCallback callback) throws UTF8Exception {
        if (packet.data instanceof byte[]) {
            @SuppressWarnings("unchecked")
            Packet<byte[]> packetToEncode = packet;
            @SuppressWarnings("unchecked")
            CodecCallback<byte[]> callbackToEncode = callback;
            encodeByteArray(packetToEncode, callbackToEncode);
            return;
        }

        String encoded = String.valueOf(packet.type);

        if (null != packet.data) {
            encoded += utf8encode ? UTF8.encode(String.valueOf(packet.data), utf8Options) : String.valueOf(packet.data);
        }

        @SuppressWarnings("unchecked")
        CodecCallback<String> tempCallback = callback;
        tempCallback.call(encoded);
    }

    private static void encodeByteArray(Packet<byte[]> packet, CodecCallback<byte[]> callback) {
        byte[] data = packet.data;
        byte[] resultArray = new byte[1 + data.length];
        resultArray[0] = Integer.valueOf(packet.type).byteValue();
        System.arraycopy(data, 0, resultArray, 1, data.length);
        callback.call(resultArray);
    }

    public static Packet<String> decodePacket(String data) {
        return decodePacket(data, false);
    }

    public static Packet<String> decodePacket(String data, boolean utf8decode) {
        if (data == null) {
            return err;
        }

        int type;
        try {
            type = Character.getNumericValue(data.charAt(0));
        } catch (IndexOutOfBoundsException e) {
            type = -1;
        }

        if (utf8decode) {
            try {
                data = UTF8.decode(data, utf8Options);
            } catch (UTF8Exception e) {
                return err;
            }
        }

        if (type < 0 || type >= PACKETS.size()) {
            return err;
        }

        if (data.length() > 1) {
            return new Packet<>(type, data.substring(1));
        } else {
            return new Packet<>(type);
        }
    }

    public static Packet<byte[]> decodePacket(byte[] data) {
        int type = data[0];
        byte[] intArray = new byte[data.length - 1];
        System.arraycopy(data, 1, intArray, 0, intArray.length);
        return new Packet<>(type, intArray);
    }

    public static void encodePayload(Packet[] packets, CodecCallback callback) throws UTF8Exception {
        for (Packet packet : packets) {
            if (packet.data instanceof byte[]) {
                @SuppressWarnings("unchecked")
                CodecCallback<byte[]> _callback = (CodecCallback<byte[]>) callback;
                encodePayloadAsBinary(packets, _callback);
                return;
            }
        }

        if (packets.length == 0) {
            callback.call("0:");
            return;
        }

        final StringBuilder result = new StringBuilder();

        for (Packet packet : packets) {
            encodePacket(packet, false, message -> result.append(setLengthHeader((String) message)));
        }

        callback.call(result.toString());
    }

    private static String setLengthHeader(String message) {
        return message.length() + ":" + message;
    }

    private static void encodePayloadAsBinary(Packet[] packets, CodecCallback<byte[]> callback) throws UTF8Exception {
        if (packets.length == 0) {
            callback.call(new byte[0]);
            return;
        }

        final ArrayList<byte[]> results = new ArrayList<>(packets.length);

        for (Packet packet : packets) {
            encodeOneBinaryPacket(packet, results::add);
        }

        callback.call(concat(results.toArray(new byte[results.size()][])));
    }

    private static void encodeOneBinaryPacket(Packet p, final CodecCallback<byte[]> doneCallback) throws UTF8Exception {
        encodePacket(p, true, packet -> {
            if (packet instanceof String) {
                String encodingLength = String.valueOf(((String) packet).length());
                byte[] sizeBuffer = new byte[encodingLength.length() + 2];

                sizeBuffer[0] = (byte) 0; // is a string
                for (int i = 0; i < encodingLength.length(); i++) {
                    sizeBuffer[i + 1] = (byte) Character.getNumericValue(encodingLength.charAt(i));
                }
                sizeBuffer[sizeBuffer.length - 1] = (byte) 255;
                doneCallback.call(concat(new byte[][]{sizeBuffer, stringToByteArray((String) packet)}));
                return;
            }

            String encodingLength = String.valueOf(((byte[]) packet).length);
            byte[] sizeBuffer = new byte[encodingLength.length() + 2];
            sizeBuffer[0] = (byte) 1; // is binary
            for (int i = 0; i < encodingLength.length(); i++) {
                sizeBuffer[i + 1] = (byte) Character.getNumericValue(encodingLength.charAt(i));
            }
            sizeBuffer[sizeBuffer.length - 1] = (byte) 255;
            doneCallback.call(concat(new byte[][]{sizeBuffer, (byte[]) packet}));
        });
    }

    public static void decodePayload(String data, DecodePayloadCallback<String> callback) {
        if (data == null || data.length() == 0) {
            callback.call(err, 0, 1);
            return;
        }

        StringBuilder length = new StringBuilder();
        for (int i = 0, l = data.length(); i < l; i++) {
            char chr = data.charAt(i);

            if (':' != chr) {
                length.append(chr);
                continue;
            }

            int n;
            try {
                n = Integer.parseInt(length.toString());
            } catch (NumberFormatException e) {
                callback.call(err, 0, 1);
                return;
            }

            String msg;
            try {
                msg = data.substring(i + 1, i + 1 + n);
            } catch (IndexOutOfBoundsException e) {
                callback.call(err, 0, 1);
                return;
            }

            if (msg.length() != 0) {
                Packet<String> packet = decodePacket(msg, false);
                if (err.type == packet.type && err.data.equals(packet.data)) {
                    callback.call(err, 0, 1);
                    return;
                }

                boolean ret = callback.call(packet, i + n, l);
                if (!ret) {
                    return;
                }
            }

            i += n;
            length = new StringBuilder();
        }

        if (length.length() > 0) {
            callback.call(err, 0, 1);
        }
    }

    public static void decodePayload(byte[] data, DecodePayloadCallback callback) {
        ByteBuffer bufferTail = ByteBuffer.wrap(data);
        List<Object> buffers = new ArrayList<>();

        while (bufferTail.capacity() > 0) {
            StringBuilder strLen = new StringBuilder();
            boolean isString = (bufferTail.get(0) & 0xFF) == 0;
            for (int i = 1; ; i++) {
                int b = bufferTail.get(i) & 0xFF;
                if (b == 255) {
                    break;
                }
                // supports only integer
                if (strLen.length() > MAX_INT_CHAR_LENGTH) {
                    callback.call(err, 0, 1);
                    return;
                }
                strLen.append(b);
            }

            bufferTail.position(strLen.length() + 1);
            bufferTail = bufferTail.slice();

            int msgLength = Integer.parseInt(strLen.toString());

            bufferTail.position(1);
            bufferTail.limit(msgLength + 1);
            byte[] msg = new byte[bufferTail.remaining()];
            bufferTail.get(msg);
            if (isString) {
                buffers.add(byteArrayToString(msg));
            } else {
                buffers.add(msg);
            }
            bufferTail.clear();
            bufferTail.position(msgLength + 1);
            bufferTail = bufferTail.slice();
        }

        int total = buffers.size();
        for (int i = 0; i < total; i++) {
            Object buffer = buffers.get(i);
            if (buffer instanceof String) {
                @SuppressWarnings("unchecked")
                DecodePayloadCallback<String> tempCallback = callback;
                tempCallback.call(decodePacket((String) buffer, true), i, total);
            } else if (buffer instanceof byte[]) {
                @SuppressWarnings("unchecked")
                DecodePayloadCallback<byte[]> tempCallback = callback;
                tempCallback.call(decodePacket((byte[]) buffer), i, total);
            }
        }
    }

    private static String byteArrayToString(byte[] bytes) {
        StringBuilder builder = new StringBuilder();
        for (byte b : bytes) {
            builder.appendCodePoint(b & 0xFF);
        }
        return builder.toString();
    }

    private static byte[] stringToByteArray(String string) {
        int len = string.length();
        byte[] bytes = new byte[len];
        for (int i = 0; i < len; i++) {
            bytes[i] = (byte) Character.codePointAt(string, i);
        }
        return bytes;
    }

    private static byte[] concat(byte[][] list) {
        int length = 0;
        for (byte[] buf : list) {
            length += buf.length;
        }
        return concat(list, length);
    }

    private static byte[] concat(byte[][] list, int length) {
        if (list.length == 0) {
            return new byte[0];
        } else if (list.length == 1) {
            return list[0];
        }

        ByteBuffer buffer = ByteBuffer.allocate(length);
        for (byte[] buf : list) {
            buffer.put(buf);
        }

        return buffer.array();
    }

    public final static class Encoder {

        @Inject
        public Encoder() {
        }

        public void encode(Packet obj, CodecCallback callback) {
            if ((obj.type == EVENT || obj.type == ACK) && HasBinary.hasBinary(obj.data)) {
                obj.type = obj.type == EVENT ? BINARY_EVENT : BINARY_ACK;
            }

            Logging.d("encoding packet %s", obj);

            if (BINARY_EVENT == obj.type || BINARY_ACK == obj.type) {
                encodeAsBinary(obj, callback);
            } else {
                String encoding = encodeAsString(obj);
                callback.call(new String[]{encoding});
            }
        }

        private String encodeAsString(Packet obj) {
            StringBuilder str = new StringBuilder("" + obj.type);

            if (BINARY_EVENT == obj.type || BINARY_ACK == obj.type) {
                str.append(obj.attachments);
                str.append("-");
            }

            if (obj.nsp != null && obj.nsp.length() != 0 && !"/".equals(obj.nsp)) {
                str.append(obj.nsp);
                str.append(",");
            }

            if (obj.id >= 0) {
                str.append(obj.id);
            }

            if (obj.data != null) {
                str.append(obj.data);
            }

            Logging.d("encoded %s as %s", obj, str);
            return str.toString();
        }

        private void encodeAsBinary(Packet obj, CodecCallback callback) {
            Binary.DeconstructedPacket deconstruction = Binary.deconstructPacket(obj);
            String pack = encodeAsString(deconstruction.packet);
            List<Object> buffers = new ArrayList<Object>(Arrays.asList(deconstruction.buffers));

            buffers.add(0, pack);
            callback.call(buffers.toArray());
        }
    }

     public final static class Decoder {

        /*package*/ BinaryReconstructor reconstructor;

        private CodecCallback onDecodedCallback;

        @Inject
        public Decoder() {
            this.reconstructor = null;
        }

        public void add(String obj) {
            Packet packet = decodeString(obj);
            if (BINARY_EVENT == packet.type || BINARY_ACK == packet.type) {
                this.reconstructor = new BinaryReconstructor(packet);

                if (this.reconstructor.reconPack.attachments == 0) {
                    if (this.onDecodedCallback != null) {
                        this.onDecodedCallback.call(packet);
                    }
                }
            } else {
                if (this.onDecodedCallback != null) {
                    this.onDecodedCallback.call(packet);
                }
            }
        }

        public void add(byte[] obj) {
            if (this.reconstructor == null) {
                throw new RuntimeException("got binary data when not reconstructing a packet");
            } else {
                Packet packet = this.reconstructor.takeBinaryData(obj);
                if (packet != null) {
                    this.reconstructor = null;
                    if (this.onDecodedCallback != null) {
                        this.onDecodedCallback.call(packet);
                    }
                }
            }
        }

        private static Packet decodeString(String str) {
            int i = 0;
            int length = str.length();

            Packet<Object> p = new Packet<>(Character.getNumericValue(str.charAt(0)));

            if (p.type < 0 || p.type > BINARY_ACK) {
                return error();
            }

            if (BINARY_EVENT == p.type || BINARY_ACK == p.type) {
                if (!str.contains("-") || length <= i + 1) {
                    return error();
                }
                StringBuilder attachments = new StringBuilder();
                while (str.charAt(++i) != '-') {
                    attachments.append(str.charAt(i));
                }
                p.attachments = Integer.parseInt(attachments.toString());
            }

            if (length > i + 1 && '/' == str.charAt(i + 1)) {
                StringBuilder nsp = new StringBuilder();
                while (true) {
                    ++i;
                    char c = str.charAt(i);
                    if (',' == c) {
                        break;
                    }
                    nsp.append(c);
                    if (i + 1 == length) {
                        break;
                    }
                }
                p.nsp = nsp.toString();
            } else {
                p.nsp = "/";
            }

            if (length > i + 1) {
                char next = str.charAt(i + 1);
                if (Character.getNumericValue(next) > -1) {
                    StringBuilder id = new StringBuilder();
                    while (true) {
                        ++i;
                        char c = str.charAt(i);
                        if (Character.getNumericValue(c) < 0) {
                            --i;
                            break;
                        }
                        id.append(c);
                        if (i + 1 == length) {
                            break;
                        }
                    }
                    try {
                        p.id = Integer.parseInt(id.toString());
                    } catch (NumberFormatException e) {
                        return error();
                    }
                }
            }

            if (length > i + 1) {
                try {
                    str.charAt(++i);
                    p.data = new JSONTokener(str.substring(i)).nextValue();
                } catch (JSONException e) {
                    Logging.w("An error occured while retrieving data from JSONTokener, %s", e);
                    return error();
                }
            }
            Logging.d("decoded %s as %s", str, p);
            return p;
        }

        public void destroy() {
            if (this.reconstructor != null) {
                this.reconstructor.finishReconstruction();
            }
            this.onDecodedCallback = null;
        }

        public <T> void onDecoded(CodecCallback<T> callback) {
            this.onDecodedCallback = callback;
        }
    }


    /*package*/ static class BinaryReconstructor {

        public Packet reconPack;

        /*package*/ List<byte[]> buffers;

        BinaryReconstructor(Packet packet) {
            this.reconPack = packet;
            this.buffers = new ArrayList<>();
        }

        public Packet takeBinaryData(byte[] binData) {
            this.buffers.add(binData);
            if (this.buffers.size() == this.reconPack.attachments) {
                Packet packet = Binary.reconstructPacket(this.reconPack,
                        this.buffers.toArray(new byte[this.buffers.size()][]));
                this.finishReconstruction();
                return packet;
            }
            return null;
        }

        public void finishReconstruction() {
            this.reconPack = null;
            this.buffers = new ArrayList<>();
        }
    }
    private static Packet<String> error() {
        return new Packet<>(ERROR, "parser error");
    }

    public interface CodecCallback<T> {

        /**
         * 编解码回调
         * @param data
         */
        void call(T data);
    }


    public interface DecodePayloadCallback<T> {

        /**
         * 解码延迟加载回调。
         * @param packet
         * @param index
         * @param total
         * @return
         */
        boolean call(Packet<T> packet, int index, int total);
    }
}
