package com.kaolafm.report.util;

import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.database.ReportData;
import com.kaolafm.report.database.greendao.DaoMaster;
import com.kaolafm.report.database.greendao.DaoSession;
import com.kaolafm.report.model.ReportBeanBigData;

import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.query.QueryBuilder;

import java.util.List;
import java.util.concurrent.Callable;

import io.reactivex.Single;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

/**
 * <AUTHOR> on 2019/1/9.
 */

public class ReportBigDataDBHelper {
    private static final String DATA_BASE_NAME = "reportBigData.db";
    private static ReportBigDataDBHelper reportDBHelper;
    private DaoSession daoSession;

    private ReportBigDataDBHelper() {
    }


    public static ReportBigDataDBHelper getInstance() {
        if (reportDBHelper == null) {
            synchronized (ReportBigDataDBHelper.class) {
                if (reportDBHelper == null) {
                    reportDBHelper = new ReportBigDataDBHelper();
                }
            }
        }
        return reportDBHelper;
    }

    public void init() {
        DaoMaster.DevOpenHelper devOpenHelper = new DaoMaster.DevOpenHelper(ReportHelper.getInstance().getContext(), DATA_BASE_NAME);
        Database database;
        try {
            database = devOpenHelper.getWritableDb();
        } catch (Exception e) {
            e.printStackTrace();
            return;
        }
        DaoMaster daoMaster = new DaoMaster(database);
        daoSession = daoMaster.newSession();
    }

    public Single<Long> insertData(String str) {
        if (isDaoSessionUnavailable()) {
            return null;
        }
        ReportData reportData = new ReportData();
        reportData.setSendStr(str);
        return Single.fromCallable(() -> {
            daoSession.insert(reportData);
            return daoSession.getReportDataDao().count();
        }).observeOn(AndroidSchedulers.mainThread())
                .subscribeOn(Schedulers.io());
    }

    public Single<Long> deleteData(Long id) {
        if (isDaoSessionUnavailable()) {
            return null;
        }
        return Single.fromCallable(() -> {
            daoSession.getReportDataDao().deleteByKey(id);
            return daoSession.getReportDataDao().count();
        }).observeOn(AndroidSchedulers.mainThread()).subscribeOn(Schedulers.io());
    }

    public Single<ReportBeanBigData> read() {
        if (isDaoSessionUnavailable()) {
            return null;
        }
        return Single.fromCallable((Callable<List<ReportData>>) () -> {
            QueryBuilder queryBuilder = daoSession.queryBuilder(ReportData.class);
            return queryBuilder.limit(ReportConstants.READ_BIG_DATA_BASE_MAX_COUNT).list();
        }).map(reportData -> {
            ReportBeanBigData reportBean = new ReportBeanBigData();
            reportBean.setType(ReportConstants.UPLOAD_TASK_TYPE_BY_DATA_BASE);
            for (int i = 0; i < reportData.size(); i++) {
                reportBean.addData(reportData.get(i).getId(), reportData.get(i).getSendStr());
            }
            return reportBean;
        }).observeOn(AndroidSchedulers.mainThread()).subscribeOn(Schedulers.io());
    }

    private boolean isDaoSessionUnavailable() {
        return daoSession == null;
    }

    public void release() {
        if (isDaoSessionUnavailable()) {
            return;
        }
        daoSession.getDatabase().close();
    }
}
