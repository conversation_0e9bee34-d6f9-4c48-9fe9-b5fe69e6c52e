package com.kaolafm.opensdk.api.live.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * <AUTHOR>
 * @date 2023-02-16
 */
public class Gift implements Parcelable {

    private Long giftId;//礼物id

    private String giftImg;//礼物图片

    private String giftName;//礼物名称

    private Long giftCost;//礼物价值.单位:云币

    private Integer giftType;//礼物类型.0-免费礼物,1-收费礼物

    private Integer rewardIntegral;//赠送后可得积分.单位:积分

    public Gift() {
    }

    public Gift(Long giftId, String giftImg, String giftName, Long giftCost, Integer giftType, Integer rewardIntegral) {
        this.giftId = giftId;
        this.giftImg = giftImg;
        this.giftName = giftName;
        this.giftCost = giftCost;
        this.giftType = giftType;
        this.rewardIntegral = rewardIntegral;
    }

    protected Gift(Parcel in) {
        if (in.readByte() == 0) {
            giftId = null;
        } else {
            giftId = in.readLong();
        }
        giftImg = in.readString();
        giftName = in.readString();
        if (in.readByte() == 0) {
            giftCost = null;
        } else {
            giftCost = in.readLong();
        }
        if (in.readByte() == 0) {
            giftType = null;
        } else {
            giftType = in.readInt();
        }
        if (in.readByte() == 0) {
            rewardIntegral = null;
        } else {
            rewardIntegral = in.readInt();
        }
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        if (giftId == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeLong(giftId);
        }
        dest.writeString(giftImg);
        dest.writeString(giftName);
        if (giftCost == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeLong(giftCost);
        }
        if (giftType == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(giftType);
        }
        if (rewardIntegral == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(rewardIntegral);
        }
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<Gift> CREATOR = new Creator<Gift>() {
        @Override
        public Gift createFromParcel(Parcel in) {
            return new Gift(in);
        }

        @Override
        public Gift[] newArray(int size) {
            return new Gift[size];
        }
    };

    public Long getGiftId() {
        return giftId;
    }

    public void setGiftId(Long giftId) {
        this.giftId = giftId;
    }

    public String getGiftImg() {
        return giftImg;
    }

    public void setGiftImg(String giftImg) {
        this.giftImg = giftImg;
    }

    public String getGiftName() {
        return giftName;
    }

    public void setGiftName(String giftName) {
        this.giftName = giftName;
    }

    public Long getGiftCost() {
        return giftCost;
    }

    public void setGiftCost(Long giftCost) {
        this.giftCost = giftCost;
    }

    public Integer getGiftType() {
        return giftType;
    }

    public void setGiftType(Integer giftType) {
        this.giftType = giftType;
    }

    public Integer getRewardIntegral() {
        return rewardIntegral;
    }

    public void setRewardIntegral(Integer rewardIntegral) {
        this.rewardIntegral = rewardIntegral;
    }
}
