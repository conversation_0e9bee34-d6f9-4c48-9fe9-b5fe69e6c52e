package com.kaolafm.opensdk.api.search.model;

import android.os.Parcel;
import android.os.Parcelable;
import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR> @date 2019/4/29
 */
public class HighLightWord implements Parcelable {

    /**
     * field : name
     * start : -1
     * offset : -1
     * token : 岳飞
     */

    @SerializedName("field")
    private String field;

    @SerializedName("start")
    private Integer start;

    @SerializedName("offset")
    private Integer offset;

    @SerializedName("token")
    private String token;


    protected HighLightWord(Parcel in) {
        field = in.readString();
        if (in.readByte() == 0) {
            start = null;
        } else {
            start = in.readInt();
        }
        if (in.readByte() == 0) {
            offset = null;
        } else {
            offset = in.readInt();
        }
        token = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(field);
        if (start == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(start);
        }
        if (offset == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(offset);
        }
        dest.writeString(token);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<HighLightWord> CREATOR = new Creator<HighLightWord>() {
        @Override
        public HighLightWord createFromParcel(Parcel in) {
            return new HighLightWord(in);
        }

        @Override
        public HighLightWord[] newArray(int size) {
            return new HighLightWord[size];
        }
    };

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public Integer getStart() {
        return start;
    }

    public void setStart(Integer start) {
        this.start = start;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public static Creator<HighLightWord> getCREATOR() {
        return CREATOR;
    }

    @Override
    public String toString() {
        return "HighLightWord{" +
                "field='" + field + '\'' +
                ", start=" + start +
                ", offset=" + offset +
                ", token='" + token + '\'' +
                '}';
    }
}
