package com.kaolafm.report.event;

import com.kaolafm.report.util.ReportConstants;

/**
 * 弹窗曝光
 */
public class DialogExposureEvent extends BaseReportEventBean {
    private String dialogid;       //如果按钮在弹窗上，报所在的弹窗id
    private String pageId;  //所在页面的编码id
    private long duration;  //弹窗曝光时间,单位：毫秒
    /**
     * 1. 如果是vip付费弹窗，就上报背景页面的内容id
     * 2. 如为付费内容则上报合集id
     */
    private String contentid;   //如果弹窗有内容则上报内容id;

    public String getDialogid() {
        return dialogid;
    }

    public void setDialogid(String dialogid) {
        this.dialogid = dialogid;
    }

    public String getPageId() {
        return pageId;
    }

    public void setPageId(String pageId) {
        this.pageId = pageId;
    }

    public long getDuration() {
        return duration;
    }

    public void setDuration(long duration) {
        this.duration = duration;
    }

    public String getContentid() {
        return contentid;
    }

    public void setContentid(String contentid) {
        this.contentid = contentid;
    }

    public DialogExposureEvent() {
        setEventcode(ReportConstants.EVENT_ID_DIALOG_EXPOSURE);
    }

    public DialogExposureEvent(String dialogid, String pageId, long duration, String contentid) {
        setEventcode(ReportConstants.EVENT_ID_DIALOG_EXPOSURE);
        this.dialogid = dialogid;
        this.pageId = pageId;
        this.duration = duration;
        this.contentid = contentid;
    }
}
