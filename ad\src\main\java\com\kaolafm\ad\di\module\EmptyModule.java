package com.kaolafm.ad.di.module;

import com.kaolafm.opensdk.http.core.TokenRefresh;

import dagger.Module;
import dagger.Provides;
import io.reactivex.Single;

/**
 * <AUTHOR>
 * @date 2020-03-03
 */
@Module
public abstract class EmptyModule {

    @Provides
    static TokenRefresh provideEmptyTokenRefresh() {
        return new TokenRefresh() {
            @Override
            public Single refresh() {
                return null;
            }

            @Override
            public void logout() {

            }
        };
    }
}
