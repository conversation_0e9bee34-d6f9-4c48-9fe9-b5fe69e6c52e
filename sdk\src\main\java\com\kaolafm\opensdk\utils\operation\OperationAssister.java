package com.kaolafm.opensdk.utils.operation;

import android.text.TextUtils;

import com.kaolafm.opensdk.api.operation.model.ImageFile;
import com.kaolafm.opensdk.api.operation.model.category.CategoryMember;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 运营接口返回的数据操作辅助类，使用了访问者模式。
 *
 * <AUTHOR>
 * @date 2018/9/21
 */

public class OperationAssister {

    private static List<IOperationProcessor> processorList = new ArrayList<>();

    static {
        processorList.add(new AlbumProcessor());
        processorList.add(new BroadcastProcessor());
        processorList.add(new TVProcessor());
        processorList.add(new CategoryProcessor());
        processorList.add(new LiveProcessor());
        processorList.add(new QQMusicProcessor());
        processorList.add(new RadioProcessor());
        processorList.add(new FeatureProcessor());
        processorList.add(new VideoAlbumProcessor());
        processorList.add(new InfoFragmentProcessor());
    }

    private OperationAssister() {
    }

    /**
     * 获取分类成员的id
     *
     * @param member 分类成员
     * @return id或0
     */
    public static long getId(CategoryMember member) {
        for (IOperationProcessor operationProcessor : processorList) {
            if (operationProcessor.accept(member)) {
                return operationProcessor.getId(member);
            }
        }
        return 0;
    }

    /**
     * 获取栏目成员的id
     *
     * @param member 栏目成员
     * @return id或0
     */
    public static long getId(ColumnMember member) {
        for (IOperationProcessor operationProcessor : processorList) {
            if (operationProcessor.accept(member)) {
                return operationProcessor.getId(member);
            }
        }
        return 0;
    }

    /**
     * 获取分类成员的收听数，有些类型是没有收听数的。
     *
     * @param member 分类成员
     * @return 收听数或0
     */
    public static long getListenNum(CategoryMember member) {
        for (IOperationProcessor operationProcessor : processorList) {
            if (operationProcessor.accept(member)) {
                return operationProcessor.getListenNum(member);
            }
        }
        return 0;
    }

    /**
     * 获取分类成员的类型。@see {@link com.kaolafm.opensdk.ResType}
     *
     * @param member 分类成员
     */
    public static int getType(CategoryMember member) {
        for (IOperationProcessor operationProcessor : processorList) {
            if (operationProcessor.accept(member)) {
                return operationProcessor.getType(member);
            }
        }
        return -1;
    }

    /**
     * 获取栏目成员的类型。@see {@link com.kaolafm.opensdk.ResType}
     *
     * @param member 栏目成员
     */
    public static int getType(ColumnMember member) {
        for (IOperationProcessor operationProcessor : processorList) {
            if (operationProcessor.accept(member)) {
                return operationProcessor.getType(member);
            }
        }
        return -1;
    }

    //=========================获取图片信息============

    /**
     * 获取栏目成员图标icon
     *
     * @param member 栏目成员
     * @return 返回icon的url地址或空
     */
    public static String getIcon(ColumnMember member) {
        if (member != null) {
            return getImage(ImageFile.KEY_ICON, member.getImageFiles());
        }
        return "";
    }

    /**
     * 获取分类成员图标icon
     *
     * @param member 栏目成员
     * @return icon的url地址或空
     */
    public static String getIcon(CategoryMember member) {
        if (member != null) {
            return getImage(ImageFile.KEY_ICON, member.getImageFiles());
        }
        return "";
    }

    /**
     * 获取栏目成员的封面cover
     *
     * @param member 栏目成员
     * @return cover的url地址或空
     */
    public static String getCover(ColumnMember member) {
        if (member != null) {
            return getImage(ImageFile.KEY_COVER, member.getImageFiles());
        }
        return "";
    }

    /**
     * 获取分类成员的封面cover
     *
     * @param member 分类成员
     * @return cover的url地址或空
     */
    public static String getCover(CategoryMember member) {
        if (member != null) {
            return getImage(ImageFile.KEY_COVER, member.getImageFiles());
        }
        return "";
    }

    /**
     * 从集合中获取对应的图片url地址
     *
     * @param key        键
     * @param imageFiles ImageFile集合
     */
    public static String getImage(String key, Map<String, ImageFile> imageFiles) {
        if (imageFiles != null) {
            ImageFile imageFile = imageFiles.get(key);
            if (imageFile != null) {
                return imageFile.getUrl();
            }
        }
        return "";
    }

    /**
     * 获取分类成员的图片，如果没有Icon就返回cover
     *
     * @param member
     * @return
     */
    public static String getImage(CategoryMember member) {
        String image = getIcon(member);
        if (TextUtils.isEmpty(image)) {
            image = getCover(member);
        }
        return image;
    }

    /**
     * 获取栏目成员的图片，如果没有Icon就返回cover
     *
     * @param member
     * @return
     */
    public static String getImage(ColumnMember member) {
        String image = getIcon(member);
        if (TextUtils.isEmpty(image)) {
            image = getCover(member);
        }
        return image;
    }

    /**
     * 从集合中获取对应的图片url地址（首页大卡组件）
     *
     * @param imageFiles ImageFile集合
     */
    public static String getCardImage(Map<String, ImageFile> imageFiles) {
        if (imageFiles != null) {
            ImageFile imageFile = imageFiles.get(ImageFile.KEY_ICON);
            if (imageFile != null) {
                return imageFile.getUrl();
            } else {
                imageFile = imageFiles.get(ImageFile.KEY_COVER);
                if (imageFile != null) {
                    return imageFile.getUrl();
                }
            }
        }
        return "";
    }

    /**
     * 从集合中获取对应的图片url地址（首页大卡组件）
     *
     * @param imageFiles ImageFile集合
     */
    public static String getCardBgImage(Map<String, ImageFile> imageFiles) {
        if (imageFiles != null) {
            ImageFile imageFile = imageFiles.get(ImageFile.KEY_BG);
            if (imageFile != null) {
                return imageFile.getUrl();
            }
        }
        return "";
    }
    /**
     * 从集合中获取对应的图片url地址（话题组件）
     *  品牌电台汽车图片
     * @param imageFiles ImageFile集合
     */
    public static String getCardImgImage(Map<String, ImageFile> imageFiles) {
        if (imageFiles != null) {
            ImageFile imageFile = imageFiles.get(ImageFile.KEY_CAR_IMG);
            if (imageFile != null) {
                return imageFile.getUrl();
            }
        }
        return "";
    }
    /**
     * 从集合中获取对应的图片url地址（话题组件）
     *  品牌电台logo图片
     * @param imageFiles ImageFile集合
     */
    public static String getCardLogoImage(Map<String, ImageFile> imageFiles) {
        if (imageFiles != null) {
            ImageFile imageFile = imageFiles.get(ImageFile.KEY_LOGO);
            if (imageFile != null) {
                return imageFile.getUrl();
            }
        }
        return "";
    }
}
