package com.kaolafm.opensdk.api.music.qq;

import android.text.TextUtils;

import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.account.token.QQMusicAccessToken;
import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.music.qq.model.AlbumResult;
import com.kaolafm.opensdk.api.music.qq.model.BaseMusicResult;
import com.kaolafm.opensdk.api.music.qq.model.CategoryLabel;
import com.kaolafm.opensdk.api.music.qq.model.MusicRadioCategory;
import com.kaolafm.opensdk.api.music.qq.model.Song;
import com.kaolafm.opensdk.api.music.qq.model.SongCharts;
import com.kaolafm.opensdk.api.music.qq.model.SongChartsResult;
import com.kaolafm.opensdk.api.music.qq.model.SongMenu;
import com.kaolafm.opensdk.api.music.qq.model.SubcategoryLabel;
import com.kaolafm.opensdk.api.music.qq.model.TencentLoginResult;
import com.kaolafm.opensdk.api.music.qq.model.TencentUserInfo;
import com.kaolafm.opensdk.api.music.qq.model.VipInfo;
import com.kaolafm.opensdk.di.qualifier.QQMusicParam;
import com.kaolafm.opensdk.di.qualifier.QQRefreshParam;
import com.kaolafm.opensdk.di.qualifier.QQUserInfoParam;
import com.kaolafm.opensdk.di.qualifier.WeChatRefreshParam;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;
import javax.inject.Provider;

import dagger.Lazy;

/**
 * QQ音乐网络请求
 *
 * <AUTHOR> Yan
 * @date 2018/8/9
 */

public class QQMusicRequest extends BaseRequest {

    private final QQMusicApiService mQQMusicService;

    @Inject
    @QQMusicParam
    Provider<Map<String, String>> mQQMusicCommonParams;

    @Inject
    @WeChatRefreshParam
    Provider<Map<String, String>> mWxRefreshParamsLazy;

    @Inject
    @QQRefreshParam
    Provider<Map<String, String>> mQQRefreshParamsLazy;

    @Inject
    @QQUserInfoParam
    Provider<Map<String, String>> mQQUserInfoParamsLazy;

    @Inject
    @AppScope
    protected Lazy<AccessTokenManager> mAccessTokenManagerLazy;

    @Inject
    public QQMusicRequest() {
//        mRequestComponent.getQQMusicRequestComponent().inject(this);
        mQQMusicService = obtainRetrofitService(QQMusicApiService.class);
        mUrlManager.putDomain(ApiHostConstants.QQMUSIC_DOMAIN_NAME, ApiHostConstants.QQMUSIC_HOST);
        mUrlManager.addRealtimeParamListener(ApiHostConstants.QQMUSIC_DOMAIN_NAME,
                () -> mQQMusicCommonParams.get());
    }

    //====================================电台============================================
    //====================================歌词============================================

    /**
     * 根据歌曲的songId获取歌词
     */
    public void getLyrics(long songId, HttpCallback<String> callback) {
        doHttpDeal(mQQMusicService.getLyrics(songId), BaseMusicResult::getResult, callback);
    }
    //====================================登录============================================

    /**
     * OPI业务方获取音乐微信二维码url。
     */
    public void getWeChatQRCodeForLogin(HttpCallback<String> callback) {
        doHttpDeal(mQQMusicService.getWeChatQRCodeForLogin(),
                TencentLoginResult::getQrCode, callback);
    }

    /**
     * OPI业务方通过微信用户授权code，登录音乐，并换取、刷新音乐相关凭证接口。
     * 其中返回参数中music_id，music_key用于音乐接口访问的凭证，有具有有效期。
     * 通过微信授权码code，登录音乐，并拿到相应凭证
     */
    public void loginQQMusicWithWechatAuthorizationCode(String wechatCode,
            HttpCallback<TencentUserInfo> callback) {
        HashMap<String, String> params = new HashMap<>();
        params.put("wx_code", String.valueOf(wechatCode));
        params.put("login_type", "5");
        doHttpDeal(mQQMusicService.getQQMusicDocumentsByWeChat(params),
                result -> {
                    if (result != null) {
                        result.setLoginType(QQMusicConstant.LOGIN_TYPE_WECHAT);
                        mAccessTokenManagerLazy.get()
                                .setCurrentAccessToken(new QQMusicAccessToken().setCurrentAccessToken(result));
                        return result.getUserInfo();
                    }
                    return null;
                }, callback);
    }

    /**
     * OPI业务方通过微信用户授权code，登录音乐，并换取、刷新音乐相关凭证接口。
     * 其中返回参数中music_id，music_key用于音乐接口访问的凭证，有具有有效期。
     * 刷新音乐帐号musicid的登录key
     */
    public void refreshWeChatTokenForLogin(HttpCallback<TencentLoginResult> callback) {
        doHttpDeal(mQQMusicService.getQQMusicDocumentsByWeChat(mWxRefreshParamsLazy.get()), callback);
    }

    /**
     * 同步方式请求
     * OPI业务方通过微信用户授权code，登录音乐，并换取、刷新音乐相关凭证接口。
     * 其中返回参数中music_id，music_key用于音乐接口访问的凭证，有具有有效期。
     * 刷新音乐帐号musicid的登录key
     */
    public TencentLoginResult refreshWeChatTokenForLogin() {
        return doHttpDealSync(mQQMusicService.getQQMusicDocumentsByWeChatSynch(mWxRefreshParamsLazy.get()));
    }

    /**
     * 使用qq授权码代用户登录
     */
    public void loginQQMusicWithQQAuthorizationCode(String qqCode, HttpCallback<TencentUserInfo> callback) {
        HashMap<String, String> params = new HashMap<>();
        params.put("cmd", "1");
        params.put("qq_code", qqCode);
        doHttpDeal(mQQMusicService.qqLoginFunctions(params),
                result -> {
                    if (result != null) {
                        result.setLoginType(QQMusicConstant.LOGIN_TYPE_QQ);
                        mAccessTokenManagerLazy.get()
                                .setCurrentAccessToken(new QQMusicAccessToken().setCurrentAccessToken(result));
                        return result.getUserInfo();
                    }
                    return null;
                }, callback);
    }

    /**
     * 刷新用户qq登录凭证
     */
    public void refreshQQTokenForLogin(HttpCallback<TencentLoginResult> callback) {
        doHttpDeal(mQQMusicService.qqLoginFunctions(mQQRefreshParamsLazy.get()), callback);
    }

    /**
     * 同步方式
     * 刷新用户qq登录凭证
     */
    public TencentLoginResult refreshQQTokenForLogin() {
        return doHttpDealSync(mQQMusicService.qqLoginFunctionsSynch(mQQRefreshParamsLazy.get()));
    }

    /**
     * 获取qq用户信息
     */
    public void getQQUserInfo(HttpCallback<TencentUserInfo> callback) {
        doHttpDeal(mQQMusicService.qqLoginFunctions(mQQUserInfoParamsLazy.get()),
                        TencentLoginResult::getUserInfo, callback);
    }

    //====================================歌曲============================================

    /**
     * 通过歌曲mid，获取歌曲的信息。一次访问，最多拉取50首歌曲。
     * 可批量获取，多mid有逗号隔开
     *
     * @param songMids 歌曲mid,多mid用逗号隔开
     */
    public void getSongListBatch(String songMids, HttpCallback<List<Song>> callback) {
        doHttpDeal(mQQMusicService.getSongListBatch(songMids), BaseMusicResult::getResult, callback);
    }

    /**
     * 通过歌曲mid，获取歌曲的信息。一次访问，最多拉取50首歌曲。
     * 可批量获取，多mid有逗号隔开
     * 同步的方式
     *
     * @param songMids 歌曲mid,多mid用逗号隔开
     */
    public BaseMusicResult<List<Song>> getSongListBatch(String songMids) {
        return doHttpDealSync(mQQMusicService.getSongListBatchSynch(songMids));
    }

    //====================================搜索============================================

    //====================================歌单============================================

    /**
     * 获取歌单广场歌单列表
     *
     * @param start    起始页
     * @param size     每页大小
     * @param order    顺序，2：最新，5：最热
     * @param callback 请求回调
     */
    public void getSongMenuListOfSquare(int start, int size, int order, HttpCallback<List<SongMenu>> callback) {
        doHttpDeal(mQQMusicService.getSongMenuListOfSquare(start, size, order),
                BaseMusicResult::getResult, callback);
    }

    /**
     * 歌单广场的歌单进行收藏、取消收藏操作，同时可获取用户收藏歌单。
     *
     * @param operateType 操作类型，1：收藏，2取消收藏，3 获取收藏歌单
     * @param dissId      要收藏/取消收藏的歌单id
     */
    public void operateSongMenuOfSquare(int operateType, String dissId,
            HttpCallback<BaseMusicResult<List<SongMenu>>> callback) {
        doHttpDeal(mQQMusicService.opareteSongMenuOfSquare(operateType, dissId), callback);
    }

    /**
     * 收藏/取消收藏歌单
     *
     * @param collect true收藏，false取消收藏
     * @param dissId  要收藏/取消收藏的歌单id
     */
    public void collectSongMenu(boolean collect, String dissId, HttpCallback<List<SongMenu>> callback) {
        operateSongMenuOfSquare(collect ? 1 : 2, dissId, new HttpCallback<BaseMusicResult<List<SongMenu>>>() {
            @Override
            public void onSuccess(BaseMusicResult<List<SongMenu>> baseMusicResult) {
                if (baseMusicResult.getCode() == 0) {
                    getFavoriteSongMenuList(callback);
                } else {
                    callback.onError(new ApiException("失败"));
                }
            }

            @Override
            public void onError(ApiException exception) {
                callback.onError(exception);
            }
        });
    }

    /**
     * 获取用户收藏的歌单
     */
    public void getFavoriteSongMenuList(HttpCallback<List<SongMenu>> callback) {
        operateSongMenuOfSquare(3, null, new HttpCallback<BaseMusicResult<List<SongMenu>>>() {
            @Override
            public void onSuccess(BaseMusicResult<List<SongMenu>> baseMusicResult) {
                callback.onSuccess(baseMusicResult.getResult());
            }

            @Override
            public void onError(ApiException exception) {
                callback.onError(exception);
            }
        });
    }

    /**
     * 获取歌单中歌曲列表。（由于歌单歌曲数不可控，目前只返回简要索引信息，播放再请求获取批量歌曲信息接口{@link #getSongListBatch(String, HttpCallback)} }
     *
     * @param dissId 要操作的歌单id
     */
    public void getSongListOfSongMenu(long dissId, HttpCallback<List<Song>> callback) {
        doHttpDeal(mQQMusicService.getSongListOfSongMenu(dissId), BaseMusicResult::getResult, callback);
    }

    /**
     * 获取个人歌单列表
     */
    public void getSelfSongMenuList(HttpCallback<List<SongMenu>> callback) {
        doHttpDeal(mQQMusicService.getSelfSongMenuList(), BaseMusicResult::getResult, callback);
    }

    /**
     * 获取<我喜欢>的歌单id
     */
    public void getMyLikeId(HttpCallback<Long> callback) {
        getSelfSongMenuList(new HttpCallback<List<SongMenu>>() {
            @Override
            public void onSuccess(List<SongMenu> songMenus) {
                if (songMenus != null && songMenus.size() > 0) {
                    SongMenu songMenu = songMenus.get(0);
                    if (TextUtils.equals(songMenu.getDissName(), "我喜欢")) {
                        if (callback != null) {
                            callback.onSuccess(songMenu.getDissId());
                        }
                        return;
                    }
                }
                if (callback != null) {
                    callback.onError(new ApiException("id为空"));
                }
            }

            @Override
            public void onError(ApiException exception) {
                if (callback != null) {
                    callback.onError(exception);
                }
            }
        });
    }

    /**
     * 歌单中增加/删除歌曲
     *
     * @param cmd     操作类型，1：增加，2删除
     * @param dissId  要操作的歌单id
     * @param songIds 歌曲id，多个歌曲id用逗号分隔
     */
    public void operateSongOfSongMenu(int cmd, long dissId, String songIds, HttpCallback<Boolean> callback) {
        doHttpDeal(mQQMusicService.opareteSongOfSongMenu(cmd, dissId, songIds),
                        baseMusicResult -> baseMusicResult.getCode() == 0, callback);
    }

    /**
     * 喜欢这首歌
     */
    public void likeThis(long myLikeId, String songId, HttpCallback<Boolean> callback) {
        if (myLikeId != 0) {
            operateSongOfSongMenu(1, myLikeId, songId, callback);
            //上报喜欢
//            PlayItem curPlayItem = MusicListManager.getInstance().getCurPlayItem();
//            reportCollectSong(curPlayItem.getPosition() / 1000, curPlayItem.getAudioId(),
//                    curPlayItem.getRecommendReason());
        }
    }

    /**
     * 不喜欢这首歌
     */
    public void dislikeThis(long myLikeId, String songId, HttpCallback<Boolean> callback) {
        if (myLikeId != 0) {
            operateSongOfSongMenu(2, myLikeId, songId, callback);
//            //上报不喜欢
//            PlayItem curPlayItem = MusicListManager.getInstance().getCurPlayItem();
//            reportDeleteSong(curPlayItem.getPosition() / 1000, curPlayItem.getAudioId(),
//                    curPlayItem.getRecommendReason());
        }
    }

    //====================================公共电台=========================================

    /**
     * 获取公共电台列表，获取的结果是分类的。
     */
    public void getPublicRadioList(HttpCallback<List<MusicRadioCategory>> callback) {
        doHttpDeal(mQQMusicService.getPublicRadioList(), BaseMusicResult::getResult, callback);
    }

    /***
     * 根据电台id获取电台歌曲列表
     * @param radioId 电台id
     * @param callback
     */
    public void getSongListOfRadio(long radioId, HttpCallback<List<Song>> callback) {
        doHttpDeal(mQQMusicService.getSongListOfRadio(radioId), BaseMusicResult::getResult, callback);
    }

    //====================================个性化推荐========================================

    /**
     * 获取每日推荐30首
     */
    public void getDayRecommendSongs(HttpCallback<List<Song>> callback) {
        doHttpDeal(mQQMusicService.getDayRecommendSongs(), BaseMusicResult::getResult, callback);
    }

    /**
     * 获取个人电台
     */
    public void getIndividualSongsOfRadio(HttpCallback<List<Song>> callback) {
        doHttpDeal(mQQMusicService.getIndividualSongsOfRadio("get_recommand_song"),
                        BaseMusicResult::getResult,
                        callback);
    }

    /**
     * 推荐上报操作
     *
     * @param operType   操作类型
     * @param listenTime 表示上报时当前歌听的播放时长，单位s
     * @param songId     表示上报的歌曲id
     * @param pingpong   表示获取到歌曲的推荐码（代表推荐原因。oper_type =get_recommand_song，获得歌曲时，每首歌下面有返回码pingpong
     */
    public void reportOperateForRecommand(String operType, long listenTime, long songId, String pingpong,
            HttpCallback<BaseMusicResult> callback) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("oper_type", operType);
        params.put("repo_listen_time", listenTime);
        params.put("repo_song_id", songId);
        if (!TextUtils.isEmpty(pingpong)) {
            params.put("repo_pingpong", pingpong);
        }
        doHttpDeal(mQQMusicService.reportOperateForRecommand(params), callback);
    }

    /**
     * 上报收藏（我喜欢）
     *
     * @param listenTime 表示上报时当前歌听的播放时长，单位s
     * @param songId     表示上报的歌曲Id
     * @param pingpong   表示获取到歌曲的推荐码（代表推荐原因。oper_type =get_recommand_song，获得歌曲时，每首歌下面有返回码pingpong
     */
    public void reportCollectSong(long listenTime, long songId, String pingpong) {
        reportOperateForRecommand("report_collect_song", listenTime, songId, pingpong, null);
    }

    /**
     * 上报删除（不喜欢）
     *
     * @param listenTime 表示上报时当前歌听的播放时长，单位s
     * @param songId     表示上报的歌曲Id
     * @param pingpong   表示获取到歌曲的推荐码（代表推荐原因。oper_type =get_recommand_song，获得歌曲时，每首歌下面有返回码pingpong
     */
    public void reportDeleteSong(long listenTime, long songId, String pingpong) {
        reportOperateForRecommand("report_delete_song", listenTime, songId, pingpong, null);
    }

    /**
     * 上报播放下一首
     *
     * @param listenTime 表示上报时当前歌听的播放时长，单位s
     * @param songId     表示上报的歌曲Id
     * @param pingpong   表示获取到歌曲的推荐码（代表推荐原因。oper_type =get_recommand_song，获得歌曲时，每首歌下面有返回码pingpong
     */
    public void reportPlayNextSong(long listenTime, long songId, String pingpong) {
        reportOperateForRecommand("report_play_next_song", listenTime, songId, pingpong,
                null);
    }

    /**
     * 上报完整收听
     *
     * @param listenTime 表示上报时当前歌听的播放时长，单位s
     * @param songId     表示上报的歌曲Id
     * @param pingpong   表示获取到歌曲的推荐码（代表推荐原因。oper_type =get_recommand_song，获得歌曲时，每首歌下面有返回码pingpong
     */
    public void reportListenToEnd(long listenTime, long songId, String pingpong) {
        reportOperateForRecommand("report_listen_to_end", listenTime, songId, pingpong, null);
    }

    //====================================排行榜===========================================

    /**
     * 获取榜单列表
     */
    public void getSongChartsList(HttpCallback<List<SongCharts>> callback) {
        doHttpDeal(mQQMusicService.getSongChartsList(), BaseMusicResult::getResult, callback);
    }

    /**
     * 获取qq音乐的排行榜的歌单的歌曲列表。
     *
     * @param chartsId 榜单id
     */
    public void getSongListOfSongCharts(long chartsId, HttpCallback<SongChartsResult> callback) {
        doHttpDeal(mQQMusicService.getSongListOfSongCharts(chartsId), callback);
    }

    //====================================专辑============================================
    //====================================歌手============================================
    //====================================分类============================================

    /**
     * 提供拉取音乐馆分类标签，分类子标签，分类歌曲，分类专辑等四功能
     *
     * @param params oper_type 操作类型，取值如下：<br/>
     *               get_category_group：获取主分类标签<br/>
     *               get_category_detail：获取标签详情，同时若该标签下有子标签，返回子标签信息<br/>
     *               get_category_song：获取分类标签下的歌曲信息<br/>
     *               get_category_album：获取分类标签下的专辑信息<br/>
     *               order 排序方式，取值如下：<br/>
     *               0：最新
     *               1：最热<br/>
     *               category_id 分类标签id<br/>
     *               pagenum 页索引<br/>
     *               pagesize 每页信息条目数<br/>
     */
    public void getCategoryLibrary(Map<String, Object> params, HttpCallback<BaseMusicResult> callback) {

        doHttpDeal(mQQMusicService.getCategoryLibrary(params), callback);
    }

    /**
     * 获取主分类标签
     */
    public void getMainCategoryLabels(HttpCallback<List<CategoryLabel>> callback) {
        doHttpDeal(mQQMusicService.getMainCategoryLabels("get_category_group"),
                BaseMusicResult::getResult, callback);
    }

    /**
     * 获取标签详情，同时若该标签下有子标签，返回子标签信息
     */
    public void getCategoryDetail(long categoryId, HttpCallback<SubcategoryLabel> callback) {
        doHttpDeal(mQQMusicService.getCategoryDetail("get_category_detail", categoryId),
                        BaseMusicResult::getResult, callback);
    }

    /**
     * 获取分类标签下歌曲
     * 每次请求随机返回该分类下的200首歌曲。
     */
    public void getSongListOfCategoryLabel(long categoryId, HttpCallback<List<Song>> callback) {
        doHttpDeal(mQQMusicService.getSongListOfCategoryLabel("get_category_song", categoryId),
                        BaseMusicResult::getResult, callback);
    }

    /**
     * 获取分类标签下的专辑信息
     * 分页返回专辑列表。
     */
    public void getAlbumListOfCategoryLabel(long categoryId, int pageNum, int pageSize,
            HttpCallback<List<AlbumResult>> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("oper_type", "get_category_album");
        params.put("category_id", categoryId);
        params.put("pagenum", pageNum);
        params.put("pagesize", pageSize);
        doHttpDeal(mQQMusicService.getAlbumListOfCategoryLabel(params), BaseMusicResult::getResult,
                        callback);
    }

    //====================================用户信息=========================================

    /**
     * 获取用户会员信息
     */
    public void getUserVipInfo(HttpCallback<VipInfo> callback) {
        doHttpDeal(mQQMusicService.getUserVipInfo(), BaseMusicResult::getResult, callback);
    }

    public Map<String, String> getCommonParams() {
        return mQQMusicCommonParams.get();
    }
}
