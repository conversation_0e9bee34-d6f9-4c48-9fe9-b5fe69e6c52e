package com.kaolafm.opensdk.api.feature;

import androidx.annotation.IntDef;

import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.feature.model.FeatureAudioDetails;
import com.kaolafm.opensdk.api.feature.model.FeatureDetails;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.List;

public class FeatureRequest extends BaseRequest {
    private FeatureService mService;

    public FeatureRequest() {
        mService = obtainRetrofitService(FeatureService.class);
    }

    /**
     * 获取专题详情
     *
     * @param featureId 专题Id
     * @param callback  回调
     */
    public void getFeatureDetails(long featureId, HttpCallback<FeatureDetails> callback) {
        doHttpDeal(mService.getFeatureDetails(featureId), baseResult -> {
            FeatureDetails featureDetails = baseResult.getResult();
            return featureDetails;
        }, callback);
    }

    /**
     * 正序
     */
    private static final int SORT_ACS = 1;

    /**
     * 倒序
     */
    private static final int SORT_DESC = -1;

    @IntDef({SORT_ACS, SORT_DESC})
    public @interface Sort {

    }

    /**
     * 请求播单
     *
     * @param featureId 专题id
     * @param audioId   单曲ID: 根据单曲id定位分页（此参数可传0）
     * @param sort      -1倒序 1正序
     * @param pageSize  请求页码 1, 2, 3...
     * @param pageNum   每页条目数
     * @param callback  回调
     */
    public void getPlaylist(long featureId, long audioId, @FeatureRequest.Sort int sort, int pageSize, int pageNum,
                            HttpCallback<BasePageResult<List<FeatureAudioDetails>>> callback) {
        doHttpDeal(mService.getPlaylist(featureId, sort, pageSize, pageNum, audioId), BaseResult::getResult, callback);
    }

    /**
     * 请求播单
     *
     * @param featureId 专题id
     * @param sort      -1倒序 1正序
     * @param pageSize  请求页码 1, 2, 3...
     * @param pageNum   每页条目数
     * @param callback  回调
     */
    public void getPlaylist(long featureId, @FeatureRequest.Sort int sort, int pageSize, int pageNum,
                            HttpCallback<BasePageResult<List<FeatureAudioDetails>>> callback) {
        getPlaylist(featureId, 0, sort, pageSize, pageNum, callback);
    }
}
