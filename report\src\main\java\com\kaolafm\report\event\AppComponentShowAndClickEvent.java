package com.kaolafm.report.event;

import com.kaolafm.report.util.ReportConstants;

/**
 * app内首页组件点击、曝光事件
 * 蔡佳彬  2120
 */
public class AppComponentShowAndClickEvent extends BaseReportEventBean {
    private String mold;//click:点击 exposure:曝光
    /**
     * 1-2+1组件
     * 2-2+1组件
     * 1-2+1组件
     * 1-2+1组件
     * 1-2+1组件
     * 1-2+1组件
     * 1-2+1组件
     */
    private String cardid;//组件的编码id
    private String tag; //标签精品 vip 无
    private String action; //点击类型 jump：跳转 play：播放
    private String radioid; //专辑、专题等合集的id
    private String audioid; //上报单曲id
    private String pageid; //页面编码id
    private String column_code; //栏目id
    private String memberorder; //服务端吐出的位置顺序
    private String placeorder; //组件内的位置
    private String paytype; //付费类型


    public AppComponentShowAndClickEvent() {
        setEventcode(ReportConstants.EVENT_COMPONENT_SHOW_AND_CLICK);
    }

    public String getMold() {
        return mold;
    }

    public void setMold(String mold) {
        this.mold = mold;
    }

    public String getCardid() {
        return cardid;
    }

    public void setCardid(String cardid) {
        this.cardid = cardid;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getRadioid() {
        return radioid;
    }

    public void setRadioid(String radioid) {
        this.radioid = radioid;
    }

    public String getAudioid() {
        return audioid;
    }

    public void setAudioid(String audioid) {
        this.audioid = audioid;
    }

    public String getPageid() {
        return pageid;
    }

    public void setPageid(String pageid) {
        this.pageid = pageid;
    }

    public String getColumn_code() {
        return column_code;
    }

    public void setColumn_code(String column_code) {
        this.column_code = column_code;
    }

    public String getMemberorder() {
        return memberorder;
    }

    public void setMemberorder(String memberorder) {
        this.memberorder = memberorder;
    }

    public String getPlaceorder() {
        return placeorder;
    }

    public void setPlaceorder(String placeorder) {
        this.placeorder = placeorder;
    }

    public String getPaytype() {
        return paytype;
    }

    public void setPaytype(String paytype) {
        this.paytype = paytype;
    }
}
