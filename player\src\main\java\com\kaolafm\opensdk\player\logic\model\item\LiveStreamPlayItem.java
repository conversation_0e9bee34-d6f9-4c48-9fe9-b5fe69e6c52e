package com.kaolafm.opensdk.player.logic.model.item;

import android.os.Parcel;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.player.logic.model.item.model.InfoData;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.RadioInfoData;
import com.kaolafm.opensdk.player.logic.model.item.model.TimeInfoData;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

/**
 * 直播入流
 */
public class LiveStreamPlayItem extends PlayItem {

    public static final int NoLive = 0;
    public static final int Living = 1;
    public static final int PlaybackGenerating = 2;
    public static final int PlaybackAvailable = 3;

    /**
     * 信息相关数据
     */
    private InfoData mInfoData;

    /**
     * 播放时间相关数据
     */
    private TimeInfoData mTimeInfoData;

    /**
     * 电台相关数据
     */
    private RadioInfoData mRadioInfoData;

    /**
     * 聊天室id
     */
    private long liveId;

    /**
     * 直播状态
     */
    private int status;

    /**
     * 主持人id
     */
    private long comperesId;

    public LiveStreamPlayItem() {
        mInfoData = new InfoData();
        mTimeInfoData = new TimeInfoData();
        mRadioInfoData = new RadioInfoData();
    }

    @Override
    public String getRadioId() {
        return String.valueOf(mRadioInfoData.getRadioId());
    }

    @Override
    public String getTitle() {
        String title = mInfoData.getTitle();
        if(StringUtil.isEmpty(title)){
            title = mInfoData.getAlbumName();
        }
        return title;
    }

    @Override
    public String getPicUrl() {
        String picUrl = mInfoData.getAudioPic();
        if (StringUtil.isEmpty(picUrl)) {
            picUrl = mInfoData.getAlbumPic();
        }
        return picUrl;
    }

    @Override
    public String getHost() {
        return mInfoData.getHosts();
    }

    @Override
    public String getAlbumTitle() {
        return mInfoData.getAlbumName();
    }

    @Override
    public String getAlbumId() {
        return String.valueOf(mInfoData.getAlbumId());
    }

    @Override
    public String getBeginTime() {
        return mTimeInfoData.getBeginTime();
    }

    @Override
    public String getUpdateTime() {
        return mInfoData.getUpdateTime();
    }

    @Override
    public String getEndTime() {
        return mTimeInfoData.getEndTime();
    }

    @Override
    public int getType() {
        return PlayerConstants.RESOURCES_TYPE_LIVE_STREAM;
    }

    public InfoData getInfoData() {
        return mInfoData;
    }

    public void setInfoData(InfoData infoData) {
        this.mInfoData = infoData;
    }

    public TimeInfoData getTimeInfoData() {
        return mTimeInfoData;
    }

    public void setTimeInfoData(TimeInfoData timeInfoData) {
        this.mTimeInfoData = timeInfoData;
    }

    public long getLiveId() {
        return liveId;
    }

    public void setLiveId(long liveId) {
        this.liveId = liveId;
    }

    @Override
    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public long getComperesId() {
        return comperesId;
    }

    public void setComperesId(long comperesId) {
        this.comperesId = comperesId;
    }

    @Override
    public String getRadioName() {
        return mRadioInfoData.getRadioName();
    }

    public RadioInfoData getRadioInfoData() {
        return mRadioInfoData;
    }

    public void setRadioInfoData(RadioInfoData radioInfoData) {
        this.mRadioInfoData = radioInfoData;
    }

    @Override
    public boolean isLiving() {
        return status == Living;
    }

    private LiveStreamPlayItem(Parcel parcel) {

    }

    public static final Creator<LiveStreamPlayItem> CREATOR = new Creator<LiveStreamPlayItem>() {

        @Override
        public LiveStreamPlayItem createFromParcel(Parcel source) {
            return new LiveStreamPlayItem(source);
        }

        @Override
        public LiveStreamPlayItem[] newArray(int size) {
            return new LiveStreamPlayItem[size];
        }
    };


    @Override
    public long getFinishTime() {
        return mTimeInfoData.getFinishTime();
    }
}
