package com.kaolafm.opensdk.http.socket.parser;


import android.text.TextUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

public class ParseQS {

    private ParseQS() {
    }

    public static String encode(Map<String, String> obj) {
        StringBuilder str = new StringBuilder();
        for (Map.Entry<String, String> entry : obj.entrySet()) {
            if (str.length() > 0) {
                str.append("&");
            }
            str.append(encodeURIComponent(entry.getKey()))
                    .append("=")
                    .append(encodeURIComponent(entry.getValue()));
        }
        return str.toString();
    }

    public static Map<String, String> decode(String qs) {
        String[] pairs = qs.split("&");
        Map<String, String> qry = new HashMap<>(pairs.length);
        for (String pair : pairs) {
            String[] pairPair = pair.split("=");
            qry.put(decodeURIComponent(pairPair[0]), pairPair.length > 1 ? decodeURIComponent(pairPair[1]) : "");
        }
        return qry;
    }

    public static String encodeURIComponent(String str) {
        try {
            if (TextUtils.isEmpty(str)) {
                return "";
            }
            return URLEncoder.encode(str, "UTF-8")
                    .replace("+", "%20")
                    .replace("%21", "!")
                    .replace("%27", "'")
                    .replace("%28", "(")
                    .replace("%29", ")")
                    .replace("%7E", "~");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    public static String decodeURIComponent(String str) {
        try {
            return URLDecoder.decode(str, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }
}
