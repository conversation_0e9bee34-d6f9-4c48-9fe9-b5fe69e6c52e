package com.kaolafm.opensdk.api.search.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 文本搜索返回的节目数据
 *
 * <AUTHOR>
 * @date 2018/8/7
 */

public class SearchProgramBean extends VoiceSearchProgramBean implements Parcelable {

    /**
     {
     "id": 1600000000301,
     "name": "楚天交通",
     "img": "https://iovimg.radio.cn/mz/images/202111/900065f8-11b1-446e-a516-056fc316ef77/default.png",
     "type": 11,
     "albumName": "楚天交通",
     "source": 167406,
     "duration": 0,
     "playUrl": "https://liveplay.kaolafm.net/fm/1600000000301/playlist.m3u8",
     "oldId": 0,
     "sourceName": "Unknown",
     "callback": "0_analyzer_00_1660706489212861",
     "fine": 0,
     "vip": 0,
     "audition": 0,
     "totalNumber": 0,
     "freq": "FM92.7",
     "host": [],
     "listenNum": 0,
     "isShowRed": 0,
     "isRequest": 0,
     "originalDuration": 0,
     "highlight": [{
     "field": "name",
     "start": -1,
     "offset": -1,
     "token": "中国交通广播"
     }, {
     "field": "name",
     "start": -1,
     "offset": -1,
     "token": "中国"
     }, {
     "field": "name",
     "start": -1,
     "offset": -1,
     "token": "国交"
     }, {
     "field": "name",
     "start": -1,
     "offset": -1,
     "token": "交通广播"
     }, {
     "field": "name",
     "start": -1,
     "offset": -1,
     "token": "交通"
     }, {
     "field": "name",
     "start": -1,
     "offset": -1,
     "token": "通广播"
     }, {
     "field": "name",
     "start": -1,
     "offset": -1,
     "token": "广播"
     }]
     }
     */

    /** 播放量 */
    @SerializedName("listenNum")
    private Long listenNum;

    /** 高亮展示 */
    @SerializedName("highlight")
    private List<HighLightWord> highlight;

    /**
     * 广播/听电视是否有正在播放的节目单，
     * 如果有可以通过 {@link com.kaolafm.opensdk.api.broadcast.BroadcastRequest#getBroadcastCurrentProgramDetails(long, HttpCallback)}请求正在播放的节目信息。0：没有，1：有
     */
    @SerializedName("programEnable")
    private int programEnable;


    public SearchProgramBean() {
    }

    protected SearchProgramBean(Parcel in) {
        super(in);
        if (in.readByte() == 0) {
            listenNum = null;
        } else {
            listenNum = in.readLong();
        }
        highlight = in.createTypedArrayList(HighLightWord.CREATOR);
        programEnable = in.readInt();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        super.writeToParcel(dest, flags);
        if (listenNum == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeLong(listenNum);
        }
        dest.writeTypedList(highlight);
        dest.writeInt(programEnable);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<SearchProgramBean> CREATOR = new Creator<SearchProgramBean>() {
        @Override
        public SearchProgramBean createFromParcel(Parcel in) {
            return new SearchProgramBean(in);
        }

        @Override
        public SearchProgramBean[] newArray(int size) {
            return new SearchProgramBean[size];
        }
    };

    public Long getListenNum() {
        return listenNum;
    }

    public void setListenNum(Long listenNum) {
        this.listenNum = listenNum;
    }

    public List<HighLightWord> getHighlight() {
        return highlight;
    }

    public void setHighlight(List<HighLightWord> highlight) {
        this.highlight = highlight;
    }

    public int getProgramEnable() {
        return programEnable;
    }

    public void setProgramEnable(int programEnable) {
        this.programEnable = programEnable;
    }

    @Override
    public String toString() {
        return "SearchProgramBean{" +
                "listenNum=" + listenNum +
                ", highlight=" + highlight +
                ", id=" + id +
                ", name='" + name + '\'' +
                ", img='" + img + '\'' +
                ", comperes=" + comperes +
                ", localFlag='" + localFlag + '\'' +
                ", type=" + type +
                ", albumName='" + albumName + '\'' +
                ", duration=" + duration +
                ", playUrl='" + playUrl + '\'' +
                ", vip=" + vip +
                ", fine=" + fine +
                ", audition=" + audition +
                ", freq=" + freq +
                ", programEnable=" + programEnable +
                ", callback='" + callback +
                '}';
    }
}
