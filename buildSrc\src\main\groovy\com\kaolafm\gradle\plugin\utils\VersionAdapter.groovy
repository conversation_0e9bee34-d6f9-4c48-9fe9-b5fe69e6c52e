package com.kaolafm.gradle.plugin.utils

import com.android.build.gradle.api.LibraryVariant
import com.kaolafm.gradle.plugin.utils.Util
import org.gradle.api.Project
import org.gradle.api.Task
import org.gradle.api.artifacts.Dependency
import org.gradle.api.file.ConfigurableFileCollection
/**
 * <AUTHOR> on 2019/7/16.
 */
class VersionAdapter {

    private Project mProject

    private LibraryVariant mVariant

    private String mGradlePluginVersion

    VersionAdapter(Project project, LibraryVariant variant) {
        mProject = project
        mVariant = variant
        // gradle version
        project.rootProject.buildscript.getConfigurations().getByName("classpath").getDependencies().each { Dependency dep ->
            if (dep.group == "com.android.tools.build" && dep.name == "gradle") {
                mGradlePluginVersion = dep.version
            }
        }
        if (mGradlePluginVersion == null) {
            throw new IllegalStateException("com.android.tools.build:gradle is no set in the root build.gradle file")
        }
    }

    String getGradleVersion() {
        return mGradlePluginVersion
    }

    ConfigurableFileCollection getClassPathDirFiles() {
        ConfigurableFileCollection classpath
        if (Util.compareVersion(mGradlePluginVersion, "3.5.0") >= 0) {
            classpath = mProject.files("${mProject.buildDir.path}/intermediates/" +
                    "javac/${mVariant.name}/classes")
        } else if (Util.compareVersion(mGradlePluginVersion, "3.2.0") >= 0) { // >= Versions 3.2.X
            classpath = mProject.files("${mProject.buildDir.path}/intermediates/" +
                    "javac/${mVariant.name}/compile${mVariant.name.capitalize()}JavaWithJavac/classes")
        } else { // Versions 3.0.x and 3.1.x
            classpath = mProject.files("${mProject.buildDir.path}/intermediates/classes/${mVariant.dirName}")
        }
        return classpath
    }

    ConfigurableFileCollection getRClassPath() {
        if (Util.compareVersion(mGradlePluginVersion, "3.5.0") >= 0) {
            return mProject.files("${mProject.buildDir.path}/intermediates/" + "compile_only_not_namespaced_r_class_jar/"
                    + "${mVariant.name}")
        } else if (Util.compareVersion(mGradlePluginVersion, "3.3.0") >= 0) {
            return mProject.files("${mProject.buildDir.path}/intermediates/" + "compile_only_not_namespaced_r_class_jar/"
                    + "${mVariant.name}/generate${mVariant.name.capitalize()}RFile")
        } else {
            return getClassPathDirFiles()
        }
    }

    File getLibsDirFile() {
        if (Util.compareVersion(mGradlePluginVersion, '3.6.0') >= 0) {
            return mProject.file("${mProject.buildDir.path}/intermediates/aar_libs_directory/${mVariant.dirName}/libs")
        } else if (Util.compareVersion(mGradlePluginVersion, '3.1.0') >= 0) {
            return mProject.file(mProject.buildDir.path + '/intermediates/packaged-classes/' + mVariant.dirName + "/libs")
        } else {
            return mProject.file(mProject.buildDir.path + '/intermediates/bundles/' + mVariant.dirName + "/libs")
        }
    }

    Task getJavaCompileTask() {
        return mVariant.getJavaCompileProvider().get()
    }

    Task getProcessManifest() {
        return mVariant.getOutputs().first().getProcessManifestProvider().get()

    }

    Task getMergeAssets() {
        return mVariant.getMergeAssetsProvider().get()
    }

    File getSymbolFile() {
        if (Util.compareVersion(mGradlePluginVersion, "3.6.0") >= 0) {
            return mProject.file(mProject.buildDir.path + '/intermediates/compile_symbol_list/' + mVariant.dirName + "/R.txt")
        } else if (Util.compareVersion(mGradlePluginVersion, "3.1.0") >= 0) {
            return mProject.file(mProject.buildDir.path + '/intermediates/symbols/' + mVariant.dirName + "/R.txt")
        } else {
            return mProject.file(mProject.buildDir.path + '/intermediates/bundles/' + mVariant.name + "/R.txt")
        }
    }

    String getSyncLibJarsTaskPath() {
        if (Util.compareVersion(mGradlePluginVersion, '3.6.0') >= 0) {
            return "sync${mVariant.name.capitalize()}LibJars"
        } else {
            return "transformClassesAndResourcesWithSyncLibJarsFor${mVariant.name.capitalize()}"
        }
    }

    File getUnzipDir() {
        return mProject.file("${mProject.getBuildDir()}/outputs/aar-R/${mVariant.dirName}")
    }

    File getJarDir() {
        return mProject.file("${mProject.getBuildDir()}/outputs/aar-R/${mVariant.dirName}/libs")
    }

    File getAarOutputDir() {
        // aar output dir
        return mProject.file("${mProject.getBuildDir()}/outputs/aar/")
    }

    String getAarOutputPath() {
        return mVariant.outputs.first().outputFile.absolutePath
    }


}
