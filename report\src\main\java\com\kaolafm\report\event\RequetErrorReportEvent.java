package com.kaolafm.report.event;


import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> on 2019/1/21.
 * 请求错误数据上报
 */

public class RequetErrorReportEvent extends BaseReportEventBean {
    /**
     * http请求状态码
     */
    private String result;
    /**
     * 网络情况	0：有网，1：无网
     */
    private String speed = "0";
    /**
     * 音频URL
     */
    private String url;
    /**
     * 错误信息	根据不同的情况，可以考虑加入错误信息或程序抛出的Exception
     */
    private String message;
    private String audioid;
    private String radioid;
    /**
     * 当前资源ip
     */
    private String remarks1;
    /**
     * dns
     */
    private String remarks2;
    /**
     * 请求失败原因	1：资源获取失败；2：播放失败
     */
    private String remarks3;

    public RequetErrorReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_REQUEST_ERROR);
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getSpeed() {
        return speed;
    }

    public void setSpeed(String speed) {
        this.speed = speed;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getAudioid() {
        return audioid;
    }

    public void setAudioid(String audioid) {
        this.audioid = audioid;
    }

    public String getRadioid() {
        return radioid;
    }

    public void setRadioid(String radioid) {
        this.radioid = radioid;
    }

    public String getRemarks1() {
        return remarks1;
    }

    public void setRemarks1(String remarks1) {
        this.remarks1 = remarks1;
    }

    public String getRemarks2() {
        return remarks2;
    }

    public void setRemarks2(String remarks2) {
        this.remarks2 = remarks2;
    }

    public String getRemarks3() {
        return remarks3;
    }

    public void setRemarks3(String remarks3) {
        this.remarks3 = remarks3;
    }
}
