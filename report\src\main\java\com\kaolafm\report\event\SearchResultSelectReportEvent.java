package com.kaolafm.report.event;


import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> on 2019/1/21.
 * 搜索结果选择
 */

public class SearchResultSelectReportEvent extends BaseReportEventBean {
    public static final String WAY_TYPE_AUDIO = "1";
    public static final String WAY_TYPE_HAND = "2";
    public static final String WAY_TYPE_UN_KNOW = "3";

    /**
     * 选择方式 1.语音选择；2.手动选择；3.未知
     */
    private String way = WAY_TYPE_UN_KNOW;
    /**
     * 选择结果内容对应的radioid，单曲则为专辑id，ai电台为电台id，专辑为专辑id
     */
    private String radioid;
    /**
     * 对应选择的结果内容id：单曲id，专辑id、ai电台id、广播id（至少一个）
     */
    private String remarks2;
    /**
     * 被选择的结果索引号：1，2，3，4
     */
    private String remarks3;
    /**
     * 搜索服务端透传的数据
     */
    private String remarks9;

    public SearchResultSelectReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_SEARCH_RESULT_SELECT);
    }

    public String getWay() {
        return way;
    }

    public void setWay(String way) {
        this.way = way;
    }

    public String getRadioid() {
        return radioid;
    }

    public void setRadioid(String radioid) {
        this.radioid = radioid;
    }

    public String getRemarks2() {
        return remarks2;
    }

    public void setRemarks2(String remarks2) {
        this.remarks2 = remarks2;
    }

    public String getRemarks3() {
        return remarks3;
    }

    public void setRemarks3(String remarks3) {
        this.remarks3 = remarks3;
    }

    public String getRemarks9() {
        return remarks9;
    }

    public void setRemarks9(String remarks9) {
        this.remarks9 = remarks9;
    }
}
