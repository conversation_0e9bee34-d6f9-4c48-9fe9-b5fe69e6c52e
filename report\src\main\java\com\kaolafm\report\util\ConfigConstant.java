package com.kaolafm.report.util;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.database.ConfigData;
import com.kaolafm.report.database.greendao.DaoMaster;
import com.kaolafm.report.database.greendao.DaoSession;

import org.greenrobot.greendao.database.Database;

import java.util.List;

import io.reactivex.Single;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

/**
 * <AUTHOR> on 2019/2/18.
 */

public class ConfigConstant {
    public static final int TYPE_PLAY_PARAMETER = 1;
    public static final int TYPE_REPORT_PRIVATE_PARAMETER = 2;
    public static final int TYPE_REPORT_CAR_PARAMETER = 3;
}
