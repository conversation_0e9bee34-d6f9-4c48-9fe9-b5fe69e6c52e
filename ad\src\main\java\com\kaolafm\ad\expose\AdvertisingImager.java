package com.kaolafm.ad.expose;

import com.kaolafm.ad.api.model.ImageAdvert;
import com.kaolafm.ad.api.model.InteractionAdvert;

/**
 * 图片广告接口
 *
 * <AUTHOR>
 * @date 2020-01-09
 */
public interface AdvertisingImager extends Executor {

    /**
     * 展示图片广告。所有的图片广告都会通过该方法曝光。
     * @param advert
     */
    void display(ImageAdvert advert);

    /**
     * 关闭图片广告。所有的图片广告都会通过该方法关闭。
     * @param advert
     */
    void hide(ImageAdvert advert);

    /**
     * 跳过广告。
     */
    void skip(ImageAdvert advert);

    /**
     * 展示二次互动。所有的二次互动广告都会通过该方法展示。
     * @param advert
     */
    void displayInteraction(InteractionAdvert advert);

    /**
     * 关闭二次互动。所有的二次互动广告都会通过该方法关闭。
     * @param advert
     */
    void hideInteraction(InteractionAdvert advert);

    /**
     * 点击广告。
     */
    void click(InteractionAdvert advert);
}
