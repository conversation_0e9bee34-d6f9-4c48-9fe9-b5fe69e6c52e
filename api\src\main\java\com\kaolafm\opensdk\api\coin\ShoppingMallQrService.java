package com.kaolafm.opensdk.api.coin;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Query;


/**
 * <AUTHOR>
 **/
interface ShoppingMallQrService {

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_QRCODE_SHOPPING_MALL)
    Single<BaseResult<String>> getQrOfShoppingMall(@Query("width") int width);
}


