package com.kaolafm.opensdk.http.socket;

/**
 * 读取状态
 *
 * <AUTHOR>
 * @date 2020-01-07
 */
public enum ReadyState {
    /**
     * 打开中
     */
    OPENING,
    /**
     * 打开
     */
    OPEN,
    /**
     * 关闭
     */
    CLOSED,
    /**
     * 暂停
     */
    PAUSED,

    /**
     * 关闭中
     */
    CLOSING;

    @Override
    public String toString() {
        return super.toString().toLowerCase();
    }
}
