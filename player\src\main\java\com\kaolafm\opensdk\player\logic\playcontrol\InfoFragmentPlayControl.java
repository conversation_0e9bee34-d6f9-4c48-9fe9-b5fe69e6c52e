package com.kaolafm.opensdk.player.logic.playcontrol;

import com.kaolafm.opensdk.api.media.model.AudioFileInfo;
import com.kaolafm.opensdk.api.media.model.ToneQuality;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.List;

/**
 * 资讯碎片播放控制
 * <AUTHOR> qian
 */
public class InfoFragmentPlayControl extends BasePlayControl {

    @Override
    ToneQuality getToneQuality(PlayItem playItem, List<AudioFileInfo> playListUrlInfos) {
        return null;
    }

    @Override
    void requestPlayUrl(PlayItem playItem, OnGetPlayUrlData callback) {

    }

    @Override
    String getPlayItemResourceType(PlayItem playItem) {
        return "mp3";
    }
}
