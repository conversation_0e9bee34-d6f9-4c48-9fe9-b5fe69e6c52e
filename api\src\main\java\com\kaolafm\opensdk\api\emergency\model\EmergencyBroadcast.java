package com.kaolafm.opensdk.api.emergency.model;

import java.io.Serializable;

public class EmergencyBroadcast implements Serializable {

    /**
     * 应急广播标题文字
     */
    private String headline;

    /**
     * 应急广播正文文字
     */
    private String eventDescription;
    /**
     * 应急广播标题加工音频地址
     */
    private String headlinePath;

    /**
     * 应急广播正文加工音频地址
     */
    private String eventDescriptionPath;

    /**
     * 应急广播id
     */
    private String emergencyId;

    /**
     * 应急广播发布时间 时间戳,到毫秒
     */
    private String publishTime;

    /**
     * 应急广播发布机构名称
     */
    private String sender;

    /**
     * 应急广播灾害类型
     */
    private String eventType;

    /**
     * 应急广播预警等级:1.红色预警 2.橙色预警 3.黄色预警 4.蓝色预警
     */
    private String eventLevel;
    /**
     * 插播类型:0-立即插播  1-延时插播
     */
    private String playType;
    /**
     * 消息等级:1-情感化问候、车载服务类消息 2-节目预约、社群等主动交互类消息 3-云听应急广播消息
     */
    private String msgLevel;
    /**
     * 消息内容类型:0.应急播报
     * 1.AI路况消息 2.AI气象消息 3.出行服务及电商消息
     * 4.本地服务消息 5.活动运营相关消息  6.收听助手相关消息
     */
    private String msgContentType;
    /**
     * 消息展示样式:0-纯文本 1-文+图  2-文+按钮 3-文+图+按钮
     */
    private String msgStyleType;
    /**
     * 提示标题:如疫情消息，国家应急广播，节日提醒等
     */
    private String tipsTitle;
    /**
     * 一句话简述
     */
    private String eventDescriptionExtract;
    /**
     * 左侧按钮文字
     */
    private String msgDetailsBtnTextLeft;
    /**
     * 右侧按钮文字
     */
    private String msgDetailsBtnTextRight;
    /**
     * 消息标题图片:消息来源机构的图片url，如国家应急广播的图片url
     */
    private String msgTipsPicUrl;
    /**
     *消息泡泡小卡片背景图地址
     */
    private String cardBgUrl;
    /**
     * 消息详情背景图片地址
     */
    private String msgDetailsBgUrl;

    public String getHeadline() {
        return headline;
    }

    public void setHeadline(String headline) {
        this.headline = headline;
    }

    public String getEventDescription() {
        return eventDescription;
    }

    public void setEventDescription(String eventDescription) {
        this.eventDescription = eventDescription;
    }

    public String getHeadlinePath() {
        return headlinePath;
    }

    public void setHeadlinePath(String headlinePath) {
        this.headlinePath = headlinePath;
    }

    public String getEventDescriptionPath() {
        return eventDescriptionPath;
    }

    public void setEventDescriptionPath(String eventDescriptionPath) {
        this.eventDescriptionPath = eventDescriptionPath;
    }

    public String getEmergencyId() {
        return emergencyId;
    }

    public void setEmergencyId(String emergencyId) {
        this.emergencyId = emergencyId;
    }

    public String getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(String publishTime) {
        this.publishTime = publishTime;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getEventLevel() {
        return eventLevel;
    }

    public void setEventLevel(String eventLevel) {
        this.eventLevel = eventLevel;
    }

    public String getPlayType() {
        return playType;
    }

    public void setPlayType(String playType) {
        this.playType = playType;
    }

    public String getMsgLevel() {
        return msgLevel;
    }

    public void setMsgLevel(String msgLevel) {
        this.msgLevel = msgLevel;
    }

    public String getMsgContentType() {
        return msgContentType;
    }

    public void setMsgContentType(String msgContentType) {
        this.msgContentType = msgContentType;
    }

    public String getMsgStyleType() {
        return msgStyleType;
    }

    public void setMsgStyleType(String msgStyleType) {
        this.msgStyleType = msgStyleType;
    }

    public String getTipsTitle() {
        return tipsTitle;
    }

    public void setTipsTitle(String tipsTitle) {
        this.tipsTitle = tipsTitle;
    }

    public String getEventDescriptionExtract() {
        return eventDescriptionExtract;
    }

    public void setEventDescriptionExtract(String eventDescriptionExtract) {
        this.eventDescriptionExtract = eventDescriptionExtract;
    }

    public String getMsgDetailsBtnTextLeft() {
        return msgDetailsBtnTextLeft;
    }

    public void setMsgDetailsBtnTextLeft(String msgDetailsBtnTextLeft) {
        this.msgDetailsBtnTextLeft = msgDetailsBtnTextLeft;
    }

    public String getMsgDetailsBtnTextRight() {
        return msgDetailsBtnTextRight;
    }

    public void setMsgDetailsBtnTextRight(String msgDetailsBtnTextRight) {
        this.msgDetailsBtnTextRight = msgDetailsBtnTextRight;
    }

    public String getMsgTipsPicUrl() {
        return msgTipsPicUrl;
    }

    public void setMsgTipsPicUrl(String msgTipsPicUrl) {
        this.msgTipsPicUrl = msgTipsPicUrl;
    }

    public String getCardBgUrl() {
        return cardBgUrl;
    }

    public void setCardBgUrl(String cardBgUrl) {
        this.cardBgUrl = cardBgUrl;
    }

    public String getMsgDetailsBgUrl() {
        return msgDetailsBgUrl;
    }

    public void setMsgDetailsBgUrl(String msgDetailsBgUrl) {
        this.msgDetailsBgUrl = msgDetailsBgUrl;
    }
}
