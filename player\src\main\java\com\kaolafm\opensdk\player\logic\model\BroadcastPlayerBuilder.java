package com.kaolafm.opensdk.player.logic.model;

import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

/**
 * <AUTHOR> on 2019/4/1.
 */

public class BroadcastPlayerBuilder extends CustomPlayerBuilder {
    /**
     * 当播放广播是是否忽略检查地方台
     * 用于支持分区域的广播播放国家台时是否尝试获取当前经纬度所在省市的地方台
     * true：不尝试播放定位所在地的地方台
     */
    private boolean isIgnoreLocalRadio = false;

    public BroadcastPlayerBuilder() {
        setType(PlayerConstants.RESOURCES_TYPE_BROADCAST);
    }

    public boolean isIgnoreLocalRadio() {
        return isIgnoreLocalRadio;
    }

    public BroadcastPlayerBuilder setIgnoreLocalRadio(boolean ignoreLocalRadio) {
        isIgnoreLocalRadio = ignoreLocalRadio;
        return this;
    }
}
