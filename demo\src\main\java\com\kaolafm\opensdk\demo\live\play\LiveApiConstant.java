package com.kaolafm.opensdk.demo.live.play;


public interface LiveApiConstant {

    /**
     * 网宿的AK
     */
    public static final String AK_WANGSU = "272f0544e3e65a6b813c4e35931a70254868ccbd";

    /**
     * 网宿的SK
     */
    public static final String SK_WANGSU = "638b2274abf0eeeb1470e8552368ca4ad4d8c82a";

    /**
     * 网宿的bucket
     */
    public static final String BUCKET_WANGSU = "kaolafm-audio";

    /**
     * 网宿的deadline 北京时间 2068/6/1 20:24:49
     */
    public static final String DEADLINE_WANGSU = "3105779089000";

    /**
     * 网宿上传成功后返回文本和url
     */
    public static final String RETURN_BODY_WANGSU = "fname=$(fname)&url=$(url)";

    /**
     * 上传到网宿的文件夹
     */
    public static final String FILE_DIR_NAME_WANGSU = "kradio_live_radio";

    /**
     * 网宿的回调地址
     */
    public static final String WANGSU_UPLOAD_CALLBACK_URL =
            "http://iovopen.radio.cn" + "/live/saveVoiceMail";

    /**
     * 网宿的上传地址
     */
    public static final String RECORD_UPLOAD_HOST_WANGSU = "http://radiobuy.up17.v1.wcsapi.com";

}
