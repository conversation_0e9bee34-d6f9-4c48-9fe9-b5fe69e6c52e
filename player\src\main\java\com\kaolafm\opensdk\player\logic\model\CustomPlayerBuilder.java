package com.kaolafm.opensdk.player.logic.model;

import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

/**
 * <AUTHOR> on 2019/4/1.
 */

public class CustomPlayerBuilder extends PlayerBuilder{
    /**
     * 单曲id
     */
    private String mChildId;

    /**
     * 需要seek的点
     */
    private long mSeekPosition;



    public CustomPlayerBuilder() {
    }

    public String getChildId() {
        return mChildId;
    }

    public CustomPlayerBuilder setChildId(String sonId) {
        this.mChildId = sonId;
        return CustomPlayerBuilder.this;
    }

    public long getSeekPosition() {
        return mSeekPosition;
    }

    public CustomPlayerBuilder setSeekPosition(long seekPosition) {
        this.mSeekPosition = seekPosition;
        return CustomPlayerBuilder.this;
    }


}
