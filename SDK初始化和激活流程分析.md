# SDK初始化和激活流程分析

## 整体架构

SDK采用了分层架构设计，主要包含以下核心类：

1. **OpenSDK**: 对外暴露的主要接口类，是SDK的入口点
2. **OpenSDKEngine**: 实际执行初始化和激活的引擎类
3. **KradioSDKInternalEngine**: 基础SDK内部入口，执行核心初始化/激活逻辑
4. **BaseEngine**: 封装SDK初始化/激活的公共部分的抽象基类
5. **VerifyActivation**: 激活验证接口，由VerifyActivationImpl实现
6. **InitRequest**: 处理初始化和激活相关的网络请求

## 初始化流程

### 初始化调用链

```
OpenSDK.initSDK() 
  -> OpenSDKEngine.init() 
    -> BaseEngine.init() 
      -> OpenSDKEngine.internalInit() 
        -> KradioSDKInternalEngine.internalInit()
          -> 设置deviceId、加载Profile、加载AccessToken、初始化报告系统
```

### 初始化关键代码

#### 1. OpenSDK 初始化入口

```java
// OpenSDK.java
public void initSDK(Application application, String deviceId, HttpCallback<Boolean> callback) throws InitSDKException {//应用实例，设备id、回调
    AdvertOptions options = new AdvertOptions();
    options.setDeviceId(deviceId);
    initSDK(application, options, callback);
}

public void initSDK(Application application, AdvertOptions options, HttpCallback<Boolean> callback) throws InitSDKException {//应用实例，配置容器、回调   （配置选项更多）
    initStepOne(application, options.getDeviceId(), options);
    mEngine.init(application, options, callback);
}
//初始化第一步，检查 预初始化状态 和 deviceId
private void initStepOne(Application application, String deviceId, AdvertOptions options) throws InitSDKException {
    if (!isPreInited) {
        throw new InitSDKException("Please do preInit first in application onCreate method");
    }
    options.setDeviceId(deviceId);
    if (TextUtils.isEmpty(deviceId) || deviceId.length() > 64) {
        throw new InitSDKException("the deviceId cannot be empty or the deviceId's length > 64");
    }
    //基础组件初始化
    SpUtil.init(application);//实例化SharedPreferences；存储access_token和open_uid等关键凭证、存储用户配置、设备信息和SDK状态、保存激活状态，确保下次启动时不需要重新激活
    SeCoreUtils.getInstance().SeCoreInit(application); //核心工具类-用于加密
}
```

#### 2. OpenSDKEngine 初始化实现

```java
// OpenSDKEngine.java
@Override
protected void internalInit(Application application, AdvertOptions options, HttpCallback<Boolean> callback) {
    ComponentKit.getInstance().inject(DaggerAppComponent.builder(), application, options, this);//依赖注入
    super.internalInit(application, options, callback);//父类初始化
    mAdvertisingInternalEngineLazy.get().internalInit(application, options, callback);//广告初始化
    PlayerManager.getInstance().init(application);//播放器初始化
}
```

#### 3. KradioSDKInternalEngine 核心初始化逻辑

```java
// KradioSDKInternalEngine.java
@Override
protected void internalInit(Application application, T options, HttpCallback<Boolean> callback) {
    ComponentKit.getInstance().inject(new BaseRequest() {
        @Override
        protected <T> T obtainRetrofitService(Class<T> service) {
            return super.obtainRetrofitService(service);
        }
    });//网络请求组件注入
    mApplication = application;//应用实例赋值
    setDeviceId(options.getDeviceId());//设置deviceId
    loadProfile();//加载配置文件
    loadAccessToken();//加载访问令牌
    initReport();//报告系统初始化
}

private void loadAccessToken() {
   //这个方法从SharedPreferences中读取保存的令牌信息，包括：
   //accessToken：访问令牌字符串
   //userId：用户ID
   //openId：开放平台用户ID
   //expiresTime：令牌过期时间
    mAccessTokenManager.loadCurrentAccessToken();//加载当前访问令牌
}

private void loadProfile() {
    mProfileManager.loadProfile();//加载用户配置文件
    mMusicProfileManger.loadCurrentProfile();//加载音乐配置
}

```
加载令牌的目的是：

3.1. **检查激活状态**：
   - 如果`openId`不为空，说明设备已经激活过
   - 如果为空，后续需要进行激活流程

3.2. **检查登录状态**：
   - 如果`userId`和`accessToken`不为空，说明用户已登录
   - 如果为空，用户需要登录才能使用需要授权的功能

3.3. **避免重复激活**：
   - 如果已经有令牌，可以直接使用，不需要重新激活
   - 节省网络请求，提高用户体验

3.4. **准备后续请求**：
   - 加载的令牌会用于后续的API请求
   - 作为请求头或参数，证明身份和权限

令牌的完整生命周期是：
1. **创建**：首次激活或登录时创建
2. **加载**：SDK初始化时从缓存加载
3. **使用**：用于API请求的授权
4. **刷新**：令牌过期时刷新
5. **清除**：用户登出或清除数据时清除

在SDK初始化阶段，我们只是执行了"加载"这一步，如果没有找到令牌，后续会在激活流程中创建新的令牌。


#### 4. BaseEngine 初始化控制流程

```java
// BaseEngine.java
@Override
public void init(Application application, O options, HttpCallback<Boolean> callback) {
    if (!initialized) {
        internalInit(application, options, callback);
        initialized = true;
        if (callback != null) {
            callback.onSuccess(true);
        }
    } else {
        if (callback != null) {
            callback.onSuccess(true);
        }
    }
}
```

### 初始化过程概要

1. **预初始化 (preInit)**

   - 在Application的onCreate方法中调用
   - 设置isPreInited标志为true，为后续初始化做准备
2. **正式初始化 (initSDK)**

   - 检查是否已预初始化
   - 验证deviceId有效性（不为空且长度不超过64）
   - 初始化SpUtil和SeCoreUtils
   - 注入依赖组件
   - 设置设备ID
   - 加载用户配置文件(Profile)和访问令牌(AccessToken)
   - 初始化报告系统
   - 初始化播放器管理器

## 激活流程

### 激活调用链

```
OpenSDK.activate() 
  -> OpenSDKEngine.activate() 
    -> BaseEngine.activate() 
      -> KradioSDKInternalEngine.internalActivate() 
        -> VerifyActivationImpl.activate()
          -> InitRequest.activateOrInitKaola()
```

### 激活关键代码

#### 1. OpenSDK 激活入口

```java
// OpenSDK.java
public void activate(HttpCallback<Boolean> callback) {
    mEngine.activate(callback);
}

public void initAndActivate(Application application, String deviceId, HttpCallback<Boolean> callback) throws InitSDKException {
    AdvertOptions options = new AdvertOptions();
    initStepOne(application, deviceId, options);
    mEngine.config(application, options, callback);
}
```

#### 2. OpenSDKEngine 激活实现

```java
// OpenSDKEngine.java
@Override
public void activate(HttpCallback<Boolean> callback) {
    super.activate(new HttpCallback<Boolean>() {
        @Override
        public void onSuccess(Boolean aBoolean) {
            configBrand();
            if (callback != null) {
                callback.onSuccess(aBoolean);
            }
        }

        @Override
        public void onError(ApiException exception) {
            if (callback != null) {
                callback.onError(exception);
            }
        }
    });
}

private void configBrand() {
    mInitRequestLazy.get().getBrand(new HttpCallback<String>() {
        @Override
        public void onSuccess(String brandName) {
            mAdvertisingInternalEngineLazy.get().setBrand(brandName);
        }

        @Override
        public void onError(ApiException exception) {
        }
    });
}
```

#### 3. BaseEngine 激活控制流程

```java
// BaseEngine.java
@Override
public void activate(HttpCallback<Boolean> callback) {
    if (!initialized) {
        throw new RuntimeException("请先初始化sdk");
    }
    if (!isActivated()) {
        internalActivate(callback);
    } else {
        if (callback != null) {
            callback.onSuccess(true);
        }
    }
}
```

#### 4. KradioSDKInternalEngine 核心激活逻辑

```java
// KradioSDKInternalEngine.java
@Override
protected void internalActivate(HttpCallback<Boolean> callback) {
    mVerifyActivation.activate(new HttpCallback<Boolean>() {
        @Override
        public void onSuccess(Boolean isActivated) {
            initReport();
            if (callback != null) {
                callback.onSuccess(isActivated);
            }
        }

        @Override
        public void onError(ApiException exception) {
            if (callback != null) {
                callback.onError(exception);
            }
        }
    });
}
```

#### 5. VerifyActivationImpl 激活实现

```java
// VerifyActivationImpl.java
@Override
public void activate(HttpCallback<Boolean> callback) {
    mCallback = callback;
    if (activating) {
        return;
    }
    activating = true;
    try {
        mSemaphore.acquire(); //阻塞地获取信号量，保证不重复激活
        if (activated) {
            mSemaphore.release();
            return;
        }
        mInitRequestLazy.get().activateOrInitKaola(new HttpCallback<String>() { //最终激活请求
            @Override
            public void onSuccess(String openId) {
                saveOpenId(openId);
                activating = false;
                mSemaphore.release();
            }

            @Override
            public void onError(ApiException exception) {
                callback.onError(exception);
                activating = false;
                mSemaphore.release();
            }
        });
    } catch (InterruptedException e) {
        e.printStackTrace();
        activating = false;
        mSemaphore.release();
    }
}
```

#### 6. InitRequest 激活网络请求

```java
// InitRequest.java
public void activateOrInitKaola(HttpCallback<String> callback) {
    doHttpDeal(activateOrInitKaola(), callback);
}

private Single<String> activateOrInitKaola() {
    return Single.create(emitter -> activeKaola(new HttpCallback<String>() {
        @Override
        public void onSuccess(String openId) {
            if (!TextUtils.isEmpty(openId)) {
                InitRequest.this.onSuccess(emitter, openId);
            } else {
                initKaola(emitter);
            }
        }

        @Override
        public void onError(ApiException exception) {
            if (exception != null && exception.getCode() == ERROR_CODE_REPEAT_ACTIVATION) {
                initKaola(emitter);
            } else {
                InitRequest.this.onError(emitter, exception);
            }
        }
    }));
}
```

### 激活过程详解

1. **激活请求**

   - 通过InitRequest发送激活请求到服务器
   - 如果设备已激活(返回错误码50500)，则自动调用初始化接口获取openId
   - 如果激活成功但未获取openId，也会调用初始化接口
2. **激活结果处理**

   - 保存openId到本地存储
   - 更新KaolaAccessToken中的openId
   - 设置activated标志为true
   - 配置品牌信息
   - 初始化报告系统
3. **激活同步机制**

   - 使用信号量(Semaphore)确保激活操作只执行一次
   - 多线程情况下，未抢到信号的线程会等待
   - 激活完成后释放信号量

## 初始化和激活后的操作

### 位置信息设置

```java
// OpenSDK.java
public void setLocation(String lng, String lat) {
    mEngine.setLocation(lng, lat);
}

// OpenSDKEngine.java
@Override
public void setLocation(String lng, String lat) {
    super.setLocation(lng, lat);
    mAdvertisingInternalEngineLazy.get().setLocation(lng, lat);

    EmergencyBroadcastManager.getInstance().socketUploadLocationIfNeed();
}
```

### 品牌信息配置

```java
// OpenSDKEngine.java
private void configBrand() {
    mInitRequestLazy.get().getBrand(new HttpCallback<String>() {
        @Override
        public void onSuccess(String brandName) {
            mAdvertisingInternalEngineLazy.get().setBrand(brandName);
        }

        @Override
        public void onError(ApiException exception) {
        }
    });
}
```

### 紧急广播管理

```java
// OpenSDKEngine.java
@Override
public void setLocation(String lng, String lat) {
    super.setLocation(lng, lat);
    mAdvertisingInternalEngineLazy.get().setLocation(lng, lat);

    EmergencyBroadcastManager.getInstance().socketUploadLocationIfNeed();
}
```

## OpenID获取失败分析

### 可能导致OpenID获取失败的原因

1. **初始化顺序问题**

   - 未先调用preInit()方法，导致初始化失败
   - 在SDK.initSDK()之前尝试获取OpenID
   - 在激活完成前尝试获取OpenID
2. **网络请求问题**

   - 激活请求(activeKaola)网络失败
   - 初始化请求(initKaola)网络失败
   - 服务器返回的数据中不包含OpenID
3. **多线程并发问题**

   - 多个线程同时尝试激活，导致信号量机制混乱
   - 激活过程中应用被杀死，导致激活状态未正确保存
4. **数据存储问题**

   - SpUtil存储OpenID失败
   - AccessToken对象创建或保存失败
5. **重试机制失效**

   - saveOpenId()方法中的重试机制(retryWhen)未能成功重试
   - 重试次数(5次)用尽但仍未成功

### 解决方案

1. **确保正确的初始化顺序**

   ```java
   OpenSDK.getInstance().preInit(application);

   try {
       OpenSDK.getInstance().initSDK(application, deviceId, new HttpCallback<Boolean>() {
           @Override
           public void onSuccess(Boolean success) {
           }

           @Override
           public void onError(ApiException exception) {
           }
       });
   } catch (InitSDKException e) {
   }

   OpenSDK.getInstance().activate(new HttpCallback<Boolean>() {
       @Override
       public void onSuccess(Boolean success) {
           String openId = OpenSDK.getInstance().getOpenId();
       }

       @Override
       public void onError(ApiException exception) {
       }
   });
   ```
2. **添加OpenID检查和重试机制**

   ```java
   private void ensureOpenIdAvailable(Callback callback) {
       if (TextUtils.isEmpty(OpenSDK.getInstance().getOpenId())) {
           OpenSDK.getInstance().activate(new HttpCallback<Boolean>() {
               @Override
               public void onSuccess(Boolean success) {
                   if (success && !TextUtils.isEmpty(OpenSDK.getInstance().getOpenId())) {
                       callback.onSuccess();
                   } else {
                       callback.onFailure("Failed to get OpenID after activation");
                   }
               }

               @Override
               public void onError(ApiException exception) {
                   callback.onFailure(exception.getMessage());
               }
           });
       } else {
           callback.onSuccess();
       }
   }
   ```
3. **检查网络状态**

   ```java
   private boolean isNetworkAvailable() {
       ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
       NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
       return activeNetworkInfo != null && activeNetworkInfo.isConnected();
   }
   ```
4. **添加日志和监控**

   ```java
   @Override
   public void activate(HttpCallback<Boolean> callback) {
       Log.d(TAG, "activate: starting activation process");
       mCallback = callback;
       if (activating) {
           Log.d(TAG, "activate: already activating, skipping");
           return;
       }
       activating = true;
       try {
           Log.d(TAG, "activate: acquiring semaphore");
           mSemaphore.acquire();
           if (activated) {
               Log.d(TAG, "activate: already activated, releasing semaphore");
               mSemaphore.release();
               return;
           }
           Log.d(TAG, "activate: calling activateOrInitKaola");
           mInitRequestLazy.get().activateOrInitKaola(new HttpCallback<String>() {
               @Override
               public void onSuccess(String openId) {
                   Log.d(TAG, "activate: activateOrInitKaola success, openId=" + (TextUtils.isEmpty(openId) ? "empty" : "not empty"));
                   saveOpenId(openId);
                   activating = false;
                   mSemaphore.release();
               }

               @Override
               public void onError(ApiException exception) {
                   Log.e(TAG, "activate: activateOrInitKaola error", exception);
                   callback.onError(exception);
                   activating = false;
                   mSemaphore.release();
               }
           });
       } catch (InterruptedException e) {
           Log.e(TAG, "activate: interrupted", e);
           e.printStackTrace();
           activating = false;
           mSemaphore.release();
       }
   }
   ```

## 设计理念

1. **分离关注点**

   - 将初始化和激活逻辑分离，使SDK可以分步骤启动
   - 通过接口和抽象类实现功能解耦
2. **延迟初始化**

   - 使用Lazy注入，延迟创建对象实例，优化资源使用
3. **安全激活**

   - 使用信号量保证激活过程的线程安全
   - 防止多线程环境下的重复激活
4. **容错设计**

   - 激活失败后自动尝试初始化获取openId
   - 网络请求失败后有重试机制
5. **模块化设计**

   - 各功能模块可以单独打包或一起打包
   - 通过依赖注入实现模块间的低耦合

## 为什么需要初始化和激活

1. **初始化的必要性**

   - 设置设备标识和基本配置
   - 加载用户配置和访问令牌
   - 准备SDK内部组件和依赖
2. **激活的必要性**

   - 向服务器注册设备，获取唯一的openId
   - 建立设备与服务器的关联关系
   - 获取品牌信息和其他配置
   - 激活报告系统和广告引擎

初始化和激活是SDK使用的前提条件，确保SDK能够正常工作，并与服务器建立有效的通信。通过这两个步骤，SDK能够获取必要的配置信息和身份认证，为后续的功能调用奠定基础。
