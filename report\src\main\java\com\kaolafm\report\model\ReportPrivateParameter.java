package com.kaolafm.report.model;

/**
 * <AUTHOR> on 2019/2/18.
 */

public class ReportPrivateParameter {
    private long action_id;
    private int mSessionId = 0;
    private String app_version;
    private boolean isFirstListen = true;
    private String carType;
    private String appVersionName;

    public long getAction_id() {
        action_id++;
        return action_id;
    }

    public void setAction_id(long action_id) {
        this.action_id = action_id;
    }

    public int getmSessionId() {
        return mSessionId;
    }

    public void setmSessionId(int mSessionId) {
        this.mSessionId = mSessionId;
    }

    public String getApp_version() {
        return app_version;
    }

    public void setApp_version(String app_version) {
        this.app_version = app_version;
    }

    public boolean isFirstListen() {
        return isFirstListen;
    }

    public void setFirstListen(boolean firstListen) {
        isFirstListen = firstListen;
    }

    public String getCarType() {
        return carType;
    }

    public void setCarType(String carType) {
        this.carType = carType;
    }

    public String getAppVersionName() {
        return appVersionName;
    }

    public void setAppVersionName(String appVersionName) {
        this.appVersionName = appVersionName;
    }
}
