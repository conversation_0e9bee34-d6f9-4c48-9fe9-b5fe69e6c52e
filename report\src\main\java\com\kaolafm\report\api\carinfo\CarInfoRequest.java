package com.kaolafm.report.api.carinfo;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.report.api.carinfo.model.CarInfoData;

/**
 * <AUTHOR> on 2019/2/13.
 */

public class CarInfoRequest extends BaseRequest {
    private CarInfoService reportInfoService;

    public CarInfoRequest() {
        reportInfoService = mRepositoryManager.obtainRetrofitService(CarInfoService.class);
    }


    public void getReportInfo(HttpCallback<CarInfoData> callback) {
        doHttpDeal(reportInfoService.getReportInfo(), BaseResult::getResult, callback);

    }
}
