package com.kaolafm.opensdk.api.music.qq.model;

import com.google.gson.annotations.SerializedName;

/**
 * 音乐电台
 * <AUTHOR>
 * @date 2018/4/29
 */

public class MusicRadio {

    /**
     * listen_num : 285120
     * radio_id : 123
     * radio_name : 80后
     * radio_pic : http://y.gtimg.cn/music/photo/radio/track_radio_123_10_3.jpg
     */

    @SerializedName("listen_num")
    private int listenNum;

    @SerializedName("radio_id")
    private int radioId;

    @SerializedName("radio_name")
    private String radioName;

    @SerializedName("radio_pic")
    private String radioPic;

    public int getListenNum() {
        return listenNum;
    }

    public int getRadioId() {
        return radioId;
    }

    public String getRadioName() {
        return radioName;
    }

    public String getRadioPic() {
        return radioPic;
    }

    public void setListenNum(int listenNum) {
        this.listenNum = listenNum;
    }

    public void setRadioId(int radioId) {
        this.radioId = radioId;
    }

    public void setRadioName(String radioName) {
        this.radioName = radioName;
    }

    public void setRadioPic(String radioPic) {
        this.radioPic = radioPic;
    }
}
