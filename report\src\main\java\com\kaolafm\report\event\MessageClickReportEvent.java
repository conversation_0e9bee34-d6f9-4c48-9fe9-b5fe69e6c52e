package com.kaolafm.report.event;

import com.kaolafm.report.util.ReportConstants;

public class MessageClickReportEvent extends BaseReportEventBean {
    /**
     * 单曲id	若无单曲曝光则不传参数取值
     */
    private String audioid;
    /**
     * 内容类型	0.应急播报   1.AI路况消息  2.AI气象消息
     *  3.出行服务及电商消息 4.本地服务消息 5.活动运营相关消息  6.收听助手相关消息
     */
    private String radiotype;
    /**
     * 页面id	同页面曝光事件参数说明
     */
    private String pageid;
    /**
     * 成员位置	服务端吐出的顺序位置
     */
    private String remarks2;

    public MessageClickReportEvent(){
        setEventcode(ReportConstants.PAGE_ID_MESSAGE_CLICK);
    }

    public String getAudioid() {
        return audioid;
    }

    public void setAudioid(String audioid) {
        this.audioid = audioid;
    }

    public String getRadiotype() {
        return radiotype;
    }

    public void setRadiotype(String radiotype) {
        this.radiotype = radiotype;
    }

    public String getPageid() {
        return pageid;
    }

    public void setPageid(String pageid) {
        this.pageid = pageid;
    }

    public String getRemarks2() {
        return remarks2;
    }

    public void setRemarks2(String remarks2) {
        this.remarks2 = remarks2;
    }
}
