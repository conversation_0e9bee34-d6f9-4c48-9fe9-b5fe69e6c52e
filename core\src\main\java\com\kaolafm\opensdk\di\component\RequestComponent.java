package com.kaolafm.opensdk.di.component;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.di.scope.RequestScope;
import com.kaolafm.opensdk.http.socket.SocketManager;

import dagger.Subcomponent;

/**
 * 网络请求基类的子组件Subcomponent
 *
 * <AUTHOR>
 * @date 2018/8/10
 */
@RequestScope
@Subcomponent
public interface RequestComponent extends BaseSubcomponent {
    void inject(BaseRequest request);

    void inject(SocketManager socketManager);

//    void inject(BaseDBManager dbManager);
}
