package com.kaolafm.opensdk.demo.purchase;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.kaolafm.opensdk.api.purchase.PurchaseRequest;
import com.kaolafm.opensdk.api.purchase.model.QRCodeInfo;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 通用
 *
 */
public class AlbumQRCodeActivity extends BaseActivity {

    @BindView(R.id.et_album_id)
    EditText albumIdEt;

    @BindView(R.id.et_album_money)
    EditText albumMoneyEt;

    @BindView(R.id.info_view)
    TextView infoViewTv;

    @BindView(R.id.iv_album_qr_code)
    ImageView albumQrCodeImg;

    @BindView(R.id.btn_qr_code_check)
    Button qrCodeCheck;

    private String mQrCodeId;

    @Override
    public int getLayoutId() {
        return R.layout.activity_album_qr_code;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        setTitle("专辑二维码");
    }

    @Override
    public void initData() {

    }

    @OnClick({R.id.btn_album_qr_code_get,R.id.btn_qr_code_check})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.btn_album_qr_code_get:
                getAlbumQrCode();
                break;
            case R.id.btn_qr_code_check:
                skipToCheck();
                break;
        }
    }

    private void getAlbumQrCode(){
        new PurchaseRequest().getAlbumQRCode(Long.valueOf(albumIdEt.getText().toString().trim()),
                Long.valueOf(albumMoneyEt.getText().toString().trim()),
                new HttpCallback<QRCodeInfo>() {
                    @Override
                    public void onSuccess(QRCodeInfo qrCodeInfo) {
                        mQrCodeId = qrCodeInfo.getQrCodeId();
                        infoViewTv.setText(qrCodeInfo.toString());
                        Glide.with(AlbumQRCodeActivity.this)
                                .load(qrCodeInfo.getQrCodeImg())
                                .into(albumQrCodeImg);
                        qrCodeCheck.setVisibility(View.VISIBLE);
                    }

                    @Override
                    public void onError(ApiException exception) {
                        showError("获取失败", exception);
                    }
                });
    }

    private void skipToCheck(){
        Intent intent = new Intent(AlbumQRCodeActivity.this, QRCodeStatusActivity.class);
        intent.putExtra(QRCodeStatusActivity.KEY_QRCODE_ID, mQrCodeId);
        startActivity(intent);
    }
}
