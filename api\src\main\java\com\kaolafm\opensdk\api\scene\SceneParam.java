package com.kaolafm.opensdk.api.scene;

import com.google.gson.annotations.SerializedName;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 **/
class SceneParam {
    /**
     * 扩展信息
     */
    @SerializedName("extInfo")
    private Map<String, String> extInfo;

    @SerializedName("events")
    private Map<String, String> events;

    public SceneParam() {
        events = new HashMap<>();
    }

    public void addScene(Scene scene) {
        events.put(scene.getCode(), scene.getType());
    }

    public void setExtInfo(Map<String, String> extInfo) {
        this.extInfo = extInfo;
    }
}
