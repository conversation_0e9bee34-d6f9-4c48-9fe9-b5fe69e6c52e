package com.kaolafm.report.util;

import android.content.Context;

import com.kaolafm.base.utils.NetworkMonitor;
import com.kaolafm.base.utils.NetworkUtil;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.CrashReportEvent;
import com.kaolafm.report.model.ReportTask;

import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

/**
 * <AUTHOR> on 2019/2/26.
 */

public class ReportShenCeTimerManager {

    private Disposable mDisposable = null;

    private static ReportShenCeTimerManager reportShenCeTimerManager;

    private ReportShenCeTimerManager() {
    }

    public static ReportShenCeTimerManager getInstance() {
        if (reportShenCeTimerManager == null) {
            synchronized (ReportShenCeTimerManager.class) {
                if (reportShenCeTimerManager == null) {
                    reportShenCeTimerManager = new ReportShenCeTimerManager();
                }
            }
        }
        return reportShenCeTimerManager;
    }


    public void init() {
        NetworkMonitor.getInstance(ReportHelper.getInstance().getContext()).registerNetworkStatusChangeListener(onNetworkStatusChangedListener);
        initCrashHandler();
        if (mDisposable == null) {
            runTimer();
        }
    }

    private void runTimer() {
        mDisposable = Observable.interval(getTimer(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(aLong -> addSendTask());
    }

    public void addSendTask() {
        if (!NetworkUtil.isNetworkAvailable(ReportHelper.getInstance().getContext())) {
            return;
        }

        if (ReportShenCeTaskHelper.getInstance().isHasSendTask()) {
            return;
        }
        ReportTask reportTask = new ReportTask();
        reportTask.setType(ReportConstants.TASK_TYPE_SEND_DATA);
        reportTask.setSingleTask(ReportShenCeDBHelper.getInstance().read());
        ReportShenCeTaskHelper.getInstance().insertTask(reportTask);
    }

    private static NetworkMonitor.OnNetworkStatusChangedListener onNetworkStatusChangedListener = (newStatus, oldStatus) -> ReportParameterManager
            .getInstance().initNetwork();

    public void initCrashHandler() {
        CrashHandler handler = CrashHandler.getInstance();
        handler.programStart(ReportHelper.getInstance().getContext());
        handler.setOnCrashListener(ex -> {
            CrashReportEvent reportEvent = new CrashReportEvent();
            reportEvent.setMessage(ex.getMessage());
            ReportHelper.getInstance().addEvent(reportEvent, false);
        });
    }

    public int getTimer() {
        int timer = ReportParameterManager.getInstance().getTimer();
        if (timer < ReportConstants.READ_DATE_BASE_TIMER || timer > ReportConstants.READ_DATE_BASE_MAX_TIMER) {
            timer = ReportConstants.READ_DATE_BASE_TIMER;
        }
        return timer;
    }

    public void release() {
        if (mDisposable != null && !mDisposable.isDisposed()) {
            mDisposable.dispose();
            mDisposable = null;
        }
        Context context = ReportHelper.getInstance().getContext();

        if (context != null) {
            NetworkMonitor.getInstance(ReportHelper.getInstance().getContext()).removeNetworkStatusChangeListener(onNetworkStatusChangedListener);
        }
    }
}
