package com.kaolafm.opensdk.di.component;

import com.kaolafm.opensdk.api.music.qq.QQMusicRequest;
import com.kaolafm.opensdk.di.module.QQMusicModule;
import com.kaolafm.opensdk.di.scope.QQMusicScope;
import dagger.Subcomponent;

/**
 * QQ音乐网络请求Component
 * <AUTHOR>
 * @date 2018/8/10
 */
@QQMusicScope
@Subcomponent(modules = {QQMusicModule.class})
public interface QQMusicRequestComponent {
    void inject(QQMusicRequest musicRequest);
}
