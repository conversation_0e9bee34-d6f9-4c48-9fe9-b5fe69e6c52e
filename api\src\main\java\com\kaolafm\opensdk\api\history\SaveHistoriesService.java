package com.kaolafm.opensdk.api.history;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.history.model.SyncHistoryStatus;
import com.kaolafm.opensdk.api.login.model.Success;

import io.reactivex.Single;
import okhttp3.RequestBody;
import retrofit2.Response;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * <AUTHOR>
 **/
public interface SaveHistoriesService {

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @POST(KaolaApiConstant.SAVE_HISTORIES)
    Single<BaseResult<SyncHistoryStatus>> saveHistories(@Body RequestBody requestBody);

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @POST(KaolaApiConstant.UPDATE_AUDIO_STATE)
    Single<Response<BaseResult<Success>>> updateAudioState(@Query("id") long id, @Query("type") int type, @Query("come") int come);
}
