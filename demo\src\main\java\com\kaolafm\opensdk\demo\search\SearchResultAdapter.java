package com.kaolafm.opensdk.demo.search;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.search.model.SearchProgramBean;
import com.kaolafm.opensdk.demo.BaseAdapter;
import com.kaolafm.opensdk.demo.BaseHolder;
import com.kaolafm.opensdk.demo.R;

import butterknife.BindView;

/**
 * 老接口的adapter
 *
 * <AUTHOR>
 * @date 2018/8/6
 */

public class SearchResultAdapter extends BaseAdapter<SearchProgramBean> {

    @Override
    protected BaseHolder<SearchProgramBean> getViewHolder(View view, int viewType) {
        return new SearchResultHolder(view);
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_search_result;
    }

    static class SearchResultHolder extends BaseHolder<SearchProgramBean> {

        @BindView(R.id.iv_search_result_img)
        ImageView mIvSearchResultImg;

        @BindView(R.id.tv_search_result_name)
        TextView mTvSearchResultName;

        @BindView(R.id.tv_search_result_album)
        TextView mTvSearchResultAlbum;

        @BindView(R.id.item_type_tv)
        TextView item_type_tv;


        public SearchResultHolder(View itemView) {
            super(itemView);
        }

        @Override
        public void setupData(SearchProgramBean searchProgramBean, int position) {
            Glide.with(itemView).load(searchProgramBean.getImg()).into(mIvSearchResultImg);
            mTvSearchResultName.setText(searchProgramBean.getName());
            mTvSearchResultAlbum.setText(searchProgramBean.getAlbumName());

            String type = "";
            switch (searchProgramBean.getType()) {
                case ResType.TYPE_RADIO:
                case ResType.TYPE_ALBUM:
                    type = "专辑";
                    break;
                case ResType.TYPE_AUDIO:
                    type = "单曲";
                    break;
                case ResType.TYPE_BROADCAST:
                    type = "在线广播";
                    break;
                case ResType.TYPE_TV:
                    type = "听电视";
                    break;
                case ResType.TYPE_LIVE:
                    type = "直播";
                    break;
                default:
                    type = searchProgramBean.getType() + "";
                    break;
            }
            item_type_tv.setText(type);
        }
    }
}
