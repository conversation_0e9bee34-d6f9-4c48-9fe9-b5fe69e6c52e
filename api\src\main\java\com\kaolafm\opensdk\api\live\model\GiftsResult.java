package com.kaolafm.opensdk.api.live.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-02-16
 */
public class GiftsResult implements Parcelable {

    private Long balance;//云币余额.若用户未登录账号，则云币余额数值展示：0
    private Integer showRewardContent;//赠送礼物可获得积分文案是否展示. 0-不展示,1-展示
    private List<Gift> giftList;//礼物列表

    public GiftsResult() {
    }

    public GiftsResult(Long balance, Integer showRewardContent, List<Gift> giftList) {
        this.balance = balance;
        this.showRewardContent = showRewardContent;
        this.giftList = giftList;
    }

    protected GiftsResult(Parcel in) {
        if (in.readByte() == 0) {
            balance = null;
        } else {
            balance = in.readLong();
        }
        if (in.readByte() == 0) {
            showRewardContent = null;
        } else {
            showRewardContent = in.readInt();
        }
        giftList = in.createTypedArrayList(Gift.CREATOR);
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        if (balance == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeLong(balance);
        }
        if (showRewardContent == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(showRewardContent);
        }
        dest.writeTypedList(giftList);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<GiftsResult> CREATOR = new Creator<GiftsResult>() {
        @Override
        public GiftsResult createFromParcel(Parcel in) {
            return new GiftsResult(in);
        }

        @Override
        public GiftsResult[] newArray(int size) {
            return new GiftsResult[size];
        }
    };

    public Long getBalance() {
        return balance;
    }

    public void setBalance(Long balance) {
        this.balance = balance;
    }

    public Integer getShowRewardContent() {
        return showRewardContent;
    }

    public void setShowRewardContent(Integer showRewardContent) {
        this.showRewardContent = showRewardContent;
    }

    public List<Gift> getGiftList() {
        return giftList;
    }

    public void setGiftList(List<Gift> giftList) {
        this.giftList = giftList;
    }
}
