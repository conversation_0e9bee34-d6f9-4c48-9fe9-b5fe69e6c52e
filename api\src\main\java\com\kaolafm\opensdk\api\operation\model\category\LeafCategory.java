package com.kaolafm.opensdk.api.operation.model.category;

import java.util.List;

/**
 * 叶子分类。只有该类型下才有分类成员
 */
public class LeafCategory extends Category {

    /** 分类成员列表*/
    private List<CategoryMember> categoryMembers;

    @Override
    public String toString() {
        return "LeafCategory{" +
                "categoryMembers=" + categoryMembers +
                "} " + super.toString();
    }

    public List<CategoryMember> getCategoryMembers() {
        return categoryMembers;
    }

    public void setCategoryMembers(List<CategoryMember> categoryMembers) {
        this.categoryMembers = categoryMembers;
    }
}
