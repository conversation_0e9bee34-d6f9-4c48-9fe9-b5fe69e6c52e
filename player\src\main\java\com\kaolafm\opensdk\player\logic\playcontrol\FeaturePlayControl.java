package com.kaolafm.opensdk.player.logic.playcontrol;

import com.kaolafm.opensdk.api.media.model.AudioFileInfo;
import com.kaolafm.opensdk.api.media.model.ToneQuality;
import com.kaolafm.opensdk.player.logic.model.item.FeaturePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.List;

/**
 * 专题播放控制
 * <AUTHOR> <PERSON>ian
 */
public class FeaturePlayControl extends BasePlayControl {

    @Override
    ToneQuality getToneQuality(PlayItem playItem, List<AudioFileInfo> playListUrlInfos) {
        return null;
    }

    @Override
    void requestPlayUrl(PlayItem playItem, OnGetPlayUrlData callback) {
        FeaturePlayItem featurePlayItem = (FeaturePlayItem) playItem;
        setPlayUrl(playItem, featurePlayItem.getPlayUrlDataList());
        callback.onDataGet(featurePlayItem.getPlayUrl());
    }

    @Override
    String getPlayItemResourceType(PlayItem playItem) {
        return "mp3";
    }
}
