package com.kaolafm.opensdk.api.init;

import android.annotation.SuppressLint;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.base.utils.SpUtil;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.account.token.KaolaAccessToken;
import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.core.HttpBeforeHandler;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.core.VerifyActivation;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.log.Logging;

import java.net.URL;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

import dagger.Lazy;
import io.reactivex.Observable;
import okhttp3.Interceptor;
import okhttp3.Request;

/**
 * 验证激活的接口实现类。
 * 使用信号量来保证激活只执行一次。
 * 信号量设置1，只有一个信号，如果有多线程的多次激活请求会进行抢信号，如果没有抢到就会等待。
 * 抢到后需要判断是否已经激活了，未激活就走激活流程，无论成功失败都释放锁。
 * 激活过了就释放锁走下面的流程。
 *
 * <AUTHOR> Yan
 * @date 2019-11-12
 */
@AppScope
public class VerifyActivationImpl implements VerifyActivation, HttpBeforeHandler {

    public static final String TAG = "VerifyActivationImpl";
    @Inject
    @AppScope
    Lazy<AccessTokenManager> mTokenManager;

    @Inject
    @AppScope
    Lazy<InitRequest> mInitRequestLazy;

    /**
     * 是否正在激活， true正在激活
     */
    private volatile boolean activating = false;

    /**
     * 是否已经激活。true表示已经激活
     */
    private volatile boolean activated;

    private HttpCallback<Boolean> mCallback;

    /**
     * 信号量，获取不到许可就会一直等待。
     */
    private Semaphore mSemaphore;

    @Inject
    public VerifyActivationImpl() {
        mSemaphore = new Semaphore(1, true);
    }

    @Override
    public void autoActivate(Request request) {
        Log.i(TAG, "autoActivate: 开始自动激活，activating=" + activating + ", activated=" + activated + ", 线程=" + Thread.currentThread() + ", URL=" + request.url().url());
        try {
            mSemaphore.acquire();
            Log.i(TAG, "autoActivate: 获取信号量成功");
            //不是正在激活并且是未激活才同步执行激活
            if (!activating && !activated) {
                activating = true;
                Log.i(TAG, "autoActivate: 开始执行同步激活");
                try {
                    String openId = mInitRequestLazy.get().activate();
                    Log.i(TAG, "autoActivate: 同步激活返回openId=" + (TextUtils.isEmpty(openId) ? "为空" : "不为空"));
                    saveOpenId(openId);
                } catch (Exception e) {
                    Log.e(TAG, "autoActivate: 同步激活失败", e);
                } finally {
                    activating = false;
                }
            } else {
                Log.i(TAG, "autoActivate: 跳过同步激活，activating=" + activating + ", activated=" + activated);
            }
        } catch (InterruptedException e) {
            Log.e(TAG, "autoActivate: 获取信号量被中断", e);
        } finally {
            activating = false;
            mSemaphore.release();
            Log.i(TAG, "autoActivate: 释放信号量");
        }
    }

    @SuppressLint("CheckResult")
    private void saveOpenId(String openId) {
        Log.i(TAG, "saveOpenId: 开始保存openId，openId是否为空=" + TextUtils.isEmpty(openId));
        if (!TextUtils.isEmpty(openId)) {
            Log.i(TAG, "saveOpenId: 保存openId到SpUtil，openId=" + openId);
            SpUtil.putString("AccessTokenOpenId", openId);
            Observable.fromCallable(() -> {
                Log.i(TAG, "saveOpenId: 开始设置AccessToken");
                AccessTokenManager accessTokenManager = mTokenManager.get();
                if (accessTokenManager != null) {
                    Log.i(TAG, "saveOpenId: AccessTokenManager不为空，获取KaolaAccessToken");
                    KaolaAccessToken kaolaAccessToken = accessTokenManager.getKaolaAccessToken();
                    if (kaolaAccessToken != null) {
                        Log.i(TAG, "saveOpenId: KaolaAccessToken不为空，设置openId=" + openId);
                        String oldOpenId = kaolaAccessToken.getOpenId();
                        Log.i(TAG, "saveOpenId: 原openId=" + (TextUtils.isEmpty(oldOpenId) ? "为空" : oldOpenId));
                        kaolaAccessToken.setOpenId(openId);
                        accessTokenManager.setCurrentAccessToken(kaolaAccessToken);
                        activated = true;
                        Log.i(TAG, "saveOpenId: AccessToken设置成功，activated=true");
                    } else {
                        Log.e(TAG, "saveOpenId: KaolaAccessToken为空，无法设置openId");
                    }
                    if (activated) {
                        new Handler(Looper.getMainLooper()).post(() -> {
                            if (mCallback != null) {
                                Log.i(TAG, "saveOpenId: 回调激活成功");
                                mCallback.onSuccess(true);
                                mCallback = null;
                            } else {
                                Log.w(TAG, "saveOpenId: mCallback为空，无法回调激活成功");
                            }
                        });
                    } else {
                        new Handler(Looper.getMainLooper()).post(() -> {
                            if (mCallback != null) {
                                Log.e(TAG, "saveOpenId: 回调激活失败，同步激活失败");
                                mCallback.onError(new ApiException("同步激活失败"));
                                mCallback = null;
                            } else {
                                Log.w(TAG, "saveOpenId: mCallback为空，无法回调激活失败");
                            }
                        });
                    }
                    return true;
                } else {
                    Log.e(TAG, "saveOpenId: AccessTokenManager为空，无法设置openId");
                    throw new RuntimeException("accessTokenManager is NULL");
                }
            }).retryWhen(throwableObservable -> throwableObservable
                    .zipWith(Observable.range(1, 5), (error, retryCount) -> retryCount)
                    .flatMap(retryCount -> {
                        Log.w(TAG, "saveOpenId: 操作重试: 第 " + retryCount + " 次重试，将在 600ms 后重试");
                        return Observable.timer(600, TimeUnit.MILLISECONDS);
                    })
            ).subscribe(
                    result -> Log.i(TAG, "saveOpenId: 操作成功: " + result),
                    error -> Log.e(TAG, "saveOpenId: 操作最终失败: ", error)
            );
        } else {
            Log.e(TAG, "saveOpenId: openId为空，激活失败");
            new Handler(Looper.getMainLooper()).post(() -> {
                if (mCallback != null) {
                    Log.e(TAG, "saveOpenId: 回调激活失败，openId为空");
                    mCallback.onError(new ApiException("同步激活失败，openId为空"));
                    mCallback = null;
                } else {
                    Log.w(TAG, "saveOpenId: mCallback为空，无法回调激活失败");
                }
            });
        }
    }

    /**
     * 判断是否重新激活
     *
     * @param url
     * @return
     */
    private boolean shouldReactivate(URL url) {
        if (activated || !ApiHostConstants.OPEN_KAOLA_HOST.equals(url.getHost())) {
            return false;
        }
        String urlPath = url.getPath();
        boolean shouldReactivate = !KaolaApiConstant.REQUEST_KAOLA_INIT.equals(urlPath)
                && !KaolaApiConstant.REQUEST_KAOLA_ACTIVATE.equals(urlPath);
        Log.i(TAG, "shouldReactivate: URL=" + url + ", 是否需要重新激活=" + shouldReactivate);
        return shouldReactivate;
    }

    @Override
    public void activate(HttpCallback<Boolean> callback) {
        Log.i(TAG, "activate: 开始激活，activating=" + activating + ", activated=" + activated);
        mCallback = callback;
        if (activating) {
            Log.w(TAG, "activate: 正在激活中，跳过");
            return;
        }
        activating = true;
        try {
            Log.i(TAG, "activate: 尝试获取信号量");
            mSemaphore.acquire();
            Log.i(TAG, "activate: 获取信号量成功");
            if (activated) {
                Log.i(TAG, "activate: 已经激活，释放信号量并返回");
                mSemaphore.release();
                return;
            }
            Log.i(TAG, "activate: 调用activateOrInitKaola开始激活");
            mInitRequestLazy.get().activateOrInitKaola(new HttpCallback<String>() {
                @Override
                public void onSuccess(String s) {
                    Log.i(TAG, "activate: activateOrInitKaola成功，openId是否为空=" + TextUtils.isEmpty(s));
                    saveOpenId(s);
                    activating = false;
                    mSemaphore.release();
                    Log.i(TAG, "activate: 释放信号量");
                }

                @Override
                public void onError(ApiException exception) {
                    Log.e(TAG, "activate: activateOrInitKaola失败", exception);
                    callback.onError(exception);
                    activating = false;
                    mSemaphore.release();
                    Log.i(TAG, "activate: 释放信号量");
                }
            });
        } catch (InterruptedException e) {
            Log.e(TAG, "activate: 获取信号量被中断", e);
            e.printStackTrace();
            activating = false;
            mSemaphore.release();
            Log.i(TAG, "activate: 释放信号量");
        }
    }

    @Override
    public boolean isActivate() {
        KaolaAccessToken kaolaAccessToken = mTokenManager.get().getKaolaAccessToken();
        if (kaolaAccessToken == null) {
            Log.w(TAG, "isActivate: KaolaAccessToken为空，返回未激活状态");
            return false;
        }
        activated = !TextUtils.isEmpty(kaolaAccessToken.getOpenId());
        Log.i(TAG, "isActivate: 检查激活状态，openId是否为空=" + TextUtils.isEmpty(kaolaAccessToken.getOpenId()) + ", activated=" + activated);
        return activated;
    }

    public void clearAll() {
        Log.i(TAG, "clearAll: 清除所有数据");
        mTokenManager.get().clearAll();
    }

    @Override
    public Request onHttpRequestBefore(Interceptor.Chain chain, Request request) {
        if (shouldReactivate(request.url().url())) {
            Log.i(TAG, "onHttpRequestBefore: 需要重新激活，URL=" + request.url().url());
            autoActivate(request);
        }
        return request;
    }

    //由于是接口的default方法，加override会编译不通过。
    public int priority() {
        return 10;
    }
}
