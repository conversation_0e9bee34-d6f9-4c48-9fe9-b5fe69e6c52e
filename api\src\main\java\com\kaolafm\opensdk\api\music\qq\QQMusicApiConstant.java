package com.kaolafm.opensdk.api.music.qq;


/**
 * qq音乐api常量
 *
 * <AUTHOR>
 * @date 2018/4/19
 */
final class QQMusicApiConstant {

    private static final String QQMUSIC_BIN = "/fcgi-bin";

    //-------------------------------返回码-----------------

    public static final int CODE_LOGIN_STATUS_FAILED = 10300;
    /**微信登陆态较验失败*/
    public static final int CODE_LOGIN_STATUS_FAILED_WX = 10500;
    /**第三方登录态较验失败*/
    public static final int CODE_LOGIN_STATUS_FAILED_THIRD = 10600;
    /**联合登录下，微信登陆态较验失败!*/
    public static final int CODE_LOGIN_STATUS_FAILED_CONNECT_WX = 11104;
    /** 签名验证不合法 */
    public static final int CODE_LOGIN_STATUS_FAILED_VERIFICATION = 10203;

    //====================================随便听听电台============================================

    //====================================歌词============================================

    /**
     * 获取歌词
     */
    public static final String GET_LYRICS = QQMUSIC_BIN + "/fcg_music_custom_get_lyric.fcg";

    //====================================登录============================================

    /**
     * 获取微信登录二维码URL
     */
    public static final String GET_WECHAT_QR_CODE_FOR_LOGIN = QQMUSIC_BIN + "/fcg_music_custom_get_wx_two_dimension_code.fcg";

    /**
     * OPI业务方通过微信用户授权code，登录音乐，并换取、刷新音乐相关凭证接口。
     * 其中返回参数中music_id，music_key用于音乐接口访问的凭证，有具有有效期。
     */
    public static final String GET_QQMUSIC_DOCUMENTS_BY_WECHAT = QQMUSIC_BIN + "/fcg_music_custom_wx_login.fcg";

    /**
     * OPI业务方申请业务绑定得到bind_music_third_appname，
     * 然后通过微信用户登录第三方的openid，access_token，登录音乐，并换取、刷新音乐相关凭证接口。
     * 其中返回参数中music_id，music_key用于音乐接口访问的凭证，具有有效期。
     */
    public static final String WECHAT_LOGIN_WITH_THIRD_PARTY = QQMUSIC_BIN + "/fcg_music_custom_wx_login_third_party.fcg";

    /**OPI业务方获取音乐QQ二维码url， 登录，获取用户信息，刷新票据等功能。 */
    public static final String QQ_LOGIN_FUNCTIONS = QQMUSIC_BIN + "/fcg_music_custom_qq_connect_login.fcg";

    //====================================歌曲============================================
    /**
     * 根据mid获取歌曲信息，可批量获取，多mid有逗号隔开
     */
    public static final String GET_SONG_LIST_BATCH = QQMUSIC_BIN + "/fcg_music_custom_get_song_info_batch.fcg";

    //====================================搜索============================================

    /**
     * 根据关键词搜索歌曲
     */
    public static final String SEARCH_SONG_BY_KEYWORD = QQMUSIC_BIN + "/fcg_music_custom_search.fcg";

    //====================================歌单============================================

    /**
     * 获取歌单广场歌单
     */
    public static final String GET_SONGMENU_LIST_OF_SQUARE = QQMUSIC_BIN + "/fcg_music_custom_get_songlist_square.fcg";

    /**
     * 歌单广场的歌单进行收藏、取消收藏操作，同时可获取用户收藏歌单。
     */
    public static final String OPERATE_SONGMENU_OF_SQUATE = QQMUSIC_BIN + "/fcg_music_custom_oper_songlist_square.fcg";

    /**
     * 获取个人歌单列表
     */
    public static final String GET_SELF_SONGMENU_LIST = QQMUSIC_BIN + "/fcg_music_custom_get_songlist_self.fcg";

    /**
     * 歌单中增加/删除歌曲
     */
    public static final String OPERATE_SONG_OF_SONGMENU = QQMUSIC_BIN + "/fcg_music_custom_oper_song_of_songlist.fcg";

    /**
     * 获取歌单中歌曲列表。（由于歌单歌曲数不可控，目前只返回简要索引信息，播放再请求获取批量歌曲信息接口
     */
    public static final String GET_SONG_LIST_OF_SONGMENU = QQMUSIC_BIN + "/fcg_music_custom_get_songlist_detail.fcg";

    //====================================公共电台=========================================

    /**
     * 获取公共电台列表
     */
    public static final String GET_PUBLIC_RADIO_LIST = QQMUSIC_BIN + "/fcg_music_custom_get_radio_list.fcg";

    /**
     * 根据电台id获取电台歌曲
     */
    public static final String GET_SONG_LIST_OF_RADIO = QQMUSIC_BIN + "/fcg_music_custom_get_radio_songlist.fcg";

    //====================================个性化推荐========================================

    /**
     * 每日推荐30首
     */
    public static final String GET_DAY_RECOMMEND_SONGS = QQMUSIC_BIN + "/fcg_music_custom_rc_songs.fcg";

    /**
     * 获取音乐后台每日推荐的歌单。
     */
    public static final String GET_DAY_RECOMMEND_SONGMENUS = QQMUSIC_BIN + "/fcg_music_custom_rc_station_list.fcg";

    /**
     * 依据传入的qq号，获取个性化算法所推荐的歌曲。同时为了能更好的反馈推荐数据的准确性，接口还包括：歌曲收藏、歌曲删除、播放下一首、一直听到结束等反馈功能，用于动态调整推荐数据。
     */
    public static final String GET_INDIVIDUAL_SONGS_OF_RADIO = QQMUSIC_BIN + "/fcg_music_custom_individual_radio.fcg";

    //====================================排行榜===========================================

    /**
     * 获取qq音乐的排行榜的歌单。
     */
    public static final String GET_SONG_CHARTS_LIST = QQMUSIC_BIN + "/fcg_music_custom_get_toplist.fcg";

    /**
     * 获取qq音乐的排行榜的歌曲
     */
    public static final String GET_SONG_LIST_OF_SONG_CHARTS = QQMUSIC_BIN + "/fcg_music_custom_get_toplist_info.fcg";

    //====================================专辑============================================

    /**
     * 通过专辑id，获取专辑内的歌曲信息。可多张专辑一起获取
     */
    public static final String GET_SONG_LIST_OF_ALBUM = QQMUSIC_BIN + "/fcg_music_custom_get_album_info_batch.fcg";

    //====================================歌手============================================

    /**
     * 通过歌手id，获取歌手下的歌曲信息，可分页拉取。
     */
    public static final String GET_SONG_LIST_OF_SINGER = QQMUSIC_BIN + "/fcg_music_custom_get_singer_info.fcg";

    /**
     * 获取热门歌手列表。按照分类索引（支持地区，性别，流派），拉取相应分类下的热门歌手id列表。（注意：目前至多返回200个，部分分类少于200个）
     */
    public static final String GET_SINGER_LIST = QQMUSIC_BIN + "/fcg_music_custom_get_singer_list.fcg";

    /**
     * 通过歌手id，获取歌手下的专辑信息，可分页拉取。
     */
    public static final String GET_ALBUM_LIST_OF_SINGER = QQMUSIC_BIN + "/fcg_music_custom_get_singer_album.fcg";

    //====================================分类============================================

    /**
     * 提供拉取音乐馆分类标签，分类子标签，分类歌曲，分类专辑等四功能
     */
    public static final String GET_CATEGORY_LIBRARY = QQMUSIC_BIN + "/fcg_music_custom_library_category.fcg";

    //====================================用户信息============================================

    /**
     * 查询接入业务下，登录用户的会员状态
     */
    public static final String GET_USER_VIP_INFO = QQMUSIC_BIN + "/fcg_music_custom_user_vip_info.fcg";


}
