package com.kaolafm.opensdk.demo.search;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemSelectedListener;
import android.widget.EditText;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Spinner;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.OnClick;
import com.kaolafm.opensdk.OpenSDK;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;

/**
 * <AUTHOR>
 * @date 2018/8/2
 */

public class VoiceSearchActivity extends BaseActivity {

    @BindView(R.id.et_search_album_name)
    EditText mEtSearchAlbumName;

    @BindView(R.id.et_search_artist)
    EditText mEtSearchArtist;

    @BindView(R.id.et_search_audio_name)
    EditText mEtSearchAudioName;

    @BindView(R.id.et_search_category)
    EditText mEtSearchCategory;

    @BindView(R.id.et_search_voice_source)
    EditText mEtSearchVoiceSource;

    @BindView(R.id.et_search_semantics_keyword)
    EditText mEtSearchSemanticsKeyword;

    @BindView(R.id.et_search_voice_origin_text)
    EditText mEtSearchVoiceOriginText;

    @BindView(R.id.et_search_voice_text)
    EditText mEtSearchVoiceText;

    @BindView(R.id.rb_search_field_integrate)
    RadioButton mRbSearchFieldIntegrate;

    @BindView(R.id.rb_search_field_music)
    RadioButton mRbSearchFieldMusic;

    @BindView(R.id.rg_search_field)
    RadioGroup mRgSearchField;

    @BindView(R.id.tv_search_field_title)
    TextView mTvSearchFieldTitle;

    @BindView(R.id.tv_search_semantics_commit)
    TextView mTvSearchSemanticsCommit;

    @BindView(R.id.tv_search_semantics_title)
    TextView mTvSearchSemanticsTitle;

    //搜索资源类型
    private String mType;

    /**
     * 语音商返回的原始json串
     */
    private String mOriginJson;

    @Override
    public int getLayoutId() {
        return R.layout.activity_search;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        setTitle("语义搜索");
    }

    @Override
    public void initData() {
    }

    @OnClick({R.id.tv_search_semantics_commit})
    public void onViewClicked(View view) {
        Intent intent = new Intent(this, SearchResultActivity.class);
        switch (view.getId()) {
            case R.id.tv_search_semantics_commit:
                intent.putExtra(SearchResultActivity.KEY_SEARCH_TYPE, SearchResultActivity.SEARCH_BY_SEMATICS);
                mOriginJson = mEtSearchVoiceOriginText.getText().toString().trim();
                intent.putExtra(SearchResultActivity.KEY_ORIGIN_JSON, mOriginJson);

                //场景
                int field;
                int checkedFieldId = mRgSearchField.getCheckedRadioButtonId();
                if (checkedFieldId == R.id.rb_search_field_music) {
                    field = 1;
                } else if (checkedFieldId == R.id.rb_search_field_integrate) {
                    field = 2;
                } else if (checkedFieldId == R.id.rb_search_field_broadcast) {
                    field = 6;
                } else {
                    showToast("场景必填");
                    return;
                }
                intent.putExtra(SearchResultActivity.KEY_FIELD, field);

                String artist = mEtSearchArtist.getText().toString().trim();
                String audioName = mEtSearchAudioName.getText().toString().trim();
                //艺术家
                intent.putExtra(SearchResultActivity.KEY_ARTIST, artist);
                //音频名称
                intent.putExtra(SearchResultActivity.KEY_AUDIO_NAME, audioName);
                //专辑名
                intent.putExtra(SearchResultActivity.KEY_ALBUM_NAME, mEtSearchAlbumName.getText().toString().trim());
                //分类
                intent.putExtra(SearchResultActivity.KEY_CATEGORY, mEtSearchCategory.getText().toString().trim());
                //音源标识
                String voiceSource = mEtSearchVoiceSource.getText().toString().trim();
                if (TextUtils.isEmpty(voiceSource)) {
                    showToast("音源不能为空，可以传0");
                    return;
                }
                intent.putExtra(SearchResultActivity.KEY_VOICE_SOURCE, voiceSource);
                //关键词
                String semanticsKeyword = mEtSearchSemanticsKeyword.getText().toString().trim();
                if (TextUtils.isEmpty(semanticsKeyword)) {
                    showToast("关键词不能为空");
                    return;
                }
                intent.putExtra(SearchResultActivity.KEY_KEYWORD, semanticsKeyword);
                //用户声控的原始串
                String voiceText = mEtSearchVoiceText.getText().toString().trim();
                if (TextUtils.isEmpty(voiceText)) {
                    showToast("用户声控的原始串不能为空");
                    return;
                }
                intent.putExtra(SearchResultActivity.KEY_VOICE_TEXT, voiceText);
                startActivity(intent);
                break;
            default:
                break;
        }
    }
}
