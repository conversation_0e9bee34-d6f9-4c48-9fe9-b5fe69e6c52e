package com.kaolafm.opensdk.api.live.model;


/**
 * <AUTHOR>
 * <p>
 * 发送消息的基本信息
 */
public class SendChatMsgData {

    // todo chuanlive -> import com.netease.nimlib.sdk.msg.constant.SessionTypeEnum;
//    public SessionTypeEnum sessionType;
    /**
     * 聊天内容
     */
    public String content;
    /**
     * 房间ID
     */
    public String sessionId;
    /**
     * 文件对象
     */
    public String file;

    /**
     * 语音时长
     */
    public int duration;

    /**
     * 是否启用自家图片服务器true为是，false为否
     */
    public boolean canUserSelfImageServer;
}