package com.kaolafm.opensdk.utils.operation;

import android.text.TextUtils;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.operation.model.category.CategoryMember;
import com.kaolafm.opensdk.api.operation.model.column.CategoryColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;
import javax.inject.Inject;

/**
 * <AUTHOR> @date 2018/11/8
 */

public class CategoryProcessor implements IOperationProcessor {

    @Inject
    public CategoryProcessor() {
    }

    @Override
    public boolean accept(CategoryMember member) {
        return false;
    }

    @Override
    public boolean accept(ColumnMember member) {
        return member instanceof CategoryColumnMember;
    }

    @Override
    public long getId(CategoryMember member) {
        return 0;
    }

    @Override
    public long getId(ColumnMember member) {
        String categoryCode = ((CategoryColumnMember) member).getCategoryCode();
        return !TextUtils.isEmpty(categoryCode)? Integer.valueOf(categoryCode) : 0;
    }

    @Override
    public long getListenNum(CategoryMember member) {
        return 0;
    }

    @Override
    public int getType(CategoryMember member) {
        return 0;
    }

    @Override
    public int getType(ColumnMember member) {
        return ResType.TYPE_CATEGORY;
    }

    @Override
    public void play(CategoryMember member) {

    }

    @Override
    public void play(ColumnMember member) {

    }
}
