package com.kaolafm.opensdk.api.operation.model.column;

import com.kaolafm.opensdk.api.operation.model.component.ComponentBase;

import java.util.List;

/**
 *
 */
public class ColumnChild extends Column{
    /*
    * 置顶成员	对象	（该字段当type=column时有效）
    * */
    private ColumnMember topColumnMember;
    /*
    * 运营方式	Integer	1人工，2自动（该字段当type=column时有效）
    * */
    private int isManual;
    /*
    *组件列表	对象数组	（该字段当type=column时有效）
     */
    private List<ComponentBase> columnComponents;
}
