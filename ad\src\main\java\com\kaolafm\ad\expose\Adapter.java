package com.kaolafm.ad.expose;

import com.kaolafm.ad.api.model.Advert;

/**
 * 广告各个类型对应的执行类适配接口。
 * <AUTHOR>
 * @date 2020-02-17
 */
public interface Adapter<T> extends Executor {

    /**
     * 判断广告是否是对应的类型
     *
     * @param advert 广告
     * @return true 表示是
     */
    boolean accept(Advert advert);

    /**
     * 曝光广告。这里的广告不会为空，在Manager类中已经判空过了，
     *
     * @param advert
     */
    void expose(Advert advert);

    /**
     * 关闭广告。这里的广告不会为空，在Manager类中已经判空过了
     *
     * @param advert
     */
    void close(Advert advert);

    /**
     * 获取对应的广告执行类接口
     *
     * @return 广告执行类接口
     */
    T getExecutor();

    /**
     * 设置对应的广告执行类接口
     *
     * @param t 执行类接口
     */
    void setExecutor(T t);
}
