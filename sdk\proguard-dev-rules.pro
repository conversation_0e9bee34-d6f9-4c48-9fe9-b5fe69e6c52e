-include ./proguard-sdk-rules.pro
-include ../ad/proguard-advert-dev-rules.pro
-include ../report/proguard-report-dev-rules.pro
# 这句话能够使我们的项目混淆后产生映射文件
# 包含有类名->混淆后类名的映射关系
-verbose
-printmapping proguardMapping-SDK-dev.txt

-keepclasseswithmembernames class com.kaolafm.opensdk.api.** { *;}
-keep class com.kaolafm.opensdk.account.profile.KaolaProfileManager{*;}
-keepclasseswithmembernames class com.kaolafm.opensdk.KradioSDK{*;}
-keep class com.kaolafm.**{*;}

