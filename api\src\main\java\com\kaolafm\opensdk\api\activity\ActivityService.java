package com.kaolafm.opensdk.api.activity;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.activity.model.Activity;
import com.kaolafm.opensdk.api.activity.model.ActivityInfo;

import java.util.List;

import io.reactivex.Single;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Query;

public interface ActivityService {

    /**
     * 活动列表
     *
     * @return 活动列表
     */
    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER})
    @GET(KaolaApiConstant.GET_ACTIVITY_INFO_LIST)
    Single<BaseResult<BasePageResult<List<Activity>>>> getInfoList(@Query("appid") String appid);
    /**
     * 活动详情
     *
     * @return 活动详情
     */
    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER})
    @GET(KaolaApiConstant.GET_ACTIVITY_INFO)
    Single<BaseResult<ActivityInfo>> getInfoActivty(@Query("activityId") String activityId);

    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER})
    @GET(KaolaApiConstant.GET_ACTIVITY_INFO_LIST)
    Single<BaseResult<BasePageResult<List<Activity>>>> getInfoList(@Query("appid") String appid
            , @Query("pageNum") int pageNum, @Query("pageSize") int pageSize);

    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER})
    @GET(KaolaApiConstant.GET_ACTIVITY_INFO_LIST)
    Call<BaseResult<BasePageResult<List<Activity>>>> getInfoListSync(@Query("appid") String appid);
}
