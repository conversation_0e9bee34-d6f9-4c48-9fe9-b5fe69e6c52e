package com.kaolafm.opensdk.api.purchase;

import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.api.purchase.model.Order;
import com.kaolafm.opensdk.api.purchase.model.PurchaseSucess;
import com.kaolafm.opensdk.api.purchase.model.PurchasedItem;
import com.kaolafm.opensdk.api.purchase.model.QRCodeInfo;
import com.kaolafm.opensdk.api.purchase.model.VipMeals;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.List;

/**
 * 支付相关http请求
 */
public class PurchaseRequest extends BaseRequest {

    private final PurchaseService mService;

    public PurchaseRequest() {
        this.mService = obtainRetrofitService(PurchaseService.class);
    }

    /**
     * 已购列表
     * @param pageNum 页码
     * @param pageSize 行数
     * @param callback 回调结果
     */
    public void getPurchasedList(int pageNum, int pageSize,
                                 HttpCallback<BasePageResult<List<PurchasedItem>>> callback){
        doHttpDeal(mService.getPurchasedList(pageNum, pageSize), BaseResult::getResult, callback);
    }

    /**
     * 已购列表
     *
     * @param pageNum 页码
     * @param pageSize 行数
     */
    private BasePageResult<List<PurchasedItem>> getPurchasedListSync(int pageNum, int pageSize) {
        BaseResult<BasePageResult<List<PurchasedItem>>> baseResult = doHttpDealSync(mService.getPurchasedListSync(pageNum, pageSize));
        return baseResult != null? baseResult.getResult() : null;
    }

    /**
     * 已购列表一键播放
     * @param clockid  如果为空播放首页，非空为返回结果里的clockId。
     */
    private BasePageResult<List<AudioDetails>> getPlayPurchasedListSync(long clockid) {
        BaseResult<BasePageResult<List<AudioDetails>>> baseResult = doHttpDealSync(mService.getPlayPurchasedListSync(clockid));
        return baseResult != null? baseResult.getResult() : null;
    }

    /**
     * 已购列表一键播放
     * @param clockid 如果为空播放首页，非空为返回结果里的clockId。
     * @param callback 回调结果
     */
    public void getPlayPurchasedList(String clockid,
                                     HttpCallback<List<AudioDetails>> callback){
        doHttpDeal(mService.getPlayPurchasedList(clockid), BaseResult::getResult, callback);
    }


    /**
     * 订单列表
     * @param pageNum 页码
     * @param pageSize 行数
     * @param callback 回调结果
     */
    public void getOrderList(int pageNum, int pageSize,
                                 HttpCallback<BasePageResult<List<Order>>> callback) {
        doHttpDeal(mService.getOrderList(pageNum, pageSize), BaseResult::getResult, callback);
    }
    /**
     * 订单列表
     * @param pageNum 页码
     * @param pageSize 行数
     */
    public BasePageResult<List<Order>> getOrderList(int pageNum, int pageSize) {
        BaseResult<BasePageResult<List<Order>>> baseResult =  doHttpDealSync(mService.getOrderListSync(pageNum, pageSize));
        return baseResult != null? baseResult.getResult() : null;
    }

    /**
     * 获取vip套餐信息列表
     * @param callback 回调结果
     */
    public void getVipMeats(HttpCallback<List<VipMeals>> callback) {
        doHttpDeal(mService.getVipMeals(), BaseResult::getResult, callback);
    }
    /**
     * 获取vip套餐信息列表
     */
    public List<VipMeals> getVipMeatsSync() {
        BaseResult<List<VipMeals>> baseResult =  doHttpDealSync(mService.getVipMealsSync());
        return baseResult != null? baseResult.getResult() : null;
    }

    /**
     * 生成购买vip二维码
     * @param mealId vip套餐id
     * @param mealMoney 套餐金额
     * @param callback 回调结果
     */
    public void getVipQRCode(Long mealId, Long mealMoney,
                            HttpCallback<QRCodeInfo> callback) {
        doHttpDeal(mService.getVipQRCode(mealId,mealMoney), BaseResult::getResult, callback);
    }
    /**
     * 生成购买vip二维码
     * @param meatId vip套餐id
     * @param meatMoney 套餐金额
     */
    public QRCodeInfo getVipQRCodeSync(Long meatId, Long meatMoney) {
        BaseResult<QRCodeInfo> baseResult =  doHttpDealSync(mService.getVipQRCodeSync(meatId,meatMoney));
        return baseResult != null? baseResult.getResult() : null;
    }

    /**
     * 生成购买专辑二维码
     * @param albumId 专辑id
     * @param money 专辑金额
     * @param callback 回调结果
     */
    public void getAlbumQRCode(Long albumId, Long money,
                             HttpCallback<QRCodeInfo> callback) {
        doHttpDeal(mService.getAlbumQRCode(albumId,money), BaseResult::getResult, callback);
    }
    /**
     * 生成购买专辑二维码
     * @param albumId 专辑id
     * @param money 专辑金额
     */
    public QRCodeInfo getAlbumQRCodeSync(Long albumId, Long money) {
        BaseResult<QRCodeInfo> baseResult =  doHttpDealSync(mService.getAlbumQRCodeSync(albumId,money));
        return baseResult != null? baseResult.getResult() : null;
    }

    /**
     * 生成购买单曲二维码
     * @param audioIds 单曲id 逗号分隔的id字符串
     * @param money 专辑金额
     * @param callback 回调结果
     */
    public void getAudioQRCode(String audioIds, Long albumId, Long money,
                               HttpCallback<QRCodeInfo> callback) {
        doHttpDeal(mService.getAudioQRCode(audioIds,albumId,money), BaseResult::getResult, callback);
    }
    /**
     * 生成购买单曲二维码
     * @param audioIds 单曲id 逗号分隔的id字符串
     * @param money 专辑金额
     */
    public QRCodeInfo getAudioQRCodeSync(String audioIds, Long albumId,Long money) {
        BaseResult<QRCodeInfo> baseResult =  doHttpDealSync(mService.getAudioQRCodeSync(audioIds,albumId,money));
        return baseResult != null? baseResult.getResult() : null;
    }

    /**
     * 查看二维码状态
     * @param qrCodeId 二维码id
     * 0-未支付，1-已支付，2-过期
     * @param callback 回调结果
     */
    public void qrCodeStatus(String qrCodeId,
                               HttpCallback<PurchaseSucess> callback) {
        doHttpDeal(mService.qrCodeStatus(qrCodeId), BaseResult::getResult, callback);
    }
    /**
     * 查看二维码状态
     * @param qrCodeId 二维码id
     * @return 0-未支付，1-已支付，2-过期
     */
    public PurchaseSucess qrCodeStatusSync(String qrCodeId) {
        BaseResult<PurchaseSucess> baseResult =  doHttpDealSync(mService.qrCodeStatusSync(qrCodeId));
        return baseResult != null? baseResult.getResult() : null;
    }

    /**
     * 云币购买专辑
     * @param albumId 专辑id
     * @param money 专辑金额
     * @param callback 回调结果
     */
    public void buyAlbumByCoin(Long albumId,Long money,
                             HttpCallback<PurchaseSucess> callback) {
        doHttpDeal(mService.buyAlbumByCoin(albumId,money), BaseResult::getResult, callback);
    }
    /**
     * 云币购买专辑
     * @param albumId 专辑id
     * @param money 专辑金额
     * @return 是否成功
     */
    public PurchaseSucess buyAlbumByCoinSync(Long albumId,Long money) {
        BaseResult<PurchaseSucess> baseResult =  doHttpDealSync(mService.buyAlbumByCoinSync(albumId,money));
        return baseResult != null? baseResult.getResult() : null;
    }

    /**
     * 云币购买单曲
     * @param money 金额
     * @param audioIds 单曲id 逗号分隔的id字符串
     * @param callback 回调结果
     */
    public void buyAudiosByCoin(String audioIds,Long albumId, Long money,
                               HttpCallback<PurchaseSucess> callback) {
        doHttpDeal(mService.buyAudiosByCoin(audioIds, albumId, money), BaseResult::getResult, callback);
    }
    /**
     * 云币购买单曲
     *@param money 金额
     * @param audioIds 单曲id 逗号分隔的id字符串
     * @return 是否成功
     */
    public PurchaseSucess buyAudiosByCoinSync(String audioIds,Long albumId, Long money) {
        BaseResult<PurchaseSucess> baseResult =  doHttpDealSync(mService.buyAudiosByCoinSync(audioIds, albumId,money));
        return baseResult != null? baseResult.getResult() : null;
    }

    /**
     * 生成购买商品二维码
     * @param goodsId   商品id
     * @param money     支付金额
     * @param payType   支付方式
     * @param callback
     */
    public void getGoodsQRCode(Long goodsId,
                                 Long money,
                                 Integer payType,
                                 HttpCallback<QRCodeInfo> callback) {
//        mUrlManager.putDomain(ApiHostConstants.MALL_DOMAIN_NAME, ApiHostConstants.MALL_HOST);
        doHttpDeal(mService.getGoodsQRCode(goodsId, money, payType), BaseResult::getResult, callback);
    }

    /**
     * 获取购买商品二维码的支付结果
     */
    public void getGoodsQRCodeStatus(String qrCodeId,
                                HttpCallback<PurchaseSucess> callback) {
//        mUrlManager.putDomain(ApiHostConstants.MALL_DOMAIN_NAME, ApiHostConstants.MALL_HOST);
        doHttpDeal(mService.getQRCodeStatus(qrCodeId), BaseResult::getResult, callback);
    }
}
