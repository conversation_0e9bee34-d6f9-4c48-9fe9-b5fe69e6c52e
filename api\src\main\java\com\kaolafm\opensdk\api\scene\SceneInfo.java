package com.kaolafm.opensdk.api.scene;

import android.os.Parcel;
import android.os.Parcelable;
import com.google.gson.annotations.SerializedName;
import com.kaolafm.opensdk.ResType;

/**
 * <AUTHOR>
 **/
public class SceneInfo implements Parcelable {

    /**
     * code : 10000
     * icon : http://img.kaolafm.net/mz/images/201610/cb392742-4859-448e-be51-98162476d24a/default.jpg
     * message : 忙碌了一天，轻松一下吧，推荐您收听《点火_晚上》
     * contentId : 1100000000016
     * contentName : 中国原创音乐
     * contentType : 1
     */

    @SerializedName("code")
    private int code;

    @SerializedName("icon")
    private String icon;

    @SerializedName("message")
    private String message;

    @SerializedName("contentId")
    private long contentId;

    @SerializedName("contentName")
    private String contentName;
    /**1是专辑，2是广播，3是直播，4是单曲，5是qq音乐*/
    @SerializedName("contentType")
    private int contentType;
    /**当资源类型是qq音乐时，此字段有效，是qq音乐那边的资源id*/
    @SerializedName("qqOldId")
    private int qqOldId;
    /**当资源类型是qq音乐时，此字段有效，是qq音乐的类型 ：0场景电台 1标签电台*/
    @SerializedName("qqMusicType")
    private int qqMusicType;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public long getContentId() {
        return contentId;
    }

    public void setContentId(long contentId) {
        this.contentId = contentId;
    }

    public String getContentName() {
        return contentName;
    }

    public void setContentName(String contentName) {
        this.contentName = contentName;
    }

    /**
     * 为了统一类型，请使用{@link #getContentResType()}
     * @return
     */
    public int getContentType() {
        return contentType;
    }

    public int getContentResType() {
        switch (contentType) {
            case 0:
                return ResType.TYPE_ALL;
            case 1:
                return ResType.TYPE_ALBUM;
            case 2:
                return ResType.TYPE_BROADCAST;
            case 3:
                return ResType.TYPE_LIVE;
            case 4:
                return ResType.TYPE_RADIO;
            case 5:
                return ResType.TYPE_QQ_MUSIC;
            case 6:
                return ResType.TYPE_NEWS;
            case 7:
                return ResType.TYPE_TV;
            case 8:
                return ResType.TYPE_FEATURE;
            default:
        }
        return contentType;
    }

    public void setContentType(int contentType) {
        this.contentType = contentType;
    }

    public int getQqOldId() {
        return qqOldId;
    }

    public void setQqOldId(int qqOldId) {
        this.qqOldId = qqOldId;
    }

    public int getQqMusicType() {
        return qqMusicType;
    }

    public void setQqMusicType(int qqMusicType) {
        this.qqMusicType = qqMusicType;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.code);
        dest.writeString(this.icon);
        dest.writeString(this.message);
        dest.writeLong(this.contentId);
        dest.writeString(this.contentName);
        dest.writeInt(this.contentType);
        dest.writeInt(this.qqOldId);
        dest.writeInt(this.qqMusicType);
    }

    public SceneInfo() {
    }

    protected SceneInfo(Parcel in) {
        this.code = in.readInt();
        this.icon = in.readString();
        this.message = in.readString();
        this.contentId = in.readLong();
        this.contentName = in.readString();
        this.contentType = in.readInt();
        this.qqOldId = in.readInt();
        this.qqMusicType = in.readInt();
    }

    public static final Creator<SceneInfo> CREATOR = new Creator<SceneInfo>() {
        @Override
        public SceneInfo createFromParcel(Parcel source) {
            return new SceneInfo(source);
        }

        @Override
        public SceneInfo[] newArray(int size) {
            return new SceneInfo[size];
        }
    };

    @Override
    public String toString() {
        return "SceneInfo{" +
                "code=" + code +
                ", icon='" + icon + '\'' +
                ", message='" + message + '\'' +
                ", contentId=" + contentId +
                ", contentName='" + contentName + '\'' +
                ", contentType=" + contentType +
                ", qqOldId=" + qqOldId +
                ", qqMusicType=" + qqMusicType +
                '}';
    }
}
