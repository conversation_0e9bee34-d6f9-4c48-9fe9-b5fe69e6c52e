ext {
    android = [
            compileSdkVersion            : 28,
            buildToolsVersion            : "28.0.3",
            minSdkVersion                : 23,
            targetSdkVersion             : 28,
            versionCode                  : 308,
            versionName                  : "3.0.11.pre18",
            adVersionCode                : 10200,
            adVersionName                : "1.2.0",
            javaSourceVersion            : JavaVersion.VERSION_1_8,
            javaTargetVersion            : JavaVersion.VERSION_1_8
    ]
    version = [
            retrofitSdkVersion           : "2.9.0",
            butterknifeSdkVersion        : "10.2.1",
            rxlifecycle3SdkVersion       : "3.1.0",
            dagger2Version               : "2.25.4"
    ]

    maven = [
            artifactId                   : "open-sdk",
            groupId                      : "com.kaolafm",
            libType                      : "jar",
            libDescription               : "dependences lib",
            LocalRepoUrl                 : readLocalProperties("local.repo.url"),
            UserName                     : "admin",
            Password                     : "QA4A8saRBZFyuicF",

    ]

    dependencies = [
            "junit"                      : "junit:junit:4.12",
            "lifecycle-runtime"          : 'androidx.lifecycle:lifecycle-runtime:2.5.0',
            "lifecycle-extensions"       : 'androidx.lifecycle:lifecycle-extensions:2.2.0',
            "room-runtime"               : 'androidx.room:room-runtime:2.4.1',
            "room-compiler"              : 'androidx.room:room-compiler:2.2.6',
            "room-rxjava2"               : 'androidx.room:room-rxjava2:2.4.1',
            "annotations"                : "androidx.annotation:annotation:1.3.0",
            "retrofit2"                  : "com.squareup.retrofit2:retrofit:${version["retrofitSdkVersion"]}",
            "retrofit2-gson"             : "com.squareup.retrofit2:converter-gson:${version["retrofitSdkVersion"]}",
            "retrofit2-rxjava2"          : "com.squareup.retrofit2:adapter-rxjava2:${version["retrofitSdkVersion"]}",

            "okhttp3"                    : "com.squareup.okhttp3:okhttp:3.10.0",
            "rxandroid2"                 : "io.reactivex.rxjava2:rxandroid:2.1.1",
            "rxjava2"                    : "io.reactivex.rxjava2:rxjava:2.1.16",
            "rxlifecycle3"               : "com.trello.rxlifecycle3:rxlifecycle:${version["rxlifecycle3SdkVersion"]}",
            "rxlifecycle3-components"    : "com.trello.rxlifecycle3:rxlifecycle-components:${version["rxlifecycle3SdkVersion"]}",
            "gson"                       : "com.google.code.gson:gson:2.10.1",
            "greenDao"                   : "org.greenrobot:greendao:3.3.0",
            "dagger2"                    : "com.google.dagger:dagger:${version["dagger2Version"]}",
            "dagger2-compiler"           : "com.google.dagger:dagger-compiler:${version["dagger2Version"]}",
            "dagger2-android"            : "com.google.dagger:dagger-android:${version["dagger2Version"]}",
            "dagger2-android-support"    : "com.google.dagger:dagger-android-support:${version["dagger2Version"]}",
            "dagger2-android-processor"  : "com.google.dagger:dagger-android-processor:${version["dagger2Version"]}",

            // alibaba
            "fastjson"                   : "com.alibaba:fastjson:1.2.83",
            //bug report
//            "ha-adapter"               : "com.aliyun.ams:alicloud-android-ha-adapter:1.2.2.0-open",
//            "ha-crashreporter"         : "com.aliyun.ams:alicloud-android-ha-crashreporter:1.7.0",

    ]
    def domainType = System.getProperty("dt", "")
    config = [
            "api_version" : System.getProperty("av", "v3"),
            "domain_type" : domainType ? ".${domainType}" : "",
//            "snap" :System.getProperty("av","") == "v3" ? "-SNAPSHOT" : "",
            "repository" : System.getProperty("repos", "")
    ]
}



def readLocalProperties(String name) {
    def properties = new Properties()
    File file = project.rootProject.file('local.properties')
    if (file.exists()) {
        def inputStream = file.newDataInputStream()
        properties.load(inputStream)
        if (properties.containsKey(name)) {
            return properties.getProperty(name)
        }
    }
}