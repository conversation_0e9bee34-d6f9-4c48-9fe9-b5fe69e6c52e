package com.kaolafm.opensdk.http.socket;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.log.Logging;

import org.json.JSONException;
import org.json.JSONObject;

import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import javax.inject.Inject;
import javax.inject.Provider;

import dagger.Lazy;


/**
 * Socket管理类，也是socket对外暴露功能的类。
 *
 * <AUTHOR>
 * @date 2020-01-19
 */
public class SocketManager {

    private static volatile SocketManager mInstance;

    @Inject
    Provider<Socket> mSocketProvider;

    @Inject
    @AppScope
    Lazy<Gson> mGsonLazy;

    private static final ConcurrentHashMap<String, Socket> CLIENTS = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, ListenerWrapper> LISTENERS = new ConcurrentHashMap<>();

    private SocketManager() {
        ComponentKit.getInstance().inject(this);
    }

    public static SocketManager getInstance() {
        if (mInstance == null) {
            synchronized (SocketManager.class) {
                if (mInstance == null) {
                    mInstance = new SocketManager();
                }
            }
        }
        return mInstance;
    }

    /**
     * 获取一个socket实例，如果已经缓存中有会在缓存中取，没有就新建并缓存。
     *
     * @param url
     * @param opts
     * @return
     */
    public Socket socket(String url, Socket.Options opts) {
        Socket client = CLIENTS.get(url);
        if (client == null) {
            client = newSocket(url, opts);
            CLIENTS.putIfAbsent(url, client);
        }
        return client;
    }

    public Socket socket(String url, String query, Map<String, String> headers) {
        Socket client = CLIENTS.get(url);
        if (client == null) {
            Socket.Options options = new Socket.Options();
            options.query = query;
            options.reconnection = true;
            options.reconnectionDelay = 2000;
            options.headers = headers;
            client = newSocket(url, options);
            CLIENTS.putIfAbsent(url, client);
        }
        return client;
    }

    public Socket socket(String url, String query) {
        return socket(url, query, null);
    }

    private Socket socket(String url) {
        return socket(url, "");
    }

    /**
     * 创建一个socket实例
     *
     * @param url
     * @param opts
     * @return
     */
    public Socket newSocket(String url, Socket.Options opts) {
        URL parse;
        URI source;
        try {
            parse = Url.parse(url);
            source = parse.toURI();
        } catch (URISyntaxException e) {
            throw new RuntimeException(e);
        }
        if (opts == null) {
            opts = new Socket.Options();
        }
        Socket client = mSocketProvider.get();
        client.config(source, opts);
        client.namespace(parse.getPath());
        return client;
    }

    /**
     * 长连接连接后，自动请求一次。
     * @param url
     * @param query
     * @param event
     * @param params
     * @param callback
     * @param <T>
     */
    public <T> void request(String url, String query, String event, Map<String, Object> params, SocketListener<T> callback) {
        Socket socket = socket(url, query);
        if (!TextUtils.isEmpty(event)) {
            socket.on(event, getListener(url, event, callback));
        }
        if (!socket.isConnected()) {
            socket.on(SocketEvent.EVENT_CONNECT, args -> {
                if (TextUtils.isEmpty(event)) {
                    return;
                }
                JSONObject jsonObject = null;
                try {
                    jsonObject = new JSONObject(mGsonLazy.get().toJson(params));
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                Logging.d("emit event %s with param %s", event, jsonObject);
                socket.emit(event, jsonObject);
            });
            socket.connect();
        }
    }

    public <T> void addListener(String url, String event, SocketListener<T> callback) {
        addListener(url, null, event, callback);
    }

    public <T> void addListener(String url, String query, String event, SocketListener<T> callback) {
        addListener(url, query, event, null, callback);
    }

    /**
     * 添加长连接连接，如果长连接未连接，会自动连接。
     * @param url
     * @param query
     * @param event
     * @param callback
     * @param <T>
     */
    public <T> void addListener(String url, String query, String event, Map<String, String> headers, SocketListener<T> callback) {
        Socket socket = socket(url, query, headers);
        ListenerWrapper<T> listener = new ListenerWrapper<>(mGsonLazy.get());
        listener.setCallback(event, callback);
        socket.on(event, listener);
        if (!socket.isConnected()) {
            socket.connect();
        }
    }


    /**
     * 移除长连接监听
     * @param url
     * @param event
     */
    public void removeListener(String url, String event) {
        ListenerWrapper listener = LISTENERS.remove(url);
        if (listener != null) {
            Socket socket = socket(url);
            socket.off(event, listener);
        }
    }

    public <T> void sendMessage(String url, String event, Map<String, Object> params, SocketListener<T> callback) {
        Socket socket = socket(url);
        socket.on(event, getListener(url, event, callback));
        if (!socket.isConnected()) {
            socket.emit(event, params);
        }
    }

    private <T> ListenerWrapper<T> getListener(String url, String event, SocketListener<T> callback) {
        ListenerWrapper<T> listener = new ListenerWrapper<>(mGsonLazy.get());
        listener.setCallback(event, callback);
        LISTENERS.put(url, listener);
        return listener;
    }

    /**
     * 断开指定url的长连接。
     * @param url
     */
    public void disconnect(String url) {
        Socket socket = CLIENTS.get(url);
        if (socket != null) {
            socket.disconnect();
        }
    }

    /**
     * 断开所有长连接
     */
    public void disconnenct() {
        for (Socket socket : CLIENTS.values()) {
            socket.disconnect();
        }
    }

}
