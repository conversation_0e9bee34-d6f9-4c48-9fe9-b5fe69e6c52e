package com.kaolafm.ad.report.api.monitor;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.http.core.HttpCallback;

import io.reactivex.functions.Function;


public class MonitorRequest extends BaseRequest {

    private MonitorApiService monitorApiService;

    public MonitorRequest() {
        monitorApiService = mRepositoryManager.obtainRetrofitService(MonitorApiService.class);
    }

    private Function<retrofit2.Response<Void>, Boolean> map() {
        return retrofit2.Response::isSuccessful;
    }

    public void report(String url,HttpCallback<Boolean> callback){
        doHttpDeal(monitorApiService.report(url),map(),callback);
    }

}
