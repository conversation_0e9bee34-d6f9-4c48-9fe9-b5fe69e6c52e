package com.kaolafm.opensdk.http.core;

import com.kaolafm.opensdk.http.error.ApiException;

import org.reactivestreams.Subscription;

import java.util.concurrent.atomic.AtomicReference;

import io.reactivex.FlowableSubscriber;
import io.reactivex.disposables.Disposable;
import io.reactivex.internal.subscriptions.SubscriptionHelper;
import io.reactivex.internal.util.EndConsumerHelper;

/**
 * 可以dispose的flowable订阅者
 * <AUTHOR>
 * @date 2020-02-16
 */
public class DisposableSubscriber<T> implements FlowableSubscriber<T>, Disposable {
    private final AtomicReference<Subscription> mDisposableAtomicReference = new AtomicReference<>();

    private final FlowableCallback<T> mCallback;

    public DisposableSubscriber(FlowableCallback<T> callback) {
        mCallback = callback;
    }

    @Override
    public void onSubscribe(Subscription s) {
        s.request(Integer.MAX_VALUE);
        if (EndConsumerHelper.setOnce(mDisposableAtomicReference, s, this.getClass())) {
            if (mCallback != null) {
                mCallback.onStart();
            }
        }

    }

    @Override
    public void onNext(T t) {
        if (mCallback != null) {
            mCallback.onProgress(t);
        }
    }

    @Override
    public void onError(Throwable t) {
        error(t);
    }

    @Override
    public void onComplete() {
        success();
    }

    @Override
    public void dispose() {
        SubscriptionHelper.cancel(mDisposableAtomicReference);
    }

    @Override
    public boolean isDisposed() {
        return mDisposableAtomicReference.get() == SubscriptionHelper.CANCELLED;
    }

    private void success() {
        if (mCallback != null) {
            mCallback.onSuccess(null);
        }
        dispose();
    }

    private void error(Throwable e) {
        if (mCallback != null) {
            if (e instanceof ApiException) {
                mCallback.onError((ApiException) e);
            } else {
                mCallback.onError(new ApiException(e.getMessage()));
            }
        }
        dispose();
    }

}
