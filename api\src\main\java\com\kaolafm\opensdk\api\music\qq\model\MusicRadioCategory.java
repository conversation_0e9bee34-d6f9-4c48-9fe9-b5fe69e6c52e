package com.kaolafm.opensdk.api.music.qq.model;

import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * 音乐电台分类
 * <AUTHOR>
 * @date 2018/4/29
 */

public class MusicRadioCategory {

    /**
     * group_name : 人群
     * group_type_id : 59
     * radio_list : [{"listen_num":285120,"radio_id":123,"radio_name":"80后","radio_pic":"http://y.gtimg.cn/music/photo/radio/track_radio_123_10_3.jpg"},{"listen_num":236400,"radio_id":124,"radio_name":"90后","radio_pic":"http://y.gtimg.cn/music/photo/radio/track_radio_124_10_3.jpg"}]
     */

    @SerializedName("group_name")
    private String groupName;

    @SerializedName("group_type_id")
    private int groupTypeId;

    @SerializedName("radio_list")
    private List<MusicRadio> radioList;

    public String getGroupName() {
        return groupName;
    }

    public int getGroupTypeId() {
        return groupTypeId;
    }

    public List<MusicRadio> getRadioList() {
        return radioList;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public void setGroupTypeId(int groupTypeId) {
        this.groupTypeId = groupTypeId;
    }

    public void setRadioList(List<MusicRadio> radioList) {
        this.radioList = radioList;
    }
}
