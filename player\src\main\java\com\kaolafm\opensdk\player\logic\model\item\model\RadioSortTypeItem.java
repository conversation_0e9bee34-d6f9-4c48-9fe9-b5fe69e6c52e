package com.kaolafm.opensdk.player.logic.model.item.model;

import com.google.gson.annotations.SerializedName;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Unique;

/**
 * AI电台、专辑在播单页的显示排序
 */
@Entity
public class RadioSortTypeItem {
    @Id(autoincrement = true)
    private Long id;
    /**
     * 电台类型
     */
    @SerializedName("radio_type")
    private int radioType;
    /**
     * 电台id或专辑id
     */
    @Unique
    @SerializedName("radio_id")
    private long radioId;
    /**
     * 排序方式：1--->升序；-1--->降序
     */
    @SerializedName("sort_type")
    private int sortType;

    @Generated(hash = 176290410)
    public RadioSortTypeItem(Long id, int radioType, long radioId, int sortType) {
        this.id = id;
        this.radioType = radioType;
        this.radioId = radioId;
        this.sortType = sortType;
    }

    @Generated(hash = 1237788387)
    public RadioSortTypeItem() {
    }

    public int getRadioType() {
        return radioType;
    }

    public void setRadioType(int radioType) {
        this.radioType = radioType;
    }

    public long getRadioId() {
        return radioId;
    }

    public void setRadioId(long radioId) {
        this.radioId = radioId;
    }

    public int getSortType() {
        return sortType;
    }

    public void setSortType(int sortType) {
        this.sortType = sortType;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
