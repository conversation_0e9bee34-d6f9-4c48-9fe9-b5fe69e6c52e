package com.kaolafm.opensdk.api.music.qq;

/**
 * QQ音乐相关常量
 * <AUTHOR>
 * @date 2018/8/13
 */

public final class QQMusicConstant {
    /**
     * 账号登录类型
     */
    public static final String LOGIN_TYPE = "login_type";

    /**
     * QQ音乐相关票据登录验证
     */
    public static final int LOGIN_TYPE_TICKET = 1;
    /**
     * qq登录账号类型
     */
    public static final int LOGIN_TYPE_QQ = 2;
    /**
     * 微信登录账号类型
     */
    public static final int LOGIN_TYPE_WECHAT = 3;
    /**
     * 硬件登录
     */
    public static final int LOGIN_TYPE_DEVICE = 4;

    /**
     * 用户（QQ或微信）与硬件联合登录, 此种登录类型包函了login_type =0，1，2，3，4几种情况（单账号登录)
     */
    public static final int LOGIN_TYPE_QQMUSIC = 5;
}
