package com.kaolafm.opensdk.demo.live.chat;

import android.content.Context;
import android.net.Uri;
import android.util.Base64;
import android.util.Log;

//import com.chinanetcenter.wcs.android.api.FileUploader;
//import com.chinanetcenter.wcs.android.api.ParamsConf;
//import com.chinanetcenter.wcs.android.listener.FileUploaderListener;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.kaolafm.opensdk.demo.live.play.LiveApiConstant;
import com.kaolafm.opensdk.demo.live.play.LiveManager;
import com.kaolafm.opensdk.demo.live.ui.LivePresenter;
import com.kaolafm.opensdk.demo.live.ui.UserInfoManager;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

public class RecordUploadHelper {

    private static final String TAG = "LivePresenter";



    public static String generateFileUploadedPath(String fileName) {
        return LiveApiConstant.FILE_DIR_NAME_WANGSU + "/" + fileName;
    }

    public static String generateFileUploadName() {
        return UserInfoManager.getInstance().getUserId() + "-"
                + System.currentTimeMillis() + LiveManager.EXTENSION;
    }

    //上传到网速的云节点
//    public static void uploadToWangsu(Context context, String path, UploadParam program,
//                                      FileUploaderListener listener, String fileName) {
//        if (LivePresenter.DEBUG_LIVE) {
//            Log.d(TAG, "uploadToWangsu path : " + path);
//        }
//        FileUploader.setUploadUrl(LiveApiConstant.RECORD_UPLOAD_HOST_WANGSU);
//        ParamsConf conf = new ParamsConf();
//        conf.fileName = path;
//        String uploadName = RecordUploadHelper.generateFileUploadedPath(fileName);
//        conf.keyName = uploadName;
//        conf.mimeType = "audio/aac";
//        FileUploader.setParams(conf);
//        HashMap<String, String> callbackBody = new HashMap<String, String>();
//        callbackBody.put("upload_name", LiveApiConstant.RETURN_BODY_WANGSU);
//        String token = getUploadToken(uploadName, program);
//        if (token != null) {
//            FileUploader.upload(context, token, Uri.parse(path), callbackBody, listener);
//        } else {
//            if (LivePresenter.DEBUG_LIVE) {
//                Log.w(TAG, "uploadToWangsu token is null");
//            }
//        }
//    }


    /**
     * 注意：这个类的成员变量名称需要和网宿云存储定义的字段一致，最终生成json使用。
     */
    static class UploadPolicy {
        String scope = LiveApiConstant.BUCKET_WANGSU;
        String deadline = LiveApiConstant.DEADLINE_WANGSU;
        int overwrite = 0;
        int fsizeLimit = 0;
        int instant = 0;
        int separate = 0;
        String callbackUrl;
    }

    public static class UploadParam {
        long liveProgramId;
        String uid;
        String nickName;
        int timeLength;
        String voiceFileUrl;

        public UploadParam(long liveProgramId, String uid, String nickName, int timeLength,
                           String voiceFileUrl) {
            this.liveProgramId = liveProgramId;
            this.uid = uid;
            this.nickName = nickName;
            this.timeLength = timeLength;
            this.voiceFileUrl = voiceFileUrl;
        }

        @Override
        public String toString() {
            String ret = "?liveProgramId=" + liveProgramId + "&uid=" + uid + "&nickName="
                    + nickName + "&timeLength=" + timeLength + "&voiceFileUrl=" + voiceFileUrl;
            return ret;
        }
    }

    static class UploadPolicyTypeAdapter extends TypeAdapter<UploadPolicy> {
        @Override
        public void write(JsonWriter out, UploadPolicy value) throws IOException {
            out.beginObject();
            out.name("scope").value(value.scope);
            out.name("deadline").value(value.deadline);
            out.name("overwrite").value(value.overwrite);
            out.name("fsizeLimit").value(value.fsizeLimit);
            out.name("instant").value(value.instant);
            out.name("separate").value(value.separate);
            out.name("callbackUrl").value(value.callbackUrl);
            out.endObject();
        }

        @Override
        public UploadPolicy read(JsonReader in) throws IOException {
            return null;
        }
    }

    public static String getUploadToken(String fileName, UploadParam param) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "getUploadToken fileName : " + fileName);
        }
        Gson gson = new GsonBuilder().disableHtmlEscaping()
                .registerTypeAdapter(UploadPolicy.class, new UploadPolicyTypeAdapter()).create();

        //初始化上传参数
        UploadPolicy policy = new UploadPolicy();
        policy.callbackUrl = LiveApiConstant.WANGSU_UPLOAD_CALLBACK_URL + param;
        String putPolicy = gson.toJson(policy);
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "getUploadToken putPolicy: " + putPolicy);
        }

        //url安全的base64编码上传策略
        byte[] encodePutPolicyByte = Base64.encode(putPolicy.getBytes(), Base64.NO_WRAP);
        String encodePutPolicy = null;
        try {
            encodePutPolicy = new String(encodePutPolicyByte, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            Log.e(TAG, "getUploadToken encodePutPolicy", e);
            return null;
        }
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "getUploadToken encodePutPolicy: " + encodePutPolicy);
        }
        //hmac-sha1签名数据
        String signedPutPolicy = hamcsha1(encodePutPolicy.getBytes(),
                LiveApiConstant.SK_WANGSU.getBytes());
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "getUploadToken signedPutPolicy: " + signedPutPolicy);
        }

        //url安全的base64编码签名数据
        byte[] encodeSignByte = Base64.encode(signedPutPolicy.getBytes(), Base64.NO_WRAP);
        String encodeSign = null;
        try {
            encodeSign = new String(encodeSignByte, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            Log.e(TAG, "getUploadToken encodeSign", e);
        }
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "getUploadToken encodeSign: " + encodeSign);
        }

        //生成上传凭证
        String uploadToken = LiveApiConstant.AK_WANGSU + ":" + encodeSign + ":" + encodePutPolicy;
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "getUploadToken uploadToken: " + uploadToken);
        }

        return uploadToken;
    }


    public static String byte2hex(byte[] b) {
        StringBuilder hs = new StringBuilder();
        String stmp;
        for (int n = 0; b != null && n < b.length; n++) {
            stmp = Integer.toHexString(b[n] & 0XFF);
            if (stmp.length() == 1) {
                hs.append('0');
            }
            hs.append(stmp);
        }
        return hs.toString();
    }

    public static String hamcsha1(byte[] data, byte[] key) {
        try {
            SecretKeySpec signingKey = new SecretKeySpec(key, "HmacSHA1");
            Mac mac = Mac.getInstance("HmacSHA1");
            mac.init(signingKey);
            return byte2hex(mac.doFinal(data));
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        }
        return null;
    }
}
