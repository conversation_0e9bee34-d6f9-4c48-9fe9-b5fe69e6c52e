package com.kaolafm.opensdk.api.bind;

import android.util.Log;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.http.core.HttpCallback;

/**
 * <AUTHOR>
 **/
public class BindUserRequest extends BaseRequest {
    BindUserService mService;

    public BindUserRequest() {
        mService = obtainRetrofitService(BindUserService.class);
    }


    public void bindDevice(HttpCallback<Boolean> callback) {
//        MediaType JSON = MediaType.parse("application/json; charset=utf-8");

//        RequestBody body = RequestBody.create(JSON, content);
        doHttpDeal(mService.bindDevice(), baseResult -> {
            Log.i("kradio", "apply: baseResult=" + baseResult.getResult());
            String status = baseResult.getResult().getStatus();
            return "1".equals(status);
        }, callback);
    }
}
