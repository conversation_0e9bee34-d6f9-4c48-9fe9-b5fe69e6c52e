package com.kaolafm.opensdk.demo.player;

import android.os.Bundle;
import android.view.View;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.opensdk.api.subscribe.SubscribeRequest;
import com.kaolafm.opensdk.demo.detail.DetailActivity;
import com.kaolafm.opensdk.demo.detail.StringAdapter.Item;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.playlist.RadioPlayListControl;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-03-20
 */
public class SubscribePlayerActivity extends BasePlayerActivity {

    private boolean isLoadMore;
    private RadioPlayListControl radioPlayListControl;

    @Override
    public void initView(Bundle savedInstanceState) {
        super.initView(savedInstanceState);
        setTitle("一键播放订阅播放器页面");
        //添加播放状态监听
        PlayerManager.getInstance().addPlayControlStateCallback(mPlayerStateListener);
        PlayerManager.getInstance().addPlayListControlStateCallback(mPlayListStateListener);
//        //注册加载进度监听
//        mPlayerManager.regDownloadProgressListener(mOnDownloadProgressListener);
        btnSubscribe.setVisibility(View.VISIBLE);

    }

    @Override
    public void initData() {
        isLoadMore = false;
        PlayerManager.getInstance().start(new PlayerBuilder().setId(String.valueOf(-1)).setType(PlayerConstants.RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE));
    }

    private void showPlaylist(List<PlayItem> playItemList) {
        if (!ListUtil.isEmpty(playItemList)) {
            List<Item> datas = new ArrayList<>();
            for (PlayItem item: playItemList) {
                Item sai = new Item();
                sai.id = item.getAudioId();
                sai.type = DetailActivity.TYPE_AUDIO;
                sai.title = item.getTitle();
                sai.details = item.getRadioName();
                datas.add(sai);
            }
            mAdapter.setDataList(datas);
            if (isLoadMore) {
                if (mTrfDetailPlaylist != null) {
                    mTrfDetailPlaylist.finishLoadmore();
                }
            }
        }
        select();
    }

    @Override
    protected void playPre() {
        PlayerManager.getInstance().playPre();
    }

    @Override
    protected void switchPlayPause() {
        PlayerManager.getInstance().switchPlayerStatus();
    }

    @Override
    protected void playNext() {
        PlayerManager.getInstance().playNext();
    }

    @Override
    protected void refresh() {

    }

    @Override
    protected void loadMore() {
        isLoadMore = true;
        getSubscribePlaylist(true);
//        PlayerRadioListManager.getInstance().fetchMorePlaylist(new OnPlayItemInfoListener() {
//            @Override
//            public void onPlayItemReady(PlayItem playItem) {
//                Log.e("SubscribePlayerActivity", "onPlayItemReady: "+playItem);
//
//            }
//
//            @Override
//            public void onPlayItemUnavailable() {
//                Log.e("SubscribePlayerActivity", "onPlayItemUnavailable: ");
//                if (mTrfDetailPlaylist != null) {
//                    mTrfDetailPlaylist.finishLoadmore();
//                }
//            }
//
//            @Override
//            public void onPlayItemReady(List<PlayItem> list) {
//                Log.e("SubscribePlayerActivity", "onPlayItemReady: ");
//            }
//        });
    }

    @Override
    protected void playItem(Item item) {
        PlayerManager.getInstance().getPlayItemFromAudioId(item.id, new PlayerManager.GetPlayItemListener() {
            @Override
            public void success(PlayItem playitem) {
                if (playitem != null) {
                    PlayerManager.getInstance().start(new PlayerBuilder().setId(String.valueOf(playitem.getAudioId())).setType(PlayerConstants.RESOURCES_TYPE_AUDIO));
                }
            }

            @Override
            public void error(ApiException exception) {

            }
        });

    }

    @Override
    protected void seek(int progress) {
        PlayerManager.getInstance().seek(progress);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        PlayerManager.getInstance().removePlayControlStateCallback(mPlayerStateListener);
        PlayerManager.getInstance().removePlayListControlStateCallback(mPlayListStateListener);
    }

    void getSubscribePlaylist(boolean isLoadMore){
        new SubscribeRequest();
    }


    private IPlayListStateListener mPlayListStateListener = new IPlayListStateListener() {
        @Override
        public void onPlayListChange(List<PlayItem> list) {
            showPlaylist(list);
        }

        @Override
        public void onPlayListChangeError(PlayItem playItem, int i, int i1) {

        }
    };
}
