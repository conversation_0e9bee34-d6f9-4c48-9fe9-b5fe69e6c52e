package com.kaolafm.report.database;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Id;

/**
 * <AUTHOR> on 2019/1/9.
 */

@Entity
public class ReportData {
    @Id(autoincrement = true)
    private Long id;

    int type;
    String sendStr;



    @Generated(hash = 1803818035)
    public ReportData(Long id, int type, String sendStr) {
        this.id = id;
        this.type = type;
        this.sendStr = sendStr;
    }
    @Generated(hash = 789822999)
    public ReportData() {
    }



    public Long getId() {
        return this.id;
    }
    public void setId(Long id) {
        this.id = id;
    }
    public int getType() {
        return this.type;
    }
    public void setType(int type) {
        this.type = type;
    }
    public String getSendStr() {
        return this.sendStr;
    }
    public void setSendStr(String sendStr) {
        this.sendStr = sendStr;
    }
}
