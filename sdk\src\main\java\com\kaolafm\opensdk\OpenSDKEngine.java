package com.kaolafm.opensdk;

import android.app.Application;
import android.util.Log;

import com.kaolafm.ad.AdvertOptions;
import com.kaolafm.ad.AdvertisingInternalEngine;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.init.InitRequest;
import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.di.component.DaggerAppComponent;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.emergencybroadcast.EmergencyBroadcastManager;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;

import javax.inject.Inject;

import dagger.Lazy;

/**
 * 各个功能模块一起打包时的SDK的入口。
 *
 * <AUTHOR>
 * @date 2020-03-05
 */
public class OpenSDKEngine extends KradioSDKInternalEngine<AdvertOptions> {
    private static final String TAG = "OpenSDKEngine";
    
    @Inject
    @AppScope
    Lazy<AdvertisingInternalEngine> mAdvertisingInternalEngineLazy;

    @Inject
    @AppScope
    Lazy<InitRequest> mInitRequestLazy;

    public OpenSDKEngine() {
    }

    @Override
    protected void internalInit(Application application, AdvertOptions options, HttpCallback<Boolean> callback) {
        Log.i(TAG, "internalInit: 开始内部初始化");
        try {
            ComponentKit.getInstance().inject(DaggerAppComponent.builder(), application, options, this);
            Log.i(TAG, "internalInit: 依赖注入完成");
            
            super.internalInit(application, options, callback);
            Log.i(TAG, "internalInit: 父类初始化完成");
            
            mAdvertisingInternalEngineLazy.get().internalInit(application, options, callback);
            Log.i(TAG, "internalInit: 广告引擎初始化完成");
            
            PlayerManager.getInstance().init(application);
            Log.i(TAG, "internalInit: 播放器管理器初始化完成");
        } catch (Exception e) {
            Log.e(TAG, "internalInit: 内部初始化失败", e);
            throw e;
        }
    }

    @Override
    protected void internalActivate(HttpCallback<Boolean> callback) {
        Log.i(TAG, "internalActivate: 开始内部激活");
        //todo 嵌套需要优化。
        super.internalActivate(new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                Log.i(TAG, "internalActivate: 父类激活成功");
                if (!mAdvertisingInternalEngineLazy.get().isActivated()) {
                    Log.i(TAG, "internalActivate: 开始激活广告引擎");
                    mAdvertisingInternalEngineLazy.get().internalActivate(new HttpCallback<Boolean>() {
                        @Override
                        public void onSuccess(Boolean result) {
                            Log.i(TAG, "internalActivate: 广告引擎激活成功");
                        }
                        
                        @Override
                        public void onError(ApiException exception) {
                            Log.e(TAG, "internalActivate: 广告引擎激活失败", exception);
                        }
                    });
                } else {
                    Log.i(TAG, "internalActivate: 广告引擎已激活，无需再次激活");
                }
                if (callback != null) {
                    callback.onSuccess(aBoolean);
                }
            }

            @Override
            public void onError(ApiException exception) {
                Log.e(TAG, "internalActivate: 父类激活失败", exception);
                if (callback != null) {
                    callback.onError(exception);
                }
            }
        });
    }

    @Override
    public void activate(HttpCallback<Boolean> callback) {
        Log.i(TAG, "activate: 开始激活");
        super.activate(new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                Log.i(TAG, "activate: 激活成功，开始配置品牌信息");
                //需要设置品牌
                configBrand();
                if (callback != null) {
                    callback.onSuccess(aBoolean);
                }
            }

            @Override
            public void onError(ApiException exception) {
                Log.e(TAG, "activate: 激活失败", exception);
                if (callback != null) {
                    callback.onError(exception);
                }
            }
        });
    }

    private void configBrand() {
        Log.i(TAG, "configBrand: 开始获取品牌信息");
        mInitRequestLazy.get().getBrand(new HttpCallback<String>() {
            @Override
            public void onSuccess(String s) {
                Log.i(TAG, "configBrand: 获取品牌信息成功，品牌=" + s);
                mAdvertisingInternalEngineLazy.get().setBrand(s);
            }

            @Override
            public void onError(ApiException exception) {
                Log.e(TAG, "configBrand: 获取品牌信息失败", exception);
            }
        });
    }

    @Override
    public void config(Application application, AdvertOptions options, HttpCallback<Boolean> callback) {
        Log.i(TAG, "config: 开始配置SDK");
        init(application, options, null);
        activate(callback);
    }

    @Override
    public void release() {
        Log.i(TAG, "release: 开始释放资源");
        super.release();
        mAdvertisingInternalEngineLazy.get().release();
    }

    @Override
    public void setLatitude(String latitude) {
        Log.i(TAG, "setLatitude: 设置纬度=" + latitude);
        super.setLatitude(latitude);
        mAdvertisingInternalEngineLazy.get().setLatitude(latitude);
    }

    @Override
    public void setLongitude(String longitude) {
        Log.i(TAG, "setLongitude: 设置经度=" + longitude);
        super.setLongitude(longitude);
        if(mAdvertisingInternalEngineLazy != null && mAdvertisingInternalEngineLazy.get() != null) {
            mAdvertisingInternalEngineLazy.get().setLongitude(longitude);
        }
    }

    @Override
    public void setLocation(String lng, String lat) {
        Log.i(TAG, "setLocation: 设置位置信息，经度=" + lng + "，纬度=" + lat);
        super.setLocation(lng, lat);
        if(mAdvertisingInternalEngineLazy != null && mAdvertisingInternalEngineLazy.get() != null) {
            mAdvertisingInternalEngineLazy.get().setLocation(lng, lat);
        }

        //定位刷新时上报定位信息
        Log.i(TAG, "setLocation: 上报位置信息到紧急广播管理器");
        EmergencyBroadcastManager.getInstance().socketUploadLocationIfNeed();
    }

    @Override
    public void setLocation(String lng, String lat, String coordType) {
        Log.i(TAG, "setLocation: 设置位置信息，经度=" + lng + "，纬度=" + lat + "，坐标类型=" + coordType);
        if (StringUtil.isEmpty(coordType)) {
            setLocation(lng, lat);
        } else {
            super.setLocation(lng, lat, coordType);
            if(mAdvertisingInternalEngineLazy != null && mAdvertisingInternalEngineLazy.get() != null) {
                mAdvertisingInternalEngineLazy.get().setLocation(lng, lat, coordType);
            }
            //定位刷新时上报定位信息
            Log.i(TAG, "setLocation: 上报位置信息到紧急广播管理器");
            EmergencyBroadcastManager.getInstance().socketUploadLocationIfNeed();
        }
    }
}
