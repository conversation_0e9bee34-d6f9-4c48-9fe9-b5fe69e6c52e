package com.kaolafm.opensdk.http.socket;

import android.text.TextUtils;
import android.util.SparseArray;

import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.opensdk.http.socket.parser.Parser;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.net.URI;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Queue;

import javax.inject.Inject;

/**
 * 长连接
 *
 * <AUTHOR>
 * @date 2020-01-02
 */
public class Socket extends Emitter {

    /**
     * 长连接是否已经连接
     */
    private volatile boolean connected = false;

    @Inject
    SocketConnection connection;

    private EventHandler eventHandler;

    private String namespace;
    private String query;
    private boolean encoding = false;

    @Inject
    Parser.Encoder encoder;

    private List<String> events = new ArrayList<String>() {{
        add(SocketEvent.EVENT_CONNECT);
        add(SocketEvent.EVENT_CONNECT_ERROR);
        add(SocketEvent.EVENT_CONNECT_TIMEOUT);
        add(SocketEvent.EVENT_CONNECTING);
        add(SocketEvent.EVENT_DISCONNECT);
        add(SocketEvent.EVENT_ERROR);
        add(SocketEvent.EVENT_RECONNECT);
        add(SocketEvent.EVENT_RECONNECT_ATTEMPT);
        add(SocketEvent.EVENT_RECONNECT_FAILED);
        add(SocketEvent.EVENT_RECONNECT_ERROR);
        add(SocketEvent.EVENT_RECONNECTING);
        add(SocketEvent.EVENT_PING);
        add(SocketEvent.EVENT_PONG);
    }};
    private int ids;
    private SparseArray<Ack> acks = new SparseArray<>();
    private final Queue<Packet<JSONArray>> sendBuffer = new LinkedList<>();
    private final Queue<List<Object>> receiveBuffer = new LinkedList<>();
    private List<Packet> packetBuffer = new ArrayList<>();

    @Inject
    public Socket() {
    }

    public void config(URI uri, Options opts) {
        connection.init(this, uri, opts);
        query = opts.query;
    }

    public Socket(URI uri, Options opts) {
        connection.init(this, uri, opts);
        query = opts.query;
        encoder = new Parser.Encoder();
    }

    public void connect() {
        Logging.d("connect socket");
        EventThread.exec(() -> {
            if (connected) {
                return;
            }
            subEvents();
            connection.start();
            if (connection.readyState == ReadyState.OPEN) {
                onOpen();
            }
            emit(SocketEvent.EVENT_CONNECTING);
        });
    }

    /**
     * 设置事件监听
     */
    private void subEvents() {
        if (eventHandler == null) {
            eventHandler = new EventHandler(connection);
            eventHandler.add(SocketEvent.EVENT_OPEN, args -> onOpen());
            eventHandler.add(SocketEvent.EVENT_PACKET, args -> onPacket((Packet<?>) args[0]));
            eventHandler.add(SocketEvent.EVENT_CLOSE, args -> onClose(args.length > 0 ? (String) args[0] : null));
        }
    }

    private void onOpen() {
        Logging.d("transport is open - connecting, namespace="+namespace);
        if (!"/".equals(namespace)) {
            Packet packet = new Packet(Parser.CONNECT);
            packet.query = query;
            packet.nsp = namespace;
            packet(packet);
        }
    }

    @Override
    public Emitter emit(String event, Object... args) {
        Logging.d("emit event %s with param %s", event, Arrays.toString(args));
        EventThread.exec(() -> {
            if (events.contains(event)) {
                super.emit(event, args);
                return;
            }
            //对于自定义的事件进行特殊处理
            Ack ack;
            Object[] _args;
            int lastIndex = args.length - 1;

            if (args.length > 0 && args[lastIndex] instanceof Ack) {
                _args = new Object[lastIndex];
                System.arraycopy(args, 0, _args, 0, lastIndex);
                ack = (Ack) args[lastIndex];
            } else {
                _args = args;
                ack = null;
            }

            emit(event, _args, ack);
        });
        return this;
    }

    /**
     * Emits an event with an acknowledge.
     *
     * @param event an event name
     * @param args  data to send.
     * @param ack   the acknowledgement to be called
     * @return a reference to this object.
     */
    public Emitter emit(final String event, final Object[] args, final Ack ack) {
        EventThread.exec(() -> {
            JSONArray jsonArgs = new JSONArray();
            jsonArgs.put(event);

            if (args != null) {
                for (Object arg : args) {
                    jsonArgs.put(arg);
                }
            }

            Packet<JSONArray> packet = new Packet<>(Parser.EVENT, jsonArgs);

            if (ack != null) {
                Logging.d("emitting packet with ack id %d", ids);
                acks.put(ids, ack);
                packet.id = ids++;
            }

            if (connected) {
                packet(packet);
            } else {
                sendBuffer.add(packet);
            }
        });
        return this;
    }

    public void send(final Object... args) {
        emit(SocketEvent.EVENT_MESSAGE, args);
    }

    private void packet(Packet packet) {
        packet.nsp = namespace;
        Logging.d("writing packet %s", packet);
        if (!TextUtils.isEmpty(packet.query) && packet.type == Parser.CONNECT) {
            packet.nsp += "?" + packet.query;
        }
        if (!encoding) {
            encoding = true;
            encoder.encode(packet, (Parser.CodecCallback<Object[]>) datas -> {
                for (Object data : datas) {
                    connection.write(data);
                }
                encoding = false;
                processPacketQueue();
            });
        } else {
            packetBuffer.add(packet);
        }
    }

    /**
     * 处理缓存的packet数据
     */
    private void processPacketQueue() {
        if (!packetBuffer.isEmpty() && !encoding) {
            packet(packetBuffer.remove(0));
        }
    }

    @SuppressWarnings("unchecked")
    private void onPacket(Packet<?> packet) {
        if (!this.namespace.equals(packet.nsp)) {
            return;
        }

        switch (packet.type) {
            case Parser.CONNECT:
                this.onConnect();
                break;

            case Parser.EVENT:
            case Parser.BINARY_EVENT: {
                this.onEvent((Packet<JSONArray>) packet);
                break;
            }

            case Parser.ACK:
            case Parser.BINARY_ACK: {
                Packet<JSONArray> p = (Packet<JSONArray>) packet;
                this.onAck(p);
                break;
            }

            case Parser.DISCONNECT:
                this.onDisconnect();
                break;

            case Parser.ERROR:
                this.emit(SocketEvent.EVENT_ERROR, packet.data);
                break;
            default:
        }
    }

    private void onConnect() {
        connected = true;
        emit(SocketEvent.EVENT_CONNECT);
        emitBuffered();
    }

    /**
     * 发送在连接成功之前的缓存数据
     */
    private void emitBuffered() {
        List<Object> data;
        while ((data = this.receiveBuffer.poll()) != null) {
            String event = (String) data.get(0);
            super.emit(event, data.toArray());
        }
        this.receiveBuffer.clear();

        Packet<JSONArray> packet;
        while ((packet = this.sendBuffer.poll()) != null) {
            this.packet(packet);
        }
        this.sendBuffer.clear();
    }

    private void onEvent(Packet<JSONArray> packet) {
        List<Object> args = new ArrayList<>(Arrays.asList(toArray(packet.data)));
        Logging.d("emitting evets %s, id=%s", args, packet.id);
        if (packet.id >= 0) {
            Logging.d("attaching ack callback to event");
            //在参数中追加Ack回调，在发送数据的时候保存起来，收到确认信息的时候在onAck()中回调。
            args.add(ack(packet.id));
        }
        if (connected) {
            if (!args.isEmpty()) {
                super.emit(args.remove(0).toString(), args.toArray());
            }
        } else {
            receiveBuffer.add(args);
        }
    }

    private Ack ack(int id) {
        final boolean[] send = {false};
        return args -> EventThread.exec(() -> {
            if (send[0]) {
                return;
            }
            send[0] = true;
            Logging.d("sending ack %s", args.length != 0 ? args : null);
            JSONArray jsonArray = new JSONArray();
            for (Object arg : args) {
                jsonArray.put(arg);
            }
            Packet<JSONArray> packet = new Packet<>(Parser.ACK, jsonArray);
            packet.id = id;
            packet(packet);
        });
    }

    private void onAck(Packet<JSONArray> packet) {
        Ack ack = acks.get(packet.id);
        acks.remove(packet.id);
        if (ack != null) {
            ack.call(toArray(packet.data));
        }
    }

    private void onDisconnect() {
        Logging.d("server disconnect ($s)", namespace);
        release();
        onClose("server disconnect");
    }

    private void onClose(String reason) {
        Logging.d("close because %s", reason, null);
        connected = false;
        emit(SocketEvent.EVENT_DISCONNECT, reason);
    }
    public void disconnect() {
        EventThread.exec(() -> {
            if (connected) {
                packet(new Packet(Parser.DISCONNECT));
            }
            release();
            if (connected) {
                onClose("socket disconnect");
            }
        });
    }

    public void release() {
        eventHandler.release();
        eventHandler = null;
        connection.release();
    }

    public void namespace(String nsp) {
        namespace = nsp;
    }

    public String namespace() {
        return namespace;
    }

    void enableEncoding() {
        this.packetBuffer.clear();
        this.encoding = false;
    }

    private static Object[] toArray(JSONArray array) {
        int length = array.length();
        Object[] data = new Object[length];
        for (int i = 0; i < length; i++) {
            Object v;
            try {
                v = array.get(i);
            } catch (JSONException e) {
                Logging.w("An error occured while retrieving data from JSONArray, %s", e);
                v = null;
            }
            data[i] = JSONObject.NULL.equals(v) ? null : v;
        }
        return data;
    }

    public boolean isConnected() {
        return connected;
    }

    public static class Options extends SocketConnection.Options {
    }
}
