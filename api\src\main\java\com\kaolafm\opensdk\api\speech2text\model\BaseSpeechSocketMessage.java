package com.kaolafm.opensdk.api.speech2text.model;

import java.io.Serializable;

public class BaseSpeechSocketMessage implements Serializable {
    /**
     * 任务id
     * 32位UUID字符串，实时语音任务期间不变,保持和开始时间的值相同
     */
    private String taskId;

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    @Override
    public String toString() {
        return "BaseSpeechSocketResult{" +
                "taskId='" + taskId + '\'' +
                '}';
    }
}