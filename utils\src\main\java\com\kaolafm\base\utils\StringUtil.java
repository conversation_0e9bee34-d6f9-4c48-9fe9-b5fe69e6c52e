package com.kaolafm.base.utils;

import android.text.InputFilter;
import android.text.Spanned;
import android.text.TextUtils;
import java.io.StringReader;
import java.io.StringWriter;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2018/4/19
 */

public final class StringUtil {

    private StringUtil() {
    }

    //emoji过滤器
    public static final InputFilter emojiFilter = new InputFilter() {

        Pattern emoji = Pattern.compile(
                "[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\u2600-\u27ff]",
                Pattern.UNICODE_CASE | Pattern.CASE_INSENSITIVE);

        @Override
        public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart,
                int dend) {

            Matcher emojiMatcher = emoji.matcher(source);
            if (emojiMatcher.find()) {
                return "";
            }

            return null;
        }
    };


    /**
     * 字符串转换成十六进制字符串
     *
     * @return String 每个Byte之间空格分隔，如: [61 6C 6B]
     */
    public static String str2HexStr(String str) {

        char[] chars = "0123456789ABCDEF".toCharArray();
        StringBuilder sb = new StringBuilder("");
        byte[] bs = str.getBytes();
        int bit;

        for (int i = 0; i < bs.length; i++) {
            bit = (bs[i] & 0x0f0) >> 4;
            sb.append(chars[bit]);
            bit = bs[i] & 0x0f;
            sb.append(chars[bit]);
        }
        return sb.toString().trim();
    }


    /**
     * json 格式化
     */
    public static String jsonFormat(String json) {
        if (TextUtils.isEmpty(json)) {
            return "Empty/Null json content";
        }
        String message;
        try {
            json = json.trim();
            if (json.startsWith("{")) {
                JSONObject jsonObject = new JSONObject(json);
                message = jsonObject.toString(4);
            } else if (json.startsWith("[")) {
                JSONArray jsonArray = new JSONArray(json);
                message = jsonArray.toString(4);
            } else {
                message = json;
            }
        } catch (JSONException e) {
            message = json;
        }
        return message;
    }


    /**
     * xml 格式化
     */
    public static String xmlFormat(String xml) {
        if (TextUtils.isEmpty(xml)) {
            return "Empty/Null xml content";
        }
        String message;
        try {
            Source xmlInput = new StreamSource(new StringReader(xml));
            StreamResult xmlOutput = new StreamResult(new StringWriter());
            Transformer transformer = TransformerFactory.newInstance().newTransformer();
            transformer.setOutputProperty(OutputKeys.INDENT, "yes");
            transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");
            transformer.transform(xmlInput, xmlOutput);
            message = xmlOutput.getWriter().toString().replaceFirst(">", ">\n");
        } catch (TransformerException e) {
            message = xml;
        }
        return message;
    }

    public static <T> String array2String(String separator, T[] in) {
        if (in == null) {
            return null;
        }
        StringBuffer out = new StringBuffer();
        for (int i = 0, size = in.length; i < size; i++) {
            out.append(in[i]).append(separator);
        }
        out.deleteCharAt(out.length() - 1);
        return out.toString();
    }

    public static <T> String array2String(T[] in) {
        return array2String(",", in);
    }

    /**
     * 集合转字符串, 且每个元素用给定的参数隔开
     *
     * @param separator 字符串的分隔符
     */
    public static String collection2String(Collection<?> in, String separator) {
        if (in == null) {
            return null;
        }
        return iterator2String(in.iterator(), separator);
    }

    public static String iterator2String(Iterator<?> it, String separator) {
        if (it == null) {
            return null;
        }

        StringBuilder out = new StringBuilder();
        while (it.hasNext()) {
            if (out.length() > 0) {
                out.append(separator);
            }
            out.append(it.next().toString());
        }

        return out.toString();
    }

    /**
     * 去掉url中的路径，留下请求参数部分
     *
     * @param strURL url地址
     * @return url请求参数部分
     */
    public static String truncateUrlPage(String strURL) {
        String strAllParam = null;
        String[] arrSplit = null;

        strURL = strURL.trim();

        arrSplit = strURL.split("[?]");
        if (strURL.length() > 1) {
            if (arrSplit.length > 1) {
                if (arrSplit[1] != null) {
                    strAllParam = arrSplit[1];
                }
            }
        }

        return strAllParam;
    }

    /**
     * 解析出url参数中的键值对
     * 如 "index.jsp?Action=del&id=123"，解析出Action:del,id:123存入map中
     *
     * @param url url地址
     * @return url请求参数部分
     */
    public static Map<String, String> getRequestParams(String url) {
        Map<String, String> mapRequest = new HashMap<>();

        String[] arrSplit = null;

        String strUrlParam = truncateUrlPage(url);
        if (strUrlParam == null) {
            return mapRequest;
        }
        //每个键值为一组 www.2cto.com
        arrSplit = strUrlParam.split("[&]");
        for (String strSplit : arrSplit) {
            String[] arrSplitEqual = null;
            arrSplitEqual = strSplit.split("[=]");

            //解析出键值
            if (arrSplitEqual.length > 1) {
                //正确解析
                mapRequest.put(arrSplitEqual[0], arrSplitEqual[1]);

            } else {
                if (!TextUtils.isEmpty(arrSplitEqual[0])) {
                    //只有参数没有值，不加入
                    mapRequest.put(arrSplitEqual[0], "");
                }
            }
        }
        return mapRequest;
    }

    /**
     * 字符串拼接
     */
    public static String join(Object... args) {
        if (args == null) {
            return "";
        }
        final StringBuilder buf = new StringBuilder();

        for (int i = 0; i < args.length; i++) {
            if (args[i] != null) {
                buf.append(args[i]);
            }
        }
        return buf.toString();
    }

    /**
     * 格式化数字以万/亿记
     */
    public static String formatNum(long num) {
        StringBuffer sb = new StringBuffer();
        DecimalFormat df = new DecimalFormat("0.0");
        if (num > 10000 && num < 100000000) {
            return sb.append(df.format(num / 10000F)).append("万").toString();
        } else if (num > 100000000) {
            return sb.append(df.format(num / 100000000F)).append("亿").toString();
        }
        return String.valueOf(num);
    }

    /**
     * 生成考拉签名参数
     * @param params
     * @param appKey
     * @return
     */
    public static String createSign(Map<String, String> params, String appKey) {
        ArrayList<Entry<String, String>> entries = new ArrayList<>(params.entrySet());
        Collections.sort(entries, (o1, o2) -> o1.getKey().compareTo(o2.getKey()));
        String keyValues = array2StringWithIndex(entries.toArray()).insert(0, appKey).append(appKey).toString();
        return MD5.getMD5Str(keyValues);
    }

    /**
     * 将数组转换为带索引的字符串
     */
    public static StringBuilder array2StringWithIndex(Object[] objects) {
        StringBuilder sb = new StringBuilder();
        String preValue = null;
        int size = objects.length;
        for (int i = 0; i < size; i++) {
            Object object = objects[i];
            if (object != null) {
                String value = object.toString().trim();
                if (!TextUtils.isEmpty(value) && !value.equals(preValue)) {
                    sb.append(i).append(value);
                    preValue = value;
                }
            }
        }
//        sb.append(size).append("access=internal");
        return sb;
    }

    /**
     * object对象转换String。对数组做了处理。
     * @param object
     * @return
     */
    public static String toString(Object object) {
        if (object == null) {
            return "null";
        }
        if (!object.getClass().isArray()) {
            return object.toString();
        }
        if (object instanceof boolean[]) {
            return Arrays.toString((boolean[]) object);
        }
        if (object instanceof byte[]) {
            return Arrays.toString((byte[]) object);
        }
        if (object instanceof char[]) {
            return Arrays.toString((char[]) object);
        }
        if (object instanceof short[]) {
            return Arrays.toString((short[]) object);
        }
        if (object instanceof int[]) {
            return Arrays.toString((int[]) object);
        }
        if (object instanceof long[]) {
            return Arrays.toString((long[]) object);
        }
        if (object instanceof float[]) {
            return Arrays.toString((float[]) object);
        }
        if (object instanceof double[]) {
            return Arrays.toString((double[]) object);
        }
        if (object instanceof Object[]) {
            return Arrays.deepToString((Object[]) object);
        }
        return "Couldn't find a correct type for the object";
    }

    /**
     * Helper function for null and empty string testing.
     *
     * @return true iff s == null or s.equals("");
     */
    public static boolean isEmpty(String s) {
        return makeSafe(s).length() == 0;
    }

    /**
     * Helper function for making null strings safe for comparisons, etc.
     *
     * @return (s == null) ? "" : s;
     */
    public static String makeSafe(String s) {
        return (s == null) ? "" : s;
    }

    /**
     * 校验当前字符串是否是纯数字
     *
     * @param value
     * @return true为是，false为否
     */
    public static boolean isDigitsOnly(String value) {
        if (value != null && TextUtils.isDigitsOnly(value)) {
            return true;
        }
        return false;
    }

    /**
     * 按需求定制系统String.format函数
     *
     * @param format
     * @param args
     * @return
     */
    public static String format(String format, Object... args) {
        if (format == null) {
            return "";
        }

        final String nullStr = "null";

        for (int i = 0; i < args.length; i++) {
            Object tempStr = args[i];
            if (tempStr == null || nullStr.equals(tempStr.toString().toLowerCase())) {
                args[i] = "";
            }
        }
        return String.format(Locale.US, format, args);
    }
}
