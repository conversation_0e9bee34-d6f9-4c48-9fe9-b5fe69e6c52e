package com.kaolafm.opensdk.player.logic.util;

import android.content.Context;
import android.content.SharedPreferences;

import com.google.gson.Gson;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.media.ToneQualityRequest;
import com.kaolafm.opensdk.api.media.model.ToneQuality;
import com.kaolafm.opensdk.api.media.model.ToneQualityResponse;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.ArrayList;
import java.util.List;


/******************************************
 * 类描述： Kradio 在线电台版音质设置. 类名称：SoundQualityHelper
 * 通过后台配置完成
 *
 * @version: 1.0
 * @author: shaoningYang
 * @time: 2016-11-17 10:50
 ******************************************/
public class ToneQualityHelper {
    public static final String TONE_PREFERENCE_NAME = "tone_set_sp";
    public static final String SOUND_QUALITY_PREFERENCE_NAME = "sound_quality_set_sp";
    /**
     * 音质设置值
     */
    public static final String TONE_VALUE = "tonevalue";
    public static final String SOUND_VALUE = "soundquality";

    /**
     * 在线电台版音质，通过后台配置完成,与mToneQuality不同时使用
     */
    private ToneQuality mToneQuality;

    private SharedPreferences sharedPreferenceUtil;

    private ArrayList<IToneQualityListener> mIToneQualityListenerArrayList;

    private ToneQualityHelper() {
        mIToneQualityListenerArrayList = new ArrayList<>();
    }

    private static class SOUND_QUALITY_CLASS_INSTANCE {
        private final static ToneQualityHelper SOUND_QUALITY_HELPER = new ToneQualityHelper();
    }

    /**
     * 获取音频音质帮助实例
     *
     * @return
     */
    public static ToneQualityHelper getInstance() {
        return SOUND_QUALITY_CLASS_INSTANCE.SOUND_QUALITY_HELPER;
    }

    public void initToneSetValue(Context context) {
        sharedPreferenceUtil = context.getSharedPreferences(SOUND_QUALITY_PREFERENCE_NAME, Context.MODE_PRIVATE);
        //如果设置了旧版的音质，则应替换成新版音质对象
        boolean isSetOldToneQuality = checkAndSetOldToneValue(context);
        if (isSetOldToneQuality) return;

        initQuality();
    }

    private void initQuality() {
        String soundQuality = sharedPreferenceUtil.getString(SOUND_VALUE, null);
        if (!StringUtil.isEmpty(soundQuality)) {
            mToneQuality = new Gson().fromJson(soundQuality, ToneQuality.class);
        }
    }

    /**
     * 检查是否设置了旧版的音质
     *
     * @param context
     * @return true:设置了，false:没设置
     */
    private boolean checkAndSetOldToneValue(Context context) {
        SharedPreferences mOldPreferenceUtil = context.getSharedPreferences(TONE_PREFERENCE_NAME, Context.MODE_PRIVATE);
        if (mOldPreferenceUtil.contains(TONE_VALUE)) {
            new ToneQualityRequest().getSoundQualities(new HttpCallback<ToneQualityResponse>() {
                @Override
                public void onSuccess(ToneQualityResponse toneQualityResponse) {
                    int mQuality = mOldPreferenceUtil.getInt(TONE_VALUE, 0);
                    if (toneQualityResponse == null || ListUtil.isEmpty(toneQualityResponse.getBitrate()))
                        return;
                    ToneQuality quality = findQuality(mQuality, toneQualityResponse.getBitrate());
                    if (quality != null) {
                        setToneQuality(quality);
                        //只有获取到对应的音质对象才要删除
                        mOldPreferenceUtil.edit().remove(TONE_VALUE).apply();
                        return;
                    }
                    initQuality();
                }

                @Override
                public void onError(ApiException exception) {
                    initQuality();
                }
            });
            return true;
        }
        return false;
    }

    /**
     * 找到对应的音质对象
     *
     * @param mQuality
     * @param bitrate
     * @return
     */
    private ToneQuality findQuality(int mQuality, List<ToneQuality> bitrate) {
        ToneQuality mToneQuality = null;
        for (ToneQuality toneQuality : bitrate) {
            if (mQuality == toneQuality.getType()) {
                mToneQuality = toneQuality;
                break;
            }
        }
        return mToneQuality;
    }

    public void setToneQuality(ToneQuality toneQuality) {
        mToneQuality = toneQuality;
        if (sharedPreferenceUtil == null) {
            return;
        }

        sharedPreferenceUtil.edit().putString(SOUND_VALUE, new Gson().toJson(toneQuality)).apply();
        notifyQualityChange();
    }

    public ToneQuality getToneQuality() {
        return mToneQuality;
    }

    public void registerToneQualityListener(IToneQualityListener iToneQualityListener) {
        if (mIToneQualityListenerArrayList.contains(iToneQualityListener)) {
            return;
        }
        mIToneQualityListenerArrayList.add(iToneQualityListener);
    }

    public void removeToneQualityListener(IToneQualityListener iToneQualityListener) {
        if (mIToneQualityListenerArrayList.contains(iToneQualityListener)) {
            mIToneQualityListenerArrayList.remove(iToneQualityListener);
        }
    }

    private void notifyQualityChange() {
        for (int i = 0, size = mIToneQualityListenerArrayList.size(); i < size; i++) {
            IToneQualityListener iToneQualityListener = mIToneQualityListenerArrayList.get(i);
            if (iToneQualityListener == null) {
                continue;
            }
            iToneQualityListener.toneQualityChange(mToneQuality);
        }
    }

    public interface IToneQualityListener {
        void toneQualityChange(ToneQuality toneQuality);
    }
}
