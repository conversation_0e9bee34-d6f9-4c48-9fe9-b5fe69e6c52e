//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by <PERSON>rnflower decompiler)
//

package com.kaolafm.opensdk.api.subscribe;

import android.os.Parcel;
import android.os.Parcelable;
import androidx.annotation.IntDef;

import com.google.gson.annotations.SerializedName;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

public class SubscribeStatus implements Parcelable {

    /*订阅状态*/
    /** 订阅失败*/
    public static final int STATE_FAILURE = 0;
    /** 订阅成功*/
    public static final int STATE_SUCCESS = 1;

    @Retention(RetentionPolicy.SOURCE)
    @IntDef({STATE_FAILURE, STATE_SUCCESS})
    public @interface State {
    }

    /**订阅状态*/
    @SerializedName("status")
    @State
    private int status;

    public @State
    int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.status);
    }

    public SubscribeStatus() {
    }

    protected SubscribeStatus(Parcel in) {
        this.status = in.readInt();
    }

    public static final Creator<SubscribeStatus> CREATOR = new Creator<SubscribeStatus>() {
        @Override
        public SubscribeStatus createFromParcel(Parcel source) {
            return new SubscribeStatus(source);
        }

        @Override
        public SubscribeStatus[] newArray(int size) {
            return new SubscribeStatus[size];
        }
    };
}
