package com.kaolafm.report.event;


import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> on 2019/1/21.
 * 卡顿开始数据上报
 */

public class BufferStartReportEvent extends BaseReportEventBean {
    /**
     * 0.默认卡顿; 1.拖拽卡顿
     */
    public static final String TYPE_NORMAL = "0";
    public static final String TYPE_PUSH = "1";

    private String audioid;
    private String radioid;
    private String albumid;
    /**
     * 卡顿类型
     * 0.默认卡顿; 1.拖拽卡顿
     */
    private String type = "0";
    /**
     * dns
     */
    private String remarks1;
    /**
     * 资源url
     */
    private String remarks2;
    public BufferStartReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_BUFFER_START);
    }


    public String getAudioid() {
        return audioid;
    }

    public void setAudioid(String audioid) {
        this.audioid = audioid;
    }

    public String getRadioid() {
        return radioid;
    }

    public void setRadioid(String radioid) {
        this.radioid = radioid;
    }

    public String getAlbumid() {
        return albumid;
    }

    public void setAlbumid(String albumid) {
        this.albumid = albumid;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRemarks1() {
        return remarks1;
    }

    public void setRemarks1(String remarks1) {
        this.remarks1 = remarks1;
    }

    public String getRemarks2() {
        return remarks2;
    }

    public void setRemarks2(String remarks2) {
        this.remarks2 = remarks2;
    }

}
