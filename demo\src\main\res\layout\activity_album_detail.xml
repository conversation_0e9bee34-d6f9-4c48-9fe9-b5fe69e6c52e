<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="300dp">

        <TextView
            android:id="@+id/tv_demo_pgc_info"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:textColor="@color/colorBlack"
            android:textSize="10sp"
            tools:text="详情:" />
    </ScrollView>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!--Album-->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="专辑(Album)" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btnGetAlbumDetailsSingle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="获取专辑详情(单个)" />


                <Button
                    android:id="@+id/btnGetAlbumDetailsMultil"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="获取专辑详情(多个)" />
            </LinearLayout>

            <!--Radio-->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="电台(Radio)" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btnGetRadioDetailsSingle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="获取电台详情(单个)" />
            </LinearLayout>

            <!--单曲-->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="单曲" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btnGetAudioDetails"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="获取单曲详情(单个)" />


                <Button
                    android:id="@+id/btnGetAudioDetailsMutil"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="获取单曲详情(多个)" />
            </LinearLayout>

            <!--播单-->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="播单(Playlist)" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btnGetPlaylistAlbum"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="获取播单(专辑Album)" />


                <Button
                    android:id="@+id/btnGetPlaylistRadio"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="获取播单(电台Radio)" />
            </LinearLayout>

            <!--节目单-->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="在线广播(Broadcast)" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btnGetBroadcastList"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="获取在线广播列表" />


                <Button
                    android:id="@+id/btnGetBroadcastDetails"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="获取在线广播详情" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">


                <Button
                    android:id="@+id/btnGetBroadcastProgramList"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="获取在线广播节目列表" />

                <Button
                    android:id="@+id/btnGetBroadcastProgramDetails"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:text="获取在线广播节目详情(指定节目id)" />

            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btnGetBroadcastCurrentProgramDetails"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:text="获取在线广播当前节目详情(指定在线广播id)" />


            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/getBroadcastCategory"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:text="获取在线广播分类" />


            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/getBroadcastAreaList"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:text="获取在线广播地区列表" />

                <Button
                    android:id="@+id/getBroadcastArea"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:text="获取在线广播地区(经纬)" />

            </LinearLayout>


        </LinearLayout>
    </ScrollView>
</LinearLayout>
