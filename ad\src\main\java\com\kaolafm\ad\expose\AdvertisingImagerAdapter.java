package com.kaolafm.ad.expose;

import com.kaolafm.ad.api.model.Advert;
import com.kaolafm.ad.api.model.ImageAdvert;
import com.kaolafm.ad.api.model.InteractionAdvert;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.error.ApiException;

import javax.inject.Inject;

/**
 * 图片广告展示适配类
 *
 * <AUTHOR>
 * @date 2020-02-17
 */
@AppScope
public class AdvertisingImagerAdapter implements Adapter<AdvertisingImager> {

    private AdvertisingImager mImager;

    @Inject
    AdvertisingImagerAdapter() {
    }

    @Override
    public boolean accept(Advert advert) {
        return advert instanceof ImageAdvert || advert instanceof InteractionAdvert;
    }

    @Override
    public void expose(Advert advert) {
        if (mImager != null) {
            if (advert instanceof ImageAdvert) {
                mImager.display((ImageAdvert) advert);
            } else {//二次互动是业务手动调用接口实现。
                mImager.displayInteraction((InteractionAdvert) advert);
            }
        }
    }

    @Override
    public void close(Advert advert) {
        if (mImager != null) {
            //null表示关闭所有广告
            if (advert == null) {
                mImager.hide(null);
                mImager.hideInteraction(null);
            } else if (advert instanceof ImageAdvert) {
                mImager.hide((ImageAdvert) advert);
            } else if (advert instanceof InteractionAdvert) {
                mImager.hideInteraction((InteractionAdvert) advert);
            }
        }
    }

    @Override
    public AdvertisingImager getExecutor() {
        return mImager;
    }

    @Override
    public void setExecutor(AdvertisingImager advertisingImager) {
        mImager = advertisingImager;
    }

    @Override
    public void error(String adZoneId, int subtype, ApiException e) {
        if (mImager != null) {
            mImager.error(adZoneId, subtype, e);
        }
    }
}
