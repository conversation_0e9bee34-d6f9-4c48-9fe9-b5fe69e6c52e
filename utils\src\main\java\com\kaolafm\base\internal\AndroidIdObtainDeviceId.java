package com.kaolafm.base.internal;

import android.content.Context;
import android.provider.Settings;
import android.telephony.TelephonyManager;
import android.text.TextUtils;

import com.kaolafm.base.utils.MD5;

import java.util.UUID;

import static android.content.Context.TELEPHONY_SERVICE;

/**
 * 通过Android id生成uuid。这个是旧的生成方式，1.5.0以前版本的SDK使用的是这种。
 * <AUTHOR>
 * @date 2019-06-17
 */
public class AndroidIdObtainDeviceId extends BaseObtainDeviceId {

    public AndroidIdObtainDeviceId() {
    }

    @Override
    protected String createUUID(Context context) {
        final String androidId = Settings.Secure.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID);
        // Use the Android ID unless it's broken, in which case fallback on deviceId,
        // unless it's not available, then fallback on a random number which we store
        // to a prefs file
        String uuid = null;
        try {
            //多个设备产生的id不相同就使用该id。
            if (!"9774d56d682e549c".equals(androidId)) {
                uuid = UUID.nameUUIDFromBytes(androidId.getBytes("UTF-8")).toString();
            } else {
                final String deviceId = ((TelephonyManager) context.getSystemService(TELEPHONY_SERVICE)).getDeviceId();
                if (!TextUtils.isEmpty(deviceId)) {
                    uuid = UUID.nameUUIDFromBytes(deviceId.getBytes("UTF-8")).toString();
                }
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }

        return TextUtils.isEmpty(uuid)? null : MD5.getMD5Str(uuid);
    }
}
