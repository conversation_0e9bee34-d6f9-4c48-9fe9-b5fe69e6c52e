package com.kaolafm.ad.api.internal;

import com.kaolafm.ad.AdConstant;
import com.kaolafm.ad.api.internal.model.AdvertisingResult;
import com.kaolafm.opensdk.api.BaseResult;

import java.util.Map;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.QueryMap;

/**
 * <AUTHOR>
 * @date 2020-01-02
 */
public interface AdInternalService {

    @Headers(AdConstant.DOMAIN_HEADER_AD)
    @GET(AdConstant.ENGINE)
    Single<BaseResult<AdvertisingResult>> getAdvertisings(@QueryMap Map<String, String> params);
}
