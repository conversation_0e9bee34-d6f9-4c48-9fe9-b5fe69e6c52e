package com.kaolafm.opensdk.demo.operation.category;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import butterknife.BindView;
import com.bumptech.glide.Glide;
import com.kaolafm.opensdk.demo.BaseAdapter;
import com.kaolafm.opensdk.demo.BaseHolder;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.api.operation.model.ImageFile;
import com.kaolafm.opensdk.api.operation.model.category.CategoryMember;
import com.kaolafm.opensdk.utils.operation.OperationAssister;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/8/8
 */

public class CategoryMemberAdapter extends BaseAdapter<CategoryMember> {

    @Override
    protected BaseHolder<CategoryMember> getViewHolder(View view, int viewType) {
        return new CategoryMemberHolder(view);
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_category;
    }

    static class CategoryMemberHolder extends BaseHolder<CategoryMember> {

        @BindView(R.id.iv_item_category_icon)
        ImageView mIvItemCategoryIcon;

        @BindView(R.id.tv_item_category_des)
        TextView mTvItemCategoryDes;

        @BindView(R.id.tv_item_category_name)
        TextView mTvItemCategoryName;

        public CategoryMemberHolder(View itemView) {
            super(itemView);
        }

        @Override
        public void setupData(CategoryMember categorymember, int position) {
            Map<String, ImageFile> imageFiles = categorymember.getImageFiles();
            String cover = OperationAssister.getImage(ImageFile.KEY_COVER, imageFiles);
            if (TextUtils.isEmpty(cover)) {
                cover = OperationAssister.getImage(ImageFile.KEY_ICON, imageFiles);
            }
            Glide.with(itemView).load(cover).into(mIvItemCategoryIcon);
            String title = categorymember.getTitle();
            String subtitle = categorymember.getSubtitle();
            if (!TextUtils.isEmpty(subtitle)) {
                title = title + "--" + subtitle;
            }
            mTvItemCategoryName.setText(title);
            mTvItemCategoryDes.setText(categorymember.getDescription());
        }
    }
}
