package com.kaolafm.opensdk.api.scene;

import androidx.annotation.StringDef;

/**
 * <AUTHOR>
 *         速度场景
 **/
public final class SpeedScene extends Scene {
    private static final String CODE = "100002";
    //目前只有一个type
    public static final String TYPE_LOW_SPEED = "1";
    public static final String TYPE_MEDIUM_SPEED = "2";
    public static final String TYPE_HIGH_SPEED = "3";

    @StringDef({TYPE_LOW_SPEED, TYPE_MEDIUM_SPEED, TYPE_HIGH_SPEED})
    public @interface TYPE {
    }

    private String type = TYPE_LOW_SPEED;

    public SpeedScene(@TYPE String type) {
        this.type = type;
    }

    @Override
    public String getCode() {
        return CODE;
    }

    @Override
    public String getType() {
        return type;
    }
}
