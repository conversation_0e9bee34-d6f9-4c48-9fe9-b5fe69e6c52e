package com.kaolafm.opensdk.api.yunxin.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

/**
 * 云信自定义消息-直播礼物排行榜
 * <AUTHOR>
 * @date 2023-02-16
 */
public class GiftRankMsg implements Parcelable {
    private List<GiftRankUser>  rewardRanking;

    public GiftRankMsg() {
    }

    public GiftRankMsg(List<GiftRankUser> rewardRanking) {
        this.rewardRanking = rewardRanking;
    }

    protected GiftRankMsg(Parcel in) {
        rewardRanking = in.createTypedArrayList(GiftRankUser.CREATOR);
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeTypedList(rewardRanking);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<GiftRankMsg> CREATOR = new Creator<GiftRankMsg>() {
        @Override
        public GiftRankMsg createFromParcel(Parcel in) {
            return new GiftRankMsg(in);
        }

        @Override
        public GiftRankMsg[] newArray(int size) {
            return new GiftRankMsg[size];
        }
    };

    public List<GiftRankUser> getRewardRanking() {
        return rewardRanking;
    }

    public void setRewardRanking(List<GiftRankUser> rewardRanking) {
        this.rewardRanking = rewardRanking;
    }

    @Override
    public String toString() {
        return "GiftRankMsg{" +
                "rewardRanking=" + rewardRanking +
                '}';
    }
}
