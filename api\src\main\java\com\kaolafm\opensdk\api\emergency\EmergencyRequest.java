package com.kaolafm.opensdk.api.emergency;


import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.CrashMessageBaseBean;
import com.kaolafm.opensdk.api.emergency.model.EmergencyBroadcast;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.HashMap;


public class EmergencyRequest extends BaseRequest {

    private final EmergencyService mService;

    public EmergencyRequest() {
        mService = obtainRetrofitService(EmergencyService.class);
    }

    /**
     * 获取当前紧急广播
     */
    public void getEmergencyMessage(HttpCallback<EmergencyBroadcast> callback) {
        doHttpDeal(mService.getEmergencyMessage(), BaseResult::getResult, callback);
    }

    public void getEmergencyPolling(HttpCallback<EmergencyBroadcast> callback) {
        HashMap<String, String> params = new HashMap<>();
        params.put("msgContentType", "0");
        doHttpDeal(mService.getEmergencyPolling(params), BaseResult::getResult, callback);
    }
}
