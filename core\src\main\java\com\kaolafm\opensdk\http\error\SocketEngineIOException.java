package com.kaolafm.opensdk.http.error;

/**
 * 长连接引擎异常
 * <AUTHOR>
 */
public class SocketEngineIOException extends Exception {

    public String transport;
    public Object code;

    public SocketEngineIOException() {
        super();
    }

    public SocketEngineIOException(String message) {
        super(message);
    }

    public SocketEngineIOException(String message, Throwable cause) {
        super(message, cause);
    }

    public SocketEngineIOException(Throwable cause) {
        super(cause);
    }
}
