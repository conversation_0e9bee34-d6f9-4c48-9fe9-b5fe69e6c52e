package com.kaolafm.opensdk.api.live.model;

import java.io.Serializable;

/**
 * 直播 - 用户被禁言，被踢出直播间 - 客户端监听事件
 */
public class LiveStatusCode implements Serializable {

    private String openid;

    // 1-被禁言，2-被踢出直播间
    private String type;
    // 聊天室id
    private Long roomId;
    // 应用id
    private String appid;
    // 设备id
    private String deviceid;
    // 用户账号
    private String account;

    private String avatar;

    private String nickName;

    public String getOpenid() {
        return openid == null ? "" : openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getType() {
        return type == null ? "" : type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getRoomId() {
        return roomId;
    }

    public void setRoomId(Long roomId) {
        this.roomId = roomId;
    }

    public String getAppid() {
        return appid == null ? "" : appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getDeviceid() {
        return deviceid == null ? "" : deviceid;
    }

    public void setDeviceid(String deviceid) {
        this.deviceid = deviceid;
    }

    public String getAccount() {
        return account == null ? "" : account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getAvatar() {
        return avatar == null ? "" : avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getNickName() {
        return nickName == null ? "" : nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    @Override
    public String toString() {
        return "LiveStatusCode{" +
                "openid='" + openid + '\'' +
                ", type='" + type + '\'' +
                ", roomId=" + roomId +
                ", appid='" + appid + '\'' +
                ", deviceid='" + deviceid + '\'' +
                ", account='" + account + '\'' +
                ", avatar='" + avatar + '\'' +
                ", nickName='" + nickName + '\'' +
                '}';
    }
}
