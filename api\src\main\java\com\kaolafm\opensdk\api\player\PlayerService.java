package com.kaolafm.opensdk.api.player;

import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.player.model.PlayUrlData;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Query;

/**
 * <AUTHOR>
 * @date 2019-11-13
 */
interface PlayerService {

    @Headers(PlayerRequestConstant.DOMAIN_HEADER_API_KAOLA)
    @GET("album/playurl")
    Single<BaseResult<PlayUrlData>> getPlayUrl(@Query("urlPlayId") String playId);
}
