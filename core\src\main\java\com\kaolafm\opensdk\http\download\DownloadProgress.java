package com.kaolafm.opensdk.http.download;

/**
 * 下载状态
 *
 * <AUTHOR>
 * @date 2020-02-10
 */
public class DownloadProgress {

    /**已经下载的文件大小*/
    private long downloadedSize = 0;

    /**文件总大小*/
    private long totalSize = 0;

    /**是不是分块下载，如果是，总大小totalSize = -1*/
    private boolean chunked = false;

    public DownloadProgress(long downloadedSize, long totalSize, boolean chunked) {
        this.downloadedSize = downloadedSize;
        this.totalSize = totalSize;
        this.chunked = chunked;
    }

    public DownloadProgress(long downloadedSize, long totalSize) {
        this(downloadedSize, totalSize, false);
    }

    public DownloadProgress(long totalSize, boolean chunked) {
        this(0, totalSize, chunked);
    }

    public long getDownloadedSize() {
        return downloadedSize;
    }

    public void setDownloadedSize(long downloadedSize) {
        this.downloadedSize = downloadedSize;
    }

    public long getTotalSize() {
        return totalSize;
    }

    public void setTotalSize(long totalSize) {
        this.totalSize = totalSize;
    }

    public boolean isChunked() {
        return chunked;
    }

    public void setChunked(boolean chunked) {
        this.chunked = chunked;
    }

    public DownloadProgress increaseSize(long size) {
        downloadedSize += size;
        return this;
    }
}
