package com.kaolafm.report.model;

import io.reactivex.Single;

/**
 * <AUTHOR> on 2019/1/10.
 */

public class ReportTask<T> {
    private int type;
    private Single<T> singleTask;

    public Single<T> getSingleTask() {
        return singleTask;
    }

    public void setSingleTask(Single<T> singleTask) {
        this.singleTask = singleTask;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}
