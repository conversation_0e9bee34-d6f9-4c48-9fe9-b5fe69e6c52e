package com.kaolafm.opensdk.player.core.listener;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-06-19 18:18
 ******************************************/
public interface OnPlayLogicListener {
    /**
     * 播放前是否需要判断厂商可以播放场景
     * （如通用：播放前需要判断音源是否在考拉fm）
     * 返回true为外部进行了处理播放不会继续走下去，false为进行默认逻辑
     *
     * @return
     */
    boolean onPlayLogicDispose();
}
