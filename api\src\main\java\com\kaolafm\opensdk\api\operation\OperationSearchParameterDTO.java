package com.kaolafm.opensdk.api.operation;

import java.util.Map;

/**
 * 运营相关查询条件,
 */
class OperationSearchParameterDTO {

    /**是否递归获取:1是；0否*/
    private Integer isRecursive;
    /**父栏目code*/
    private String parentCode;
    /**经度*/
    private String lng;
    /** 纬度*/
    private String lat;
    /**分区*/
    private String zone;
    /** 内容类型 1:专辑，2:在线广播，3:直播，4:AI电台，5:QQ音乐电台 */
    private Integer contentType;
    /**扩展信息*/
    private Map<String, String> extInfo;
    /**是否包含成员：1是；0否*/
    private Integer withMembers;
    /**分类成员页码，选择包含成员时，必传*/
    private Integer pageNum;
    /**分类成员分页大小，选择包含成员时，必传*/
    private Integer pageSize;

    public Integer getContentType() {
        return contentType;
    }

    public void setContentType(Integer contentType) {
        this.contentType = contentType;
    }

    public String getZone() {
        return zone;
    }

    public void setZone(String zone) {
        this.zone = zone;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLng() {
        return lng;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public Map<String, String> getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(Map<String, String> extInfo) {
        this.extInfo = extInfo;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public Integer getIsRecursive() {
        return isRecursive;
    }

    public void setIsRecursive(Integer isRecursive) {
        this.isRecursive = isRecursive;
    }

    public Integer getWithMembers() {
        return withMembers;
    }

    public void setWithMembers(Integer withMembers) {
        this.withMembers = withMembers;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public String toString() {
        return "OperationSearchParameterDTO{" +
                "isRecursive=" + isRecursive +
                ", parentCode='" + parentCode + '\'' +
                ", lng='" + lng + '\'' +
                ", lat='" + lat + '\'' +
                ", zone='" + zone + '\'' +
                ", contentType=" + contentType +
                ", extInfo=" + extInfo +
                ", withMembers=" + withMembers +
                ", pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                '}';
    }
}
