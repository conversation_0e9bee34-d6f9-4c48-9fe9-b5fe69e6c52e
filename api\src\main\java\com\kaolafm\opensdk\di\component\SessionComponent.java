package com.kaolafm.opensdk.di.component;

import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.api.init.InitRequest;

import dagger.Subcomponent;

/**
 * 需要继承AppComponet的Subcomponet集合，添加的时候在这里添加就可以，不需要在AppComponent中添加。
 * <AUTHOR>
 * @date 2018/8/10
 */
@Subcomponent
public interface SessionComponent extends BaseSubcomponent {

    /**
     * QQ音乐播放器的Subcomponet
     * @return
     */
//    MusicPlayerComponent getMusicPlayerComponent();

    void inject(AccessTokenManager accessTokenManager);

    void inject(InitRequest request);
}
