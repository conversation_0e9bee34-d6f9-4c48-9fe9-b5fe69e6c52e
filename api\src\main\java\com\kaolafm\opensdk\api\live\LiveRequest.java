package com.kaolafm.opensdk.api.live;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.goods.model.GoodsResult;
import com.kaolafm.opensdk.api.live.model.ChatRoomMessageInfoResult;
import com.kaolafm.opensdk.api.live.model.ChatRoomTokenDetail;
import com.kaolafm.opensdk.api.live.model.GiftGivingResult;
import com.kaolafm.opensdk.api.live.model.GiftsResult;
import com.kaolafm.opensdk.api.live.model.LiveChatRoomMemberInfoResult;
import com.kaolafm.opensdk.api.live.model.LiveInfoDetail;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.HashMap;

import okhttp3.MediaType;
import okhttp3.RequestBody;

/**
 * <AUTHOR>
 */
public class LiveRequest extends BaseRequest {

    public static final String KEY_PROGRAM_ID = "programid";

    public static final String KEY_KRADIO_ID = "kradioId";

    public static final String KEY_PHONE_NUM = "phoneNum";

    private final LiveService mLiveService;

    public LiveRequest() {
        mLiveService = obtainRetrofitService(LiveService.class);
    }

    /**
     * 根据直播间的id获取直播信息
     *
     * @param id 直播间的Id
     */
    public void getLiveInfo(String id, HttpCallback<LiveInfoDetail> callback) {
        doHttpDeal(mLiveService.getLiveInfo(id), BaseResult::getResult, callback);
    }

    /**
     * 根据手机号获取进入直播的token。该接口需要用户手机号已经注册过考拉的账号。直播间显示的是考拉账号的用户名和头像。
     *
     * @param uid   用户uid
     * @param phone 手机号
     */
    public void getChatRoomToken(String uid, String phone, HttpCallback<ChatRoomTokenDetail> callback) {
        HashMap<String, String> tempMap = new HashMap<>();
        tempMap.put(KEY_KRADIO_ID, uid);
        tempMap.put(KEY_PHONE_NUM, phone);
        doHttpDeal(mLiveService.getChatRoomToken(tempMap), BaseResult::getResult, callback);
    }

    /**
     * 根据开发者提供的唯一标识、头像、昵称获取进入直播的token。
     *
     * @param id       唯一标识id
     * @param avatar   头像
     * @param nickName 昵称
     */
    public void getChatRoomToken(String id, String avatar, String nickName,
                                 HttpCallback<ChatRoomTokenDetail> callback) {
        HashMap<String, String> params = new HashMap<>();
        params.put("otherId", id);
        params.put("avatar", avatar);
        params.put("nickName", nickName);
        String body = mGsonLazy.get().toJson(params);
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);
        doHttpDeal(mLiveService.getChatRoomTokenByUnique(requestBody), BaseResult::getResult, callback);
    }

    /**
     * 在直播间中发送消息给后台服务器
     *
     * @param open_uid
     * @param appId
     * @param voiceUrl 语音消息保存在阿里云对象存储服务器中的绝对地址，需要编码。例如：https://iovimage.radio.cn/...
     * @param prgramid 直播节目id
     * @param callback
     */
    public void sendMessageToServer(String open_uid,
                                    String appId,
                                    String voiceUrl,
                                    long prgramid, HttpCallback<BaseResult<String>> callback) {
        doHttpDeal(mLiveService.sendMessage(open_uid, appId, voiceUrl, prgramid), callback);
    }

    /**
     * 获取礼物列表
     * @param liveId
     * @param callback
     */
    public void getGifts(Integer liveId, HttpCallback<GiftsResult> callback) {
        doHttpDeal(mLiveService.getGifts(liveId), BaseResult::getResult, callback);
    }

    /**
     * 送礼物
     * @param giftId    礼物id
     * @param liveId    直播id
     * @param count     礼物数量
     * @param callback
     */
    public void givingGifts(Long giftId,
                            Long liveId,
                            Integer count,
                            HttpCallback<GiftGivingResult> callback) {
        doHttpDeal(mLiveService.givingGifts(giftId, liveId, count), BaseResult::getResult, callback);
    }

    /**
     * 获取商品列表
     * @param liveId    直播id
     * @param callback
     */
    public void getGoods(Integer liveId, HttpCallback<GoodsResult> callback) {
        mUrlManager.putDomain(ApiHostConstants.MALL_DOMAIN_NAME, ApiHostConstants.MALL_HOST);
        doHttpDeal(mLiveService.getGoodsList(liveId), BaseResult::getResult, callback);
    }

    /**
     * 获取聊天室成员信息
     * @param liveId    聊天室id
     * @param callback
     */
    public void fetchRoomMembers(Long liveId, HttpCallback<LiveChatRoomMemberInfoResult> callback) {
        doHttpDeal(mLiveService.fetchRoomMembers(liveId), BaseResult::getResult, callback);
    }

    /**
     * 获取聊天室历史消息
     * @param liveId    直播间id
     * @param roomId    聊天室id
     * @param msgCount  查询历史消息最大条数, 不填默认为500条（暂定）
     * @param callback
     */
    public void getHistoryMessages(Long liveId, Long roomId, Long msgCount, HttpCallback<ChatRoomMessageInfoResult> callback) {
        doHttpDeal(mLiveService.getHistoryMessages(liveId, roomId, msgCount), BaseResult::getResult, callback);
    }

}
