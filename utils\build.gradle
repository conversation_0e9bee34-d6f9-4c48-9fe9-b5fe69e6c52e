apply plugin: 'com.android.library'
apply plugin: "build-jar"

def and = rootProject.ext.android

def VERSION_CODE = and.versionCode
def VERSION_NAME = and.versionName


android {
    compileSdkVersion and.compileSdkVersion
    buildToolsVersion and.buildToolsVersion
    defaultConfig {
        minSdkVersion and.minSdkVersion
        targetSdkVersion and.targetSdkVersion
        versionCode VERSION_CODE
        versionName VERSION_NAME
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

        ndk {
            abiFilter("arm64-v8a")
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-utils-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility and.javaSourceVersion
        targetCompatibility and.javaTargetVersion
    }
    packagingOptions {
        exclude 'LICENSE.txt'
    }

}

dependencies {
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation('androidx.test.espresso:espresso-core:3.1.0', {
        exclude group: 'com.android.support', module: 'support-annotations'
    })
    testImplementation 'org.mockito:mockito-core:2.19.0'
    androidTestImplementation 'androidx.annotation:annotation:1.6.0'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test:rules:1.3.0'

    compileOnly 'androidx.appcompat:appcompat:1.2.0'
    compileOnly fileTree(dir: 'libs', include: ['*.jar'])
}

upload {
    sdkFlavor {
        proguardConfigFile = ["proguard-utils-rules.pro"]
        includePackage = ["com/kaolafm/base"]
        versionName = VERSION_NAME
        outputFileName = "Utils"
        mavenConfig {
            artifactId     'utils'
            groupId        'com.kaolafm'
            libType        'jar'
            libDescription 'SDK Utils'
//                repository      readLocalProperties('local.repo.url')
        }
    }
}
