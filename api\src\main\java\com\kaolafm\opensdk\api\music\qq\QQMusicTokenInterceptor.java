package com.kaolafm.opensdk.api.music.qq;

import android.os.Build;
import android.os.SystemClock;
import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;

import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.account.token.QQMusicAccessToken;
import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.music.qq.model.TencentLoginResult;
import com.kaolafm.opensdk.di.scope.AppScope;

import org.json.JSONObject;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.Map;

import javax.inject.Inject;

import okhttp3.HttpUrl;
import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.Buffer;
import okio.BufferedSource;

import static java.nio.charset.StandardCharsets.UTF_8;

/**
 * 验证qq音乐的token是否过期的Inerceptor,如果过去就同步请求刷新token
 *
 * <AUTHOR>
 * @date 2018/5/6
 */

public class QQMusicTokenInterceptor implements Interceptor {

    @Inject
    @AppScope
    AccessTokenManager mTokenManager;

    private QQMusicRequest mMusicRequest;

    @Inject
    public QQMusicTokenInterceptor() {
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    @Override
    public Response intercept(@NonNull Chain chain) throws IOException {
        Request request = chain.request();
        Response originalResponse = chain.proceed(request);
        //不是QQ音乐的host就返回原结果
        String url = request.url().toString();
        //如果不是QQ音乐的host或者是刷新token的请求就不处理
        if (!url.startsWith(ApiHostConstants.QQMUSIC_HOST)
                || url.contains("refresh_key")
                || url.contains("qq_refresh_token")) {
            return originalResponse;
        }
        //判断返回的信息中是否是由于token过期
        if (!isTokenExpires(originalResponse)) {
            return originalResponse;
        }
        refreshToken();
        // 更换新的登录信息参数
        HttpUrl.Builder builder = request.url().newBuilder();
        Map<String, String> loginInfoParams = mMusicRequest.getCommonParams();
        if (loginInfoParams != null && !loginInfoParams.isEmpty()) {
            for (String key : loginInfoParams.keySet()) {
                builder.removeAllQueryParameters(key);
                builder.addQueryParameter(key, loginInfoParams.get(key));
            }
        }
        // 关闭原来的请求
        originalResponse.body().close();
        return chain.proceed(request.newBuilder().url(builder.build()).build());
    }

    private void refreshToken() {
        mMusicRequest = new QQMusicRequest();
        // 同步方式刷新token
        TencentLoginResult loginResult = null;
        QQMusicAccessToken musicAccessToken = mTokenManager.getQQMusicAccessToken();
        int loginType = musicAccessToken.getLoginType();
        if (loginType == QQMusicConstant.LOGIN_TYPE_QQ) {
            loginResult = mMusicRequest.refreshQQTokenForLogin();
        } else if (loginType == QQMusicConstant.LOGIN_TYPE_WECHAT) {
            loginResult = mMusicRequest.refreshWeChatTokenForLogin();
        }
        if (loginResult != null) {
            loginResult.setLoginType(loginType);
            mTokenManager.setCurrentAccessToken(musicAccessToken.refreshToken(loginResult));
        }

    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    private boolean isTokenExpires(Response response) {
        long start = SystemClock.currentThreadTimeMillis();
        ResponseBody body = response.newBuilder().build().body();
        try {
            BufferedSource source = body.source();
            source.request(Long.MAX_VALUE);
            Buffer buffer = source.buffer();
            Buffer clone = buffer.clone();
            Charset charset = charset(body);
            JSONObject jsonObject = new JSONObject(clone.readString(charset));
            int retCode = jsonObject.getInt("ret");
            return retCode == QQMusicApiConstant.CODE_LOGIN_STATUS_FAILED
                    || retCode == QQMusicApiConstant.CODE_LOGIN_STATUS_FAILED_WX
                    || retCode == QQMusicApiConstant.CODE_LOGIN_STATUS_FAILED_THIRD
                    || retCode == QQMusicApiConstant.CODE_LOGIN_STATUS_FAILED_CONNECT_WX
                    || retCode == QQMusicApiConstant.CODE_LOGIN_STATUS_FAILED_VERIFICATION;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    private Charset charset(ResponseBody body) {
        MediaType contentType = body.contentType();
        return contentType != null ? contentType.charset(UTF_8) : UTF_8;
    }

    private boolean isText(MediaType mediaType) {
        if (mediaType.type() != null && mediaType.type().equals("text")) {
            return true;
        }
        if (mediaType.subtype() != null) {
            if (mediaType.subtype().equals("json") ||
                    mediaType.subtype().equals("xml") ||
                    mediaType.subtype().equals("html") ||
                    mediaType.subtype().equals("webviewhtml")
                    ) {
                return true;
            }
        }
        return false;
    }
}
