package com.kaolafm.opensdk.api.login;


import com.kaolafm.opensdk.api.KaolaApiConstant;

/**
 * @ClassName KRadioApiConstants
 * @Description kradio 请求常量值 wiki "http://wiki.kaolafm.com/pages/viewpage.action?pageId=********"
 * <AUTHOR>
 * @Date 2020-02-14 10:33
 * @Version 1.0
 */
public class KRadioApiConstants {
    /**
     * 获取验证码
     */
    public static final String REQUEST_VERIFY_CODE_URL = KaolaApiConstant.KAOLA_VERSION + "/security/account/internal/getVerifyCode";
    /**
     * 使用二维码登录
     */
    public static final String REQUEST_LOGIN_URL = KaolaApiConstant.KAOLA_VERSION + "/security/account/internal/sms/login";
    /**
     * 退出登录
     */
    public static final String REQUEST_LOGOUT_URL = KaolaApiConstant.KAOLA_VERSION + "/security/account/internal/logout";
    /**
     * 扫描后获取token
     */
    public static final String REQUEST_TOKEN_URL = KaolaApiConstant.KAOLA_VERSION + "/security/account/internal/getToken";

    public static final String KEY_SIGN = "sign";

    public static final String SAVE_HISTORY_ACCUMULATE_URL = KaolaApiConstant.KAOLA_VERSION + "/kradio/saveHistoryAccumulate";

    public static final String GET_EFFECTIVE_LIVE_URL = KaolaApiConstant.KAOLA_VERSION + "/liveplay/getEffectiveLive";
}
