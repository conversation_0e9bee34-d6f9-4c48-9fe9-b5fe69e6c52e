package com.kaolafm.report.model;

import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> on 2019/1/25.
 * 播放上报参数. 主要用于播放结束使用
 */

public class PlayReportParameter {
    /**
     * 1.当前通过qqapi接口获取的内容（排行榜等）单曲id为qqapi
     * 2.QQ音乐电台（一人一首招牌歌等）中单曲，上报的audioid 为 qqradio
     */
    private String audioid;
    /**
     * 1.通过api接口获得qq音乐内容，radioid为空
     * 2.qq音乐电台中单曲的radio为qq音乐电台的id
     */
    private String radioid;
    /**
     * 专辑同radioid；电台流为当前播放单曲所属专辑的id
     * qq api接口获取内容，为空
     * qq音乐电台的单曲，为空
     */
    private String albumid;

    /**
     * 播放类型	0：离线播放；1：在线播放
     */
    private String type = ReportConstants.PLAY_TYPE_ON_LINE;
    /**
     * 播放器位置	1：app播放器；2：外部播放器
     */
    private String position = ReportConstants.POSITION_INNER_APP;

    /**
     * 开机收听	0:否；1：是
     */
    private String isStartFirstPlay;

    /**
     * 获取方式
     */
    private String contentObtainType = ReportConstants.COTENT_BY_OTHER;

    /**
     * 搜索服务端透传的数据
     */
    private String searchResultContent;

    /**
     * 首次收听
     */
    private String isFirst = "0";
    /**
     * 播放进度
     */
    private long playPosition;

    /**
     * 总长度
     */
    private long totalLength;

    /**
     * 播放开始时间
     */
    private String startTime;

    /**
     * 切换原因
     */
    private String changeType;

    private String playId;

    /**
     * 是否是内部播放器
     */
    protected String innerPlayer;

    /**
     * 电台编排位
     */
    private String radioType;
    /**
     * isThirdParty//是否是第三方源，1是0否，1的时候要上报收听
     */
    private int isThirdParty = ReportConstants.REPORT_EVENT_NORMAL;

    /**
     * 播放来源
     */
    private String audioSource;

    /**
     * 推荐结果追踪号
     */
    private String recommendResultCallback;

    /**
     * 直播标识id
     */
    private String liveType_live_id;
    /**
     * 直播计划id
     */
    private String liveType_plan_id;
    /**
     * 支持人id
     */
    private String liveType_compereid;
    /**
     * 进入直播的位置 1：运营位；2：AI电台；3：电台直播开播PUSH；
     */
    private String liveType_position;
    /**
     * 直播状态 1：直播中；2：回放中
     */
    private String liveType_status;
    /**
     * 播放状态 1：直播中；2：回放中
     */
    private String broadcast_status;

    /**
     * 来源类型
     */
    private int sourceType = ReportConstants.SOURCE_TYPE_ALBUM;
    /**
     * 0免费 1付费 2试听
     */
    private int audioid_type = 0;
    /**
     * 精品 VIP 无
     */
    private String tag = "";

    public int getAudioid_type() {
        return audioid_type;
    }

    public void setAudioid_type(int audioid_type) {
        this.audioid_type = audioid_type;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getAudioid() {
        return audioid;
    }

    public void setAudioid(String audioid) {
        this.audioid = audioid;
    }

    public String getRadioid() {
        return radioid;
    }

    public void setRadioid(String radioid) {
        this.radioid = radioid;
    }

    public String getAlbumid() {
        return albumid;
    }

    public void setAlbumid(String albumid) {
        this.albumid = albumid;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }


    public long getPlayPosition() {
        return playPosition;
    }

    public void setPlayPosition(long playPosition) {
        this.playPosition = playPosition;
    }

    public long getTotalLength() {
        return totalLength;
    }

    public void setTotalLength(long totalLength) {
        this.totalLength = totalLength;
    }

    public String getIsStartFirstPlay() {
        return isStartFirstPlay;
    }

    public void setIsStartFirstPlay(String isStartFirstPlay) {
        this.isStartFirstPlay = isStartFirstPlay;
    }

    public String getContentObtainType() {
        return contentObtainType;
    }

    public void setContentObtainType(String contentObtainType) {
        this.contentObtainType = contentObtainType;
    }

    public String getSearchResultContent() {
        return searchResultContent;
    }

    public void setSearchResultContent(String searchResultContent) {
        this.searchResultContent = searchResultContent;
    }

    public String getIsFirst() {
        return isFirst;
    }

    public void setIsFirst(String isFirst) {
        this.isFirst = isFirst;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getChangeType() {
        return changeType;
    }

    public void setChangeType(String changeType) {
        this.changeType = changeType;
    }

    public String getPlayId() {
        return playId;
    }

    public void setPlayId(String playId) {
        this.playId = playId;
    }

    public int getIsThirdParty() {
        return isThirdParty;
    }

    public void setIsThirdParty(int isThirdParty) {
        this.isThirdParty = isThirdParty;
    }

    public boolean isSendEventNow() {
        return isThirdParty == ReportConstants.REPORT_EVENT_RIGHT_NOW;
    }

    public String getInnerPlayer() {
        return innerPlayer;
    }

    public String getRadioType() {
        return radioType;
    }

    public void setRadioType(String radioType) {
        this.radioType = radioType;
    }

    public String getAudioSource() {
        return audioSource;
    }

    public void setAudioSource(String audioSource) {
        this.audioSource = audioSource;
    }

    public String getRecommendResultCallback() {
        return recommendResultCallback;
    }

    public void setRecommendResultCallback(String recommendResultCallback) {
        this.recommendResultCallback = recommendResultCallback;
    }

    public String getLiveType_live_id() {
        return liveType_live_id;
    }

    public void setLiveType_live_id(String liveType_live_id) {
        this.liveType_live_id = liveType_live_id;
    }

    public String getLiveType_plan_id() {
        return liveType_plan_id;
    }

    public void setLiveType_plan_id(String plan_id) {
        this.liveType_plan_id = plan_id;
    }

    public String getLiveType_compereid() {
        return liveType_compereid;
    }

    public void setLiveType_compereid(String liveType_compereid) {
        this.liveType_compereid = liveType_compereid;
    }

    public String getLiveType_position() {
        return liveType_position;
    }

    public void setLiveType_position(String liveType_position) {
        this.liveType_position = liveType_position;
    }

    public String getLiveType_status() {
        return liveType_status;
    }

    public void setLiveType_status(String liveType_status) {
        this.liveType_status = liveType_status;
    }

    public String getBroadcast_status() {
        return broadcast_status;
    }

    public void setBroadcast_status(String broadcast_status) {
        this.broadcast_status = broadcast_status;
    }

    public int getSourceType() {
        return sourceType;
    }

    public void setSourceType(int sourceType) {
        this.sourceType = sourceType;
    }
}
