package com.kaolafm.opensdk.api.operation.model.category;

/**
 * 人工运营分类成员:QQ音乐电台
 */
public class RadioQQMusicCategoryMember extends CategoryMember {

    private long radioQQMusicId;

    /** QQ音乐的id*/
    private int oldId;

    /** 电台类型: 0=场景电台；1=标签电台*/
    private int radioQQMusicType;

    public long getRadioQQMusicId() {
        return radioQQMusicId;
    }

    public void setRadioQQMusicId(long radioQQMusicId) {
        this.radioQQMusicId = radioQQMusicId;
    }

    public int getOldId() {
        return oldId;
    }

    public void setOldId(int oldId) {
        this.oldId = oldId;
    }

    public int getRadioQQMusicType() {
        return radioQQMusicType;
    }

    public void setRadioQQMusicType(int radioQQMusicType) {
        this.radioQQMusicType = radioQQMusicType;
    }

    @Override
    public String toString() {
        return "RadioQQMusicCategoryMember{" +
                "radioQQMusicId=" + radioQQMusicId +
                ", oldId=" + oldId +
                ", radioQQMusicType=" + radioQQMusicType +
                '}';
    }
}
