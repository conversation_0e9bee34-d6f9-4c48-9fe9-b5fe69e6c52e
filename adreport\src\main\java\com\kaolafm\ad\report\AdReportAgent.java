package com.kaolafm.ad.report;


import com.kaolafm.ad.report.bean.BaseAdEvent;

public class AdReportAgent {

    /**
     * 通用广告事件上报接口
     * @param id sessionId
     * @param eventType  上报类型（EventType)
     */
    public static void onEvent(String id,int eventType){
        BaseAdEvent baseAdEvent = new BaseAdEvent();
        baseAdEvent.setSessionId(id);
        baseAdEvent.setEventType(eventType);
        onEvent(baseAdEvent);
    }

    public static void onEvent(BaseAdEvent baseAdEvent){
        EventTask eventTask = new EventTask(baseAdEvent);
        AdReportManager.getInstance().getExecutorService().execute(eventTask);
    }

    public static class EventType {
        public static final int PV = 0;
        public static final int PLAY_START = 1;
        public static final int PLAY_END = 2;
        public static final int CLICK = 3;
        public static final int SKIP = 4;
        public static final int DISPLAY_INTERRUPT = 5;
        public static final int DISPLAY_END = 6;
        public static final int DISPLAY_MORE_INTERACTION = 7;
        public static final int MODE_INTERACTION_END = 8;
        public static final int MIAOZHEN_MONITOR = 9;
        public static final int TALKING_DATA_MONITOR = 10;
    }

}
