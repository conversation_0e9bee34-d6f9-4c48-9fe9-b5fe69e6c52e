//package com.kaolafm.opensdk.demo.activity;
//
//import android.graphics.Rect;
//import android.support.v7.widget.GridLayoutManager;
//import android.support.v7.widget.RecyclerView;
//import android.support.v7.widget.StaggeredGridLayoutManager;
//import android.view.View;
//
//import com.kaolafm.kradio.k_kaolafm.R;
//import com.kaolafm.kradio.lib.utils.ResUtil;
//
//public class RvItemDecoration extends RecyclerView.ItemDecoration {
//
//    @Override
//    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
//
//        outRect.bottom = ResUtil.getDimen(R.dimen.m30);
//
//        final int spanCount = getSpanCount(parent);
//        final int childCount = parent.getAdapter().getItemCount();
//        final int adapterPosition = parent.getChildAdapterPosition(view);
//        if (isLastColumn(adapterPosition, spanCount, childCount)) {
//            outRect.right = 0;
//        } else {
//            outRect.right = ResUtil.getDimen(R.dimen.m50);
//        }
//
//    }
//
//    private boolean isLastColumn(int position, int spanCount, int childCount) {
//        return (position + 1) % spanCount == 0;
//    }
//
//    private int getSpanCount(RecyclerView parent) {
//        RecyclerView.LayoutManager layoutManager = parent.getLayoutManager();
//
//        if (layoutManager instanceof GridLayoutManager) {
//            return ((GridLayoutManager) layoutManager).getSpanCount();
//        } else if (layoutManager instanceof StaggeredGridLayoutManager) {
//            return ((StaggeredGridLayoutManager) layoutManager).getSpanCount();
//        } else {
//            throw new UnsupportedOperationException("the GridDividerItemDecoration can only be used in " +
//                    "the RecyclerView which use a GridLayoutManager or StaggeredGridLayoutManager");
//        }
//    }
//}
