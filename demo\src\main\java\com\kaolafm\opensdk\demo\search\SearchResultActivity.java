package com.kaolafm.opensdk.demo.search;

import android.content.Intent;
import android.os.Bundle;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.BindView;
import com.kaolafm.opensdk.api.search.SearchRequest;
import com.kaolafm.opensdk.api.search.model.VoiceSearchProgramBean;
import com.kaolafm.opensdk.api.search.model.VoiceSearchResult;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.lcodecore.tkrefreshlayout.RefreshListenerAdapter;
import com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout;
import java.util.List;

/**
 * <AUTHOR> Yan
 * @date 2018/8/6
 */

public class SearchResultActivity extends BaseActivity {
    public static final String KEY_SEARCH_TYPE = "SearchType";
    public static final String KEY_ORIGIN_JSON = "OriginJson";
    public static final String KEY_FIELD = "Field";
    public static final String KEY_ARTIST = "Artist";
    public static final String KEY_AUDIO_NAME = "AudioName";
    public static final String KEY_ALBUM_NAME = "AlbumName";
    public static final String KEY_CATEGORY = "Category";
    public static final String KEY_KEYWORD = "Keyword";
    public static final String KEY_CREDIBILITY = "Credibility";
    public static final String KEY_VOICE_SOURCE = "VoiceSource";
    public static final String KEY_VOICE_QUALITY = "VoiceQuality";
    public static final String KEY_VOICE_TEXT = "VoiceText";

    public static final int SEARCH_BY_KEYWORD = 10;

    public static final int SEARCH_BY_KEYWORD_TYPE = 11;

    public static final int SEARCH_BY_SEMATICS = 12;

    @BindView(R.id.rv_search_result_list)
    RecyclerView mRvSearchResultList;

    @BindView(R.id.trl_result_list_refresh)
    TwinklingRefreshLayout mTrlResultListRefresh;

    private String mAlbumName;

    private String mArtist;

    private String mAudioName;

    private String mCategory;

    private int mCredibility;

    private int mField;

    private String mKeyword;

    private int mNextPage;

    private String mOriginJson;

    private int mPageSize;

    private SearchResultAdapter mResultAdapter;

    private SearchResultNewAdapter mSearchResultNewAdapter;

    private int mSearchType;

    private String mType;

    private boolean isHaveNext;

    private int mVoiceQuality;

    private String mVoiceSource;

    private String mVoiceText;


    @Override
    public int getLayoutId() {
        return R.layout.activity_search_result;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        setTitle("搜索结果列表页");
        mRvSearchResultList.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false));
        mTrlResultListRefresh.setEnableRefresh(false);
        mTrlResultListRefresh.setEnableLoadmore(false);
        mRvSearchResultList.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));
    }

    @Override
    public void initArgs() {
        Intent intent = getIntent();
        if (intent != null) {
            mSearchType = intent.getIntExtra(KEY_SEARCH_TYPE, 0);
            mKeyword = intent.getStringExtra(KEY_KEYWORD);
            if (mSearchType == SEARCH_BY_KEYWORD) {
                mResultAdapter = new SearchResultAdapter();
                mResultAdapter.setOnItemClickListener((view, viewType, voiceSearchData, position) -> {
                    Intent detailIntent = new Intent(SearchResultActivity.this, ProgramDetailActivity.class);
                    detailIntent.putExtra(ProgramDetailActivity.KEY_PROGRAM, voiceSearchData);
                    startActivity(detailIntent);
                });
                mRvSearchResultList.setAdapter(mResultAdapter);
            } else if (mSearchType == SEARCH_BY_KEYWORD_TYPE) {
                mPageSize = intent.getIntExtra("PageSize", 20);
                mType = intent.getStringExtra("Type");
                mResultAdapter = new SearchResultAdapter();
                mResultAdapter.setOnItemClickListener((view, viewType, voiceSearchData, position) -> {
                    Intent detailIntent = new Intent(SearchResultActivity.this, ProgramDetailActivity.class);
                    detailIntent.putExtra(ProgramDetailActivity.KEY_PROGRAM, voiceSearchData);
                    startActivity(detailIntent);
                });
                mRvSearchResultList.setAdapter(mResultAdapter);
                mTrlResultListRefresh.setOnRefreshListener(new RefreshListenerAdapter() {
                    @Override
                    public void onLoadMore(TwinklingRefreshLayout refreshLayout) {
                        super.onLoadMore(refreshLayout);
                        if (isHaveNext) {
                            searchByType(mNextPage);
                        } else {
                            //没有下一页就不加载
                            showToast("没有更多");
                            mTrlResultListRefresh.finishLoadmore();
                        }
                    }
                });
            } else if (mSearchType == SEARCH_BY_SEMATICS) {
                mSearchResultNewAdapter = new SearchResultNewAdapter();
                mSearchResultNewAdapter.setOnItemClickListener((view, viewType, searchProgramBean, position) -> {
                    Intent detailIntent = new Intent(SearchResultActivity.this, ProgramDetailActivity.class);
                    detailIntent.putExtra(ProgramDetailActivity.KEY_VOICE_PROGRAM, searchProgramBean);
                    startActivity(detailIntent);
                });
                mRvSearchResultList.setAdapter(mSearchResultNewAdapter);
                mOriginJson = intent.getStringExtra(KEY_ORIGIN_JSON);
                mVoiceSource = intent.getStringExtra(KEY_VOICE_SOURCE);
                mVoiceQuality = intent.getIntExtra(KEY_VOICE_QUALITY, -1);
                mField = intent.getIntExtra(KEY_FIELD, -1);
                mCredibility = intent.getIntExtra(KEY_CREDIBILITY, 0);
                mArtist = intent.getStringExtra(KEY_ARTIST);
                mAudioName = intent.getStringExtra(KEY_AUDIO_NAME);
                mAlbumName = intent.getStringExtra(KEY_ALBUM_NAME);
                mCategory = intent.getStringExtra(KEY_CATEGORY);
                mVoiceText = intent.getStringExtra(KEY_VOICE_TEXT);
            }
        }
    }

    @Override
    public void initData() {
        switch (mSearchType) {
            case SEARCH_BY_KEYWORD:
                break;
            case SEARCH_BY_KEYWORD_TYPE:
                searchByType(1);
                break;
            case SEARCH_BY_SEMATICS:
                //新接口
                new SearchRequest()
                        .searchBySemantics(mVoiceSource, mVoiceQuality, mOriginJson, mField,
                                mCredibility, mArtist, mAudioName, mAlbumName, mCategory, mKeyword, mVoiceText, null,
                                null, null,
                                new HttpCallback<VoiceSearchResult>() {
                                    @Override
                                    public void onSuccess(VoiceSearchResult voiceSearchResult) {
                                        List<VoiceSearchProgramBean> programList = voiceSearchResult.getProgramList();
                                        if (programList != null && programList.size() > 0) {
                                            mSearchResultNewAdapter.setDataList(programList);
                                            if(voiceSearchResult.getPlayType() == 1){
                                                PlayerManager.getInstance().start(new PlayerBuilder().setId(String.valueOf(programList.get(voiceSearchResult.getPlayIndex()).getId())).setType(programList.get(voiceSearchResult.getPlayIndex()).getType()));
                                            }
                                        } else {
                                            showToast("数据为空");
                                        }
                                    }

                                    @Override
                                    public void onError(ApiException exception) {
                                        showToast("网络请求错误，错误码=" + exception.getCode() + ", 错误信息=" + exception
                                                .getMessage());
                                    }
                                });
                break;
            default:
        }
    }

    private void searchByType(int pageNum) {
    }
}
