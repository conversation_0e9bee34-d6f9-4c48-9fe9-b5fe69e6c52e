package com.kaolafm.ad.api.internal;

import com.kaolafm.ad.api.internal.model.AdvertisingResult;
import com.kaolafm.ad.db.manager.ReportParamDBManager;
import com.kaolafm.ad.di.component.AdvertSubcomponent;
import com.kaolafm.ad.di.qualifier.AdInitParam;
import com.kaolafm.ad.di.qualifier.BasicParam;
import com.kaolafm.ad.di.qualifier.CommonParam;
import com.kaolafm.ad.profile.AdvertisingProfile;
import com.kaolafm.ad.report.api.ReportRequest;
import com.kaolafm.ad.util.AdParamsUtil;
import com.kaolafm.base.utils.MD5;
import com.kaolafm.opensdk.api.AbstractRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.di.qualifier.ProfileQualifier;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.Set;

import javax.inject.Inject;
import javax.inject.Provider;

import dagger.Lazy;
import io.reactivex.Single;
import io.reactivex.functions.Function;
import retrofit2.Response;

/**
 * 广告上报接口
 *
 * <AUTHOR> Yan
 * @date 2020-01-02
 */
@AppScope
public class AdReportRequest extends AbstractRequest implements ReportRequest {

    private AdReportService mAdReportService;

    @Inject
    @AdInitParam
    Lazy<String> activeParam;

    @Inject
    @ProfileQualifier
    Lazy<AdvertisingProfile> profile;

    @Inject
    @CommonParam
    Provider<Set<OrderedPair>> commonParams;

    @Inject
    @BasicParam
    Provider<Set<OrderedPair>> basicParams;

    @Inject
    public AdReportRequest() {
        AdvertSubcomponent subcomponent = ComponentKit.getInstance().getSubcomponent();
        subcomponent.inject(this);
        mAdReportService = obtainRetrofitService(AdReportService.class);
    }

    private Function<Response<Void>, Boolean> map() {
        return Response::isSuccessful;
    }

    public void active(HttpCallback<Boolean> callback) {
        doHttpDeal(mAdReportService.active(activeParam.get()), map(), callback);
    }

    /**
     * 显示广告上报
     *
     * @param sessionId 每次请求广告返回的唯一值
     * @param time     广告显示时间 yyyy-MM-dd_HH:mm:ss
     * @param callback 回调
     */
    @Override
    public void display(String sessionId, String time, HttpCallback<Boolean> callback) {
        Single<Response<Void>> display = getParam(sessionId, time).flatMap((param -> mAdReportService.display(param)));
        doHttpDeal(display, map(), callback);
    }

    /**
     * 上报广告显示结束时间
     *
     * @param sessionId 每次请求广告返回的唯一值
     * @param endTime  广告图片显示结束时间 yyyy-MM-dd_HH:mm:ss
     * @param callback 回调
     */
    @Override
    public void endDisplay(String sessionId, String endTime, HttpCallback<Boolean> callback) {
        Single<Response<Void>> endPlay = getParam(sessionId, endTime).flatMap(param -> mAdReportService.endDisplay(param));
        doHttpDeal(endPlay, map(), callback);
    }

    /**
     * 上报打断广告显示
     *
     * @param      sessionId 每次请求广告返回的唯一值
     * @param interruptTime 广告图片因阻碍用户操作而被关闭的时间，格式为yyyy-MM-dd_HH:mm:ss
     * @param callback      回调
     */
    @Override
    public void interruptDisplay(String sessionId, String interruptTime, HttpCallback<Boolean> callback) {
        Single<Response<Void>> interruptDisplay = getParam(sessionId, interruptTime).flatMap(param -> mAdReportService.interruptDisplay(param));
        doHttpDeal(interruptDisplay, map(), callback);
    }

    /**
     * 上报开始播放
     *
     * @param sessionId 每次请求广告返回的唯一值
     * @param time     开始播放时间，格式为yyyy-MM-dd_HH:mm:ss
     * @param callback 回调
     */
    @Override
    public void play(String sessionId, String time, HttpCallback<Boolean> callback) {
        Single<Response<Void>> play = getParamsSet(sessionId, time).flatMap(set -> {
            add(set, 55, "event_code", 1);
            return mAdReportService.play(AdParamsUtil.createParamData(set));
        });
        doHttpDeal(play, map(), callback);
    }

    /**
     * 上报播放结束
     *
     * @param sessionId 每次请求广告返回的唯一值
     * @param time     播放结束时间, 格式为yyyy-MM-dd_HH:mm:ss
     * @param playTime 播放时长，单位秒
     * @param callback 回调
     */
    @Override
    public void endPlay(String sessionId, String time, long playTime, HttpCallback<Boolean> callback) {
        Single<Response<Void>> endPlay = ReportParamDBManager.getInstance().queryById(sessionId).flatMap(reportAdvert -> {
            Set<OrderedPair> params = basicParams.get();
            add(params, 10, "end_time", time);
            add(params, 60, "event_code", 2);
            add(params, 70, "play_time", playTime);
            if (reportAdvert != null) {
                add(params, 20, "sessionId", reportAdvert.getSessionId());
            }
            return mAdReportService.play(AdParamsUtil.createParamData(params));
        });
        doHttpDeal(endPlay, map(), callback);
    }

    /**
     * 上报跳过广告
     *
     * @param sessionId 每次请求广告返回的唯一值
     * @param skipTime 用户跳过广告的时间，格式为yyyy-MM-dd_HH:mm:ss
     * @param callback 回调
     */
    @Override
    public void skip(String sessionId, String skipTime, HttpCallback<Boolean> callback) {
        Single<Response<Void>> skip = getParam(sessionId, skipTime).flatMap(param -> mAdReportService.skip(param));
        doHttpDeal(skip, map(), callback);
    }

    /**
     * 上报二次互动图片显示时间
     *
     * @param    sessionId 每次请求广告返回的唯一值
     * @param displayTime 二次互动图标显示的时间，格式为yyyy-MM-dd_HH:mm:ss
     * @param callback    回调
     */
    @Override
    public void displayMoreInteraction(String sessionId, String displayTime, HttpCallback<Boolean> callback) {
        Single<Response<Void>> display = getParam(sessionId, displayTime).flatMap(param -> mAdReportService.displayMoreInteraction(param));
        doHttpDeal(display, map(), callback);
    }

    /**
     * 上报二次互动广告图片显示结束时间
     *
     * @param       sessionId 每次请求广告返回的唯一值
     * @param displayEndTime 二次互动广告图片显示结束时间，格式为yyyy-MM-dd_HH:mm:ss
     * @param callback       回调
     */
    @Override
    public void displayMoreInteractionEnd(String sessionId, String displayEndTime, HttpCallback<Boolean> callback) {
        Single<Response<Void>> displayEnd = getParam(sessionId, displayEndTime).flatMap(param -> mAdReportService.displayMoreInteractionEnd(param));
        doHttpDeal(displayEnd, map(), callback);
    }

    /**
     * 上报广告点击事件
     *
     * @param  sessionId 每次请求广告返回的唯一值
     * @param clickTime 二次互动广告图片显示结束时间，格式为yyyy-MM-dd_HH:mm:ss
     * @param callback  回调
     */
    @Override
    public void click(String sessionId, String clickTime, HttpCallback<Boolean> callback) {
        Single<Response<Void>> click = getParam(sessionId, clickTime).flatMap(param -> mAdReportService.click(param));
        doHttpDeal(click, map(), callback);
    }

    /**
     * 上报引擎错误
     *  @param adZoneId
     * @param baseResult   广告引擎返回的结果
     * @param callback 回调
     */
    public void error(String adZoneId, BaseResult<AdvertisingResult> baseResult, HttpCallback<Boolean> callback) {
        AdvertisingResult result = baseResult.getResult();
        if (result != null) {
            Set<OrderedPair> params = commonParams.get();
            add(params, 10, "error_time", AdParamsUtil.getTime());
            add(params, 20, "sessionId", result.getSessionId());
            add(params, 80, "member_id", result.getMemberId());
            add(params, 90, "secondary_member_id", "");
            add(params, 100, "media_id", "");
            add(params, 110, "adzone_id", "");
            add(params, 120, "campaign_id", "");
            add(params, 130, "adgroup_id", "");
            add(params, 140, "creative_id", "");
            add(params, 150, "customer_id", "");
            add(params, 160, "trans_type", result.getTransType());
            add(params, 170, "cost", result.getCost());
            add(params, 210, "md5_code", MD5.getMD5Str(adZoneId + profile.get().getAppId() + result.getSessionId() + "7eXEa4QOi1JdH3LI"));
            add(params, 211, "error_code", baseResult.getCode());
            doHttpDeal(mAdReportService.error(AdParamsUtil.createParamData(params)), map(), callback);
        }
    }

    private Single<Set<OrderedPair>> getParamsSet(String sessionId, String time) {
        //根据id查询数据库
        return ReportParamDBManager.getInstance().queryById(sessionId)
                .map(reportAdvert -> {
                    if (reportAdvert == null) {
                        throw new ApiException(String.format("no advert found for sessionId (%s) in db ", sessionId));
                    }
                    Set<OrderedPair> orderedPairs = commonParams.get();
                    add(orderedPairs, 10, "time", time);
                    add(orderedPairs, 20, "sessionId", reportAdvert.getSessionId());
                    add(orderedPairs, 80, "member_id", reportAdvert.getMemberId());
                    add(orderedPairs, 90, "secondary_member_id", reportAdvert.getSecondaryMemberIds());
                    add(orderedPairs, 100, "media_id", reportAdvert.getMediaId());
                    add(orderedPairs, 110, "adzone_id", reportAdvert.getAdZoneId());
                    add(orderedPairs, 120, "campaign_id", reportAdvert.getCampaignId());
                    add(orderedPairs, 130, "adgroup_id", reportAdvert.getAdGroupId());
                    add(orderedPairs, 140, "creative_id", reportAdvert.getCreativeId());
                    add(orderedPairs, 150, "customer_id", reportAdvert.getCustomerId());
                    add(orderedPairs, 160, "trans_type", reportAdvert.getTransType());
                    add(orderedPairs, 170, "cost", reportAdvert.getCost());
                    add(orderedPairs, 210, "md5_code", MD5.getMD5Str(reportAdvert.getAdZoneId() + profile.get().getAppId() + reportAdvert.getSessionId() + reportAdvert.getCost() + "7eXEa4QOi1JdH3LI"));
                    return orderedPairs;
                });
    }

    private void add(Set<OrderedPair> set, int index, String key, Object value) {
        set.add(new OrderedPair(index, key, value));
    }

    private void remove(Set<OrderedPair> set, String key) {
        set.remove(new OrderedPair(key));
    }

    private Single<String> getParam(String sessionId, String time) {
        return getParamsSet(sessionId, time).map(orderedPairs -> AdParamsUtil.createParamData(orderedPairs));
    }

}
