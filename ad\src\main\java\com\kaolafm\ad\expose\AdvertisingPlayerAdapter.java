package com.kaolafm.ad.expose;

import com.kaolafm.ad.api.model.Advert;
import com.kaolafm.ad.api.model.AudioAdvert;
import com.kaolafm.ad.api.model.AudioImageAdvert;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.error.ApiException;

import javax.inject.Inject;

/**
 * 音频广告的适配类
 * <AUTHOR>
 * @date 2020-02-17
 */
@AppScope
public class AdvertisingPlayerAdapter implements Adapter<AdvertisingPlayer> {


    private AdvertisingPlayer mPlayer;

    @Inject
    AdvertisingPlayerAdapter() {
    }

    @Override
    public boolean accept(Advert advert) {
        return !(advert instanceof AudioImageAdvert) && advert instanceof AudioAdvert;
    }

    @Override
    public void expose(Advert advert) {
        if (mPlayer != null) {
            mPlayer.play((AudioAdvert) advert);
        }
    }

    @Override
    public void close(Advert advert) {
        if (mPlayer != null) {
            mPlayer.stop((AudioAdvert) advert);
        }
    }

    @Override
    public AdvertisingPlayer getExecutor() {
        return mPlayer;
    }

    @Override
    public void setExecutor(AdvertisingPlayer advertisingPlayer) {
        mPlayer = advertisingPlayer;
    }

    @Override
    public void error(String adZoneId, int subtype, ApiException e) {
       if (mPlayer != null) {
           mPlayer.error(adZoneId, subtype, e);
       }
    }
}
