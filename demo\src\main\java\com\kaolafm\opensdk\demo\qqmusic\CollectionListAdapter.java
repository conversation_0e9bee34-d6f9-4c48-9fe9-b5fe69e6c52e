package com.kaolafm.opensdk.demo.qqmusic;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import butterknife.BindView;
import com.bumptech.glide.Glide;
import com.kaolafm.opensdk.api.music.qq.model.Song;
import com.kaolafm.opensdk.demo.BaseAdapter;
import com.kaolafm.opensdk.demo.BaseHolder;
import com.kaolafm.opensdk.demo.R;

/**
 * <AUTHOR>
 * @date 2018/10/26
 */

public class CollectionListAdapter extends BaseAdapter<Song> {

    @Override
    protected BaseHolder<Song> getViewHolder(View view, int viewType) {
        return new CollectionListHolder(view);
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_collection_song;
    }

    static class CollectionListHolder extends BaseHolder<Song> {

        @BindView(R.id.iv_collection_song_item_img)
        ImageView mIvCollectionSongItemImg;

        @BindView(R.id.tv_collection_song_item_des)
        TextView mTvCollectionSongItemDes;

        @BindView(R.id.tv_collection_song_item_title)
        TextView mTvCollectionSongItemTitle;


        public CollectionListHolder(View itemView) {
            super(itemView);
        }

        @Override
        public void setupData(Song song, int position) {
            Glide.with(itemView).load(song.getAlbumPic()).into(mIvCollectionSongItemImg);
            mTvCollectionSongItemTitle.setText(song.getSongName());
            mTvCollectionSongItemDes.setText(song.getAlbumName());
        }
    }
}
