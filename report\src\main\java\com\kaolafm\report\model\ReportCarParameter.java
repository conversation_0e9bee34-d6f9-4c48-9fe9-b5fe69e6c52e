package com.kaolafm.report.model;

import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> on 2019/1/24.
 * 需要外部传入的公共参数
 */

public class ReportCarParameter {

    /**
     * 激活渠道
     */
    private String firstAppId;

    /**
     * 激活渠道类型
     */
    private String marketType;

    /**
     * 激活渠道名称
     */
    private String firstAppIdName;

    /**
     * 合作方式
     */
    private String appIdType;

    /**
     * 车厂
     */
    private String oem;

    /**
     * 品牌
     */
    private String carBrand;

    /**
     * 车型
     */
    private String carType;
    /**
     * 数据上报 timer
     */
    private int timer = ReportConstants.READ_DATE_BASE_TIMER;

    /**
     * 上次获取的时间
     */
    private long systemTime;

    /**
     * 开发者id
     */
    private String developer;


    public int getTimer() {
        return timer;
    }

    public void setTimer(int timer) {
        this.timer = timer;
    }

    public long getSystemTime() {
        return systemTime;
    }

    public void setSystemTime(long systemTime) {
        this.systemTime = systemTime;
    }

    public String getFirstAppId() {
        return firstAppId;
    }

    public void setFirstAppId(String firstAppId) {
        this.firstAppId = firstAppId;
    }

    public String getMarketType() {
        return marketType;
    }

    public void setMarketType(String marketType) {
        this.marketType = marketType;
    }

    public String getFirstAppIdName() {
        return firstAppIdName;
    }

    public void setFirstAppIdName(String firstAppIdName) {
        this.firstAppIdName = firstAppIdName;
    }

    public String getAppIdType() {
        return appIdType;
    }

    public void setAppIdType(String appIdType) {
        this.appIdType = appIdType;
    }

    public String getOem() {
        return oem;
    }

    public void setOem(String oem) {
        this.oem = oem;
    }

    public String getCarBrand() {
        return carBrand;
    }

    public void setCarBrand(String carBrand) {
        this.carBrand = carBrand;
    }

    public String getCarType() {
        return carType;
    }

    public void setCarType(String carType) {
        this.carType = carType;
    }

    public String getDeveloper() {
        return developer;
    }

    public void setDeveloper(String developer) {
        this.developer = developer;
    }
}
