package com.kaolafm.gradle.plugin

import com.android.build.gradle.api.LibraryVariant
import com.jfrog.bintray.gradle.BintrayPlugin
import com.kaolafm.gradle.plugin.model.SDKFlavor
import com.kaolafm.gradle.plugin.model.UploadExtension
import com.kaolafm.gradle.plugin.processors.UploadProcessor
import com.kaolafm.gradle.plugin.processors.VariantProcessor
import com.kaolafm.gradle.plugin.utils.Util
import org.gradle.api.NamedDomainObjectContainer
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.ProjectConfigurationException
import org.gradle.api.artifacts.*
import org.gradle.api.publish.maven.plugins.MavenPublishPlugin

class OnePublishPlugin implements Plugin<Project> {

    private static final String PLUGIN_EXTENSION_NAME = "upload"

    public static final String ARTIFACT_TYPE_AAR = 'aar'

    public static final String ARTIFACT_TYPE_JAR = 'jar'

    private Project mProject

    private Configuration mEmbed

    private Set<ResolvedArtifact> mArtifacts

    private Set<ResolvedDependency> mUnresolvedDependencies

    private HashMap<String, Object> mEmbedDependencies = new HashMap<>()
    private VariantProcessor mProcessor

    @Override
    void apply(Project project) {
        mProject = project
        Util.project = project
        def uploadExtension = project.extensions.create(PLUGIN_EXTENSION_NAME, UploadExtension, project)
        applyPlugin(project)
        createConfiguration()
        project.afterEvaluate {
            resolveArtifacts()
            dealUnresolvedArtifacts(mEmbed)
            project.android.libraryVariants.all { variant ->
                if ("release" == variant.buildType.name) {
                    processVariant(variant)
                }
            }
            processUpload(uploadExtension.getFlavors(), getTaskName())
        }
    }

    private void processUpload(NamedDomainObjectContainer<SDKFlavor> flavors, String taskName) {
        UploadProcessor processor = new UploadProcessor(mProject)
        flavors.all { flavor ->
            //同步的时候taskName为空也需要执行，创建task。不为空时，只执行和task相同的渠道
            if (!taskName || taskName.endsWith(flavor.name.capitalize()) || taskName.startsWith("build${flavor.name.capitalize()}")) {
                def libraryVariant
                mProject.android.libraryVariants.all { variant ->
                    if ("release" == variant.buildType.name) {
                        libraryVariant = variant
                    }
                    //设置是否打包旧SDK
                    variant.buildConfigField("boolean", "INCLUDE_OLD_SDK", "${flavor.includeOldSDK}")
                }
                if (libraryVariant != null) {
                    mProcessor.rebundleAar(flavor)
                    processor.upload(libraryVariant, flavor)
                }
            }
        }
    }

    private void applyPlugin(Project project) {
        project.apply([plugin: 'maven-publish'])
        new BintrayPlugin().apply(project)
        project.plugins.apply(MavenPublishPlugin)
    }

    private String getTaskName() {
        def parameter = mProject.getGradle().getStartParameter()
        if (parameter) {
            def requestList = parameter.getTaskRequests()
            if (requestList && requestList.size() > 0) {
                def args = requestList[0].args
                if (args && args.size() > 0) {
                    return args[0]
                }
            }
        }
        return null
    }

    private void processVariant(LibraryVariant variant) {
        mProcessor = new VariantProcessor(mProject, variant)
        mProcessor.addArtifacts(mArtifacts)
        mProcessor.addUnResolveArtifact(mUnresolvedDependencies)
        mProcessor.processVariant()
    }

    void createConfiguration() {
        mEmbed = getEmbed(mProject)
        mProject.gradle.addListener(new DependencyResolutionListener() {
            @Override
            void beforeResolve(ResolvableDependencies resolvableDependencies) {
                addDependency(mEmbedDependencies, mEmbed)
                mEmbedDependencies.values().each {
                    mProject.dependencies.add('api', it)
                }
                mProject.gradle.removeListener(this)
            }

            @Override
            void afterResolve(ResolvableDependencies resolvableDependencies) {
            }
        })
    }

    private void addDependency(HashMap<String, Object> deps, Configuration embed) {
        embed.dependencies.each { dependency ->
            Object dep
            if (dependency instanceof ProjectDependency) {
                Project module = mProject.project(":${dependency.name}")
                dep = module
                if (!deps.containsKey(dep.toString())) {
                    addDependency(deps, getEmbed(module))
                }
            } else if (dependency instanceof FileCollectionDependency) {
                dep = dependency.files
            } else {
                dep = dependency.name
            }
            deps.put(dep.toString(), dep)

        }
    }

    private Configuration getEmbed(Project project) {
        Configuration embed = project.configurations.maybeCreate("embed")
        if (embed.state == Configuration.State.UNRESOLVED) {
            embed.visible = false
            embed.transitive = false
            embed.canBeResolved = true
        }
        return embed
    }

    private void resolveArtifacts() {
        def set = new HashSet<>()
        mEmbed.resolvedConfiguration.resolvedArtifacts.each { artifact ->
            // jar file wouldn't be here
            if (ARTIFACT_TYPE_AAR == artifact.type || ARTIFACT_TYPE_JAR == artifact.type) {
                println("[embed detected][$artifact.type]$artifact.id.componentIdentifier.displayName")
            } else {
                throw new ProjectConfigurationException('Only support embed aar and jar dependencies!', null)
            }
            set.add(artifact)
        }
        mArtifacts = Collections.unmodifiableSet(set)
    }

    private void dealUnresolvedArtifacts(Configuration embed) {
        def dependencies = Collections.unmodifiableSet(embed.resolvedConfiguration.firstLevelModuleDependencies)
        def dependencySet = new HashSet()
        dependencies.each { dependency ->
            boolean match = false
            mArtifacts.each { artifact ->
                if (dependency.moduleName == artifact.name) {
                    match = true
                }
            }
            if (!match) {
                println('[unResolve dependency detected][' + dependency.name + ']')
                dependencySet.add(dependency)
            }
        }
        mUnresolvedDependencies = Collections.unmodifiableSet(dependencySet)
    }
}