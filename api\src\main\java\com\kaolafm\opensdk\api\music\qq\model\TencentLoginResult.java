package com.kaolafm.opensdk.api.music.qq.model;

import com.google.gson.annotations.SerializedName;
import com.kaolafm.opensdk.api.music.qq.LoginType;

/**
 * 微信/qq,登录QQ音乐返回结果。
 *
 * <AUTHOR>
 * @date 2018/5/1
 */

public class TencentLoginResult extends BaseMusicResult {

    /**
     * refresh_time : 3600
     *  wx_music_openid  : xxxxxxxxxxx
     *  wx_music_unionid  : xxxxxxxxxxxxxx
     */

    /**
     * wx_two_dimension_code :
     */
    /**
     * qr_code :
     */
    /**
     * 二维码图片url
     */
    @SerializedName(value = "wx_two_dimension_code", alternate = {"qr_code"})
    private String qrCode;

    /**
     * 微信扫码登录返回数据
     * music_id : 1152921504712243993
     * music_key : xxxxxxxxxxxxxxxxxxxxx
     * refresh_time : 3600
     * user_info : {"city":"Shenzhen","country":"CN","headimgurl":"xxxxxxxxxxxxxx","nickname":"刘晶","province":"Guangdong","sex":1}
     * wx_access_token : xxxxxx
     * wx_openid : xxxxxxxxxxx
     * wx_refresh_token : xxxxxxxxxxxxxx
     * wx_unionid : xxxxxxxxxxxxxx
     */

    /**
     * QQ扫码登录返回数据
     * expires_in : 7776000
     * is_login_lost : 0
     * qq_open_app_id : 101448240
     * qq_access_token : C54A0BEF1E35D002E2ECEBE3FDB9BAD3
     * qq_openid : BDB28310A1D7960C10F0BF5911A0E12C
     * qq_refresh_token : 7F675798DF4F031BE77CDF8580AB355A
     * user_info : {"city":"深圳","gender":"男","head_img":"http://q.qlogo.cn/qqapp/101448240/BDB28310A1D7960C10F0BF5911A0E12C/100","is_user_info_lost":0,"nickname":"水晶","province":"广东","qzone_img":"http://qzapp.qlogo.cn/qzapp/101448240/BDB28310A1D7960C10F0BF5911A0E12C/100","year":"1987"}
     */

    /**
     * 音乐账号id，微信登录使用
     */
    @SerializedName("music_id")
    private long musicId;

    /**
     * 音乐登录访问凭证，微信登录使用
     */
    @SerializedName("music_key")
    private String musicKey;

    /**
     * 有效时长（秒）
     */
    @SerializedName(value = "refresh_time", alternate = {"expires_in"})
    private long refreshTime;

    @SerializedName("user_info")
    private TencentUserInfo userInfo;

    /**
     * 微信用户接口调用凭证；微信登录不需要，QQ登录要用到
     */
    @SerializedName(value = "wx_access_token", alternate = {"qq_access_token"})
    private String token;

    /**
     * 微信用户授权用户唯一标识；QQ用户登录标识;绑定音乐微信用户授权用户唯一标识
     */
    @SerializedName(value = "wx_openid", alternate = {"qq_openid", "wx_music_openid"})
    private String openId;

    /**
     * 微信用户填写通过wx_access_token获取到的wx_refresh_token参数； QQ用户登录刷新凭证
     */
    @SerializedName(value = "wx_refresh_token", alternate = {"qq_refresh_token"})
    private String refreshToken;

    /**
     * 用户标识，对微信开发者帐号唯一；绑定音乐微信用户标识，对微信开发者帐号唯一
     */
    @SerializedName(value = "wx_unionid", alternate = {"wx_music_unionid"})
    private String unionId;

    /**
     * 音乐open api在开放互联（open.qq.com）注册的appid;
     */
    @SerializedName(value = "qq_open_app_id")
    private String appId;

    /**
     * 表示登录过程是否进行了柔性处理。
     * 0：表示没有。
     * 1：表示有，同时user_info参数中，信息会缺失。业务方可能通过功能三再次获取用户信息。
     * QQ登录返回的数据
     */
    @SerializedName("is_login_lost")
    private int isLoginLost;

    /**
     * 登录方法
     */
    @LoginType
    private int loginType;

    public int getLoginType() {
        return loginType;
    }

    public void setLoginType(@LoginType int loginType) {
        this.loginType = loginType;
    }

    public long getMusicId() {
        return musicId;
    }

    public String getMusicKey() {
        return musicKey;
    }

    public long getRefreshTime() {
        return refreshTime;
    }

    public TencentUserInfo getUserInfo() {
        return userInfo;
    }

    public String getToken() {
        return token;
    }

    public String getOpenId() {
        return openId;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public String getQrCode() {
        return qrCode;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setMusicId(long musicId) {
        this.musicId = musicId;
    }

    public void setMusicKey(String musicKey) {
        this.musicKey = musicKey;
    }

    public void setRefreshTime(long refreshTime) {
        this.refreshTime = refreshTime;
    }

    public void setUserInfo(TencentUserInfo userInfo) {
        this.userInfo = userInfo;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public void setQrCode(String qrCode) {
        this.qrCode = qrCode;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public int getIsLoginLost() {
        return isLoginLost;
    }

    public void setIsLoginLost(int isLoginLost) {
        this.isLoginLost = isLoginLost;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    @Override
    public String toString() {
        return "TencentLoginResult{" +
                ", qrCode='" + qrCode + '\'' +
                ", musicId=" + musicId +
                ", musicKey='" + musicKey + '\'' +
                ", refreshTime=" + refreshTime +
                ", userInfo=" + userInfo +
                ", token='" + token + '\'' +
                ", openId='" + openId + '\'' +
                ", refreshToken='" + refreshToken + '\'' +
                ", unionId='" + unionId + '\'' +
                ", isLoginLost=" + isLoginLost +
                ", loginType=" + loginType +
                '}';
    }
}
