package com.kaolafm.report.event;

import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> on 2019/1/21.
 * 版本变更
 */

public class VersionChangeReportEvent extends BaseReportEventBean {
    /**
     * 升级成功
     */
    public static final String UPDATE_RESULT_SUCCESS = "1";

    /**
     * 状态码
     */
    private String result;
    /**
     * 升级类型
     */
    private String type;
    /**
     * 失败信息
     */
    private String message;
    /**
     * 升级前版本
     */
    private String remarks1;
    /**
     * 目标版本号
     */
    private String remarks2;
    /**
     * 升级结果
     */
    private String remarks3 = UPDATE_RESULT_SUCCESS;

    public VersionChangeReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_VERSION_CHANGE);
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getRemarks1() {
        return remarks1;
    }

    public void setRemarks1(String remarks1) {
        this.remarks1 = remarks1;
    }

    public String getRemarks2() {
        return remarks2;
    }

    public void setRemarks2(String remarks2) {
        this.remarks2 = remarks2;
    }

    public String getRemarks3() {
        return remarks3;
    }

    public void setRemarks3(String remarks3) {
        this.remarks3 = remarks3;
    }
}
