package com.kaolafm.base.internal;

import android.Manifest.permission;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Environment;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import android.text.TextUtils;
import android.util.Pair;

import com.kaolafm.base.utils.FileUtil;
import com.kaolafm.base.utils.SpUtil;

import java.io.File;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 从缓存里获取uuid，文件和sp。这个需要放在列表的第一位，保证可以缓存。
 *
 * <AUTHOR>
 * @date 2019-06-17
 */
public class CacheObtainDeviceId extends BaseObtainDeviceId {

    public static final String KAOLA_AUTO_BASE = "kradio";

    public static final String UDID_PATH_KAOLA_API = "kaolaapi";

    /**
     * 保存的文件名
     */
    public static final String UDID_PATH_UDID_FILE = "udid.dat";

    /**
     * 文件全路径
     */
    private static final String UDID_FILE_PATH = File.separator + KAOLA_AUTO_BASE + File.separator + UDID_PATH_KAOLA_API
            + File.separator + UDID_PATH_UDID_FILE;

    public static final String UDID_PREFERENCE_NAME = "udid_information_sdk";

    public static final String UDID_VALUE = "udid_value";

    /**
     * 来自新创建
     */
    private static final int TYPE_NEW = 0;

    /**
     * 来自sp
     */
    private static final int TYPE_SP = 1;

    /**
     * 来自文件
     */
    private static final int TYPE_FILE = 2;

    /**
     * id来源
     */
    private volatile int mSourceType = TYPE_NEW;

    private Context mContext;
    private Thread mSaveThread;
    private ExecutorService mExecutorService;
    private Runnable mTask;

    public CacheObtainDeviceId() {
    }

    @Override
    protected String createUUID(Context context) {
        mContext = context;
        Pair<String, Boolean> results = null;
        if (TextUtils.isEmpty(mId)) {
            results = getId();
            mId = results.first;
        }
        boolean empty = TextUtils.isEmpty(mId);
        if (!empty) {
            mSourceType = TYPE_SP;
        }
        //sp中没有id或者没有加过密都需要重新读取文件并加密一次
        if (empty || (results != null && !results.second)) {
            File file = Environment.getExternalStorageDirectory();
            if (file != null) {
                if (ActivityCompat.checkSelfPermission(context, permission.READ_EXTERNAL_STORAGE)
                        != PackageManager.PERMISSION_GRANTED) {
                    throw new RuntimeException("请设置读取SD卡的权限");
                }
                mId = FileUtil.readDecryptedFileFromSDCard(file.getAbsolutePath() + UDID_FILE_PATH);
            }
            if (!TextUtils.isEmpty(mId) && empty) {
                mSourceType = TYPE_FILE;
            }
        }
        return mId;
    }

    /**
     * 缓存uuid到内存、sp、文件
     *
     * @param uuid
     * @param index
     */
    @Override
    public void saveUUID(String uuid, int index) {
        super.saveUUID(uuid, index);
        saveUUID(uuid);
    }

    public void saveUUID(String uuid) {
        mId = uuid;
        if (mExecutorService == null) {
            mExecutorService = Executors.newSingleThreadExecutor();
            mTask = () -> {
                if (mSourceType != TYPE_SP) {
                    putId(uuid);
                }
                if (mSourceType != TYPE_FILE) {
                    File file = Environment.getExternalStorageDirectory();
                    if (file != null) {
                        if (ContextCompat.checkSelfPermission(mContext, permission.WRITE_EXTERNAL_STORAGE)
                                != PackageManager.PERMISSION_GRANTED) {
                            throw new RuntimeException("请设置写入SD卡的权限");
                        }
                        FileUtil.writeEncryptedFileToSDCard(file.getAbsolutePath() + UDID_FILE_PATH, uuid);
                    }
                }
            };
        }
        mExecutorService.submit(mTask);
    }

    private void putId(String value) {
        SpUtil.init(mContext, UDID_PREFERENCE_NAME);
        SpUtil.putEncryptedString(UDID_PREFERENCE_NAME, UDID_VALUE, value);
    }

    private Pair<String, Boolean> getId() {
        SpUtil.init(mContext, UDID_PREFERENCE_NAME);
        return SpUtil.getDecryptedString(UDID_PREFERENCE_NAME, UDID_VALUE, null);
    }
}
