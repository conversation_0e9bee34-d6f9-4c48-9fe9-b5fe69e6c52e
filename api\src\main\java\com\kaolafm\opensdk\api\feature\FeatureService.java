package com.kaolafm.opensdk.api.feature;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.feature.model.FeatureAudioDetails;
import com.kaolafm.opensdk.api.feature.model.FeatureDetails;

import java.util.List;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Query;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: AlbumService.java                                               
 *                                                                  *
 * Created in 2023/01/11 下午2:32
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
/*package*/ interface FeatureService {

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_FEATURE_DETAILS)
    Single<BaseResult<FeatureDetails>> getFeatureDetails(@Query("featureId") long featureId);

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_FEATURE_LIST)
    Single<BaseResult<BasePageResult<List<FeatureAudioDetails>>>> getPlaylist(@Query("featureId") long featureId, @Query("sortType") int sortType, @Query("pageSize") int pageSize, @Query("pageNum") int pageNum, @Query("audioid") long audioId);
}