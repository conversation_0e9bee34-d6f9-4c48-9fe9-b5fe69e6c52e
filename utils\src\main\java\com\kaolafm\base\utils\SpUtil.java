package com.kaolafm.base.utils;

import android.app.Application;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;
import android.text.TextUtils;
import android.util.Pair;

import java.util.HashMap;
import java.util.Map;

/**
 * SharedPreferences 工具类。有加密功能
 *
 * <AUTHOR>
 * @date 2020-01-17
 */
public class SpUtil {

    /**
     * 默认sp的名称。初始化时不传入名称，会使用该默认名字
     */
    private static final String DEFAULT_NAME = "utils_default";

    /**
     * 加密标识后缀，由于标记读取时是否是加密后的文件
     */
    private static final String ENCRYPT_FLAG_SUFFIX = "_v2";

    /**
     * 是否是异步提交，true 是异步，使用apply，效率更高，false是同步，使用commit
     */
    private static boolean isAsync = true;

    private static Map<String, SharedPreferences> mSharedPreferencesMap = new HashMap<>();
    private static Context mContext;

    private SpUtil() {
    }

    public static void init(Context context) {
        mContext = context;
        init(context, DEFAULT_NAME);
    }

    public static void init(Context context, String name) {
        if (!mSharedPreferencesMap.containsKey(name)) {
            init(name, getDefaultSharedPreferences(context, name));
        }
    }

    public static void init(String name, SharedPreferences preferences) {
        mSharedPreferencesMap.put(name, preferences);
    }

    private static SharedPreferences getDefaultSharedPreferences(Context context, String name) {
        return context.getSharedPreferences(name, Context.MODE_PRIVATE);
    }

    public static void clear(String spName) {
        apply(getEditor(spName).clear());
    }

    public static void clear() {
        clear(DEFAULT_NAME);
    }

    public static void remove(String spName, String key) {
        apply(getEditor(spName).remove(key));
    }

    /**
     * 移除加密的数据
     * @param spName
     * @param key
     */
    public static void removeEncrypted(String spName, String key) {
        apply(getEditor(spName).remove(key).remove(key+ENCRYPT_FLAG_SUFFIX));
    }

    public static void remove(String key) {
        removeAnyByName(DEFAULT_NAME, key);
    }

    public static void removeAnyByName(String spName, String... keys) {
        Editor edit = getEditor(spName);
        for (String key : keys) {
            edit.remove(key);
        }
        apply(edit);
    }
    public static void removeAnyEncryptedByName(String spName, String... keys) {
        Editor edit = getEditor(spName);
        for (String key : keys) {
            edit.remove(key).remove(key+ENCRYPT_FLAG_SUFFIX);
        }
        apply(edit);
    }

    public static void removeAny(String... keys) {
        removeAnyByName(DEFAULT_NAME, keys);
    }

    public static String removeWith(String spName, String key) {
        SharedPreferences sp = getSharedPreferences(spName);
        String value = sp.getString(key, null);
        apply(sp.edit().remove(key));
        return value;
    }

    public static String removeWith(String key) {
        return removeWith(DEFAULT_NAME, key);
    }

    public static void putBoolean(String spName, String key, boolean value) {
        Editor editor = getEditor(spName);
        editor.putBoolean(key, value);
        apply(editor);
    }

    public static void putBoolean(String key, boolean value) {
        putBoolean(DEFAULT_NAME, key, value);
    }

    public static boolean getBoolean(String spName, String key, boolean defValue) {
        return getSharedPreferences(spName).getBoolean(key, defValue);
    }

    public static boolean getBoolean(String key, boolean defValue) {
        return getBoolean(DEFAULT_NAME, key, defValue);
    }

    public static void putInt(String spName, String key, int value) {
        apply(getEditor(spName).putInt(key, value));
    }

    public static void putInt(String key, int value) {
        putInt(DEFAULT_NAME, key, value);
    }

    public static int getInt(String spName, String key, int defValue) {
        return getSharedPreferences(spName).getInt(key, defValue);
    }

    public static int getInt(String key, int defValue) {
        return getInt(DEFAULT_NAME, key, defValue);
    }

    public static void putFloat(String spName, String key, float value) {
        apply(getEditor(spName).putFloat(key, value));
    }

    public static void putFloat(String key, float value) {
        putFloat(DEFAULT_NAME, key, value);
    }

    public static float getFloat(String spName, String key, float defValue) {
        return getSharedPreferences(spName).getFloat(key, defValue);
    }

    public static float getFloat(String key, float defValue) {
        return getFloat(DEFAULT_NAME, key, defValue);
    }

    public static void putLong(String spName, String key, long value) {
        apply(getEditor(spName).putLong(key, value));
    }

    public static void putLong(String key, long value) {
        putLong(DEFAULT_NAME, key, value);
    }

    public static long getLong(String spName, String key, long defValue) {
        return getSharedPreferences(spName).getLong(key, defValue);
    }

    public static long getLong(String key, long defValue) {
        return getLong(DEFAULT_NAME, key, defValue);
    }

    public static void putString(String spName, String key, String value) {
        apply(getEditor(spName).putString(key, value));
    }

    public static void putString(String key, String value) {
        putString(DEFAULT_NAME, key, value);
    }

    public static String getString(String spName, String key, String defValue) {
        return getSharedPreferences(spName).getString(key, defValue);
    }

    public static String getString(String key, String defValue) {
        return getString(DEFAULT_NAME, key, defValue);
    }

    /**
     * 直接存储加密字符串。
     *
     * 存储的时候给key添加了后缀_v2。
     *
     * @param spName
     * @param key
     * @param value
     */
    public static void putEncryptedString(String spName, String key, String value) {
        putString(spName, key + ENCRYPT_FLAG_SUFFIX, SeCoreUtils.encrypt(value));
    }

    /**
     * 获取解密后字符串，如果未加密就直接返回，并再加密储存一下。
     * 该方法为了兼容以前未加密版本，将key后面追加了 _v2。
     * @param spName
     * @param key
     * @param defValue
     * @return 返回数据对，第一个表示存储的值，第二个表示是否加密过，true表示已经加密，false表示未加密。
     */
    public static Pair<String, Boolean> getDecryptedString(String spName, String key, String defValue) {
        SharedPreferences sp = getSharedPreferences(spName);
        String encryptValue = sp.getString(key + ENCRYPT_FLAG_SUFFIX, defValue);
        String result;
        boolean encrypted;
        //不存在加密文件
        if (TextUtils.isEmpty(encryptValue) || encryptValue.equals(defValue)) {
            String value = sp.getString(key, defValue);
            if (!TextUtils.isEmpty(value)) {
                //保存加密并删除原来的
                Editor edit = sp.edit();
                edit.putString(key + ENCRYPT_FLAG_SUFFIX, SeCoreUtils.encrypt(value));
                edit.remove(key);
                apply(edit);
            }
            result = value;
            encrypted = false;
        }else {
            result = SeCoreUtils.decrypt(encryptValue);
            encrypted = true;
        }
        return new Pair<>(result, encrypted);
    }

    private static Editor getEditor(String spName) {
        return getSharedPreferences(spName).edit();
    }

    private static SharedPreferences getSharedPreferences(String spName) {
        SharedPreferences sharedPreferences = mSharedPreferencesMap.get(spName);
        if (sharedPreferences == null) {
            init(mContext,spName);
            sharedPreferences = getDefaultSharedPreferences(mContext,spName);
//            throw new NullPointerException("没有初始化名称为" + spName + "的SharedPreferences");
        }
        return sharedPreferences;
    }

    private static void apply(Editor editor) {
        if (isAsync) {
            editor.apply();
        } else {
            editor.commit();
        }
    }

}
