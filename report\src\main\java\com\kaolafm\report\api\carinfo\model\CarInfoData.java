package com.kaolafm.report.api.carinfo.model;

/**
 * <AUTHOR> on 2019/2/13.
 * 数据上报信息
 */

public class CarInfoData {

    /**
     * carConfig : {"reportInterval":10}
     * carInfo : {"firstAppId":null,"marketType":"","firstAppIdName":"长城（API）","appIdType":"2","oem":"","carBrand":"","carType":""}
     */

    private CarConfigBean carConfig;
    private CarInfoBean carInfo;

    public CarConfigBean getCarConfig() {
        return carConfig;
    }

    public void setCarConfig(CarConfigBean carConfig) {
        this.carConfig = carConfig;
    }

    public CarInfoBean getCarInfo() {
        return carInfo;
    }

    public void setCarInfo(CarInfoBean carInfo) {
        this.carInfo = carInfo;
    }

    public static class CarConfigBean {
        /**
         * reportInterval : 10
         */

        private int reportInterval;

        public int getReportInterval() {
            return reportInterval;
        }

        public void setReportInterval(int reportInterval) {
            this.reportInterval = reportInterval;
        }
    }

    public static class CarInfoBean {
        /**
         * 激活渠道
         */
        private String firstAppId;

        /**
         * 激活渠道类型
         */
        private String marketType;

        /**
         * 激活渠道名称
         */
        private String firstAppIdName;

        /**
         * 合作方式
         */
        private String appIdType;

        /**
         * 车厂
         */
        private String oem;

        /**
         * 品牌
         */
        private String carBrand;

        /**
         * 车型
         */
        private String carType;

        /**
         * 开发者id
         */
        private String developer;

        public String getFirstAppId() {
            return firstAppId;
        }

        public void setFirstAppId(String firstAppId) {
            this.firstAppId = firstAppId;
        }

        public String getMarketType() {
            return marketType;
        }

        public void setMarketType(String marketType) {
            this.marketType = marketType;
        }

        public String getFirstAppIdName() {
            return firstAppIdName;
        }

        public void setFirstAppIdName(String firstAppIdName) {
            this.firstAppIdName = firstAppIdName;
        }

        public String getAppIdType() {
            return appIdType;
        }

        public void setAppIdType(String appIdType) {
            this.appIdType = appIdType;
        }

        public String getOem() {
            return oem;
        }

        public void setOem(String oem) {
            this.oem = oem;
        }

        public String getCarBrand() {
            return carBrand;
        }

        public void setCarBrand(String carBrand) {
            this.carBrand = carBrand;
        }

        public String getCarType() {
            return carType;
        }

        public void setCarType(String carType) {
            this.carType = carType;
        }

        public String getDeveloper() {
            return developer;
        }

        public void setDeveloper(String developer) {
            this.developer = developer;
        }
    }
}
