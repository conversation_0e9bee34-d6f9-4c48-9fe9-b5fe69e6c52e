package com.kaolafm.opensdk.account.token;

import android.util.Log;

import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.di.component.SessionComponent;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.report.ReportHelper;

import javax.inject.Inject;

/**
 * token 管理类
 *
 * <AUTHOR>
 * @date 2018/7/27
 */
public class AccessTokenManager {

    public static final String TOKEN_QQMUSIC = "token_qq_music";

    private static volatile AccessTokenManager sInstance;

    private static final String TAG = "AccessTokenManager";

    @Inject
    @AppScope
    RealAccessTokenManager mRealManager;

    private AccessTokenManager() {
        SessionComponent subcomponent = null;
        try {
            subcomponent = ComponentKit.getInstance().getSubcomponent();
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, "AccessTokenManager init exception: " + e.getMessage());
        }
        if (subcomponent != null) {
            subcomponent.inject(this);
        }
    }

    public static AccessTokenManager getInstance() {
        if (sInstance == null) {
            synchronized (AccessTokenManager.class) {
                if (sInstance == null) {
                    sInstance = new AccessTokenManager();
                }
            }
        }
        if(sInstance.mRealManager == null){
            sInstance.mRealManager = new RealAccessTokenManager();
        }
        return sInstance;
    }

    /**
     * 退出指定账号。
     *
     * @param type 要退出的账号类型
     */
    public void logout(String type) {
        if (mRealManager != null) {
            mRealManager.logout(type);
        } else {
            Log.e(TAG, "logout failed");
        }
    }

    /**
     * 退出所有账户
     */
    public void logoutAll() {
        if (mRealManager != null) {
            mRealManager.logoutAll();
        } else {
            Log.e(TAG, "logoutAll failed");
        }
    }

    /**
     * 清除所有数据
     */
    public void clearAll() {
        if (mRealManager != null) {
            mRealManager.clearAll();
        } else {
            Log.e(TAG, "clearAll failed");
        }
    }

    /**
     * 清除指定账户数据
     *
     * @param type
     */
    public void clear(String type) {
        if (mRealManager != null) {
            mRealManager.clear(type);
        } else {
            Log.e(TAG, "clearAll failed");
        }
    }


    /**
     * 保存token
     *
     * @param token
     */
    public void setCurrentAccessToken(AccessToken token) {
        if (mRealManager != null) {
            mRealManager.setCurrentAccessToken(token);
        } else {
            Log.e(TAG, "setCurrentAccessToken failed");
        }
    }

    /**
     * 获取指定账号类型的token
     *
     * @param type 账号类型
     * @return
     */
    public <T extends AccessToken> T getCurrentAccessToken(String type) {
        if (mRealManager != null) {
            return mRealManager.getCurrentAccessToken(type);
        } else {
            Log.e(TAG, "getCurrentAccessToken failed");
            return null;
        }
    }

    /**
     * 注册监听token的变化
     *
     * @param observer
     */
    public void registerObserver(TingbanTokenObserver observer) {
        if (mRealManager != null) {
            mRealManager.registerObserver(observer);
        } else {
            Log.e(TAG, "registerObserver failed");
        }
    }

    /**
     * 注销token变化监听
     *
     * @param observer
     */
    public void unregisterObserver(TingbanTokenObserver observer) {
        if (mRealManager != null) {
            mRealManager.unregisterObserver(observer);
        } else {
            Log.e(TAG, "unregisterObserver failed");
        }
    }

    /**
     * 退出考拉账号，这个是退出本地缓存。
     * <br>
     * 如果要取消考拉账号的授权，使用{@link com.kaolafm.opensdk.api.login.LoginRequest#logoutYunting(HttpCallback)}
     */
    public void logoutKaola() {
        Logging.d("退出考拉账号");
        if (mRealManager != null) {
            mRealManager.logout(RealAccessTokenManager.TOKEN_KAOLA);
        } else {
            Log.e(TAG, "logoutKaola failed");
        }
        ReportHelper.getInstance().initUid(null);
    }

    /**
     * 退出QQ音乐账号。
     */
    public void logoutQQMusic() {
        if (mRealManager != null) {
            mRealManager.logout(TOKEN_QQMUSIC);
        } else {
            Log.e(TAG, "logoutQQMusic failed");
        }
    }

    /**
     * 获取考拉账号的Token。其实是K-radio账号的token。
     *
     * @return
     */
    public KaolaAccessToken getKaolaAccessToken() {
        if (mRealManager != null) {
            return mRealManager.getKaolaAccessToken();
        } else {
            Log.e(TAG, "getKaolaAccessToken failed");
            return null;
        }
    }

    /**
     * 获取QQ音乐账号的Token
     *
     * @return
     */
    public QQMusicAccessToken getQQMusicAccessToken() {
        QQMusicAccessToken accessToken = mRealManager.getCurrentAccessToken(TOKEN_QQMUSIC);
        return accessToken != null ? accessToken : new QQMusicAccessToken();
    }
}
