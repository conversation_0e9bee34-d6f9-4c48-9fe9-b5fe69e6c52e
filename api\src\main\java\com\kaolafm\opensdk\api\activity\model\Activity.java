package com.kaolafm.opensdk.api.activity.model;


import android.os.Parcel;
import android.os.Parcelable;

/**
 * 活动信息
 */

public class Activity implements Parcelable {

    /**
     * 活动id
     */
    private Integer id;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动说明
     */
    private String description;

    /**
     * 活动状态 0-已结束，1-进行中
     */
    private Integer status;

    /**
     * 二维码地址
     */
    private String qrCodeUrl;

    /**
     * 二维码下方描述
     */
    private String codeDes;
    /**
     * 音频播放地址
     */
    private String radioUrl;
    /**
     * 视频地址
     */
    private String vedioUrl;
    /**
     * 样式类型
     */
    private String styleType;
    /**
     * 背景地址
     */
    private String backgroundUrl;
    /**
     * 是否显示详情按钮 0-不显示 1-显示
     */
    private int hasButton;
    /**
     * 活动类型
     */
    private int activityType;
    /**
     * 活动开始时间
     */
    private String startTime;
    /**
     * 活动结束时间
     */
    private String endTime;
    /**
     * 活动详情图片
     */
    private String imgUrl;

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getQrCodeUrl() {
        return qrCodeUrl;
    }

    public void setQrCodeUrl(String qrCodeUrl) {
        this.qrCodeUrl = qrCodeUrl;
    }

    public String getCodeDes() {
        return codeDes;
    }

    public void setCodeDes(String codeDes) {
        this.codeDes = codeDes;
    }

    public String getRadioUrl() {
        return radioUrl;
    }

    public void setRadioUrl(String radioUrl) {
        this.radioUrl = radioUrl;
    }

    public String getVedioUrl() {
        return vedioUrl;
    }

    public void setVedioUrl(String vedioUrl) {
        this.vedioUrl = vedioUrl;
    }

    public String getStyleType() {
        return styleType;
    }

    public void setStyleType(String styleType) {
        this.styleType = styleType;
    }

    public String getBackgroundUrl() {
        return backgroundUrl;
    }

    public void setBackgroundUrl(String backgroundUrl) {
        this.backgroundUrl = backgroundUrl;
    }

    public int getHasButton() {
        return hasButton;
    }

    public void setHasButton(int hasButton) {
        this.hasButton = hasButton;
    }

    public int getActivityType() {
        return activityType;
    }

    public void setActivityType(int activityType) {
        this.activityType = activityType;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    @Override
    public String toString() {
        return "Activity{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", status=" + status +
                ", qrCodeUrl='" + qrCodeUrl + '\'' +
                ", codeDes='" + codeDes + '\'' +
                ", radioUrl='" + radioUrl + '\'' +
                ", vedioUrl='" + vedioUrl + '\'' +
                ", styleType='" + styleType + '\'' +
                ", backgroundUrl='" + backgroundUrl + '\'' +
                ", hasButton=" + hasButton +
                ", activityType=" + activityType +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                '}';
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(id);
        dest.writeString(name);
        dest.writeString(description);
        dest.writeInt(status);
        dest.writeString(qrCodeUrl);
        dest.writeString(codeDes);
        dest.writeString(radioUrl);
        dest.writeString(vedioUrl);
        dest.writeString(styleType);
        dest.writeString(backgroundUrl);
        dest.writeInt(hasButton);
        dest.writeInt(activityType);
        dest.writeString(startTime);
        dest.writeString(endTime);
    }

    public Activity(Parcel in) {
        id = in.readInt();
        name = in.readString();
        description = in.readString();
        status = in.readInt();
        qrCodeUrl = in.readString();
        codeDes = in.readString();
        radioUrl = in.readString();
        vedioUrl = in.readString();
        styleType = in.readString();
        backgroundUrl = in.readString();
        hasButton = in.readInt();
        activityType = in.readInt();
        startTime = in.readString();
        endTime = in.readString();
    }


    public static final Creator<Activity> CREATOR = new Creator<Activity>() {
        @Override
        public Activity createFromParcel(Parcel in) {
            return new Activity(in);
        }

        @Override
        public Activity[] newArray(int size) {
            return new Activity[size];
        }
    };
}
