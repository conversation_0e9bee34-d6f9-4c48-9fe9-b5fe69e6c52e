发布OpenSDK版本步骤
1. 解决完相关问题；
2. 调整sdk/maven_push.gradle文件中的upload.sdkFlavors.dev作用域下的versionName
3. Mac 系统下执行./gradlew uploadDev Windows系统下执行gradlew uploadDev 或 选择项目右侧Gradle/KaoLaOpenSdk/Tasks/build-upload/uploadDev
4. 编译opensdk提交到远程库，新增了两个参数dt(域名类型)，av（api版本）。
    dt方便使用不同的域名，默认不写的话是线上环境，其他取值为test和prelease。
    av=v3是api版本，默认不写的话是v3，其他取值为v1和v2。
    repos=pub 推到对外发布仓库：http://iovnexus.radio.cn/#browse/browse:ClientReleases
    否则，推到对内发布仓库：http://iovnexus.radio.cn/#browse/browse:ClientSnapshots，且会自动添加后缀 -SNAPSHOT

    例如./gradlew clean;./gradlew uploadDev -Ddt=test -Dav=v3 -Drepos=pub

    外部仓库：
    内部只读用户和密码：InternalReadonly        rvPy8VXwTr7$ufUw

    内部仓库：
    只读用户和密码：OnlyReader    ewX64rxnDXGK7D6x