package com.kaolafm.opensdk.demo.search;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import butterknife.BindView;
import com.bumptech.glide.Glide;
import com.kaolafm.opensdk.api.search.model.VoiceSearchProgramBean;
import com.kaolafm.opensdk.demo.BaseAdapter;
import com.kaolafm.opensdk.demo.BaseHolder;
import com.kaolafm.opensdk.demo.R;

/**
 * <AUTHOR>
 * @date 2018/8/7
 */

public class SearchResultNewAdapter extends BaseAdapter<VoiceSearchProgramBean> {

    @Override
    protected BaseHolder<VoiceSearchProgramBean> getViewHolder(View view, int viewType) {
        return new SearchResultHolder(view);
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_search_result;
    }

    static class SearchResultHolder extends BaseHolder<VoiceSearchProgramBean> {

        @BindView(R.id.iv_search_result_img)
        ImageView mIvSearchResultImg;

        @BindView(R.id.tv_search_result_album)
        TextView mTvSearchResultAlbum;

        @BindView(R.id.tv_search_result_name)
        TextView mTvSearchResultName;

        public SearchResultHolder(View itemView) {
            super(itemView);
        }

        @Override
        public void setupData(VoiceSearchProgramBean searchProgramBean, int position) {
            Glide.with(itemView).load(searchProgramBean.getImg()).into(mIvSearchResultImg);
            mTvSearchResultName.setText(searchProgramBean.getName());
            mTvSearchResultAlbum.setText(searchProgramBean.getAlbumName());
        }
    }
}
