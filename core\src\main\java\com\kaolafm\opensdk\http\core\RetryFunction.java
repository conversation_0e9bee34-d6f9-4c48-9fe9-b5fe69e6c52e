package com.kaolafm.opensdk.http.core;

import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.http.error.ErrorCode;
import com.kaolafm.opensdk.log.Logging;

import org.reactivestreams.Publisher;

import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

import io.reactivex.Flowable;
import io.reactivex.Single;
import io.reactivex.functions.Function;

/**
 * 用于重试的类
 *
 * <AUTHOR>
 * @date 2020-02-10
 */
public class RetryFunction {

    /**
     * 最大重试次数
     */
    private final int maxRetries = 3;
    /**
     * 重试间隔
     */
    private final int retryDelayMillis = 10_000;

    private int retryCount = 0;

    @Inject
    TokenRefresh mTokenRefresh;

    public Flowable apply(Flowable<Throwable> throwableSingle) {
        return throwableSingle.flatMap((Function<Throwable, Publisher<?>>) throwable -> {
            Logging.d("网络请求出现错误, %s, cause=%s", throwable, throwable.getCause());
            Single single = retry(throwable);
            if (single == null) {
                single = refreshToken(throwable);
            }
            return single == null ? Flowable.error(throwable) : single.toFlowable();
        });
    }

    private Single retry(Throwable throwable) {
        //超时重试
        boolean isRetry = false;
        if (throwable instanceof ApiException) {
            int code = ((ApiException) throwable).getCode();
            if (code == ErrorCode.HTTP_CONNECT_TIMEOUT || code == ErrorCode.HTTP_REQUEST_TIME_OUT) {
                isRetry = true;
            }
        } else if (throwable instanceof ConnectException || throwable instanceof SocketTimeoutException) {
            isRetry = true;
        }
        if (isRetry) {
            if (++retryCount <= maxRetries) {
                return Single.timer(retryDelayMillis, TimeUnit.MILLISECONDS);
            }
        }
        return null;
    }

    private Single refreshToken(Throwable throwable) {
        if (throwable instanceof ApiException) {
            int code = ((ApiException) throwable).getCode();
            if (mTokenRefresh != null) {
                if (code == ErrorCode.TOKEN_EXPIRED) {
                    return mTokenRefresh.refresh();
                } else if (code == ErrorCode.REFRESH_TOKEN_EXPIRED) {
                    mTokenRefresh.logout();
                }
            }
        }
        return null;
    }
}
