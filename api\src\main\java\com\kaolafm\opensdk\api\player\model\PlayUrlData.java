package com.kaolafm.opensdk.api.player.model;

/**
 * <AUTHOR> on 2018/9/18.
 */

public class PlayUrlData {
    private long currentTime;//当前时间
    private long distanceTime;//失效间隔

    private String playUrl;//播放地址
    private String mp3PlayUrl;//mp3播放地址
    private String aacPlayUrl;//aac播放地址
    private String m3u8PlayUrl;//m3u8播放地址

    private String playKey;//播放key
    private String mp3PlayKey;//mp3播放key
    private String aacPlayKey;//aac播放key
    private String m3u8PlayKey;//m3u8播放key

    private long ts;//失效时间戳

    public long getCurrentTime() {
        return currentTime;
    }

    public void setCurrentTime(long currentTime) {
        this.currentTime = currentTime;
    }

    public long getDistanceTime() {
        return distanceTime;
    }

    public void setDistanceTime(long distanceTime) {
        this.distanceTime = distanceTime;
    }

    public String getPlayUrl() {
        return playUrl;
    }

    public void setPlayUrl(String playUrl) {
        this.playUrl = playUrl;
    }

    public String getMp3PlayUrl() {
        return mp3PlayUrl;
    }

    public void setMp3PlayUrl(String mp3PlayUrl) {
        this.mp3PlayUrl = mp3PlayUrl;
    }

    public String getAacPlayUrl() {
        return aacPlayUrl;
    }

    public void setAacPlayUrl(String aacPlayUrl) {
        this.aacPlayUrl = aacPlayUrl;
    }

    public String getM3u8PlayUrl() {
        return m3u8PlayUrl;
    }

    public void setM3u8PlayUrl(String m3u8PlayUrl) {
        this.m3u8PlayUrl = m3u8PlayUrl;
    }

    public String getPlayKey() {
        return playKey;
    }

    public void setPlayKey(String playKey) {
        this.playKey = playKey;
    }

    public String getMp3PlayKey() {
        return mp3PlayKey;
    }

    public void setMp3PlayKey(String mp3PlayKey) {
        this.mp3PlayKey = mp3PlayKey;
    }

    public String getAacPlayKey() {
        return aacPlayKey;
    }

    public void setAacPlayKey(String aacPlayKey) {
        this.aacPlayKey = aacPlayKey;
    }

    public String getM3u8PlayKey() {
        return m3u8PlayKey;
    }

    public void setM3u8PlayKey(String m3u8PlayKey) {
        this.m3u8PlayKey = m3u8PlayKey;
    }

    public long getTs() {
        return ts;
    }

    public void setTs(long ts) {
        this.ts = ts;
    }
}
