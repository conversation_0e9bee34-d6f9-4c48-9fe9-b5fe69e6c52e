package com.kaolafm.opensdk.http.core;

import androidx.annotation.Keep;

import javax.inject.Inject;

import io.reactivex.BackpressureStrategy;
import io.reactivex.Observable;
import io.reactivex.ObservableSource;
import io.reactivex.functions.Function;

/**
 * Observable使用的重试function
 * <AUTHOR>
 * @date 2019/4/10
 */
public class ObservableRetryFunction extends RetryFunction
        implements Function<Observable<Throwable>, ObservableSource<?>> {

    @Inject
    public ObservableRetryFunction() {
    }

    @Keep
    @Override
    public ObservableSource<?> apply(Observable<Throwable> throwableObservable) {
        return super.apply(throwableObservable.toFlowable(BackpressureStrategy.DROP)).toObservable();
    }
}
