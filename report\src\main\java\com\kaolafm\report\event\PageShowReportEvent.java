package com.kaolafm.report.event;

import com.kaolafm.report.util.ReportConstants;

/**
 * 页面曝光上报 290版本新增
 */
public class PageShowReportEvent extends BaseReportEventBean{

    //页面地址
    private String pageid;
    //页面停留时长，单位毫秒
    private String pagetime;

    public PageShowReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_PAGE_SHOW);
    }

    public String getPageId() {
        return pageid;
    }

    public void setPageId(String pageid) {
        this.pageid = pageid;
    }

    public String getPageTime() {
        return pagetime;
    }

    public void setPageTime(String pagetime) {
        this.pagetime = pagetime;
    }
}
