package com.kaolafm.opensdk.api.topic;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.topic.model.OperationResponse;
import com.kaolafm.opensdk.api.topic.model.TopicDetail;
import com.kaolafm.opensdk.api.topic.model.TopicPosts;

import java.util.List;

import io.reactivex.Single;
import okhttp3.RequestBody;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * <AUTHOR>
 * @date 2019-03-28
 */
public interface TopicService {

    /**
     * 获取话题详情
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_TOPIC_BASE_INFO)
    Single<BaseResult<TopicDetail>> getTopicDetail(@Query("topicId") long topicId);

    /**
     * 获取话题详情
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_TOPIC_POSTS_LIST)
    Single<BaseResult<BasePageResult<List<TopicPosts>>>> getTopicPostsList(@Query("topicId") long topicId, @Query("orderWay") int orderWay, @Query("pageNum") int pageNum, @Query("pageSize") int pageSize);


    /**
     * 点赞、取消点赞
     *
     * @param type 0:点赞 1:取消点赞
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @POST(KaolaApiConstant.POST_OPERATE_POSTS)
    Single<BaseResult<OperationResponse>> operatePosts(@Query("postId") long postId, @Query("type") int type);

    /**
     * 发帖
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @POST(KaolaApiConstant.POST_PUBLISH_POSTS)
    Single<BaseResult<OperationResponse>> publishPosts(@Body RequestBody params);

}
