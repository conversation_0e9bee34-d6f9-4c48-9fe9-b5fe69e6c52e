package com.kaolafm.opensdk.player.core.listener;

import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

/******************************************
 * 类描述： 播放状态回调 类名称：IPlayerStateVideoCoreListener
 *
 ******************************************/
public interface IPlayerStateVideoCoreListener {
    void onPlayerVideoRenderingStart(String url);

    void onPlayerVideoSizeChanged(String url, int width, int height);
}
