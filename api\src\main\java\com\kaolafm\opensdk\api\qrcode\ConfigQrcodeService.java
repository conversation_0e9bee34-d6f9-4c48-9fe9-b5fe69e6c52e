package com.kaolafm.opensdk.api.qrcode;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.qrcode.model.ConfigQrcode;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Query;

/**
 * 二维码相关接口
 */
public interface ConfigQrcodeService {


    /**
     * 二维码公共接口
     * @param type(1 云听出行小程序入口二维码图片, 2 积分中心, 3 意见反馈)
     * @return
     */
    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER})
    @GET(KaolaApiConstant.QRCODE_CONFIG)
    Single<BaseResult<ConfigQrcode>> getQrcode(@Query("type") Integer type);

}
