package com.kaolafm.opensdk.player.logic.factory;

import android.util.SparseArray;

import com.kaolafm.opensdk.player.logic.playcontrol.AlbumPlayControl;
import com.kaolafm.opensdk.player.logic.playcontrol.BroadcastPlayControl;
import com.kaolafm.opensdk.player.logic.playcontrol.FeaturePlayControl;
import com.kaolafm.opensdk.player.logic.playcontrol.IPlayControl;
import com.kaolafm.opensdk.player.logic.playcontrol.InfoFragmentPlayControl;
import com.kaolafm.opensdk.player.logic.playcontrol.LivingPlayControl;
import com.kaolafm.opensdk.player.logic.playcontrol.RadioPlayControl;
import com.kaolafm.opensdk.player.logic.playcontrol.TVPlayControl;
import com.kaolafm.opensdk.player.logic.playcontrol.VideoPlayControl;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;


/**
 * <AUTHOR> on 2019-06-19.
 */

public class PlayControlFactory {
    private static IPlayControl mCurrentPlayControl;
    private static SparseArray<IPlayControl> PLAY_CONTROL_ARRAY = new SparseArray<>();

    static {
        // 创建 IPlayControl
        PLAY_CONTROL_ARRAY.put(PlayerConstants.RESOURCES_TYPE_ALBUM, new AlbumPlayControl());
        PLAY_CONTROL_ARRAY.put(PlayerConstants.RESOURCES_TYPE_AUDIO, new AlbumPlayControl());
        PLAY_CONTROL_ARRAY.put(PlayerConstants.RESOURCES_TYPE_INFO_FRAGMENT, new InfoFragmentPlayControl());
        PLAY_CONTROL_ARRAY.put(PlayerConstants.RESOURCES_TYPE_BROADCAST, new BroadcastPlayControl());
        PLAY_CONTROL_ARRAY.put(PlayerConstants.RESOURCES_TYPE_LIVING, new LivingPlayControl());
        PLAY_CONTROL_ARRAY.put(PlayerConstants.RESOURCES_TYPE_TV, new TVPlayControl());
        PLAY_CONTROL_ARRAY.put(PlayerConstants.RESOURCES_TYPE_RADIO, new RadioPlayControl());
        PLAY_CONTROL_ARRAY.put(PlayerConstants.RESOURCES_TYPE_FEATURE, new FeaturePlayControl());
        PLAY_CONTROL_ARRAY.put(PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM, new VideoPlayControl());
        PLAY_CONTROL_ARRAY.put(PlayerConstants.RESOURCES_TYPE_VIDEO_AUDIO, new VideoPlayControl());
    }

    private PlayControlFactory() {
    }

    public static IPlayControl getPlayControl(int type) {
        IPlayControl iPlayControl = PLAY_CONTROL_ARRAY.get(type);
        if (iPlayControl == null) {
            iPlayControl = new AlbumPlayControl();
        }
        if (mCurrentPlayControl != null && mCurrentPlayControl != iPlayControl) {
            mCurrentPlayControl.release();
        }
        mCurrentPlayControl = iPlayControl;
        return iPlayControl;
    }
}
