package com.kaolafm.opensdk.api.login.model;

import com.google.gson.annotations.SerializedName;

/**
 * 成功
 *
 * <AUTHOR>
 * @date 2018/8/2
 */

public class Success {

    /** 获取手机验证码成功，退出登录成功*/
    public static final String CODE_SUCCESS = "20000";

    /** 手机号已经注册*/
    public static final String PHONE_NUMBER_IS_EXIST = "60100";

    /** 手机未注册*/
    public static final String PHONE_NUMBER_IS_NOT_EXIST = CODE_SUCCESS;

    /** 第三方用户的验证通过。*/
    public static final String VERIFICATION_SUCCESS = "10000";

    public static final int STATUS_SUCCESS = 1;

    /** 状态码*/
    @SerializedName(value = "code", alternate = {"status"})
    private String code;

    /** 失败/成功信息*/
    private String msg;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    @Override
    public String toString() {
        return "Success{" +
                "code='" + code + '\'' +
                ", msg='" + msg + '\'' +
                '}';
    }
}
