package com.kaolafm.opensdk.http.socket;

import com.kaolafm.base.utils.ListUtil;

import java.util.LinkedList;
import java.util.Queue;

/**
 * 事件处理类。主要是包装一下事件。
 *
 * <AUTHOR>
 * @date 2020-01-07
 */
public class EventHandler {

    private Emitter emitter;

    private Queue<Handler> handlers;

    public EventHandler(Emitter emitter) {
        this.emitter = emitter;
        handlers = new LinkedList<>();
    }

    public Handler add(String event, Emitter.Listener listener) {
        emitter.on(event, listener);
        Handler handler = () -> emitter.off(event, listener);
        add(handler);
        return handler;
    }

    public EventHandler add(Handler handler) {
        if (handler != null) {
            handlers.add(handler);
        }
        return this;
    }

    public void release() {
        cleanup();
        handlers = null;
        emitter = null;
    }

    public void cleanup() {
        if (!ListUtil.isEmpty(handlers)) {
            Handler handler;
            while ((handler = handlers.poll()) != null) {
                handler.destroy();
            }
        }
    }

    public void remove(String event, Emitter.Listener listener) {
        if (!ListUtil.isEmpty(handlers)) {
            emitter.off(event, listener);
        }
    }

    public interface Handler {
        /**
         * 销毁
         */
        void destroy();
    }
}
