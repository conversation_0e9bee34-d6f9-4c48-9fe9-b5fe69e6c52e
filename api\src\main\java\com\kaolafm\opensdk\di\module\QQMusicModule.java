package com.kaolafm.opensdk.di.module;

import com.kaolafm.base.utils.MD5;
import com.kaolafm.opensdk.account.profile.QQMusicProfileManger;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.account.token.TokenCache;
import com.kaolafm.opensdk.account.token.QQMusicAccessToken;
import com.kaolafm.opensdk.account.token.QQMusicAccessTokenCache;
import com.kaolafm.opensdk.api.music.qq.QQMusicConstant;
import com.kaolafm.opensdk.di.qualifier.AccessTokenQualifier;
import com.kaolafm.opensdk.di.qualifier.QQMusicNewParam;
import com.kaolafm.opensdk.di.qualifier.QQMusicParam;
import com.kaolafm.opensdk.di.qualifier.QQRefreshParam;
import com.kaolafm.opensdk.di.qualifier.QQUserInfoParam;
import com.kaolafm.opensdk.di.qualifier.WeChatRefreshParam;
import com.kaolafm.opensdk.di.scope.AppScope;

import java.util.HashMap;
import java.util.Map;

import dagger.Binds;
import dagger.Module;
import dagger.Provides;
import dagger.multibindings.IntoMap;
import dagger.multibindings.StringKey;

/**
 * QQ音乐的module
 *
 * <AUTHOR> Yan
 * @date 2018/8/10
 */
@Module
public abstract class QQMusicModule {

    @Provides
    @QQMusicParam
    static Map<String, String> provideQQMusicCommonParam(@AppScope QQMusicProfileManger profileManger,
            @QQMusicNewParam Map<String, String> params, @AccessTokenQualifier QQMusicAccessToken accessToken) {
        params.put("app_id", profileManger.getAppId());
        params.put("app_key", profileManger.getAppKey());
        params.put("sign", profileManger.getSign());
        params.put("device_id", profileManger.getDeviceId());
        params.put("client_ip", profileManger.getClientIp());
        params.put("timestamp", profileManger.getTimestamp());
        //登录后token相关参数
        if (accessToken != null && accessToken.isLogin() && accessToken.getLoginType() != -1) {
            params.put("login_type", String.valueOf(QQMusicConstant.LOGIN_TYPE_QQMUSIC));
            int loginType = accessToken.getLoginType();
            params.put("user_login_type", String.valueOf(loginType));
            if (QQMusicConstant.LOGIN_TYPE_QQ == loginType) {
                params.put("open_id", accessToken.getUnionId());
                params.put("open_app_id", accessToken.getOpenId());
                params.put("access_token", accessToken.getToken());
            } else if (QQMusicConstant.LOGIN_TYPE_WECHAT == loginType) {
                params.put("music_id", String.valueOf(accessToken.getMusicId()));
                params.put("music_key", accessToken.getMusicKey());
            }
        }
        return params;
    }

    @Provides
    @WeChatRefreshParam
    static Map<String, String> provideWxRefreshParams(@AccessTokenQualifier QQMusicAccessToken accessToken,
            @QQMusicNewParam Map<String, String> params) {
        params.put("refresh_key", "1");
        params.put("music_id", String.valueOf(accessToken.getMusicId()));
        params.put("music_key", accessToken.getMusicKey());
        params.put("wx_openid", accessToken.getOpenId());
        params.put("wx_refresh_token", accessToken.getRefreshToken());
        params.put("secret", MD5.getMD5Str(accessToken.getMusicId() + "pc_music_wxlogin").toLowerCase());
        return params;
    }

    @Provides
    @QQRefreshParam
    static Map<String, String> provideQQRefreshParams(@AccessTokenQualifier QQMusicAccessToken accessToken,
            @QQMusicNewParam Map<String, String> params) {
        params.put("cmd", "2");
        params.put("qq_access_token", accessToken.getToken());
        params.put("qq_openid", accessToken.getOpenId());
        params.put("qq_refresh_token", accessToken.getRefreshToken());
        params.put("secret",
                MD5.getMD5Str(accessToken.getOpenId() + "music_opi_qqconnet_login").toLowerCase());
        return params;
    }

    @Provides
    @QQUserInfoParam
    static Map<String, String> provideQQUserInfoParams(@AccessTokenQualifier QQMusicAccessToken accessToken,
            @QQMusicNewParam Map<String, String> params) {
        params.put("cmd", "3");
        params.put("qq_access_token", accessToken.getToken());
        params.put("qq_openid", accessToken.getOpenId());
        return params;
    }

    @Provides
    @QQMusicNewParam
    static Map<String, String> provideNewHashMap() {
        return new HashMap<>();
    }

    @Provides
    @AccessTokenQualifier
    static QQMusicAccessToken provideQQMusicAccessToken(@AppScope AccessTokenManager accessTokenManager) {
        return accessTokenManager.getQQMusicAccessToken();
    }

}
