package com.kaolafm.opensdk.api.broadcast;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

public class BroadcastInfo implements Parcelable {
    public List<BroadcastDetails> dataList;
    public List<Long> errorBids;

    protected BroadcastInfo(Parcel in) {
        dataList = in.createTypedArrayList(BroadcastDetails.CREATOR);
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeTypedList(dataList);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<BroadcastInfo> CREATOR = new Creator<BroadcastInfo>() {
        @Override
        public BroadcastInfo createFromParcel(Parcel in) {
            return new BroadcastInfo(in);
        }

        @Override
        public BroadcastInfo[] newArray(int size) {
            return new BroadcastInfo[size];
        }
    };
}
