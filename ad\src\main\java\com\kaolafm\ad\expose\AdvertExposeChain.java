package com.kaolafm.ad.expose;

import com.kaolafm.ad.api.model.Advert;
import com.kaolafm.opensdk.log.Logging;

import java.util.ArrayList;
import java.util.List;

/**
 * 广告曝光的责任链，这里是用来处理拦截器逻辑的。
 * <AUTHOR>
 * @date 2020-03-10
 */
class AdvertExpose<PERSON>hain implements AdvertInterceptor.Chain {

    private List<AdvertInterceptor> mInterceptors;
    private int mIndex;
    private Advert mAdvert;
    private AdvertisingManager mManager;
    private AdvertInterceptor mInterceptor;

    AdvertExposeChain(AdvertisingManager manager) {
        mManager = manager;
        mInterceptors = new ArrayList<>();
        mIndex = 0;
    }

    void addInterceptor(AdvertInterceptor interceptor) {
        mInterceptors.add(interceptor);
    }

    void addInterceptors(List<AdvertInterceptor> interceptors) {
        mInterceptors.addAll(interceptors);
    }

    @Override
    public Advert advert() {
        return mAdvert;
    }

    @Override
    public void process(Advert advert) {
        if (advert == null) {
            Logging.d("曝光的广告为空");
            error(new RuntimeException("曝光的广告为空"));
            return;
        }
        mAdvert = advert;
        if (mIndex >= mInterceptors.size()) {
            error(new IndexOutOfBoundsException("拦截器集合size="+mInterceptors.size()+", index="+mIndex));
            return;
        }
        try {
            mInterceptor = mInterceptors.get(mIndex);
            mIndex++;
            mInterceptor.intercept(this);
        }catch (Exception e) {
            error(new RuntimeException("拦截器<"+mInterceptor+">拦截的时候发生异常", e));
        }
    }

    @Override
    public void error(Exception e) {
        mManager.dispatchAdvertError("", mAdvert != null? mAdvert.getSubtype(): -1, e);
    }
}
