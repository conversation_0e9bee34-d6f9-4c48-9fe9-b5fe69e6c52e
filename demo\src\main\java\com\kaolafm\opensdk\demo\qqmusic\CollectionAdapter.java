package com.kaolafm.opensdk.demo.qqmusic;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import butterknife.BindView;
import com.bumptech.glide.Glide;
import com.kaolafm.opensdk.api.music.qq.model.SongMenu;
import com.kaolafm.opensdk.demo.BaseAdapter;
import com.kaolafm.opensdk.demo.BaseHolder;
import com.kaolafm.opensdk.demo.R;

/**
 * <AUTHOR>
 * @date 2018/10/26
 */

public class CollectionAdapter extends BaseAdapter<SongMenu> {

    @Override
    protected BaseHolder<SongMenu> getViewHolder(View view, int viewType) {
        return new CollectionViewHolder(view);
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_collection;
    }

    static class CollectionViewHolder extends BaseHolder<SongMenu> {

        @BindView(R.id.iv_collection_item_img)
        ImageView mIvCollectionItemImg;

        @BindView(R.id.tv_collection_item_num)
        TextView mTvCollectionItemNum;

        @BindView(R.id.tv_collection_item_des)
        TextView mTvCollectionItemDes;

        @BindView(R.id.tv_collection_item_listen)
        TextView mTvCollectionItemListen;

        @BindView(R.id.tv_collection_item_name)
        TextView mTvCollectionItemName;

        public CollectionViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        public void setupData(SongMenu songMenu, int position) {
            Glide.with(itemView).load(songMenu.getPicUrl()).into(mIvCollectionItemImg);
            mTvCollectionItemNum.setText(songMenu.getSongNum() + "首");
            mTvCollectionItemListen.setText(String.valueOf(songMenu.getListenNum()));
            mTvCollectionItemName.setText(songMenu.getDissName());
            mTvCollectionItemDes.setText(songMenu.getIntroduction());
        }
    }
}
