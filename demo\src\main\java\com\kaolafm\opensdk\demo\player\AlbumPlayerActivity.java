package com.kaolafm.opensdk.demo.player;

import android.os.Bundle;
import android.os.IInterface;
import android.view.View;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.media.AlbumRequest;
import com.kaolafm.opensdk.api.media.model.AlbumDetails;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.api.subscribe.SubscribeRequest;
import com.kaolafm.opensdk.demo.detail.DetailActivity;
import com.kaolafm.opensdk.demo.detail.StringAdapter.Item;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playlist.util.PlayListUtils;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import java.util.ArrayList;
import java.util.List;

/**
 * 专辑播放器页面
 *
 * <AUTHOR> Yan
 * @date 2018/12/7
 */

public class AlbumPlayerActivity extends BasePlayerActivity {

    private boolean isLoadMore = false;
    private int mNextPage = 1;
    private boolean isHaveNext = true;

    @Override
    public void initView(Bundle savedInstanceState) {
        super.initView(savedInstanceState);
        setTitle("专辑播放器页面");
        PlayerManager.getInstance().addPlayControlStateCallback(mPlayerStateListener);
        PlayerManager.getInstance().addPlayListControlStateCallback(mPlayListStateListener);
    }

    @Override
    public void initData() {
        if (mIds.length <= 1) {
            getAlbumDetail(mId);
        } else {
            getAlbumDetails(mIds);
        }
//        getAlbumPlaylist(false);
        getSubscribeState();
        if (PlayerManager.getInstance().getCurPlayItem().getType() == PlayerConstants.RESOURCES_TYPE_ALBUM
                && String.valueOf(mId).equals(PlayerManager.getInstance().getCurPlayItem().getAlbumId())) {
            showListInfo(false, PlayerManager.getInstance().getPlayList());
            select();
        }

        PlayerManager.getInstance().start(new PlayerBuilder().setId(String.valueOf(mId)).setType(PlayerConstants.RESOURCES_TYPE_ALBUM));

    }

    /**
     * 获取订阅状态
     */
    private void getSubscribeState() {
        new SubscribeRequest().isSubscribed(mId, new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                isSubscribed = aBoolean;
                btnSubscribe.setText(aBoolean ? "取消订阅" : "订阅");
                btnSubscribe.setEnabled(true);
                btnSubscribe.setVisibility(View.VISIBLE);
            }

            @Override
            public void onError(ApiException exception) {
                btnSubscribe.setText(exception.getMessage());
            }
        });
    }

    /**
     * 获取专辑播单
     */
    private void getAlbumPlaylist(boolean isLoadMore) {
        play2(isLoadMore);
    }

    /**
     * 第二种播放方式，自己获取播单播放
     */
    private void play2(boolean isLoadMore) {
        if (isLoadMore && !isHaveNext) {
            //如果加载更多没有下一页了就不在请求
            if (mTrfDetailPlaylist != null) {
                mTrfDetailPlaylist.finishLoadmore();
            }
            showToast("没有更多");
            return;
        }
        //直接请求接口获取播单
        new AlbumRequest().getPlaylist(mId, 1, 10, mNextPage,
                new HttpCallback<BasePageResult<List<AudioDetails>>>() {

                    @Override
                    public void onSuccess(BasePageResult<List<AudioDetails>> result) {
                        if (result != null) {
                            isHaveNext = result.getHaveNext() == 1;
                            mNextPage = result.getNextPage();
                            if (!ListUtil.isEmpty(result.getDataList())) {
                                List<PlayItem> playItemList = PlayListUtils.audioDetailToAlbumPlayItem(result.getDataList(), PlayerManager.getInstance().getPlayListInfo());
                                showListInfo(isLoadMore, playItemList);
                            }
                        }
                    }

                    @Override
                    public void onError(ApiException exception) {
                        showError("获取专辑播单错误", exception);
                        if (mTrfDetailPlaylist != null) {
                            mTrfDetailPlaylist.finishLoadmore();
                        }
                    }
                });
    }

    private void showListInfo(boolean isLoadMore, List<PlayItem> playItemList) {
        List<Item> datas = new ArrayList<>();
        if (!ListUtil.isEmpty(playItemList)) {
            for (int i = 0, size = playItemList.size(); i < size; i++) {
                PlayItem item = playItemList.get(i);
                Item sai = new Item();
                sai.id = item.getAudioId();
                sai.type = DetailActivity.TYPE_ALBUM;
                sai.title = item.getTitle();
                sai.details = item.getAlbumTitle();
                sai.audition = item.getAudition();
                sai.fine = item.getFine();
                sai.buyStatus = item.getBuyStatus();
                sai.item = item;
                sai.playItem = item;
                datas.add(sai);
            }
        }

        if (datas.isEmpty()) {
            showToast(isLoadMore ? "没有更多" : "播单列表为空");
        } else {
            if (isLoadMore) {
                mAdapter.addDataList(datas);
            } else {
                mAdapter.setDataList(datas);
            }
        }
        if (mTrfDetailPlaylist != null) {
            mTrfDetailPlaylist.finishLoadmore();
        }
    }

    /**
     * 获取一个专辑详情
     */
    private void getAlbumDetail(long albumId) {
        new AlbumRequest().getAlbumDetails(albumId, new HttpCallback<AlbumDetails>() {
            @Override
            public void onSuccess(AlbumDetails albumDetails) {
                if (albumDetails == null) {
                    return;
                }
                showDetail(albumDetails, albumDetails.getImg());
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取单个专辑详情错误", exception);
            }
        });
    }

    /**
     * 获取多个专辑详情
     */
    private void getAlbumDetails(Long[] albumIds) {
        new AlbumRequest().getAlbumDetails(albumIds, new HttpCallback<List<AlbumDetails>>() {

            @Override
            public void onSuccess(List<AlbumDetails> albumDetails) {
                showDetail(albumDetails, "");
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取多个专辑详情错误", exception);
            }
        });
    }

    @Override
    protected void playPre() {
        PlayerManager.getInstance().playPre();
    }

    @Override
    protected void switchPlayPause() {
        PlayerManager.getInstance().switchPlayerStatus(true);
    }

    @Override
    protected void playNext() {
        PlayerManager.getInstance().playNext();
        //如果是自己请求获取的播单，这里需要自己再请求数据并播放。
    }

    @Override
    protected void refresh() {

    }

    @Override
    protected void loadMore() {
//        getAlbumPlaylist(true);
        isLoadMore = true;
        PlayerManager.getInstance().loadNextPage(new IPlayListGetListener() {
            @Override
            public void onDataGet(PlayItem playItem, List<PlayItem> playItemList) {
                showListInfo(true, playItemList);
            }

            @Override
            public void onDataGetError(PlayItem playItem, int i, int i1) {
                isLoadMore = false;
            }
        });
    }

    @Override
    protected void playItem(Item item) {
        PlayerManager.getInstance().startPlayItemInList(item.playItem, null);
    }

    @Override
    protected void seek(int progress) {
        PlayerManager.getInstance().seek(progress);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        PlayerManager.getInstance().removePlayControlStateCallback(mPlayerStateListener);
        PlayerManager.getInstance().removePlayListControlStateCallback(mPlayListStateListener);
    }


    private IPlayListStateListener mPlayListStateListener = new IPlayListStateListener() {
        @Override
        public void onPlayListChange(List<PlayItem> list) {
            showToast("专辑播单发生变化,list.size = " + list.size());
            if (!isLoadMore) {
                showListInfo(false, list);
            }
            isLoadMore = true;
        }

        @Override
        public void onPlayListChangeError(PlayItem playItem, int i, int i1) {

        }
    };
}
