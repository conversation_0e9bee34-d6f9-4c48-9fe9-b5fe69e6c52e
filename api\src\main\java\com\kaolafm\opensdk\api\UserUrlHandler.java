package com.kaolafm.opensdk.api;

import com.kaolafm.opensdk.account.token.RealAccessTokenManager;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.core.HttpBeforeHandler;

import java.util.HashMap;
import java.util.Map;

import javax.inject.Inject;

import dagger.Lazy;
import okhttp3.HttpUrl;
import okhttp3.Interceptor;
import okhttp3.Request;

/**
 * 订阅和历史在未登录和登录的情况下path不一样，所以需要动态替换
 *
 * <AUTHOR>
 * @date 2020/5/19
 */
@AppScope
public class UserUrlHandler implements HttpBeforeHandler {

    private Map<String, String> mPaths = new HashMap<>();
    private volatile boolean mLogin;
    /**
     * 忽略标记，url中带有该标记的，不做path切换。
     */
    public static final String SWITCH_IGNORE = "#switch_ignore";

    @Inject
    public UserUrlHandler(@AppScope Lazy<RealAccessTokenManager> realAccessTokenManagerLazy) {
        mPaths.put(KaolaApiConstant.KRADIO_USER, KaolaApiConstant.AUTH_USER);
        mPaths.put(KaolaApiConstant.HISTORY_LIST, KaolaApiConstant.LIST_HISTORY_AT_LOGIN);
        mLogin = realAccessTokenManagerLazy.get().getKaolaAccessToken().isLogin();
        realAccessTokenManagerLazy.get().registerObserver(token -> mLogin = token != null && token.isLogin());
    }

    @Override
    public Request onHttpRequestBefore(Interceptor.Chain chain, Request request) {
        if (!mLogin) {
            return request;
        }
        HttpUrl oldHttpUrl = request.url();
        String path = oldHttpUrl.encodedPath();
        if (path.contains(SWITCH_IGNORE)) {
            return pruneIdentification(request.newBuilder(), oldHttpUrl.toString());
        }
        //只有带有指定path的url才进行切换path操作
        if (contains(path)) {
            Request.Builder newBuilder = request.newBuilder();
            for (String s : mPaths.keySet()) {
                path = path.replace(s, mPaths.get(s));
            }
            //移除第一个斜杠。不移除会出现两个斜杠，但是也不影响。
            if (path.startsWith("/")) {
                path = path.substring(1);
            }
            HttpUrl.Builder newUrlBuilder = oldHttpUrl.newBuilder();
            //移除原来的path
            for (int i = 0; i < oldHttpUrl.pathSize(); i++) {
                newUrlBuilder.removePathSegment(0);
            }
            newUrlBuilder.addEncodedPathSegments(path);
            return newBuilder.url(newUrlBuilder.build()).build();
        }

        return request;
    }

    /**
     * 移除url中的忽略标识
     */
    private Request pruneIdentification(Request.Builder newBuilder, String url) {
        String[] strings = url.split(SWITCH_IGNORE);
        StringBuilder sb = new StringBuilder();
        for (String s : strings) {
            sb.append(s);
        }
        return newBuilder.url(sb.toString()).build();
    }
    
    private boolean contains(String path) {
        for (String s : mPaths.keySet()) {
            if (path.contains(s)) {
                return true;
            }
        }
        return false;
    }
}
