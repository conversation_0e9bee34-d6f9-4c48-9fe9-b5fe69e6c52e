package com.kaolafm.opensdk.http.error;

import io.reactivex.Single;
import io.reactivex.SingleSource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/2/27
 */
public final class SingleErrorFunc<E> extends BaseErrorFunc<SingleSource<? extends E>> {

    public SingleErrorFunc(List<ResponseErrorListener> errorListenerList) {
        super(errorListenerList);
    }

    @Override
    SingleSource<? extends E> getError(ApiException e) {
        return Single.error(e);
    }
}
