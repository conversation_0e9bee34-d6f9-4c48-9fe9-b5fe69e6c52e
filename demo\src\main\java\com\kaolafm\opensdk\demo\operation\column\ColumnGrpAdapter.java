package com.kaolafm.opensdk.demo.operation.column;

import android.content.Intent;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import butterknife.BindView;
import butterknife.OnClick;

import com.bumptech.glide.Glide;
import com.kaolafm.opensdk.api.operation.model.ImageFile;
import com.kaolafm.opensdk.api.operation.model.column.Column;
import com.kaolafm.opensdk.api.operation.model.column.ColumnGrp;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;
import com.kaolafm.opensdk.demo.BaseAdapter;
import com.kaolafm.opensdk.demo.BaseHolder;
import com.kaolafm.opensdk.demo.R;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/7/31
 */

public class ColumnGrpAdapter extends BaseAdapter {

    @Override
    protected BaseHolder getViewHolder(View view, int viewType) {
        return new ColumnGrpViewHolder(view);
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_column;
    }

    static class ColumnGrpViewHolder extends BaseHolder {

        @BindView(R.id.iv_columngrp_img)
        ImageView mIvColumngrpImg;

        @BindView(R.id.tv_columnggrp_code)
        TextView mTvColumnggrpCode;

        @BindView(R.id.tv_columngrp_des)
        TextView mTvColumngrpDes;
        @BindView(R.id.tv_columnggrp_des2)
        TextView tv_columnggrp_des2;

        @BindView(R.id.tv_columngrp_subtitle)
        TextView mTvColumngrpSubtitle;

        @BindView(R.id.tv_columngrp_title)
        TextView mTvColumngrpTitle;

        @BindView(R.id.iv_columngrp_more)
        ImageView mIvColumngrpMore;

        @BindView(R.id.tv_columngrp_mark)
        TextView mTvColumngrpMark;

        private ColumnMember mMoreColumnMember;

        @OnClick(R.id.iv_columngrp_more)
        public void onViewClicked() {
            if (mMoreColumnMember != null) {
                Intent intent = new Intent(itemView.getContext(), ColumnDetailActivity.class);
                intent.putExtra(ColumnDetailActivity.KEY_COLUMN_MEMBER, mMoreColumnMember);
                itemView.getContext().startActivity(intent);
            } else {
                Toast.makeText(itemView.getContext(), "没有更多栏目成员", Toast.LENGTH_SHORT).show();
            }
        }

        public ColumnGrpViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        public void setupData(Object o, int position) {
            String code = "", title = "", subtitle = "", des = "", imgUrl = "", recommendReason = "";
            Map<String, ImageFile> imageFiles = null;
            mIvColumngrpMore.setVisibility(View.GONE);
            mTvColumngrpMark.setVisibility(View.GONE);
            tv_columnggrp_des2.setVisibility(View.GONE);
            if (o instanceof Column) {
                Column column = (Column) o;
                mIvColumngrpMore.setVisibility(column.getForwardToMore() == 1 ? View.VISIBLE : View.GONE);
                code = column.getCode();
                title = column.getTitle();
                subtitle = column.getSubtitle();
                des = column.getDescription();
                imageFiles = column.getImageFiles();
                mMoreColumnMember = column.getMoreColumnMember();
            } else if (o instanceof ColumnGrp) {
                ColumnGrp columnGrp = (ColumnGrp) o;
                code = columnGrp.getCode();
                title = columnGrp.getTitle();
                subtitle = columnGrp.getSubtitle();
                des = columnGrp.getDescription();
                imageFiles = columnGrp.getImageFiles();
            } else if (o instanceof ColumnMember) {
                ColumnMember columnMember = (ColumnMember) o;
                Integer cornerMark = columnMember.getCornerMark();
                mTvColumngrpMark.setVisibility(cornerMark != null && cornerMark == 1 ? View.VISIBLE : View.GONE);
                code = columnMember.getCode();
                title = columnMember.getTitle();
                subtitle = columnMember.getSubtitle();
                des = columnMember.getDescription();
                imageFiles = columnMember.getImageFiles();

                tv_columnggrp_des2.setText(columnMember.getRecommendReason());
                tv_columnggrp_des2.setVisibility(View.VISIBLE);
            }
            if (imageFiles != null) {
                ImageFile cover = imageFiles.get(ImageFile.KEY_COVER);
                if (cover != null) {
                    imgUrl = cover.getUrl();
                } else {
                    ImageFile icon = imageFiles.get(ImageFile.KEY_ICON);
                    if (icon != null) {
                        imgUrl = icon.getUrl();
                    }
                }
            }
            Glide.with(itemView).load(imgUrl).into(mIvColumngrpImg);
            mTvColumnggrpCode.setText(code);
            mTvColumngrpTitle.setText(title);
            mTvColumngrpSubtitle.setText(subtitle);
            mTvColumngrpDes.setText(des);

        }
    }
}
