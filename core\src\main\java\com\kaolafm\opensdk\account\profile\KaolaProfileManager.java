package com.kaolafm.opensdk.account.profile;

import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.os.Bundle;
import android.text.TextUtils;

import com.kaolafm.core.BuildConfig;
import com.kaolafm.opensdk.Options;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.log.Logging;

import javax.inject.Inject;

/**
 * KaolaProfile管理类
 *
 * <AUTHOR>
 * @date 2018/7/29
 */
@AppScope
public class KaolaProfileManager extends AbstractProfileManager<KaolaProfile, Options> {

    /**
     * 在AndroidManifest中AppId的key
     */
    private static final String APP_ID_PROPERTY = "com.kaolafm.open.sdk.AppId";

    /**
     * 在AndroidManifest中AppKey的key
     */
    private static final String APP_KEY_PROPERTY = "com.kaolafm.open.sdk.AppKey";

    /**
     * 在AndroidManifest中开启免费内容
     */
    private static final String FREE_CONTENT_PROPERTY = "com.kaolafm.open.sdk.freeContent";

    /**
     * 在AndroidManifest中Channel的key
     */
    private static final String CHANNEL_PROPERTY = "com.kaolafm.open.sdk.Channel";

    /**
     * 在AndroidManifest中配置，是否在网络请求时包名+渠道名，默认不加。
     */
    private static final String IS_PACKAGE_APPEND_SUFFIX = "com.kaolafm.open.sdk.Suffix";

    /**
     * 在AndroidManifest中CarType。默认没有。
     */
    private static final String CAR_TYPE_PROPERTYE = "com.kaolafm.open.sdk.CarType";

    private String mPackageName;

    @Inject
    public KaolaProfileManager() {
    }

    /**
     * 加载当前配置信息
     */
    @Override
    public void loadProfile() {
        getAppInfo();
        super.loadProfile();
    }


    public String getDeviceId() {
        return super.getDeviceId();
    }

    public void setDeviceId(String deviceId) {
        super.setDeviceId(deviceId);
    }

    /**
     * 获取App信息，包名和版本号。
     */
    private void getAppInfo() {
        mPackageName = mApplication.getPackageName();
        mProfile.setPackageName(mPackageName);
        String versionName = options.versionName();
        if (TextUtils.isEmpty(versionName)) {
            PackageManager pm = mApplication.getPackageManager();
            try {
                PackageInfo packageInfo = pm.getPackageInfo(mPackageName, PackageManager.GET_CONFIGURATIONS);
                versionName = packageInfo.versionName;
            } catch (NameNotFoundException e) {
                e.printStackTrace();
                throw new RuntimeException(e);
            }
        }
        mProfile.setVersionName(versionName);
    }

    /**
     * 如果没有设置AppId或AppKey，就从AndroidManifest中获取
     */
    @Override
    protected void loadProfileFromManifest() {
        if (!TextUtils.isEmpty(mProfile.getAppId()) && !TextUtils.isEmpty(mProfile.getAppKey())) {
            return;
        }
        super.loadProfileFromManifest();
    }

    @Override
    protected void setupFromManifest(Bundle metaData) {
        String appId = metaData.getString(APP_ID_PROPERTY);
        String appKey = metaData.getString(APP_KEY_PROPERTY);
        boolean freeContent = metaData.getBoolean(FREE_CONTENT_PROPERTY, false);
        if (TextUtils.isEmpty(appId) || TextUtils.isEmpty(appKey)) {
            Logging.d("没有在AndroidManifest.xml中设置AppId、AppKey");
        }

        String channel = metaData.getString(CHANNEL_PROPERTY);
        String carType = options.carType();
        if (TextUtils.isEmpty(carType)) {
            carType = metaData.getString(CAR_TYPE_PROPERTYE);
        }
        mProfile.setAppId(appId);
        mProfile.setAppKey(appKey);
        mProfile.setChannel(channel);
        mProfile.setCarType(carType);
        boolean isAppend = metaData.getBoolean(IS_PACKAGE_APPEND_SUFFIX);
        if (isAppend && !TextUtils.isEmpty(channel)) {
            mProfile.setPackageName(mPackageName + "." + channel);
        }
        String capabilities = "";
        if (!freeContent) {
            capabilities += "PAY_CONTENT_SUPPORTTED";
        }
        mProfile.setCapabilities(capabilities);
    }
    @Override
    protected void setProfile() {
        super.setProfile();
        mProfile.setDeviceId(getDeviceId());
        mProfile.setSdkVersionName(BuildConfig.VERSION_NAME);
    }

    public void setAppId(String appId) {
        mProfile.setAppId(appId);
    }

    public void setAppKey(String appKey) {
        mProfile.setAppKey(appKey);
    }

    public void setChannel(String channel) {
        mProfile.setChannel(channel);
    }

    public String getAppId() {
        return mProfile.getAppId();
    }

    public String getAppKey() {
        return mProfile.getAppKey();
    }

    public String getChannel() {
        return mProfile.getChannel();
    }

    public String getPackageName() {
        return mProfile.getPackageName();
    }

    public void setPackageName(String packageName) {
        mProfile.setPackageName(packageName);
    }

    public String getCarType() {
        return mProfile.getCarType();
    }
}
