package com.kaolafm.opensdk.api.operation.model.category;


/**
 * 人工运营分类成员:碎片。
 */
public class AudioDetailTbCategoryMember extends CategoryMember {

    private String text;

    private String newsSource;

    private String mp3PlayUrl32;

    private String mp3PlayUrl64;

    private String aacPlayUrl;

    private String aacPlayUrl32;

    private String aacPlayUrl64;

    private String aacPlayUrl128;

    private String aacPlayUrl320;

    private Long aacFileSize;

    private Long mp3FileSize32;

    private Long mp3FileSize64;

    private Long audioId;

    private Long playTimes;

    private Long duration;

    private String updateTime;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getNewsSource() {
        return newsSource;
    }

    public void setNewsSource(String newsSource) {
        this.newsSource = newsSource;
    }

    public String getMp3PlayUrl32() {
        return mp3PlayUrl32;
    }

    public void setMp3PlayUrl32(String mp3PlayUrl32) {
        this.mp3PlayUrl32 = mp3PlayUrl32;
    }

    public String getMp3PlayUrl64() {
        return mp3PlayUrl64;
    }

    public void setMp3PlayUrl64(String mp3PlayUrl64) {
        this.mp3PlayUrl64 = mp3PlayUrl64;
    }

    public String getAacPlayUrl() {
        return aacPlayUrl;
    }

    public void setAacPlayUrl(String aacPlayUrl) {
        this.aacPlayUrl = aacPlayUrl;
    }

    public String getAacPlayUrl32() {
        return aacPlayUrl32;
    }

    public void setAacPlayUrl32(String aacPlayUrl32) {
        this.aacPlayUrl32 = aacPlayUrl32;
    }

    public String getAacPlayUrl64() {
        return aacPlayUrl64;
    }

    public void setAacPlayUrl64(String aacPlayUrl64) {
        this.aacPlayUrl64 = aacPlayUrl64;
    }

    public String getAacPlayUrl128() {
        return aacPlayUrl128;
    }

    public void setAacPlayUrl128(String aacPlayUrl128) {
        this.aacPlayUrl128 = aacPlayUrl128;
    }

    public String getAacPlayUrl320() {
        return aacPlayUrl320;
    }

    public void setAacPlayUrl320(String aacPlayUrl320) {
        this.aacPlayUrl320 = aacPlayUrl320;
    }

    public Long getAacFileSize() {
        return aacFileSize;
    }

    public void setAacFileSize(Long aacFileSize) {
        this.aacFileSize = aacFileSize;
    }

    public Long getMp3FileSize32() {
        return mp3FileSize32;
    }

    public void setMp3FileSize32(Long mp3FileSize32) {
        this.mp3FileSize32 = mp3FileSize32;
    }

    public Long getMp3FileSize64() {
        return mp3FileSize64;
    }

    public void setMp3FileSize64(Long mp3FileSize64) {
        this.mp3FileSize64 = mp3FileSize64;
    }

    public Long getAudioId() {
        return audioId;
    }

    public void setAudioId(Long audioId) {
        this.audioId = audioId;
    }

    public Long getPlayTimes() {
        return playTimes;
    }

    public void setPlayTimes(Long playTimes) {
        this.playTimes = playTimes;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "AudioDetailTbCategoryMember{" +
                "text='" + text + '\'' +
                ", newsSource='" + newsSource + '\'' +
                ", mp3PlayUrl32='" + mp3PlayUrl32 + '\'' +
                ", mp3PlayUrl64='" + mp3PlayUrl64 + '\'' +
                ", aacPlayUrl='" + aacPlayUrl + '\'' +
                ", aacPlayUrl32='" + aacPlayUrl32 + '\'' +
                ", aacPlayUrl64='" + aacPlayUrl64 + '\'' +
                ", aacPlayUrl128='" + aacPlayUrl128 + '\'' +
                ", aacPlayUrl320='" + aacPlayUrl320 + '\'' +
                ", aacFileSize=" + aacFileSize +
                ", mp3FileSize32=" + mp3FileSize32 +
                ", mp3FileSize64=" + mp3FileSize64 +
                ", audioId=" + audioId +
                ", playTimes=" + playTimes +
                ", duration=" + duration +
                ", updateTime='" + updateTime + '\'' +
                '}';
    }
}
