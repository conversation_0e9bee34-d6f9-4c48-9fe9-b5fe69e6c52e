package com.kaolafm.opensdk.di.component;

import com.kaolafm.ad.AdvertOptions;
import com.kaolafm.ad.di.module.AdCommonParamModule;
import com.kaolafm.ad.di.module.AdvertConfigModule;
import com.kaolafm.opensdk.OpenSDKEngine;
import com.kaolafm.opensdk.di.module.AppModule;
import com.kaolafm.opensdk.di.module.CommonParamModule;
import com.kaolafm.opensdk.di.module.HttpClientModule;
import com.kaolafm.opensdk.di.module.HttpConfigModule;
import com.kaolafm.opensdk.di.module.MallModule;
import com.kaolafm.opensdk.di.scope.AppScope;

import dagger.Component;

/**
 * <AUTHOR>
 * @date 2020-03-04
 */
@AppScope
@Component(modules = {
        CommonParamModule.class,
        HttpClientModule.class,
        AppModule.class,
        HttpConfigModule.class,
        AdvertConfigModule.class,
        AdCommonParamModule.class,
        MallModule.class
})
public interface AppComponent extends CoreComponent<AppSubcomponent>, Injector<OpenSDKEngine> {

    @Component.Builder
    interface Builder extends CoreComponent.Builder<AppComponent, AdvertOptions> {

    }
}
