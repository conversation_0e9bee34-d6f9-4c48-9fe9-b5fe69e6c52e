package tv.danmaku.ijk.media.player;

/**
 * 这个类的参数来源
 * android/ijkplayer_android_def.h
 *
 *
 */
public interface IJKConstants {

    // media_event_type
    int MEDIA_NOP               = 0;       // interface test message
    int MEDIA_PREPARED          = 1;
    int MEDIA_PLAYBACK_COMPLETE = 2;
    int MEDIA_BUFFERING_UPDATE  = 3;        // arg1 = percentage, arg2 = cached duration
    int MEDIA_SEEK_COMPLETE     = 4;
    int MEDIA_SET_VIDEO_SIZE    = 5;        // arg1 = width, arg2 = height
    int MEDIA_PLAYING_TIME_UPDATE   = 6;    // arg1 = current position, arg2 = total duration
    int MEDIA_BUFFERING_TIME_UPDATE = 7;    // note : compatible with kaolafm's ijk old versions !
    int MEDIA_PRELOAD_TIME_UPDATE   = 8;    // note : compatible with kaolafm's ijk old versions !
    int MEDIA_GET_IMG_STATE     = 9;        // arg1 = timestamp, arg2 = result code, obj = file name
    int MEDIA_STARTED           = 10;       // no args
    int MEDIA_PAUSED            = 11;       // no args
    int MEDIA_STOPED            = 12;       // no args
    int MEDIA_TIMED_TEXT        = 99;       // not supported yet
    int MEDIA_ERROR             = 100;      // arg1, arg2
    int MEDIA_INFO              = 200;      // arg1, arg2
    int MEDIA_SET_VIDEO_SAR     = 10001;    // arg1 = sar.num, arg2 = sar.den
    int MEDIA_INTERACTION_FIRED = 20001;    // arg1 = current position, arg2 = ad space id

    // media_error_type
    int MEDIA_ERROR_UNKNOWN = 1;
    int MEDIA_ERROR_SERVER_DIED = 100;
    int MEDIA_ERROR_NOT_VALID_FOR_PROGRESSIVE_PLAYBACK = 200;
    int MEDIA_ERROR_IO          = -1004;
    int MEDIA_ERROR_MALFORMED   = -1007;
    int MEDIA_ERROR_UNSUPPORTED = -1010;
    int MEDIA_ERROR_TIMED_OUT   = -110;
    int MEDIA_ERROR_IJK_PLAYER  = -10000;

    // media_info_type
    int MEDIA_INFO_UNKNOWN = 1;
    int MEDIA_INFO_STARTED_AS_NEXT = 2;
    int MEDIA_INFO_VIDEO_RENDERING_START = 3;
    int MEDIA_INFO_VIDEO_TRACK_LAGGING = 700;
    int MEDIA_INFO_BUFFERING_START = 701;
    int MEDIA_INFO_BUFFERING_END = 702;
    int MEDIA_INFO_NETWORK_BANDWIDTH = 703;
    int MEDIA_INFO_BAD_INTERLEAVING = 800;
    int MEDIA_INFO_NOT_SEEKABLE = 801;
    int MEDIA_INFO_METADATA_UPDATE = 802;
    int MEDIA_INFO_TIMED_TEXT_ERROR = 900;
//    int MEDIA_INFO_UNSUPPORTED_SUBTITLE = 901;
//    int MEDIA_INFO_SUBTITLE_TIMED_OUT = 902;

    int MEDIA_INFO_VIDEO_ROTATION_CHANGED = 10001;
    int MEDIA_INFO_AUDIO_RENDERING_START  = 10002;
    int MEDIA_INFO_AUDIO_DECODED_START    = 10003;
    int MEDIA_INFO_VIDEO_DECODED_START    = 10004;
    int MEDIA_INFO_OPEN_INPUT             = 10005;
    int MEDIA_INFO_FIND_STREAM_INFO       = 10006;
    int MEDIA_INFO_COMPONENT_OPEN         = 10007;
    int MEDIA_INFO_VIDEO_SEEK_RENDERING_START = 10008;
    int MEDIA_INFO_AUDIO_SEEK_RENDERING_START = 10009;
    int MEDIA_INFO_MEDIA_ACCURATE_SEEK_COMPLETE = 10100;

    //android_audiotrack.c
    int STREAM_MUSIC = 3;

    //ff_ffplay_def.h
    int MOUDLE_IJK_MEDIA = 0x01;    // IJKPlayer
    int MOUDLE_IJK_LIBAV = 0x02;    // FFmpeg

    /**
     * 下面是云听自己定义的
     */
    //播放器初始化成功
    int MEDIA_IJK_SO_INIT_SUCCESS = 666666;

    int MEDIA_ERROR_NETWORK_FAILED = -1413828334;
    int MEDIA_ERROR_IMMEDIATE_EXIT = -1414092869;
    int MEDIA_AVERROR_HTTP_NOT_FOUND = -875574520;

    //来自底层PLAYER错误码
    int MEDIA_ERROR_IJK_PLAYER_ZERO = 0;

    //没有 ts 文件造成的错误
    int NO_TS_FILE_ERROR_IJK_PLAYER = 400;
    int NO_M3U8_FILE_IJK_PLAYER = 0;

    //播放ID不存在，表示服务器上还没有这个id，表示这个节目从来没有在服务器上出现过
    int NO_ID_SUB_ERROR_IJK_PLAYER = 404;
    int NO_ID_SUB_ERROR_UNKHNOWN_IJK_PLAYER = 403;

    //网关错误。这个错误目前没有找到具体出现的场景
    int BAD_GATEWAY_SUB_ERROR_IJK_PLAYER = 502;

    //播放的文件不存在。是在拿到索引文件后，却拿不到具体的TS文件，这时通常都是么有推流
    int NO_FILE_SUB_ERROR_IJK_PLAYER = 503;

    //域名解析错误
    int DOMAIN_SUB_ERROR_IJK_PLAYER = 700;
    /**
     * 多次请求索引文件相同 多半是服务器没有更新m3u8文件。这个错误码会在连续10次请求到相同索引文件的情况下发出
     */
    int NO_UPDATE_SUB_ERROR_IJK_PLAYER = 701;

    //底层播放器断点重试
    int GET_STREAM_FAILED_SUB_ERROR_IJK_PLAYER = -1;

    //首次开播seek断线重连子错误码
    int SEEK_GET_STREAM_FAILED_SUB_ERROR_IJK_PLAYER = 1;

    //底层播放器因网络断开开始尝试重连
    int GET_STREAM_CONNECTION_SUB_ERROR_IJK_PLAYER = 900;

    //底层播放器已经从断网中恢复
    int GET_STREAM_CONNECTED_SUB_ERROR_IJK_PLAYER = 901;
}
