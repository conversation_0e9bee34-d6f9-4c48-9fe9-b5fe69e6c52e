package com.kaolafm.ad.api.internal.model;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-01-10
 */
public class AdvertisingResult {

    /**
     * adCreative : [{"adzoneId":11,"customerId":11,"campainId":11,"adgroupId":11,"creativeId":11,"mediaId":11,"adType":1,"width":123,"height":456,"imageUrl":"","attachImageUrl":"","audioUrl":"","pvMonitorUrl":"","jumpSeconds":3,"monitorType":2,"imageDuration":2,"duration":2,"attachImageDuration":2,"adPlayTimestamp":["2020-01-03 11:50:00"],"moreInteraction":1,"moreInteractionIcon":"","moreInteractionType":1,"moreInteractionImage":"","moreInteractionDestURL":"","moreInteractionDisplayDuration":1,"moreInteractionIconDisplayOption":1,"moreInteractionText":"","jumpAble":1}]
     * sessionId :
     * transType : 0
     * cost : 0
     * memberId : 0
     * secondaryMemberId :
     * md5Code :
     * appid :
     * originalWidth : 1280
     * originalHeight : 800
     * cb_carousel : 1
     */
    /**广告展示后续动作需要带回的session id*/
    @SerializedName("sessionId")
    private String sessionId;

    /**广告的交易类型：1:display；2:cpm*/
    @SerializedName("transType")
    private int transType;

    /**本次展示消耗的钱或者本次展示带来点击所消耗的钱，单位为分*/
    @SerializedName("cost")
    private int cost;

    /**厂商id*/
    @SerializedName("memberId")
    private int memberId;

    /**二级厂商id*/
    @SerializedName("secondaryMemberId")
    private String secondaryMemberId;

    /**md5加密验证*/
    @SerializedName("md5Code")
    private String md5Code;

    /**应用id*/
    @SerializedName("appid")
    private String appid;

    /**对应请求参数中的宽*/
    @SerializedName("originalWidth")
    private int originalWidth;

    /**对应请求参数中的高*/
    @SerializedName("originalHeight")
    private int originalHeight;

    /**轮播数，广告位维度的数据，再有这个广告位请求的话带回给广告引擎*/
    @SerializedName("cb_carousel")
    private int cbCarousel;

    /**广告列表*/
    @SerializedName("adCreative")
    private List<AdCreative> adCreatives;

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public int getTransType() {
        return transType;
    }

    public void setTransType(int transType) {
        this.transType = transType;
    }

    public int getCost() {
        return cost;
    }

    public void setCost(int cost) {
        this.cost = cost;
    }

    public int getMemberId() {
        return memberId;
    }

    public void setMemberId(int memberId) {
        this.memberId = memberId;
    }

    public String getSecondaryMemberId() {
        return secondaryMemberId;
    }

    public void setSecondaryMemberId(String secondaryMemberId) {
        this.secondaryMemberId = secondaryMemberId;
    }

    public String getMd5Code() {
        return md5Code;
    }

    public void setMd5Code(String md5Code) {
        this.md5Code = md5Code;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public int getOriginalWidth() {
        return originalWidth;
    }

    public void setOriginalWidth(int originalWidth) {
        this.originalWidth = originalWidth;
    }

    public int getOriginalHeight() {
        return originalHeight;
    }

    public void setOriginalHeight(int originalHeight) {
        this.originalHeight = originalHeight;
    }

    public int getCbCarousel() {
        return cbCarousel;
    }

    public void setCbCarousel(int cbCarousel) {
        this.cbCarousel = cbCarousel;
    }

    public List<AdCreative> getAdCreatives() {
        return adCreatives;
    }

    public void setAdCreatives(List<AdCreative> adCreative) {
        this.adCreatives = adCreative;
    }
}
