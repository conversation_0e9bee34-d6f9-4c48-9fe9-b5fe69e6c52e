package com.kaolafm.ad.report.bean;

public class BaseAdEvent {

    private String date;

    private long creativeId;

    private String sessionId;

    private int eventType;

    public long getCreativeId() {
        return creativeId;
    }

    public String getDate() {
        return date;
    }

    public int getEventType() {
        return eventType;
    }

    public void setCreativeId(long creativeId) {
        this.creativeId = creativeId;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public void setEventType(int eventType) {
        this.eventType = eventType;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getSessionId() {
        return sessionId;
    }

    @Override
    public String toString() {
        return "BaseAdEvent{" +
                "date='" + date + '\'' +
                ", creativeId=" + creativeId +
                ", sessionId='" + sessionId + '\'' +
                ", eventType=" + eventType +
                '}';
    }
}
