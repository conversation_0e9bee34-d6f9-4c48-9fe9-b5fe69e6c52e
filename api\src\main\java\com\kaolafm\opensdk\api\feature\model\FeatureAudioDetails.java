package com.kaolafm.opensdk.api.feature.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;
import com.kaolafm.opensdk.api.media.model.AudioFileInfo;
import com.kaolafm.opensdk.api.media.model.Host;

import java.util.List;

public class FeatureAudioDetails implements Parcelable {

    /**
     * id
     */
    @SerializedName("id")
    private long id;

    /**
     * 专题id
     */
    @SerializedName("featureId")
    private long featureId;

    /**
     * 单曲id
     */
    @SerializedName("audioId")
    private long audioId;

    /**
     * 单曲名
     */
    @SerializedName("audioName")
    private String audioName;

    /**
     * 排序序号
     */
    @SerializedName("orderNum")
    private int orderNum;
    /**
     * 专题封面
     */
    @SerializedName("featurePic")
    private String featurePic;
    /**
     * 专题名
     */
    @SerializedName("featureName")
    private String featureName;
    /**
     * 更新时间
     */
    @SerializedName("updateTime")
    private long updateTime;
    /**
     * 图标
     */
    @SerializedName("audioPic")
    private String audioPic;
    /**
     * 描述
     */
    @SerializedName("audioDes")
    private String audioDes;
    /**
     * 收听数
     */
    @SerializedName("listenNum")
    private long listenNum;
    /**
     * 主持人信息
     */
    @SerializedName("host")
    private List<Host> host;

    /**
     * 订阅数
     */
    @SerializedName("subscribeNum")
    private long subscribeNum;

    /**
     * 时长
     */
    @SerializedName("duration")
    private int duration;

    /**
     * 分音质播放地址
     */
    @SerializedName("playInfoList")
    private List<AudioFileInfo> playInfoList;

    /**
     * 是否可以订阅 只有为1的时候不能订阅
     */
    @SerializedName("noSubscribe")
    private int noSubscribe;

    public FeatureAudioDetails() {
    }

    protected FeatureAudioDetails(Parcel in) {
        id = in.readLong();
        featureId = in.readLong();
        audioId = in.readLong();
        audioName = in.readString();
        orderNum = in.readInt();
        audioPic = in.readString();
        audioDes = in.readString();
        listenNum = in.readLong();
        in.readTypedList(host, Host.CREATOR);
        subscribeNum = in.readLong();
        duration = in.readInt();
        in.readTypedList(playInfoList, AudioFileInfo.CREATOR);
        featurePic = in.readString();
        featureName = in.readString();
        updateTime = in.readLong();
        noSubscribe = in.readInt();
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(id);
        dest.writeLong(featureId);
        dest.writeLong(audioId);
        dest.writeString(audioName);
        dest.writeInt(orderNum);
        dest.writeString(audioPic);
        dest.writeString(audioDes);
        dest.writeLong(listenNum);
        dest.writeTypedList(host);
        dest.writeLong(subscribeNum);
        dest.writeInt(duration);
        dest.writeTypedList(playInfoList);
        dest.writeString(featurePic);
        dest.writeString(featureName);
        dest.writeLong(updateTime);
        dest.writeInt(noSubscribe);
    }

    public static final Creator<FeatureAudioDetails> CREATOR = new Creator<FeatureAudioDetails>() {
        @Override
        public FeatureAudioDetails createFromParcel(Parcel source) {
            return new FeatureAudioDetails(source);
        }

        @Override
        public FeatureAudioDetails[] newArray(int size) {
            return new FeatureAudioDetails[size];
        }
    };

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getFeatureId() {
        return featureId;
    }

    public void setFeatureId(long featureId) {
        this.featureId = featureId;
    }

    public long getAudioId() {
        return audioId;
    }

    public void setAudioId(long audioId) {
        this.audioId = audioId;
    }

    public String getAudioName() {
        return audioName;
    }

    public void setAudioName(String audioName) {
        this.audioName = audioName;
    }

    public int getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(int orderNum) {
        this.orderNum = orderNum;
    }

    public String getAudioPic() {
        return audioPic;
    }

    public void setAudioPic(String audioPic) {
        this.audioPic = audioPic;
    }

    public String getAudioDes() {
        return audioDes;
    }

    public void setAudioDes(String audioDes) {
        this.audioDes = audioDes;
    }

    public long getListenNum() {
        return listenNum;
    }

    public void setListenNum(long listenNum) {
        this.listenNum = listenNum;
    }

    public List<Host> getHost() {
        return host;
    }

    public void setHost(List<Host> host) {
        this.host = host;
    }

    public long getSubscribeNum() {
        return subscribeNum;
    }

    public void setSubscribeNum(long subscribeNum) {
        this.subscribeNum = subscribeNum;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public List<AudioFileInfo> getPlayInfoList() {
        return playInfoList;
    }

    public void setPlayInfoList(List<AudioFileInfo> playInfoList) {
        this.playInfoList = playInfoList;
    }

    public String getFeaturePic() {
        return featurePic;
    }

    public void setFeaturePic(String featurePic) {
        this.featurePic = featurePic;
    }

    public String getFeatureName() {
        return featureName;
    }

    public void setFeatureName(String featureName) {
        this.featureName = featureName;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }

    public int getNoSubscribe() {
        return noSubscribe;
    }

    public void setNoSubscribe(int noSubscribe) {
        this.noSubscribe = noSubscribe;
    }

    @Override
    public String toString() {
        return "FeatureAudioDetails{" +
                "id=" + id +
                ", featureId=" + featureId +
                ", audioId=" + audioId +
                ", audioName='" + audioName + '\'' +
                ", orderNum=" + orderNum +
                ", featurePic='" + featurePic + '\'' +
                ", featureName='" + featureName + '\'' +
                ", updateTime=" + updateTime +
                ", audioPic='" + audioPic + '\'' +
                ", audioDes='" + audioDes + '\'' +
                ", listenNum=" + listenNum +
                ", host=" + host +
                ", subscribeNum=" + subscribeNum +
                ", duration=" + duration +
                ", playInfoList=" + playInfoList +
                ", noSubscribe=" + noSubscribe +
                '}';
    }
}
