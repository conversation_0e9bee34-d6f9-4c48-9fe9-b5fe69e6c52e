package com.kaolafm.opensdk.api.feature.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

import java.util.List;

import static com.kaolafm.opensdk.api.media.model.AlbumDetails.REVERSE_DISABLE;


public class FeatureDetails implements Parcelable {
    /**
     * 专题id
     */
    @SerializedName("id")
    private long id;
    /**
     * 状态 0-下线 1-上线
     */
    @SerializedName("status")
    private int status;
    /**
     * 专题名称
     */
    @SerializedName("featureName")
    private String featureName;
    /**
     * 专题英文名称
     */
    @SerializedName("featureEng")
    private String featureEng;
    /**
     * 描述
     */
    @SerializedName("description")
    private String description;
    /**
     * 关键字
     */
    @SerializedName("keyword")
    private List<String> keyword;

    /**
     * 内容分类
     */
    @SerializedName("contentType")
    private String contentType;
    /**
     * 专题封面
     */
    @SerializedName("coverImg")
    private String coverImg;
    /**
     * 推荐语
     */
    @SerializedName("aSentenceRecommend")
    private String aSentenceRecommend;

    /**
     * 收听数
     */
    @SerializedName("listenNum")
    private long listenNum;

    /**
     * 收藏数
     */
    @SerializedName("subscribeNum")
    private long subscribeNum;

    /**
     * 单曲最新更新时间
     */
    @SerializedName("updateTime")
    private long updateTime;

    /**
     * 单曲总数
     */
    @SerializedName("audioTotal")
    private int audioTotal;

    /**
     * 是否显示订阅按钮，0显示，1不显示
     */
    @SerializedName("noSubscribe")
    private int noSubscribe;

    /**
     * 是否支持倒序
     * 0-支持 1-不支持
     */
    @SerializedName("enableReverse")
    private int enableReverse = REVERSE_DISABLE;

    public FeatureDetails() {
    }

    protected FeatureDetails(Parcel in) {
        id = in.readLong();
        status = in.readInt();
        featureName = in.readString();
        featureEng = in.readString();
        description = in.readString();
        keyword = in.createStringArrayList();
        contentType = in.readString();
        coverImg = in.readString();
        aSentenceRecommend = in.readString();
        listenNum = in.readLong();
        subscribeNum = in.readLong();
        updateTime = in.readLong();
        audioTotal = in.readInt();
        noSubscribe = in.readInt();
        enableReverse = in.readInt();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(id);
        dest.writeInt(status);
        dest.writeString(featureName);
        dest.writeString(featureEng);
        dest.writeString(description);
        dest.writeStringList(keyword);
        dest.writeString(contentType);
        dest.writeString(coverImg);
        dest.writeString(aSentenceRecommend);
        dest.writeLong(listenNum);
        dest.writeLong(subscribeNum);
        dest.writeLong(updateTime);
        dest.writeInt(audioTotal);
        dest.writeInt(noSubscribe);
        dest.writeInt(enableReverse);
    }

    @Override
    public String toString() {
        return "FeatureDetails{" +
                "id=" + id +
                ", status=" + status +
                ", featureName='" + featureName + '\'' +
                ", featureEng='" + featureEng + '\'' +
                ", description='" + description + '\'' +
                ", keyword=" + keyword +
                ", contentType='" + contentType + '\'' +
                ", coverImg='" + coverImg + '\'' +
                ", aSentenceRecommend='" + aSentenceRecommend + '\'' +
                ", listenNum=" + listenNum +
                ", subscribeNum=" + subscribeNum +
                ", updateTime=" + updateTime +
                ", audioTotal=" + audioTotal +
                ", noSubscribe=" + noSubscribe +
                ", enableReverse=" + enableReverse +
                '}';
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<FeatureDetails> CREATOR = new Creator<FeatureDetails>() {
        @Override
        public FeatureDetails createFromParcel(Parcel in) {
            return new FeatureDetails(in);
        }

        @Override
        public FeatureDetails[] newArray(int size) {
            return new FeatureDetails[size];
        }
    };

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getFeatureName() {
        return featureName;
    }

    public void setFeatureName(String featureName) {
        this.featureName = featureName;
    }

    public String getFeatureEng() {
        return featureEng;
    }

    public void setFeatureEng(String featureEng) {
        this.featureEng = featureEng;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<String> getKeyword() {
        return keyword;
    }

    public void setKeyword(List<String> keyword) {
        this.keyword = keyword;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getCoverImg() {
        return coverImg;
    }

    public void setCoverImg(String coverImg) {
        this.coverImg = coverImg;
    }

    public String getaSentenceRecommend() {
        return aSentenceRecommend;
    }

    public void setaSentenceRecommend(String aSentenceRecommend) {
        this.aSentenceRecommend = aSentenceRecommend;
    }

    public long getListenNum() {
        return listenNum;
    }

    public void setListenNum(long listenNum) {
        this.listenNum = listenNum;
    }

    public long getSubscribeNum() {
        return subscribeNum;
    }

    public void setSubscribeNum(long subscribeNum) {
        this.subscribeNum = subscribeNum;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }

    public int getAudioTotal() {
        return audioTotal;
    }

    public void setAudioTotal(int audioTotal) {
        this.audioTotal = audioTotal;
    }

    public int getNoSubscribe() {
        return noSubscribe;
    }

    public void setNoSubscribe(int noSubscribe) {
        this.noSubscribe = noSubscribe;
    }

    public int getEnableReverse() {
        return enableReverse;
    }

    public void setEnableReverse(int enableReverse) {
        this.enableReverse = enableReverse;
    }
}
