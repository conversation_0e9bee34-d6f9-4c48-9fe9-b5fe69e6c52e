package com.kaolafm.report.api.report;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.http.core.HttpCallback;

/**
 * 上报开关请求
 */
public class ReportSwitchRequest extends BaseRequest {

    private ReportSwitchServce mReportSwitchServce;

    public ReportSwitchRequest() {
        mReportSwitchServce = obtainRetrofitService(ReportSwitchServce.class);
    }

    /**
     * 检查是否上传大数据中心。
     * @param callback
     */
    public void isUploadBigDatacenter(HttpCallback<ReportSwitchOption> callback) {
        doHttpDeal(mReportSwitchServce.isUploadBigDatacenter(), baseResult -> {
            ReportSwitchOption result = baseResult.getResult();
            return result ;
        }, callback);
    }
}
