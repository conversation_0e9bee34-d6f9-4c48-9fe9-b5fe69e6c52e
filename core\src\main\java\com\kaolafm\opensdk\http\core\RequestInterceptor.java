package com.kaolafm.opensdk.http.core;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.util.Log;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.base.utils.ZipHelper;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.log.LogLevel;
import com.kaolafm.opensdk.log.Logging;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.inject.Inject;
import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.Buffer;
import okio.BufferedSource;

/**
 * 网络拦截器，主要用来打印网络请求和响应数据
 *
 * <AUTHOR>
 * @date 2018/4/18
 */
@AppScope
public class RequestInterceptor implements Interceptor {


    @Inject
    FormatPrinter mPrinter;

    @Inject
    LogLevel.RequestLevel mLogLevel;

    @Inject
    public RequestInterceptor() {}

    @Override
    public Response intercept(@NonNull Chain chain) throws IOException {
        Request request = chain.request();
        boolean logRequest = (mLogLevel == LogLevel.RequestLevel.ALL || mLogLevel == LogLevel.RequestLevel.REQUEST) && Logging.isDebug();
        if (logRequest) {
            //请求数据不为空并且类型是可解析的。
            if (request.body() != null && isParsable(request.body().contentType())) {
                mPrinter.printJsonRequest(request, parseParams(request));
            } else {
                mPrinter.printFileRequest(request);
            }
        }
        boolean logResponse = (mLogLevel == LogLevel.RequestLevel.ALL || mLogLevel == LogLevel.RequestLevel.RESPONSE) && Logging.isDebug();

        long t1 = logResponse ? System.nanoTime() : 0;
        Response originalResponse;
        try {
            originalResponse = chain.proceed(request);
        } catch (Exception e) {
            Log.w("Http Error: ", "intercept: " + e);
            throw e;
        }
        long t2 = logResponse ? System.nanoTime() : 0;
        //打印响应结果
        if (logResponse) {
            ResponseBody responseBody = originalResponse.body();
            String bodyString = null;
            if (responseBody != null && isParsable(responseBody.contentType())) {
                bodyString = printResult(originalResponse);
            }
            final List<String> segmentList = request.url().encodedPathSegments();
            final String header = originalResponse.headers().toString();
            final int code = originalResponse.code();
            final boolean isSuccessful = originalResponse.isSuccessful();
            final String message = originalResponse.message();
            final String url = originalResponse.request().url().toString();

            if (responseBody != null && isParsable(responseBody.contentType())) {
                mPrinter.printJsonResponse(TimeUnit.NANOSECONDS.toMillis(t2 - t1), isSuccessful,
                        code, header, responseBody.contentType(), bodyString, segmentList, message, url);
            } else {
                mPrinter.printFileResponse(TimeUnit.NANOSECONDS.toMillis(t2 - t1),
                        isSuccessful, code, header, segmentList, message, url);
            }

        }
        return originalResponse;
    }

    /**
     * 打印响应结果
     */
    @Nullable
    private String printResult(Response response) throws IOException {
        try {
            //读取服务器返回的结果
            ResponseBody responseBody = response.newBuilder().build().body();
            BufferedSource source = responseBody.source();
            source.request(Long.MAX_VALUE);
            Buffer buffer = source.buffer();

            //获取content的压缩类型
            String encoding = response
                    .headers()
                    .get("Content-Encoding");

            Buffer clone = buffer.clone();

            //解析response content
            return parseContent(responseBody, encoding, clone);
        } catch (IOException e) {
            e.printStackTrace();
            return "{\"error\": \"" + e.getMessage() + "\"}";
        }
    }

    /**
     * 解析服务器响应的内容
     */
    private String parseContent(ResponseBody responseBody, String encoding, Buffer clone) {
        Charset charset = Charset.forName("UTF-8");
        MediaType contentType = responseBody.contentType();
        if (contentType != null) {
            charset = contentType.charset(charset);
        }
        //content使用gzip压缩
        if (encoding != null && "gzip".equalsIgnoreCase(encoding)) {
            //解压
            return ZipHelper.decompressForGzip(clone.readByteArray(), convertCharset(charset));
            //content使用zlib压缩
        } else if (encoding != null && "zlib".equalsIgnoreCase(encoding)) {
            //解压
            return ZipHelper.decompressToStringForZlib(clone.readByteArray(), convertCharset(charset));
        } else {//content没有被压缩
            return clone.readString(charset);
        }
    }

    /**
     * 解析请求服务器的请求参数
     */
    public static String parseParams(Request request) throws UnsupportedEncodingException {
        try {
            RequestBody body = request.newBuilder().build().body();
            if (body == null) {
                return "";
            }
            Buffer requestBuffer = new Buffer();
            body.writeTo(requestBuffer);
            Charset charset = Charset.forName("UTF-8");
            MediaType contentType = body.contentType();
            if (contentType != null) {
                charset = contentType.charset(charset);
            }
            return StringUtil
                    .jsonFormat(URLDecoder.decode(requestBuffer.readString(charset), convertCharset(charset)));
        } catch (IOException e) {
            e.printStackTrace();
            return "{\"error\": \"" + e.getMessage() + "\"}";
        }
    }

    /**
     * 是否可以解析
     */
    public static boolean isParsable(MediaType mediaType) {
        return isText(mediaType) || isPlain(mediaType)
                || isJson(mediaType) || isForm(mediaType)
                || isHtml(mediaType) || isXml(mediaType);
    }

    public static boolean isText(MediaType mediaType) {
        if (mediaType == null || mediaType.type() == null) {
            return false;
        }
        return mediaType.type().equals("text");
    }

    public static boolean isPlain(MediaType mediaType) {
        if (mediaType == null || mediaType.subtype() == null) {
            return false;
        }
        return mediaType.subtype().toLowerCase().contains("plain");
    }

    public static boolean isJson(MediaType mediaType) {
        if (mediaType == null || mediaType.subtype() == null) {
            return false;
        }
        return mediaType.subtype().toLowerCase().contains("json");
    }

    public static boolean isXml(MediaType mediaType) {
        if (mediaType == null || mediaType.subtype() == null) {
            return false;
        }
        return mediaType.subtype().toLowerCase().contains("xml");
    }

    public static boolean isHtml(MediaType mediaType) {
        if (mediaType == null || mediaType.subtype() == null) {
            return false;
        }
        return mediaType.subtype().toLowerCase().contains("html");
    }

    public static boolean isForm(MediaType mediaType) {
        if (mediaType == null || mediaType.subtype() == null) {
            return false;
        }
        return mediaType.subtype().toLowerCase().contains("x-www-form-urlencoded");
    }

    public static String convertCharset(Charset charset) {
        String s = charset.toString();
        int i = s.indexOf("[");
        if (i == -1) {
            return s;
        }
        return s.substring(i + 1, s.length() - 1);
    }
}
