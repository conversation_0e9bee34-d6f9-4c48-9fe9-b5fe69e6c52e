package com.kaolafm.opensdk.http.download.engine;

import android.util.Pair;

import com.kaolafm.base.utils.FileUtil;
import com.kaolafm.opensdk.http.download.DownloadRequest;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.opensdk.utils.HttpUtil;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import okio.Buffer;
import okio.BufferedSink;
import okio.BufferedSource;
import okio.ByteString;
import okio.Okio;
import okio.Sink;
import okio.Source;
import retrofit2.Response;

/**
 * 断点下载临时文件逻辑
 *
 * <AUTHOR>
 * @date 2020-02-11
 */
public class RangeTempHelper {

    private static final String FILE_HEADER_MAGIC_NUMBER = "a1b2c3d4e5f6";

    private static final long FILE_HEADER_MAGIC_NUMBER_SIZE = 6L;

    private static final long FILE_HEADER_SIZE = FILE_HEADER_MAGIC_NUMBER_SIZE + 16L;

    private static final long RANGE_SIZE = 5 * 1024 * 1024;

    private File mTmp;
    private long mTotalSize;
    private long mSliceCount;

    private List<Segment> mSegments = new ArrayList<>();

    public RangeTempHelper(File tmp) {
        mTmp = tmp;
    }

    public void write(Response response, DownloadRequest request) {
        long totalSize = HttpUtil.getContentLength(response);
        long sliceCount = HttpUtil.getSliceCount(response, RANGE_SIZE);
        Sink sink = null;
        BufferedSink buffer = null;
        try {
            sink = Okio.sink(mTmp);
            buffer = Okio.buffer(sink);
            writeHeader(buffer, totalSize, sliceCount);
            writeContent(buffer, totalSize, sliceCount, RANGE_SIZE);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } finally {
            FileUtil.closeIO(sink, buffer);
        }
    }

    private void writeHeader(BufferedSink sink, long totalSize, long sliceCount) {
        mTotalSize = totalSize;
        mSliceCount = sliceCount;
        try {
            sink.write(ByteString.decodeHex(FILE_HEADER_MAGIC_NUMBER));
            sink.writeLong(totalSize);
            sink.writeLong(sliceCount);
            sink.flush();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void writeContent(BufferedSink buffer, long totalSize, long sliceCount, long rangeSize) {
        mSegments.clear();
        long start = 0L;
        for (int i = 0; i < sliceCount; i++) {
            long end = (i == sliceCount - 1) ? totalSize - 1 : start + rangeSize - 1;
            mSegments.add(new Segment(i, start, start, end));
            start += rangeSize;
        }
        for (Segment segment : mSegments) {
            segment.write(buffer);
        }
    }

    public boolean read(Response response, DownloadRequest request) {
        long totalSize = HttpUtil.getContentLength(response);
        long sliceCount = HttpUtil.getSliceCount(response, RANGE_SIZE);
        Source source = null;
        BufferedSource bufferedSource = null;
        try {
            source = Okio.source(mTmp);
            bufferedSource = Okio.buffer(source);
            readHeader(bufferedSource);
            readContent(bufferedSource, sliceCount);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } finally {
            FileUtil.closeIO(source, bufferedSource);
        }
        return check(totalSize, sliceCount);
    }

    private boolean check(long totalSize, long sliceCount) {
        return mTotalSize == totalSize && mSliceCount == sliceCount;
    }

    private void readContent(BufferedSource bufferedSource, long sliceCount) {
        mSegments.clear();
        for (int i = 0; i < sliceCount; i++) {
            mSegments.add(new Segment().read(bufferedSource));
        }
    }

    private void readHeader(BufferedSource bufferedSource) {
        try {
            String header = bufferedSource.readByteString(FILE_HEADER_MAGIC_NUMBER_SIZE).hex();
            if (!FILE_HEADER_MAGIC_NUMBER.equals(header)) {
                Logging.d("this is not a temp file, create new.");
                return;
            }
            mTotalSize = bufferedSource.readLong();
            mSliceCount = bufferedSource.readLong();
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    /**
     * 上一次的进度，第一次是已下载的大小，第二个是总大小。
     *
     * @return
     */
    Pair<Long, Long> lastProgress() {
        return new Pair<>(downloadSize(), mTotalSize);
    }

    private long downloadSize() {
        long downloadSize = 0;
        for (Segment segment : mSegments) {
            downloadSize += segment.completeSize();
        }
        return downloadSize;
    }

    List<Segment> getSegments() {
        return mSegments;
    }

    /**
     * 片段信息
     */
    class Segment {

        static final long SEGMENT_SIZE = 32L;
        long index;
        long start;
        long current;
        long end;

        public Segment() {
        }

        public Segment(long index, long start, long current, long end) {
            this.index = index;
            this.start = start;
            this.current = current;
            this.end = end;
        }

        public void write(BufferedSink sink) {
            try {
                sink.writeLong(index);
                sink.writeLong(start);
                sink.writeLong(current);
                sink.writeLong(end);
                sink.flush();
            } catch (IOException e) {
                e.printStackTrace();
            }

        }

        public Segment read(BufferedSource source) {
            Buffer buffer = new Buffer();
            try {
                source.readFully(buffer, SEGMENT_SIZE);
                index = buffer.readLong();
                start = buffer.readLong();
                current = buffer.readLong();
                end = buffer.readLong();
            } catch (IOException e) {
                e.printStackTrace();
            }
            return this;
        }

        /**
         * 该片段是否已完成
         *
         * @return
         */
        public boolean isComplete() {
            return (current - end) == 1;
        }

        /**
         * 已下载完成大小
         *
         * @return
         */
        public long completeSize() {
            return current - start;
        }

        /**
         * 开始位置大小
         *
         * @return
         */
        public long startByte() {
            return FILE_HEADER_SIZE + SEGMENT_SIZE * index;
        }

        /**
         * 剩余未下载文件大小
         *
         * @return
         */
        public long remainSize() {
            return end - current + 1;
        }
    }
}
