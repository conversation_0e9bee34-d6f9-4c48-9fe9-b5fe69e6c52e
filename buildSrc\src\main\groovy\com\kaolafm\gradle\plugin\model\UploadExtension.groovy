package com.kaolafm.gradle.plugin.model

import org.gradle.api.Action
import org.gradle.api.NamedDomainObjectContainer
import org.gradle.api.Project
/**
 * <AUTHOR>
 * @date 2019-06-07
 */
class UploadExtension {

    private NamedDomainObjectContainer<SDKFlavor> flavors

    UploadExtension(Project project) {
        flavors = project.container(SDKFlavor)
    }

    public void sdkFlavors(Action<? super NamedDomainObjectContainer<SDKFlavor>> action) {
        action.execute(flavors)
    }

    NamedDomainObjectContainer<SDKFlavor> getFlavors() {
        return flavors
    }

    public void sdkFlavor(Action<SDKFlavor> action) {
        def flavor = new SDKFlavor("release")
        flavors.add(flavor)
        action.execute(flavor)
    }


}
