package com.kaolafm.opensdk.api;


import java.io.Serializable;
import java.util.Objects;

public class CrashMessageBaseBean implements Serializable {
    //    {
//        "result": {
//        "headline": "正安县气象台解除大雾橙色预警[II级/严重]",
//                "eventDescription": "正安县气象台4月26日6时52分发布大雾橙色预警信号：预计未来6小时我县正习高速、银白高速、凤仪等中、高海拔乡镇部分路段将出现能见度小于200米的大雾，请注意交通安全。",
//                "headlinePath": "mz/audios/202206/987efb5c-9f7c-4289-b242-1a21d1b2cdc3.mp3",
//                "eventDescriptionPath": "mz/audios/202206/2ce8192b-f795-40e9-8e72-3d7778fe05de.mp3",
//                "sendTime": "2022-08-02 18:30:00",
//                "playType": "0",
//                "msgLevel": "3",
//                "tipsTitle": "国家应急广播",
//                "msgDetailsStartTime": "2022-08-01 18:30:00",
//                "msgDetailsEndTime": "2022-08-03 18:30:00",
//                "msgDetailsBgUrl": "消息详情背景图片地址",
//                "msgDetailsQrUrl": "消息详情二维码地址",
//                "msgDetailsBtbStyleLeft": 1,
//                "msgDetailsBtbStyleRight": 1,
//                "msgDetailsBtnTextLeft": "左侧按钮文字",
//                "msgDetailsBtnTextRight": "右侧按钮文字",
//                "msgDetailsBtnActionLeft": "左侧按钮行为",
//                "msgDetailsBtnActionRight": "右侧按钮行为",
//                "emergencyId": "1518847539976286208",
//                "publishTime": "2022-04-26 14:58:35",
//                "sender": "正安县气象台",
//                "eventType": "01",
//                "eventLevel": "02"
//    },
//        "requestId": "",
//            "serverTime": "1659436200000"
//    }


    /**
     * 消息id
     */
    private String msgId;

    /**
     * 应急广播标题文字
     */
    private String headline;
    /**
     * 应急广播正文文字
     */
    private String eventDescription;
    /**
     * 应急广播正文加工音频地址
     */
    private String eventDescriptionPath;
    /**
     * 消息发送时间，时间戳,到毫秒
     */
    private String sendTime;

    /**
     * 插播类型 0-立即插播  1-延时插播
     */
    private String playType;
    /**
     * 消息等级 一共三级
     * 1-情感化问候、车载服务类消息 2-节目预约、社群等主动交互类消息 3-云听应急广播消息
     *
     * V3.0 一期 改动：1级最紧急
     */
    private String msgLevel;
    /**
     * 1级消息: 4.本地服务消息 5.活动运营相关消息  6.收听助手相关消息
     * 2级消息: 1.AI路况消息 2.AI气象消息 3.出行服务及电商消息
     * 3级消息: 0.应急播报
     */
    private String msgContentType;
    private String tipsTitle; //消息弹窗提示标题（如疫情消息，国家应急广播，节日提醒等）
    private String msgDetailsStartTime;//活动开始时间 时间戳,到毫秒
    private String msgDetailsEndTime;//活动结束时间 时间戳,到毫秒
    private String msgDetailsBgUrl;//消息详情背景图片地址
    private String msgDetailsQrUrl;//消息详情二维码地址
    private String msgDetailsBtnTextLeft;//左侧按钮文字
    private String msgDetailsBtnTextRight;//右侧按钮文字
    private CrashMessageButtonActionBean msgDetailsBtnActionLeft;//左侧按钮行为
    private CrashMessageButtonActionBean msgDetailsBtnActionRight;//右侧按钮行为
//    应急广播相关:
    /**
     * 应急广播id
     */
    private String emergencyId;
    /**
     * 应急广播发布时间 时间戳,到毫秒
     */
    private String publishTime;
    /**
     * 应急广播发布机构名称
     */
    private String sender;
    /**
     * 应急广播灾害类型
     */
    private String eventType;
    /**
     * 应急广播预警等级:1.红色预警 2.橙色预警 3.黄色预警 4.蓝色预警
     */
    private String eventLevel;
    /**
     * 应急消息提示图片
     * 消息来源机构的图片url，如国家应急广播的图片url
     */
    private String msgTipsPicUrl;
    /**
     * 一句话简介
     */
    private String eventDescriptionExtract;
    /**
     * 消息泡泡小卡片背景图
     */
    private String cardBgUrl;
    /**
     * 综合版消息泡泡小卡片背景图
     */
    private String comprehensiveCardBgUrl;
    /**
     * 0-文 1-文+图 2-文+按钮 3-文+图+按钮
     */
    private int msgStyleType;

    private String msgIconUrl;

    private CrashMessageButtonActionBean msgBubbleBtnActionLeft;
    private CrashMessageButtonActionBean msgBubbleBtnActionRight;
    private String headlinePath;

    /**
     * 如果msgType = 01 应急广播
     * 如果msgType = 02 活动
     * 如果msgType = 03 内容
     * 如果msgType = 04 广播
     * 如果msgType = 05 直播
     */
    private String msgType;
    private long startTime;
    private String msgPicUrl;

    private String msgDetailsPicTips;

    /**活动起止时间，逗号分割*/
    private String msgTime;

    private String msgQrUrl;

    private boolean terminatePlayMsg;
    public String getMsgQrUrl(){
        return msgQrUrl;
    }
    public void setMsgQrUrl(String msgQrUrl){
        this.msgQrUrl = msgQrUrl;
    }

    public String getMsgTime() {
        return msgTime;
    }

    public void setMsgTime(String msgTime) {
        this.msgTime = msgTime;
    }

    public CrashMessageTypeDataBean getTypeData() {
        return typeData;
    }

    public void setTypeData(CrashMessageTypeDataBean typeData) {
        this.typeData = typeData;
    }

    private CrashMessageTypeDataBean typeData;

    public String getMsgDetailsPicTips(){
        return this.msgDetailsPicTips;
    }

    public void setMsgDetailsPicTips(String msgDetailsPicTips){
        this.msgDetailsPicTips = msgDetailsPicTips;
    }

    public String getMsgPicUrl() {
        return msgPicUrl;
    }

    public void setMsgPicUrl(String msgPicUrl) {
        this.msgPicUrl = msgPicUrl;
    }

    public String getMsgBubbleBtnTextLeft() {
        return msgBubbleBtnTextLeft;
    }

    public void setMsgBubbleBtnTextLeft(String msgBubbleBtnTextLeft) {
        this.msgBubbleBtnTextLeft = msgBubbleBtnTextLeft;
    }

    public String getMsgBubbleBtnTextRight() {
        return msgBubbleBtnTextRight;
    }

    public void setMsgBubbleBtnTextRight(String msgBubbleBtnTextRight) {
        this.msgBubbleBtnTextRight = msgBubbleBtnTextRight;
    }

    public String getMsgDetailsPicUrl() {
        return msgDetailsPicUrl;
    }

    public void setMsgDetailsPicUrl(String msgDetailsPicUrl) {
        this.msgDetailsPicUrl = msgDetailsPicUrl;
    }

    private String msgBubbleBtnTextLeft;
    private String msgBubbleBtnTextRight;
    private String msgDetailsPicUrl;


    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    private long endTime;

    public String getHeadlinePath(){
        return this.headlinePath;
    }

    public void setHeadlinePath(String headlinePath){
        this.headlinePath = headlinePath;
    }


    public String getMsgType(){
        return this.msgType;
    }

    public void setMsgType(String msgType){
        this.msgType = msgType;
    }

    public void setMsgBubbleBtnActionLeft(CrashMessageButtonActionBean bean){
        this.msgBubbleBtnActionLeft = bean;
    }
    public CrashMessageButtonActionBean getMsgBubbleBtnActionLeft(){
        return this.msgBubbleBtnActionLeft;
    }
    public void setMsgBubbleBtnActionRight(CrashMessageButtonActionBean bean){
        this.msgBubbleBtnActionRight = bean;
    }
    public CrashMessageButtonActionBean getMsgBubbleBtnActionRight(){
        return this.msgBubbleBtnActionRight;
    }

    public void setMsgIconUrl(String msgIconUrl){
        this.msgIconUrl = msgIconUrl;
    }
    public String getMsgIconUrl(){
        return this.msgIconUrl;
    }

    public String getComprehensiveCardBgUrl() {
        return comprehensiveCardBgUrl;
    }

    public void setComprehensiveCardBgUrl(String comprehensiveCardBgUrl) {
        this.comprehensiveCardBgUrl = comprehensiveCardBgUrl;
    }

    public String getCardBgUrl() {
        return cardBgUrl;
    }

    public void setCardBgUrl(String cardBgUrl) {
        this.cardBgUrl = cardBgUrl;
    }

    public int getMsgStyleType() {
        return msgStyleType;
    }

    public void setMsgStyleType(int msgStyleType) {
        this.msgStyleType = msgStyleType;
    }

    public String getEventDescriptionExtract() {
        return eventDescriptionExtract;
    }

    public void setEventDescriptionExtract(String eventDescriptionExtract) {
        this.eventDescriptionExtract = eventDescriptionExtract;
    }

    public String getMsgTipsPicUrl() {
        return msgTipsPicUrl;
    }

    public void setMsgTipsPicUrl(String msgTipsPicUrl) {
        this.msgTipsPicUrl = msgTipsPicUrl;
    }

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public String getHeadline() {
        return headline;
    }

    public void setHeadline(String headline) {
        this.headline = headline;
    }

    public String getEventDescription() {
        return eventDescription;
    }

    public void setEventDescription(String eventDescription) {
        this.eventDescription = eventDescription;
    }

    public String getEventDescriptionPath() {
        return eventDescriptionPath;
    }

    public void setEventDescriptionPath(String eventDescriptionPath) {
        this.eventDescriptionPath = eventDescriptionPath;
    }

    public String getSendTime() {
        return sendTime;
    }

    public void setSendTime(String sendTime) {
        this.sendTime = sendTime;
    }

    public String getPlayType() {
        return playType;
    }

    public void setPlayType(String playType) {
        this.playType = playType;
    }

    public String getMsgLevel() {
        return msgLevel;
    }

    public void setMsgLevel(String msgLevel) {
        this.msgLevel = msgLevel;
    }

    public String getTipsTitle() {
        return tipsTitle;
    }

    public void setTipsTitle(String tipsTitle) {
        this.tipsTitle = tipsTitle;
    }

    public String getMsgDetailsStartTime() {
        return msgDetailsStartTime;
    }

    public void setMsgDetailsStartTime(String msgDetailsStartTime) {
        this.msgDetailsStartTime = msgDetailsStartTime;
    }

    public String getMsgDetailsEndTime() {
        return msgDetailsEndTime;
    }

    public void setMsgDetailsEndTime(String msgDetailsEndTime) {
        this.msgDetailsEndTime = msgDetailsEndTime;
    }

    public String getMsgDetailsBgUrl() {
        return msgDetailsBgUrl;
    }

    public void setMsgDetailsBgUrl(String msgDetailsBgUrl) {
        this.msgDetailsBgUrl = msgDetailsBgUrl;
    }

    public String getMsgDetailsQrUrl() {
        return msgDetailsQrUrl;
    }

    public void setMsgDetailsQrUrl(String msgDetailsQrUrl) {
        this.msgDetailsQrUrl = msgDetailsQrUrl;
    }

    public String getMsgDetailsBtnTextLeft() {
        return msgDetailsBtnTextLeft;
    }

    public void setMsgDetailsBtnTextLeft(String msgDetailsBtnTextLeft) {
        this.msgDetailsBtnTextLeft = msgDetailsBtnTextLeft;
    }

    public String getMsgDetailsBtnTextRight() {
        return msgDetailsBtnTextRight;
    }

    public void setMsgDetailsBtnTextRight(String msgDetailsBtnTextRight) {
        this.msgDetailsBtnTextRight = msgDetailsBtnTextRight;
    }

    public CrashMessageButtonActionBean getMsgDetailsBtnActionLeft() {
        return msgDetailsBtnActionLeft;
    }

    public void setMsgDetailsBtnActionLeft(CrashMessageButtonActionBean msgDetailsBtnActionLeft) {
        this.msgDetailsBtnActionLeft = msgDetailsBtnActionLeft;
    }

    public CrashMessageButtonActionBean getMsgDetailsBtnActionRight() {
        return msgDetailsBtnActionRight;
    }

    public void setMsgDetailsBtnActionRight(CrashMessageButtonActionBean msgDetailsBtnActionRight) {
        this.msgDetailsBtnActionRight = msgDetailsBtnActionRight;
    }

    public String getEmergencyId() {
        return emergencyId;
    }

    public void setEmergencyId(String emergencyId) {
        this.emergencyId = emergencyId;
    }

    public String getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(String publishTime) {
        this.publishTime = publishTime;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getEventLevel() {
        return eventLevel;
    }

    public void setEventLevel(String eventLevel) {
        this.eventLevel = eventLevel;
    }

    public String getMsgContentType() {
        return msgContentType;
    }

    public void setMsgContentType(String msgContentType) {
        this.msgContentType = msgContentType;
    }

    public boolean isTerminatePlayMsg() {
        return terminatePlayMsg;
    }

    public void setTerminatePlayMsg(boolean terminatePlayMsg) {
        this.terminatePlayMsg = terminatePlayMsg;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CrashMessageBaseBean that = (CrashMessageBaseBean) o;
        return msgId.equals(that.msgId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(msgId);
    }
}
