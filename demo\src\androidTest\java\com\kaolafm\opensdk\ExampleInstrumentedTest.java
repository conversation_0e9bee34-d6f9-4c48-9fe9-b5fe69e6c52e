package com.kaolafm.opensdk;

import android.app.Application;
import android.content.Context;
import androidx.test.platform.app.InstrumentationRegistry;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import android.util.Log;

import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import org.junit.Test;
import org.junit.runner.RunWith;

/**
 * Instrumented test, which will execute on an Android device.
 *
 * @see <a href="http://d.android.com/tools/testing">Testing documentation</a>
 */
@RunWith(AndroidJUnit4.class)
public class ExampleInstrumentedTest {

    @Test
    public void useAppContext() throws Exception {
        // Context of the app under test.
        Context appContext = InstrumentationRegistry.getTargetContext();

        OpenSDK.getInstance().initAndActivate((Application) appContext.getApplicationContext(), new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                System.out.println("aBoolean="+aBoolean);
            }

            @Override
            public void onError(ApiException exception) {
                Log.e("ExampleInstrumentedTest", "onError: "+exception);
            }
        });
    }
}
