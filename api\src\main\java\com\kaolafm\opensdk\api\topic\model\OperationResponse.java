package com.kaolafm.opensdk.api.topic.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

/**
 * 点赞操作响应
 *
 * <AUTHOR>
 * @date 2019-03-18
 */
public class OperationResponse implements Parcelable {

    /**
     * 执行状态
     * 0-失败
     * 1-成功
     */
    @SerializedName("status")
    private int status;
    /**
     * 信息
     */
    @SerializedName("msg")
    private String msg;

    protected OperationResponse(Parcel in) {
        status = in.readInt();
        msg = in.readString();
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static final Creator<OperationResponse> CREATOR = new Creator<OperationResponse>() {
        @Override
        public OperationResponse createFromParcel(Parcel in) {
            return new OperationResponse(in);
        }

        @Override
        public OperationResponse[] newArray(int size) {
            return new OperationResponse[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(status);
        dest.writeString(msg);
    }

}
