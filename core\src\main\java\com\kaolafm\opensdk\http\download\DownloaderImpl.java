package com.kaolafm.opensdk.http.download;

import com.kaolafm.opensdk.http.cache.IntelligentCache;
import com.kaolafm.opensdk.http.cache.Cache;
import com.kaolafm.opensdk.http.download.engine.RealDownloadRequest;

/**
 * 下载器实现类。
 *
 * <AUTHOR>
 * @date 2020-02-10
 */
public class DownloaderImpl implements Downloader {

    private RealDownloadRequest mRequest;

    private Cache<String, DownloadRequest> mCache;

    public DownloaderImpl() {
        mCache = new IntelligentCache<>(10 * 1024);
        mRequest = new RealDownloadRequest();
    }

    @Override
    public void download(DownloadRequest request, DownloadListener listener) {
        mCache.put(request.url, request);
        mRequest.download(request, listener);
    }

    @Override
    public void pause(String url) {
        mRequest.cancel(url);
    }

    @Override
    public void cancel(String url) {
        DownloadRequest downloadRequest = mCache.remove(url);
        if (downloadRequest != null) {
            downloadRequest.getShadow().delete();
            downloadRequest.getTmp().delete();
        }
        mRequest.cancel(url);
    }
}
