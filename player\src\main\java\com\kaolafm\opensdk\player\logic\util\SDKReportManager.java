package com.kaolafm.opensdk.player.logic.util;

import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.base.utils.DeviceUtil;
import com.kaolafm.base.utils.NetworkUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.live.model.LiveInfoDetail;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.PlayItemConstants;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.LivePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.LiveStreamPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.RadioPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.TVPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.BufferEndReportEvent;
import com.kaolafm.report.event.BufferStartReportEvent;
import com.kaolafm.report.event.LivingStartListenReportEvent;
import com.kaolafm.report.event.RequetErrorReportEvent;
import com.kaolafm.report.inner.InnerPlayReportParameter;
import com.kaolafm.report.model.PlayReportParameter;
import com.kaolafm.report.util.ReportConstants;

import tv.danmaku.ijk.media.player.IjkMediaPlayer;

/**
 * sdk数据自动上报管理类
 *
 * <AUTHOR> on 2021/5/12.
 */

public class SDKReportManager {
    /**
     * 当前的播放对象
     */
    private PlayItem mCurrentPlayItem;

    /**
     * 当前碎片播放总时长 单位ms
     */
    private long mAudioPlayedTime;

    /**
     * 记录碎片播放时长按秒除以10的值，为了10秒一上报
     */
    private long mAudioPlayedTimeDivideTen = 0L;

    /**
     * 记录上次position的获取时间，用以检测卡顿
     */
    private long mBufferStartTime = 0;

    /**
     * 播放器是否触发了seek true为是，false为否
     */
    private boolean isSeekEvent;

    private long mCurrentPosition;

    private volatile static SDKReportManager sdkReportManager;

    private SDKReportManager() {
        initPlayerListener();
    }

    public static SDKReportManager getInstance() {
        if (sdkReportManager == null) {
            synchronized (SDKReportManager.class) {
                if (sdkReportManager == null) {
                    sdkReportManager = new SDKReportManager();
                }
            }
        }
//        ReportHelper.getInstance().initBySdk();
        return sdkReportManager;
    }

    public void initPlayerListener() {
        PlayerManager.getInstance().addPlayControlStateCallback(playerStateListenerWrapper);
    }

    BasePlayStateListener playerStateListenerWrapper = new BasePlayStateListener() {

        @Override
        public void onPlayerPreparing(PlayItem playItem) {
            super.onPlayerPreparing(playItem);
            resetReportPlayedTime();
        }

        @Override
        public void onPlayerPlaying(PlayItem playItem) {
            super.onPlayerPlaying(playItem);
            reportStartPlay(playItem);
        }

        @Override
        public void onPlayerPaused(PlayItem playItem) {
            super.onPlayerPaused(playItem);
        }

        @Override
        public void onProgress(PlayItem playItem, long progress, long duration) {
            PlayerLogUtil.log(getClass().getSimpleName(), "onProgress", "progress=" + progress + ";duration=" + duration);
            if (mCurrentPosition != 0) {
                long playTime = progress - mCurrentPosition;
                if (playTime > 0) {
                    mAudioPlayedTime += playTime;
                    ReportHelper.getInstance().setPlayPosition(mAudioPlayedTime, duration);
                    if ((playItem instanceof BroadcastPlayItem || playItem instanceof TVPlayItem) && playItem.isLiving()) {
                        long mAudioPlayedTimeDivideTenTmp = mAudioPlayedTime / 1000 / 10;
                        if (mAudioPlayedTimeDivideTen != mAudioPlayedTimeDivideTenTmp) {
                            mAudioPlayedTimeDivideTen = mAudioPlayedTimeDivideTenTmp;
                            reportBroadcastPlaying(false);
                        }
                    }
                    PlayerLogUtil.log(getClass().getSimpleName(), "onProgress", "mAudioPlayedTime=" + mAudioPlayedTime);
                }
            }
            mCurrentPosition = progress;
            PlayerLogUtil.log(getClass().getSimpleName(), "onProgress", "mCurrentPosition=" + mCurrentPosition);
        }

        @Override
        public void onPlayerFailed(PlayItem playItem, int i, int i1) {
            super.onPlayerFailed(playItem, i, i1);
            reportRequestError(playItem, i, i1);
        }

        @Override
        public void onPlayerEnd(PlayItem playItem) {
            super.onPlayerEnd(playItem);
            reportEndPlay(ReportConstants.PLAY_CHANGE_BY_AUTO);
            resetReportPlayedTime();
        }

        @Override
        public void onSeekStart(PlayItem playItem) {
            isSeekEvent = true;
            resetReportPlayedTime();
        }

        @Override
        public void onSeekComplete(PlayItem playItem) {
            super.onSeekComplete(playItem);
        }

        @Override
        public void onBufferingStart(PlayItem playItem) {
            super.onBufferingStart(playItem);
            mBufferStartTime = SystemClock.elapsedRealtime();
            reportBufferingStart(playItem, isSeekEvent);
        }

        @Override
        public void onBufferingEnd(PlayItem playItem) {
            super.onBufferingEnd(playItem);
            reportBufferingEnd(playItem, isSeekEvent, mBufferStartTime);
            isSeekEvent = false;
        }
    };

    /**
     * 重置播放时间
     */
    private void resetReportPlayedTime() {
        mAudioPlayedTime = 0;
        mCurrentPosition = 0;
    }

    /**
     * 上报播放开始事件
     *
     * @param playItem
     */
    public void reportStartPlay(PlayItem playItem) {
        if (!ReportHelper.getInstance().isUseBySDK) {
            return;
        }
        if (playItem == null || StringUtil.isEmpty(playItem.getPlayUrl())) {
            return;
        }
        int type = playItem.getType();
        //播放的是相同的碎片，不上报点播开始事件
        if (PlayItemUtil.isSameProgram(playItem, mCurrentPlayItem)) {
            return;
        }
        mCurrentPlayItem = playItem;
        if (type == PlayerConstants.RESOURCES_TYPE_LIVE_STREAM) {
            reportLivingStreamPlay(playItem);
        } else if (type == PlayerConstants.RESOURCES_TYPE_LIVING) {
            reportLivingPlay(playItem);
        } else {
            reportStartOtherPlay(playItem);
        }

    }

    public void reportQQMusicStartPlay(PlayReportParameter playReportParameter) {
        if (!ReportHelper.getInstance().isUseBySDK) {
            return;
        }
        ReportHelper.getInstance().addStartListenReport(playReportParameter);
    }

    public void reportSetPlayPosition(long playPosition, long total) {
        if (!ReportHelper.getInstance().isUseBySDK) {
            return;
        }
        ReportHelper.getInstance().setPlayPosition(playPosition, total);
    }

    /**
     * 上报播放结束事件
     */
    public void reportEndPlay(String reason) {
        if (!ReportHelper.getInstance().isUseBySDK) {
            return;
        }
        reportEndPlay(reason, false);
    }

    /**
     * 上报播放结束事件
     */
    public void reportEndPlay(String reason, boolean isNeedReport) {
        if (!ReportHelper.getInstance().isUseBySDK) {
            return;
        }
        //直播自然结束，更新状态
        if (mCurrentPlayItem instanceof LivePlayItem) {
            ReportHelper.getInstance().setLiveStatus(mCurrentPlayItem.getStatus());
        }
        ReportHelper.getInstance().addEndListenReport(reason, isNeedReport);
    }

    /**
     * 上报播放结束事件
     */
    public void reportEndPlay(String reason, boolean isNeedReport, boolean isClearParameter) {
        if (!ReportHelper.getInstance().isUseBySDK) {
            return;
        }
        ReportHelper.getInstance().addEndListenReport(reason, isNeedReport, isClearParameter);
    }

    public void reportBroadcastPlaying(boolean isClearParameter) {
        if (!ReportHelper.getInstance().isUseBySDK) {
            return;
        }
        ReportHelper.getInstance().addBroadcastPlaying(isClearParameter);
    }

    /**
     * 上报卡顿开始
     *
     * @param playItem
     * @param isSeek
     */
    public void reportBufferingStart(PlayItem playItem, boolean isSeek) {
        if (!ReportHelper.getInstance().isUseBySDK) {
            return;
        }
        if (playItem == null) {
            return;
        }
        DeviceUtil.getDeviceDns(dns -> {
            BufferStartReportEvent bufferStartReportEvent = new BufferStartReportEvent();
            bufferStartReportEvent.setAlbumid(String.valueOf(playItem.getAlbumId()));
            bufferStartReportEvent.setAudioid(String.valueOf(playItem.getAudioId()));
            if (PlayerManager.getInstance().getCurPlayItem() != null &&
                    PlayerManager.getInstance().getCurPlayItem() instanceof RadioPlayItem) {
                bufferStartReportEvent.setRadioid(String.valueOf(PlayerManager.getInstance().getCurPlayItem().getRadioId()));
            } else {
                bufferStartReportEvent.setRadioid(String.valueOf(playItem.getAlbumId()));
            }

            bufferStartReportEvent.setRemarks1(dns);
            if (isSeek) {
                bufferStartReportEvent.setType("1");
            }
            bufferStartReportEvent.setRemarks2(playItem.getPlayUrl());
            ReportHelper.getInstance().addEvent(bufferStartReportEvent, false);
        });
    }

    /**
     * 上报卡顿结束
     *
     * @param playItem
     * @param isSeek
     * @param bufferStartTime 卡顿开始时间
     */
    public void reportBufferingEnd(PlayItem playItem, boolean isSeek,
                                   long bufferStartTime) {
        if (!ReportHelper.getInstance().isUseBySDK) {
            return;
        }
        if (playItem == null) {
            return;
        }
        DeviceUtil.getDeviceDns(dns -> {
            BufferEndReportEvent bufferEndReportEvent = new BufferEndReportEvent();
            bufferEndReportEvent.setAlbumid(String.valueOf(playItem.getAlbumId()));
            bufferEndReportEvent.setAudioid(String.valueOf(playItem.getAudioId()));
            if (PlayerManager.getInstance().getCurPlayItem() != null &&
                    PlayerManager.getInstance().getCurPlayItem() instanceof RadioPlayItem) {
                bufferEndReportEvent.setRadioid(String.valueOf(PlayerManager.getInstance().getCurPlayItem().getRadioId()));
            } else {
                bufferEndReportEvent.setRadioid(String.valueOf(playItem.getAlbumId()));
            }
            try {
                double time = (SystemClock.elapsedRealtime() - bufferStartTime) / 1000.0;
                bufferEndReportEvent.setPlaytime(String.format("%.2f", time));
            } catch (Exception e) {
                Log.e("SDKReportManager", "reportBufferingEnd: ", e);
            }
            bufferEndReportEvent.setRemarks1(dns);
            if (isSeek) {
                bufferEndReportEvent.setType("1");
            }
            bufferEndReportEvent.setRemarks2(playItem.getPlayUrl());
            ReportHelper.getInstance().addEvent(bufferEndReportEvent, false);
        });

    }


    /**
     * @param playItem
     * @param what
     * @param extra
     */

    public void reportRequestError(PlayItem playItem, int what, int extra) {
        if (!ReportHelper.getInstance().isUseBySDK) {
            return;
        }
        if (playItem == null) {
            return;
        }
        DeviceUtil.getDeviceDns(dns -> {
            RequetErrorReportEvent errorReportEvent = new RequetErrorReportEvent();
            errorReportEvent.setMessage(what + "&" + extra);
            errorReportEvent.setAudioid(String.valueOf(playItem.getAudioId()));
            if (PlayerManager.getInstance().getCurPlayItem() != null &&
                    PlayerManager.getInstance().getCurPlayItem() instanceof RadioPlayItem) {
                errorReportEvent.setRadioid(String.valueOf(PlayerManager.getInstance().getCurPlayItem().getRadioId()));
            } else {
                errorReportEvent.setRadioid(String.valueOf(playItem.getAlbumId()));
            }
            errorReportEvent.setUrl(playItem.getPlayUrl());
            errorReportEvent.setRemarks2(dns);
            String curDnsIpUsed = null;
            String dnsStr = playItem.getMapCacheData(PlayItemConstants.ITEM_KEY_DNS_ADDRESS);
            if (!TextUtils.isEmpty(dnsStr)) {
                if (dnsStr.contains(",")) {
                    String[] addressArray = dnsStr.split(",");
                    if (addressArray.length > 0) {
                        curDnsIpUsed = addressArray[0];
                    }
                } else {
                    curDnsIpUsed = dnsStr;
                }
            }
            errorReportEvent.setRemarks1(curDnsIpUsed);
            if (what == IjkMediaPlayer.MEDIA_ERROR_IJK_PLAYER || what == IjkMediaPlayer.MEDIA_ERROR_IJK_PLAYER_ZERO) {
                errorReportEvent.setRemarks3("1");
            } else {
                errorReportEvent.setRemarks3("2");
            }
            errorReportEvent.setResult(String.valueOf(extra));
            boolean isHasNetwork = NetworkUtil.isNetworkAvailable(ComponentKit.getInstance().getApplication());
            if (!isHasNetwork) {
                errorReportEvent.setSpeed("1");
            }
            ReportHelper.getInstance().addEvent(errorReportEvent, false);
        });
    }


    private void reportLivingStreamPlay(PlayItem playItem) {
        if (playItem instanceof LiveStreamPlayItem) {
            LiveStreamPlayItem liveStreamPlayItem = (LiveStreamPlayItem) playItem;
            InnerPlayReportParameter playReportParameter = new InnerPlayReportParameter();
            playReportParameter.setAudioid(String.valueOf(liveStreamPlayItem.getAudioId()));
            playReportParameter.setIsThirdParty(playItem.getIsThirdParty());
            playReportParameter.setLiveType_status(String.valueOf((liveStreamPlayItem).getStatus()));
            playReportParameter.setLiveType_live_id(String.valueOf((liveStreamPlayItem).getInfoData().getAlbumId()));
            playReportParameter.setLiveType_plan_id(String.valueOf((liveStreamPlayItem).getLiveId()));
            playReportParameter.setLiveType_position(playItem.getMapCacheData(PlayItemConstants.KEY_LIVING_LOCATION));
            playReportParameter.setSourceType(ReportConstants.SOURCE_TYPE_LIVING);
            playReportParameter.setTotalLength(liveStreamPlayItem.getDuration());
            playReportParameter.setLiveType_compereid(String.valueOf((liveStreamPlayItem).getComperesId()));
            playReportParameter.setInnerPlayer();
            ReportHelper.getInstance().addStartListenReport(playReportParameter);
        }
    }

    private void reportLivingPlay(PlayItem playItem) {
        if (playItem instanceof LivePlayItem) {
            LivePlayItem livePlayItem = (LivePlayItem) playItem;
            if (livePlayItem.getStatus() != LiveInfoDetail.STATUS_LIVING) {
                //当直播非“播放中”状态，则应忽略上报直播开始事件，且如果正在播放其他节目，应上报结束
                ReportHelper.getInstance().addEndListenReport("3", true);
                //置空，为了重新播放该直播时可以顺利上报开始事件，不被判断相同节目的逻辑拦截
                mCurrentPlayItem = null;
                return;
            }
            InnerPlayReportParameter playReportParameter = new InnerPlayReportParameter();
            playReportParameter.setAudioid(String.valueOf(livePlayItem.getAudioId()));
            playReportParameter.setIsThirdParty(playItem.getIsThirdParty());
            playReportParameter.setLiveType_status(String.valueOf(livePlayItem.getStatus()));
            playReportParameter.setLiveType_live_id(String.valueOf(livePlayItem.getInfoData().getAlbumId()));
            playReportParameter.setLiveType_plan_id(String.valueOf(livePlayItem.getLiveId()));
            playReportParameter.setLiveType_position(LivingStartListenReportEvent.POSITION_RECOMMENT);
            playReportParameter.setSourceType(ReportConstants.SOURCE_TYPE_LIVING);
            playReportParameter.setTotalLength(livePlayItem.getDuration());
            playReportParameter.setLiveType_compereid(livePlayItem.getComperesId());
            playReportParameter.setInnerPlayer();
            ReportHelper.getInstance().addStartListenReport(playReportParameter);
        }
    }

    private void reportStartOtherPlay(PlayItem playItem) {
        InnerPlayReportParameter playReportParameter = new InnerPlayReportParameter();
        playReportParameter.setIsThirdParty(playItem.getIsThirdParty());
        /*
            当播放专辑/单曲时，albumid为当前播放单曲所属专辑id；
            当播放AI电台里专辑/单曲时，albumid为当前播放单曲所属专辑id
            当播放广播、听电视时，albumid为0
        */
        switch (playItem.getType()) {
            case PlayerConstants.RESOURCES_TYPE_BROADCAST:
            case PlayerConstants.RESOURCES_TYPE_TV:
                playReportParameter.setAlbumid("0");
                break;
            default:
                playReportParameter.setAlbumid(String.valueOf(playItem.getAlbumId()));
        }
        //当前播放单曲id；广播、听电视直播节目id或广播、听电视回放音频id；（没有广播、听电视节目id时传空）
        playReportParameter.setAudioid(playItem.getAudioId() != 0 ? String.valueOf(playItem.getAudioId()) : "");
        /*
            当播放专辑/单曲时，radioid为当前播放单曲所属专辑id；
            当播放AI电台时，radioid为当前AI电台id；
            注：播放AI电台包括点击AI电台播单的某一专辑单曲或AI电台流自动播放到某一专辑单曲，此时上报的radioid都为当前AI电台id
            当播放广播、听电视时（无论直播或回放），radioid为当前广播、听电视id。
        * */
        playReportParameter.setRadioid(playItem.getRadioId());
        if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_RADIO) {
            playReportParameter.setRadioType(String.valueOf(playItem.getRadioSubTagType()));
        }
        playReportParameter.setAudioSource(playItem.getSource());
        playReportParameter
                .setRecommendResultCallback(playItem.getCallback());
        playReportParameter.setSourceType(getReportType());

        playReportParameter.setBroadcast_status(getBroadcastStatus(playItem));
        playReportParameter.setTotalLength(playItem.getDuration());
        if (playItem.getVip() == 1) {
            playReportParameter.setTag("VIP");
        } else if (playItem.getFine() == 1) {
            playReportParameter.setTag("精品");
        } else {
            playReportParameter.setTag("无");
        }
        if (playItem.getBuyType() == AudioDetails.BUY_TYPE_FREE) {
            playReportParameter.setAudioid_type(0);
        } else if (playItem.getBuyType() == AudioDetails.BUY_TYPE_AUDITION) {
            playReportParameter.setAudioid_type(2);
        } else {
            playReportParameter.setAudioid_type(1);
        }
        if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE
                || playItem.getType() == PlayerConstants.RESOURCES_TYPE_PURCHASE_ONE_KEY_LISTENER_TYPE) {
            playReportParameter.setContentObtainType(ReportConstants.COTENT_BY_ONEKEY);
        } else if (playItem.getPosition() != 0) {
            playReportParameter.setContentObtainType(ReportConstants.COTENT_BY_BREAKPOINT);
        }
        playReportParameter.setInnerPlayer();
        ReportHelper.getInstance().addStartListenReport(playReportParameter);
    }

    private int getReportType() {
        int radioType = PlayerManager.getInstance().getCurPlayItem().getType();
        switch (radioType) {
            case PlayerConstants.RESOURCES_TYPE_BROADCAST:
                return ReportConstants.SOURCE_TYPE_BROADCAST;
            case PlayerConstants.RESOURCES_TYPE_TV:
                return ReportConstants.SOURCE_TYPE_TV;
            case PlayerConstants.RESOURCES_TYPE_LIVING:
                return ReportConstants.SOURCE_TYPE_LIVING;
            default:
                return ReportConstants.SOURCE_TYPE_ALBUM;
        }
    }

    /**
     * 1：直播中；2：回放中
     */
    private String getBroadcastStatus(PlayItem playItem) {
        if (playItem instanceof BroadcastPlayItem || playItem instanceof TVPlayItem) {
            if (PlayerConstants.BROADCAST_STATUS_LIVING == playItem.getStatus()) {
                return "1";
            } else if (PlayerConstants.BROADCAST_STATUS_PLAYBACK == playItem.getStatus()) {
                return "2";
            }
            return "1";
        }
        return null;
    }

}
