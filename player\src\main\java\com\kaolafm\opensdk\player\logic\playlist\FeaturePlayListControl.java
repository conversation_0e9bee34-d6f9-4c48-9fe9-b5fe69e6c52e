package com.kaolafm.opensdk.player.logic.playlist;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.feature.FeatureRequest;
//import com.kaolafm.opensdk.api.media.AudioRequest;
import com.kaolafm.opensdk.api.feature.model.FeatureAudioDetails;
import com.kaolafm.opensdk.api.feature.model.FeatureDetails;
import com.kaolafm.opensdk.api.media.model.AlbumDetails;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener;
import com.kaolafm.opensdk.player.logic.model.CustomPlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.item.FeaturePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.InfoData;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playlist.innerinterface.IDataListCallback;
import com.kaolafm.opensdk.player.logic.playlist.util.PlayListUtils;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.opensdk.player.logic.util.PlayerPreconditions;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> on 2019/3/18.
 */

public class FeaturePlayListControl extends BasePlayListControl {
    private static final int LOAD_DATA = 0;
    private static final int LOAD_DATA_NEXT = 1;
    private static final int LOAD_DATA_PRE = 2;
    public static final int LOAD_DATA_PAGE = 3; //获取整页数据
    private static final int LOAD_DATA_PAGE_AND_PLAY_NEXT_ITEM = 4; //获取整页数据后播放下一个
    private static final int LOAD_DATA_PAGE_AND_PLAY_PRE_ITEM = 5; //获取整页数据后播放上一个
    private FeatureRequest mFeatureRequest;
    //    private AudioRequest mAudioRequest;
    private PlayerBuilder tempPlayerBuilder;
    private int mPageSize = PlayerConstants.PAGE_NUMBER_10;

    public FeaturePlayListControl() {
        super();
        mFeatureRequest = new FeatureRequest();
//        mAudioRequest = new AudioRequest();
    }

    @Override
    public void initPlayList(PlayerBuilder playerBuilder, IPlayListGetListener iPlayListGetListener) {
        PlayerLogUtil.log(getClass().getSimpleName(), "initPlayList", "feature");
//        if (playerBuilder.getType() == PlayerConstants.RESOURCES_TYPE_AUDIO) {
//            PlayerLogUtil.log(getClass().getSimpleName(), "initPlayList", "play audio, get audio detail");
//            tempPlayerBuilder = new CustomPlayerBuilder();
//            ((CustomPlayerBuilder) tempPlayerBuilder).setChildId(playerBuilder.getId()).setType(playerBuilder.getType());
//            getAudioInfo(iPlayListGetListener);
//            return;
//        }
        tempPlayerBuilder = playerBuilder;
        super.initPlayList(tempPlayerBuilder, iPlayListGetListener);
        getFeatureInfo(iPlayListGetListener);
    }

    @Override
    public int getCurPosition() {
        if (mPosition != -1 && !isInList(mPlayItemArrayList, mCurPlayItem)) {
            return -1;
        }
        return mPosition;
    }

    @Override
    public void getNextPlayItem(IPlayListGetListener iPlayListGetListener) {
        if (mPlayItemArrayList != null && mCurPlayItem != null && mPosition != -1) {
            boolean isInList = isInList(mPlayItemArrayList, mCurPlayItem);
            if (!isInList) {
                loadPageData(LOAD_DATA_PAGE_AND_PLAY_NEXT_ITEM,
                        mCurPlayItem.getAudioId(), 1, iPlayListGetListener);
                return;
            }
        }
        super.getNextPlayItem(iPlayListGetListener);
    }

    @Override
    public void getPrePlayItem(IPlayListGetListener iPlayListGetListener) {
        if (mPlayItemArrayList != null && mCurPlayItem != null && mPosition != -1) {
            boolean isInList = isInList(mPlayItemArrayList, mCurPlayItem);
            if (!isInList) {
                loadPageData(LOAD_DATA_PAGE_AND_PLAY_PRE_ITEM,
                        mCurPlayItem.getAudioId(), 1, iPlayListGetListener);
                return;
            }
        }
        super.getPrePlayItem(iPlayListGetListener);
    }

    @Override
    public void loadNextPage(IPlayListGetListener iPlayListGetListener) {
        PlayerLogUtil.log(getClass().getSimpleName(), "loadNextPage", "feature get next page ...");
        long featureId = string2Long(mPlaylistInfo.getId());

        FeaturePlayItem invalidPlayItem = new FeaturePlayItem();
        InfoData infoData = new InfoData();
        infoData.setAlbumId(featureId);
        invalidPlayItem.setInfodata(infoData);

        loadPlayListData(featureId, mPlaylistInfo.getSort(), mPlaylistInfo.getNextPage(), new IDataListCallback<BasePageResult<List<FeatureAudioDetails>>>() {
            @Override
            public void success(BasePageResult<List<FeatureAudioDetails>> listBasePageResult) {
                ArrayList<PlayItem> playItemArrayList = PlayListUtils.audioDetailToFeaturePlayItem(listBasePageResult.getDataList(), mPlaylistInfo);
                if (ListUtil.isEmpty(playItemArrayList)) {
                    notifyPlayListGetError(iPlayListGetListener, invalidPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_PAGE_NULL, -1);
                    notifyPlayListChangeError(invalidPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_PAGE_NULL, -1);
                    return;
                }
                updatePlayListInfo(LOAD_DATA_NEXT, listBasePageResult);
                updatePlayListContent(LOAD_DATA_NEXT, playItemArrayList, iPlayListGetListener);
            }

            @Override
            public void error(ApiException e) {
                PlayerLogUtil.log(getClass().getSimpleName(), "loadNextPage", "get playlist error....");
                notifyPlayListGetError(iPlayListGetListener, invalidPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_PAGE_SERVER, e.getCode());
                notifyPlayListChangeError(invalidPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_PAGE_SERVER, e.getCode());
            }
        });
    }

    @Override
    public void loadPrePage(IPlayListGetListener iPlayListGetListener) {
        PlayerLogUtil.log(getClass().getSimpleName(), "loadPrePage", "feature get pre page playlist...");
        if (!mPlaylistInfo.isHasPrePage()) {
            return;
        }
        long featureId = string2Long(mPlaylistInfo.getId());

        FeaturePlayItem featurePlayItem = new FeaturePlayItem();
        InfoData infoData = new InfoData();
        infoData.setAlbumId(featureId);
        featurePlayItem.setInfodata(infoData);

        loadPlayListData(featureId, mPlaylistInfo.getSort(), mPlaylistInfo.getPrePage(), new IDataListCallback<BasePageResult<List<FeatureAudioDetails>>>() {
            @Override
            public void success(BasePageResult<List<FeatureAudioDetails>> listBasePageResult) {
                ArrayList<PlayItem> playItemArrayList = PlayListUtils.audioDetailToFeaturePlayItem(listBasePageResult.getDataList(), mPlaylistInfo);
                if (ListUtil.isEmpty(playItemArrayList)) {
                    notifyPlayListGetError(iPlayListGetListener, featurePlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_PAGE_NULL, -1);
                    notifyPlayListChangeError(featurePlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_PAGE_NULL, -1);
                    return;
                }
                updatePlayListInfo(LOAD_DATA_PRE, listBasePageResult);
                updatePlayListContent(LOAD_DATA_PRE, playItemArrayList, iPlayListGetListener);
            }

            @Override
            public void error(ApiException e) {
                PlayerLogUtil.log(getClass().getSimpleName(), "loadPrePage", "get playlist error....");
                notifyPlayListGetError(iPlayListGetListener, featurePlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_PAGE_SERVER, e.getCode());
                notifyPlayListChangeError(featurePlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_PAGE_SERVER, e.getCode());
            }
        });
    }

    @Override
    public void loadPageData(int type, long audioId, int pageNum, IPlayListGetListener iPlayListGetListener) {
        PlayerLogUtil.log(getClass().getSimpleName(), "loadPageData", "feature get page data playlist...");

        int errorCode1 = -1;
        int errorCode2 = -1;
        if (type == LOAD_DATA_PAGE_AND_PLAY_NEXT_ITEM) {
            errorCode1 = PlayerConstants.ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_NEXT_ITEM_PAGE_NULL;
            errorCode2 = PlayerConstants.ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_NEXT_ITEM_PAGE_SERVER;
        } else if (type == LOAD_DATA_PAGE_AND_PLAY_PRE_ITEM) {
            errorCode1 = PlayerConstants.ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_PRE_ITEM_PAGE_NULL;
            errorCode2 = PlayerConstants.ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_PRE_ITEM_PAGE_SERVER;
        } else if (type == LOAD_DATA_PAGE) {
            errorCode1 = PlayerConstants.ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_PAGE_NULL;
            errorCode2 = PlayerConstants.ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_PAGE_SERVER;
        }
        int finalErrorCode1 = errorCode1;
        int finalErrorCode2 = errorCode2;
        long featureId = string2Long(mPlaylistInfo.getId());

        FeaturePlayItem featurePlayItem = new FeaturePlayItem();
        InfoData infoData = new InfoData();
        infoData.setAlbumId(featureId);
        featurePlayItem.setInfodata(infoData);
        featurePlayItem.setAudioId(audioId);

        loadPlayListData(featureId, audioId, mPlaylistInfo.getSort(), pageNum, new IDataListCallback<BasePageResult<List<FeatureAudioDetails>>>() {
            @Override
            public void success(BasePageResult<List<FeatureAudioDetails>> listBasePageResult) {
                ArrayList<PlayItem> playItemArrayList = PlayListUtils.audioDetailToFeaturePlayItem(listBasePageResult.getDataList(), mPlaylistInfo);
                if (ListUtil.isEmpty(playItemArrayList)) {
                    notifyPlayListGetError(iPlayListGetListener, featurePlayItem, finalErrorCode1, -1);
                    return;
                }
                updatePlayListInfo(type, listBasePageResult);
                updatePlayListContent(type, playItemArrayList, iPlayListGetListener);
            }

            @Override
            public void error(ApiException e) {
                PlayerLogUtil.log(getClass().getSimpleName(), "loadPageData", "get playlist error....");
                notifyPlayListGetError(iPlayListGetListener, featurePlayItem, finalErrorCode2, e.getCode());
                notifyPlayListChangeError(featurePlayItem, finalErrorCode2, e.getCode());
            }
        });
    }

    private void loadPlayListData(long featureId, int sort, int pageNum, IDataListCallback<BasePageResult<List<FeatureAudioDetails>>> iDataListCallback) {
        loadPlayListData(featureId, 0, sort, pageNum, iDataListCallback);
    }

    /**
     * 加载数据
     *
     * @param featureId
     * @param audioId
     * @param sort
     * @param pageNum
     * @param iDataListCallback
     */
    private void loadPlayListData(long featureId, long audioId, int sort, int pageNum, IDataListCallback<BasePageResult<List<FeatureAudioDetails>>> iDataListCallback) {
        mFeatureRequest.getPlaylist(featureId, audioId, sort, mPageSize, pageNum, new HttpCallback<BasePageResult<List<FeatureAudioDetails>>>() {
            @Override
            public void onSuccess(BasePageResult<List<FeatureAudioDetails>> listBasePageResult) {
//                if (PlayerPreconditions.checkNull(listBasePageResult) || ListUtil.isEmpty(listBasePageResult.getDataList())) {
//                    if (iDataListCallback != null) {
//                        iDataListCallback.error();
//                    }
//                    return;
//                }
                if (iDataListCallback != null) {
                    iDataListCallback.success(listBasePageResult);
                }
            }

            @Override
            public void onError(ApiException e) {
                if (iDataListCallback != null) {
                    iDataListCallback.error(e);
                }
            }
        });
    }

    private int getNotifyPlayListIndex(ArrayList<PlayItem> playItemArrayList, long audioId) {
        if (audioId <= 0) {
            return 0;
        }
        for (int i = 0; i < playItemArrayList.size(); i++) {
            PlayItem playItem = playItemArrayList.get(i);
            if (PlayerPreconditions.checkNull(playItem)) {
                continue;
            }

            if (playItem.getAudioId() == audioId) {
                return i;
            }
        }

        return 0;
    }

    private void getFeatureInfo(final IPlayListGetListener iPlayListGetListener) {
        PlayerLogUtil.log(getClass().getSimpleName(), "getFeatureInfo");
        long featureId = string2Long(tempPlayerBuilder.getId());
        long audioId = 0;
        if (tempPlayerBuilder instanceof CustomPlayerBuilder) {
            audioId = string2Long(((CustomPlayerBuilder) tempPlayerBuilder).getChildId());
        }

        long finalAudioId = audioId;
        FeaturePlayItem featurePlayItem = new FeaturePlayItem();
        featurePlayItem.getInfoData().setAlbumId(featureId);
        featurePlayItem.setAudioId(audioId);

        mFeatureRequest.getFeatureDetails(featureId, new HttpCallback<FeatureDetails>() {
            @Override
            public void onSuccess(FeatureDetails featureDetails) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getFeatureInfo", "success");
                initPlayListInfo(featureDetails);

                if (PlayerPreconditions.checkNull(featureDetails)) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "getFeatureInfo", "success, but data is null");
                    notifyPlayListGetError(iPlayListGetListener, featurePlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_NULL, -1);
                    notifyPlayListChangeError(featurePlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_NULL, -1);
                    return;
                }

                featurePlayItem.setListenCount(featureDetails.getListenNum());
                featurePlayItem.getInfoData().setAlbumName(featureDetails.getFeatureName());
                featurePlayItem.getInfoData().setAlbumPic(featureDetails.getCoverImg());
                featurePlayItem.setNoSubscribe(featureDetails.getNoSubscribe());
                mPlaylistInfo.setNoSubscribe(featureDetails.getNoSubscribe());
                mPlaylistInfo.setEnableReverse(featureDetails.getEnableReverse() == AlbumDetails.REVERSE_ENABLE);
                tempPlayerBuilder.setNoSubscribe(0);
                if (featureDetails.getStatus() != PlayerConstants.ALBUM_ONLINE) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "getfeatureInfo", "success, offline");
                    notifyPlayListGetError(iPlayListGetListener, featurePlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_ALBUM_OFFLINE, -1);
                    notifyPlayListChangeError(featurePlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_ALBUM_OFFLINE, -1);
                    return;
                }

                loadPlayListData(featureId, finalAudioId, mPlaylistInfo.getSort(), 1, new IDataListCallback<BasePageResult<List<FeatureAudioDetails>>>() {
                    @Override
                    public void success(BasePageResult<List<FeatureAudioDetails>> listBasePageResult) {
                        if (PlayerPreconditions.checkNull(listBasePageResult) || ListUtil.isEmpty(listBasePageResult.getDataList())) {
                            PlayerLogUtil.log(getClass().getSimpleName(), "getfeatureInfo", "get play list success, but list is null");
                            notifyPlayListGetError(iPlayListGetListener, featurePlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_PAGE_NULL, -1);
                            notifyPlayListChangeError(featurePlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_PAGE_NULL, -1);
                            return;
                        }
                        PlayerLogUtil.log(getClass().getSimpleName(), "getfeatureInfo", "get play list success");
//                        AlbumPlayListControl.super.initPlayList(tempPlayerBuilder, iPlayListGetListener);
                        updatePlayListInfo(LOAD_DATA, listBasePageResult);
                        ArrayList<PlayItem> playItemArrayList = PlayListUtils.audioDetailToFeaturePlayItem(listBasePageResult.getDataList(), mPlaylistInfo);
                        if (ListUtil.isEmpty(playItemArrayList)) {
                            notifyPlayListGetError(iPlayListGetListener, featurePlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_PAGE_NULL, -1);
                            notifyPlayListChangeError(featurePlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_PAGE_NULL, -1);
                            return;
                        }
                        release();
                        updatePlayListContent(LOAD_DATA, finalAudioId, playItemArrayList, iPlayListGetListener);
                    }

                    @Override
                    public void error(ApiException e) {
                        PlayerLogUtil.log(getClass().getSimpleName(), "getfeatureInfo", "get play list error");
                        notifyPlayListGetError(iPlayListGetListener, featurePlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_PAGE_SERVER, e.getCode());
                        notifyPlayListChangeError(featurePlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_PAGE_SERVER, e.getCode());
                    }
                });
            }

            @Override
            public void onError(ApiException e) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getfeatureInfo", "get feature info error");
                initPlayListInfo(null);
                notifyPlayListGetError(iPlayListGetListener, featurePlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_SERVER, e.getCode());
                notifyPlayListChangeError(featurePlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_SERVER, e.getCode());
            }
        });

    }

    private long string2Long(String strValue) {
        long longValue = 0;
        if (!StringUtil.isEmpty(strValue)) {
            try {
                longValue = Long.parseLong(strValue);
            } catch (Exception e) {

            }
        }
        return longValue;
    }

//    private void getAudioInfo(final IPlayListGetListener iPlayListGetListener) {
//        long audioId = string2Long(((CustomPlayerBuilder) tempPlayerBuilder).getChildId());
//        mAudioRequest.getAudioDetails(audioId, new HttpCallback<AudioDetails>() {
//            @Override
//            public void onSuccess(AudioDetails audioDetails) {
//                //此处获取到的audioDetails中只有id，不再包含url信息
//                PlayerLogUtil.log(getClass().getSimpleName(), "getAudioInfo", "success");
//                mPlaylistInfo.setTempId(String.valueOf(audioDetails.getAlbumId()));
//                mPlaylistInfo.setTempChildId(String.valueOf(audioDetails.getAudioId()));
//                tempPlayerBuilder.setId(String.valueOf(audioDetails.getAlbumId()));
//                getFeatureInfo(iPlayListGetListener);
//            }
//
//            @Override
//            public void onError(ApiException e) {
//                PlayerLogUtil.log(getClass().getSimpleName(), "getAudioInfo", "error");
//                AlbumPlayItem playItem = new AlbumPlayItem();
//                playItem.setAudioId(audioId);
//                notifyPlayListGetError(iPlayListGetListener, playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_SERVER, e.getCode());
//                notifyPlayListChangeError(playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_SERVER, e.getCode());
//            }
//        });
//    }

    private void updatePlayListContent(int type, ArrayList<PlayItem> playItems, final IPlayListGetListener iPlayListGetListener) {
        updatePlayListContent(type, 0, playItems, iPlayListGetListener);
    }

    private void updatePlayListContent(int type, long audioId, ArrayList<PlayItem> playItems, final IPlayListGetListener iPlayListGetListener) {
        // 最终添加 mPlayItemArrayList
        int needPlayIndex = 0;
        switch (type) {
            case LOAD_DATA:
                needPlayIndex = getNotifyPlayListIndex(playItems, audioId);
                break;
            case LOAD_DATA_NEXT:

                break;
            case LOAD_DATA_PRE:
                needPlayIndex = playItems.size() - 1;
                break;
            case LOAD_DATA_PAGE_AND_PLAY_NEXT_ITEM:
                needPlayIndex = getNotifyPlayListIndex(playItems, mCurPlayItem.getAudioId());
                if (++needPlayIndex >= playItems.size()) {
                    loadNextPage(iPlayListGetListener);
                    return;
                }
                break;
            case LOAD_DATA_PAGE_AND_PLAY_PRE_ITEM:
                needPlayIndex = getNotifyPlayListIndex(playItems, mCurPlayItem.getAudioId());
                if (--needPlayIndex < 0) {
                    loadPrePage(iPlayListGetListener);
                    return;
                }
                break;
            default:
                break;
        }
        if (type == LOAD_DATA_PRE) {
            PlayItem playItem = getCurPlayItem();
            PlayerLogUtil.log(getClass().getSimpleName(), "updatePlayListContent", "current position = " + getCurPosition());
            mPlayItemArrayList.addAll(0, playItems);
            setCurPosition(playItem);
        } else if (type == LOAD_DATA_PAGE || type == LOAD_DATA_PAGE_AND_PLAY_NEXT_ITEM || type == LOAD_DATA_PAGE_AND_PLAY_PRE_ITEM) {
            PlayItem playItem = getPlayItem(mCurPlayItem, playItems);
            PlayerLogUtil.log(getClass().getSimpleName(), "updatePlayListContent", "current position = " + getCurPosition());
            mPlayItemArrayList.clear();
            mPlayItemArrayList.addAll(playItems);
            setCurPosition(playItem);
        } else {
            List<PlayItem> diffItems = getDiffList(mPlayItemArrayList, playItems);
            mPlayItemArrayList.addAll(diffItems);
        }

        PlayerLogUtil.log(getClass().getSimpleName(), "updatePlayListContent", "needPlayIndex = " + needPlayIndex);
        notifyPlayListGet(iPlayListGetListener, playItems.get(needPlayIndex), playItems);
        notifyPlayListChange(playItems);
    }

    private PlayItem getPlayItem(PlayItem playItem, List<PlayItem> playItemList) {
        if (playItem == null) {
            return null;
        }
        PlayItem item = null;
        for (int i = 0; i < playItemList.size(); i++) {
            if (playItem.getAudioId() == playItemList.get(i).getAudioId()) {
                item = playItemList.get(i);
                break;
            }
        }

        return item;
    }

    /**
     * 去重
     * 资讯内容更新比较即时，当拉取下一页的时候可能更新的多条内容，导致拉取重复数据，播放列表显示异常
     */
    public static List<PlayItem> getDiffList(List<PlayItem> fromList,
                                             List<PlayItem> onList) {
        List<PlayItem> result = new ArrayList<>();
        for (PlayItem bean : onList) {
            boolean hasValue = false;
            for (PlayItem item : fromList) {
                if (bean.getAudioId() == item.getAudioId()) {
                    hasValue = true;
                    break;
                }
            }
            if (!hasValue) {
                result.add(bean);
            }
        }
        return result;
    }

    public static boolean isInList(List<PlayItem> fromList,
                                   PlayItem playItem) {
        boolean hasValue = false;
        if (playItem == null) {
            return false;
        }
        for (PlayItem item : fromList) {
            if (playItem.getAudioId() == item.getAudioId()) {
                hasValue = true;
                break;
            }
        }

        return hasValue;
    }

    private void initPlayListInfo(FeatureDetails featureDetails) {
        if (!StringUtil.isEmpty(mPlaylistInfo.getTempId())) {
            mPlaylistInfo.setId(mPlaylistInfo.getTempId());
        }
        mPlaylistInfo.setChildId(null);
        if (featureDetails != null) {
            mPlaylistInfo.setSort(tempPlayerBuilder.getSort());
            mPlaylistInfo.setAlbumName(featureDetails.getFeatureName());
            mPlaylistInfo.setAlbumPic(featureDetails.getCoverImg());
            mPlaylistInfo.setCountNum(featureDetails.getAudioTotal());
            mPlaylistInfo.setFollowedNum(featureDetails.getSubscribeNum());
            mPlaylistInfo.setListenNum(featureDetails.getListenNum());
            mPlaylistInfo.setSourceName(featureDetails.getFeatureName());
            mPlaylistInfo.setSourceLogo(featureDetails.getCoverImg());
            mPlaylistInfo.setBreakPointContinue("1");   //专题播放时永远从最新一个开始播放
        }
    }

    private void updatePlayListInfo(int type, BasePageResult basePageResult) {
        switch (type) {
            case LOAD_DATA_PRE: {
                mPlaylistInfo.setPrePage(basePageResult.getPrePage());
                mPlaylistInfo.setHasPrePage(basePageResult.getHavePre() == PlayerConstants.HAVE_PAGE_DATA);
            }
            break;
            case LOAD_DATA_NEXT: {
                mPlaylistInfo.setNextPage(basePageResult.getNextPage());
                mPlaylistInfo.setHasNextPage(basePageResult.getHaveNext() == PlayerConstants.HAVE_PAGE_DATA);
            }
            break;
            case LOAD_DATA:
            case LOAD_DATA_PAGE_AND_PLAY_NEXT_ITEM:
            case LOAD_DATA_PAGE_AND_PLAY_PRE_ITEM:
            case LOAD_DATA_PAGE:
                mPlaylistInfo.resetNextPage(basePageResult.getNextPage());
                mPlaylistInfo.setHasNextPage(basePageResult.getHaveNext() == PlayerConstants.HAVE_PAGE_DATA);
                mPlaylistInfo.resetPrePage(basePageResult.getPrePage());
                mPlaylistInfo.setHasPrePage(basePageResult.getHavePre() == PlayerConstants.HAVE_PAGE_DATA);
                break;
            default:
                break;
        }
        mPlaylistInfo.setPageIndex(String.valueOf(basePageResult.getNextPage()));
        mPlaylistInfo.setAllSize(basePageResult.getCount());
    }

    private void setPageSize(int pageSize) {
        mPageSize = pageSize;
    }
}
