package com.kaolafm.opensdk.di.module;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.TypeAdapterFactory;
import com.kaolafm.opensdk.Options;
import com.kaolafm.opensdk.di.qualifier.HandlerInterceptor;
import com.kaolafm.opensdk.di.qualifier.HttpInterceptor;
import com.kaolafm.opensdk.di.scope.AppScope;

import java.util.Set;
import java.util.concurrent.TimeUnit;

import dagger.Module;
import dagger.Provides;
import okhttp3.HttpUrl;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory;
import retrofit2.converter.gson.GsonConverterFactory;

/**
 * 提供网络请求相关的第三方框架实例
 *
 * <AUTHOR>
 * @date 2018/7/23
 */
@Module
public class HttpClientModule {

    private static final long TIME_OUT = 10;

    @Provides
    @AppScope
    Retrofit provideRetrofit(OkHttpClient httpClient, HttpUrl httpUrl, Gson gson) {
        return new Retrofit.Builder()
                .baseUrl(httpUrl)
                .client(httpClient)
                .addCallAdapterFactory(RxJava2CallAdapterFactory.create())
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build();
    }

    @Provides
    @AppScope
    OkHttpClient provideOkHttpClient(OkHttpClient.Builder builder) {
        return builder.build();
    }

    @Provides
    @AppScope
    OkHttpClient.Builder provideOkHttpClientBuilder(Interceptor interceptor, @HandlerInterceptor Interceptor handlerInterceptor, @HttpInterceptor Set<Interceptor> interceptorSet/*该参数是包含所有带有@IntoSet注解并返回值为Interceptor的方法返回集合*/, Options options) {
        long timeout = options.httpTimeout();
        if (timeout <= 0) {
            timeout = TIME_OUT;
        }
        OkHttpClient.Builder builder = new OkHttpClient.Builder()
                .connectTimeout(timeout, TimeUnit.SECONDS)
                .readTimeout(timeout, TimeUnit.SECONDS)
                .writeTimeout(timeout, TimeUnit.SECONDS)
                .addNetworkInterceptor(interceptor)
                //不添加到set集合中，而是在这里添加是为了保证该拦截器第一个执行。
                .addInterceptor(handlerInterceptor);
        if (interceptorSet != null) {
            for (Interceptor i : interceptorSet) {
                if (i != null) {
                    builder.addInterceptor(i);
                }
            }
        }
        if (options.interceptor() != null) {
            builder.addInterceptor(options.interceptor());
        }
        return builder;
    }

    /**
     * 在{@link HttpConfigModule}中带有注解@IntoSet且返回值是TypeAdapterFactory的方法会自动加入到该参数Set&lt;TypeAdapterFactory&gt;中
     * @param typeAdapterFactorySet
     * @return
     */
    @Provides
    @AppScope
    Gson provideGson(Set<TypeAdapterFactory> typeAdapterFactorySet) {
        GsonBuilder gsonBuilder = new GsonBuilder()
                //支持将序列化key为object的map,默认只能序列化key为string的map
                .enableComplexMapKeySerialization();
        for (TypeAdapterFactory factory : typeAdapterFactorySet) {
            gsonBuilder.registerTypeAdapterFactory(factory);
        }
        return gsonBuilder.create();
    }

}
