package com.kaolafm.opensdk.http.urlmanager;

import android.text.TextUtils;

import com.kaolafm.opensdk.Options;
import com.kaolafm.opensdk.account.token.RealAccessTokenManager;
import com.kaolafm.opensdk.di.qualifier.DomainMapQualifier;
import com.kaolafm.opensdk.di.qualifier.ParamQualifier;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.core.HttpBeforeHandler;
import com.kaolafm.opensdk.utils.BaseHttpsStrategy;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;
import javax.inject.Provider;

import dagger.Lazy;
import okhttp3.FormBody;
import okhttp3.HttpUrl;
import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.MultipartBody.Part;
import okhttp3.Request;
import okhttp3.Request.Builder;
import okhttp3.RequestBody;
import okio.Buffer;

/**
 * 域名和公共参数管理，用于多域名和请求中域名更换管理。公共参数添加。<br/>
 * 只能替换如http/https://www.xxxxx.com等最基本的host<br/>
 * 如果是http/https://www.xxxxx.com/xxx等则无法替换<br/>
 *
 * <AUTHOR>
 * @date 2018/4/20
 */
@AppScope
public final class UrlManagerImpl implements UrlManager, HttpBeforeHandler {

    private static final String POST = "POST";

    private static final String GLOBAL_DOMAIN_NAME = "global_domain_name";

    @Inject
    @DomainMapQualifier
    Map<String, String> mDomainNames;

    @Inject
    @ParamQualifier
    Map<String, Provider<Map<String, String>>> mCommonParams;

    @Inject
    @AppScope
    Lazy<RealAccessTokenManager> mRealAccessTokenManagerLazy;

    @Inject
    @AppScope
    Options mOptions;

    private final Map<String, RealtimeParamListener> mRealtimeParams = new HashMap<>();

    @Inject
    public UrlManagerImpl() {
    }

    /**
     * 进行baseurl进行网络
     */
    @Override
    public Request processRequest(Request oldRequest) {
        Builder newBuilder = oldRequest.newBuilder();
        HttpUrl oldHttpUrl = oldRequest.url();
        //如果 Url 地址中包含 IDENTIFICATION_IGNORE 标识符, 管理器将不会对此 Url 进行任何切换 BaseUrl 的操作
        String url = oldHttpUrl.toString();
        if (url.contains(IDENTIFICATION_IGNORE)) {
            return pruneIdentification(newBuilder, url);
        }
        //如果有多条DOMAIN_NAME的header，只要最新设置的。
        String domainName = oldRequest.header(DOMAIN_NAME);
        if (!TextUtils.isEmpty(domainName)) {
            newBuilder.removeHeader(DOMAIN_NAME);
        } else {
            domainName = GLOBAL_DOMAIN_NAME;
        }
        HttpUrl httpUrl = fetchDomain(domainName);
        HttpUrl.Builder urlBuilder = newBuilder(oldRequest, oldHttpUrl, httpUrl);
        Map<String, String> params = getCommonParams(domainName);
        if (params != null) {
            //现在公共参数都是直接拼接到请求链接后面的。
            for (String key : params.keySet()) {
                urlBuilder.addQueryParameter(key, params.get(key));
            }
        }
        return newBuilder.url(urlBuilder.build()).build();
    }

    private HttpUrl.Builder newBuilder(Request oldRequest, HttpUrl oldHttpUrl, HttpUrl httpUrl) {
        HttpUrl.Builder builder = oldHttpUrl.newBuilder();
        if (httpUrl != null && !oldHttpUrl.host().equals(httpUrl.host())) {
            builder.host(httpUrl.host());
        }
        String scheme = isUseHttps() ? "https" : "http";
        String protocol = oldRequest.header(HTTPS_PROTOCOL);
        if ("http".equals(protocol) || "https".equals(protocol)) {
            scheme = protocol;
        }
        int port = HttpUrl.defaultPort(scheme);
        if (httpUrl != null) {
            int newPort = httpUrl.port();
            if (newPort > 0 && newPort != port && newPort != HttpUrl.defaultPort(httpUrl.scheme())) {
                port = newPort;
            }
        }
        builder.scheme(scheme).port(port);
        return builder;
    }

    private Map<String, String> getCommonParams(String domainName) {
        //获取固定公共参数
        Map<String, String> params = null;
        if (!mCommonParams.isEmpty()) {
            Provider<Map<String, String>> mapProvider = mCommonParams.get(domainName);
            if (mapProvider != null) {
                params = mapProvider.get();
            }
        }
        //获取实时公共参数
        RealtimeParamListener paramListener = mRealtimeParams.get(domainName);
        if (paramListener != null) {
            Map<String, String> realtimeParams = paramListener.getParam();
            if (params != null) {
                if (realtimeParams != null) {
                    params.putAll(realtimeParams);
                }
            } else {
                params = realtimeParams;
            }
        }
        return params;
    }

    /**
     * 处理post请求公共参数
     */
    private Builder processPost(Request oldRequest, Map<String, String> params) {
        Builder newBuilder;
        String postBodyString = "";
        RequestBody oldBody = oldRequest.body();
        if (oldBody instanceof FormBody) {
            FormBody.Builder formBodyBuilder = new FormBody.Builder();
            if (params != null) {
                for (String key : params.keySet()) {
                    formBodyBuilder.add(key, params.get(key));
                }
            }
            newBuilder = oldRequest.newBuilder();
            RequestBody formBody = formBodyBuilder.build();
            postBodyString = bodyToString(oldRequest.body());
            postBodyString += ((postBodyString.length() > 0) ? "&" : "") + bodyToString(formBody);
            newBuilder.post(RequestBody
                    .create(MediaType.parse("application/x-www-form-urlencoded;charset=UTF-8"), postBodyString));
        } else if (oldBody instanceof MultipartBody) {
            MultipartBody oldBodyMultipart = (MultipartBody) oldBody;
            List<Part> oldPartList = oldBodyMultipart.parts();
            MultipartBody.Builder builder = new MultipartBody.Builder();
            builder.setType(MultipartBody.FORM);
            for (Part part : oldPartList) {
                builder.addPart(part);
            }
            if (params != null) {
                for (String key : params.keySet()) {
                    builder.addPart(RequestBody.create(MediaType.parse("text/plain"), params.get(key)));
                }
            }
            newBuilder = oldRequest.newBuilder();
            newBuilder.post(builder.build());
        } else {
            newBuilder = oldRequest.newBuilder();
        }
        return newBuilder;
    }

    private static String bodyToString(final RequestBody request) {
        try {
            final RequestBody copy = request;
            final Buffer buffer = new Buffer();
            if (copy != null) {
                copy.writeTo(buffer);
            } else {
                return "";
            }
            return buffer.readUtf8();
        } catch (final IOException e) {
            return "did not work";
        }
    }

    private HttpUrl fetchDomain(String domainName) {
        String domain = mDomainNames.get(domainName);
        if (TextUtils.isEmpty(domain)) {
            return null;
        }
        return HttpUrl.parse(domain);
    }

    /**
     * 添加host及其对应的公共参数
     *
     * @param domainName   host的命名标识，一一对应host。一定要与@Headers中一样，<br/>
     *                     如@Headers{{@link #DOMAIN_NAME_HEADER} + {@code domainName}}
     * @param domainUrl    域名host
     * @param commonParams 公共参数Map集合
     */
    public void putDomainAndParams(String domainName, String domainUrl, Map<String, String> commonParams) {
        if (TextUtils.isEmpty(domainName) || TextUtils.isEmpty(domainUrl)) {
            return;
        }
        synchronized (this) {
            if (!mDomainNames.containsKey(domainName) && !mDomainNames.containsValue(domainUrl)) {
                mDomainNames.put(domainName, domainUrl);
            }
            if (!mCommonParams.containsKey(domainName)) {
                if (commonParams != null && !commonParams.isEmpty()) {
                    mCommonParams.put(domainName, () -> commonParams);
                }
            }
        }
    }

    /**
     * 添加host
     *
     * @param domainName host的命名标识，一一对应host。一定要与@Headers中一样，<br/>
     *                   如@Headers{{@link #DOMAIN_NAME_HEADER} + {@code domainName}}
     * @param domainUrl  域名host
     */
    @Override
    public void putDomain(String domainName, String domainUrl) {
        if (!TextUtils.isEmpty(domainName) && !TextUtils.isEmpty(domainUrl)) {
            if (!mDomainNames.containsKey(domainName) && !mDomainNames.containsValue(domainUrl)) {
                synchronized (this) {
                    mDomainNames.put(domainName, domainUrl);
                }
            }
        }
    }

    /**
     * 添加公共参数
     *
     * @param domainName   host的命名标识，一一对应host。一定要与@Headers中一样，<br/>
     *                     如@Headers{{@link #DOMAIN_NAME_HEADER} + {@code domainName}}
     * @param commonParams 公共参数Map集合
     */
    public void putCommonParams(String domainName, Provider<Map<String, String>> commonParams) {
        if (commonParams != null) {
            synchronized (this) {
                if (!mCommonParams.containsKey(domainName) && !mCommonParams.containsValue(commonParams)) {
                    mCommonParams.put(domainName, commonParams);
                }
            }
        }
    }

    /**
     * 添加公共参数
     *
     * @param domainName host的命名标识，一一对应host。一定要与@Headers中一样，<br/>
     *                   如@Headers{{@link #DOMAIN_NAME_HEADER} + {@code domainName}}
     * @param paramName  公共参数
     */
    public void putCommonParams(String domainName, String paramName, String paramValue) {
        if (!TextUtils.isEmpty(paramName) && !TextUtils.isEmpty(paramName)) {
            synchronized (this) {
                Map<String, String> params = mCommonParams.get(domainName).get();
                if (params == null) {
                    params = new HashMap<>();
                }
                params.put(paramName, paramValue);
                Map<String, String> finalParams = params;
                mCommonParams.put(domainName, () -> finalParams);
            }
        }
    }

    /**
     * 添加忽略标识。添加忽略标号的请求不会再动态替换host。
     */
    public String setIdentificationIgnore(String url) {
        return url + IDENTIFICATION_IGNORE;
    }

    /**
     * 移除url中的忽略标识
     */
    private Request pruneIdentification(Builder newBuilder, String url) {
        String[] strings = url.split(IDENTIFICATION_IGNORE);
        StringBuilder sb = new StringBuilder();
        for (String s : strings) {
            sb.append(s);
        }
        return newBuilder.url(sb.toString()).build();
    }

    /**
     * 添加实时回调参数，用于添加那些需要在请求时才传入的参数
     */
    @Override
    public synchronized void addRealtimeParamListener(String domainName, RealtimeParamListener listener) {
        if (!mRealtimeParams.containsKey(domainName)) {
            mRealtimeParams.put(domainName, listener);
        }
    }

    @Override
    public Request onHttpRequestBefore(Interceptor.Chain chain, Request request) {
        return processRequest(request);
    }

    @Override
    public boolean isUseHttps() {
        return mOptions.isUseHttps(BaseHttpsStrategy.REPLACE_URL);
    }
}
