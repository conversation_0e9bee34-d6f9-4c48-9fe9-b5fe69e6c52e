package com.kaolafm.report.event;

import com.kaolafm.report.util.ReportConstants;

/**
 * 按钮点击事件上报 290版本新增
 */
public class ButtonClickReportEvent extends BaseReportEventBean {
    /**
     * 01 sign out  退出登录
     * 02 renew  前往续费
     * 03  click clear  一键清空（收听历史）
     * 04 start  开始使用
     * 05 disagree  不同意并退出
     * 06 banner 活动专区
     */
    public static final String BUTTON_SIGN_OUT = "01";
    public static final String BUTTON_RENEW = "02";
    public static final String BUTTON_CLICK_CLEAR = "03";
    public static final String BUTTON_START = "04";
    public static final String BUTTON_DISAGREE = "05";
    public static final String BUTTON_BANNER = "06";
    public static final String BUTTON_JOIN_NOW = "07";
    //-----------------------------在线电台-------------------------------------
//    01 sign out  退出登录
    public static final String ONLINE_BUTTON_SIGN_OUT = "25001001";
    //     02 renew  前往续费
    public static final String ONLINE_BUTTON_RENEW = "25001003";
    //     03  听迹 一键清空（收听历史）
    public static final String ONLINE_BUTTON_CLICK_CLEAR = "23002001";
    //     04 start  开始使用
    public static final String ONLINE_BUTTON_START = "20001001";
    //     05 disagree  不同意并退出
    public static final String ONLINE_BUTTON_DISAGREE = "20001002";
    //     06 banner 活动专区
    public static final String ONLINE_BUTTON_BANNER = "06";
    //      07  立即加入
    public static final String ONLINE_BUTTON_JOIN_NOW = "25001002";
    //       08  地理位置
    public static final String ONLINE_BUTTON_LOACION_PAGE = "21001005";
    //    s搜索
    public static final String ONLINE_BUTTON_SEARCH_PAGE = "21001006";
    //        09  分类
    public static final String ONLINE_BUTTON_CLASS_PAGE = "21001002";
    //        10 听迹
    public static final String ONLINE_BUTTON_TJ_PAGE = "21001003";
    //         11 活动
    public static final String ONLINE_BUTTON_ACTIVITY_PAGE = "21001004";
    //    活动列表-查看详情按钮
    public static final String ONLINE_BUTTON_ACTIVITY_PAGE_DATAILS = "24001001";
    //         12   分类页面广播tab
    public static final String ONLINE_BUTTON_CLASS_GB = "12";
    //    分类页面广播tab-国家台
    public static final String ONLINE_BUTTON_CLASS_GB_COUNTRIES = "22001001";
    //    分类页面广播tab-省市台
    public static final String ONLINE_BUTTON_CLASS_GB_CITY = "22001002";
    //    分类页面广播tab-分类台
    public static final String ONLINE_BUTTON_CLASS_GB_CLASS = "22001003";
    //          13  分类页面电视tab
    public static final String ONLINE_BUTTON_CALSS_TV = "13";
    //          14  分类页面AI电台tab
    public static final String ONLINE_BUTTON_CLASS_AI = "14";
    //          15  分类页面专辑tab
    public static final String ONLINE_BUTTON_CLASS_ALBUM = "15";
    //           16  听迹页面我的订阅
    public static final String ONLINE_BUTTON_TJ_MY_ADD = "16";
    //           17  听迹页面收听历史
    public static final String ONLINE_BUTTON_TJ_HISTORY = "17";
    //             * 18   播放详情页正倒序按钮
    public static final String ONLINE_BUTTON_PLAY_ODER = "28001001";
    //              * 19  播放详情页一键置顶按钮
    public static final String ONLINE_BUTTON_PLAY_TOP = "28001002";
    //    播放详情页vip免费听
    public static final String ONLINE_BUTTON_PLAY_VIP_LISTEN = "28001003";
    //    播放详情页-专辑/AI电台-购买专辑按钮
    public static final String ONLINE_BUTTON_PLAY_PAY = "28001004";
    //    播放详情页—广播/电视-昨天tab
    public static final String ONLINE_BUTTON_PLAY_YESTERDAY = "28002001";
    //    播放详情页—广播/电视-今天tab
    public static final String ONLINE_BUTTON_PLAY_TODAY = "28002002";
    //    播放详情页—广播/电视-明天tab
    public static final String ONLINE_BUTTON_PLAY_TOMORROW = "28002003";
    //              * 20   用户中心
    public static final String ONLINE_BUTTON_MINE_PAGE = "21001007";
    //               * 21  用户中心页面收听时长
    public static final String ONLINE_BUTTON_MINE_CHANG = "21";
    //                * 22   用户中心页面设置
    public static final String ONLINE_BUTTON_MINE_SETTING = "22";
    //                * 23  消息泡泡详情播放icon
    public static final String ONLINE_BUTTON_MSG_PLAY_ICON = "27003001";
    //                * 24   消息卡片查看详情
    public static final String ONLINE_BUTTON_MSG_DETAILS_SHOW = "27002001";
    //    左侧消息小卡片-关闭按钮
    public static final String ONLINE_BUTTON_MSG_CLOSE = "27002002";
    //                * 25  消息泡泡（消息盒子）
    public static final String ONLINE_BUTTON_MESSAGE_PAGE = "21001008";
    //      * 26  猜你喜欢
    public static final String ONLINE_BUTTON_HOME_PAGE = "21001001";
    //-----------------------------在线电台-------------------------------------
    private String buttonname;

    public ButtonClickReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_BUTTON_CLICK);
    }

    public ButtonClickReportEvent(String buttonname) {
        setEventcode(ReportConstants.EVENT_ID_BUTTON_CLICK);
        this.buttonname = buttonname;
    }

    public String getButtonname() {
        return buttonname;
    }

    public void setButtonname(String buttonname) {
        this.buttonname = buttonname;
    }
}
