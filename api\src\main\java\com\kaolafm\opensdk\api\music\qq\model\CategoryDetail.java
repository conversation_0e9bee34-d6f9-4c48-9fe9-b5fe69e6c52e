package com.kaolafm.opensdk.api.music.qq.model;

import com.google.gson.annotations.SerializedName;

/**
 * 分类标签详细信息，
 *
 * <AUTHOR>
 * @date 2018/4/23
 */

public class CategoryDetail {


    /**
     * category_desc :
     * category_id : 3230
     * category_imgurl :
     * category_is_hot : 0
     * category_is_new : 0
     * category_is_parent : 0
     * category_name : 经典国语
     * category_share_pic : http://y.gtimg.cn/music/common/upload/t_tag_group_set/80361.jpg
     * category_show_detail : 0
     * category_show_type : 1
     * group_id : 2
     */
    @SerializedName("category_desc")
    private String categoryDesc;

    @SerializedName("category_id")
    private int categoryId;

    @SerializedName("category_imgurl")
    private String categoryImgurl;

    @SerializedName("category_is_hot")
    private int categoryIsHot;

    @SerializedName("category_is_new")
    private int categoryIsNew;

    @SerializedName("category_is_parent")
    private int categoryIsParent;

    @SerializedName("category_name")
    private String categoryName;

    @SerializedName("category_share_pic")
    private String categorySharePic;

    @SerializedName("category_show_detail")
    private int categoryShowDetail;

    @SerializedName("category_show_type")
    private int categoryShowType;

    @SerializedName("group_id")
    private int groupId;

    public String getCategoryDesc() {
        return categoryDesc;
    }

    public int getCategoryId() {
        return categoryId;
    }

    public String getCategoryImgurl() {
        return categoryImgurl;
    }

    public int getCategoryIsHot() {
        return categoryIsHot;
    }

    public int getCategoryIsNew() {
        return categoryIsNew;
    }

    public int getCategoryIsParent() {
        return categoryIsParent;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public String getCategorySharePic() {
        return categorySharePic;
    }

    public int getCategoryShowDetail() {
        return categoryShowDetail;
    }

    public int getCategoryShowType() {
        return categoryShowType;
    }

    public int getGroupId() {
        return groupId;
    }

    public void setCategoryDesc(String categoryDesc) {
        this.categoryDesc = categoryDesc;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }

    public void setCategoryImgurl(String categoryImgurl) {
        this.categoryImgurl = categoryImgurl;
    }

    public void setCategoryIsHot(int categoryIsHot) {
        this.categoryIsHot = categoryIsHot;
    }

    public void setCategoryIsNew(int categoryIsNew) {
        this.categoryIsNew = categoryIsNew;
    }

    public void setCategoryIsParent(int categoryIsParent) {
        this.categoryIsParent = categoryIsParent;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public void setCategorySharePic(String categorySharePic) {
        this.categorySharePic = categorySharePic;
    }

    public void setCategoryShowDetail(int categoryShowDetail) {
        this.categoryShowDetail = categoryShowDetail;
    }

    public void setCategoryShowType(int categoryShowType) {
        this.categoryShowType = categoryShowType;
    }

    public void setGroupId(int groupId) {
        this.groupId = groupId;
    }

}
