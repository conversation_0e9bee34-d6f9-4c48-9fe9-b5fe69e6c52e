package com.kaolafm.opensdk.api.ad.model;

import com.google.gson.annotations.SerializedName;

import java.util.Map;

/**
 * 广告曝光场景与广告位映射关系
 *
 * <AUTHOR>
 * @date 2020-01-02
 */
public class AdZoneMapping {


    /**
     * adExposureScene : 1
     * adZoneId : 28
     * extInfo : {}
     */

    /**
     * 广告曝光场景ID
     * 1	开屏
     * 2	专辑下碎片播放切换
     * 3	智能电台碎片播放切换
     * 4	品牌电台碎片播放切换
     * 5	栏目成员封面替换
     * 6	定时
     */
    @SerializedName("adExposureScene")
    private int adExposureScene;

    /**
     * 广告位ID。-1，表示该广告曝光场景下的广告位信息是动态的，智能电台详情、编排位配置广告位或在栏目成员的扩展属性配置广告位；
     */
    @SerializedName("adZoneId")
    private int adZoneId;

    /**
     * 额外信息
     */
    @SerializedName("extInfo")
    private Map<String, Object> extInfo;

    public int getAdExposureScene() {
        return adExposureScene;
    }

    public void setAdExposureScene(int adExposureScene) {
        this.adExposureScene = adExposureScene;
    }

    public int getAdZoneId() {
        return adZoneId;
    }

    public void setAdZoneId(int adZoneId) {
        this.adZoneId = adZoneId;
    }

    public Map<String, Object> getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(Map<String, Object> extInfo) {
        this.extInfo = extInfo;
    }

}
