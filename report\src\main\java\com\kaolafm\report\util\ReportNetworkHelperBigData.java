package com.kaolafm.report.util;

import android.util.Log;

import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.report.api.report.ReportBigDataRequest;
import com.kaolafm.report.api.report.ReportBigDataUtils;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR> on 2019/1/14.
 */

class ReportNetworkHelperBigData {
    private static final String TAG = "ReportNetHelperBigData";
    private static ReportNetworkHelperBigData reportNetworkHelper;

    private ReportNetworkHelperBigData() {
    }

    public static ReportNetworkHelperBigData getInstance() {
        if (reportNetworkHelper == null) {
            synchronized (ReportNetworkHelperBigData.class) {
                if (reportNetworkHelper == null) {
                    reportNetworkHelper = new ReportNetworkHelperBigData();
                }
            }
        }
        return reportNetworkHelper;
    }

    public void request(String json, IRequestCallBack iRequestCallBack) {
        Log.i(TAG, "request,json="+json);
        String data = null;
        try {
            data = ReportBigDataUtils.getData(new JSONObject(json));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        String finalData = data;
        Log.i(TAG, "request,finalData="+finalData);
        new ReportBigDataRequest().getReport(finalData, new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                iRequestCallBack.result(aBoolean);
            }

            @Override
            public void onError(ApiException exception) {
                iRequestCallBack.result(false);
            }
        });

    }


    public interface IRequestCallBack {
        void result(boolean isOk);
    }


}
