package com.kaolafm.opensdk.api.music.qq.model;

import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/4/23
 */

public class MusicSearchResult extends BaseMusicResult<List<Song>> {

    /**
     * cur_num : 2
     * cur_page : 1
     * keyword : 黑小伙 贺西格
     * list : [{"album_id":654389,"album_mid":"002sGFPw0qMEmZ","album_name":"忘忧·马头琴","album_pic":"http://y.gtimg.cn/music/photo_new/T002R120x120M000002sGFPw0qMEmZ.jpg","genre":"Pop 流行","isonly":1,"k_song_id":0,"k_song_mid":"","language":"纯音乐","playable":1,"public_time":"2009-11-03","singer_id":202074,"singer_mid":"002oUKo51ZbHSz","singer_name":"欧尼尔","singer_pic":"http://y.gtimg.cn/music/photo_new/T001R120x120M000002oUKo51ZbHSz.jpg","size_try":0,"song_h5_url":"http://c.y.qq.com/v8/playsong.html?songmid=002b6rjx2qQxFm&ADTAG=opi12345670","song_id":7170327,"song_mid":"002b6rjx2qQxFm","song_name":"黑小伙","song_play_time":287,"song_play_url":"http://isure.stream.qqmusic.qq.com/C200001ZE3la3nFBUK.m4a?vkey=20B1043F4C10A60F785C7F9676A6E7F24A7ED630250AF9855325AE14738B0BF19B7D4CB8343D3C9D2838BEFE8AF7494416FB512FBDCC8DA6&guid=1234713248&fromtag=50&uin=105397017","song_play_url_hq":"","song_play_url_sq":"","song_play_url_standard":"http://isure.stream.qqmusic.qq.com/C400001ZE3la3nFBUK.m4a?vkey=81AAFD1622A03AA2FBE0ACF629A02FE88D6F0D2B2E14E6C3942445FD2B8BC02389EC4D4E2A536E45557EDEF3111485E0010EE28B45E50FAB&guid=1234713248&fromtag=50&uin=105397017","song_size":1807214,"song_size_hq":0,"song_size_sq":0,"song_size_standard":3741071,"try_begin":0,"try_end":0},{"album_id":1827473,"album_mid":"002a4ZaQ4aSi4s","album_name":"争奇斗艳","album_pic":"http://y.gtimg.cn/music/photo_new/T002R120x120M000002a4ZaQ4aSi4s.jpg","genre":"Pop 流行","isonly":0,"k_song_id":0,"k_song_mid":"","language":"纯音乐","playable":1,"public_time":"2017-01-19","singer_id":31344,"singer_mid":"002JghXL0NzNU0","singer_name":"西域胡杨","singer_pic":"http://y.gtimg.cn/music/photo_new/T001R120x120M000002JghXL0NzNU0.jpg","size_try":0,"song_h5_url":"http://c.y.qq.com/v8/playsong.html?songmid=003ejdqV0VdvRh&ADTAG=opi12345670","song_id":200536945,"song_mid":"003ejdqV0VdvRh","song_name":"黑小伙","song_play_time":287,"song_play_url":"http://isure.stream.qqmusic.qq.com/C200003ejdqV0VdvRh.m4a?vkey=A74DD7E07CCF3089179714CCCE3A4ABD2BF51BECD567DF2F3F68B026B40727179D7879E075EC7DDDAABC54D51FA8ABEF263274DA2DB842A2&guid=1234713248&fromtag=50&uin=105397017","song_play_url_hq":"http://isure.stream.qqmusic.qq.com/C600003ejdqV0VdvRh.m4a?vkey=F0CE2DF3329666DAD411422F3D581B45897A130AE045E3AB1BD1EA7E2863FBBD9503A9BBE9E12989CB3DD6FD97B0C68E9C6DE46344208C76&guid=1234713248&fromtag=50&uin=105397017","song_play_url_sq":"","song_play_url_standard":"http://isure.stream.qqmusic.qq.com/C400003ejdqV0VdvRh.m4a?vkey=FA3B2D01B34D38959EA60EC2A5227317C49695A5072C3D2AF28B25E9139BAF6B919FFFF476C4E80547F2E3140FDA108FF0F78CE131CD9A11&guid=1234713248&fromtag=50&uin=105397017","song_size":1807730,"song_size_hq":7131230,"song_size_sq":0,"song_size_standard":3746741,"try_begin":0,"try_end":0}]
     * total_num : 2
     */

    @SerializedName("cur_num")
    private int curNum;

    @SerializedName("cur_page")
    private int curPage;

    @SerializedName("keyword")
    private String keyword;

    @SerializedName("total_num")
    private int totalNum;
}
