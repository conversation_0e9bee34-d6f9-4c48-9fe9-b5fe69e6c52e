package com.kaolafm.opensdk.live;

import android.util.Log;

import com.kaolafm.opensdk.api.live.model.LiveUserEnterChatroomMsg;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.socket.SocketEvent;
import com.kaolafm.opensdk.socket.SocketListener;

import java.util.Map;

/**
 * 直播 - 长连接事件 - 进入聊天室 - 客户端推送事件
 */
public class LiveUserEnterChatroomListener implements SocketListener<LiveUserEnterChatroomMsg> {

    private static final String TAG = "LiveUserEnterChatroom";

    private LiveUserEnterChatroomListener() {
    }

    public static LiveUserEnterChatroomListener INSTANCE = new LiveUserEnterChatroomListener();

    @Override
    public String getEvent() {
        return SocketEvent.LIVE_ENTER_CHATROOM;
    }

    @Override
    public Map<String, Object> getParams(Map<String, Object> params) {
        return params;
    }

    @Override
    public boolean isNeedParams() {
        return true;
    }

    @Override
    public boolean isNeedRequest() {
        return true;
    }

    @Override
    public void onSuccess(LiveUserEnterChatroomMsg liveUserEnterChatroomMsg) {
        Log.e(TAG, "---live   LiveUserEnterChatroomListener onSuccess liveUserEnterMsg=" + liveUserEnterChatroomMsg);
    }

    @Override
    public void onError(ApiException exception) {
        Log.e(TAG, "---live   LiveUserEnterChatroomListener onError exception=" + exception);
    }
}
