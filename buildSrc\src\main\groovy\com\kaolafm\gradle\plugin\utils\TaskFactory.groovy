package com.kaolafm.gradle.plugin.utils

import com.kaolafm.gradle.plugin.tasks.BuildJarTask
import com.kaolafm.gradle.plugin.tasks.BuildProguardTask
import com.kaolafm.gradle.plugin.tasks.BuildSourceJarTask
import com.kaolafm.gradle.plugin.utils.Util
import org.gradle.api.Action
import org.gradle.api.DefaultTask
import org.gradle.api.Project
import org.gradle.api.Task

class TaskFactory {

    static BuildJarTask  createBuildJar(Project project, String flavorName) {
        return  createTaskInternal(project, "build${flavorName.capitalize()}Jar", BuildJarTask.class)
    }

    static BuildProguardTask createBuildProguardTask(Project project, String flavorName) {
        return createTaskInternal(project,"build${flavorName.capitalize()}ProguardJar", BuildProguardTask.class)
    }

    static BuildSourceJarTask createBuildSourceJarTask(Project project, String flavorName) {
        return createTaskInternal(project,"build${flavorName.capitalize()}SourceJar", BuildSourceJarTask.class)
    }

    static Task createUploadTask(Project project, String flavorName) {
        return createTaskInternal(project,"upload${flavorName.capitalize()}", DefaultTask.class)
    }

    private static <T> Task createTaskInternal(Project project, String name, Class<T> type) {
        Task task = project.tasks.findByName(name)
        if (task == null) {
            task = project.tasks.create(name, type)
            task.group = Util.GROUP_NAME
        }
        return task
    }

    static <T> Task createTask(Project project, String name, Class<T> type, Action<? super T> configuration) {
        Task task = project.tasks.findByName(name)
        if (task == null) {
            if (configuration != null) {
                task = project.tasks.create(name, type, configuration)
            }else {
                task = project.tasks.create(name, type)
            }
        }
        return task
    }

    static <T> Task createTask(Project project, String name, Class<T> type) {
        createTask(project, name, type, null)
    }

    static Task create(Project project, String name) {
        Task task = project.tasks.findByName(name)
        if (task == null) {
            task = project.tasks.create(name)
        }
        return task
    }

}