package com.kaolafm.opensdk.demo.purchase;

import android.os.Bundle;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;

import butterknife.BindView;

/**
 * 通用
 *
 */
public abstract class CommonActivity extends BaseActivity {

    @BindView(R.id.rv_common)
    RecyclerView recyclerView;

    protected CommonAdapter adapter;

    @Override
    public int getLayoutId() {
        return R.layout.activity_common;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this);
        recyclerView.setLayoutManager(linearLayoutManager);
        adapter = new CommonAdapter();
        recyclerView.setAdapter(adapter);
        recyclerView.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));
    }
}
