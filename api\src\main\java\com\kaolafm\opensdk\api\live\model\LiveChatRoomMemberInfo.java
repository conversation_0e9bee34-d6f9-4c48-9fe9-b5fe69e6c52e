package com.kaolafm.opensdk.api.live.model;

import android.os.Parcel;
import android.os.Parcelable;

public class LiveChatRoomMemberInfo implements Parcelable {

    // 直播间id
    private Long liveId;

    // 聊天室id
    private Long roomId;

    private String deviceId;

    // 用户账号
    private String account;

    private Integer userType;

    // 用户头像
    private String avatar;

    // 昵称
    private String nickName;

    public Long getLiveId() {
        return liveId;
    }

    public void setLiveId(Long liveId) {
        this.liveId = liveId;
    }

    public Long getRoomId() {
        return roomId;
    }

    public void setRoomId(Long roomId) {
        this.roomId = roomId;
    }

    public String getDeviceId() {
        return deviceId == null ? "" : deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getAccount() {
        return account == null ? "" : account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public String getAvatar() {
        return avatar == null ? "" : avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getNickName() {
        return nickName == null ? "" : nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    @Override
    public String toString() {
        return "LiveChatRoomMemberInfo{" +
                "liveId=" + liveId +
                ", roomId=" + roomId +
                ", deviceId='" + deviceId + '\'' +
                ", account='" + account + '\'' +
                ", userType=" + userType +
                ", avatar='" + avatar + '\'' +
                ", nickName='" + nickName + '\'' +
                '}';
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeValue(this.liveId);
        dest.writeValue(this.roomId);
        dest.writeString(this.deviceId);
        dest.writeString(this.account);
        dest.writeValue(this.userType);
        dest.writeString(this.avatar);
        dest.writeString(this.nickName);
    }

    public LiveChatRoomMemberInfo() {
    }

    protected LiveChatRoomMemberInfo(Parcel in) {
        this.liveId = (Long) in.readValue(Long.class.getClassLoader());
        this.roomId = (Long) in.readValue(Long.class.getClassLoader());
        this.deviceId = in.readString();
        this.account = in.readString();
        this.userType = (Integer) in.readValue(Integer.class.getClassLoader());
        this.avatar = in.readString();
        this.nickName = in.readString();
    }

    public static final Creator<LiveChatRoomMemberInfo> CREATOR = new Creator<LiveChatRoomMemberInfo>() {
        @Override
        public LiveChatRoomMemberInfo createFromParcel(Parcel source) {
            return new LiveChatRoomMemberInfo(source);
        }

        @Override
        public LiveChatRoomMemberInfo[] newArray(int size) {
            return new LiveChatRoomMemberInfo[size];
        }
    };
}
