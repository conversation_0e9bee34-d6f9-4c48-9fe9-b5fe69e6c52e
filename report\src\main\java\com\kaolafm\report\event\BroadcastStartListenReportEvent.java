package com.kaolafm.report.event;

import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> on 2019-07-23.
 */
@Deprecated
public class BroadcastStartListenReportEvent extends BaseReportEventBean{
    public static final String STATUS_LIVING = "1";
    public static final String STATUS_PLAYBACK = "2";

    /**
     * 单曲id（回放id）
     */
    private String audioid;
    /**
     * 节目id（在线广播id）
     */
    private String radioid;
    /**
     * 播放状态 1：直播中；2：回放中
     */
    private String status;

    public BroadcastStartListenReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_BROADCAST_START_LISTEN);
    }

    public String getAudioid() {
        return audioid;
    }

    public void setAudioid(String audioid) {
        this.audioid = audioid;
    }

    public String getRadioid() {
        return radioid;
    }

    public void setRadioid(String radioid) {
        this.radioid = radioid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
