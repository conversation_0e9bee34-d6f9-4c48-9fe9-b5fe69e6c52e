package com.kaolafm.ad.expose;

import com.kaolafm.ad.api.model.Advert;
import com.kaolafm.ad.api.model.AudioImageAdvert;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.error.ApiException;

import javax.inject.Inject;

/**
 * 音图适配类
 *
 * <AUTHOR>
 * @date 2020-02-17
 */
@AppScope
public class CompositeAdapter implements Adapter<CompositeExecutor> {

    private CompositeExecutor mExecutor;

    @Inject
    CompositeAdapter(CompositeExecutor executor) {
        mExecutor = executor;
    }

    @Override
    public boolean accept(Advert advert) {
        return advert instanceof AudioImageAdvert;
    }

    @Override
    public void expose(Advert advert) {
        AudioImageAdvert audioImageAdvert = (AudioImageAdvert) advert;
        mExecutor.getPlayer().expose(audioImageAdvert);
        mExecutor.getImager().expose(audioImageAdvert != null? audioImageAdvert.getImageAdvert(): null);
    }

    @Override
    public void close(Advert advert) {
        AudioImageAdvert audioImageAdvert = (AudioImageAdvert) advert;
        //关闭所有广告的时候，广告会为null。
        mExecutor.getPlayer().close(audioImageAdvert);
        mExecutor.getImager().close(audioImageAdvert != null? audioImageAdvert.getImageAdvert(): null);
    }

    @Override
    public CompositeExecutor getExecutor() {
        return mExecutor;
    }

    @Override
    public void setExecutor(CompositeExecutor compositeExecutor) {
        mExecutor = compositeExecutor;
    }

    @Override
    public void error(String adZoneId, int subtype, ApiException e) {
        //空实现。已经调用过了
    }
}
