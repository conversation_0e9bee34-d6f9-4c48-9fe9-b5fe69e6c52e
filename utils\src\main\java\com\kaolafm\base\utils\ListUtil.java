package com.kaolafm.base.utils;

import android.util.SparseArray;

import java.util.Collection;
import java.util.Map;

/******************************************
 * 类描述: 集合工具类
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2017-11-28 14:52
 ******************************************/

public final class ListUtil {

    public static boolean isEmpty(SparseArray data) {
        return data == null || data.size() == 0;
    }

    public static boolean isEmpty(Collection data) {
        return data == null || data.isEmpty();
    }

    public static boolean isEmpty(Map data) {
        return data == null || data.isEmpty();
    }

    public static boolean isEmpty(Object[] data) {
        return data == null || data.length == 0;
    }
}
