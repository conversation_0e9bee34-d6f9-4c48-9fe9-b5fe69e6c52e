package com.kaolafm.gradle.plugin.tasks

import com.kaolafm.gradle.plugin.model.SDKFlavor
import org.gradle.api.tasks.bundling.Jar

class BuildSourceJarTask extends Jar {

    BuildSourceJarTask() {
    }

    void config(SDKFlavor flavor) {

        this.archiveClassifier.set("sources")
        includeEmptyDirs = false

        flavor.includeSourcePackage.forEach {
            this.include("$it/**/*.java")
        }
        flavor.includeSourceJava.forEach {
            this.include(it)
        }
        flavor.excludeSourcePackage.forEach {
            this.exclude("$it/**/*.java")
        }
        flavor.excludeSourceJava.forEach {
            this.exclude(it)
        }
    }
}