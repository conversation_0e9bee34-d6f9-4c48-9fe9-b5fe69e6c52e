package com.kaolafm.opensdk.api.qrcode.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * <AUTHOR>
 * @date 2023-02-16
 */
public class ConfigQrcode implements Parcelable {

    String imgUrl; //二维码图片地址

    protected ConfigQrcode(Parcel in) {
        imgUrl = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(imgUrl);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<ConfigQrcode> CREATOR = new Creator<ConfigQrcode>() {
        @Override
        public ConfigQrcode createFromParcel(Parcel in) {
            return new ConfigQrcode(in);
        }

        @Override
        public ConfigQrcode[] newArray(int size) {
            return new ConfigQrcode[size];
        }
    };

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }
}
