package com.kaolafm.opensdk.api.media;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.media.model.VideoAlbumDetails;
import com.kaolafm.opensdk.api.media.model.VideoAudioDetails;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.List;

import androidx.annotation.IntDef;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: AlbumRequest.java                                               
 *                                                                  *
 * Created in 2018/8/10 下午2:50                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class VideoFragmentRequest extends BaseRequest {

    private VideoFragmentService mService;


    public VideoFragmentRequest() {
        mService = obtainRetrofitService(VideoFragmentService.class);
    }


    /**
     * 获取视频碎片详情
     *
     * @param albumId  专辑Id
     * @param callback 回调
     */
    public void getFragmentDetails(long albumId, HttpCallback<VideoAudioDetails> callback) {
        doHttpDeal(mService.getDetails(albumId), baseResult -> {
            VideoAudioDetails videoAudioDetails = baseResult.getResult();
            return videoAudioDetails;
        }, callback);
    }


}
