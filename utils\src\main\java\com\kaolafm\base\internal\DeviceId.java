package com.kaolafm.base.internal;

import android.content.Context;
import android.text.TextUtils;

import com.kaolafm.base.utils.DeviceUtil;

/**
 * DeviceId 获取接口，这个是内部使用的，对外暴露的是{@link com.kaolafm.base.utils.DeviceUtil}中对应的借口。
 * 该类对外是混淆的，由于Kradio需要手动设置DeviceId接口，所以需要免混淆该类用于客户端进行覆盖。
 * <AUTHOR>
 * @date 2019-10-21
 */
public final class DeviceId {

    private static String deviceId;

    /**
     * 获取设备的deviceId，这个是SDK对外使用的。
     * @param context
     * @return
     */
    public static String getDeviceId(Context context) {
        if (isIdInvalid(deviceId)) {
            deviceId = DeviceUtil.getImei(context);
        }
        return deviceId;
    }

    private static boolean isIdInvalid(String id) {
        return TextUtils.isEmpty(id) || "0".equals(id) || "null".equalsIgnoreCase(id);
    }

}
