package com.kaolafm.ad.api.model;

import android.os.Parcel;

/**
 * 二次互动广告bean
 *
 * <AUTHOR>
 * @date 2020-01-15
 */
public class InteractionAdvert extends Advert {

    /**
     * 二次互动伴随文字描述
     */
    private String description;

    /**
     * 展示时机。1，广告开始曝光时；2，广告曝光结束；
     */
    private int opportunity;

    /**
     * 互动操作类型。1，显示图片；2，跳转到指定URL；3，表示不支持点击
     */
    private int interactionType;

    /**
     * 点击Icon需要显示的图片URL或者需要跳转的URL
     */
    private String destUrl;

    /**
     * 本地缓存的图片路径
     */
    private String localImagePath;

    /**
     * 跳转图片的宽
     */
    private int width;

    /**
     * 跳转图片的高
     */
    private int height;

    public InteractionAdvert() {
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getOpportunity() {
        return opportunity;
    }

    public void setOpportunity(int opportunity) {
        this.opportunity = opportunity;
    }

    public int getInteractionType() {
        return interactionType;
    }

    public void setInteractionType(int interactionType) {
        this.interactionType = interactionType;
    }

    public String getDestUrl() {
        return destUrl;
    }

    public void setDestUrl(String destUrl) {
        this.destUrl = destUrl;
    }

    public String getLocalImagePath() {
        return localImagePath;
    }

    public void setLocalImagePath(String localImagePath) {
        this.localImagePath = localImagePath;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        super.writeToParcel(dest, flags);
        dest.writeString(this.description);
        dest.writeInt(this.opportunity);
        dest.writeInt(this.interactionType);
        dest.writeString(this.destUrl);
        dest.writeString(this.localImagePath);
        dest.writeInt(this.width);
        dest.writeInt(this.height);
    }

    protected InteractionAdvert(Parcel in) {
        super(in);
        this.description = in.readString();
        this.opportunity = in.readInt();
        this.interactionType = in.readInt();
        this.destUrl = in.readString();
        this.localImagePath = in.readString();
        this.width = in.readInt();
        this.height = in.readInt();
    }

    public static final Creator<InteractionAdvert> CREATOR = new Creator<InteractionAdvert>() {
        @Override
        public InteractionAdvert createFromParcel(Parcel source) {
            return new InteractionAdvert(source);
        }

        @Override
        public InteractionAdvert[] newArray(int size) {
            return new InteractionAdvert[size];
        }
    };
}
