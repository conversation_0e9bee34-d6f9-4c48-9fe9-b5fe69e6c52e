package com.kaolafm.opensdk.api.media.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class ToneQualityResponse implements Parcelable {
    /**
     * 音质列表
     */
    @SerializedName("bitrate")
    private List<ToneQuality> bitrate;

    public List<ToneQuality> getBitrate() {
        return bitrate;
    }

    public void setBitrate(List<ToneQuality> bitrate) {
        this.bitrate = bitrate;
    }

    protected ToneQualityResponse(Parcel in) {
        bitrate = in.createTypedArrayList(ToneQuality.CREATOR);
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeTypedList(bitrate);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<ToneQualityResponse> CREATOR = new Creator<ToneQualityResponse>() {
        @Override
        public ToneQualityResponse createFromParcel(Parcel in) {
            return new ToneQualityResponse(in);
        }

        @Override
        public ToneQualityResponse[] newArray(int size) {
            return new ToneQualityResponse[size];
        }
    };

    @Override
    public String toString() {
        return "SoundQualityResponse{" +
                "bitrate=" + bitrate +
                '}';
    }
}
