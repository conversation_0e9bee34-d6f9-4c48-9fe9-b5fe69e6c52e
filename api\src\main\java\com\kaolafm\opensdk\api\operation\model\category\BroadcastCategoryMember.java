package com.kaolafm.opensdk.api.operation.model.category;

/**
 * 人工运营分类成员：在线广播
 */
public class BroadcastCategoryMember extends CategoryMember {

    /** 在线广播资源id*/
    private long broadcastId;

    /** 在线广播的播放次数*/
    private int playTimes;

    /** 广播频率 */
    private String freq;

    /** 广播内容类型（音乐，交通，新闻等）*/
    private int broadcastSort;

    public long getBroadcastId() {
        return broadcastId;
    }

    public void setBroadcastId(long broadcastId) {
        this.broadcastId = broadcastId;
    }

    public int getPlayTimes() {
        return playTimes;
    }

    public void setPlayTimes(int playTimes) {
        this.playTimes = playTimes;
    }

    public String getFreq() {
        return freq;
    }

    public void setFreq(String freq) {
        this.freq = freq;
    }

    public int getBroadcastSort() {
        return broadcastSort;
    }

    public void setBroadcastSort(int broadcastSort) {
        this.broadcastSort = broadcastSort;
    }

    @Override
    public String toString() {
        return "BroadcastCategoryMember{" +
                "broadcastId=" + broadcastId +
                ", playTimes=" + playTimes +
                ", freq='" + freq + '\'' +
                ", broadcastSort=" + broadcastSort +
                '}';
    }
}
