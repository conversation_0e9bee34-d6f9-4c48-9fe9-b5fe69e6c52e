package com.kaolafm.report.util;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.report.model.ReportBean;
import com.kaolafm.report.model.ReportTask;

import java.util.LinkedList;

import io.reactivex.Single;

/**
 * <AUTHOR> on 2019/1/10.
 */

public class ReportShenCeTaskHelper {
    private static ReportShenCeTaskHelper reportShenCeTaskHelper;
    private boolean isRunning = false;
    LinkedList<ReportTask> reportTaskLinkedList;
    private boolean isClose = false;

    private ReportShenCeTaskHelper() {
        reportTaskLinkedList = new LinkedList<>();
        ReportShenCeTimerManager.getInstance().init();
    }

    public static ReportShenCeTaskHelper getInstance() {
        if (reportShenCeTaskHelper == null) {
            synchronized (ReportShenCeTaskHelper.class) {
                if (reportShenCeTaskHelper == null) {
                    reportShenCeTaskHelper = new ReportShenCeTaskHelper();
                }
            }
        }
        return reportShenCeTaskHelper;
    }

    public synchronized void insertTask(ReportTask reportTask) {
        reportTaskLinkedList.add(reportTask);

        if (!isRunning) {
            isRunning = true;
            run();
        }
    }

    public void taskDone() {
        run();
    }

    public synchronized ReportTask getTask() {
        if (reportTaskLinkedList.size() <= 0) {
            return null;
        }
        return reportTaskLinkedList.pop();
    }

    public void run() {
        ReportTask reportTask = getTask();
        if (reportTask == null) {
            isRunning = false;
            return;
        }

        switch (reportTask.getType()) {
            case ReportConstants.TASK_TYPE_INSERT: {
                Single<Long> single = reportTask.getSingleTask();
                if (single != null) {
                    single.subscribe(count -> {
                        if (count >= ReportConstants.READ_DATA_BASE_MAX_COUNT && !isHasSendTask()) {
                            ReportShenCeTimerManager.getInstance().addSendTask();
                        }
                        taskDone();
                    }, throwable -> {
                        taskDone();
                    });
                }
            }
            break;
            case ReportConstants.TASK_TYPE_SEND_DATA: {
                Single<ReportBean> single = reportTask.getSingleTask();
                if (single != null) {
                    single.subscribe(reportBean -> {
                        if (reportBean != null && !StringUtil.isEmpty(reportBean.getReportValue()) && !ListUtil.isEmpty(reportBean.getIdList())) {
                            ReportUploadShenCeTask reportUploadShenCeTask = new ReportUploadShenCeTask(reportBean);
                            reportUploadShenCeTask.report();
                        } else {
                            taskDone();
                        }
                    }, throwable -> {
                        taskDone();
                    });
                }
            }
            break;
            case ReportConstants.TASK_TYPE_DELETE: {
                Single<Long> single = reportTask.getSingleTask();
                if (single != null) {
                    single.subscribe(count -> {
                        if (count > ReportConstants.READ_DATA_BASE_MAX_COUNT && !isHasSendTask()) {
                            ReportShenCeTimerManager.getInstance().addSendTask();
                        }
                        taskDone();
                    }, throwable -> {
                        taskDone();
                    });
                }
            }
            break;
            default:
                break;
        }
    }

    /**
     * 队列里面是否还有未发送的任务
     *
     * @return
     */
    public boolean isHasSendTask() {
        if (reportTaskLinkedList.size() <= 0) {
            return false;
        }

        for (int i = 0; i < reportTaskLinkedList.size(); i++) {
            ReportTask reportTask = reportTaskLinkedList.get(i);
            if (reportTask == null) {
                continue;
            }
            if (reportTask.getType() == ReportConstants.TASK_TYPE_SEND_DATA) {
                return true;
            }
        }

        return false;
    }

    public void release() {
        isClose = true;
        reportTaskLinkedList.clear();
    }

}
