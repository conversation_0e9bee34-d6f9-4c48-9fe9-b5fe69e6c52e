package com.kaolafm.opensdk.demo;

import android.os.Bundle;
import androidx.fragment.app.FragmentActivity;

/**
 * Activity接口，只是为了规范方法。
 * <AUTHOR>
 * @date 2018/4/13
 */

public interface IActivity {

    /**
     * 初始化 View,如果initView返回0,则不会调用{@link FragmentActivity#setContentView(int)}
     *
     * @return
     */
    int getLayoutId();

    /**
     * view相关操作
     *
     * @param savedInstanceState
     */
    void initView(Bundle savedInstanceState);

    /**
     * 初始化必要数据
     */
    void initArgs();

    /**
     * 获取数据
     */
    void initData();

    /**
     * 显示加载
     */
    void showLoading();

    /**
     * 隐藏加载
     */
    void hideLoading();
}
