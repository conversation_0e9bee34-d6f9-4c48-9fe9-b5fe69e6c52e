package com.kaolafm.opensdk.api.media;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.media.model.AIAudioDetails;
import com.kaolafm.opensdk.api.media.model.RadioDetails;

import java.util.List;
import java.util.Map;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Query;
import retrofit2.http.QueryMap;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: RadioService.java                                               
 *                                                                  *
 * Created in 2018/8/10 下午5:04                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
/*package*/ interface RadioService {

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_RADIO_DETAILS)
    Single<BaseResult<RadioDetails>> getRadioDetails(@Query("rid") long radioId);


    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_RADIO_LIST)
    Single<BaseResult<List<AIAudioDetails>>> getPlaylist(@QueryMap Map<String, Object> params);

}
