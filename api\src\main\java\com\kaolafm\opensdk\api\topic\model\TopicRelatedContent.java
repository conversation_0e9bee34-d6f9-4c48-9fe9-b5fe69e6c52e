package com.kaolafm.opensdk.api.topic.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

/**
 * 话题相关内容
 *
 * <AUTHOR>
 * @date 2019-03-18
 */
public class TopicRelatedContent implements Parcelable {
    /**
     * 标题
     */
    @SerializedName("title")
    private String title;
    /**
     * 标签
     */
    @SerializedName("tag")
    private String tag;
    /**
     * 图片
     */
    @SerializedName("img")
    private String img;
    /**
     * 站内跳转地址
     */
    @SerializedName("link")
    private String link;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    protected TopicRelatedContent(Parcel in) {
        title = in.readString();
        tag = in.readString();
        img = in.readString();
        link = in.readString();
    }

    public static final Creator<TopicRelatedContent> CREATOR = new Creator<TopicRelatedContent>() {
        @Override
        public TopicRelatedContent createFromParcel(Parcel in) {
            return new TopicRelatedContent(in);
        }

        @Override
        public TopicRelatedContent[] newArray(int size) {
            return new TopicRelatedContent[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(title);
        dest.writeString(tag);
        dest.writeString(img);
        dest.writeString(link);
    }

}
