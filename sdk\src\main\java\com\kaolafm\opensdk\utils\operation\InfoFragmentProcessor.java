package com.kaolafm.opensdk.utils.operation;

import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.operation.model.category.AlbumCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.CategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.InfoFragmentCategoryMember;
import com.kaolafm.opensdk.api.operation.model.column.InfoFragmentDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.InfoFragmentDetailColumnMember;

import javax.inject.Inject;

/**
 * 资讯碎片 分类/栏目成员处理类
 *
 * <AUTHOR>
 * @date 2018/9/21
 */

public class InfoFragmentProcessor implements IOperationProcessor {

    @Inject
    public InfoFragmentProcessor() {
    }

    @Override
    public boolean accept(CategoryMember member) {
        return member instanceof InfoFragmentCategoryMember;
    }

    @Override
    public boolean accept(ColumnMember member) {
        return member instanceof InfoFragmentDetailColumnMember;
    }

    @Override
    public long getId(CategoryMember member) {
        return ((InfoFragmentCategoryMember) member).getAlbumId();
    }

    @Override
    public long getId(ColumnMember member) {
        return ((InfoFragmentDetailColumnMember) member).getAlbumId();
    }

    @Override
    public long getListenNum(CategoryMember member) {
        return ((InfoFragmentCategoryMember) member).getPlayTimes();
    }

    @Override
    public int getType(CategoryMember member) {
        return ResType.TYPE_INFO_FRAGMENT;
    }

    @Override
    public int getType(ColumnMember member) {
        return ResType.TYPE_INFO_FRAGMENT;
    }

    @Override
    public void play(CategoryMember member) {
//        PlayerManager.getInstance(OpenSDK.getInstance().getContext()).playAlbum(getId(member));
    }

    @Override
    public void play(ColumnMember member) {
//        PlayerManager.getInstance(OpenSDK.getInstance().getContext()).playAlbum(getId(member));
    }
}
