package com.kaolafm.opensdk.http.socket;

/**
 * 数据包
 * <AUTHOR>
 */
public class Packet<T> {

    /**
     * 这个数值不能更改，是和服务端对应的
     */
    public static final int OPEN = 0;
    public static final int CLOSE = 1;
    public static final int PING = 2;
    public static final int PONG = 3;
    public static final int MESSAGE = 4;
    public static final int UPGRADE = 5;
    public static final int NOOP = 6;
    public static final int ERROR = -1;

    public T data;
    public int type = -1;
    public int id = -1;
    public String nsp;
    public int attachments;
    public String query;


    public Packet(int type) {
        this(type, null);
    }

    public Packet(int type, T data) {
        this.type = type;
        this.data = data;
    }

    @Override
    public String toString() {
        return "Packet{" +
                "data=" + data +
                ", type=" + type +
                ", id=" + id +
                ", nsp='" + nsp + '\'' +
                ", attachments=" + attachments +
                ", query='" + query + '\'' +
                '}';
    }
}
