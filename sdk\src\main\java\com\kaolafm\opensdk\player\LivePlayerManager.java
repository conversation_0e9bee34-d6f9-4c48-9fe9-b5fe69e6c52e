package com.kaolafm.opensdk.player;

import android.text.TextUtils;

import com.kaolafm.base.utils.NetworkUtil;
import com.kaolafm.opensdk.OpenSDK;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.item.LivePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
//import com.kaolafm.sdk.core.util.PlayerBeanUtil;
//import com.kaolafm.sdk.vehicle.GeneralCallback;
//import com.kaolafm.sdk.vehicle.KlSdkVehicle;

import java.util.ArrayList;
import java.util.List;

/******************************************
 * 类描述: 直播控制管理类
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-04-09 09:53
 ******************************************/

public final class LivePlayerManager {

    /**
     * 直播播放器是否可用 true为是，false为否 （此boolean值主要目的用于区分当前播放器播放的是否为直播相关内容）
     */
    private boolean isLiveBroadcastPlayerEnable;


    private PlayItem mPlayItem;

    private List<IPlayerStateListener> mPlayerStateListeners = new ArrayList<>();
    private PlayerManager mPlayer;

    private LivePlayerManager() {
        mPlayer = PlayerManager.getInstance();
    }

    private static class PLAYER_LIVE_BROADCAST_MANAGER_INSTANCE {
        private static LivePlayerManager PLAYER_LIVE_BROADCAST_MANAGER = new LivePlayerManager();
    }

    public static LivePlayerManager getInstance() {
        return PLAYER_LIVE_BROADCAST_MANAGER_INSTANCE.PLAYER_LIVE_BROADCAST_MANAGER;
    }

    public void start(PlayItem playItem) {
        start(playItem, true);
    }

    public void start() {

    }

    public PlayItem getPlayItem() {
        return mPlayItem;
    }

    /**
     * 播放指定的PlayItem
     *
     * @param playItem 要播放的PlayItem
     */
    public void play(PlayItem playItem) {
        start(playItem, true);
    }


    public String getRadioId() {
        if (mPlayItem != null) {
            return String.valueOf(((LivePlayItem)mPlayItem).getLiveId());
        }
        return null;
    }


    private void start(PlayItem playItem, boolean isAutoPlay) {
        if (playItem == null || TextUtils.isEmpty(playItem.getPlayUrl())) {
            return;
        }
        mPlayItem = playItem;
        enablePlayer();
        playItem.setPosition(0);
        mPlayer.start(new PlayerBuilder().setType(ResType.TYPE_LIVE).setId(String.valueOf(((LivePlayItem)playItem).getLiveId())));
    }

    public void play() {
        initCacheVar();
        if (checkBinderIsNull()) {
            return;
        }
        mPlayer.play();
    }

    public void pause() {
        initCacheVar();
        if (checkBinderIsNull()) {
            return;
        }
        mPlayer.pause();
    }

    public PlayItem getCurrentPlayItem() {
        return mPlayItem;
    }

    public void stop() {
        initCacheVar();
        mPlayer.stop();
    }

    public void reset() {
        initCacheVar();
        if (checkBinderIsNull()) {
            return;
        }
        mPlayer.reset();
    }

    public void release() {
        mCurrentPosition = 0;
        mPlayer.destroy();
    }

    public void switchPlayerStatus() {
        initCacheVar();

        if (checkBinderIsNull()) {
            return;
        }
        if (isPlaying()) {
            pause();
        } else {
            if (canReStartPlayer()) {
                start();
            } else {
                play();
            }
        }
    }

    public boolean isPlaying() {
        if (checkBinderIsNull()) {
            return false;
        }
        return mPlayer.isPlaying() && isLiveBroadcastPlayerEnable;
    }

    public void seek(int position) {
        if (checkBinderIsNull() || !NetworkUtil.isNetworkAvailable(OpenSDK.getInstance().getContext())) {
            return;
        }
        mPlayer.seek(position);
    }

    public void playPre() {
        //Live, do nothing
    }

    public void playNext() {
        //Live, do nothing
    }

    public void destroy() {
        initCacheVar();

        removeSelfPlayerStateListener();
        if (isLiveBroadcastPlayerEnable) {
        }
//        if (mGetContentGeneralCallbacks != null && mGetContentGeneralCallbacks.size() > 0) {
//            mGetContentGeneralCallbacks.clear();
//        }
        //  stopReportLivingPlay();
    }

    public void enablePlayer() {
        if (isLiveBroadcastPlayerEnable) {
            return;
        }
        isLiveBroadcastPlayerEnable = true;
        addSelfPlayerStateListener();
        disableOtherPlayer();
    }

    public void disableOtherPlayer() {
//        MusicPlayerManager musicPlayerManager = MusicPlayerManager.getInstance();
//        if (musicPlayerManager.isPlayerEnable()) {
//            musicPlayerManager.disablePlayer();
//        }
    }

    public void setVolume(float leftVolume, float rightVolume) {
        if (checkBinderIsNull()) {
            return;
        }
        mPlayer.setMediaVolume(leftVolume, rightVolume);
    }

    public void setUsageAndContentType(int usage, int contentType) {
        if (checkBinderIsNull()) {
            return;
        }
        mPlayer.setUsageAndContentType(usage, contentType);
    }

    public boolean hasNext() {
        return false;
    }

    public boolean hasPre() {
        return false;
    }

    public void disablePlayer() {
        isLiveBroadcastPlayerEnable = false;
        removeSelfPlayerStateListener();
    }

    public boolean isPlayerEnable() {
        return isLiveBroadcastPlayerEnable;
    }

    /**
     * 校验播放器Binder是否为空 如果为空则不进行与底层播放器相关的任何操作
     *
     * @return true为空，false为否
     */
    private boolean checkBinderIsNull() {
        return false;
    }

    private void addSelfPlayerStateListener() {
        mPlayer.addPlayControlStateCallback(mPlayerStateListener);
    }

    public void removeSelfPlayerStateListener() {
        mPlayer.removePlayControlStateCallback(mPlayerStateListener);
    }

    /**
     * 移除播放器状态监听
     */
    public void removePlayerStateListener(IPlayerStateListener playerStateListener) {
        // stopReportLivingPlay();
        if (checkBinderIsNull()) {
            return;
        }
        mPlayerStateListeners.remove(playerStateListener);
    }

    /**
     * 新增播放器状态监听
     */
    public void addPlayerStateListener(IPlayerStateListener playerStateListener) {
        if (!mPlayerStateListeners.contains(playerStateListener)) {
            mPlayerStateListeners.add(playerStateListener);
        }
    }

    private long mCurrentPosition;
    /**
     * 当前单曲播放总时长 单位ms
     */
    private long mAudioPlayedTime;
    private IPlayerStateListener mPlayerStateListener = new IPlayerStateListener() {
        private PlayItem mPlayItem = new LivePlayItem();

        @Override
        public void onIdle(PlayItem playItem) {
            if (isLiveBroadcastPlayerEnable) {
                mCurrentPosition = 0;
                for (IPlayerStateListener listener : mPlayerStateListeners) {
                    if (listener != null) {
                        listener.onIdle(playItem);
                    }
                }
            }
        }

        @Override
        public void onPlayerPreparing(PlayItem playItem) {
            if (isLiveBroadcastPlayerEnable) {
                mCurrentPosition = 0;
                for (IPlayerStateListener listener : mPlayerStateListeners) {
                    if (listener != null) {
                        listener.onPlayerPreparing(playItem);
                    }
                }
            }
        }

        @Override
        public void onPlayerPreparingComplete(PlayItem playItem) {
            if (isLiveBroadcastPlayerEnable) {
                mCurrentPosition = 0;
                for (IPlayerStateListener listener : mPlayerStateListeners) {
                    if (listener != null) {
                        listener.onPlayerPreparingComplete(playItem);
                    }
                }
            }
        }

        @Override
        public void onPlayerPlaying(PlayItem playItem) {
            for (IPlayerStateListener listener : mPlayerStateListeners) {
                if (listener != null) {
                    listener.onPlayerPlaying(playItem);
                }
            }
        }

        @Override
        public void onProgress(PlayItem playItem, long position, long duration) {
            if (isLiveBroadcastPlayerEnable) {
                if (mCurrentPosition != 0) {
                    long playTime = position - mCurrentPosition;
                    if (playTime > 0) {
                        mAudioPlayedTime += playTime;
                    }
                }
                mCurrentPosition = position;
                for (IPlayerStateListener listener : mPlayerStateListeners) {
                    if (listener != null) {
                        listener.onProgress(playItem,  position, duration);
                    }
                }
            }
        }

        @Override
        public void onPlayerPaused(PlayItem playItem) {
            if (isLiveBroadcastPlayerEnable) {
                mCurrentPosition = 0;
                for (IPlayerStateListener listener : mPlayerStateListeners) {
                    if (listener != null) {
                        listener.onPlayerPaused(playItem);
                    }
                }
            }
        }

        @Override
        public void onPlayerFailed(final PlayItem playItem, int what, int extra) {
            if (isLiveBroadcastPlayerEnable) {
                mCurrentPosition = 0;
                for (IPlayerStateListener listener : mPlayerStateListeners) {
                    if (listener != null) {
                        listener.onPlayerFailed(playItem, what, extra);
                    }
                }
            }
        }

        @Override
        public void onPlayerEnd(PlayItem playItem) {
            if (isLiveBroadcastPlayerEnable) {
                mCurrentPosition = 0;
                if (!NetworkUtil.isNetworkAvailable(OpenSDK.getInstance().getContext())) {
                    return;
                }
                for (IPlayerStateListener listener : mPlayerStateListeners) {
                    if (listener != null) {
                        listener.onPlayerEnd(playItem);
                    }
                }
            }
        }

        @Override
        public void onSeekStart(PlayItem playItem) {
            if (isLiveBroadcastPlayerEnable) {
                mCurrentPosition = 0;
                for (IPlayerStateListener listener : mPlayerStateListeners) {
                    if (listener != null) {
                        listener.onSeekStart(playItem);
                    }
                }
            }
        }

        @Override
        public void onSeekComplete(PlayItem playItem) {
            for (IPlayerStateListener listener : mPlayerStateListeners) {
                if (listener != null) {
                    listener.onSeekComplete(playItem);
                }
            }
        }

        @Override
        public void onBufferingStart(PlayItem playItem) {
            if (isLiveBroadcastPlayerEnable) {
                mCurrentPosition = 0;
                for (IPlayerStateListener listener : mPlayerStateListeners) {
                    if (listener != null) {
                        listener.onBufferingStart(playItem);
                    }
                }
            }
        }

        @Override
        public void onBufferingEnd(PlayItem playItem) {
            if (isLiveBroadcastPlayerEnable) {
                for (IPlayerStateListener listener : mPlayerStateListeners) {
                    if (listener != null) {
                        listener.onBufferingEnd(playItem);
                    }
                }
            }
        }

        @Override
        public void onDownloadProgress(PlayItem playItem, long progress, long total) {
        }
    };

    /**
     * 当前播放器是否可以重启播放
     *
     * @return true为是，false为否
     */
    public boolean canReStartPlayer() {
        boolean canReStart = mPlayer.getPlayStatus() == PlayerConstants.TYPE_PLAYER_PAUSED;
        PlayItem playItem = mPlayer.getCurPlayItem();
        if (playItem != null) {
            // 此段添加目的就是为了防止播放的节目处于暂停状态的直播节目
            canReStart = canReStart || playItem.isLiving();
        }
        return canReStart;
    }

//    private ArrayList<GeneralCallback> mGetContentGeneralCallbacks;
//
//    /**
//     * 添加播放一个音频获取内容结果监听事件
//     *
//     * @param callback
//     */
//    public void addGetContentListener(GeneralCallback<Boolean> callback) {
//        if (mGetContentGeneralCallbacks == null) {
//            mGetContentGeneralCallbacks = new ArrayList<>();
//        }
//        if (mGetContentGeneralCallbacks.contains(callback)) {
//            return;
//        }
//        mGetContentGeneralCallbacks.add(callback);
//    }
//
//    /**
//     * 移除播放一个音频获取内容结果监听事件
//     *
//     * @param callback
//     */
//    public void removeGetContentListener(GeneralCallback<Boolean> callback) {
//        if (mGetContentGeneralCallbacks == null) {
//            return;
//        }
//        mGetContentGeneralCallbacks.remove(callback);
//    }

    /**
     * 添加PlayerService未启动之前添加的所有监听
     */
    void addAllPlayerListener() {
        if (checkBinderIsNull()) {
            return;
        }
//        try {
//            mPlayerBinder.addPlayerStateListener(mPlayerStateListener);
//            for (int i = 0, size = mUnAddedPlayerStateListeners.size(); i < size; i++) {
//                IPlayerStateListener listener = mUnAddedPlayerStateListeners.get(i);
//                if (listener == null) {
//                    continue;
//                }
//                mPlayerBinder.addPlayerStateListener(listener);
//            }
//            mUnAddedPlayerStateListeners.clear();
//        } catch (Throwable e) {
//            e.printStackTrace();
//        }
    }

    /**
     * 添加点播播放状态回调 这个是重复的
     *
     * @param iPlayerStateListener
     */
    public void addIPlayerStateListener(IPlayerStateListener iPlayerStateListener) {
        addPlayerStateListener(iPlayerStateListener);
    }

    /**
     * 移除点播播放状态回调
     *
     * @param iPlayerStateListener
     */
    public void removeIPlayerStateListener(IPlayerStateListener iPlayerStateListener) {
        removePlayerStateListener(iPlayerStateListener);
    }

//    private void notifyGetContent(boolean result) {
//        if (mGetContentGeneralCallbacks == null) {
//            return;
//        }
//        ArrayList<GeneralCallback> generalCallbacks = (ArrayList<GeneralCallback>) mGetContentGeneralCallbacks.clone();
//        for (int i = 0, size = generalCallbacks.size(); i < size; i++) {
//            GeneralCallback callback = generalCallbacks.get(i);
//            if (callback == null) {
//                continue;
//            }
//            callback.onResult(result);
//        }
//    }

    private void initCacheVar() {
        mCurrentPosition = 0;
    }

}
