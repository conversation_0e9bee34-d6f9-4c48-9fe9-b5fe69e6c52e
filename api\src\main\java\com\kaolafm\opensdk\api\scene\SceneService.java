package com.kaolafm.opensdk.api.scene;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;

import io.reactivex.Single;
import okhttp3.RequestBody;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;

/**
 * <AUTHOR>
 **/
interface  SceneService {

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @POST(KaolaApiConstant.GET_SCENE_INFO)
    Single<BaseResult<SceneInfo>> getSceneInfo(@Body RequestBody requestBody);
}
