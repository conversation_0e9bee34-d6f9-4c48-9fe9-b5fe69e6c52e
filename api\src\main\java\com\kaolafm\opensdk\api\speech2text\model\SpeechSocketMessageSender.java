package com.kaolafm.opensdk.api.speech2text.model;

import java.io.Serializable;
import java.util.Arrays;

/**
 * 语音转文字服务数据发送模型
 */
public class SpeechSocketMessageSender extends BaseSpeechSocketMessage {

    /**
     * 消息id
     * 32位UUID字符串,服务器返回没有该参数
     */
    private String messageId;

    /**
     * 录音配置参数
     * 发送开始事件时需要赋值
     */
    private StartSendMessageParams params;

    /**
     * 音频数据
     * 上传音频数据中时需要赋值
     * 建议每40ms 发送40ms 时长（即1:1实时率）的数据包，对应 pcm 大小为：8k 采样率640字节，16k 采样率1280字节
     */
    private byte[] voice;

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public StartSendMessageParams getStartSendMessageParams() {
        return params;
    }

    public void setStartSendMessageParams(StartSendMessageParams params) {
        this.params = params;
    }

    public byte[] getVoice() {
        return voice;
    }

    public void setVoice(byte[] voice) {
        this.voice = voice;
    }

    public static class StartSendMessageParams implements Serializable {
        /**
         * 音频编码格式
         * 支持pcm和wav，默认是pcm（无压缩的pcm文件或wav文件），16bit
         */
        private String format;
        /**
         * 采样率
         * 默认是16000 Hz。目前只支持8000HZ和16000HZ
         */
        private int sampleRate;

        public String getFormat() {
            return format;
        }

        public void setFormat(String format) {
            this.format = format;
        }

        public int getSampleRate() {
            return sampleRate;
        }

        public void setSampleRate(int sampleRate) {
            this.sampleRate = sampleRate;
        }

        @Override
        public String toString() {
            return "StartSendMessageParams{" +
                    "format='" + format + '\'' +
                    ", sampleRate=" + sampleRate +
                    '}';
        }
    }


    @Override
    public String toString() {
        return "SpeechSocketMessageSender{" +
                "taskId='" + getTaskId() + '\'' +
                ", messageId='" + messageId + '\'' +
                ", startSendMessageParams=" + params +
                ", voice=" + Arrays.toString(voice) +
                '}';
    }
}
