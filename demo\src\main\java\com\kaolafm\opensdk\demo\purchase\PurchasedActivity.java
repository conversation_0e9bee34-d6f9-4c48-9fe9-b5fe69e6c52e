package com.kaolafm.opensdk.demo.purchase;

import android.os.Bundle;

import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.purchase.PurchaseRequest;
import com.kaolafm.opensdk.api.purchase.model.PurchasedItem;
import com.kaolafm.opensdk.api.purchase.model.VipMeals;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.ArrayList;
import java.util.List;

/**
 * vip套餐
 *
 */
public class PurchasedActivity extends CommonActivity {

    @Override
    public void initView(Bundle savedInstanceState) {
        super.initView(savedInstanceState);
        setTitle("已购列表");
    }

    @Override
    public void initData() {
        new PurchaseRequest().getPurchasedList(1, 10, new HttpCallback<BasePageResult<List<PurchasedItem>>>() {
            @Override
            public void onSuccess(BasePageResult<List<PurchasedItem>> result) {
                if (adapter != null) {
                    List<Object> list = new ArrayList<>();
                    for (PurchasedItem i : result.getDataList()) {
                        list.add(i);
                    }
                    adapter.setDataList(list);
                }
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取列表失败", exception);
            }
        });
    }
}
