package com.kaolafm.opensdk.account.token;

import android.os.Parcelable;

/**
 * token 操作相关的接口
 * <AUTHOR>
 * @date 2018/7/27
 */

interface AccessToken extends Parcelable {

    /**
     * 判断是否登录，true表示已登录
     * @return
     */
    boolean isLogin();

    /**
     * 退出登录
     */
    void logout();

    /**
     * 清理数据
     */
    void clear();

    /**
     * token是否过期，true表示已过期。
     * @return
     */
    boolean isExpires();
}
