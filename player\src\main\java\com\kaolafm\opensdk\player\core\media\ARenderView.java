package com.kaolafm.opensdk.player.core.media;

import android.content.Context;
import android.util.AttributeSet;
import android.view.SurfaceHolder;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

public class ARenderView extends View implements IRenderView{
    public ARenderView(Context context) {
        super(context);
    }

    public ARenderView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public ARenderView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public ARenderView(Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    @Override
    public View getView() {
        return this;
    }

    @Override
    public boolean shouldWaitForResize() {
        return false;
    }

    @Override
    public void setVideoSize(int videoWidth, int videoHeight) {

    }

    @Override
    public void setVideoSampleAspectRatio(int videoSarNum, int videoSarDen) {

    }

    @Override
    public void setVideoRotation(int degree) {

    }

    @Override
    public void setAspectRatio(int aspectRatio) {

    }

    @Override
    public void addRenderCallback(@NonNull IRenderCallback callback) {

    }

    @Override
    public void removeRenderCallback(@NonNull IRenderCallback callback) {

    }

    IRenderView.ISurfaceHolder getSurfaceHolder(){
        return null;
    }
}
