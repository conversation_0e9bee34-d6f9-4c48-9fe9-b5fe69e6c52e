package com.kaolafm.opensdk.api.live.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 10/9/23
 */
public class ChatRoomMessageInfoResult implements Parcelable {

    private List<ChatRoomMessageInfo> messageInfoList;

    public List<ChatRoomMessageInfo> getMessageInfoList() {
        if (messageInfoList == null) {
            return new ArrayList<>();
        }
        return messageInfoList;
    }

    public void setMessageInfoList(List<ChatRoomMessageInfo> messageInfoList) {
        this.messageInfoList = messageInfoList;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeTypedList(this.messageInfoList);
    }

    public ChatRoomMessageInfoResult() {
    }

    protected ChatRoomMessageInfoResult(Parcel in) {
        this.messageInfoList = in.createTypedArrayList(ChatRoomMessageInfo.CREATOR);
    }

    public static final Parcelable.Creator<ChatRoomMessageInfoResult> CREATOR = new Parcelable.Creator<ChatRoomMessageInfoResult>() {
        @Override
        public ChatRoomMessageInfoResult createFromParcel(Parcel source) {
            return new ChatRoomMessageInfoResult(source);
        }

        @Override
        public ChatRoomMessageInfoResult[] newArray(int size) {
            return new ChatRoomMessageInfoResult[size];
        }
    };
}
