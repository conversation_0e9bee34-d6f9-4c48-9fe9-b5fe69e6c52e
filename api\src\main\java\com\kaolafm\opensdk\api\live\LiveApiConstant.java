package com.kaolafm.opensdk.api.live;


import com.kaolafm.opensdk.HostConstants;

public interface LiveApiConstant {

    String UPLOAD_CALLBACK_URL = HostConstants.HOST_HTTPS + HostConstants.OPEN_KAOLA_HOST + "/live/saveVoiceMail";
    String BUCKET_NAME = "yunting-bj-tingban-audio";
    String OSS_ENDPOINT = "http://oss-cn-beijing.aliyuncs.com";

    String STS_SERVER_URL = HostConstants.HOST_HTTPS + HostConstants.OPEN_KAOLA_HOST + "/live/getTokenAliyunOSS?appId=";//STS 地址

    String SEND_MESSAGE_BASE_URL = "https://image.kaolafm.net/";

    /**
     * 是否使用新版直播接口
     */
    boolean IS_USE_NEW_SOCKET = true;

}
