# 云听SDK播放逻辑分析

## 一、播放器架构

SDK采用了分层设计，通过分层解耦提高代码可维护性和扩展性：

### 1. 顶层接口层

#### 1.1 PlayerService
- 作为Android Service提供后台播放服务
- 负责音频焦点管理和播放状态维护
- 核心方法实现：
```java
public class PlayerService extends Service {
    private ContextMediaPlayer mContextMediaPlayer;
    
    // 播放控制方法
    private void playInner() {
        if (!requestAudioFocus()) {
            return;
        }
        mContextMediaPlayer.getMediaPlayer().play();
    }
    
    private void pauseInner() {
        mContextMediaPlayer.getMediaPlayer().pause();
    }
    
    private void stopInner() {
        mContextMediaPlayer.getMediaPlayer().stop();
    }
    
    private void resetInner() {
        mContextMediaPlayer.getMediaPlayer().reset(true);
    }
}
```

#### 1.2 PlayerManager
- 对外提供统一的播放控制接口
- 管理播放队列和播放模式
- 处理播放事件回调

### 2. 播放器适配层

#### 2.1 AMediaPlayer (抽象基类)
- 定义统一的播放器接口
- 规范状态转换和事件分发
- 核心接口定义：
```java
public abstract class AMediaPlayer {
    // 播放控制
    public abstract void play();
    public abstract void pause();
    public abstract void stop();
    public abstract void reset(boolean needResetLastPlaybackRateFlag);
    
    // 播放信息
    public abstract long getDuration();
    public abstract long getCurrentPosition();
    public abstract boolean isPlaying();
    
    // 状态监听
    protected abstract void notifyPlayerPreparingComplete();
    protected abstract void notifyPlayerPreparing();
    protected abstract void notifyPlayerPlaying();
    protected abstract void notifyPlayerPaused();
}
```

#### 2.2 IJKMediaPlayerAdapter
- IJKPlayer的适配器实现
- 将IJK的接口转换为统一接口
- 处理IJK特有的功能
```java
public class IJKMediaPlayerAdapter extends AMediaPlayer {
    private IjkMediaPlayer mIjkMediaPlayer;
    private int mPlayStatus;
    private float mPlaybackRate = 1.0f;
    
    @Override
    public void play() {
        performAction(mIjkMediaPlayer::play);
    }
    
    @Override
    public void prepare(int needSeek, int streamTypeChannel) {
        performAction(() -> {
            mIjkMediaPlayer.prepare(needSeek, streamTypeChannel);
            notifyPlayerPreparing();
        });
    }
}
```

#### 2.3 ContextMediaPlayer
- 管理播放器上下文
- 处理播放器切换
- 实现：
```java
public class ContextMediaPlayer {
    public static final int TYPE_IJK_MEDIA_PLAYER = 1;
    private AMediaPlayer mediaPlayer;

    public void initPlayer(int type, Context context) {
        if (TYPE_IJK_MEDIA_PLAYER == type) {
            mediaPlayer = new IJKMediaPlayerAdapter(context);
        }
    }
}
```

### 3. 底层播放引擎

#### 3.1 IjkMediaPlayer
- 基于FFmpeg的播放器实现 
- 提供底层解码和渲染能力
- 核心实现：
```java
public final class IjkMediaPlayer implements IJKConstants {
    // Native方法
    private native void _setDataSource(String path);
    private native void _prepare(int needSeek, int streamType);
    private native void _start();
    private native void _stop();
    private native void _pause();
    
    // 状态回调
    @CalledByNative
    private void postEventFromNative(int what, int arg1, int arg2) {
        // 处理native层回调事件
    }
}
```
## 二、播放流程分析

### 1. 初始化流程

#### 1.1 详细初始化步骤
```java
// 1. PlayerService初始化
public class PlayerService extends Service {
    @Override
    public void onCreate() {
        super.onCreate();
        // 初始化播放器上下文
        mContextMediaPlayer = new ContextMediaPlayer();
        // 初始化为IJK播放器
        mContextMediaPlayer.initPlayer(ContextMediaPlayer.TYPE_IJK_MEDIA_PLAYER, this);
    }
}

// 2. ContextMediaPlayer创建与初始化
public class ContextMediaPlayer {
    public void initPlayer(int type, Context context) {
        if (TYPE_IJK_MEDIA_PLAYER == type) {
            mediaPlayer = new IJKMediaPlayerAdapter(context);
        }
    }
}

// 3. IJKMediaPlayerAdapter初始化
public class IJKMediaPlayerAdapter extends AMediaPlayer {
    public IJKMediaPlayerAdapter(Context context) {
        this.mContext = context;
        // 创建IJK播放器实例
        mIjkMediaPlayer = new IjkMediaPlayer();
        // 设置回调
        ijkCallBack = new IJKCallBack(this);
        mIjkMediaPlayer.setIjkPlayerCallBack(ijkCallBack);
    }
}

// 4. IjkMediaPlayer native层初始化
public final class IjkMediaPlayer {
    private native void native_setup(Object IjkMediaPlayer_this);
    
    public IjkMediaPlayer() {
        // 初始化native层
        native_setup(new WeakReference<IjkMediaPlayer>(this));
        // 初始化音频管理
        AudioManager am = (AudioManager) mContext.getSystemService(Context.AUDIO_SERVICE);
        mAudioManager = new WeakReference<AudioManager>(am);
    }
}
```

#### 1.2 初始化流程说明
1. **Service层初始化**
   - 创建PlayerService服务
   - 配置音频焦点管理
   - 初始化播放器组件

2. **上下文管理初始化**
   - 创建ContextMediaPlayer实例
   - 根据类型选择播放器实现
   - 配置播放器参数

3. **适配层初始化**
   - 创建IJKMediaPlayerAdapter实例
   - 设置播放器回调监听
   - 配置播放器状态管理

4. **Native层初始化**
   - 加载so库
   - 初始化FFmpeg解码器
   - 配置音频输出

### 2. 专辑播放流程

#### 2.1 准备阶段源码分析

```java
// IJKMediaPlayerAdapter中的实现
public class IJKMediaPlayerAdapter extends AMediaPlayer {
    // 1. 设置数据源
    @Override
    public void setDataSource(String source) {
        mDataSource = source;
        performAction(() -> mIjkMediaPlayer.setDataSource(source));
    }

    // 2. 准备播放器
    @Override
    public void prepare(int needSeek, int streamTypeChannel) {
        performAction(() -> {
            mIjkMediaPlayer.prepare(needSeek, streamTypeChannel);
            notifyPlayerPreparing();
        });
    }

    // 3. 状态回调处理
    private static class IJKCallBack implements IjkMediaPlayer.ijkPlayerCallBack {
        @Override
        public void message(int what, int arg1, int arg2, Object obj) {
            switch (what) {
                case IjkMediaPlayer.MEDIA_PREPARED: {
                    // 准备完成回调
                    ijkMediaPlayerAdapter.notifyPlayerPreparingComplete();
                }
                break;
            }
        }
    }
}
```

#### 2.2 开始播放实现

```java
// 1. 播放控制
public class IJKMediaPlayerAdapter extends AMediaPlayer {
    @Override
    public void play() {
        performAction(mIjkMediaPlayer::play);
    }
    
    private void notifyPlayerPlaying() {
        if (mIPlayerStateCoreListener != null) {
            mIPlayerStateCoreListener.onPlayerPlaying(mDataSource);
        }
    }
}

// 2. 状态变化处理
private static class IJKCallBack implements IjkMediaPlayer.ijkPlayerCallBack {
    @Override
    public void message(int what, int arg1, int arg2, Object obj) {
        switch (what) {
            case IjkMediaPlayer.MEDIA_STARTED: {
                ijkMediaPlayerAdapter.notifyPlayerPlaying();
            }
            break;
        }
    }
}

// 3. Native层实现
public final class IjkMediaPlayer {
    private native void _play();
    private native void _start();
    
    public void play() {
        // 调用native方法开始解码和播放
        _play();
        _start();
    }
}
```

#### 2.3 播放过程监控

```java
// 1. 缓冲进度更新
public class IJKMediaPlayerAdapter extends AMediaPlayer {
    private void notifyBufferProgress(long progress, long total) {
        if (mIPlayerBufferProgressListener != null) {
            mIPlayerBufferProgressListener.onBufferProgress(progress, total);
        }
    }
}

// 2. 播放进度更新
private static class IJKCallBack implements IjkMediaPlayer.ijkPlayerCallBack {
    @Override
    public void message(int what, int arg1, int arg2, Object obj) {
        switch (what) {
            case IjkMediaPlayer.MEDIA_PLAYING_TIME_UPDATE: {
                // arg1为当前位置，arg2为总时长
                ijkMediaPlayerAdapter.notifyProgress(arg1, arg2);
            }
            break;
            
            case IjkMediaPlayer.MEDIA_BUFFERING_UPDATE: {
                if (arg1 < 0 || arg2 < 0) {
                    return;
                }
                ijkMediaPlayerAdapter.notifyBufferProgress(arg1, arg2);
            }
            break;
        }
    }
}

// 3. 预加载进度监控
case IjkMediaPlayer.MEDIA_PRELOAD_TIME_UPDATE: {
    if (arg1 == arg2) {
        ijkMediaPlayerAdapter.notifyBufferProgress(arg1, arg2);
        return;
    }
    if (arg1 < 0 || arg2 < 0) {
        return;
    }
    ijkMediaPlayerAdapter.notifyBufferProgress(arg1, arg2);
}
```

### 3. 广播播放流程

#### 3.1 广播特性处理

```java
public class IJKMediaPlayerAdapter extends AMediaPlayer {
    // 1. 流式播放特性处理
    @Override
    public void start(String url, long duration, long position, int streamTypeChannel,
                     boolean audioFadeEnabled, AudioFadeConfig audioFadeConfig, 
                     String httpProxy, boolean clearDnsCache, VideoView videoView,
                     boolean needUseLastPlaybackRate, boolean shouldResetPlaybackRate) {
        
        // 广播流特殊处理
        if (isLiveStream(url)) {
            // 禁用seek
            mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "enable-accurate-seek", 0);
            // 设置较小的缓冲区
            mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "buffer-size", 1024);
            // 设置最大延迟
            mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "max-delay", 500);
        }
        
        // 常规初始化
        mDataSource = url;
        mVideoViewRef = new WeakReference<>(videoView);
        notifyPlayerIdle();
        stopIjk();
        resetIjk();
    }
    
    // 2. 播放控制限制
    @Override 
    public void pause() {
        if (isLiveStream(mDataSource)) {
            // 广播流不支持暂停，直接停止
            stop();
            return;
        }
        performAction(mIjkMediaPlayer::pause);
    }
    
    @Override
    public long getDuration() {
        if (isLiveStream(mDataSource)) {
            // 广播流无法获取总时长
            return -1;
        }
        return mIjkMediaPlayer.getDur();
    }
}

// Native层特殊配置
public final class IjkMediaPlayer {
    public void setLiveStreamingOption() {
        // 设置实时性优先
        _setOption(OPT_CATEGORY_PLAYER, "fast", 1);
        // 设置较小的缓存时间
        _setOption(OPT_CATEGORY_PLAYER, "framedrop", 1);
        // 设置网络超时时间
        _setOption(OPT_CATEGORY_FORMAT, "timeout", 10000000);
    }
}
```

#### 3.2 数据流处理

```java
public class IJKMediaPlayerAdapter extends AMediaPlayer {
    // 1. 缓冲策略
    private void configureLiveStreamBuffering() {
        // 设置最小缓冲大小
        mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "min-frames", 10);
        // 设置最大缓冲时间
        mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "max-buffer-size", 1024*1024);
    }
    
    // 2. 网络监控
    private void setupNetworkMonitor() {
        // 监控网络状态变化
        // 根据网络质量动态调整缓冲策略
    }
}

// 3. 错误恢复机制
private static class IJKCallBack implements IjkMediaPlayer.ijkPlayerCallBack {
    @Override
    public void message(int what, int arg1, int arg2, Object obj) {
        switch (what) {
            case IjkMediaPlayer.MEDIA_ERROR: {
                if (isLiveStream) {
                    // 直播流错误自动重试
                    rePlay();
                }
            }
            break;
        }
    }
    
    private void rePlay() {
        IJKMediaPlayerAdapter adapter = ijkMediaPlayerAdapterWeakReference.get();
        if (adapter != null) {
            try {
                adapter.reset(true);
                adapter.setDataSource(adapter.mDataSource);
                adapter.prepare();
                adapter.play();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
```
## 三、关键状态流转

```
              ┌─────────────────┐
              │      IDLE       │
              └────────┬────────┘
                       ↓ setDataSource()
              ┌─────────────────┐
              │   INITIALIZED   │
              └────────┬────────┘
                       ↓ prepare()/prepareAsync()
              ┌─────────────────┐
              │   PREPARING     │
              └────────┬────────┘
                       ↓ onPrepared()
              ┌─────────────────┐
              │    PREPARED     │
              └────────┬────────┘
                       ↓ play()
              ┌─────────────────┐
              │    PLAYING      │◄─────┐
              └────────┬────────┘      │
                       ↓ pause()        │
              ┌─────────────────┐      │
              │     PAUSED      │      │
              └────────┬────────┘      │
                       └───────────────┘
```

## 四、播放器关键功能实现

### 1. 音频解码播放

#### 1.1 解码器配置
```java
public class IJKMediaPlayerAdapter extends AMediaPlayer {
    private void setAvCodecOption() {
        // 设置音频解码参数
        mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_CODEC, "skip_loop_filter", 48);
        // 设置音频解码线程数
        mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_CODEC, "threads", 1);
        // 设置音频解码策略
        mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "soundtouch", 1);
    }
}

// Native层音频解码实现
public final class IjkMediaPlayer {
    private native void _setOption(int category, String name, String value);
    private native void _setOption(int category, String name, long value);
    
    // 音频解码回调
    @CalledByNative
    private void onAudioDecoderOpen(Bundle bundle) {
        // 处理音频解码器初始化
        if (mijkPlayerCallBack != null) {
            mijkPlayerCallBack.message(MEDIA_INFO_AUDIO_DECODED_START, 0, 0, bundle);
        }
    }
}
```

#### 1.2 音频输出
```java
public class IJKMediaPlayerAdapter extends AMediaPlayer {
    // 音频输出配置
    @Override
    public void setUsageAndContentType(int usage, int contentType) {
        performAction(() -> {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                mIjkMediaPlayer.setAudioAttributes(usage, contentType);
            }
        });
    }
    
    // 音量控制
    @Override
    public void setMediaVolume(float leftVolume, float rightVolume) {
        performAction(() -> mIjkMediaPlayer.setVolume(leftVolume, rightVolume));
    }
}
```

### 2. 缓冲机制

#### 2.1 缓冲区管理
```java
public class IJKMediaPlayerAdapter extends AMediaPlayer {
    // 配置缓冲参数
    private void configureBuffering() {
        // 设置播放器缓冲大小
        mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "packet-buffering", 1);
        // 设置最大缓冲大小(单位: 字节)
        mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "max-buffer-size", 15 * 1024 * 1024);
        // 设置起播缓冲大小
        mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "start-on-prepared", 1);
    }
    
    // 缓冲状态监听
    private void notifyBufferingStart() {
        if (mIPlayerStateCoreListener != null) {
            mIPlayerStateCoreListener.onBufferingStart(mDataSource);
        }
    }
    
    private void notifyBufferingEnd() {
        if (mIPlayerStateCoreListener != null) {
            mIPlayerStateCoreListener.onBufferingEnd(mDataSource);
        }
    }
}
```

#### 2.2 预加载实现
```java
public class IJKMediaPlayerAdapter extends AMediaPlayer {
    @Override
    public void preload(String url) {
        // 预加载逻辑
        mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "start-on-prepared", 0);
        setDataSource(url);
        prepare();
    }
    
    // 预加载进度监听
    private static class IJKCallBack implements IjkMediaPlayer.ijkPlayerCallBack {
        @Override
        public void message(int what, int arg1, int arg2, Object obj) {
            switch (what) {
                case IjkMediaPlayer.MEDIA_PRELOAD_TIME_UPDATE: {
                    ijkMediaPlayerAdapter.notifyBufferProgress(arg1, arg2);
                }
                break;
            }
        }
    }
}
```

### 3. 播放控制详细实现

```java
public class IJKMediaPlayerAdapter extends AMediaPlayer {
    // 1. 开始播放
    @Override
    public void play() {
        performAction(() -> {
            mIjkMediaPlayer.play();
            notifyPlayerPlaying();
        });
    }
    
    // 2. 暂停播放
    @Override
    public void pause() {
        performAction(() -> {
            mIjkMediaPlayer.pause();
            notifyPlayerPaused();
        });
    }
    
    // 3. 停止播放
    @Override
    public void stop() {
        notifyPlayerIdle();
        performAction(() -> {
            mIjkMediaPlayer.stop();
            mIjkMediaPlayer.reset();
        });
    }
    
    // 4. 跳转播放
    @Override
    public void seek(long msec) {
        performAction(() -> {
            mIjkMediaPlayer.seek(msec);
            notifySeekStart();
        });
    }
    
    // 5. 释放资源
    @Override
    public void release() {
        ijkCallBack.clear();
        performAction(() -> {
            mIjkMediaPlayer.stop();
            mIjkMediaPlayer.release();
        });
    }
}
```

### 4. 状态监听系统

#### 4.1 状态定义
```java
public interface PlayerConstants {
    int TYPE_PLAYER_DEFAULT = 0;       // 默认状态
    int TYPE_PLAYER_PREPARING = 1;     // 准备中
    int TYPE_PLAYER_PREPARED = 2;      // 准备完成
    int TYPE_PLAYER_PLAYING = 3;       // 播放中
    int TYPE_PLAYER_PAUSED = 4;        // 已暂停
    int TYPE_PLAYER_COMPLETED = 5;     // 播放完成
    int TYPE_PLAYER_ERROR = 6;         // 播放错误
}
```

#### 4.2 状态监听实现
```java
public class IJKMediaPlayerAdapter extends AMediaPlayer {
    private IPlayerStateCoreListener mIPlayerStateCoreListener;
    
    // 状态通知方法
    private void notifyPlayerPreparingComplete() {
        mPlayStatus = PlayerConstants.TYPE_PLAYER_PREPARED;
        if (mIPlayerStateCoreListener != null) {
            mIPlayerStateCoreListener.onPlayerPreparingComplete(mDataSource);
        }
    }
    
    private void notifyPlayerPreparing() {
        mPlayStatus = PlayerConstants.TYPE_PLAYER_PREPARING;
        if (mIPlayerStateCoreListener != null) {
            mIPlayerStateCoreListener.onPlayerPreparing(mDataSource);
        }
    }
    
    private void notifyPlayerPlaying() {
        mPlayStatus = PlayerConstants.TYPE_PLAYER_PLAYING;
        if (mIPlayerStateCoreListener != null) {
            mIPlayerStateCoreListener.onPlayerPlaying(mDataSource);
        }
    }
    
    private void notifyPlayerPaused() {
        mPlayStatus = PlayerConstants.TYPE_PLAYER_PAUSED;
        if (mIPlayerStateCoreListener != null) {
            mIPlayerStateCoreListener.onPlayerPaused(mDataSource);
        }
    }
    
    private void notifyPlayerEnd() {
        mPlayStatus = PlayerConstants.TYPE_PLAYER_COMPLETED;
        if (mIPlayerStateCoreListener != null) {
            mIPlayerStateCoreListener.onPlayerEnd(mDataSource);
        }
    }
    
    private void notifyPlayerFailed(int what, int extra, String dnsAddress) {
        mPlayStatus = PlayerConstants.TYPE_PLAYER_ERROR;
        if (mIPlayerStateCoreListener != null) {
            mIPlayerStateCoreListener.onPlayerFailed(what, extra, dnsAddress);
        }
    }
}
```

## 五、错误处理机制

### 1. 网络相关错误

#### 1.1 错误码定义
```java
public interface IJKConstants {
    // 网络错误相关常量
    int MEDIA_ERROR_NETWORK_FAILED = -1413828334;  // 网络连接失败
    int MEDIA_ERROR_TIMED_OUT = -110;              // 网络超时
    int NO_ID_SUB_ERROR_IJK_PLAYER = 404;         // 资源不存在
    int BAD_GATEWAY_SUB_ERROR_IJK_PLAYER = 502;   // 网关错误
    int MEDIA_AVERROR_HTTP_NOT_FOUND = -875574520; // HTTP 404错误
}
```

#### 1.2 错误处理实现
```java
public class IJKMediaPlayerAdapter extends AMediaPlayer {
    // 网络错误处理
    private void handleNetworkError(int what, int extra) {
        String dnsAddress = getDnsAddress();
        
        // HTTP 404错误特殊处理
        if (extra == IjkMediaPlayer.MEDIA_AVERROR_HTTP_NOT_FOUND) {
            notifyPlayerFailed(IjkMediaPlayer.NO_ID_SUB_ERROR_IJK_PLAYER, extra, dnsAddress);
            return;
        }
        
        // 网络超时自动重试
        if (what == IjkMediaPlayer.MEDIA_ERROR_IJK_PLAYER && 
            extra == IjkMediaPlayer.MEDIA_ERROR_TIMED_OUT) {
            rePlay();
            return;
        }
        
        notifyPlayerFailed(what, extra, dnsAddress);
    }
    
    // DNS地址获取
    @Override
    public String getDnsAddress() {
        try {
            return InetAddress.getByName(new URL(mDataSource).getHost()).getHostAddress();
        } catch (Exception e) {
            return "";
        }
    }
}
```

### 2. 解码相关错误

#### 2.1 解码错误处理
```java
public class IJKMediaPlayerAdapter extends AMediaPlayer {
    private void handleDecoderError(int what, int extra) {
        // 1. 格式不支持
        if (what == IjkMediaPlayer.MEDIA_ERROR_IJK_PLAYER && 
            extra == IjkMediaPlayer.MEDIA_ERROR_UNSUPPORTED) {
            notifyPlayerFailed(what, extra, "Unsupported media format");
            return;
        }
        
        // 2. 解码失败
        if (what == IjkMediaPlayer.MEDIA_ERROR_IJK_PLAYER && 
            extra == IjkMediaPlayer.MEDIA_ERROR_DECODE) {
            // 尝试切换解码器
            switchDecoder();
            return;
        }
        
        // 3. 硬件加速异常
        if (extra == IjkMediaPlayer.MEDIA_ERROR_HW_DECODE) {
            // 切换到软解
            disableHwAccel();
            rePlay();
            return;
        }
    }
    
    // 切换解码器
    private void switchDecoder() {
        mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_CODEC, "skip_loop_filter", 48);
        mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_CODEC, "threads", 1);
        rePlay();
    }
    
    // 禁用硬件加速
    private void disableHwAccel() {
        mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "mediacodec", 0);
        mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "mediacodec-auto-rotate", 0);
        mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "mediacodec-handle-resolution-change", 0);
    }
}
```

### 3. 资源相关错误

#### 3.1 资源错误处理
```java
public class IJKMediaPlayerAdapter extends AMediaPlayer {
    private void handleResourceError(int what, int extra) {
        // 1. 文件不存在
        if (extra == IjkMediaPlayer.NO_TS_FILE_ERROR_IJK_PLAYER) {
            notifyPlayerFailed(what, extra, "File not found");
            return;
        }
        
        // 2. 权限问题
        if (extra == IjkMediaPlayer.MEDIA_ERROR_ACCESS_DENIED) {
            notifyPlayerFailed(what, extra, "Access denied");
            return;
        }
        
        // 3. 内存不足
        if (extra == IjkMediaPlayer.MEDIA_ERROR_OOM) {
            // 尝试释放内存
            System.gc();
            // 降低解码质量
            mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "framedrop", 5);
            rePlay();
            return;
        }
    }
}

// 全局错误处理
private static class IJKCallBack implements IjkMediaPlayer.ijkPlayerCallBack {
    @Override
    public void message(int what, int arg1, int arg2, Object obj) {
        switch (what) {
            case IjkMediaPlayer.MEDIA_ERROR: {
                IJKMediaPlayerAdapter adapter = ijkMediaPlayerAdapterWeakReference.get();
                if (adapter != null) {
                    // 根据错误类型分发处理
                    if (isNetworkError(arg1, arg2)) {
                        adapter.handleNetworkError(arg1, arg2);
                    } else if (isDecoderError(arg1, arg2)) {
                        adapter.handleDecoderError(arg1, arg2);
                    } else if (isResourceError(arg1, arg2)) {
                        adapter.handleResourceError(arg1, arg2);
                    } else {
                        // 未知错误，重置播放器
                        adapter.reset(true);
                        adapter.notifyPlayerFailed(arg1, arg2, null);
                    }
                }
            }
            break;
        }
    }
}
```

## 六、性能优化策略

### 1. 播放延迟优化

#### 1.1 预加载机制实现
```java
public class IJKMediaPlayerAdapter extends AMediaPlayer {
    // 预加载配置
    private void setupPreloadConfig() {
        // 设置预加载缓冲大小
        mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "start-on-prepared", 0);
        mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "reconnect", 1);
        
        // 预加载超时配置
        mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "timeout", 30 * 1000 * 1000);
        mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "enable-accurate-seek", 1);
    }
    
    // DNS预解析
    private void preDnsResolve(String host) {
        try {
            InetAddress.getAllByName(host);
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
    }
}

// DNS缓存优化
public class DnsCache {
    private static LruCache<String, String> sIpCache = new LruCache<>(100);
    
    public static void cacheIp(String host, String ip) {
        sIpCache.put(host, ip);
    }
    
    public static String getCachedIp(String host) {
        return sIpCache.get(host);
    }
}
```

#### 1.2 播放器复用
```java
public class PlayerManager {
    private ContextMediaPlayer mContextMediaPlayer;
    
    // 播放器复用池
    private static class PlayerPool {
        private static final int MAX_POOL_SIZE = 3;
        private final List<IJKMediaPlayerAdapter> mPlayerPool = new ArrayList<>();
        
        public IJKMediaPlayerAdapter obtain() {
            synchronized (mPlayerPool) {
                if (!mPlayerPool.isEmpty()) {
                    return mPlayerPool.remove(0);
                }
                return new IJKMediaPlayerAdapter(mContext);
            }
        }
        
        public void recycle(IJKMediaPlayerAdapter player) {
            synchronized (mPlayerPool) {
                if (mPlayerPool.size() < MAX_POOL_SIZE) {
                    player.reset(true);
                    mPlayerPool.add(player);
                } else {
                    player.release();
                }
            }
        }
    }
}
```

### 2. 内存管理

#### 2.1 资源释放
```java
public class IJKMediaPlayerAdapter extends AMediaPlayer {
    @Override
    public void release() {
        // 1. 清理回调
        ijkCallBack.clear();
        
        // 2. 释放surface
        performAction(() -> {
            mIjkMediaPlayer.setSurface(null);
            mIjkMediaPlayer.setDisplay(null);
        });
        
        // 3. 释放播放器
        performAction(() -> {
            mIjkMediaPlayer.stop();
            mIjkMediaPlayer.release();
        });
        
        // 4. 清理引用
        mIPlayerStateCoreListener = null;
        mIPlayerBufferProgressListener = null;
        mVideoViewRef = null;
    }
    
    // 弱引用管理
    private static class IJKCallBack implements IjkMediaPlayer.ijkPlayerCallBack {
        private WeakReference<IJKMediaPlayerAdapter> ijkMediaPlayerAdapterWeakReference;
        
        public IJKCallBack(IJKMediaPlayerAdapter adapter) {
            ijkMediaPlayerAdapterWeakReference = new WeakReference<>(adapter);
        }
        
        public void clear() {
            IJKMediaPlayerAdapter adapter = ijkMediaPlayerAdapterWeakReference.get();
            if (adapter != null) {
                adapter.mIjkMediaPlayer.setIjkPlayerCallBack(null);
            }
            ijkMediaPlayerAdapterWeakReference.clear();
        }
    }
}
```

### 3. 网络优化

#### 3.1 智能CDN切换
```java
public class CDNManager {
    private List<String> mCdnUrls = new ArrayList<>();
    private int mCurrentCdnIndex = 0;
    
    // 切换CDN
    public String switchCDN() {
        synchronized (mCdnUrls) {
            mCurrentCdnIndex = (mCurrentCdnIndex + 1) % mCdnUrls.size();
            return mCdnUrls.get(mCurrentCdnIndex);
        }
    }
    
    // 根据网络质量选择CDN
    public String selectBestCDN(NetworkQuality quality) {
        switch (quality) {
            case EXCELLENT:
                return selectCDNByLatency();
            case POOR:
                return selectCDNByBandwidth();
            default:
                return mCdnUrls.get(mCurrentCdnIndex);
        }
    }
}
```

#### 3.2 动态码率调整
```java
public class IJKMediaPlayerAdapter extends AMediaPlayer {
    // 动态码率配置
    private void setupDynamicBitrate() {
        mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "framedrop", 1);
        mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "packet-buffering", 1);
    }
    
    // 网络状态监听
    private final BroadcastReceiver mNetworkReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (ConnectivityManager.CONNECTIVITY_ACTION.equals(intent.getAction())) {
                NetworkInfo info = intent.getParcelableExtra(ConnectivityManager.EXTRA_NETWORK_INFO);
                adjustPlaybackQuality(info);
            }
        }
    };
    
    // 根据网络调整播放质量
    private void adjustPlaybackQuality(NetworkInfo info) {
        if (info != null && info.isConnected()) {
            if (info.getType() == ConnectivityManager.TYPE_WIFI) {
                // WiFi网络，使用高质量配置
                setHighQualityConfig();
            } else {
                // 移动网络，使用省流量配置
                setLowQualityConfig();
            }
        }
    }
    
    private void setHighQualityConfig() {
        mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "max-buffer-size", 15 * 1024 * 1024);
        mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "framedrop", 0);
    }
    
    private void setLowQualityConfig() {
        mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "max-buffer-size", 5 * 1024 * 1024);
        mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "framedrop", 5);
    }
}
```
