<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <EditText
        android:id="@+id/et_init_app_id"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="请输入AppId，默认值=in2193"
        />
    <EditText
        android:id="@+id/et_init_app_key"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/et_init_app_id"
        android:hint="请输入AppKey，默认值=09843cde4e4638b3083529f8a42b4a43"
        />
    <EditText
        android:id="@+id/et_init_channel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/et_init_app_key"
        android:hint="请输入渠道"
        />
    <Button
        android:id="@+id/btn_init"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/et_init_channel"
        android:text="初始化"
        />

</androidx.constraintlayout.widget.ConstraintLayout>