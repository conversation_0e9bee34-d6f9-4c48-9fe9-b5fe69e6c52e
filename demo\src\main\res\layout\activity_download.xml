<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <Button
        android:id="@+id/btn_download_start"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="开始下载" />

    <Button
        android:id="@+id/btn_download_pause"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="暂停"
        app:layout_constraintLeft_toRightOf="@id/btn_download_start" />

    <Button
        android:id="@+id/btn_download_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="取消"
        app:layout_constraintLeft_toRightOf="@id/btn_download_pause" />

    <ProgressBar
        android:id="@+id/pb_download_progress"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        style="?android:attr/progressBarStyleHorizontal"
        app:layout_constraintTop_toBottomOf="@id/btn_download_start"
        android:orientation="horizontal"
        />

</androidx.constraintlayout.widget.ConstraintLayout>