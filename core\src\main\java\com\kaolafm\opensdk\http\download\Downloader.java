package com.kaolafm.opensdk.http.download;

/**
 * 下载器
 * <AUTHOR>
 * @date 2020-02-10
 */
public interface Downloader {
    /**
     * 开始下载
     * @param request
     * @param listener
     */
    void download(DownloadRequest request, DownloadListener listener);

    /**
     * 暂停下载
     * @param url
     */
    void pause(String url);

    /**
     * 取消下载
     * @param url
     */
    void cancel(String url);
}
