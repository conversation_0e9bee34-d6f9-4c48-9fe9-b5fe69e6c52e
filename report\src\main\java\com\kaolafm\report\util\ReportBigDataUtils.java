package com.kaolafm.report.util;

import android.util.Log;

import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Collections;

public class ReportBigDataUtils {

    // 大数据中心数据上报鉴权私钥
    private static final String BIG_DATA_PRIVATE_KEY = "X6p5T5F2lQD2vj5Ci4g139SnedB7DuVvmkrRYVqSWtlhncOV7RBQrItsoxaN3KXpJrRYVqSWtlhncOV7RBQ";

    /**
     * 根据参数排序
     * @param paramaterses
     * @return
     */
    public static ArrayList<Paramaters> orderByASCII(ArrayList<Paramaters> paramaterses)
    {
        if (paramaterses != null && paramaterses.size() != 0){
            Collections.sort(paramaterses,new CompareUtils());
            return paramaterses;
        }
        return null;
    }

    /**
     * byte数组转换为16进制字符串
     *
     * @param bts
     *            数据源
     * @return 16进制字符串
     */
    public static String bytes2Hex(byte[] bts) {
        String des = "";
        String tmp = null;
        for (int i = 0; i < bts.length; i++) {
            tmp = (Integer.toHexString(bts[i] & 0xFF));
            if (tmp.length() == 1) {
                des += "0";
            }
            des += tmp;
        }
        return des;
    }

    /**
     * 加密
     * @param plaintext 明文
     * @return ciphertext 密文
     */
    public final static String  encryptMD5(String plaintext) {
        char hexDigits[] = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                'a', 'b', 'c', 'd', 'e', 'f' };
        try {
            byte[] btInput = plaintext.getBytes();
            // 获得MD5摘要算法的 MessageDigest 对象
            MessageDigest mdInst = MessageDigest.getInstance("MD5");
            // 使用指定的字节更新摘要
            mdInst.update(btInput);
            // 获得密文
            byte[] md = mdInst.digest();
            // 把密文转换成十六进制的字符串形式
            int j = md.length;
            char str[] = new char[j * 2];
            int k = 0;
            for (int i = 0; i < j; i++) {
                byte byte0 = md[i];
                str[k++] = hexDigits[byte0 >>> 4 & 0xf];
                str[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(str);
        } catch (Exception e) {
            return null;
        }
    }

    public static String getToken(ArrayList<Paramaters> paramaterses){
        // 新建一个list,值为空的<key,value>不参与token运算
        ArrayList<Paramaters> tokenList = new ArrayList<>();
        for(int i = 0; i < paramaterses.size(); i++){
            if("".equals(paramaterses.get(i).getValue()) || null == paramaterses.get(i).getValue()){

            }else{
                tokenList.add(paramaterses.get(i));
            }
        }
        Log.wtf("H5_token_tokenList：", "" + tokenList.size());
        // ASCII排序
        ArrayList<Paramaters> list = orderByASCII(tokenList);
        // 生成token
        String params = "";
        for (int i = 0; i < list.size(); i++) {
            if (i == 0) {
                params = list.get(i).getKey() + "=" + list.get(i).getValue();
            } else {
                params = params + "&" + list.get(i).getKey() + "=" + list.get(i).getValue();
            }

        }
        String md5 = encryptMD5(params);
        return md5;
    }
}
