package com.kaolafm.opensdk.player.core.model;

public class AudioFadeConfig {

    /**
     * 起播淡入
     */
    private boolean playStartFadeIn = true;

    /**
     * Seek起播淡入
     */
    private boolean seekStartFadeIn = true;

    /**
     * 暂停起播淡入
     */
    private boolean pauseStartFadeIn = true;

    /**
     * 暂停淡出
     */
    private boolean pauseFadeOut = true;

    /**
     * 播放自然结束淡出
     */
    private boolean playFinishFadeOut = true;

    /**
     * 播放被停止淡出
     */
    private boolean playStopFadeOut = true;

    private long playStartFadeInDuration = 1000;

    private long seekStartFadeInDuration = 1000;

    private long pauseStartFadeInDuration = 1000;

    private long pauseFadeOutDuration = 500;

    private long playFinishFadeOutDuration = 1000;

    private long playStopFadeOutDuration = 300;

    public AudioFadeConfig disablePlayStartFadeIn() {
        this.playStartFadeIn = false;
        return this;
    }

    public AudioFadeConfig disableSeekStartFadeIn() {
        this.seekStartFadeIn = false;
        return this;
    }

    public AudioFadeConfig disablePauseStartFadeIn() {
        this.pauseStartFadeIn = false;
        return this;
    }

    public AudioFadeConfig disablePauseFadeOut() {
        this.pauseFadeOut = false;
        return this;
    }

    public AudioFadeConfig disablePlayFinishFadeOut() {
        this.playFinishFadeOut = false;
        return this;
    }

    public AudioFadeConfig disablePlayStopFadeOut() {
        this.playStopFadeOut = false;
        return this;
    }

    public boolean isPlayStartFadeIn() {
        return playStartFadeIn;
    }

    public boolean isSeekStartFadeIn() {
        return seekStartFadeIn;
    }

    public boolean isPauseStartFadeIn() {
        return pauseStartFadeIn;
    }

    public boolean isPauseFadeOut() {
        return pauseFadeOut;
    }

    public boolean isPlayFinishFadeOut() {
        return playFinishFadeOut;
    }

    public boolean isPlayStopFadeOut() {
        return playStopFadeOut;
    }

    public long getPlayStartFadeInDuration() {
        return playStartFadeInDuration;
    }

    public long getSeekStartFadeInDuration() {
        return seekStartFadeInDuration;
    }

    public long getPauseStartFadeInDuration() {
        return pauseStartFadeInDuration;
    }

    public long getPauseFadeOutDuration() {
        return pauseFadeOutDuration;
    }

    public long getPlayFinishFadeOutDuration() {
        return playFinishFadeOutDuration;
    }

    public long getPlayStopFadeOutDuration() {
        return playStopFadeOutDuration;
    }

    @Override
    public String toString() {
        return "AudioFadeConfig{" +
                "playStartFadeIn=" + playStartFadeIn +
                ", seekStartFadeIn=" + seekStartFadeIn +
                ", pauseStartFadeIn=" + pauseStartFadeIn +
                ", pauseFadeOut=" + pauseFadeOut +
                ", playFinishFadeOut=" + playFinishFadeOut +
                ", playStopFadeOut=" + playStopFadeOut +
                ", playStartFadeInDuration=" + playStartFadeInDuration +
                ", seekStartFadeInDuration=" + seekStartFadeInDuration +
                ", pauseStartFadeInDuration=" + pauseStartFadeInDuration +
                ", pauseFadeOutDuration=" + pauseFadeOutDuration +
                ", playFinishFadeOutDuration=" + playFinishFadeOutDuration +
                ", playStopFadeOutDuration=" + playStopFadeOutDuration +
                '}';
    }
}
