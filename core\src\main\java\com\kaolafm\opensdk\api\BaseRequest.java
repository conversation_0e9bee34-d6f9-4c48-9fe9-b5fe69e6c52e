package com.kaolafm.opensdk.api;

import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.urlmanager.UrlManager;

import javax.inject.Inject;

/**
 * 网络请求基类，主要使用Dagger2注入获取所需要的全局单例对象。获取公共参数
 *
 * <AUTHOR>
 * @date 2018/7/25
 */

public abstract class BaseRequest extends AbstractRequest {

    @Inject
    @AppScope
    protected UrlManager mUrlManager;

    protected BaseRequest() {
        ComponentKit.getInstance().inject(this);
    }

}
