package com.kaolafm.ad.api.internal;

import android.text.TextUtils;

import com.kaolafm.ad.AdConstant;
import com.kaolafm.ad.api.internal.model.AdCreative;
import com.kaolafm.ad.api.internal.model.AdvertisingResult;
import com.kaolafm.ad.api.internal.model.ReportParamEntity;
import com.kaolafm.ad.api.model.Advert;
import com.kaolafm.ad.api.model.AdvertisingDetails;
import com.kaolafm.ad.api.model.AudioAdvert;
import com.kaolafm.ad.api.model.AudioImageAdvert;
import com.kaolafm.ad.api.model.ImageAdvert;
import com.kaolafm.ad.db.manager.ReportParamDBManager;
import com.kaolafm.ad.di.component.AdvertSubcomponent;
import com.kaolafm.ad.di.qualifier.AdvertHostQualifier;
import com.kaolafm.ad.util.AdBeanUtil;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.SpUtil;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.di.qualifier.ParamQualifier;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.http.socket.SocketListener;
import com.kaolafm.opensdk.http.socket.SocketManager;
import com.kaolafm.opensdk.http.socket.parser.ParseQS;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;
import javax.inject.Provider;

import dagger.Lazy;
import io.reactivex.Single;
import io.reactivex.functions.Function;
import io.reactivex.functions.Predicate;

/**
 * 使用Kradio SDK的公共参数的广告请求，内部使用，不对外暴露
 *
 * <AUTHOR> Yan
 * @date 2020-01-02
 */
@AppScope
public class AdInternalRequest extends BaseRequest {

    private AdInternalService mAdInternalService;

    @Inject
    @AppScope
    Lazy<AdReportRequest> reportRequestLazy;

    @Inject
    @ParamQualifier
    Map<String, Provider<Map<String, String>>> mCommonParams;

    @Inject
    @AdvertHostQualifier
    String mSocketHost;
    private SocketListener<AdvertisingResult> mListener;

    @Inject
    public AdInternalRequest() {
        AdvertSubcomponent subcomponent = ComponentKit.getInstance().getSubcomponent();
        subcomponent.inject(this);
        mAdInternalService = obtainRetrofitService(AdInternalService.class);
    }

    /**
     * 获取广告详情列表
     *
     * @param adZoneId        必填 广告位ID
     * @param picWidth        必填 图片广告位的宽 可传多个 以逗号分隔 在请求包含图片信息的广告位时为必填。
     * @param picHeight       必填 图片广告位的高 可传多个 以逗号分隔 在请求包含图片信息的广告位时为必填。
     *                        对于图片分辨率参数的说明:
     *                        如需要1280×960和1920×1080的图片时 c_picWidth应传"1280,1920" c_picHeight应传"960,1080"
     * @param acceptedAdTypes 必填 多种类型逗号隔开。可接受广告类型，客户端（非SDK）必传。3音频广告（新）；4图片广告（新）；5音图广告（新）
     * @param advancedAttrs   选填 json格式，需要编码。用于精准投放广告或广告服务判断投放广告时机等。
     *                        通过
     *                        <p>
     *                        {@link com.kaolafm.ad.util.AdParamsUtil#getAdvanceAttrs(long, long, long, long, int)}、
     *                        <p>
     *                        {@link com.kaolafm.ad.util.AdParamsUtil#getAlbumAttrs(long, long, int)}、
     *                        <p>
     *                        {@link com.kaolafm.ad.util.AdParamsUtil#getRadioAttrs(long, long, int)}
     *                        获取就是已经处理好的，直接填入就可以了。
     * @param callback        必填 选填
     */
    private <T> void getAdvertisingList(String adZoneId,
                                        String picWidth,
                                        String picHeight,
                                        String acceptedAdTypes,
                                        String advancedAttrs,
                                        Function<AdvertisingResult, T> map,
                                        HttpCallback<T> callback) {
        Map<String, String> params = getParamMap(adZoneId, picWidth, picHeight, acceptedAdTypes, advancedAttrs);
        Single<BaseResult<AdvertisingResult>> advertisings = mAdInternalService.getAdvertisings(params).map(baseResult -> {
            reportError(adZoneId, baseResult);
            return baseResult;
        });
        doHttpDeal(advertisings, baseResult -> {
            AdvertisingResult result = baseResult.getResult();
            if (result != null && !ListUtil.isEmpty(result.getAdCreatives())) {
                return map.apply(result);
            }
            return null;
        }, callback);
    }

    private <T extends Advert> void getAdvertisingList(String adZoneId,
                                                       String picWidth,
                                                       String picHeight,
                                                       String acceptedAdTypes,
                                                       String advancedAttrs,
                                                       Predicate<Advert> predicate,
                                                       HttpCallback<List<T>> callback) {
        getAdvertisingList(adZoneId, picWidth, picHeight, acceptedAdTypes, advancedAttrs, (Function<AdvertisingResult, List<T>>) result -> {
            List<AdCreative> adCreatives = result.getAdCreatives();
            ArrayList<T> details = new ArrayList<>();
            for (AdCreative adCreative : adCreatives) {
                saveReportParams(result, adCreative);
                Advert advert = AdBeanUtil.transform(adCreative);
                if (predicate.test(advert)) {
                    details.add((T) advert);
                }
            }
            return details;
        }, callback);
    }

    public void getAdvertList(String adZoneId,
                              String picWidth,
                              String picHeight,
                              String acceptedAdTypes,
                              String advancedAttrs,
                              HttpCallback<List<Advert>> callback) {
        getAdvertisingList(adZoneId, picWidth, picHeight, acceptedAdTypes, advancedAttrs, (Predicate<Advert>) advert -> true, callback);
    }

    /**
     * 获取广告位的轮播数
     *
     * @param adZoneId
     * @return
     */
    private int getCarousel(String adZoneId) {
        return SpUtil.getInt(AdConstant.SP_NAME, adZoneId, 0);
    }

    /**
     * 获取广告详情列表
     *
     * @param adZoneId        必填 广告位ID
     * @param picWidth        选填 图片广告位的宽，可传多个，以逗号分隔，在请求包含图片信息的广告位时为必填。
     * @param picHeight       选填 图片广告位的高，可传多个，以逗号分隔，在请求包含图片信息的广告位时为必填。
     *                        对于图片分辨率参数的说明:
     *                        如需要1280×960和1920×1080的图片时 c_picWidth应传"1280,1920" c_picHeight应传"960,1080"
     * @param acceptedAdTypes 必填 多种类型逗号隔开。可接受广告类型，客户端（非SDK）必传。3音频广告（新）；4图片广告（新）；5音图广告（新）
     * @param advancedAttrs   选填 json格式，需要编码。用于精准投放广告或广告服务判断投放广告时机等。
     *                        通过
     *                        <p>
     *                        {@link com.kaolafm.ad.util.AdParamsUtil#getAdvanceAttrs(long, long, long, long, int)}、
     *                        <p>
     *                        {@link com.kaolafm.ad.util.AdParamsUtil#getAlbumAttrs(long, long, int)}、
     *                        <p>
     *                        {@link com.kaolafm.ad.util.AdParamsUtil#getRadioAttrs(long, long, int)}
     *                        获取就是已经处理好的，直接填入就可以了。
     * @param callback        必填 选填
     */
    public void getAdvertisingList(String adZoneId,
                                   String picWidth,
                                   String picHeight,
                                   String acceptedAdTypes,
                                   String advancedAttrs,
                                   HttpCallback<List<AdvertisingDetails>> callback) {
        getAdvertisingList(adZoneId, picWidth, picHeight, acceptedAdTypes, advancedAttrs, this::filterMap, callback);
    }

    /**
     * 去除不需要对外暴露的字段，保存上报需要的字段
     *
     * @param result
     * @return
     */
    private List<AdvertisingDetails> filterMap(AdvertisingResult result) {
        List<AdCreative> adCreatives = result.getAdCreatives();
        ArrayList<AdvertisingDetails> details = new ArrayList<>();
        for (AdCreative adCreative : adCreatives) {
            details.add(adCreative);
            saveReportParams(result, adCreative);
        }
        return details;
    }


    /**
     * 获取音频广告。K-radio业务使用。
     *
     * @param adZoneId      必填 广告位ID
     * @param advancedAttrs 选填 json格式，需要编码。用于精准投放广告或广告服务判断投放广告时机等。
     *                      通过
     *                      <p>
     *                      {@link com.kaolafm.ad.util.AdParamsUtil#getAdvanceAttrs(long, long, long, long, int)}、
     *                      <p>
     *                      {@link com.kaolafm.ad.util.AdParamsUtil#getAlbumAttrs(long, long, int)}、
     *                      <p>
     *                      {@link com.kaolafm.ad.util.AdParamsUtil#getRadioAttrs(long, long, int)}
     *                      获取就是已经处理好的，直接填入就可以了。
     * @param callback
     */
    public void getAudioAdvertList(String adZoneId, String advancedAttrs, HttpCallback<List<AudioAdvert>> callback) {
        getAdvertisingList(adZoneId, null, null, "3", advancedAttrs, (Predicate<Advert>) advert -> advert instanceof AudioAdvert, callback);
    }

    /**
     * 获取音频广告。SDK对外使用。
     *
     * @param adZoneId 必填 广告位ID
     * @param callback 回调
     */
    public void getAudioAdvertList(String adZoneId, HttpCallback<List<AudioAdvert>> callback) {
        getAudioAdvertList(adZoneId, null, callback);
    }

    /**
     * 获取图片广告。K-radio业务使用
     *
     * @param adZoneId      必填 广告位ID
     * @param picWidth      必填 图片广告位的宽，可传多个，以逗号分隔。
     * @param picHeight     必填 图片广告位的高，可传多个，以逗号分隔。
     *                      对于图片分辨率参数的说明:
     *                      如需要1280×960和1920×1080的图片时 c_picWidth应传"1280,1920" c_picHeight应传"960,1080"
     * @param advancedAttrs 选填 json格式，需要编码。用于精准投放广告或广告服务判断投放广告时机等。
     *                      通过
     *                      <p>
     *                      {@link com.kaolafm.ad.util.AdParamsUtil#getAdvanceAttrs(long, long, long, long, int)}、
     *                      <p>
     *                      {@link com.kaolafm.ad.util.AdParamsUtil#getAlbumAttrs(long, long, int)}、
     *                      <p>
     *                      {@link com.kaolafm.ad.util.AdParamsUtil#getRadioAttrs(long, long, int)}
     *                      获取就是已经处理好的，直接填入就可以了。
     * @param callback
     */
    public void getImageAdvertList(String adZoneId, String picWidth, String picHeight, String advancedAttrs, HttpCallback<List<ImageAdvert>> callback) {
        getAdvertisingList(adZoneId, picWidth, picHeight, "4", advancedAttrs, (Predicate<Advert>) advert -> advert instanceof ImageAdvert, callback);
    }

    /**
     * 获取图片广告。SDK对外使用
     *
     * @param adZoneId  必填 广告位ID
     * @param picWidth  必填 图片广告位的宽，可传多个，以逗号分隔。
     * @param picHeight 必填 图片广告位的高，可传多个，以逗号分隔。
     *                  对于图片分辨率参数的说明:
     *                  如需要1280×960和1920×1080的图片时 c_picWidth应传"1280,1920" c_picHeight应传"960,1080"
     * @param callback  回调
     */
    public void getImageAdvertList(String adZoneId, String picWidth, String picHeight, HttpCallback<List<ImageAdvert>> callback) {
        getImageAdvertList(adZoneId, picWidth, picHeight, null, callback);
    }

    public void getImageAdvertList(String adZoneId, HttpCallback<List<ImageAdvert>> callback) {
        getImageAdvertList(adZoneId, null, null, callback);
    }

    /**
     * 获取音图广告。K-radio业务使用。
     *
     * @param adZoneId      必填 广告位ID
     * @param picWidth      必填 图片广告位的宽，可传多个，以逗号分隔。
     * @param picHeight     必填 图片广告位的高，可传多个，以逗号分隔。
     *                      对于图片分辨率参数的说明:
     *                      如需要1280×960和1920×1080的图片时 c_picWidth应传"1280,1920" c_picHeight应传"960,1080"
     * @param advancedAttrs 选填 json格式，需要编码。用于精准投放广告或广告服务判断投放广告时机等。
     *                      通过
     *                      <p>
     *                      {@link com.kaolafm.ad.util.AdParamsUtil#getAdvanceAttrs(long, long, long, long, int)}、
     *                      <p>
     *                      {@link com.kaolafm.ad.util.AdParamsUtil#getAlbumAttrs(long, long, int)}、
     *                      <p>
     *                      {@link com.kaolafm.ad.util.AdParamsUtil#getRadioAttrs(long, long, int)}
     *                      获取就是已经处理好的，直接填入就可以了。
     * @param callback      回调
     */
    public void getAudioImageAdvertList(String adZoneId,
                                        String picWidth,
                                        String picHeight,
                                        String advancedAttrs,
                                        HttpCallback<List<AudioImageAdvert>> callback) {
        getAdvertisingList(adZoneId, picWidth, picHeight, "5", advancedAttrs, (Predicate<Advert>) advert -> advert instanceof AudioImageAdvert, callback);
    }

    public void getAudioImageAdvertList(String adZoneId, String advancedAttrs, HttpCallback<List<AudioImageAdvert>> callback) {
        getAudioImageAdvertList(adZoneId, null, null, advancedAttrs, callback);
    }

    /**
     * 获取音图广告。SDK对外使用
     *
     * @param adZoneId  必填 广告位ID
     * @param picWidth  必填 图片广告位的宽，可传多个，以逗号分隔。
     * @param picHeight 必填 图片广告位的高，可传多个，以逗号分隔。
     *                  对于图片分辨率参数的说明:
     *                  如需要1280×960和1920×1080的图片时 c_picWidth应传"1280,1920" c_picHeight应传"960,1080"
     * @param callback  回调
     */
    public void getAudioImageAdvertList(String adZoneId, String picWidth, String picHeight, HttpCallback<List<AudioImageAdvert>> callback) {
        getAudioImageAdvertList(adZoneId, picWidth, picHeight, null, callback);
    }

    public void getAudioImageAdvertList(String adZoneId, HttpCallback<List<AudioImageAdvert>> callback) {
        getAudioImageAdvertList(adZoneId, null, null, callback);
    }

    /**
     * 保存上报的参数
     *
     * @param result
     * @param adCreative
     */
    private void saveReportParams(AdvertisingResult result, AdCreative adCreative) {
        ReportParamEntity reportParamEntity = new ReportParamEntity();
        reportParamEntity.setCreativeId(adCreative.getCreativeId());
        reportParamEntity.setAdGroupId(adCreative.getAdGroupId());
        reportParamEntity.setAdZoneId(adCreative.getAdZoneId());
        reportParamEntity.setCampaignId(adCreative.getCampaignId());
        reportParamEntity.setMediaId(adCreative.getMediaId());
        reportParamEntity.setCustomerId(adCreative.getCustomerId());

        reportParamEntity.setCost(result.getCost());
        reportParamEntity.setMemberId(result.getMemberId());
        String secondaryMemberId = result.getSecondaryMemberId();
        if (!TextUtils.isEmpty(secondaryMemberId)) {
            secondaryMemberId = secondaryMemberId.replaceAll(",", "^");
        }
        reportParamEntity.setSecondaryMemberIds(secondaryMemberId);
        reportParamEntity.setSessionId(result.getSessionId());
        reportParamEntity.setTransType(result.getTransType());
        ReportParamDBManager.getInstance().insert(reportParamEntity);
        //保存对应广告位的轮播数
        SpUtil.putInt(AdConstant.SP_NAME, String.valueOf(adCreative.getAdZoneId()), result.getCbCarousel());

        //设置上报使用的sessionId;
        adCreative.setSessionId(result.getSessionId());
    }


    /**
     * 上报广告引擎错误
     *
     * @param adZoneId
     * @param result
     */
    private void reportError(String adZoneId, BaseResult<AdvertisingResult> result) {
        if (!result.isSuccess()) {
            reportRequestLazy.get().error(adZoneId, result, null);
        }
    }

    public void getTimedAdvertList(String adZoneId,
                                   String picWidth,
                                   String picHeight,
                                   String acceptedAdTypes,
                                   String advancedAttrs,
                                   SocketListener<List<AdvertisingDetails>> listener) {
        HashMap<String, String> params = getParamMap(adZoneId, picWidth, picHeight, acceptedAdTypes, advancedAttrs);
        params.putAll(mCommonParams.get(AdConstant.DOMAIN_NAME_AD).get());
        if (mListener == null) {
            mListener = new SocketListener<AdvertisingResult>() {
                @Override
                public void onSuccess(AdvertisingResult result) {
                    if (result != null) {
                        List<AdCreative> adCreatives = result.getAdCreatives();
                        ArrayList<AdvertisingDetails> details = new ArrayList<>();
                        if (!ListUtil.isEmpty(adCreatives)) {
                            for (AdCreative adCreative : adCreatives) {
                                saveReportParams(result, adCreative);
                                adCreative.setSubtype(AdConstant.TYPE_TIMED_ADVERT);
                                details.add(adCreative);
                            }
                            if (listener != null) {
                                listener.onSuccess(details);
                            }
                        }
                    }
                }

                @Override
                public void onError(ApiException exception) {
                    if (listener != null) {
                        listener.onError(exception);
                    }
                }
            };
        }
        SocketManager.getInstance().addListener(mSocketHost, ParseQS.encode(params), AdConstant.TIMED_ADVERT_EVENT, mListener);
    }

    private HashMap<String, String> getParamMap(String adZoneId, String picWidth, String picHeight, String acceptedAdTypes, String advancedAttrs) {
        return new HashMap<String, String>() {{
                put("adzoneId", adZoneId);
                if (!TextUtils.isEmpty(picWidth)) {
                    put("c_picWidth", picWidth);
                }
                if (!TextUtils.isEmpty(picHeight)) {
                    put("c_picHeight", picHeight);
                }
                if (!TextUtils.isEmpty(advancedAttrs)) {
                    put("advancedAttrs", advancedAttrs);
                }
                if (!TextUtils.isEmpty(acceptedAdTypes)) {
                    put("acceptedAdTypes", acceptedAdTypes);
                }
                put("cb_carousel", String.valueOf(getCarousel(adZoneId)));
            }};
    }

    public void stopSocket() {
        SocketManager.getInstance().removeListener(mSocketHost, AdConstant.TIMED_ADVERT_EVENT);
    }
}
