package com.kaolafm.report.event;

import com.kaolafm.report.util.ReportConstants;

public class HomeScrollReportEvent extends BaseReportEventBean {

    private String remarks1;
    private String remarks2;
    private String remarks3;

    public HomeScrollReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_HOME_SCROLL);
    }

    public void setDownPointer(int rawX, int rawY) {
        StringBuilder sb = new StringBuilder();
        sb.append("(").append(String.valueOf(rawX)).append(",").append(rawY).append(")");
        remarks1 = sb.toString();
    }

    public void setUpPointer(int rawX, int rawY){
        StringBuilder sb = new StringBuilder();
        sb.append("(").append(String.valueOf(rawX)).append(",").append(rawY).append(")");
        remarks2 = sb.toString();
    }

    public void setScreenSize(int screenWidth,int screenHeight){
        StringBuilder sb = new StringBuilder();
        sb.append("(").append(String.valueOf(screenWidth)).append(",").append(screenHeight).append(")");
        remarks3 = sb.toString();
    }

    public String getRemarks1() {
        return remarks1;
    }

    public String getRemarks2() {
        return remarks2;
    }

    public String getRemarks3() {
        return remarks3;
    }

    @Override
    public String toString() {
        return "HomeScrollReportEvent{" +
                "remarks1='" + remarks1 + '\'' +
                ", remarks2='" + remarks2 + '\'' +
                ", remarks3='" + remarks3 + '\'' +
                '}';
    }
}
