package com.kaolafm.opensdk.api.media;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.media.model.ToneQualityResponse;
import com.kaolafm.opensdk.http.core.HttpCallback;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: AudioRequest.java                                               
 *                                                                  *
 * Created in 2018/8/13 上午10:15                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class ToneQualityRequest extends BaseRequest {


    private ToneQualityService mService;

    public ToneQualityRequest() {
        mService = obtainRetrofitService(ToneQualityService.class);
    }


    /**
     * 获取后台配置的音质列表
     *
     * @param callback 回调
     */
    public void getSoundQualities(HttpCallback<ToneQualityResponse> callback) {
        doHttpDeal(mService.getSoundQualities(), BaseResult::getResult, callback);
    }

}
