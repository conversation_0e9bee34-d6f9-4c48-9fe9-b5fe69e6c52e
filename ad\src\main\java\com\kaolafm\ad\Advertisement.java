package com.kaolafm.ad;

import android.app.Application;

import com.kaolafm.opensdk.Engine;

/**
 * <AUTHOR>
 * @date 2019-12-31
 */
public class Advertisement {


    private static Application context;

    private static Engine<AdvertOptions> engine = new AdvertisingEngine();

    private Advertisement() {

    }

    public static void init(Application application) {
        context = application;
        engine.init(application, new AdvertOptions.Builder().build(), null);
    }

    public static Application getApplication() {
        return context;
    }

    public static void release() {
        engine.release();
    }
}
