package com.kaolafm.opensdk.api.recommend;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.recommend.model.BaseSceneListData;
import com.kaolafm.opensdk.api.recommend.model.SceneDataList;

import java.util.HashMap;
import java.util.List;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.QueryMap;

/**
 * <AUTHOR> on 2019-07-18.
 */

public interface RecApiService {

    /**
     * 获取场景电台
     * @param tempMap
     * @return
     */
    @Headers(ApiHostConstants.RECOMMEND_DOMAIN_HEADER)
    @GET(RecRequestConstant.REQUEST_GET_RADIO_SCENE)
    Single<BaseSceneListData<List<SceneDataList>>> getSceneRadioList(@QueryMap HashMap<String, String> tempMap);

    /**
     * 基于车主当前开车场景（车型、时间、地点、车速、年龄、性别、天气等）的场景电台推荐
     * @param params
     * @return
     */
    @Headers(ApiHostConstants.RECOMMEND_DOMAIN_HEADER)
    @GET(RecRequestConstant.REQUEST_PRESENT_SCENE)
    Single<BaseSceneListData<List<SceneDataList>>> getPresentRadioList(@QueryMap HashMap<String, String> params);
}
