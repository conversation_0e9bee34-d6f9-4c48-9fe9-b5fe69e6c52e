package com.kaolafm.opensdk.api.login.internal;

import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.api.login.LoginRequest;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.core.TokenRefresh;

import javax.inject.Inject;

import io.reactivex.Single;

/**
 * <AUTHOR>
 * @date 2020-03-11
 */
@AppScope
public class KaolaTokenRefresh implements TokenRefresh {

    @Inject
    public KaolaTokenRefresh() {
    }

    @Override
    public Single refresh() {
        return new LoginRequest().refreshToken();
    }

    @Override
    public void logout() {
        AccessTokenManager.getInstance().logoutKaola();
    }
}
