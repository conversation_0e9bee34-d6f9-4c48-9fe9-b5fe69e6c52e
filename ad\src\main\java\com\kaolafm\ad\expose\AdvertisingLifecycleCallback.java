package com.kaolafm.ad.expose;

import com.kaolafm.ad.api.model.Advert;

/**
 * 广告生命周期回调
 *
 * <AUTHOR>
 * @date 2020-03-06
 */
public interface AdvertisingLifecycleCallback {
    /**
     * 开始创建广告实例，该方法在请求广告数据之前回调
     * @param adZoneId
     * @param subtype
     */
    void onCreate(String adZoneId, int subtype);

    /**
     * 开始曝光广告，该方法在执行具体的曝光之前回调。如果广告为空就不会回调该方法。
     * @param advert
     */
    void onStart(Advert advert);

    /**
     * 关闭广告，该方法在执行完广告的关闭之后回调。
     * @param advert
     */
    void onClose(Advert advert);

    /**
     * 广告报错。
     * @param adZoneId
     * @param subtype
     * @param e
     */
    void onError(String adZoneId, int subtype, Exception e);
}
