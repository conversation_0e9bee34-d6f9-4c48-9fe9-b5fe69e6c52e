package com.kaolafm.ad.report.parameter;


public class TalkingDataParameter {

    /**
     * 曝光广告的设备类型或者机型信息
     */
    private String devicetype;
    /**
     * 曝光广告的设备IP信息
     */
    private String ip;
    /**
     * 曝光的时间，为long型的计算机时间（13位）
     */
    private long time;
    /**
     * mac地址，原始地址，明文，带“：”
     */
    private String mac;
    /**
     * mac地址计算MD5，规则为原始地址去除“：”转大写，再计算MD5 ，再转大写
     */
    private String mac_md5;
    /**
     * Android下的AndroidID，原文，明文
     */
    private String androidid;
    /**
     * Android下的AndroidID，原文转大写，计算MD5，再转大写
     */
    private String androidid_md5;
    /**
     * Android下的IMEI，原文，明文
     */
    private String imei;
    /**
     * Android下的IMEI，原文转小写，计算MD5，再转十六进制
     */
    private String imei_md5;
    /**
     * Android下的advertising ID（AndroidIDFA），原文，明文
     */
    private String advertisingid;
    /**
     * Android下的advertising ID（AndroidIDFA），原文转大写计算MD5再转大写
     */
    private String advertisingid_md5;
    /**
     * 包名
     */
    private String pname;
    /**
     * 0-Android，1-iOS，2-WP，3-Others
     */
    private String osversion;

    /**
     * 车辆识别号码，17位英数组成；非必填
     */
    private String vin;
    /**
     * 所在城市（城市代码，精确到市县级。非必填）
     */
    private String motocity;
    /**
     * 经纬度（非必填）
     */
    private String location;
    /**
     * 车型（车辆型号值，由英数或符号组成。非必填）
     */
    private String mototype;
    /**
     * 车辆状态（高速、匀速、禁止、堵车；非必填）
     * 0-高速、1-匀速、2-静止、3-堵车；
     */
    private String motostate;
    /**
     * 屏幕类型（前排屏幕、后排屏幕、后视镜；非必填）
     * 0-前排屏幕、1-后排屏幕、2-后视镜；
     */
    private String screentype;

    public String getDevicetype() {
        return devicetype;
    }

    public String getIp() {
        return ip;
    }

    public long getTime() {
        return time;
    }

    public String getMac() {
        return mac;
    }

    public String getMac_md5() {
        return mac_md5;
    }

    public String getAndroidid() {
        return androidid;
    }

    public String getAndroidid_md5() {
        return androidid_md5;
    }

    public String getImei() {
        return imei;
    }

    public String getImei_md5() {
        return imei_md5;
    }

    public String getAdvertisingid() {
        return advertisingid;
    }

    public String getAdvertisingid_md5() {
        return advertisingid_md5;
    }

    public String getPname() {
        return pname;
    }

    public String getOsversion() {
        return osversion;
    }

    public void setDevicetype(String devicetype) {
        this.devicetype = devicetype;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public void setTime(long time) {
        this.time = time;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public void setMac_md5(String mac_md5) {
        this.mac_md5 = mac_md5;
    }

    public void setAndroidid(String androidid) {
        this.androidid = androidid;
    }

    public void setAndroidid_md5(String androidid_md5) {
        this.androidid_md5 = androidid_md5;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public void setImei_md5(String imei_md5) {
        this.imei_md5 = imei_md5;
    }

    public void setAdvertisingid(String advertisingid) {
        this.advertisingid = advertisingid;
    }

    public void setAdvertisingid_md5(String advertisingid_md5) {
        this.advertisingid_md5 = advertisingid_md5;
    }

    public void setPname(String pname) {
        this.pname = pname;
    }

    public void setOsversion(String osversion) {
        this.osversion = osversion;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getMotocity() {
        return motocity;
    }

    public void setMotocity(String motocity) {
        this.motocity = motocity;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getMototype() {
        return mototype;
    }

    public void setMototype(String mototype) {
        this.mototype = mototype;
    }

    public String getMotostate() {
        return motostate;
    }

    public void setMotostate(String motostate) {
        this.motostate = motostate;
    }

    public String getScreentype() {
        return screentype;
    }

    public void setScreentype(String screentype) {
        this.screentype = screentype;
    }

    @Override
    public String toString() {
        return "TalkingDataParameter{" +
                "devicetype='" + devicetype + '\'' +
                ", ip='" + ip + '\'' +
                ", time=" + time +
                ", mac='" + mac + '\'' +
                ", mac_md5='" + mac_md5 + '\'' +
                ", androidid='" + androidid + '\'' +
                ", androidid_md5='" + androidid_md5 + '\'' +
                ", imei='" + imei + '\'' +
                ", imei_md5='" + imei_md5 + '\'' +
                ", advertisingid='" + advertisingid + '\'' +
                ", advertisingid_md5='" + advertisingid_md5 + '\'' +
                ", pname='" + pname + '\'' +
                ", osversion='" + osversion + '\'' +
                ", vin='" + vin + '\'' +
                ", motocity='" + motocity + '\'' +
                ", location='" + location + '\'' +
                ", mototype='" + mototype + '\'' +
                ", motostate='" + motostate + '\'' +
                ", screentype='" + screentype + '\'' +
                '}';
    }
}
