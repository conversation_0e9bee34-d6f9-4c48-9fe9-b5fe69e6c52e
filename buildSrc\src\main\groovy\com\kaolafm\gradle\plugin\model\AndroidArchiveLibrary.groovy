package com.kaolafm.gradle.plugin.model

import org.gradle.api.Project
import org.gradle.api.artifacts.ModuleVersionIdentifier
import org.gradle.api.artifacts.ResolvedArtifact
import org.w3c.dom.Document
import org.w3c.dom.Element

import javax.xml.parsers.DocumentBuilderFactory
/**
 * Created by Vigi on 2017/2/16.
 * Modify by kezong on 2019/7/16.
 */
class AndroidArchiveLibrary {

    private final Project mProject

    private final ResolvedArtifact mArtifact

    private final String mVariantName

    AndroidArchiveLibrary(Project project, ResolvedArtifact artifact, String variantName) {
        if (!"aar".equals(artifact.getType())) {
            throw new IllegalArgumentException("artifact must be aar type!")
        }
        mProject = project
        mArtifact = artifact
        mVariantName = variantName
    }

    String getGroup() {
        return mArtifact.getModuleVersion().getId().getGroup()
    }

    String getName() {
        return mArtifact.getModuleVersion().getId().getName()
    }

    String getVersion() {
        return mArtifact.getModuleVersion().getId().getVersion()
    }

    File getRootFolder() {
        String explodedRootDir = mProject.getBuildDir().absolutePath + "/intermediates" + "/exploded-aar/"
        ModuleVersionIdentifier id = mArtifact.getModuleVersion().getId()
        return mProject.file(explodedRootDir
                + "/" + id.getGroup()
                + "/" + id.getName()
                + "/" + id.getVersion()
                + "/" + mVariantName)
    }

    File getAidlFolder() {
        return new File(getRootFolder(), "aidl")
    }

    File getAssetsFolder() {
        return new File(getRootFolder(), "assets")
    }

    File getClassesJarFile() {
        return new File(getRootFolder(), "classes.jar")
    }

    Set getSourceDir() {
        String moduleName = mArtifact.getModuleVersion().getId().getName()
        Project module = mProject.rootProject.project(moduleName)
        return module.android.sourceSets.main.java.srcDirs
    }

    File getFile() {
        return mArtifact.file
    }

    Collection<File> getLocalJars() {
        List<File> localJars = new ArrayList<>()
        File[] jarList = new File(getRootFolder(), "libs").listFiles()
        if (jarList != null) {
            for (File jars : jarList) {
                if (jars.isFile() && jars.getName().endsWith(".jar")) {
                    localJars.add(jars)
                }
            }
        }

        return localJars
    }

    File getJniFolder() {
        return new File(getRootFolder(), "jni")
    }

    File getResFolder() {
        return new File(getRootFolder(), "res")
    }

    File getManifest() {
        return new File(getRootFolder(), "AndroidManifest.xml")
    }

    File getLintJar() {
        return new File(getRootFolder(), "lint.jar")
    }

    List<File> getProguardRules() {
        List<File> list = new ArrayList<>()
        list.add(new File(getRootFolder(), "proguard-rules.pro"))
        list.add(new File(getRootFolder(), "proguard-project.txt"))
        return list
    }

    File getSymbolFile() {
        return new File(getRootFolder(), "R.txt")
    }

    String getPackageName() {
        String packageName = null
        File manifestFile = getManifest()
        if (manifestFile.exists()) {
            try {
                DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance()
                Document doc = dbf.newDocumentBuilder().parse(manifestFile)
                Element element = doc.getDocumentElement()
                packageName = element.getAttribute("package")
            } catch (Exception e) {
                e.printStackTrace()
            }
        } else {
            throw new RuntimeException(getName() + " module's AndroidManifest not found")
        }
        return packageName
    }
    def getSrcDirs() {
        return mProject.android.sourceSets.findByName("main").java.srcDirs[0]
    }

}
