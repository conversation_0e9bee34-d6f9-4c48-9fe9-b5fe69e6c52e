package com.kaolafm.opensdk.api.search.model;

import com.google.gson.annotations.SerializedName;
import com.kaolafm.opensdk.http.core.Response;
import java.util.List;

/**
 * 语音搜索返回原始结果
 * <AUTHOR>
 * @date 2018/8/6
 */

public class VoiceSearchResult extends Response {

    /**
     * playType : 1
     * delayTime : 0
     * playIndex : 0
     * dataList : [{"id":1200000000332,"name":"相声频道","img":"http://img.kaolafm.net/mz/images/201701/d41bcdc9-3cc3-493e-bbe3-1108db5edcb2/defaul
     * t.jpg","comperes":[],"type":3,"albumName":null,"source":1,"duration":0,"playUrl":"","oldId":0,"freq":"","sourceName":"考拉喜马拉雅"},{"id":1200000000185,"name":"相声频道.fm","img":"http://img.kaolafm.net/mz/images/201412/941e6979-9f4a-4929-a3c5-7b7512a41859/defaul
     * t.jpg","comperes":[],"type":3,"albumName":null,"source":1,"duration":0,"playUrl":"","oldId":0,"freq":"","sourceName":"考拉喜马拉雅"}]
     */
    /** 节目列表*/
    @SerializedName("dataList")
    private List<VoiceSearchProgramBean> programList;

    /**
     * 播放延迟时间单位为ms,playTime为2时有效
     */
    @SerializedName("delayTime")
    private int delayTime;

    /**
     * 播放音频下标,下标以0开始；大于0，表示列表播放，取到此下标位置
     */
    @SerializedName("playIndex")
    private int playIndex;

    /**
     * 播放类型0: 选择播放,1: 直接播放,2: 延时播放
     */
    @SerializedName("playType")
    private int playType;


    public List<VoiceSearchProgramBean> getProgramList() {
        return programList;
    }

    public int getDelayTime() {
        return delayTime;
    }

    public int getPlayIndex() {
        return playIndex;
    }

    public int getPlayType() {
        return playType;
    }

    public void setProgramList(List<VoiceSearchProgramBean> programList) {
        this.programList = programList;
    }

    public void setDelayTime(int delayTime) {
        this.delayTime = delayTime;
    }

    public void setPlayIndex(int playIndex) {
        this.playIndex = playIndex;
    }

    public void setPlayType(int playType) {
        this.playType = playType;
    }

    @Override
    public String toString() {
        return "VoiceSearchResult{" +
                "programList=" + programList +
                ", delayTime=" + delayTime +
                ", playIndex=" + playIndex +
                ", playType=" + playType +
                '}' + super.toString();
    }
}
