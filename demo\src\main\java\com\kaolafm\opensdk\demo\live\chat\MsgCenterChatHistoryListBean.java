package com.kaolafm.opensdk.demo.live.chat;

import android.os.Parcel;
import android.os.Parcelable;

import com.kaolafm.opensdk.demo.live.util.DateFormatUtil;

/**
 *
 */
public class MsgCenterChatHistoryListBean implements Parcelable {

    /**
     * +V用户
     */
    public static final int USER_V_ANCHOR = 1;

    public static final int OFFICIAL_USER_YES = 1;
    /**
     * 屏蔽与被屏蔽
     */
    public static final int SHIELD_NO = 0;
    public static final int SHIELD_YES = 1;

    /**
     * 是不是官方服务号
     */
    public int isOfficialUser = -1;
    public String uid;
    public String nick;
    public int isShield;
    public int isVanchor;
    public String avatar;
    public int notReadCount;
    public String lastMsg;
    public long createTime;

    public MsgCenterChatHistoryListBean() {
    }

    public MsgCenterChatHistoryListBean(Parcel in) {
        in.readInt();
        in.readInt();
        in.readInt();
        in.readInt();

        in.readString();
        in.readString();
        in.readString();
        in.readString();

        in.readLong();
    }

    /**
     * 用户是不是VIP
     *
     * @return
     */
    public boolean isUserVanchor() {
        return isVanchor == USER_V_ANCHOR;
    }

    /**
     * 获取产品定义的时间样式 不要2016年的20
     *
     * @return
     */
    public String getDisplayTime() {
        String timeForDisplay = DateFormatUtil.getDateByTimeStamp(
                DateFormatUtil.DATE_FORMAT_PATTERN_6, createTime);
        return timeForDisplay.substring(2, timeForDisplay.length());
    }

    /**
     * 用户是否被屏蔽
     *
     * @return
     */
    public boolean isUserShielt() {
        return isShield == SHIELD_YES;
    }

    /**
     * 用户是官方账号
     *
     * @return
     */
    public boolean isOfficialUser() {
        return isOfficialUser == OFFICIAL_USER_YES;
    }


    public static final Creator<MsgCenterChatHistoryListBean> CREATOR = new Creator<MsgCenterChatHistoryListBean>() {
        @Override
        public MsgCenterChatHistoryListBean createFromParcel(Parcel in) {
            return new MsgCenterChatHistoryListBean(in);
        }

        @Override
        public MsgCenterChatHistoryListBean[] newArray(int size) {
            return new MsgCenterChatHistoryListBean[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {

        dest.writeInt(isOfficialUser);
        dest.writeInt(isShield);
        dest.writeInt(isVanchor);
        dest.writeInt(notReadCount);

        dest.writeString(uid);
        dest.writeString(nick);
        dest.writeString(avatar);
        dest.writeString(lastMsg);

        dest.writeLong(createTime);
    }
}