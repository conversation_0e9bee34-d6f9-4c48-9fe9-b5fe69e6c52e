package com.kaolafm.report.util;

import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.report.model.ReportBean;
import com.kaolafm.report.model.ReportTask;


/**
 * <AUTHOR> on 2019/1/10.
 */

public class ReportUploadShenCeTask {
    private ReportBean mReportBean;

    public ReportUploadShenCeTask(ReportBean reportBean) {
        mReportBean = reportBean;
    }

    public void report() {
        ReportNetworkHelper.getInstance().request(mReportBean.getReportValue(), isOk -> {
            if (isOk) {
                disposeSuccessResult();
            } else {
                disposeErrorResult();
            }
        });
    }

    public void disposeErrorResult() {
        if (mReportBean.getType() == ReportConstants.UPLOAD_TASK_TYPE_NORMAL) {
            ReportTask reportTask = new ReportTask();
            reportTask.setType(ReportConstants.TASK_TYPE_INSERT);
            reportTask.setSingleTask(ReportShenCeDBHelper.getInstance().insertData(mReportBean.getReportValue()));
            ReportShenCeTaskHelper.getInstance().insertTask(reportTask);
        } else {
            ReportShenCeTaskHelper.getInstance().taskDone();
        }
    }

    public void disposeSuccessResult() {
        if (mReportBean.getType() != ReportConstants.UPLOAD_TASK_TYPE_BY_DATA_BASE) {
            return;
        }

        ReportTask reportTask = new ReportTask();
        reportTask.setType(ReportConstants.TASK_TYPE_DELETE);
        reportTask.setSingleTask(ReportShenCeDBHelper.getInstance().deleteDataList(mReportBean.getIdList()));
        ReportShenCeTaskHelper.getInstance().insertTask(reportTask);
        ReportShenCeTaskHelper.getInstance().taskDone();
    }


}
