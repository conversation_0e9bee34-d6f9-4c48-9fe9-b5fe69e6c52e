package com.kaolafm.opensdk.demo;

import android.os.Handler;
import android.util.Log;

import com.kaolafm.ad.api.model.AudioAdvert;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.ad.expose.AdvertisingPlayer;
import com.kaolafm.opensdk.http.error.ApiException;

/**
 * <AUTHOR>
 * @date 2020-03-09
 */
public class AdvertPlayerImpl implements AdvertisingPlayer {
    @Override
    public void play(AudioAdvert audioAdvert) {
        Log.e("AdvertPlayerImpl", "play: "+audioAdvert.getLocalPath());
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                AdvertisingManager.getInstance().close(audioAdvert);
            }
        }, 6000);
    }

    @Override
    public void stop(AudioAdvert audioAdvert) {
        Log.e("AdvertPlayerImpl", "stop: "+audioAdvert);
    }

    @Override
    public void pause(AudioAdvert audioAdvert) {
        Log.e("AdvertPlayerImpl", "pause: "+audioAdvert);
    }

    @Override
    public void error(String adZoneId, int subtype, ApiException e) {
        Log.e("AdvertPlayerImpl", "error: "+adZoneId+">>>"+subtype);
    }
}
