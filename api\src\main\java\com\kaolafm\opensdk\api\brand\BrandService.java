package com.kaolafm.opensdk.api.brand;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.brand.model.BrandDetails;

import java.util.List;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Query;

/**
 * <AUTHOR>
 * @date 2019-03-28
 */
public interface BrandService {

    /**
     * 获取品牌信息，包括名称，log，用户须知
     */
    @Headers(ApiHostConstants.BRAND_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_BRAND_INFO)
    Single<BaseResult<BrandDetails>> getBrandInfo();
}
