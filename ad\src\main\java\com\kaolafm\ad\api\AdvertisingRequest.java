package com.kaolafm.ad.api;

import com.kaolafm.ad.api.internal.AdInternalRequest;
import com.kaolafm.ad.api.model.AdvertisingDetails;
import com.kaolafm.ad.api.model.AudioAdvert;
import com.kaolafm.ad.api.model.AudioImageAdvert;
import com.kaolafm.ad.api.model.ImageAdvert;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.List;

/**
 * 广告相关网络请求。
 * <AUTHOR>
 * @date 2019-12-31
 */
public class AdvertisingRequest {

    private AdInternalRequest request;

    public AdvertisingRequest() {
        request = new AdInternalRequest();
    }

    /**
     * 获取广告详情列表
     *
     * @param adZoneId  必填 广告位ID。
     * @param picWidth  选填 图片广告位的宽 可传多个 以逗号分隔 在请求包含图片信息的广告位时为必填。
     * @param picHeight 选填 图片广告位的高 可传多个 以逗号分隔 在请求包含图片信息的广告位时为必填。
     *                  对于图片分辨率参数的说明:
     *                  如需要1280×960和1920×1080的图片时 c_picWidth应传"1280,1920" c_picHeight应传"960,1080"
     * @param callback  必填 选填
     */
    public void getAdvertisingList(String adZoneId,
                                   String picWidth,
                                   String picHeight,
                                   HttpCallback<List<AdvertisingDetails>> callback) {
        request.getAdvertisingList(adZoneId, picWidth, picHeight, null, null, callback);
    }

    /**
     * 获取音频广告。SDK对外使用。
     *
     * @param adZoneId 必填 广告位ID
     * @param callback 回调
     */
    public void getAudioAdvertList(String adZoneId, HttpCallback<List<AudioAdvert>> callback) {
        request.getAudioAdvertList(adZoneId, callback);
    }

    /**
     * 获取图片广告。SDK对外使用
     *
     * @param adZoneId  必填 广告位ID
     * @param picWidth  必填 图片广告位的宽，可传多个，以逗号分隔。
     * @param picHeight 必填 图片广告位的高，可传多个，以逗号分隔。
     *                  对于图片分辨率参数的说明:
     *                  如需要1280×960和1920×1080的图片时 c_picWidth应传"1280,1920" c_picHeight应传"960,1080"
     * @param callback  回调
     */
    public void getImageAdvertList(String adZoneId, String picWidth, String picHeight, HttpCallback<List<ImageAdvert>> callback) {
        request.getImageAdvertList(adZoneId, picWidth, picHeight, callback);
    }

    /**
     * 获取音图广告。SDK对外使用
     *
     * @param adZoneId  必填 广告位ID
     * @param picWidth  必填 图片广告位的宽，可传多个，以逗号分隔。
     * @param picHeight 必填 图片广告位的高，可传多个，以逗号分隔。
     *                  对于图片分辨率参数的说明:
     *                  如需要1280×960和1920×1080的图片时 c_picWidth应传"1280,1920" c_picHeight应传"960,1080"
     * @param callback  回调
     */
    public void getAudioImageAdvertList(String adZoneId, String picWidth, String picHeight, HttpCallback<List<AudioImageAdvert>> callback) {
        request.getAudioImageAdvertList(adZoneId, picWidth, picHeight, callback);
    }

}
