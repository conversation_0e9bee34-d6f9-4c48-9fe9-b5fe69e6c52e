package com.kaolafm.opensdk.api.media;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.media.model.AlbumDetails;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.api.media.model.VideoAlbumDetails;
import com.kaolafm.opensdk.api.media.model.VideoAudioDetails;

import java.util.List;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Query;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: AlbumService.java                                               
 *                                                                  *
 * Created in 2018/8/10 下午2:49                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
/*package*/ interface VideoAlbumService {

//    @Headers("https://26e9a8ef-3e24-456a-9bc7-2a4479e5bf57.mock.pstmn.io")
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_VIDEO_ALBUM_DETAILS)
    Single<BaseResult<List<VideoAlbumDetails>>> getAlbumDetails(@Query("ids") long albumId);

//    @Headers("https://26e9a8ef-3e24-456a-9bc7-2a4479e5bf57.mock.pstmn.io")
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_VIDEO_ALBUM_DETAILS)
    Single<BaseResult<List<VideoAlbumDetails>>> getAlbumDetails(@Query("ids") String albumId);


//    @Headers("https://26e9a8ef-3e24-456a-9bc7-2a4479e5bf57.mock.pstmn.io")
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_VIDEO_AUDIO_LIST)
    Single<BaseResult<BasePageResult<List<VideoAudioDetails>>>> getPlaylist(@Query("albumid") long radioId, @Query("sorttype") int sortType, @Query("pagesize") int pageSize, @Query("pagenum") int pageNum, @Query("pieceid") long audioId);
}