package com.kaolafm.opensdk.api.media;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.media.model.AIAudioDetails;
import com.kaolafm.opensdk.api.media.model.RadioDetails;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.HashMap;
import java.util.List;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: RadioRequest.java                                               
 *                                                                  *
 * Created in 2018/8/10 下午5:03                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class RadioRequest extends BaseRequest {

    private RadioService mService;

    public RadioRequest() {
        mService = obtainRetrofitService(RadioService.class);
    }


    /**
     * 获取Radio(PGC)详情
     *
     * @param radioId  必填 AI电台Id
     * @param callback 回调
     */
    public void getRadioDetails(long radioId, HttpCallback<RadioDetails> callback) {
        doHttpDeal(mService.getRadioDetails(radioId), BaseResult::getResult, callback);
    }


    /**
     * 获取Radio(PGC)的播单
     *
     * @param radioId  必填 AI电台Id
     * @param clockId  必填 上一次请求返回的clockId，第二次请求必填
     * @param callback 回调
     */
    public void getPlaylist(long radioId, String clockId, HttpCallback<List<AIAudioDetails>> callback) {
        getPlaylist(radioId, clockId, null, null, callback);
    }

    /**
     * 获取Radio(PGC)的播单
     *
     * @param radioId  必填 AI电台Id
     * @param clockId  必填 上一次请求返回的clockId，第二次请求必填
     * @param areaCode 选填 地区编码
     * @param cityName 选填 地区城市名称
     * @param callback 回调
     */
    public void getPlaylist(long radioId, String clockId, String areaCode, String cityName,
                            HttpCallback<List<AIAudioDetails>> callback) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("rid", radioId);
        params.put("clockid", clockId);
        putNullParam(params, "areaCode", areaCode);
        putNullParam(params, "cityName", cityName);
        doHttpDeal(mService.getPlaylist(params), BaseResult::getResult, callback);
    }


}
