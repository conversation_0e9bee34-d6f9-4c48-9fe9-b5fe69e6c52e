com.kaolafm.base.internal.AndroidIdObtainDeviceId -> com.kaolafm.base.a.a:
    void <init>() -> <init>
    java.lang.String createUUID(android.content.Context) -> a
com.kaolafm.base.internal.BaseObtainDeviceId -> com.kaolafm.base.a.b:
    java.lang.String mId -> a
    int mIndex -> b
    java.util.List mList -> c
    com.kaolafm.base.internal.ObtainDeviceId mNextChain -> d
    void <init>() -> <init>
    java.lang.String getUUID(android.content.Context) -> getUUID
    void setNextChain(java.lang.String,java.util.List,int) -> setNextChain
    void saveUUID(java.lang.String,int) -> saveUUID
    java.lang.String createUUID(android.content.Context) -> a
com.kaolafm.base.internal.CacheObtainDeviceId -> com.kaolafm.base.a.c:
    java.lang.String KAOLA_AUTO_BASE -> b
    java.lang.String UDID_PATH_KAOLA_API -> c
    java.lang.String UDID_PATH_UDID_FILE -> d
    java.lang.String UDID_FILE_PATH -> e
    java.lang.String UDID_PREFERENCE_NAME -> f
    java.lang.String UDID_VALUE -> g
    int TYPE_NEW -> h
    int TYPE_SP -> i
    int TYPE_FILE -> j
    int mSourceType -> k
    android.content.Context mContext -> l
    java.lang.Thread mSaveThread -> m
    java.util.concurrent.ExecutorService mExecutorService -> n
    java.lang.Runnable mTask -> o
    void <init>() -> <init>
    java.lang.String createUUID(android.content.Context) -> a
    void saveUUID(java.lang.String,int) -> saveUUID
    void saveUUID(java.lang.String) -> a
    void putId(java.lang.String) -> b
    android.util.Pair getId() -> a
    void lambda$saveUUID$0(java.lang.String) -> c
    void <clinit>() -> <clinit>
com.kaolafm.base.internal.DefaultObtainDeviceId -> com.kaolafm.base.a.d:
    void <init>() -> <init>
    java.lang.String createUUID(android.content.Context) -> a
com.kaolafm.base.internal.DeviceId -> com.kaolafm.base.a.e:
    java.lang.String deviceId -> a
    void <init>() -> <init>
    java.lang.String getDeviceId(android.content.Context) -> a
    boolean isIdInvalid(java.lang.String) -> a
com.kaolafm.base.internal.DeviceIdFactory -> com.kaolafm.base.a.f:
    java.util.List mList -> a
    com.kaolafm.base.internal.DeviceIdFactory mInstance -> b
    com.kaolafm.base.internal.DeviceIdFactory getInstance() -> a
    void <init>() -> <init>
    java.lang.String getDeviceId(android.content.Context) -> a
    void add(com.kaolafm.base.internal.ObtainDeviceId) -> a
    void add(int,com.kaolafm.base.internal.ObtainDeviceId) -> a
    void set(int,com.kaolafm.base.internal.ObtainDeviceId) -> b
    void remove(int) -> a
    void <clinit>() -> <clinit>
com.kaolafm.base.internal.ObtainDeviceId -> com.kaolafm.base.a.g:
    java.lang.String getUUID(android.content.Context) -> getUUID
    void saveUUID(java.lang.String,int) -> saveUUID
    void setNextChain(java.lang.String,java.util.List,int) -> setNextChain
com.kaolafm.base.internal.RandomObtainDeviceId -> com.kaolafm.base.a.h:
    java.lang.String CHARSET_NAME -> b
    void <init>() -> <init>
    java.lang.String createUUID(android.content.Context) -> a
com.kaolafm.base.utils.DateUtil -> com.kaolafm.base.utils.DateUtil:
    java.lang.String ONE_SECOND_AGO -> ONE_SECOND_AGO
    java.lang.String ONE_MINUTE_AGO -> ONE_MINUTE_AGO
    java.lang.String ONE_HOUR_AGO -> ONE_HOUR_AGO
    java.lang.String ONE_DAY_AGO -> ONE_DAY_AGO
    java.lang.String ONE_MONTH_AGO -> ONE_MONTH_AGO
    java.lang.String ONE_YEAR_AGO -> ONE_YEAR_AGO
    long mServerTime -> mServerTime
    long mResponseTime -> mResponseTime
    void <init>() -> <init>
    long string2Millis(java.lang.String) -> string2Millis
    long string2Millis(java.lang.String,java.lang.String) -> string2Millis
    long string2Millis(java.lang.String,java.lang.String,java.util.Locale) -> string2Millis
    java.lang.String formatMillis(java.lang.String,long) -> formatMillis
    java.lang.String formatMillis(long) -> formatMillis
    java.lang.String millis2Day(long) -> millis2Day
    java.lang.String getDisTimeStr(long) -> getDisTimeStr
    void setServerTime(java.lang.String) -> setServerTime
    long getServerTime() -> getServerTime
com.kaolafm.base.utils.DeviceUtil -> com.kaolafm.base.utils.DeviceUtil:
    int PRIVIDER_UNKNOWN -> PRIVIDER_UNKNOWN
    int PRIVIDER_CHINA_MOBILE -> PRIVIDER_CHINA_MOBILE
    int PRIVIDER_CHINA_UNICOM -> PRIVIDER_CHINA_UNICOM
    int PRIVIDER_CHINA_TELE -> PRIVIDER_CHINA_TELE
    void <init>() -> <init>
    java.lang.String getClientIP() -> getClientIP
    java.lang.String getDeviceId(android.content.Context) -> getDeviceId
    java.lang.String getScreenResolution(android.content.Context) -> getScreenResolution
    java.lang.String getImei(android.content.Context) -> getImei
    java.lang.String getScreenSize(android.content.Context) -> getScreenSize
    java.lang.String getDeviceDns() -> getDeviceDns
    boolean isIdValid(java.lang.String) -> isIdValid
    java.lang.String getIMSI(android.content.Context) -> getIMSI
    int getProvidersName(android.content.Context) -> getProvidersName
com.kaolafm.base.utils.FileUtil -> com.kaolafm.base.utils.FileUtil:
    java.lang.String ENCRYPTED_FLAG_SUFFIX -> ENCRYPTED_FLAG_SUFFIX
    void <init>() -> <init>
    java.lang.String readAssetFile(android.content.Context,java.lang.String,java.lang.String) -> readAssetFile
    java.lang.String readFileFromSDCard(java.io.File) -> readFileFromSDCard
    java.lang.String readFileFromSDCard(java.lang.String) -> readFileFromSDCard
    void writeFileToSDCard(java.lang.String,java.lang.String) -> writeFileToSDCard
    void writeFileToSDCard(java.lang.String,java.lang.String,boolean) -> writeFileToSDCard
    boolean deleteFile(java.lang.String) -> deleteFile
    boolean deleteFile(java.io.File) -> deleteFile
    void writeEncryptedFileToSDCard(java.lang.String,java.lang.String) -> writeEncryptedFileToSDCard
    java.lang.String readDecryptedFileFromSDCard(java.lang.String) -> readDecryptedFileFromSDCard
    boolean isEncryptedFileExiste(java.lang.String) -> isEncryptedFileExiste
    java.lang.String getEncryptedFilePath(java.lang.String) -> getEncryptedFilePath
    void closeIO(java.io.Closeable[]) -> closeIO
    java.io.File getDiskCacheDir(android.content.Context,java.lang.String) -> getDiskCacheDir
    boolean isSDCardExist() -> isSDCardExist
    java.lang.String getDiskFilesDir(android.content.Context) -> getDiskFilesDir
    java.lang.String getDiskFilesDir(android.content.Context,java.lang.String) -> getDiskFilesDir
    void recreate(java.io.File) -> recreate
    void recreate(java.io.File,long) -> recreate
    void setLength(java.io.File,long) -> setLength
    java.nio.channels.FileChannel getChannel(java.io.File) -> getChannel
com.kaolafm.base.utils.KaolaTask -> com.kaolafm.base.utils.KaolaTask:
    int CPU_COUNT -> CPU_COUNT
    int CORE_POOL_SIZE -> CORE_POOL_SIZE
    int MAXIMUM_POOL_SIZE -> MAXIMUM_POOL_SIZE
    int KEEP_ALIVE -> KEEP_ALIVE
    java.util.concurrent.ThreadFactory sThreadFactory -> sThreadFactory
    java.util.concurrent.BlockingQueue sPoolWorkQueue -> sPoolWorkQueue
    java.util.concurrent.Executor THREAD_POOL_EXECUTOR -> THREAD_POOL_EXECUTOR
    int MESSAGE_POST_RESULT -> MESSAGE_POST_RESULT
    int MESSAGE_POST_PROGRESS -> MESSAGE_POST_PROGRESS
    com.kaolafm.base.utils.KaolaTask$InternalHandler sHandler -> sHandler
    java.util.concurrent.Executor sDefaultExecutor -> sDefaultExecutor
    com.kaolafm.base.utils.KaolaTask$WorkerRunnable mWorker -> mWorker
    java.util.concurrent.FutureTask mFuture -> mFuture
    com.kaolafm.base.utils.KaolaTask$Status mStatus -> mStatus
    java.util.concurrent.atomic.AtomicBoolean mCancelled -> mCancelled
    java.util.concurrent.atomic.AtomicBoolean mTaskInvoked -> mTaskInvoked
    java.lang.Object tag -> tag
    java.lang.Object getTag() -> getTag
    void setTag(java.lang.Object) -> setTag
    void init() -> init
    void <init>() -> <init>
    void postResultIfNotInvoked(java.lang.Object) -> postResultIfNotInvoked
    java.lang.Object postResult(java.lang.Object) -> postResult
    com.kaolafm.base.utils.KaolaTask$Status getStatus() -> getStatus
    java.lang.Object doInBackground(java.lang.Object[]) -> doInBackground
    void onPreExecute() -> onPreExecute
    void onPostExecute(java.lang.Object) -> onPostExecute
    void onExecute(java.lang.Object) -> onExecute
    void onProgressUpdate(java.lang.Object[]) -> onProgressUpdate
    void onCancelled(java.lang.Object) -> onCancelled
    void onCancelled() -> onCancelled
    boolean isCancelled() -> isCancelled
    boolean cancel(boolean) -> cancel
    java.lang.Object get() -> get
    java.lang.Object get(long,java.util.concurrent.TimeUnit) -> get
    com.kaolafm.base.utils.KaolaTask execute(java.lang.Object[]) -> execute
    com.kaolafm.base.utils.KaolaTask executeOnExecutor(java.util.concurrent.Executor,java.lang.Object[]) -> executeOnExecutor
    void execute(java.lang.Runnable) -> execute
    void publishProgress(java.lang.Object[]) -> publishProgress
    void finish(java.lang.Object) -> finish
    java.util.concurrent.atomic.AtomicBoolean access$100(com.kaolafm.base.utils.KaolaTask) -> access$100
    java.lang.Object access$200(com.kaolafm.base.utils.KaolaTask,java.lang.Object) -> access$200
    void access$300(com.kaolafm.base.utils.KaolaTask,java.lang.Object) -> access$300
    void access$400(com.kaolafm.base.utils.KaolaTask,java.lang.Object) -> access$400
    void <clinit>() -> <clinit>
com.kaolafm.base.utils.KaolaTask$1 -> com.kaolafm.base.utils.KaolaTask$1:
    java.util.concurrent.atomic.AtomicInteger mCount -> mCount
    void <init>() -> <init>
    java.lang.Thread newThread(java.lang.Runnable) -> newThread
com.kaolafm.base.utils.KaolaTask$2 -> com.kaolafm.base.utils.KaolaTask$2:
    com.kaolafm.base.utils.KaolaTask this$0 -> this$0
    void <init>(com.kaolafm.base.utils.KaolaTask) -> <init>
    java.lang.Object call() -> call
com.kaolafm.base.utils.KaolaTask$3 -> com.kaolafm.base.utils.KaolaTask$3:
    com.kaolafm.base.utils.KaolaTask this$0 -> this$0
    void <init>(com.kaolafm.base.utils.KaolaTask,java.util.concurrent.Callable) -> <init>
    void done() -> done
com.kaolafm.base.utils.KaolaTask$4 -> com.kaolafm.base.utils.KaolaTask$4:
    int[] $SwitchMap$com$kaolafm$base$utils$KaolaTask$Status -> $SwitchMap$com$kaolafm$base$utils$KaolaTask$Status
    void <clinit>() -> <clinit>
com.kaolafm.base.utils.KaolaTask$InternalHandler -> com.kaolafm.base.utils.KaolaTask$InternalHandler:
    void <init>() -> <init>
    void handleMessage(android.os.Message) -> handleMessage
com.kaolafm.base.utils.KaolaTask$KaolaTaskResult -> com.kaolafm.base.utils.KaolaTask$KaolaTaskResult:
    com.kaolafm.base.utils.KaolaTask mTask -> mTask
    java.lang.Object[] mData -> mData
    void <init>(com.kaolafm.base.utils.KaolaTask,java.lang.Object[]) -> <init>
com.kaolafm.base.utils.KaolaTask$Status -> com.kaolafm.base.utils.KaolaTask$Status:
    com.kaolafm.base.utils.KaolaTask$Status PENDING -> PENDING
    com.kaolafm.base.utils.KaolaTask$Status RUNNING -> RUNNING
    com.kaolafm.base.utils.KaolaTask$Status FINISHED -> FINISHED
    com.kaolafm.base.utils.KaolaTask$Status[] $VALUES -> $VALUES
    com.kaolafm.base.utils.KaolaTask$Status[] values() -> values
    com.kaolafm.base.utils.KaolaTask$Status valueOf(java.lang.String) -> valueOf
    void <init>(java.lang.String,int) -> <init>
    void <clinit>() -> <clinit>
com.kaolafm.base.utils.KaolaTask$WorkerRunnable -> com.kaolafm.base.utils.KaolaTask$WorkerRunnable:
    java.lang.Object[] mParams -> mParams
    void <init>() -> <init>
    void <init>(com.kaolafm.base.utils.KaolaTask$1) -> <init>
com.kaolafm.base.utils.ListUtil -> com.kaolafm.base.utils.ListUtil:
    void <init>() -> <init>
    boolean isEmpty(android.util.SparseArray) -> isEmpty
    boolean isEmpty(java.util.Collection) -> isEmpty
    boolean isEmpty(java.util.Map) -> isEmpty
    boolean isEmpty(java.lang.Object[]) -> isEmpty
com.kaolafm.base.utils.MD5 -> com.kaolafm.base.utils.MD5:
    char[] hexDigits -> hexDigits
    void <init>() -> <init>
    java.lang.String hexdigest(java.lang.String) -> hexdigest
    java.lang.String hexdigest(byte[]) -> hexdigest
    void main(java.lang.String[]) -> main
    java.lang.String getMD5Str(java.lang.String) -> getMD5Str
    void <clinit>() -> <clinit>
com.kaolafm.base.utils.NetworkMonitor -> com.kaolafm.base.utils.NetworkMonitor:
    java.lang.ref.WeakReference mContextWeakReference -> mContextWeakReference
    int STATUS_NO_NETWORK -> STATUS_NO_NETWORK
    int STATUS_WIFI -> STATUS_WIFI
    int STATUS_MOBILE -> STATUS_MOBILE
    int mNetStatus -> mNetStatus
    java.util.ArrayList mListeners -> mListeners
    android.net.ConnectivityManager mConnectivityManager -> mConnectivityManager
    android.content.BroadcastReceiver mReceiver -> mReceiver
    com.kaolafm.base.utils.NetworkMonitor getInstance(android.content.Context) -> getInstance
    void <init>() -> <init>
    void registerNetworkStatusChangeListener(com.kaolafm.base.utils.NetworkMonitor$OnNetworkStatusChangedListener) -> registerNetworkStatusChangeListener
    void removeNetworkStatusChangeListener(com.kaolafm.base.utils.NetworkMonitor$OnNetworkStatusChangedListener) -> removeNetworkStatusChangeListener
    void init(android.content.Context) -> init
    boolean isNetStatusChanged() -> isNetStatusChanged
    int populateNetworkStatus() -> populateNetworkStatus
    void <init>(com.kaolafm.base.utils.NetworkMonitor$1) -> <init>
    int access$200(com.kaolafm.base.utils.NetworkMonitor) -> access$200
    boolean access$300(com.kaolafm.base.utils.NetworkMonitor) -> access$300
    java.util.ArrayList access$400(com.kaolafm.base.utils.NetworkMonitor) -> access$400
com.kaolafm.base.utils.NetworkMonitor$1 -> com.kaolafm.base.utils.NetworkMonitor$1:
    com.kaolafm.base.utils.NetworkMonitor this$0 -> this$0
    void <init>(com.kaolafm.base.utils.NetworkMonitor) -> <init>
    void onReceive(android.content.Context,android.content.Intent) -> onReceive
com.kaolafm.base.utils.NetworkMonitor$NETWORK_MONITOR_CLASS -> com.kaolafm.base.utils.NetworkMonitor$NETWORK_MONITOR_CLASS:
    com.kaolafm.base.utils.NetworkMonitor NETWORK_MONITOR_INSTANCE -> NETWORK_MONITOR_INSTANCE
    void <init>() -> <init>
    com.kaolafm.base.utils.NetworkMonitor access$100() -> access$100
    void <clinit>() -> <clinit>
com.kaolafm.base.utils.NetworkMonitor$OnNetworkStatusChangedListener -> com.kaolafm.base.utils.NetworkMonitor$OnNetworkStatusChangedListener:
    void onStatusChanged(int,int) -> onStatusChanged
com.kaolafm.base.utils.NetworkUtil -> com.kaolafm.base.utils.NetworkUtil:
    java.lang.String TAG -> TAG
    int NETWORK_TYPE_UNKNOW -> NETWORK_TYPE_UNKNOW
    int NETWORK_TYPE_NO_WORK -> NETWORK_TYPE_NO_WORK
    int NETWORK_TYPE_WIFI -> NETWORK_TYPE_WIFI
    int NETWORK_TYPE_2G -> NETWORK_TYPE_2G
    int NETWORK_TYPE_3G -> NETWORK_TYPE_3G
    int NETWORK_TYPE_4G -> NETWORK_TYPE_4G
    boolean bCheckedNetInfo -> bCheckedNetInfo
    void <init>() -> <init>
    java.lang.String getMacAddr(android.content.Context) -> getMacAddr
    boolean isWAPStatic(android.content.Context) -> isWAPStatic
    boolean isNetworkAvailable(android.content.Context) -> isNetworkAvailable
    boolean isNetworkRoaming(android.content.Context) -> isNetworkRoaming
    boolean isWifiNetworkAvailable(android.content.Context) -> isWifiNetworkAvailable
    boolean isNGNetworkAvailable(android.content.Context) -> isNGNetworkAvailable
    boolean is2GMobileNetwork(android.content.Context) -> is2GMobileNetwork
    boolean is4GMobileNetwork(android.content.Context) -> is4GMobileNetwork
    android.net.NetworkInfo$State getState(android.content.Context) -> getState
    java.lang.String getCurrentAvailableNetworkType(android.content.Context) -> getCurrentAvailableNetworkType
    int getNetworkIndex(android.content.Context) -> getNetworkIndex
    int getNetwork(android.content.Context) -> getNetwork
    java.lang.String getIPAddress(android.content.Context) -> getIPAddress
    void <clinit>() -> <clinit>
com.kaolafm.base.utils.SeCore -> com.kaolafm.base.utils.SeCore:
    void <init>() -> <init>
    java.lang.String encrypt(java.lang.String) -> encrypt
    java.lang.String decrypt(java.lang.String) -> decrypt
    void <clinit>() -> <clinit>
com.kaolafm.base.utils.SeCoreUtils -> com.kaolafm.base.utils.SeCoreUtils:
    com.kaolafm.base.utils.SeCoreUtils mInstance -> mInstance
    com.kaolafm.base.utils.SeCore mSeCore -> mSeCore
    android.content.SharedPreferences mSharedPreferences -> mSharedPreferences
    java.lang.String SP_KAOLA_ENCRYPT_NAME -> SP_KAOLA_ENCRYPT_NAME
    java.lang.String KAOLA_ENCRYPT -> KAOLA_ENCRYPT
    java.lang.String KEY_ACCESSTOKEN -> KEY_ACCESSTOKEN
    java.lang.String KEY_UDID -> KEY_UDID
    void <init>() -> <init>
    com.kaolafm.base.utils.SeCoreUtils getInstance() -> getInstance
    void SeCoreInit(android.app.Application) -> SeCoreInit
    java.lang.String callEncrypt(java.lang.String,java.lang.String) -> callEncrypt
    java.lang.String encrypt(java.lang.String) -> encrypt
    java.lang.String decrypt(java.lang.String) -> decrypt
    java.lang.String callDecrypt(java.lang.String,java.lang.String,com.kaolafm.base.utils.SeCoreUtils$SecureCallBack) -> callDecrypt
    void updateCrypt() -> updateCrypt
com.kaolafm.base.utils.SeCoreUtils$SecureCallBack -> com.kaolafm.base.utils.SeCoreUtils$SecureCallBack:
    void encrypt(java.lang.String) -> encrypt
com.kaolafm.base.utils.SpUtil -> com.kaolafm.base.utils.SpUtil:
    java.lang.String DEFAULT_NAME -> DEFAULT_NAME
    java.lang.String ENCRYPT_FLAG_SUFFIX -> ENCRYPT_FLAG_SUFFIX
    boolean isAsync -> isAsync
    java.util.Map mSharedPreferencesMap -> mSharedPreferencesMap
    void <init>() -> <init>
    void init(android.content.Context) -> init
    void init(android.content.Context,java.lang.String) -> init
    void init(java.lang.String,android.content.SharedPreferences) -> init
    android.content.SharedPreferences getDefaultSharedPreferences(android.content.Context,java.lang.String) -> getDefaultSharedPreferences
    void clear(java.lang.String) -> clear
    void clear() -> clear
    void remove(java.lang.String,java.lang.String) -> remove
    void removeEncrypted(java.lang.String,java.lang.String) -> removeEncrypted
    void remove(java.lang.String) -> remove
    void removeAnyByName(java.lang.String,java.lang.String[]) -> removeAnyByName
    void removeAnyEncryptedByName(java.lang.String,java.lang.String[]) -> removeAnyEncryptedByName
    void removeAny(java.lang.String[]) -> removeAny
    java.lang.String removeWith(java.lang.String,java.lang.String) -> removeWith
    java.lang.String removeWith(java.lang.String) -> removeWith
    void putBoolean(java.lang.String,java.lang.String,boolean) -> putBoolean
    void putBoolean(java.lang.String,boolean) -> putBoolean
    boolean getBoolean(java.lang.String,java.lang.String,boolean) -> getBoolean
    boolean getBoolean(java.lang.String,boolean) -> getBoolean
    void putInt(java.lang.String,java.lang.String,int) -> putInt
    void putInt(java.lang.String,int) -> putInt
    int getInt(java.lang.String,java.lang.String,int) -> getInt
    int getInt(java.lang.String,int) -> getInt
    void putFloat(java.lang.String,java.lang.String,float) -> putFloat
    void putFloat(java.lang.String,float) -> putFloat
    float getFloat(java.lang.String,java.lang.String,float) -> getFloat
    float getFloat(java.lang.String,float) -> getFloat
    void putLong(java.lang.String,java.lang.String,long) -> putLong
    void putLong(java.lang.String,long) -> putLong
    long getLong(java.lang.String,java.lang.String,long) -> getLong
    long getLong(java.lang.String,long) -> getLong
    void putString(java.lang.String,java.lang.String,java.lang.String) -> putString
    void putString(java.lang.String,java.lang.String) -> putString
    java.lang.String getString(java.lang.String,java.lang.String,java.lang.String) -> getString
    java.lang.String getString(java.lang.String,java.lang.String) -> getString
    void putEncryptedString(java.lang.String,java.lang.String,java.lang.String) -> putEncryptedString
    android.util.Pair getDecryptedString(java.lang.String,java.lang.String,java.lang.String) -> getDecryptedString
    android.content.SharedPreferences$Editor getEditor(java.lang.String) -> getEditor
    android.content.SharedPreferences getSharedPreferences(java.lang.String) -> getSharedPreferences
    void apply(android.content.SharedPreferences$Editor) -> apply
    void <clinit>() -> <clinit>
com.kaolafm.base.utils.StringUtil -> com.kaolafm.base.utils.StringUtil:
    android.text.InputFilter emojiFilter -> emojiFilter
    void <init>() -> <init>
    java.lang.String str2HexStr(java.lang.String) -> str2HexStr
    java.lang.String jsonFormat(java.lang.String) -> jsonFormat
    java.lang.String xmlFormat(java.lang.String) -> xmlFormat
    java.lang.String array2String(java.lang.String,java.lang.Object[]) -> array2String
    java.lang.String array2String(java.lang.Object[]) -> array2String
    java.lang.String collection2String(java.util.Collection,java.lang.String) -> collection2String
    java.lang.String iterator2String(java.util.Iterator,java.lang.String) -> iterator2String
    java.lang.String truncateUrlPage(java.lang.String) -> truncateUrlPage
    java.util.Map getRequestParams(java.lang.String) -> getRequestParams
    java.lang.String join(java.lang.Object[]) -> join
    java.lang.String formatNum(long) -> formatNum
    java.lang.String createSign(java.util.Map,java.lang.String) -> createSign
    java.lang.StringBuilder array2StringWithIndex(java.lang.Object[]) -> array2StringWithIndex
    java.lang.String toString(java.lang.Object) -> toString
    boolean isEmpty(java.lang.String) -> isEmpty
    java.lang.String makeSafe(java.lang.String) -> makeSafe
    boolean isDigitsOnly(java.lang.String) -> isDigitsOnly
    java.lang.String format(java.lang.String,java.lang.Object[]) -> format
    int lambda$createSign$0(java.util.Map$Entry,java.util.Map$Entry) -> lambda$createSign$0
    void <clinit>() -> <clinit>
com.kaolafm.base.utils.StringUtil$1 -> com.kaolafm.base.utils.StringUtil$1:
    java.util.regex.Pattern emoji -> emoji
    void <init>() -> <init>
    java.lang.CharSequence filter(java.lang.CharSequence,int,int,android.text.Spanned,int,int) -> filter
com.kaolafm.base.utils.UrlUtil -> com.kaolafm.base.utils.UrlUtil:
    java.lang.String PIC_100_100 -> PIC_100_100
    java.lang.String PIC_250_250 -> PIC_250_250
    java.lang.String PIC_340_340 -> PIC_340_340
    java.lang.String PIC_720_254 -> PIC_720_254
    java.lang.String PIC_550_550 -> PIC_550_550
    java.lang.String JPG_STR -> JPG_STR
    java.lang.String JPEG_STR -> JPEG_STR
    java.lang.String PNG_STR -> PNG_STR
    void <init>() -> <init>
    java.lang.String getCustomPicUrl(java.lang.String,java.lang.String) -> getCustomPicUrl
    void appendValue(java.lang.StringBuilder,java.lang.String,java.lang.String) -> appendValue
    java.lang.String madeUrlSign(java.lang.String[],java.lang.String) -> madeUrlSign
com.kaolafm.base.utils.Yeast -> com.kaolafm.base.utils.Yeast:
    char[] alphabet -> alphabet
    int length -> length
    int seed -> seed
    java.lang.String prev -> prev
    java.util.Map map -> map
    void <init>() -> <init>
    java.lang.String encode(long) -> encode
    long decode(java.lang.String) -> decode
    java.lang.String yeast() -> yeast
    void <clinit>() -> <clinit>
com.kaolafm.base.utils.ZipHelper -> com.kaolafm.base.utils.ZipHelper:
    void <init>() -> <init>
    java.lang.String decompressToStringForZlib(byte[]) -> decompressToStringForZlib
    java.lang.String decompressToStringForZlib(byte[],java.lang.String) -> decompressToStringForZlib
    byte[] decompressForZlib(byte[]) -> decompressForZlib
    byte[] compressForZlib(byte[]) -> compressForZlib
    byte[] compressForZlib(java.lang.String) -> compressForZlib
    byte[] compressForGzip(java.lang.String) -> compressForGzip
    java.lang.String decompressForGzip(byte[]) -> decompressForGzip
    java.lang.String decompressForGzip(byte[],java.lang.String) -> decompressForGzip
    void closeQuietly(java.io.Closeable) -> closeQuietly
