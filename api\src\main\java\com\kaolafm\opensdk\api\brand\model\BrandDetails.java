package com.kaolafm.opensdk.api.brand.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 品牌信息，包括品牌名称，logo, 服务条款
 * <AUTHOR>
 * @date 2019-03-18
 */
public class BrandDetails implements Parcelable {

    /**
     * userAgreement : http://m.kaolafm.com/location/protocol.html
     * logo : http://img.kaolafm.net/kradio/kradio-logo/kradio-activate-page-logo.png
     * brand : K-Radio智能车机系统
     */

    private String brand;

    private String logo;

    private String userAgreement;

    private String userAgreementUpdateTime;

    private String privacyPolicy;

    private String brandIntroduction;

    private String privacyPolicyUpdateTime;

    protected BrandDetails(Parcel in) {
        brand = in.readString();
        logo = in.readString();
        userAgreement = in.readString();
        userAgreementUpdateTime = in.readString();
        privacyPolicy = in.readString();
        brandIntroduction = in.readString();
        privacyPolicyUpdateTime = in.readString();
    }

    public static final Creator<BrandDetails> CREATOR = new Creator<BrandDetails>() {
        @Override
        public BrandDetails createFromParcel(Parcel in) {
            return new BrandDetails(in);
        }

        @Override
        public BrandDetails[] newArray(int size) {
            return new BrandDetails[size];
        }
    };

    public String getPrivacyPolicy() {
        return privacyPolicy;
    }

    public void setPrivacyPolicy(String privacyPolicy) {
        this.privacyPolicy = privacyPolicy;
    }

    public String getBrand() {
        return brand;
    }

    public String getLogo() {
        return logo;
    }

    public String getUserAgreement() {
        return userAgreement;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public void setUserAgreement(String userAgreement) {
        this.userAgreement = userAgreement;
    }

    public String getUserAgreementUpdateTime() {
        return userAgreementUpdateTime;
    }

    public void setUserAgreementUpdateTime(String userAgreementUpdateTime) {
        this.userAgreementUpdateTime = userAgreementUpdateTime;
    }

    public String getBrandIntroduction() {
        return brandIntroduction;
    }

    public void setBrandIntroduction(String brandIntroduction) {
        this.brandIntroduction = brandIntroduction;
    }

    public String getPrivacyPolicyUpdateTime() {
        return privacyPolicyUpdateTime;
    }

    public void setPrivacyPolicyUpdateTime(String privacyPolicyUpdateTime) {
        this.privacyPolicyUpdateTime = privacyPolicyUpdateTime;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(brand);
        dest.writeString(logo);
        dest.writeString(userAgreement);
        dest.writeString(userAgreementUpdateTime);
        dest.writeString(privacyPolicy);
        dest.writeString(brandIntroduction);
        dest.writeString(privacyPolicyUpdateTime);
    }
}
