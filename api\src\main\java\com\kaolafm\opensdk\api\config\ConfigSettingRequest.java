package com.kaolafm.opensdk.api.config;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.http.core.HttpCallback;

public class ConfigSettingRequest extends BaseRequest {

    private ConfigSettingService mConfigSettingService;

    public ConfigSettingRequest() {
        mConfigSettingService = obtainRetrofitService(ConfigSettingService.class);
    }

    /**
     * 获取config接口配置信息
     * @param callback
     */
    public void getConfigSwitchInfos(HttpCallback<ConfigSettingOption> callback) {
        doHttpDeal(mConfigSettingService.getConfigSwitchInfos(), BaseResult::getResult, callback);
    }

    /** 获得版本号等信息
     * @param callback
     */
    public void getConfigFilingInfos(HttpCallback<FlingBean> callback) {
        doHttpDeal(mConfigSettingService.getConfigFilingInfos(), BaseResult::getResult, callback);
    }
}
