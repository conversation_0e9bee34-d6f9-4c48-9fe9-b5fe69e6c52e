package com.kaolafm.opensdk.api.operation.model.category;


/**
 * 人工运营分类成员:资讯碎片。
 */
public class InfoFragmentCategoryMember extends CategoryMember {

    /** 专辑id*/
    private long albumId;

    /** 专辑的播放次数*/
    private int playTimes;

    /** 是否精品  1:是,0:否 */
    private int fine;

    /** 是否vip 1:是,0:否 */
    private int vip;

    public long getAlbumId() {
        return albumId;
    }

    public void setAlbumId(long albumId) {
        this.albumId = albumId;
    }

    public int getPlayTimes() {
        return playTimes;
    }

    public void setPlayTimes(int playTimes) {
        this.playTimes = playTimes;
    }

    public int getFine() {
        return fine;
    }

    public void setFine(int fine) {
        this.fine = fine;
    }

    public int getVip() {
        return vip;
    }

    public void setVip(int vip) {
        this.vip = vip;
    }

    @Override
    public String toString() {
        return "AlbumCategoryMember{" +
                "albumId=" + albumId +
                ", playTimes=" + playTimes +
                ", fine=" + fine +
                ", vip=" + vip +
                '}' + super.toString();
    }
}
