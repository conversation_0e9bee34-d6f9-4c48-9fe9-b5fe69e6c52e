<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\utils\src\main\jniLibs"><file name="arm64-v8a/libkaolafmse.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\utils\src\main\jniLibs\arm64-v8a\libkaolafmse.so"/><file name="armeabi/libkaolafmse.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\utils\src\main\jniLibs\armeabi\libkaolafmse.so"/><file name="armeabi-v7a/libkaolafmse.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\utils\src\main\jniLibs\armeabi-v7a\libkaolafmse.so"/><file name="x86/libkaolafmse.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\utils\src\main\jniLibs\x86\libkaolafmse.so"/><file name="x86_64/libkaolafmse.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\utils\src\main\jniLibs\x86_64\libkaolafmse.so"/></source></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\utils\src\release\jniLibs"/></dataSet></merger>