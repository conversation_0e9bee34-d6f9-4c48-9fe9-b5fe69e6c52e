package com.kaolafm.opensdk.api.music.qq.model;

import com.google.gson.annotations.SerializedName;

/**
 * 歌手信息
 * <AUTHOR>
 * @date 2018/4/23
 */

public class Singer {

    /**
     * country : 内地
     * singer_id : 5062
     * singer_mid : 002J4UUk29y8BY
     * singer_name : 薛之谦
     * singer_translator_name : Joker
     */

    @SerializedName("country")
    private String country;

    @SerializedName("singer_id")
    private int singerId;

    @SerializedName("singer_mid")
    private String singerMid;

    @SerializedName("singer_name")
    private String singerName;

    /**
     * singer_title : 王菲
     */

    @SerializedName("singer_title")
    private String singerTitle;

    @SerializedName("singer_translator_name")
    private String singerTranslatorName;

    public String getCountry() {
        return country;
    }

    public int getSingerId() {
        return singerId;
    }

    public String getSingerMid() {
        return singerMid;
    }

    public String getSingerName() {
        return singerName;
    }

    public String getSingerTitle() {
        return singerTitle;
    }

    public String getSingerTranslatorName() {
        return singerTranslatorName;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public void setSingerId(int singerId) {
        this.singerId = singerId;
    }

    public void setSingerMid(String singerMid) {
        this.singerMid = singerMid;
    }

    public void setSingerName(String singerName) {
        this.singerName = singerName;
    }

    public void setSingerTitle(String singerTitle) {
        this.singerTitle = singerTitle;
    }

    public void setSingerTranslatorName(String singerTranslatorName) {
        this.singerTranslatorName = singerTranslatorName;
    }
}
