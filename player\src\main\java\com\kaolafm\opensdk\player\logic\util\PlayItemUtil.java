package com.kaolafm.opensdk.player.logic.util;

import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.opensdk.api.broadcast.BroadcastDetails;
import com.kaolafm.opensdk.api.media.model.AudioFileInfo;
import com.kaolafm.opensdk.player.logic.model.TimeDiscontinuousBroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.LivePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.TVPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.VideoAlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class PlayItemUtil {
    /**
     * 判断两个PlayItem是否是相同的节目
     *
     * @param first
     * @param second
     * @return
     */
    public static boolean isSameProgram(PlayItem first, PlayItem second) {
        if (first instanceof TVPlayItem && second instanceof TVPlayItem) {
            //针对听电视进行判断
            //过滤都是听电视的情况
            //听电视节目的audioId取自接口的programId，永远是0，因此需要匹配listenTvId字段，该字段赋值给了PlayItem的albumId字段。
            return first.getRadioId() != null && second.getRadioId() != null && first.getRadioId().equals(second.getRadioId());
        } else if (first instanceof BroadcastPlayItem && second instanceof BroadcastPlayItem) {
            //针对广播进行判断
            //广播直播的audioId为0，不能只对audioId进行对比
            return first.getRadioId() != null && second.getRadioId() != null && first.getRadioId().equals(second.getRadioId()) && first.getAudioId() == second.getAudioId();
        } else if (first != null && second != null && first.getAudioId() == second.getAudioId()) {
            //如果两个playItem的audioId相同，也有可能是一个广播直播一个听电视，这种情况需要过滤
            if ((first instanceof BroadcastPlayItem && second instanceof TVPlayItem) || (first instanceof TVPlayItem && second instanceof BroadcastPlayItem)) {
                return false;
            }
            return true;
        }
        return false;
    }

    /**
     * 是否是相同直播类型且状态一致
     *
     * @param first
     * @param second
     * @return
     */
    public static boolean isSameLivingProgramAndStatus(PlayItem first, PlayItem second) {
        if (!(first instanceof LivePlayItem && second instanceof LivePlayItem)) {
            return false;
        }
        return first.getStatus() == second.getStatus() && ((LivePlayItem) first).getLiveId() == ((LivePlayItem) second).getLiveId() && first.getAudioId() == second.getAudioId();
    }


    /**
     * 当广播播单接口返回了空数据时，创建一个持续24小时的广播节目
     *
     * @return
     */
    public static TimeDiscontinuousBroadcastPlayItem createTimeDiscontinuousBroadcastPlayItem(BroadcastDetails broadcastDetails) {
        TimeDiscontinuousBroadcastPlayItem playItem = new TimeDiscontinuousBroadcastPlayItem();
        if (broadcastDetails == null) {
            return playItem;
        }
        long serverTime = DateUtil.getServerTime();
        playItem.setStatus(PlayerConstants.BROADCAST_STATUS_LIVING);
        playItem.setPlayInfoList(broadcastDetails.getPlayInfoList());
        playItem.setPlayUrl(broadcastDetails.getPlayUrl());
        playItem.getTimeInfoData().setCurSystemTime(serverTime);
        playItem.setClassifyId(broadcastDetails.getClassifyId());
        playItem.setBackPlayInfoList(new ArrayList());
        playItem.getInfoData().setDataSrc(PlayerConstants.RESOURCES_TYPE_BROADCAST);
        playItem.getInfoData().setAlbumId(broadcastDetails.getBroadcastId());
        playItem.setAudioId(0);
        playItem.getInfoData().setTitle("未知节目");
        playItem.getInfoData().setAlbumPic(broadcastDetails.getImg());
        playItem.getInfoData().setAlbumName(broadcastDetails.getName());
        playItem.getInfoData().setAudioDes("");
        playItem.setProgramEnable(broadcastDetails.getProgramEnable());

        long start = serverTime - (serverTime + 8 * 3600) % 86400;  //从00：00：00
        long end = start + 24 * 60 * 60 * 1000 - 1000;  //到23：59：59
        long duration = end - start;
        playItem.getTimeInfoData().setStartTime(start);
        playItem.getTimeInfoData().setFinishTime(end);
        playItem.getTimeInfoData().setBeginTime("00:00:00");
        playItem.getTimeInfoData().setEndTime("23：59：59");
        playItem.setDuration((int) duration);
        playItem.getInfoData().setIcon(broadcastDetails.getIcon());
        playItem.setFrequencyChannel(broadcastDetails.getFreq());
        playItem.setBroadcastSort(broadcastDetails.getType());
        playItem.setListenCount(broadcastDetails.getOnLineNum());
        if (broadcastDetails.getCurrentAreaInfo() != null) {
            playItem.getInfoData().setAreaCode(broadcastDetails.getCurrentAreaInfo().getAreaCode());
            playItem.getInfoData().setAreaName(broadcastDetails.getCurrentAreaInfo().getAreaName());
        }
        return playItem;
    }


    /**
     * 当广播播单接口返回了空数据时，创建一个持续24小时的广播节目
     *
     * @return
     */
    public static TimeDiscontinuousBroadcastPlayItem createTimeDiscontinuousBroadcastPlayItem(long startTime, long endTime, String playUrl, List<AudioFileInfo> audioFileInfoList, int classifyId, long radioId,
                                                                                              String radioPic, String radioName, String freq, int broadcastSort, long listenNum, String areaCode, String areaName,int programEnable) {
        TimeDiscontinuousBroadcastPlayItem playItem = new TimeDiscontinuousBroadcastPlayItem();
        PlayerLogUtil.log("createTimeDiscontinuousBroadcastPlayItem:startTime=" + startTime + " ,endTime=" + endTime);
        if (startTime > endTime) {
            PlayerLogUtil.log("createTimeDiscontinuousBroadcastPlayItem error:time check error:startTime=" + startTime + " ,endTime=" + endTime);
        }

        long serverTime = DateUtil.getServerTime();
        if (serverTime >= startTime && serverTime <= endTime) {
            playItem.setStatus(PlayerConstants.BROADCAST_STATUS_LIVING);
        } else if (startTime > serverTime) {
            playItem.setStatus(PlayerConstants.BROADCAST_STATUS_NOT_ON_AIR);
        } else {
            playItem.setStatus(PlayerConstants.BROADCAST_STATUS_PLAYBACK);
        }
        playItem.setPlayInfoList(audioFileInfoList);
        playItem.setPlayUrl(playUrl);
        playItem.getTimeInfoData().setCurSystemTime(serverTime);
        playItem.setClassifyId(classifyId);
        playItem.setBackPlayInfoList(new ArrayList());
        playItem.getInfoData().setDataSrc(PlayerConstants.RESOURCES_TYPE_BROADCAST);
        playItem.getInfoData().setAlbumId(radioId);
        playItem.setAudioId(0);
        playItem.getInfoData().setTitle("未知节目");
        playItem.getInfoData().setAlbumPic(radioPic);
        playItem.getInfoData().setAlbumName(radioName);
        playItem.getInfoData().setAudioDes("");
        playItem.setProgramEnable(programEnable);

        long duration = endTime - startTime;
        playItem.getTimeInfoData().setStartTime(startTime);
        playItem.getTimeInfoData().setFinishTime(endTime);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH:mm:ss", Locale.CHINA);
        playItem.getTimeInfoData().setBeginTime(simpleDateFormat.format(new Date(startTime)));
        playItem.getTimeInfoData().setEndTime(simpleDateFormat.format(new Date(endTime)));
        playItem.setDuration((int) duration);
        playItem.getInfoData().setIcon(radioPic);
        playItem.setFrequencyChannel(freq);
        playItem.setBroadcastSort(broadcastSort);
        playItem.setListenCount(listenNum);
        playItem.getInfoData().setAreaCode(areaCode);
        playItem.getInfoData().setAreaName(areaName);
        return playItem;
    }

    public static boolean isVideo(PlayItem playItem){
        return playItem instanceof VideoAlbumPlayItem;
    }
}
