package com.kaolafm.opensdk.permission;

import android.Manifest;
import android.util.Log;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;

import com.tbruyelle.rxpermissions2.RxPermissions;

public class PermissionUtil {
    private RxPermissions mRxPermissions;
    private PermissionUtil() {
    }

    public PermissionUtil(FragmentActivity activity) {
        mRxPermissions = new RxPermissions(activity);
    }

    public PermissionUtil(Fragment fragment) {
        mRxPermissions = new RxPermissions(fragment);
    }

    public void requestLocationPermissions() {
        String[] permissions = {Manifest.permission.ACCESS_FINE_LOCATION
                , Manifest.permission.ACCESS_COARSE_LOCATION};
        requestPermissions(null, permissions);
    }

    public void requestStoragePermissions() {
        String[] permissions = {Manifest.permission.WRITE_EXTERNAL_STORAGE};
        requestPermissions(null, permissions);
    }

    public void requestAllPermissions() {
        String[] permissions = {
                Manifest.permission.WRITE_EXTERNAL_STORAGE,
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION};
        requestPermissions(null, permissions);
    }

//    public void isSetLocation() {
//                ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.READ_PHONE_STATE,
//                Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.ACCESS_FINE_LOCATION
//                , Manifest.permission.ACCESS_COARSE_LOCATION}, 1100);
//    }

    //////////////////////////
    public void requestLocationPermissions(IPermissionListener iPermissionListener) {
        String[] permissions = {Manifest.permission.ACCESS_FINE_LOCATION
                , Manifest.permission.ACCESS_COARSE_LOCATION};
        requestPermissions(iPermissionListener, permissions);
    }

    public void requestStoragePermissions(IPermissionListener iPermissionListener) {
        String[] permissions = {Manifest.permission.WRITE_EXTERNAL_STORAGE};
        requestPermissions(iPermissionListener, permissions);
    }

    public void requestAllPermissions(IPermissionListener iPermissionListener) {
        String[] permissions = {
                Manifest.permission.WRITE_EXTERNAL_STORAGE,
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION};
        requestPermissions(iPermissionListener, permissions);
    }

    private void requestPermissions(IPermissionListener iPermissionListener, String... permissionArr) {
        mRxPermissions.requestEach(permissionArr)
                .subscribe(permission -> {
                    if (permission.granted) {
                        Log.d("permission", permission.name + " is granted");
                        if (iPermissionListener != null) {
                            iPermissionListener.onSuccess(permission.name);
                        }
                    } else if (permission.shouldShowRequestPermissionRationale) {
                        Log.d("permission", permission.name + " is denied,next time see you");
                        if (iPermissionListener != null) {
                            iPermissionListener.onFail(permission.name);
                        }
                    } else {
                        Log.d("permission", permission.name + " is denied");
                        if (iPermissionListener != null) {
                            iPermissionListener.onFail(permission.name);
                        }
                    }
                });
    }

}
