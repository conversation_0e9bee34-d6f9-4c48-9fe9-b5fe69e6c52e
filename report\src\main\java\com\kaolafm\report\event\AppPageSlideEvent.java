package com.kaolafm.report.event;

import com.kaolafm.report.util.ReportConstants;

/**
 * app内页面滑动
 * 蔡佳彬 2120
 */
public class AppPageSlideEvent extends BaseReportEventBean {
   private String pageid;//所在页面id
   private String direction;//滑动方向 上滑 up 下滑 down 作滑 left 右滑right
   private String resolution;//屏幕分辨率
   private String startposition;//屏幕滑动开始坐标 格式 x,y
   private String endposition;//屏幕滑动结束坐标 格式 x,y

    public AppPageSlideEvent() {
        setEventcode(ReportConstants.EVENT_PAGE_SLIDE);
    }

    public String getPageid() {
        return pageid;
    }

    public void setPageid(String pageid) {
        this.pageid = pageid;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public String getResolution() {
        return resolution;
    }

    public void setResolution(String resolution) {
        this.resolution = resolution;
    }

    public String getStartposition() {
        return startposition;
    }

    public void setStartposition(String startposition) {
        this.startposition = startposition;
    }

    public String getEndposition() {
        return endposition;
    }

    public void setEndposition(String endposition) {
        this.endposition = endposition;
    }
}
