package com.kaolafm.opensdk.api.media.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

public class VideoPiece implements Parcelable {
    @SerializedName("id")
    private long id;

    @SerializedName("title")
    private String title;

    @SerializedName("img")
    private String img;

    @SerializedName("playUrl")
    private String playUrl;

    @SerializedName("albumId")
    private long albumId;

    protected VideoPiece(Parcel in) {
        id = in.readLong();
        title = in.readString();
        img = in.readString();
        playUrl = in.readString();
        albumId = in.readLong();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(id);
        dest.writeString(title);
        dest.writeString(img);
        dest.writeString(playUrl);
        dest.writeLong(albumId);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<VideoPiece> CREATOR = new Creator<VideoPiece>() {
        @Override
        public VideoPiece createFromParcel(Parcel in) {
            return new VideoPiece(in);
        }

        @Override
        public VideoPiece[] newArray(int size) {
            return new VideoPiece[size];
        }
    };

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getPlayUrl() {
        return playUrl;
    }

    public void setPlayUrl(String playUrl) {
        this.playUrl = playUrl;
    }

    public long getAlbumId() {
        return albumId;
    }

    public void setAlbumId(long albumId) {
        this.albumId = albumId;
    }

    @Override
    public String toString() {
        return "VideoPiece{" +
                "id='" + id + '\'' +
                ", title='" + title + '\'' +
                ", img='" + img + '\'' +
                ", playUrl='" + playUrl + '\'' +
                ", albumId='" + albumId + '\'' +
                '}';
    }
}
