package com.kaolafm.opensdk.http.error;

import io.reactivex.Observable;
import io.reactivex.ObservableSource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/2/27
 */
public class ObservableErrorFunc<E> extends BaseErrorFunc<ObservableSource<? extends E>> {

    public ObservableErrorFunc(List<ResponseErrorListener> errorListenerList) {
        super(errorListenerList);
    }

    @Override
    ObservableSource<? extends E> getError(ApiException e) {
        return Observable.error(e);
    }
}
