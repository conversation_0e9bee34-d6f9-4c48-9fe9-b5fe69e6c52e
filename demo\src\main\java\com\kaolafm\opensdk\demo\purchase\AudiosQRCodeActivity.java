package com.kaolafm.opensdk.demo.purchase;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.kaolafm.opensdk.api.purchase.PurchaseRequest;
import com.kaolafm.opensdk.api.purchase.model.QRCodeInfo;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 通用
 *
 */
public class AudiosQRCodeActivity extends BaseActivity {

    @BindView(R.id.et_album_id)
    EditText albumIdEt;

    @BindView(R.id.et_tv_audios_ids)
    EditText audiosIdsEt;

    @BindView(R.id.et_audios_money)
    EditText audiosMoneyEt;

    @BindView(R.id.info_view)
    TextView infoViewTv;

    @BindView(R.id.iv_audios_qr_code)
    ImageView audiosQrCodeImg;

    @BindView(R.id.btn_qr_code_check)
    Button qrCodeCheck;

    private String mQrCodeId;

    @Override
    public int getLayoutId() {
        return R.layout.activity_audios_qr_code;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        setTitle("单曲二维码");
    }

    @Override
    public void initData() {

    }

    @OnClick({R.id.btn_audios_qr_code_get,R.id.btn_qr_code_check})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.btn_audios_qr_code_get:
                getAudiosQrCode();
                break;
            case R.id.btn_qr_code_check:
                skipToCheck();
                break;
        }
    }

    private void getAudiosQrCode() {
        String albumId = albumIdEt.getText().toString().trim();
        String audioIds = audiosIdsEt.getText().toString().trim();
        String money = audiosMoneyEt.getText().toString().trim();
        if(TextUtils.isEmpty(albumId)){
            showToast("单曲所属专辑id不能为空");
            return;
        }
        if(TextUtils.isEmpty(audioIds)){
            showToast("单曲id不能为空");
            return;
        }
        if(TextUtils.isEmpty(money)){
            showToast("金额不能为空");
            return;
        }
        try {
            Long albumIdL = Long.valueOf(albumId);
            Long moneyL = Long.valueOf(money);
            new PurchaseRequest().getAudioQRCode(audioIds, albumIdL, moneyL,
                    new HttpCallback<QRCodeInfo>() {
                        @Override
                        public void onSuccess(QRCodeInfo qrCodeInfo) {
                            mQrCodeId = qrCodeInfo.getQrCodeId();
                            infoViewTv.setText(qrCodeInfo.toString());
                            Glide.with(AudiosQRCodeActivity.this)
                                    .load(qrCodeInfo.getQrCodeImg())
                                    .into(audiosQrCodeImg);
                            qrCodeCheck.setVisibility(View.VISIBLE);
                        }

                        @Override
                        public void onError(ApiException exception) {
                            showError("获取失败", exception);
                        }
                    });
        } catch (NumberFormatException e){
            showToast("输入有误，请检查");
        }

    }

    private void skipToCheck(){
        Intent intent = new Intent(AudiosQRCodeActivity.this, QRCodeStatusActivity.class);
        intent.putExtra(QRCodeStatusActivity.KEY_QRCODE_ID, mQrCodeId);
        startActivity(intent);
    }
}
