package com.kaolafm.opensdk.player.logic.listener;

import com.kaolafm.opensdk.player.logic.model.item.InvalidPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.List;

/**
 * <AUTHOR> on 2019/3/28.
 */

public interface IPlayListGetListener {

    void onDataGet(PlayItem playItem, List<PlayItem> playItemList);

    void onDataGetError(PlayItem playItem, int errorCode, int errorExtra);
}
