package com.kaolafm.ad.api.model;

import android.os.Parcel;

/**
 * 广告基类，带有交互广告。只有交互广告不需要继承该类
 * <AUTHOR>
 * @date 2020-01-17
 */
public class BaseAdvert extends Advert {

    /**
     * 交互广告
     */
    private InteractionAdvert interactionAdvert;

    public BaseAdvert() {
    }

    public InteractionAdvert getInteractionAdvert() {
        return interactionAdvert;
    }

    public void setInteractionAdvert(InteractionAdvert interactionAdvert) {
        this.interactionAdvert = interactionAdvert;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        super.writeToParcel(dest, flags);
        dest.writeParcelable(this.interactionAdvert, flags);
    }

    protected BaseAdvert(Parcel in) {
        super(in);
        this.interactionAdvert = in.readParcelable(InteractionAdvert.class.getClassLoader());
    }

    public static final Creator<BaseAdvert> CREATOR = new Creator<BaseAdvert>() {
        @Override
        public BaseAdvert createFromParcel(Parcel source) {
            return new BaseAdvert(source);
        }

        @Override
        public BaseAdvert[] newArray(int size) {
            return new BaseAdvert[size];
        }
    };
}
