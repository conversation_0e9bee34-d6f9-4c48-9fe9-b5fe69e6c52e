apply plugin: 'groovy'

dependencies {
    implementation gradleApi()
    implementation localGroovy()
    implementation 'com.android.tools.build:gradle:3.6.4'
    implementation 'com.jfrog.bintray.gradle:gradle-bintray-plugin:1.8.5' //用于上传Maven生成的文件到Bintray
    implementation 'com.google.auto.service:auto-service-annotations:1.0.1'
    annotationProcessor 'com.google.auto.service:auto-service:1.0.1'
    implementation 'org.greenrobot:greendao-gradle-plugin:3.3.0'
}
repositories {
    google()
    maven { url 'https://maven.aliyun.com/repository/public' }
    maven{url 'https://nexus.kaolafm.com/nexus/content/groups/android-dev/'}
}