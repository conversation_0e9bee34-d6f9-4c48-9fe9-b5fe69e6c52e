# 广播回听逻辑详解

## 概述

本文档详细梳理了考拉FM SDK中广播回听功能的完整逻辑，包括调用链、状态判断、地址获取等核心机制。广播回听是指用户可以收听已结束的广播节目的功能。

## 1. 核心概念

### 1.1 广播节目状态

广播节目有以下几种状态（定义在 `PlayerConstants` 中）：

```java
public static final int BROADCAST_STATUS_DEFAULT = 0;      // 默认状态
public static final int BROADCAST_STATUS_LIVING = 1;       // 直播中
public static final int BROADCAST_STATUS_PLAYBACK = 2;     // 可回放（回听）
public static final int BROADCAST_STATUS_NOT_ON_AIR = 3;   // 未开播
```

### 1.2 关键数据结构

#### BroadcastPlayItem（广播播放项）
- `status`: 节目状态
- `playInfoList`: 直播分音质播放地址列表
- `backPlayInfoList`: 回听分音质播放地址列表
- `timeInfoData`: 时间信息（开始时间、结束时间等）
- `programEnable`: 回放状态（0-隐藏，1-显示可播，2-显示不可播）

#### TimeInfoData（时间信息）
- `startTime`: 节目开始时间
- `finishTime`: 节目结束时间
- `curSystemTime`: 服务器当前时间

## 2. 回听逻辑核心流程

### 2.1 播放请求入口

当用户请求播放广播节目时，调用链如下：

```
用户播放请求 
    ↓
BroadcastPlayControl.start()
    ↓
BroadcastPlayControl.requestPlayUrl()
    ↓
[状态判断] 直播 or 回听？
```

### 2.2 状态判断逻辑

在 `BroadcastPlayControl.requestPlayUrl()` 方法中：

```java
void requestPlayUrl(PlayItem playItem, OnGetPlayUrlData callback) {
    BroadcastPlayItem broadcastPlayItem = (BroadcastPlayItem) playItem;
    
    // 1. 打印当前节目状态
    String statusText = getStatusText(broadcastPlayItem.getStatus());
    PlayerLogUtil.log(TAG, "requestPlayUrl", "播放节目状态: " + statusText);
    
    // 2. 根据状态选择播放地址
    if (broadcastPlayItem.isLiving()) {
        // 直播状态：使用直播地址
        setPlayUrl(playItem, broadcastPlayItem.getPlayInfoList());
        callback.onDataGet(playItem.getPlayUrl());
    } else {
        // 回听状态：检查回听地址
        if (broadcastPlayItem.getBackPlayInfoList() == null || 
            broadcastPlayItem.getBackPlayInfoList().isEmpty()) {
            // 没有回听地址，需要从服务器获取
            requestPlaybackUrlFromServer(playItem, callback);
        } else {
            // 有回听地址，直接使用
            setPlayUrl(playItem, broadcastPlayItem.getBackPlayInfoList());
            callback.onDataGet(playItem.getPlayUrl());
        }
    }
}
```

### 2.3 状态判断方法

`isLiving()` 方法的判断逻辑：

```java
public boolean isLiving() {
    return status == PlayerConstants.BROADCAST_STATUS_LIVING;
}
```

**关键点**：只有当状态为 `BROADCAST_STATUS_LIVING` 时才认为是直播，其他状态都走回听逻辑。

## 3. 回听地址获取机制

### 3.1 本地缓存检查

首先检查 `BroadcastPlayItem` 中是否已有回听地址：

```java
if (broadcastPlayItem.getBackPlayInfoList() == null || 
    broadcastPlayItem.getBackPlayInfoList().isEmpty()) {
    // 需要从服务器获取
} else {
    // 使用本地缓存的回听地址
}
```

### 3.2 服务器获取回听地址

当本地没有回听地址时，调用 `requestPlaybackUrlFromServer()` 方法：

```java
private void requestPlaybackUrlFromServer(PlayItem playItem, OnGetPlayUrlData callback) {
    // 1. 获取播放列表控制器
    PlayerManager playerManager = PlayerManager.getInstance();
    BroadcastPlayListControl playListControl = 
        (BroadcastPlayListControl) playerManager.getPlayListControl();
    
    // 2. 刷新播放项URL
    playListControl.refreshPlayItemUrl(playItem, new IPlayListGetListener() {
        @Override
        public void onDataGet(PlayItem updatedPlayItem, List<PlayItem> playItemArrayList) {
            String newUrl = updatedPlayItem.getPlayUrl();
            
            // 3. 验证回听地址是否有效
            if (StringUtil.isEmpty(newUrl) || 
                updatedPlayItem.getStatus() != PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
                // 回听还未生成或状态不正确
                callback.onDataGet(null);
            } else {
                // 回听地址有效
                callback.onDataGet(newUrl);
            }
        }
    });
}
```

### 3.3 服务器数据处理

`BroadcastPlayListControl.refreshPlayItemUrl()` 最终调用 `initLiving()` 方法：

```java
// 在 initLiving() 方法中
int programStatus = programDetails.getStatus();
if (programStatus == PlayerConstants.BROADCAST_STATUS_LIVING ||
    programStatus == PlayerConstants.BROADCAST_STATUS_DEFAULT) {
    // 直播节目
    broadcastPlayItem.setStatus(PlayerConstants.BROADCAST_STATUS_LIVING);
    broadcastPlayItem.setPlayUrl(programDetails.getPlayUrl());
} else if (programStatus == PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
    // 回放节目
    broadcastPlayItem.setStatus(PlayerConstants.BROADCAST_STATUS_PLAYBACK);
    String backUrl = programDetails.getBackLiveUrl();
    broadcastPlayItem.setPlayUrl(backUrl);
}
```

**关键点**：
- 服务器返回 `programDetails.getPlayUrl()` 用于直播
- 服务器返回 `programDetails.getBackLiveUrl()` 用于回听
- 根据服务器返回的状态设置本地状态

## 4. 状态自动转换机制

### 4.1 时间监控

在直播过程中，`BroadcastPlayControl` 会启动定时器监控节目时间：

```java
private void onTimer() {
    progressTime = progressTime + 1000;
    
    // 检查是否超过节目结束时间
    if (progressTime > livingTotalTime) {
        // 自动转换为回听状态
        ((BroadcastPlayItem) mPlayItem).setStatus(PlayerConstants.BROADCAST_STATUS_PLAYBACK);
        
        // 清除过时的直播URL
        String oldUrl = mPlayItem.getPlayUrl();
        mPlayItem.setPlayUrl(null);
        
        // 标记需要自动播放下一个节目
        mPlayItem.addMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_AUTO_PLAY_NEXT_ITEM, "1");
        
        // 通知播放结束
        notifyPlayEnd();
        stopTimer();
    }
}
```

### 4.2 播放列表中的状态检查

在 `BroadcastPlayListControl.getPlayItem()` 中也会检查时间：

```java
if (playItem.getTimeInfoData().getFinishTime() < DateUtil.getServerTime()) {
    playItem.setStatus(PlayerConstants.BROADCAST_STATUS_PLAYBACK);
    
    // 检查是否有有效的回听地址
    if (StringUtil.isEmpty(playItem.getPlayUrl()) ||
        (playItem.getBackPlayInfoList() == null || playItem.getBackPlayInfoList().isEmpty())) {
        // 强制重新获取
        playItem.setPlayUrl(null);
    }
}
```

## 5. 回听地址验证逻辑

### 5.1 验证条件

在 `requestPlaybackUrlFromServer()` 的回调中，验证回听地址的条件：

```java
if (StringUtil.isEmpty(newUrl) || 
    updatedPlayItem.getStatus() != PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
    // 无效：地址为空 或 状态不是回听状态
    callback.onDataGet(null);
} else {
    // 有效：有地址且状态为回听状态
    callback.onDataGet(newUrl);
}
```

### 5.2 验证逻辑说明

**为什么不用URL格式判断？**
- M3U8格式既可用于直播也可用于回听
- 直播的M3U8：实时更新的播放列表
- 回听的M3U8：静态的完整播放列表
- 因此应该依赖服务器返回的状态，而不是URL格式

## 6. 异常处理机制

### 6.1 回听地址获取失败

```java
@Override
public void onDataGetError(PlayItem playItem, int errorCode, int errorExtra) {
    PlayerLogUtil.log(TAG, "requestPlaybackUrlFromServer", "error, code=" + errorCode);
    // 获取失败时，返回空地址
    callback.onDataGet(null);
}
```

### 6.2 回听未生成

当服务器返回的状态不是回听状态时，说明回听内容可能还在生成中：

```java
PlayerLogUtil.log(TAG, "requestPlaybackUrlFromServer", 
    "回听正在生成中，请稍后再试. status=" + updatedPlayItem.getStatus());
```

## 7. 关键时间节点

### 7.1 节目时间计算

```java
// 在 start() 方法中计算时间
TimeInfoData timeInfoData = playItemTemp.getTimeInfoData();
curServiceTime = timeInfoData.getCurSystemTime();    // 服务器当前时间
startTime = timeInfoData.getStartTime();             // 节目开始时间
endTime = timeInfoData.getFinishTime();              // 节目结束时间
livingTotalTime = endTime - startTime;               // 节目总时长
progressTime = curServiceTime - startTime;           // 已播放时长
```

### 7.2 状态转换时机

- **直播 → 回听**：当 `progressTime > livingTotalTime` 时自动转换
- **服务器同步**：调用 `initLiving()` 时根据服务器状态同步
- **播放列表检查**：获取播放项时检查 `finishTime < serverTime`

## 8. 调用链总结

```
播放请求
    ↓
BroadcastPlayControl.start()
    ↓
BroadcastPlayControl.requestPlayUrl()
    ↓
[判断] isLiving()?
    ↓                           ↓
[直播分支]                    [回听分支]
使用playInfoList              检查backPlayInfoList
    ↓                           ↓
直接播放                    [判断] 是否为空?
                               ↓                    ↓
                          [有缓存]              [无缓存]
                          直接使用              requestPlaybackUrlFromServer()
                               ↓                    ↓
                          播放回听              BroadcastPlayListControl.refreshPlayItemUrl()
                                                   ↓
                                              BroadcastPlayListControl.initLiving()
                                                   ↓
                                              服务器获取最新状态和地址
                                                   ↓
                                              验证状态和地址有效性
                                                   ↓
                                              返回结果给播放控制器
```

## 9. 重要注意事项

1. **状态优先级**：始终以服务器返回的状态为准
2. **地址缓存**：本地会缓存回听地址，避免重复请求
3. **时间同步**：依赖服务器时间进行状态判断
4. **自动转换**：直播结束后自动转为回听状态
5. **错误处理**：回听地址获取失败时会返回null，上层需要处理

这套机制确保了广播回听功能的稳定性和用户体验的连续性。

## 10. 深层机制解析

### 10.1 播放地址设置机制

`setPlayUrl()` 方法会根据音质列表选择最佳播放地址：

```java
// 在 BasePlayControl 中
protected void setPlayUrl(PlayItem playItem, List<AudioFileInfo> audioFileInfos) {
    if (audioFileInfos != null && !audioFileInfos.isEmpty()) {
        // 根据音质偏好选择播放地址
        AudioFileInfo selectedInfo = selectBestQuality(audioFileInfos);
        playItem.setPlayUrl(selectedInfo.getPlayUrl());
    }
}
```

### 10.2 音质选择策略

系统会根据以下优先级选择音质：
1. 用户设置的音质偏好
2. 网络状况自适应
3. 可用音质列表中的最佳选择

### 10.3 缓存策略

#### 地址缓存
- `playInfoList`: 直播地址列表（实时更新）
- `backPlayInfoList`: 回听地址列表（相对稳定）

#### 缓存更新时机
- 直播地址：每次播放时可能更新
- 回听地址：首次获取后缓存，除非强制刷新

### 10.4 状态同步机制

#### 本地状态 vs 服务器状态
```java
// 本地状态判断
boolean localIsLiving = playItem.getStatus() == BROADCAST_STATUS_LIVING;

// 服务器状态同步
int serverStatus = programDetails.getStatus();
playItem.setStatus(serverStatus);
```

#### 状态不一致处理
当本地状态与服务器状态不一致时：
1. 以服务器状态为准
2. 更新本地状态
3. 清除不匹配的播放地址
4. 重新获取正确的播放地址

### 10.5 时间窗口机制

#### 直播时间窗口
```java
// 节目是否在直播时间内
boolean inLiveWindow = (serverTime >= startTime) && (serverTime <= finishTime);

// 节目是否已结束（可回听）
boolean canPlayback = serverTime > finishTime;
```

#### 预播和延播处理
- **预播**：节目开始前的准备状态
- **延播**：节目结束后的缓冲时间
- **回听生成**：节目结束后回听内容的生成时间

### 10.6 错误恢复机制

#### 地址失效处理
```java
// 当播放地址失效时
if (playError && isPlaybackMode) {
    // 清除缓存的地址
    playItem.setPlayUrl(null);
    playItem.setBackPlayInfoList(null);

    // 重新从服务器获取
    requestPlaybackUrlFromServer(playItem, callback);
}
```

#### 网络异常恢复
- 自动重试机制
- 降级播放策略
- 用户手动刷新

### 10.7 播放连续性保障

#### 直播到回听的无缝切换
```java
// 在 onTimer() 中
if (progressTime > livingTotalTime) {
    // 1. 更新状态
    setStatus(BROADCAST_STATUS_PLAYBACK);

    // 2. 清除直播地址
    setPlayUrl(null);

    // 3. 标记自动播放下一节目
    addMapCacheData(ITEM_KEY_BROADCAST_AUTO_PLAY_NEXT_ITEM, "1");

    // 4. 通知播放结束（触发下一节目播放）
    notifyPlayEnd();
}
```

#### 节目间切换逻辑
- **上一节目**：检查是否有回听地址
- **下一节目**：检查是否在播出时间内
- **自动切换**：直播结束后自动播放下一节目

### 10.8 数据一致性保障

#### 状态与地址的一致性
```java
// 确保状态与地址匹配
if (status == BROADCAST_STATUS_LIVING) {
    // 必须有直播地址
    assert playInfoList != null && !playInfoList.isEmpty();
} else if (status == BROADCAST_STATUS_PLAYBACK) {
    // 必须有回听地址或能够获取回听地址
    assert backPlayInfoList != null || canFetchFromServer;
}
```

#### 时间信息同步
```java
// 定期同步服务器时间
timeInfoData.setCurSystemTime(DateUtil.getServerTime());

// 基于服务器时间判断状态
boolean shouldBePlayback = timeInfoData.getFinishTime() < timeInfoData.getCurSystemTime();
```

### 10.9 性能优化策略

#### 减少服务器请求
- 本地缓存有效时直接使用
- 批量获取多个节目信息
- 预加载下一节目的回听地址

#### 内存管理
- 及时清理过期的播放地址
- 限制缓存的节目数量
- 使用弱引用避免内存泄漏

### 10.10 调试和监控

#### 关键日志点
```java
// 状态变化日志
PlayerLogUtil.log(TAG, "status_change",
    "from " + oldStatus + " to " + newStatus + ", audioId=" + audioId);

// 地址获取日志
PlayerLogUtil.log(TAG, "url_fetch",
    "type=" + (isLiving ? "live" : "playback") + ", url=" + url);

// 时间检查日志
PlayerLogUtil.log(TAG, "time_check",
    "serverTime=" + serverTime + ", startTime=" + startTime + ", endTime=" + endTime);
```

#### 异常监控
- 地址获取失败率
- 状态转换异常
- 播放连续性中断

这些深层机制确保了广播回听功能在各种复杂场景下的稳定运行，为用户提供流畅的收听体验。

## 11. 实际应用场景分析

### 11.1 用户收听直播节目

**场景**：用户在节目直播过程中开始收听

**流程**：
1. 用户点击播放 → `BroadcastPlayControl.start()`
2. 检查节目状态 → `isLiving()` 返回 `true`
3. 使用直播地址 → `setPlayUrl(playItem, playInfoList)`
4. 开始播放并启动定时器监控

**关键代码路径**：
```
start() → requestPlayUrl() → isLiving()=true → 使用playInfoList → 播放
```

### 11.2 用户收听已结束节目（回听）

**场景**：用户想收听昨天的新闻节目

**流程**：
1. 用户点击播放 → `BroadcastPlayControl.start()`
2. 检查节目状态 → `isLiving()` 返回 `false`
3. 检查本地回听地址 → `getBackPlayInfoList()` 为空
4. 从服务器获取 → `requestPlaybackUrlFromServer()`
5. 服务器返回回听地址 → 验证状态为 `PLAYBACK`
6. 开始播放回听内容

**关键代码路径**：
```
start() → requestPlayUrl() → isLiving()=false → backPlayInfoList为空
→ requestPlaybackUrlFromServer() → refreshPlayItemUrl() → initLiving()
→ 服务器返回PLAYBACK状态和回听地址 → 播放
```

### 11.3 直播过程中自动转为回听

**场景**：用户正在收听直播，节目结束后自动可以回听

**流程**：
1. 用户正在收听直播
2. 定时器检测到 `progressTime > livingTotalTime`
3. 自动更新状态为 `BROADCAST_STATUS_PLAYBACK`
4. 清除直播地址，标记自动播放下一节目
5. 如果用户继续播放，会自动获取回听地址

**关键代码路径**：
```
onTimer() → progressTime > livingTotalTime → setStatus(PLAYBACK)
→ setPlayUrl(null) → notifyPlayEnd() → 自动播放下一节目
```

### 11.4 回听地址获取失败的处理

**场景**：服务器回听内容还在生成中

**流程**：
1. 用户请求播放回听内容
2. 服务器返回直播地址或状态不是 `PLAYBACK`
3. 验证失败，返回 `null`
4. 上层播放器显示"回听正在生成中"提示

**关键代码路径**：
```
requestPlaybackUrlFromServer() → 服务器返回非PLAYBACK状态
→ 验证失败 → callback.onDataGet(null) → 播放失败处理
```

### 11.5 网络异常后的恢复

**场景**：播放过程中网络中断，恢复后继续播放

**流程**：
1. 网络中断，播放失败
2. 网络恢复，用户重新播放
3. 检查本地缓存的地址是否仍有效
4. 如果无效，重新从服务器获取
5. 恢复播放

## 12. 常见问题和解决方案

### 12.1 问题：回听地址包含m3u8被误判为直播地址

**原因**：早期代码用 `url.contains("m3u8")` 判断是否为直播地址

**解决方案**：改为检查节目状态 `status != BROADCAST_STATUS_PLAYBACK`

**修复代码**：
```java
// 修改前
if (StringUtil.isEmpty(newUrl) || newUrl.contains("m3u8")) {

// 修改后
if (StringUtil.isEmpty(newUrl) || updatedPlayItem.getStatus() != PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
```

### 12.2 问题：直播结束后播放地址失效

**原因**：直播地址在节目结束后会失效，但本地状态未及时更新

**解决方案**：在状态转换时清除过时的播放地址

**关键代码**：
```java
if (progressTime > livingTotalTime) {
    setStatus(BROADCAST_STATUS_PLAYBACK);
    setPlayUrl(null);  // 清除过时的直播地址
}
```

### 12.3 问题：回听内容播放失败

**可能原因**：
1. 回听内容还在生成中
2. 版权限制无法回听
3. 服务器地址失效

**解决方案**：
1. 检查 `programEnable` 字段
2. 提供用户友好的错误提示
3. 实现重试机制

### 12.4 问题：状态不一致导致播放异常

**原因**：本地状态与服务器状态不同步

**解决方案**：
1. 定期同步服务器状态
2. 播放前强制检查状态
3. 异常时清除本地缓存

## 13. 最佳实践建议

### 13.1 对于开发者

1. **状态检查**：播放前始终检查最新状态
2. **错误处理**：妥善处理各种异常情况
3. **用户体验**：提供清晰的状态提示
4. **性能优化**：合理使用缓存，避免频繁请求

### 13.2 对于测试

1. **时间边界测试**：测试节目开始/结束时的状态转换
2. **网络异常测试**：模拟网络中断和恢复
3. **并发测试**：多个节目同时播放的情况
4. **长时间测试**：验证定时器和状态转换的稳定性

### 13.3 对于运维

1. **监控指标**：回听地址获取成功率、播放连续性
2. **日志分析**：关注状态转换和地址获取的日志
3. **性能监控**：服务器响应时间、缓存命中率

## 14. 总结

广播回听功能是一个复杂的系统，涉及状态管理、时间同步、地址获取、缓存策略等多个方面。核心设计原则是：

1. **以服务器状态为准**：本地状态要与服务器保持同步
2. **分离直播和回听逻辑**：不同状态使用不同的地址和处理流程
3. **优雅的状态转换**：直播结束后自动转为回听状态
4. **健壮的错误处理**：各种异常情况都有相应的处理机制
5. **良好的用户体验**：状态转换对用户透明，播放连续性好

通过理解这些机制，开发者可以更好地维护和扩展广播回听功能，为用户提供稳定可靠的收听体验。
