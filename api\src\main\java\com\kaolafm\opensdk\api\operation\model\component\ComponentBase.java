package com.kaolafm.opensdk.api.operation.model.component;

import com.kaolafm.opensdk.api.operation.model.ImageFile;

import java.util.Map;

/**
 *组件成员对象父类
 */
public class ComponentBase {
    /**
     * 成员编码
     */
    private String code;
    /**
     * 资源id
     */
    private long id;
    /**
     * 成员标题
     */
    private String title;
    /**
     * 副标题
     */
    private String subtitle;
    /**
     * 描述
     */
    private String description;
    /**
     * 角标
     */
    private int cornerMark;
    /**
     * 成员类型
     */
    private String type;
    /**
     * 图片信息集合
     */
    private Map<String, ImageFile> imageFiles;
    /**
     * 拓展信息
     */
    private Map<String, String> extInfo;
    /**
     * 播放次数
     */
    private String playTimes;
    /**
     * 是否可以播放
     */
    private int canplay;
    /**
     * 推荐原因
     */
    private String recommendReason;
    /**
     * 序号
     */
    private int order;
}
