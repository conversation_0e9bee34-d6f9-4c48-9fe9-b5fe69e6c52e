package com.kaolafm.opensdk.http.core;

import android.text.TextUtils;

import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.opensdk.di.scope.AppScope;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import javax.inject.Inject;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 提前处理网络请求和返回结果
 *
 * <AUTHOR>
 * @date 2018/4/18
 */
@AppScope
public class HttpHandler implements Interceptor {

    private List<HttpBeforeHandler> mHttpBeforeHandlers;

    @Inject
    public HttpHandler(Set<HttpBeforeHandler> beforeHandlers) {
        mHttpBeforeHandlers = new ArrayList<>(beforeHandlers);
        //根据优先级排序
        Collections.sort(mHttpBeforeHandlers, (o1, o2) -> o2.priority() - o1.priority());
    }

    /**
     * 在真正请求网络前会先回调该方法。
     */
    public Request onHttpRequestBefore(Chain chain, Request request) {
        Request newRequest = request;
        if (!ListUtil.isEmpty(mHttpBeforeHandlers)) {
            for (HttpBeforeHandler handler : mHttpBeforeHandlers) {
                if (handler != null) {
                    newRequest = handler.onHttpRequestBefore(chain, newRequest);
                }
            }
        }
        return newRequest;
    }

    /**
     * 拿到返回结果，在执行任何处理前会先回调该方法。
     */
    public Response onHttpResultResponse(Chain chain, Response response) {
        String date = response.header("Date");
        if (!TextUtils.isEmpty(date)) {
            DateUtil.setServerTime(date);
        }
        return response;
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        Response response = chain.proceed(onHttpRequestBefore(chain, chain.request()));
        return onHttpResultResponse(chain, response);
    }
}
