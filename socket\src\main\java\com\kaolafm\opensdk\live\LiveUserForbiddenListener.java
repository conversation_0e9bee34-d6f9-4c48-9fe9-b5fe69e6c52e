package com.kaolafm.opensdk.live;

import android.util.Log;

import com.kaolafm.opensdk.api.live.model.LiveStatusCode;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.socket.SocketEvent;
import com.kaolafm.opensdk.socket.SocketListener;

import java.util.Map;

/**
 * 直播 - 用户被禁言，被踢出直播间 - 客户端监听事件
 */
public class LiveUserForbiddenListener implements SocketListener<LiveStatusCode> {

    private static final String TAG = "LiveUserForbidden";

    private LiveUserForbiddenListener() {
    }

    public static LiveUserForbiddenListener INSTANCE = new LiveUserForbiddenListener();

    @Override
    public String getEvent() {
        return SocketEvent.LIVE_USER_FORBIDDEN;
    }

    @Override
    public Map<String, Object> getParams(Map<String, Object> params) {
        return params;
    }

    @Override
    public boolean isNeedParams() {
        return true;
    }

    @Override
    public boolean isNeedRequest() {
        return false;
    }

    @Override
    public void onSuccess(LiveStatusCode liveStatusCode) {
        Log.e(TAG, "---live   LiveUserForbiddenListener onSuccess liveStatusCode=" + liveStatusCode);
        // 用户被禁言，被踢出直播间
        LiveSocketManager.getInstance().callUserForbiddenListener(liveStatusCode);
    }

    @Override
    public void onError(ApiException exception) {
        Log.e(TAG, "---live   LiveUserForbiddenListener onError exception=" + exception);
    }
}
