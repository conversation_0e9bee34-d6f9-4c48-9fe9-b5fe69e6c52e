package com.kaolafm.opensdk.api.yunxin.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.kaolafm.opensdk.api.live.LiveApiConstant;

/**
 * <AUTHOR>
 * @date 2023-02-16
 */
public class GiftRankUser implements Parcelable {

    private String nickname;//用户昵称
    private String avatar;//头像

    // ***********此处字段替换************
    private String openUid;//用户id  旧版云信使用

    private String account;//用户账号 新版接口使用
    // ***********此处字段替换************

    public String getNickname() {
        return nickname == null ? "" : nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getAvatar() {
        return avatar == null ? "" : avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getOpenUid() {
        if (LiveApiConstant.IS_USE_NEW_SOCKET){
            return getAccount();
        }
        return openUid == null ? "" : openUid;
    }

    public void setOpenUid(String openUid) {
        this.openUid = openUid;
    }

    public String getAccount() {
        return account == null ? "" : account;
    }

    public void setAccount(String account) {
        this.account = account;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.nickname);
        dest.writeString(this.avatar);
        dest.writeString(this.openUid);
        dest.writeString(this.account);
    }

    public GiftRankUser() {
    }

    protected GiftRankUser(Parcel in) {
        this.nickname = in.readString();
        this.avatar = in.readString();
        this.openUid = in.readString();
        this.account = in.readString();
    }

    public static final Parcelable.Creator<GiftRankUser> CREATOR = new Parcelable.Creator<GiftRankUser>() {
        @Override
        public GiftRankUser createFromParcel(Parcel source) {
            return new GiftRankUser(source);
        }

        @Override
        public GiftRankUser[] newArray(int size) {
            return new GiftRankUser[size];
        }
    };

    @Override
    public String toString() {
        return "GiftRankUser{" +
                "nickname='" + nickname + '\'' +
                ", avatar='" + avatar + '\'' +
                ", openUid='" + openUid + '\'' +
                ", account='" + account + '\'' +
                '}';
    }
}
