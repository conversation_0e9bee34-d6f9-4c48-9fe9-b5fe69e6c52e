package com.kaolafm.opensdk.demo.operation.category;

import android.content.Intent;
import android.os.Bundle;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.util.Log;
import android.widget.TextView;
import butterknife.BindView;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.operation.OperationRequest;
import com.kaolafm.opensdk.api.operation.model.category.CategoryMember;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.lcodecore.tkrefreshlayout.RefreshListenerAdapter;
import com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/8/8
 */

public class CategoryMemberActivity extends BaseActivity {
    public static final String KEY_CODE = "Code";

    @BindView(R.id.rv_category_member_list)
    RecyclerView mRvCategoryMemberList;

    @BindView(R.id.trf_category_member_refresh)
    TwinklingRefreshLayout mTrfCategoryMemberRefresh;

    @BindView(R.id.tv_category_member_num)
    TextView mTvCategoryMemberNum;

    private CategoryMemberAdapter mCategoryMemberAdapter;

    private String mCode;

    private int mPageNum = 1;

    @Override
    public int getLayoutId() {
        return R.layout.activity_category_member;
    }

    @Override
    public void initArgs() {
        Intent intent = getIntent();
        if (intent != null) {
            mCode = intent.getStringExtra("Code");
        }
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        setTitle("分类成员列表");
        mCategoryMemberAdapter = new CategoryMemberAdapter();
        mRvCategoryMemberList.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false));
        mRvCategoryMemberList.setAdapter(mCategoryMemberAdapter);
        mRvCategoryMemberList.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));
        mCategoryMemberAdapter.setOnItemClickListener((view, viewType, categoryMember, position) -> {
            Log.e("CategoryMemberActivity", "initView: " + categoryMember.toString());
            Intent intent = new Intent(CategoryMemberActivity.this, CategoryInfoActivity.class);
            intent.putExtra(CategoryInfoActivity.KEY_CATEGORY_MEMBER, categoryMember);
            startActivity(intent);
        });
        mTrfCategoryMemberRefresh.setOnRefreshListener(new RefreshListenerAdapter() {
            @Override
            public void onRefresh(TwinklingRefreshLayout refreshLayout) {
                super.onRefresh(refreshLayout);
                mPageNum = 1;
                initData();
            }

            @Override
            public void onLoadMore(TwinklingRefreshLayout refreshLayout) {
                super.onLoadMore(refreshLayout);
                initData();
            }
        });
    }

    @Override
    public void initData() {

        OperationRequest operationRequest = new OperationRequest();
        operationRequest.getCategoryMemberList(mCode, mPageNum, 20,
                new HttpCallback<BasePageResult<List<CategoryMember>>>() {
                    @Override
                    public void onSuccess(BasePageResult<List<CategoryMember>> result) {
                        mTrfCategoryMemberRefresh.setEnableLoadmore(result.getHaveNext() == 1);
                        mPageNum = result.getNextPage();
                        List<CategoryMember> categoryMembers = result.getDataList();
                        if (categoryMembers != null && !categoryMembers.isEmpty()) {
                            if (result.getCurrentPage() > 1) {
                                mCategoryMemberAdapter.addDataList(categoryMembers);
                            } else {
                                mCategoryMemberAdapter.setDataList(categoryMembers);
                            }
                        } else {
                            showToast("数据为空");
                        }
                        mTrfCategoryMemberRefresh.finishRefreshing();
                        mTrfCategoryMemberRefresh.finishLoadmore();
                    }

                    @Override
                    public void onError(ApiException exception) {
                        showToast("网络请求错误， 错误码：" + exception.getCode() + ", 错误信息：" + exception.getMessage());
                        mTrfCategoryMemberRefresh.finishRefreshing();
                        mTrfCategoryMemberRefresh.finishLoadmore();
                    }
                });
        operationRequest.getCategoryMemberNum(mCode, new HttpCallback<Integer>() {
            @Override
            public void onSuccess(Integer integer) {
                mTvCategoryMemberNum.setText(integer + "个分类成员");
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取分类成员个数错误", exception);
            }
        });
    }
}
