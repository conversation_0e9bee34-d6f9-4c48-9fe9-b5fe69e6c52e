package com.kaolafm.ad.db.manager;

import com.kaolafm.ad.api.internal.model.ReportParamEntity;
import com.kaolafm.ad.db.greendao.ReportParamEntityDao;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.opensdk.apt.DBOpt;
import com.kaolafm.opensdk.db.manager.BaseDBManager;

import java.util.List;

import io.reactivex.Single;
import io.reactivex.schedulers.Schedulers;

/**
 * 上报参数数据库管理类
 * <AUTHOR>
 * @date 2020-02-12
 */
@DBOpt(name = "Advertising.db")
public class ReportParamDBManager extends BaseDBManager<ReportParamEntity, ReportParamEntityDao> {

    private static volatile ReportParamDBManager mInstance;

    private ReportParamDBManager() {
    }

    public static ReportParamDBManager getInstance() {
        if (mInstance == null) {
            synchronized (ReportParamDBManager.class) {
                if (mInstance == null) {
                    mInstance = new ReportParamDBManager();
                }
            }
        }
        return mInstance;
    }

    /**
     * 根据sessionId查询
     *
     * @param sessionId
     * @return
     */
    public Single<ReportParamEntity> queryById(String sessionId) {
        return Single.fromCallable(() -> {
            List<ReportParamEntity> list = mDao
                    .queryBuilder()
                    .where(ReportParamEntityDao.Properties.SessionId.eq(sessionId))
                    .list();
            if (!ListUtil.isEmpty(list)) {
                return list.get(0);
            }
            return null;
        }).subscribeOn(Schedulers.io()).unsubscribeOn(Schedulers.io());
    }

}
