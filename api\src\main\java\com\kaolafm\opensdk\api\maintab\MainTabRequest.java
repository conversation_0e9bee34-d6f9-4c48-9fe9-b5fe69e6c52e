package com.kaolafm.opensdk.api.maintab;

import android.util.Log;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.maintab.model.MainTabBean;
import com.kaolafm.opensdk.api.operation.model.column.ColumnChild;
import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.List;

public class MainTabRequest extends BaseRequest {
    private MainTabService mService;

    public MainTabRequest() {
        mService = obtainRetrofitService(MainTabService.class);
    }


    /**
     * 获取导航条数据
     *
     * @param callback 回调
     */
    public void getMainTab(HttpCallback<List<MainTabBean>> callback) {
        if (mService == null) {
            ComponentKit.getInstance().inject(this);
            mService = obtainRetrofitService(MainTabService.class);
        }
        if (mService != null) {
            doHttpDeal(mService.getMainTab(), BaseResult::getResult, callback);
        } else {
            Log.e("MainTabService", "getMainTab mService is null");
        }
    }
    public void getMainTabContent(int withMembers, String zone,HttpCallback<List<ColumnChild>> callback) {
        doHttpDeal(mService.getMainTabContent(withMembers,zone), BaseResult::getResult, callback);
    }
}
