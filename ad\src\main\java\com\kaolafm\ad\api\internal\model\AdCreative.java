package com.kaolafm.ad.api.internal.model;

import android.os.Parcel;

import com.google.gson.annotations.SerializedName;
import com.kaolafm.ad.api.model.AdvertisingDetails;

/**
 * 广告返回值的全量数据，主要在该子类中定义不需要对外暴露的参数
 * <AUTHOR>
 * @date 2020-01-13
 */
public class AdCreative extends AdvertisingDetails {

    /**
     * adzoneId : 11
     * customerId : 11
     * campainId : 11
     * adgroupId : 11
     * mediaId : 11
     */

    /** 广告客户id*/
    @SerializedName("customerId")
    private int customerId;

    /**广告计划id*/
    @SerializedName("campainId")
    private int campaignId;

    /**广告组id*/
    @SerializedName("adgroupId")
    private int adGroupId;

    /**媒体id*/
    @SerializedName("mediaId")
    private int mediaId;

    public int getCustomerId() {
        return customerId;
    }

    public void setCustomerId(int customerId) {
        this.customerId = customerId;
    }

    public int getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(int campaignId) {
        this.campaignId = campaignId;
    }

    public int getAdGroupId() {
        return adGroupId;
    }

    public void setAdGroupId(int adGroupId) {
        this.adGroupId = adGroupId;
    }

    public int getMediaId() {
        return mediaId;
    }

    public void setMediaId(int mediaId) {
        this.mediaId = mediaId;
    }

    protected AdCreative(Parcel in) {
        super(in);
    }

}
