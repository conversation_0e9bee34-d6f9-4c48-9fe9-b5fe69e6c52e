package com.kaolafm.ad.api.model;

import android.os.Parcel;

/**
 * 图片广告bean
 *
 * <AUTHOR>
 * @date 2020-01-10
 */
public class ImageAdvert extends BaseAdvert {


    private int width;

    private int height;

    private AttachImage attachImage;

    public ImageAdvert() {
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public AttachImage getAttachImage() {
        return attachImage;
    }

    public void setAttachImage(AttachImage attachImage) {
        this.attachImage = attachImage;
    }

    public static final Creator<ImageAdvert> CREATOR = new Creator<ImageAdvert>() {
        @Override
        public ImageAdvert createFromParcel(Parcel in) {
            return new ImageAdvert(in);
        }

        @Override
        public ImageAdvert[] newArray(int size) {
            return new ImageAdvert[size];
        }
    };

    protected ImageAdvert(Parcel in) {
        super(in);
        this.width = in.readInt();
        this.height = in.readInt();
        this.attachImage = in.readParcelable(AttachImage.class.getClassLoader());
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        super.writeToParcel(dest, flags);
        dest.writeInt(width);
        dest.writeInt(height);
        dest.writeParcelable(attachImage, 0);
    }
}
