package com.kaolafm.opensdk.api.media;

import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.media.model.AlbumDetails;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import io.reactivex.Single;
import java.util.List;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Query;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: AlbumService.java                                               
 *                                                                  *
 * Created in 2018/8/10 下午2:49                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
/*package*/ interface AlbumService {

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_ALBUM_DETAILS)
    Single<BaseResult<List<AlbumDetails>>> getAlbumDetails(@Query("ids") long albumId);

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_ALBUM_DETAILS)
    Single<BaseResult<List<AlbumDetails>>> getAlbumDetails(@Query("ids") String albumId);


    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_AUDIO_LIST)
    Single<BaseResult<BasePageResult<List<AudioDetails>>>> getPlaylist(@Query("aid") long radioId, @Query("sorttype") int sortType, @Query("pagesize") int pageSize, @Query("pagenum") int pageNum, @Query("audioid") long audioId);
}