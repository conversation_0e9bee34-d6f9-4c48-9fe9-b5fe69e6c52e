package com.kaolafm.opensdk.socket;

import java.util.HashMap;
import java.util.Map;

/**
 * 长连接或轮询客户端接口
 *
 * <AUTHOR>
 * @date 2020/6/10
 */
public interface Client {

    /**
     * 创建连接需要的对象
     */
    void create();

    /**
     * 设置请求host
     */
    void setSocketHost(String socketHost);

    /**
     * 设置公共参数
     */
    void setMap(Map<String, String> map);

    /**
     * 开始
     */
    void open();

    /**
     * 重置连接
     */
    void reset();

    /**
     * 是否资源
     */
    void release();

    /**
     * 添加监听
     *
     * @param socketListener
     * @param <T>
     */
    <T> void addListener(SocketListener<T> socketListener);

    /**
     * 移除监听
     *
     * @param listener
     */
    void removeListener(SocketListener listener);

    /**
     * 发送数据
     *
     * @param msg
     * @param callback
     * @param <T>
     */
    <T> void request(Object msg, SocketListener<T> callback);

    /**
     * 请求数据
     *
     * @param listener
     * @param <T>
     */
    <T> void request(SocketListener<T> listener);

    /**
     * 是否能发送
     *
     * @return
     */
    boolean canEmit();

    /**
     * 注册SocketClient 连接失败监听器
     *
     * @param connectErrorListener
     */
    void registerConnectErrorListener(ConnectErrorListener connectErrorListener);

    /**
     * 注册SocketClient 连接中断监听器
     *
     * @param connectLostListener
     */
    void registerConnectLostListener(ConnectLostListener connectLostListener);
}
