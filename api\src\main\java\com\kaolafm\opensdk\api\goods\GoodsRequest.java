package com.kaolafm.opensdk.api.goods;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.live.model.GoodsDetails;
import com.kaolafm.opensdk.http.core.HttpCallback;

/**
 *
 */
public class GoodsRequest extends BaseRequest {

    private final GoodsService mGoodsService;

    public GoodsRequest() {
//        mUrlManager.putDomain(ApiHostConstants.MALL_DOMAIN_NAME, ApiHostConstants.MALL_HOST);
        mGoodsService = obtainRetrofitService(GoodsService.class);
    }

//    /**
//     * 获取商品列表
//     * @param liveId    直播id
//     * @param callback
//     */
//    public void getGoods(Integer liveId, HttpCallback<GoodsResult> callback) {
//        doHttpDeal(mGoodsService.getGoods(liveId), BaseResult::getResult, callback);
//    }

    /**
     * 获取商品详情
     * @param goodsId    商品id
     * @param callback
     */
    public void getGoodsDetails(Long goodsId, HttpCallback<GoodsDetails> callback) {
        doHttpDeal(mGoodsService.getGoodsDetails(goodsId), BaseResult::getResult, callback);
    }

}
