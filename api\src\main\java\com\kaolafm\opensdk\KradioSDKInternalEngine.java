package com.kaolafm.opensdk;

import android.app.Application;
import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.account.profile.KaolaProfileManager;
import com.kaolafm.opensdk.account.profile.Profile;
import com.kaolafm.opensdk.account.profile.QQMusicProfileManger;
import com.kaolafm.opensdk.account.token.KaolaAccessToken;
import com.kaolafm.opensdk.account.token.RealAccessTokenManager;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.core.VerifyActivation;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.model.ReportParameter;

import javax.inject.Inject;

/**
 * Kradio SDK 内部入口。真正执行Kradio SDK 初始化/激活逻辑的地方。
 *
 * <AUTHOR> Yan
 * @date 2020-03-05
 */
public class KradioSDKInternalEngine<T extends Options> extends BaseEngine<T, KaolaProfileManager> {
    private static final String TAG = "KradioSDKInternalEngine";

    protected Application mApplication;

    @Inject
    @AppScope
    QQMusicProfileManger mMusicProfileManger;

    @Inject
    @AppScope
    RealAccessTokenManager mAccessTokenManager;

    @Inject
    @AppScope
    VerifyActivation mVerifyActivation;

    @Override
    protected void internalInit(Application application, T options, HttpCallback<Boolean> callback) {
        Log.i(TAG, "internalInit: 开始内部初始化");
        try {
            ComponentKit.getInstance().inject(new BaseRequest() {
                @Override
                protected <T> T obtainRetrofitService(Class<T> service) {
                    return super.obtainRetrofitService(service);
                }
            });
            Log.i(TAG, "internalInit: BaseRequest注入完成");

            mApplication = application;
            Log.i(TAG, "internalInit: 应用实例设置完成");

            setDeviceId(options.getDeviceId());
            Log.i(TAG, "internalInit: 设备ID设置完成，deviceId=" + options.getDeviceId());

            loadProfile();
            Log.i(TAG, "internalInit: 配置文件加载完成");

            loadAccessToken();
            Log.i(TAG, "internalInit: 访问令牌加载完成");

            initReport();
            Log.i(TAG, "internalInit: 报告系统初始化完成");
        } catch (Exception e) {
            Log.e(TAG, "internalInit: 内部初始化失败", e);
            throw e;
        }
    }

    private void loadAccessToken() {
        Log.i(TAG, "loadAccessToken: 开始加载访问令牌");
        try {
            if (mAccessTokenManager == null) {
                Log.e(TAG, "loadAccessToken: AccessTokenManager为空，无法加载访问令牌");
                return;
            }

            Log.i(TAG, "loadAccessToken: 调用AccessTokenManager.loadCurrentAccessToken()");
            mAccessTokenManager.loadCurrentAccessToken();

            // 检查加载后的token状态
            KaolaAccessToken token = mAccessTokenManager.getKaolaAccessToken();
            if (token != null) {
                Log.i(TAG, "loadAccessToken: 访问令牌加载成功，openId是否为空=" +
                        (TextUtils.isEmpty(token.getOpenId()) ? "是" : "否") +
                        ", userId是否为空=" + (TextUtils.isEmpty(token.getUserId()) ? "是" : "否") +
                        ", accessToken是否为空=" + (TextUtils.isEmpty(token.getAccessToken()) ? "是" : "否"));
            } else {
                Log.w(TAG, "loadAccessToken: 访问令牌为空");
            }
        } catch (Exception e) {
            Log.e(TAG, "loadAccessToken: 访问令牌加载失败，异常类型=" + e.getClass().getName() + ", 异常信息=" + e.getMessage(), e);
        }
    }

    private void loadProfile() {
        Log.i(TAG, "loadProfile: 开始加载配置文件");
        try {
            if (mProfileManager == null) {
                Log.e(TAG, "loadProfile: ProfileManager为空，无法加载KaolaProfile");
                return;
            }

            Log.i(TAG, "loadProfile: 调用ProfileManager.loadProfile()");
            mProfileManager.loadProfile();

            // 检查加载后的profile状态
            Profile profile = mProfileManager.getProfile();
            if (profile != null) {
                Log.i(TAG, "loadProfile: KaolaProfile加载成功，deviceId=" + profile.getDeviceId() +
                        ", appId=" + mProfileManager.getAppId() +
                        ", appKey=" + (TextUtils.isEmpty(mProfileManager.getAppKey()) ? "为空" : "不为空"));
            } else {
                Log.w(TAG, "loadProfile: KaolaProfile为空");
            }

            if (mMusicProfileManger == null) {
                Log.e(TAG, "loadProfile: MusicProfileManger为空，无法加载QQMusicProfile");
                return;
            }

            Log.i(TAG, "loadProfile: 调用MusicProfileManger.loadCurrentProfile()");
            mMusicProfileManger.loadCurrentProfile();
            Log.i(TAG, "loadProfile: QQMusicProfile加载成功");
        } catch (Exception e) {
            Log.e(TAG, "loadProfile: 配置文件加载失败，异常类型=" + e.getClass().getName() + ", 异常信息=" + e.getMessage(), e);
        }
    }

//    private void initKlSdkVehicle() {
//        ReportHelper.getInstance().initBySdk();
//    }

    /**
     * 传入经度
     *
     * @param longitude
     */
    @Override
    public void setLongitude(String longitude) {
        Log.i(TAG, "setLongitude: 设置经度=" + longitude);
        super.setLongitude(longitude);
        ReportHelper.getInstance().setLon(longitude);
    }

    /**
     * 传入纬度
     *
     * @param latitude
     */
    @Override
    public void setLatitude(String latitude) {
        Log.i(TAG, "setLatitude: 设置纬度=" + latitude);
        super.setLatitude(latitude);
        ReportHelper.getInstance().setLat(latitude);
    }

    @Override
    public void setLocation(String lng, String lat) {
        Log.i(TAG, "setLocation: 设置位置信息，经度=" + lng + "，纬度=" + lat);
        super.setLocation(lng, lat);
        ReportHelper.getInstance().setLon(lng);
        ReportHelper.getInstance().setLat(lat);
    }

    @Override
    public void setLocation(String lng, String lat, String coordType) {
        Log.i(TAG, "setLocation: 设置位置信息，经度=" + lng + "，纬度=" + lat + "，坐标类型=" + coordType);
        if (StringUtil.isEmpty(coordType)) {
            setLocation(lng, lat);
        } else {
            super.setLocation(lng, lat, coordType);
            ReportHelper.getInstance().setLon(lng);
            ReportHelper.getInstance().setLat(lat);
            ReportHelper.getInstance().setCoordType(coordType);
        }
    }

    private void initReport() {
        Log.i(TAG, "initReport: 开始初始化报告系统");
        if (!isActivated()) {
            Log.w(TAG, "initReport: 设备未激活，跳过报告系统初始化");
            return;
        }
        /*
        这行貌似是区别新版sdk和1.5.x版本行为的，但是如果是initBySdk，会导致所有启动事件不上报
        老的代码没没动，新加了一个直接添加启动事件的方法ReportParameterManager.forceSetAppStartType
         */
        //ReportHelper.getInstance().initBySdk();  com.kaolafm.report.ReportHelper.isUseBySDK 默认为true。kradio需要设置为false。

        try {
            Log.i(TAG, "initReport: 创建ReportParameter");
            ReportParameter reportParameter = new ReportParameter();

            // 检查Profile是否可用
            if (mProfileManager == null || mProfileManager.getProfile() == null) {
                Log.e(TAG, "initReport: ProfileManager或Profile为空，无法设置报告参数");
                return;
            }

            // 设置报告参数
            String deviceId = mProfileManager.getProfile().getDeviceId();
            if (TextUtils.isEmpty(deviceId)) {
                Log.e(TAG, "initReport: deviceId为空，无法设置报告参数");
                return;
            }
            reportParameter.setDeviceId(deviceId);
            Log.i(TAG, "initReport: 设置deviceId=" + deviceId);

            String packageName = mProfileManager.getProfile().getPackageName();
            reportParameter.setChannel(packageName);
            Log.i(TAG, "initReport: 设置channel=" + packageName);

            String appId = mProfileManager.getAppId();
            reportParameter.setAppid(appId);
            Log.i(TAG, "initReport: 设置appId=" + appId);

            // 检查AccessTokenManager是否可用
            if (mAccessTokenManager == null) {
                Log.e(TAG, "initReport: AccessTokenManager为空，无法设置用户ID");
                return;
            }

            KaolaAccessToken token = mAccessTokenManager.getKaolaAccessToken();
            if (token == null) {
                Log.e(TAG, "initReport: KaolaAccessToken为空，无法设置用户ID");
                return;
            }

            String openId = token.getOpenId();
            reportParameter.setOpenid(openId);
            Log.i(TAG, "initReport: 设置openId=" + (TextUtils.isEmpty(openId) ? "为空" : openId));

            String userId = token.getUserId();
            reportParameter.setUid(userId);
            Log.i(TAG, "initReport: 设置userId=" + (TextUtils.isEmpty(userId) ? "为空" : userId));

            // 初始化ReportHelper
            Log.i(TAG, "initReport: 报告参数设置完成，开始初始化ReportHelper");
            ReportHelper reportHelper = ReportHelper.getInstance();
            reportHelper.init(mApplication, reportParameter);
            Log.i(TAG, "initReport: ReportHelper初始化完成");

            // 设置位置信息
            String lat = mProfileManager.getProfile().getLat();
            String lng = mProfileManager.getProfile().getLng();
            if (!TextUtils.isEmpty(lat) && !TextUtils.isEmpty(lng)) {
                reportHelper.setLat(lat);
                reportHelper.setLon(lng);
                Log.i(TAG, "initReport: 位置信息设置完成，经度=" + lng + "，纬度=" + lat);
            } else {
                Log.w(TAG, "initReport: 位置信息为空，跳过位置设置，lat=" + lat + ", lng=" + lng);
            }

        } catch (Exception e) {
            Log.e(TAG, "initReport: 报告系统初始化失败，异常类型=" + e.getClass().getName() + ", 异常信息=" + e.getMessage(), e);
        }
    }

    @Override
    public boolean isActivated() {
        if (mVerifyActivation == null) {
            Log.w(TAG, "isActivated: VerifyActivation为空，返回未激活状态");
            return false;
        }
        boolean isActivated = mVerifyActivation.isActivate();
        Log.i(TAG, "isActivated: 检查激活状态，结果=" + isActivated);
        return isActivated;
    }

    public void clearAll() {
        Log.i(TAG, "clearAll: 清除所有数据");
        if (mVerifyActivation != null) {
            mVerifyActivation.clearAll();
            Log.i(TAG, "clearAll: 激活数据清除完成");
        } else {
            Log.w(TAG, "clearAll: VerifyActivation为空，无法清除数据");
        }
    }

    @Override
    public void config(Application application, T options, HttpCallback<Boolean> callback) {
        Log.i(TAG, "config: 开始配置SDK");
        init(application, options, null);
        activate(callback);
    }

    @Override
    protected void internalActivate(HttpCallback<Boolean> callback) {
        Log.i(TAG, "internalActivate: 开始内部激活");
        mVerifyActivation.activate(new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                Log.i(TAG, "internalABctivate: 激活成功");
                initReport();
                if (callback != null) {
                    callback.onSuccess(aBoolean);
                }
            }

            @Override
            public void onError(ApiException exception) {
                Log.e(TAG, "internalActivate: 激活失败", exception);
                if (callback != null) {
                    callback.onError(exception);
                }
            }
        });
    }

    @Override
    public void release() {
        Log.i(TAG, "release: 开始释放资源");
        ReportHelper.getInstance().release();
        mApplication = null;
    }
}
