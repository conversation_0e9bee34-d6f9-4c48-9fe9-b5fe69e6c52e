com.kaolafm.base.utils.DateUtil -> com.kaolafm.base.utils.DateUtil:
    java.lang.String ONE_SECOND_AGO -> ONE_SECOND_AGO
    java.lang.String ONE_MINUTE_AGO -> ONE_MINUTE_AGO
    java.lang.String ONE_HOUR_AGO -> ONE_HOUR_AGO
    java.lang.String ONE_DAY_AGO -> ONE_DAY_AGO
    java.lang.String ONE_MONTH_AGO -> ONE_MONTH_AGO
    java.lang.String ONE_YEAR_AGO -> ONE_YEAR_AGO
    long mServerTime -> mServerTime
    long mResponseTime -> mResponseTime
    16:16:void <init>() -> <init>
    38:38:long string2Millis(java.lang.String) -> string2Millis
    48:48:long string2Millis(java.lang.String,java.lang.String) -> string2Millis
    52:58:long string2Millis(java.lang.String,java.lang.String,java.util.Locale) -> string2Millis
    62:63:java.lang.String formatMillis(java.lang.String,long) -> formatMillis
    66:66:java.lang.String formatMillis(long) -> formatMillis
    73:87:java.lang.String millis2Day(long) -> millis2Day
    94:116:java.lang.String getDisTimeStr(long) -> getDisTimeStr
    124:127:void setServerTime(java.lang.String) -> setServerTime
    134:139:long getServerTime() -> getServerTime
com.kaolafm.base.utils.DeviceUtil -> com.kaolafm.base.utils.DeviceUtil:
    int PRIVIDER_UNKNOWN -> PRIVIDER_UNKNOWN
    int PRIVIDER_CHINA_MOBILE -> PRIVIDER_CHINA_MOBILE
    int PRIVIDER_CHINA_UNICOM -> PRIVIDER_CHINA_UNICOM
    int PRIVIDER_CHINA_TELE -> PRIVIDER_CHINA_TELE
    java.lang.String mUdid -> mUdid
    32:57:java.lang.String getClientIP() -> getClientIP
    61:61:java.lang.String getDeviceId(android.app.Application) -> getDeviceId
    70:74:java.lang.String getScreenResolution(android.content.Context) -> getScreenResolution
    86:94:java.lang.String getImei(android.content.Context) -> getImei
    105:113:java.lang.String getScreenSize(android.content.Context) -> getScreenSize
    134:135:void <init>() -> <init>
    143:161:java.lang.String getDeviceDns() -> getDeviceDns
    168:171:java.lang.String getUdid(android.content.Context) -> getUdid
    175:178:boolean isIdValid(java.lang.String) -> isIdValid
    190:232:java.lang.String getMyUUID() -> getMyUUID
    236:257:java.lang.String getMyUUID(android.content.Context) -> getMyUUID
    270:274:java.lang.String getIMSI(android.content.Context) -> getIMSI
    282:299:int getProvidersName(android.content.Context) -> getProvidersName
com.kaolafm.base.utils.KaolaTask -> com.kaolafm.base.utils.KaolaTask:
    int CPU_COUNT -> CPU_COUNT
    int CORE_POOL_SIZE -> CORE_POOL_SIZE
    int MAXIMUM_POOL_SIZE -> MAXIMUM_POOL_SIZE
    int KEEP_ALIVE -> KEEP_ALIVE
    java.util.concurrent.ThreadFactory sThreadFactory -> sThreadFactory
    java.util.concurrent.BlockingQueue sPoolWorkQueue -> sPoolWorkQueue
    java.util.concurrent.Executor THREAD_POOL_EXECUTOR -> THREAD_POOL_EXECUTOR
    int MESSAGE_POST_RESULT -> MESSAGE_POST_RESULT
    int MESSAGE_POST_PROGRESS -> MESSAGE_POST_PROGRESS
    com.kaolafm.base.utils.KaolaTask$InternalHandler sHandler -> sHandler
    java.util.concurrent.Executor sDefaultExecutor -> sDefaultExecutor
    com.kaolafm.base.utils.KaolaTask$WorkerRunnable mWorker -> mWorker
    java.util.concurrent.FutureTask mFuture -> mFuture
    com.kaolafm.base.utils.KaolaTask$Status mStatus -> mStatus
    java.util.concurrent.atomic.AtomicBoolean mCancelled -> mCancelled
    java.util.concurrent.atomic.AtomicBoolean mTaskInvoked -> mTaskInvoked
    java.lang.Object tag -> tag
    70:70:java.lang.Object getTag() -> getTag
    74:75:void setTag(java.lang.Object) -> setTag
    84:85:void init() -> init
    63:117:void <init>() -> <init>
    120:124:void postResultIfNotInvoked(java.lang.Object) -> postResultIfNotInvoked
    128:132:java.lang.Object postResult(java.lang.Object) -> postResult
    136:136:com.kaolafm.base.utils.KaolaTask$Status getStatus() -> getStatus
    java.lang.Object doInBackground(java.lang.Object[]) -> doInBackground
    142:142:void onPreExecute() -> onPreExecute
    146:146:void onPostExecute(java.lang.Object) -> onPostExecute
    154:154:void onExecute(java.lang.Object) -> onExecute
    158:158:void onProgressUpdate(java.lang.Object[]) -> onProgressUpdate
    162:163:void onCancelled(java.lang.Object) -> onCancelled
    166:166:void onCancelled() -> onCancelled
    169:169:boolean isCancelled() -> isCancelled
    173:174:boolean cancel(boolean) -> cancel
    178:178:java.lang.Object get() -> get
    183:183:java.lang.Object get(long,java.util.concurrent.TimeUnit) -> get
    187:187:com.kaolafm.base.utils.KaolaTask execute(java.lang.Object[]) -> execute
    192:211:com.kaolafm.base.utils.KaolaTask executeOnExecutor(java.util.concurrent.Executor,java.lang.Object[]) -> executeOnExecutor
    215:216:void execute(java.lang.Runnable) -> execute
    219:223:void publishProgress(java.lang.Object[]) -> publishProgress
    226:232:void finish(java.lang.Object) -> finish
    30:30:java.util.concurrent.atomic.AtomicBoolean access$100(com.kaolafm.base.utils.KaolaTask) -> access$100
    30:30:java.lang.Object access$200(com.kaolafm.base.utils.KaolaTask,java.lang.Object) -> access$200
    30:30:void access$300(com.kaolafm.base.utils.KaolaTask,java.lang.Object) -> access$300
    30:30:void access$400(com.kaolafm.base.utils.KaolaTask,java.lang.Object) -> access$400
    31:59:void <clinit>() -> <clinit>
com.kaolafm.base.utils.KaolaTask$1 -> com.kaolafm.base.utils.KaolaTask$1:
    java.util.concurrent.atomic.AtomicInteger mCount -> mCount
    36:37:void <init>() -> <init>
    40:40:java.lang.Thread newThread(java.lang.Runnable) -> newThread
com.kaolafm.base.utils.KaolaTask$2 -> com.kaolafm.base.utils.KaolaTask$2:
    com.kaolafm.base.utils.KaolaTask this$0 -> this$0
    92:92:void <init>(com.kaolafm.base.utils.KaolaTask) -> <init>
    94:98:java.lang.Object call() -> call
com.kaolafm.base.utils.KaolaTask$3 -> com.kaolafm.base.utils.KaolaTask$3:
    com.kaolafm.base.utils.KaolaTask this$0 -> this$0
    102:102:void <init>(com.kaolafm.base.utils.KaolaTask,java.util.concurrent.Callable) -> <init>
    106:115:void done() -> done
com.kaolafm.base.utils.KaolaTask$4 -> com.kaolafm.base.utils.KaolaTask$4:
    int[] $SwitchMap$com$kaolafm$base$utils$KaolaTask$Status -> $SwitchMap$com$kaolafm$base$utils$KaolaTask$Status
    193:193:void <clinit>() -> <clinit>
com.kaolafm.base.utils.KaolaTask$InternalHandler -> com.kaolafm.base.utils.KaolaTask$InternalHandler:
    236:237:void <init>() -> <init>
    242:252:void handleMessage(android.os.Message) -> handleMessage
com.kaolafm.base.utils.KaolaTask$KaolaTaskResult -> com.kaolafm.base.utils.KaolaTask$KaolaTaskResult:
    com.kaolafm.base.utils.KaolaTask mTask -> mTask
    java.lang.Object[] mData -> mData
    264:267:void <init>(com.kaolafm.base.utils.KaolaTask,java.lang.Object[]) -> <init>
com.kaolafm.base.utils.KaolaTask$Status -> com.kaolafm.base.utils.KaolaTask$Status:
    com.kaolafm.base.utils.KaolaTask$Status PENDING -> PENDING
    com.kaolafm.base.utils.KaolaTask$Status RUNNING -> RUNNING
    com.kaolafm.base.utils.KaolaTask$Status FINISHED -> FINISHED
    com.kaolafm.base.utils.KaolaTask$Status[] $VALUES -> $VALUES
    77:77:com.kaolafm.base.utils.KaolaTask$Status[] values() -> values
    77:77:com.kaolafm.base.utils.KaolaTask$Status valueOf(java.lang.String) -> valueOf
    77:77:void <init>(java.lang.String,int) -> <init>
    77:80:void <clinit>() -> <clinit>
com.kaolafm.base.utils.KaolaTask$WorkerRunnable -> com.kaolafm.base.utils.KaolaTask$WorkerRunnable:
    java.lang.Object[] mParams -> mParams
    255:255:void <init>() -> <init>
    255:255:void <init>(com.kaolafm.base.utils.KaolaTask$1) -> <init>
com.kaolafm.base.utils.ListUtil -> com.kaolafm.base.utils.ListUtil:
    16:16:void <init>() -> <init>
    19:19:boolean isEmpty(android.util.SparseArray) -> isEmpty
    23:23:boolean isEmpty(java.util.Collection) -> isEmpty
    27:27:boolean isEmpty(java.util.Map) -> isEmpty
    31:31:boolean isEmpty(java.lang.Object[]) -> isEmpty
com.kaolafm.base.utils.MD5 -> com.kaolafm.base.utils.MD5:
    char[] hexDigits -> hexDigits
    18:19:void <init>() -> <init>
    22:30:java.lang.String hexdigest(java.lang.String) -> hexdigest
    34:54:java.lang.String hexdigest(byte[]) -> hexdigest
    58:59:void main(java.lang.String[]) -> main
    68:90:java.lang.String getMD5Str(java.lang.String) -> getMD5Str
    16:16:void <clinit>() -> <clinit>
com.kaolafm.base.utils.NetworkMonitor -> com.kaolafm.base.utils.NetworkMonitor:
    java.lang.ref.WeakReference mContextWeakReference -> mContextWeakReference
    int STATUS_NO_NETWORK -> STATUS_NO_NETWORK
    int STATUS_WIFI -> STATUS_WIFI
    int STATUS_MOBILE -> STATUS_MOBILE
    int mNetStatus -> mNetStatus
    java.util.ArrayList mListeners -> mListeners
    android.net.ConnectivityManager mConnectivityManager -> mConnectivityManager
    android.content.BroadcastReceiver mReceiver -> mReceiver
    34:39:com.kaolafm.base.utils.NetworkMonitor getInstance(android.content.Context) -> getInstance
    27:106:void <init>() -> <init>
    48:58:void registerNetworkStatusChangeListener(com.kaolafm.base.utils.NetworkMonitor$OnNetworkStatusChangedListener) -> registerNetworkStatusChangeListener
    61:68:void removeNetworkStatusChangeListener(com.kaolafm.base.utils.NetworkMonitor$OnNetworkStatusChangedListener) -> removeNetworkStatusChangeListener
    71:75:void init(android.content.Context) -> init
    78:83:boolean isNetStatusChanged() -> isNetStatusChanged
    87:102:int populateNetworkStatus() -> populateNetworkStatus
    14:14:void <init>(com.kaolafm.base.utils.NetworkMonitor$1) -> <init>
    14:14:int access$200(com.kaolafm.base.utils.NetworkMonitor) -> access$200
    14:14:boolean access$300(com.kaolafm.base.utils.NetworkMonitor) -> access$300
    14:14:java.util.ArrayList access$400(com.kaolafm.base.utils.NetworkMonitor) -> access$400
com.kaolafm.base.utils.NetworkMonitor$1 -> com.kaolafm.base.utils.NetworkMonitor$1:
    com.kaolafm.base.utils.NetworkMonitor this$0 -> this$0
    106:106:void <init>(com.kaolafm.base.utils.NetworkMonitor) -> <init>
    109:119:void onReceive(android.content.Context,android.content.Intent) -> onReceive
com.kaolafm.base.utils.NetworkMonitor$NETWORK_MONITOR_CLASS -> com.kaolafm.base.utils.NetworkMonitor$NETWORK_MONITOR_CLASS:
    com.kaolafm.base.utils.NetworkMonitor NETWORK_MONITOR_INSTANCE -> NETWORK_MONITOR_INSTANCE
    29:29:void <init>() -> <init>
    29:29:com.kaolafm.base.utils.NetworkMonitor access$100() -> access$100
    30:30:void <clinit>() -> <clinit>
com.kaolafm.base.utils.NetworkMonitor$OnNetworkStatusChangedListener -> com.kaolafm.base.utils.NetworkMonitor$OnNetworkStatusChangedListener:
    void onStatusChanged(int,int) -> onStatusChanged
com.kaolafm.base.utils.NetworkUtil -> com.kaolafm.base.utils.NetworkUtil:
    int NETWORK_TYPE_UNKNOW -> NETWORK_TYPE_UNKNOW
    int NETWORK_TYPE_NO_WORK -> NETWORK_TYPE_NO_WORK
    int NETWORK_TYPE_WIFI -> NETWORK_TYPE_WIFI
    int NETWORK_TYPE_2G -> NETWORK_TYPE_2G
    int NETWORK_TYPE_3G -> NETWORK_TYPE_3G
    int NETWORK_TYPE_4G -> NETWORK_TYPE_4G
    boolean bCheckedNetInfo -> bCheckedNetInfo
    12:12:void <init>() -> <init>
    33:37:java.lang.String getMacAddr(android.content.Context) -> getMacAddr
    48:60:boolean isWAPStatic(android.content.Context) -> isWAPStatic
    68:79:boolean isNetworkAvailable(android.content.Context) -> isNetworkAvailable
    88:106:boolean isNetworkRoaming(android.content.Context) -> isNetworkRoaming
    117:129:boolean isWifiNetworkAvailable(android.content.Context) -> isWifiNetworkAvailable
    141:154:boolean isNGNetworkAvailable(android.content.Context) -> isNGNetworkAvailable
    164:181:boolean is2GMobileNetwork(android.content.Context) -> is2GMobileNetwork
    191:204:boolean is4GMobileNetwork(android.content.Context) -> is4GMobileNetwork
    208:213:android.net.NetworkInfo$State getState(android.content.Context) -> getState
    223:246:java.lang.String getCurrentAvailableNetworkType(android.content.Context) -> getCurrentAvailableNetworkType
    251:268:int getNetworkIndex(android.content.Context) -> getNetworkIndex
    280:303:int getNetwork(android.content.Context) -> getNetwork
    24:24:void <clinit>() -> <clinit>
com.kaolafm.base.utils.StringUtil -> com.kaolafm.base.utils.StringUtil:
    android.text.InputFilter emojiFilter -> emojiFilter
    38:39:void <init>() -> <init>
    69:80:java.lang.String str2HexStr(java.lang.String) -> str2HexStr
    88:106:java.lang.String jsonFormat(java.lang.String) -> jsonFormat
    114:129:java.lang.String xmlFormat(java.lang.String) -> xmlFormat
    133:141:java.lang.String array2String(java.lang.String,java.lang.Object[]) -> array2String
    145:145:java.lang.String array2String(java.lang.Object[]) -> array2String
    154:157:java.lang.String collection2String(java.util.Collection,java.lang.String) -> collection2String
    161:173:java.lang.String iterator2String(java.util.Iterator,java.lang.String) -> iterator2String
    183:197:java.lang.String truncateUrlPage(java.lang.String) -> truncateUrlPage
    208:234:java.util.Map getRequestParams(java.lang.String) -> getRequestParams
    241:251:java.lang.String join(java.lang.Object[]) -> join
    258:265:java.lang.String formatNum(long) -> formatNum
    275:278:java.lang.String createSign(java.util.Map,java.lang.String) -> createSign
    285:297:java.lang.StringBuilder array2StringWithIndex(java.lang.Object[]) -> array2StringWithIndex
    306:339:java.lang.String toString(java.lang.Object) -> toString
    348:348:boolean isEmpty(java.lang.String) -> isEmpty
    357:357:java.lang.String makeSafe(java.lang.String) -> makeSafe
    367:370:boolean isDigitsOnly(java.lang.String) -> isDigitsOnly
    381:393:java.lang.String format(java.lang.String,java.lang.Object[]) -> format
    276:276:int lambda$createSign$0(java.util.Map$Entry,java.util.Map$Entry) -> lambda$createSign$0
    42:42:void <clinit>() -> <clinit>
com.kaolafm.base.utils.StringUtil$1 -> com.kaolafm.base.utils.StringUtil$1:
    java.util.regex.Pattern emoji -> emoji
    42:44:void <init>() -> <init>
    52:57:java.lang.CharSequence filter(java.lang.CharSequence,int,int,android.text.Spanned,int,int) -> filter
com.kaolafm.base.utils.UrlUtil -> com.kaolafm.base.utils.UrlUtil:
    java.lang.String PIC_100_100 -> PIC_100_100
    java.lang.String PIC_250_250 -> PIC_250_250
    java.lang.String PIC_340_340 -> PIC_340_340
    java.lang.String PIC_720_254 -> PIC_720_254
    java.lang.String PIC_550_550 -> PIC_550_550
    java.lang.String JPG_STR -> JPG_STR
    java.lang.String JPEG_STR -> JPEG_STR
    java.lang.String PNG_STR -> PNG_STR
    17:18:void <init>() -> <init>
    31:49:java.lang.String getCustomPicUrl(java.lang.String,java.lang.String) -> getCustomPicUrl
    61:70:void appendValue(java.lang.StringBuilder,java.lang.String,java.lang.String) -> appendValue
    79:93:java.lang.String madeUrlSign(java.lang.String[],java.lang.String) -> madeUrlSign
com.kaolafm.base.utils.ZipHelper -> com.kaolafm.base.utils.ZipHelper:
    39:40:void <init>() -> <init>
    49:49:java.lang.String decompressToStringForZlib(byte[]) -> decompressToStringForZlib
    61:80:java.lang.String decompressToStringForZlib(byte[],java.lang.String) -> decompressToStringForZlib
    92:137:byte[] decompressForZlib(byte[]) -> decompressForZlib
    147:166:byte[] compressForZlib(byte[]) -> compressForZlib
    176:188:byte[] compressForZlib(java.lang.String) -> compressForZlib
    199:213:byte[] compressForGzip(java.lang.String) -> compressForGzip
    224:224:java.lang.String decompressForGzip(byte[]) -> decompressForGzip
    235:254:java.lang.String decompressForGzip(byte[],java.lang.String) -> decompressForGzip
    258:266:void closeQuietly(java.io.Closeable) -> closeQuietly
com.kaolafm.opensdk.Options -> com.kaolafm.opensdk.a:
    9:9:void <init>() -> <init>
com.kaolafm.opensdk.KradioSDK -> com.kaolafm.opensdk.b:
    com.kaolafm.opensdk.KradioSDK mInstance -> d
    boolean isInitialized -> e
    android.app.Application mApplication -> f
    com.kaolafm.opensdk.di.component.KradioComponent mAppComponent -> g
    com.kaolafm.opensdk.account.profile.KaolaProfileManager mProfileManager -> a
    com.kaolafm.opensdk.account.profile.QQMusicProfileManger mMusicProfileManger -> b
    com.kaolafm.opensdk.account.token.AccessTokenManager mAccessTokenManager -> c
    36:36:void <init>() -> <init>
    59:59:android.app.Application getContext() -> a
    66:90:void initSDK(android.app.Application,HttpCallback) -> a
    93:94:void loadAccessToken() -> g
    97:99:void loadProfile() -> h
    102:137:void initKlSdkVehicle(HttpCallback) -> b
    140:145:void setKLSDKOpenId(com.kaolafm.sdk.vehicle.KlSdkVehicle,com.kaolafm.opensdk.account.token.KaolaAccessToken) -> a
    151:188:void activate(HttpCallback) -> a
    196:197:boolean isActivate() -> b
    205:205:com.kaolafm.opensdk.di.component.KradioComponent getAppComponent() -> c
    213:217:void setLongitude(java.lang.String) -> a
    224:228:void setLatitude(java.lang.String) -> b
    231:247:void initReport() -> i
    250:253:void release() -> d
    36:36:com.kaolafm.opensdk.KradioSDK access$000() -> e
    36:36:void access$100() -> f
    38:40:void <clinit>() -> <clinit>
com.kaolafm.opensdk.KradioSDK$1 -> com.kaolafm.opensdk.c:
    103:103:void <init>() -> <init>
    106:108:void onServiceConnected(com.kaolafm.sdk.core.mediaplayer.PlayerService$PlayerBinder) -> a
    112:114:void onServiceDisconnected(android.content.ComponentName) -> a
    118:120:void disableOtherPlayer() -> a
    124:126:void destory() -> b
com.kaolafm.opensdk.KradioSDK$2 -> com.kaolafm.opensdk.d:
    com.kaolafm.opensdk.account.token.KaolaAccessToken val$kaolaToken -> a
    HttpCallback val$callback -> b
    156:156:void <init>(com.kaolafm.opensdk.account.token.KaolaAccessToken,HttpCallback) -> <init>
    159:174:void onSuccess(java.lang.String) -> a
    178:181:void onError(ApiException) -> onError
    156:156:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.KradioSDK_MembersInjector -> com.kaolafm.opensdk.e:
    javax.inject.Provider mProfileManagerProvider -> a
    javax.inject.Provider mMusicProfileMangerProvider -> b
    javax.inject.Provider mAccessTokenManagerProvider -> c
    20:24:void <init>(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    30:30:dagger.MembersInjector create(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> a
    36:39:void injectMembers(com.kaolafm.opensdk.KradioSDK) -> a
    43:44:void injectMProfileManager(com.kaolafm.opensdk.KradioSDK,com.kaolafm.opensdk.account.profile.KaolaProfileManager) -> a
    48:49:void injectMMusicProfileManger(com.kaolafm.opensdk.KradioSDK,com.kaolafm.opensdk.account.profile.QQMusicProfileManger) -> a
    53:54:void injectMAccessTokenManager(com.kaolafm.opensdk.KradioSDK,com.kaolafm.opensdk.account.token.AccessTokenManager) -> a
    10:10:void injectMembers(java.lang.Object) -> a
com.kaolafm.opensdk.OpenSDK -> com.kaolafm.opensdk.OpenSDK:
    com.kaolafm.opensdk.OpenSDK mInstance -> a
    19:20:void <init>() -> <init>
    23:30:com.kaolafm.opensdk.OpenSDK getInstance() -> getInstance
    34:34:android.app.Application getContext() -> getContext
    38:39:void initSDK(android.app.Application) -> initSDK
    45:46:void initSDK(android.app.Application,HttpCallback) -> initSDK
    52:53:void activate(HttpCallback) -> activate
    61:61:boolean isActivate() -> isActivate
    69:69:com.kaolafm.opensdk.di.component.KradioComponent getAppComponent() -> getAppComponent
    77:78:void setLongitude(java.lang.String) -> setLongitude
    85:86:void setLatitude(java.lang.String) -> setLatitude
    93:94:void release() -> release
com.kaolafm.opensdk.ResType -> com.kaolafm.opensdk.ResType:
    int TYPE_INVALID -> TYPE_INVALID
    int TYPE_ALBUM -> TYPE_ALBUM
    int TYPE_AUDIO -> TYPE_AUDIO
    int TYPE_TOPIC -> TYPE_TOPIC
    int TYPE_RADIO -> TYPE_RADIO
    int TYPE_URL -> TYPE_URL
    int TYPE_LIVE -> TYPE_LIVE
    int TYPE_BROADCAST -> TYPE_BROADCAST
    int TYPE_ALL -> TYPE_ALL
    int TYPE_SEARCH -> TYPE_SEARCH
    int TYPE_CATEGORY -> TYPE_CATEGORY
    int TYPE_RANK_LIST -> TYPE_RANK_LIST
    int TYPE_RADIO_LIST -> TYPE_RADIO_LIST
    int TYPE_LIVE_LIST -> TYPE_LIVE_LIST
    int TYPE_TOPIC_LIST -> TYPE_TOPIC_LIST
    int TYPE_BROADCAST_LIST -> TYPE_BROADCAST_LIST
    int FUNCTION_KRADIO_RADIO -> FUNCTION_KRADIO_RADIO
    int FUNCTION_KRADIO_MUSIC -> FUNCTION_KRADIO_MUSIC
    int TYPE_QQ_MUSIC -> TYPE_QQ_MUSIC
    int TYPE_SONGMENU -> TYPE_SONGMENU
    int TYPE_SONG_CHARTS -> TYPE_SONG_CHARTS
    int TYPE_MUSIC_RADIO_LABEL -> TYPE_MUSIC_RADIO_LABEL
    int TYPE_MUSIC_RADIO_SCENE -> TYPE_MUSIC_RADIO_SCENE
    int TYPE_MUSIC_VOICE_RESULT -> TYPE_MUSIC_VOICE_RESULT
    int MUSIC_MINE_PRIVATE_FM -> MUSIC_MINE_PRIVATE_FM
    int MUSIC_MINE_DAY -> MUSIC_MINE_DAY
    int MUSIC_MINE_LIKE -> MUSIC_MINE_LIKE
    int MUSIC_MINE_FAVOURITE -> MUSIC_MINE_FAVOURITE
    int MUSIC_MINE_HISTORY -> MUSIC_MINE_HISTORY
    int RADIO_MINE_HISTORY -> RADIO_MINE_HISTORY
    6:6:void <init>() -> <init>
com.kaolafm.opensdk.account.profile.KaolaProfile -> com.kaolafm.opensdk.account.profile.a:
    java.lang.String appId -> a
    java.lang.String appKey -> b
    java.lang.String channel -> c
    java.lang.String packageName -> d
    java.lang.String versionName -> e
    java.lang.String lng -> f
    java.lang.String lat -> g
    java.lang.String deviceId -> h
    java.lang.String nonceNum -> i
    java.lang.String sdkVersionName -> j
    43:44:void <init>() -> <init>
    47:48:void setAppId(java.lang.String) -> a
    51:52:void setAppKey(java.lang.String) -> b
    55:56:void setChannel(java.lang.String) -> c
    59:60:void setPackageName(java.lang.String) -> d
    63:64:void setVersionName(java.lang.String) -> e
    67:68:void setLng(java.lang.String) -> f
    71:72:void setLat(java.lang.String) -> g
    75:76:void setDeviceId(java.lang.String) -> h
    79:80:void setNonceNum(java.lang.String) -> i
    83:83:java.lang.String getDeviceId() -> a
    87:87:java.lang.String getAppId() -> b
    91:91:java.lang.String getAppKey() -> c
    95:95:java.lang.String getChannel() -> d
    99:99:java.lang.String getPackageName() -> e
    103:103:java.lang.String getVersionName() -> f
    107:107:java.lang.String getLng() -> g
    111:111:java.lang.String getLat() -> h
    118:118:java.lang.String getNonceNum() -> i
    122:122:java.lang.String getSdkVersionName() -> j
    126:127:void setSdkVersionName(java.lang.String) -> j
com.kaolafm.opensdk.account.profile.KaolaProfileManager -> com.kaolafm.opensdk.account.profile.KaolaProfileManager:
    java.lang.String APP_ID_PROPERTY -> APP_ID_PROPERTY
    java.lang.String APP_KEY_PROPERTY -> APP_KEY_PROPERTY
    java.lang.String CHANNEL_PROPERTY -> CHANNEL_PROPERTY
    java.lang.String IS_PACKAGE_APPEND_SUFFIX -> IS_PACKAGE_APPEND_SUFFIX
    android.app.Application mApplication -> mApplication
    com.kaolafm.opensdk.account.profile.KaolaProfile mProfile -> mProfile
    java.lang.String mPackageName -> mPackageName
    57:58:void <init>() -> <init>
    65:70:void loadCurrentProfile() -> loadCurrentProfile
    76:82:void setNonce() -> setNonce
    88:99:void getAppInfo() -> getAppInfo
    105:130:void loadAccountFromMetadata() -> loadAccountFromMetadata
    133:133:com.kaolafm.opensdk.account.profile.KaolaProfile getProfile() -> getProfile
    141:142:void setAppId(java.lang.String) -> setAppId
    145:146:void setAppKey(java.lang.String) -> setAppKey
    149:150:void setChannel(java.lang.String) -> setChannel
    153:154:void setLongitude(java.lang.String) -> setLongitude
    157:158:void setLatitude(java.lang.String) -> setLatitude
    161:161:java.lang.String getAppId() -> getAppId
    165:165:java.lang.String getAppKey() -> getAppKey
    169:169:java.lang.String getChannel() -> getChannel
    173:173:java.lang.String getPackageName() -> getPackageName
com.kaolafm.opensdk.account.profile.KaolaProfileManager_Factory -> com.kaolafm.opensdk.account.profile.b:
    javax.inject.Provider mApplicationProvider -> a
    javax.inject.Provider mProfileProvider -> b
    14:17:void <init>(javax.inject.Provider,javax.inject.Provider) -> <init>
    21:21:com.kaolafm.opensdk.account.profile.KaolaProfileManager get() -> get
    26:29:com.kaolafm.opensdk.account.profile.KaolaProfileManager provideInstance(javax.inject.Provider,javax.inject.Provider) -> a
    34:34:com.kaolafm.opensdk.account.profile.KaolaProfileManager_Factory create(javax.inject.Provider,javax.inject.Provider) -> create
    38:38:com.kaolafm.opensdk.account.profile.KaolaProfileManager newKaolaProfileManager() -> a
    8:8:java.lang.Object get() -> get
com.kaolafm.opensdk.account.profile.KaolaProfileManager_MembersInjector -> com.kaolafm.opensdk.account.profile.c:
    javax.inject.Provider mApplicationProvider -> a
    javax.inject.Provider mProfileProvider -> b
    15:18:void <init>(javax.inject.Provider,javax.inject.Provider) -> <init>
    22:22:dagger.MembersInjector create(javax.inject.Provider,javax.inject.Provider) -> a
    27:29:void injectMembers(com.kaolafm.opensdk.account.profile.KaolaProfileManager) -> a
    32:33:void injectMApplication(com.kaolafm.opensdk.account.profile.KaolaProfileManager,android.app.Application) -> a
    36:37:void injectMProfile(com.kaolafm.opensdk.account.profile.KaolaProfileManager,com.kaolafm.opensdk.account.profile.KaolaProfile) -> a
    8:8:void injectMembers(java.lang.Object) -> a
com.kaolafm.opensdk.account.profile.KaolaProfile_Factory -> com.kaolafm.opensdk.account.profile.d:
    com.kaolafm.opensdk.account.profile.KaolaProfile_Factory INSTANCE -> a
    6:6:void <init>() -> <init>
    11:11:com.kaolafm.opensdk.account.profile.KaolaProfile get() -> get
    15:15:com.kaolafm.opensdk.account.profile.KaolaProfile provideInstance() -> a
    19:19:com.kaolafm.opensdk.account.profile.KaolaProfile_Factory create() -> create
    23:23:com.kaolafm.opensdk.account.profile.KaolaProfile newKaolaProfile() -> b
    6:6:java.lang.Object get() -> get
    7:7:void <clinit>() -> <clinit>
com.kaolafm.opensdk.account.profile.QQMusicProfile -> com.kaolafm.opensdk.account.profile.e:
    java.lang.String appId -> a
    java.lang.String appKey -> b
    18:19:void <init>() -> <init>
    22:22:java.lang.String getAppId() -> a
    26:27:void setAppId(java.lang.String) -> a
    30:30:java.lang.String getAppKey() -> b
    34:35:void setAppKey(java.lang.String) -> b
com.kaolafm.opensdk.account.profile.QQMusicProfileManger -> com.kaolafm.opensdk.account.profile.f:
    java.lang.String APP_ID_PROPERTY -> c
    java.lang.String APP_KEY_PROPERTY -> d
    android.app.Application mApplication -> a
    com.kaolafm.opensdk.account.profile.QQMusicProfile mMusicProfile -> b
    43:44:void <init>() -> <init>
    47:47:java.lang.String getClientIp() -> a
    51:51:java.lang.String getDeviceId() -> b
    55:55:java.lang.String getTimestamp() -> c
    59:73:void loadCurrentProfile() -> d
    77:78:void setAppId(java.lang.String) -> a
    81:82:void setAppKey(java.lang.String) -> b
    85:85:java.lang.String getAppId() -> e
    89:89:java.lang.String getAppKey() -> f
    93:98:java.lang.String getSign() -> g
com.kaolafm.opensdk.account.profile.QQMusicProfileManger_Factory -> com.kaolafm.opensdk.account.profile.g:
    javax.inject.Provider mApplicationProvider -> a
    javax.inject.Provider mMusicProfileProvider -> b
    14:17:void <init>(javax.inject.Provider,javax.inject.Provider) -> <init>
    21:21:com.kaolafm.opensdk.account.profile.QQMusicProfileManger get() -> get
    26:29:com.kaolafm.opensdk.account.profile.QQMusicProfileManger provideInstance(javax.inject.Provider,javax.inject.Provider) -> a
    34:34:com.kaolafm.opensdk.account.profile.QQMusicProfileManger_Factory create(javax.inject.Provider,javax.inject.Provider) -> create
    38:38:com.kaolafm.opensdk.account.profile.QQMusicProfileManger newQQMusicProfileManger() -> a
    8:8:java.lang.Object get() -> get
com.kaolafm.opensdk.account.profile.QQMusicProfileManger_MembersInjector -> com.kaolafm.opensdk.account.profile.h:
    javax.inject.Provider mApplicationProvider -> a
    javax.inject.Provider mMusicProfileProvider -> b
    15:18:void <init>(javax.inject.Provider,javax.inject.Provider) -> <init>
    22:22:dagger.MembersInjector create(javax.inject.Provider,javax.inject.Provider) -> a
    27:29:void injectMembers(com.kaolafm.opensdk.account.profile.QQMusicProfileManger) -> a
    32:33:void injectMApplication(com.kaolafm.opensdk.account.profile.QQMusicProfileManger,android.app.Application) -> a
    36:37:void injectMMusicProfile(com.kaolafm.opensdk.account.profile.QQMusicProfileManger,java.lang.Object) -> a
    8:8:void injectMembers(java.lang.Object) -> a
com.kaolafm.opensdk.account.profile.QQMusicProfile_Factory -> com.kaolafm.opensdk.account.profile.i:
    com.kaolafm.opensdk.account.profile.QQMusicProfile_Factory INSTANCE -> a
    6:6:void <init>() -> <init>
    11:11:com.kaolafm.opensdk.account.profile.QQMusicProfile get() -> get
    15:15:com.kaolafm.opensdk.account.profile.QQMusicProfile provideInstance() -> a
    19:19:com.kaolafm.opensdk.account.profile.QQMusicProfile_Factory create() -> create
    23:23:com.kaolafm.opensdk.account.profile.QQMusicProfile newQQMusicProfile() -> b
    6:6:java.lang.Object get() -> get
    7:7:void <clinit>() -> <clinit>
com.kaolafm.opensdk.account.token.AccessTokenManager -> com.kaolafm.opensdk.account.token.AccessTokenManager:
    java.lang.String TOKEN_KAOLA -> TOKEN_KAOLA
    java.lang.String TOKEN_QQMUSIC -> TOKEN_QQMUSIC
    java.util.Map mTokenCaches -> a
    com.kaolafm.opensdk.account.token.AccessTokenManager sInstance -> b
    27:29:void <init>() -> <init>
    32:39:com.kaolafm.opensdk.account.token.AccessTokenManager getInstance() -> getInstance
    46:54:void loadCurrentAccessToken() -> loadCurrentAccessToken
    61:65:void logout(java.lang.String) -> logout
    73:74:void logoutKaola() -> logoutKaola
    80:81:void logoutQQMusic() -> logoutQQMusic
    87:95:void logoutAll() -> logoutAll
    101:109:void clearAll() -> clearAll
    116:120:void clear(java.lang.String) -> clear
    128:136:void setCurrentAccessToken(com.kaolafm.opensdk.account.token.AccessToken) -> setCurrentAccessToken
    144:150:com.kaolafm.opensdk.account.token.AccessToken getCurrentAccessToken(java.lang.String) -> getCurrentAccessToken
    158:159:com.kaolafm.opensdk.account.token.KaolaAccessToken getKaolaAccessToken() -> getKaolaAccessToken
    167:168:com.kaolafm.opensdk.account.token.QQMusicAccessToken getQQMusicAccessToken() -> getQQMusicAccessToken
com.kaolafm.opensdk.account.token.AccessTokenManager_MembersInjector -> com.kaolafm.opensdk.account.token.a:
    javax.inject.Provider mTokenCachesProvider -> a
    13:15:void <init>(javax.inject.Provider) -> <init>
    19:19:dagger.MembersInjector create(javax.inject.Provider) -> a
    24:25:void injectMembers(com.kaolafm.opensdk.account.token.AccessTokenManager) -> a
    29:30:void injectMTokenCaches(com.kaolafm.opensdk.account.token.AccessTokenManager,java.util.Map) -> a
    8:8:void injectMembers(java.lang.Object) -> a
com.kaolafm.opensdk.account.token.AccessToken -> com.kaolafm.opensdk.account.token.AccessToken:
    boolean isLogin() -> isLogin
    void logout() -> logout
    void clear() -> clear
    boolean isExpires() -> isExpires
com.kaolafm.opensdk.account.token.TokenCache -> com.kaolafm.opensdk.account.token.b:
    boolean accept(com.kaolafm.opensdk.account.token.AccessToken) -> a
    java.lang.Object getToken() -> a
    void save(java.lang.Object) -> a
    java.lang.Object load() -> b
    void clear() -> c
    void logout() -> d
com.kaolafm.opensdk.account.token.KaolaAccessToken -> com.kaolafm.opensdk.account.token.KaolaAccessToken:
    java.lang.String openId -> openId
    long refreshTime -> refreshTime
    java.lang.String userId -> userId
    java.lang.String accessToken -> accessToken
    java.lang.String refreshToken -> refreshToken
    long expireTime -> expireTime
    android.os.Parcelable$Creator CREATOR -> CREATOR
    37:38:void <init>() -> <init>
    41:41:java.lang.String getOpenId() -> getOpenId
    46:46:boolean isLogin() -> isLogin
    51:56:void logout() -> logout
    60:62:void clear() -> clear
    66:66:boolean isExpires() -> isExpires
    70:71:void setOpenId(java.lang.String) -> setOpenId
    74:74:long getRefreshTime() -> getRefreshTime
    78:79:void setExpireTime(long) -> setExpireTime
    82:83:void setRefreshTime(long) -> setRefreshTime
    86:86:java.lang.String getUserId() -> getUserId
    90:91:void setUserId(java.lang.String) -> setUserId
    94:94:java.lang.String getAccessToken() -> getAccessToken
    99:99:java.lang.String getToken() -> getToken
    103:104:void setAccessToken(java.lang.String) -> setAccessToken
    107:107:java.lang.String getRefreshToken() -> getRefreshToken
    111:112:void setRefreshToken(java.lang.String) -> setRefreshToken
    116:122:java.lang.String toString() -> toString
    125:131:void <init>(android.os.Parcel) -> <init>
    135:135:int describeContents() -> describeContents
    140:145:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    17:17:void <init>(android.os.Parcel,com.kaolafm.opensdk.account.token.KaolaAccessToken$1) -> <init>
    147:147:void <clinit>() -> <clinit>
com.kaolafm.opensdk.account.token.KaolaAccessToken$1 -> com.kaolafm.opensdk.account.token.c:
    147:147:void <init>() -> <init>
    151:151:com.kaolafm.opensdk.account.token.KaolaAccessToken createFromParcel(android.os.Parcel) -> a
    156:156:com.kaolafm.opensdk.account.token.KaolaAccessToken[] newArray(int) -> a
    147:147:java.lang.Object[] newArray(int) -> newArray
    147:147:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
com.kaolafm.opensdk.account.token.KaolaAccessTokenCache -> com.kaolafm.opensdk.account.token.d:
    java.lang.String SP_KAOLA_ACCESS_TOKEN_NAME -> c
    java.lang.String CACHED_KAOLA_ACCESS_TOKEN -> d
    java.lang.String CACHED_KAOLA_ACCESS_TOKEN_OPEN_ID_KEY -> e
    android.content.SharedPreferences mSharedPreferences -> f
    com.kaolafm.opensdk.account.token.KaolaAccessToken mAccessToken -> a
    dagger.Lazy mGsonLazy -> b
    38:42:void <init>(android.app.Application) -> <init>
    46:46:boolean accept(com.kaolafm.opensdk.account.token.AccessToken) -> a
    51:51:com.kaolafm.opensdk.account.token.KaolaAccessToken getToken() -> e
    56:70:void save(com.kaolafm.opensdk.account.token.KaolaAccessToken) -> a
    73:74:void save(java.lang.String,java.lang.String) -> a
    77:79:void saveOpenId(java.lang.String) -> a
    83:90:com.kaolafm.opensdk.account.token.KaolaAccessToken load() -> f
    96:102:void clear() -> c
    106:108:void logout() -> d
    17:17:java.lang.Object load() -> b
    17:17:void save(java.lang.Object) -> a
    17:17:java.lang.Object getToken() -> a
com.kaolafm.opensdk.account.token.KaolaAccessTokenCache_Factory -> com.kaolafm.opensdk.account.token.e:
    javax.inject.Provider applicationProvider -> a
    javax.inject.Provider mAccessTokenProvider -> b
    javax.inject.Provider mGsonLazyProvider -> c
    20:24:void <init>(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    28:28:com.kaolafm.opensdk.account.token.KaolaAccessTokenCache get() -> get
    35:39:com.kaolafm.opensdk.account.token.KaolaAccessTokenCache provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> a
    46:46:com.kaolafm.opensdk.account.token.KaolaAccessTokenCache_Factory create(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> create
    51:51:com.kaolafm.opensdk.account.token.KaolaAccessTokenCache newKaolaAccessTokenCache(android.app.Application) -> a
    10:10:java.lang.Object get() -> get
com.kaolafm.opensdk.account.token.KaolaAccessTokenCache_MembersInjector -> com.kaolafm.opensdk.account.token.f:
    javax.inject.Provider mAccessTokenProvider -> a
    javax.inject.Provider mGsonLazyProvider -> b
    17:20:void <init>(javax.inject.Provider,javax.inject.Provider) -> <init>
    24:24:dagger.MembersInjector create(javax.inject.Provider,javax.inject.Provider) -> a
    29:31:void injectMembers(com.kaolafm.opensdk.account.token.KaolaAccessTokenCache) -> a
    35:36:void injectMAccessToken(com.kaolafm.opensdk.account.token.KaolaAccessTokenCache,com.kaolafm.opensdk.account.token.KaolaAccessToken) -> a
    39:40:void injectMGsonLazy(com.kaolafm.opensdk.account.token.KaolaAccessTokenCache,dagger.Lazy) -> a
    10:10:void injectMembers(java.lang.Object) -> a
com.kaolafm.opensdk.account.token.KaolaAccessToken_Factory -> com.kaolafm.opensdk.account.token.g:
    com.kaolafm.opensdk.account.token.KaolaAccessToken_Factory INSTANCE -> a
    6:6:void <init>() -> <init>
    11:11:com.kaolafm.opensdk.account.token.KaolaAccessToken get() -> get
    15:15:com.kaolafm.opensdk.account.token.KaolaAccessToken provideInstance() -> a
    19:19:com.kaolafm.opensdk.account.token.KaolaAccessToken_Factory create() -> create
    23:23:com.kaolafm.opensdk.account.token.KaolaAccessToken newKaolaAccessToken() -> b
    6:6:java.lang.Object get() -> get
    7:7:void <clinit>() -> <clinit>
com.kaolafm.opensdk.account.token.QQMusicAccessToken -> com.kaolafm.opensdk.account.token.QQMusicAccessToken:
    long musicId -> musicId
    java.lang.String musicKey -> musicKey
    java.lang.String token -> token
    java.lang.String openId -> openId
    java.lang.String refreshToken -> refreshToken
    java.util.Date expiresTime -> expiresTime
    java.lang.String unionId -> unionId
    java.lang.String appId -> appId
    int loginType -> loginType
    android.os.Parcelable$Creator CREATOR -> CREATOR
    20:21:void <init>() -> <init>
    24:34:void <init>(long,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.util.Date,java.lang.String,java.lang.String,int) -> <init>
    82:91:void <init>(android.os.Parcel) -> <init>
    107:107:int describeContents() -> describeContents
    112:120:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    124:124:boolean isLogin() -> isLogin
    129:130:void logout() -> logout
    134:137:void clear() -> clear
    141:141:boolean isExpires() -> isExpires
    145:145:long getMusicId() -> getMusicId
    149:150:void setMusicId(long) -> setMusicId
    153:153:java.lang.String getMusicKey() -> getMusicKey
    157:158:void setMusicKey(java.lang.String) -> setMusicKey
    161:161:java.lang.String getToken() -> getToken
    165:166:void setToken(java.lang.String) -> setToken
    169:169:java.lang.String getOpenId() -> getOpenId
    173:174:void setOpenId(java.lang.String) -> setOpenId
    177:177:java.lang.String getRefreshToken() -> getRefreshToken
    181:182:void setRefreshToken(java.lang.String) -> setRefreshToken
    185:185:java.util.Date getExpiresTime() -> getExpiresTime
    189:190:void setExpiresTime(java.util.Date) -> setExpiresTime
    193:193:java.lang.String getUnionId() -> getUnionId
    197:198:void setUnionId(java.lang.String) -> setUnionId
    201:201:java.lang.String getAppId() -> getAppId
    205:206:void setAppId(java.lang.String) -> setAppId
    209:209:int getLoginType() -> getLoginType
    213:214:void setLoginType(int) -> setLoginType
    217:228:com.kaolafm.opensdk.account.token.QQMusicAccessToken setCurrentAccessToken(com.kaolafm.opensdk.api.music.qq.model.TencentLoginResult) -> setCurrentAccessToken
    232:238:com.kaolafm.opensdk.account.token.QQMusicAccessToken refreshToken(com.kaolafm.opensdk.api.music.qq.model.TencentLoginResult) -> refreshToken
    93:93:void <clinit>() -> <clinit>
com.kaolafm.opensdk.account.token.QQMusicAccessToken$1 -> com.kaolafm.opensdk.account.token.h:
    93:93:void <init>() -> <init>
    96:96:com.kaolafm.opensdk.account.token.QQMusicAccessToken createFromParcel(android.os.Parcel) -> a
    101:101:com.kaolafm.opensdk.account.token.QQMusicAccessToken[] newArray(int) -> a
    93:93:java.lang.Object[] newArray(int) -> newArray
    93:93:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
com.kaolafm.opensdk.account.token.QQMusicAccessTokenCache -> com.kaolafm.opensdk.account.token.i:
    java.lang.String SP_QQMUSIC_ACCESS_TOKEN_NAME -> c
    java.lang.String CACHED_QQMUSIC_ACCESS_TOKEN -> d
    dagger.Lazy mGsonLazy -> a
    com.kaolafm.opensdk.account.token.QQMusicAccessToken mAccessToken -> b
    android.content.SharedPreferences mSharedPreferences -> e
    31:33:void <init>(android.app.Application) -> <init>
    37:37:boolean accept(com.kaolafm.opensdk.account.token.AccessToken) -> a
    42:42:com.kaolafm.opensdk.account.token.QQMusicAccessToken getToken() -> e
    47:55:void save(com.kaolafm.opensdk.account.token.QQMusicAccessToken) -> a
    63:70:com.kaolafm.opensdk.account.token.QQMusicAccessToken load() -> f
    75:77:void clear() -> c
    81:83:void logout() -> d
    15:15:java.lang.Object load() -> b
    15:15:void save(java.lang.Object) -> a
    15:15:java.lang.Object getToken() -> a
com.kaolafm.opensdk.account.token.QQMusicAccessTokenCache_Factory -> com.kaolafm.opensdk.account.token.j:
    javax.inject.Provider applicationProvider -> a
    javax.inject.Provider mGsonLazyProvider -> b
    javax.inject.Provider mAccessTokenProvider -> c
    20:24:void <init>(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    28:28:com.kaolafm.opensdk.account.token.QQMusicAccessTokenCache get() -> get
    35:40:com.kaolafm.opensdk.account.token.QQMusicAccessTokenCache provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> a
    47:47:com.kaolafm.opensdk.account.token.QQMusicAccessTokenCache_Factory create(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> create
    52:52:com.kaolafm.opensdk.account.token.QQMusicAccessTokenCache newQQMusicAccessTokenCache(android.app.Application) -> a
    10:10:java.lang.Object get() -> get
com.kaolafm.opensdk.account.token.QQMusicAccessTokenCache_MembersInjector -> com.kaolafm.opensdk.account.token.k:
    javax.inject.Provider mGsonLazyProvider -> a
    javax.inject.Provider mAccessTokenProvider -> b
    17:20:void <init>(javax.inject.Provider,javax.inject.Provider) -> <init>
    24:24:dagger.MembersInjector create(javax.inject.Provider,javax.inject.Provider) -> a
    29:31:void injectMembers(com.kaolafm.opensdk.account.token.QQMusicAccessTokenCache) -> a
    34:35:void injectMGsonLazy(com.kaolafm.opensdk.account.token.QQMusicAccessTokenCache,dagger.Lazy) -> a
    39:40:void injectMAccessToken(com.kaolafm.opensdk.account.token.QQMusicAccessTokenCache,com.kaolafm.opensdk.account.token.QQMusicAccessToken) -> a
    10:10:void injectMembers(java.lang.Object) -> a
com.kaolafm.opensdk.account.token.QQMusicAccessToken_Factory -> com.kaolafm.opensdk.account.token.l:
    com.kaolafm.opensdk.account.token.QQMusicAccessToken_Factory INSTANCE -> a
    6:6:void <init>() -> <init>
    11:11:com.kaolafm.opensdk.account.token.QQMusicAccessToken get() -> get
    15:15:com.kaolafm.opensdk.account.token.QQMusicAccessToken provideInstance() -> a
    19:19:com.kaolafm.opensdk.account.token.QQMusicAccessToken_Factory create() -> create
    23:23:com.kaolafm.opensdk.account.token.QQMusicAccessToken newQQMusicAccessToken() -> b
    6:6:java.lang.Object get() -> get
    7:7:void <clinit>() -> <clinit>
com.kaolafm.opensdk.api.BasePageResult -> com.kaolafm.opensdk.api.BasePageResult:
    int haveNext -> haveNext
    int nextPage -> nextPage
    int havePre -> havePre
    int prePage -> prePage
    int currentPage -> currentPage
    int count -> count
    int sumPage -> sumPage
    int pageSize -> pageSize
    java.lang.Object dataList -> dataList
    11:11:void <init>() -> <init>
    52:52:int getHaveNext() -> getHaveNext
    56:57:void setHaveNext(int) -> setHaveNext
    60:60:int getNextPage() -> getNextPage
    64:65:void setNextPage(int) -> setNextPage
    68:68:int getHavePre() -> getHavePre
    72:73:void setHavePre(int) -> setHavePre
    76:76:int getPrePage() -> getPrePage
    80:81:void setPrePage(int) -> setPrePage
    84:84:int getCurrentPage() -> getCurrentPage
    88:89:void setCurrentPage(int) -> setCurrentPage
    92:92:int getCount() -> getCount
    96:97:void setCount(int) -> setCount
    100:100:int getSumPage() -> getSumPage
    104:105:void setSumPage(int) -> setSumPage
    108:108:int getPageSize() -> getPageSize
    112:113:void setPageSize(int) -> setPageSize
    116:116:java.lang.Object getDataList() -> getDataList
    120:121:void setDataList(java.lang.Object) -> setDataList
com.kaolafm.opensdk.api.BaseRequest -> com.kaolafm.opensdk.api.BaseRequest:
    java.lang.String LIFECYCLE_TRANSFORMER_CLASS_NAME -> LIFECYCLE_TRANSFORMER_CLASS_NAME
    IRepositoryManager mRepositoryManager -> mRepositoryManager
    RetrofitUrlManager mRetrofitUrlManager -> mRetrofitUrlManager
    dagger.Lazy mProfileLazy -> mProfileLazy
    dagger.Lazy mGsonLazy -> mGsonLazy
    dagger.Lazy mAccessTokenManagerLazy -> mAccessTokenManagerLazy
    com.kaolafm.opensdk.di.component.RequestComponent mRequestComponent -> mRequestComponent
    com.trello.rxlifecycle2.LifecycleTransformer mLifecycleTransformer -> mLifecycleTransformer
    java.lang.Object mTag -> mTag
    74:77:void <init>() -> <init>
    80:80:java.lang.Object obtainRetrofitService(java.lang.Class) -> obtainRetrofitService
    84:87:void putNullParam(java.util.Map,java.lang.String,java.lang.Object) -> putNullParam
    95:96:com.kaolafm.opensdk.api.BaseRequest setTag(java.lang.Object) -> setTag
    103:104:void cancel(java.lang.Object) -> cancel
    110:115:void doHttpDeal(io.reactivex.Observable,io.reactivex.functions.Function,HttpCallback) -> doHttpDeal
    118:123:void doHttpDeal(io.reactivex.Observable,HttpCallback) -> doHttpDeal
    125:125:boolean canBindObservable() -> canBindObservable
    141:148:void doHttpDeal(io.reactivex.Single,io.reactivex.functions.Function,HttpCallback) -> doHttpDeal
    151:158:void doHttpDeal(io.reactivex.Single,HttpCallback) -> doHttpDeal
    164:164:boolean canBind() -> canBind
    171:171:java.lang.Object doHttpDealSync(retrofit2.Call) -> doHttpDealSync
    175:175:retrofit2.Response doHttpDealSyncResponse(retrofit2.Call) -> doHttpDealSyncResponse
    183:184:com.kaolafm.opensdk.api.BaseRequest bindLifecycle(com.trello.rxlifecycle2.LifecycleTransformer) -> bindLifecycle
com.kaolafm.opensdk.api.BaseRequest_MembersInjector -> com.kaolafm.opensdk.api.BaseRequest_MembersInjector:
    javax.inject.Provider mRepositoryManagerProvider -> mRepositoryManagerProvider
    javax.inject.Provider mRetrofitUrlManagerProvider -> mRetrofitUrlManagerProvider
    javax.inject.Provider mProfileLazyProvider -> mProfileLazyProvider
    javax.inject.Provider mGsonLazyProvider -> mGsonLazyProvider
    javax.inject.Provider mAccessTokenManagerLazyProvider -> mAccessTokenManagerLazyProvider
    30:36:void <init>(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    44:44:dagger.MembersInjector create(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> create
    54:59:void injectMembers(com.kaolafm.opensdk.api.BaseRequest) -> injectMembers
    63:64:void injectMRepositoryManager(com.kaolafm.opensdk.api.BaseRequest,IRepositoryManager) -> injectMRepositoryManager
    68:69:void injectMRetrofitUrlManager(com.kaolafm.opensdk.api.BaseRequest,RetrofitUrlManager) -> injectMRetrofitUrlManager
    72:73:void injectMProfileLazy(com.kaolafm.opensdk.api.BaseRequest,dagger.Lazy) -> injectMProfileLazy
    76:77:void injectMGsonLazy(com.kaolafm.opensdk.api.BaseRequest,dagger.Lazy) -> injectMGsonLazy
    81:82:void injectMAccessTokenManagerLazy(com.kaolafm.opensdk.api.BaseRequest,dagger.Lazy) -> injectMAccessTokenManagerLazy
    14:14:void injectMembers(java.lang.Object) -> injectMembers
com.kaolafm.opensdk.api.BaseResult -> com.kaolafm.opensdk.api.BaseResult:
    java.lang.String requestId -> requestId
    java.lang.String serverTime -> serverTime
    java.lang.Object result -> result
    12:12:void <init>() -> <init>
    22:22:java.lang.String getRequestId() -> getRequestId
    26:27:void setRequestId(java.lang.String) -> setRequestId
    30:30:java.lang.String getServerTime() -> getServerTime
    34:35:void setServerTime(java.lang.String) -> setServerTime
    38:38:java.lang.Object getResult() -> getResult
    42:43:void setResult(java.lang.Object) -> setResult
com.kaolafm.opensdk.api.ApiHostConstants -> com.kaolafm.opensdk.api.ApiHostConstants:
    java.lang.String BASE_HOST_URL_OPEN_KAOLA_FM -> BASE_HOST_URL_OPEN_KAOLA_FM
    java.lang.String DOMAIN_NAME_OPEN_KAOLA -> DOMAIN_NAME_OPEN_KAOLA
    java.lang.String DOMAIN_HEADER_OPEN_KAOLA -> DOMAIN_HEADER_OPEN_KAOLA
    java.lang.String BASE_HOST_URL_RECOMMEND -> BASE_HOST_URL_RECOMMEND
    java.lang.String BASE_HOST_URL_SUBSCRIBE -> BASE_HOST_URL_SUBSCRIBE
    java.lang.String DOMAIN_NAME_KRADIO_SUBSCRIBE -> DOMAIN_NAME_KRADIO_SUBSCRIBE
    java.lang.String DOMAIN_HEADER_KRADIO_SUBSCRIBE -> DOMAIN_HEADER_KRADIO_SUBSCRIBE
    java.lang.String BASE_HOST_URL_KRADIO_USER -> BASE_HOST_URL_KRADIO_USER
    java.lang.String DOMAIN_NAME_KRADIO_USER -> DOMAIN_NAME_KRADIO_USER
    java.lang.String DOMAIN_HEADER_KRADIO_USER -> DOMAIN_HEADER_KRADIO_USER
    java.lang.String DOMAIN_NAME_OPEN_HTTPS -> DOMAIN_NAME_OPEN_HTTPS
    java.lang.String DOMAIN_HEADER_OPEN_HTTPS -> DOMAIN_HEADER_OPEN_HTTPS
    java.lang.String BASE_HOST_USER_OPEN_KAOLA_FM_HTTPS -> BASE_HOST_USER_OPEN_KAOLA_FM_HTTPS
    java.lang.String BASE_HOST_SEARCH -> BASE_HOST_SEARCH
    java.lang.String DOMAIN_NAME_SEARCH -> DOMAIN_NAME_SEARCH
    java.lang.String DOMAIN_HEADER_SEARCH -> DOMAIN_HEADER_SEARCH
    java.lang.String WANGSU_UPLOAD_CALLBACK_URL -> WANGSU_UPLOAD_CALLBACK_URL
    java.lang.String RECORD_UPLOAD_HOST_WANGSU -> RECORD_UPLOAD_HOST_WANGSU
    java.lang.String QQMUSIC_HOST -> QQMUSIC_HOST
    java.lang.String QQMUSIC_DOMAIN_NAME -> QQMUSIC_DOMAIN_NAME
    java.lang.String QQMUSIC_DOMAIN_HEADER -> QQMUSIC_DOMAIN_HEADER
    java.lang.String DOMAIN_NAME_HTTPS_PROTOCOL -> DOMAIN_NAME_HTTPS_PROTOCOL
    java.lang.String DOMAIN_HEADER_HTTPS_PROTOCOL -> DOMAIN_HEADER_HTTPS_PROTOCOL
    11:11:void <init>() -> <init>
com.kaolafm.opensdk.api.KaolaApiConstant -> com.kaolafm.opensdk.api.KaolaApiConstant:
    java.lang.String SIGN -> SIGN
    java.lang.String OPEN_ID -> OPEN_ID
    java.lang.String APP_ID -> APP_ID
    java.lang.String APP_KEY -> APP_KEY
    java.lang.String DEVICE_ID -> DEVICE_ID
    java.lang.String OS -> OS
    java.lang.String CHANNEL -> CHANNEL
    java.lang.String PACKAGE_NAME -> PACKAGE_NAME
    java.lang.String OS_NAME -> OS_NAME
    java.lang.String USER_ID -> USER_ID
    java.lang.String VERSION -> VERSION
    java.lang.String SDK_VERSION -> SDK_VERSION
    java.lang.String LAT -> LAT
    java.lang.String LNG -> LNG
    java.lang.String TOKEN -> TOKEN
    java.lang.String UDID -> UDID
    java.lang.String OPEN_UID -> OPEN_UID
    java.lang.String ACCESS_TOKEN -> ACCESS_TOKEN
    java.lang.String TIMESTAMP -> TIMESTAMP
    java.lang.String NONCE -> NONCE
    java.lang.String KRADIO_USER_ID -> KRADIO_USER_ID
    java.lang.String OSVERSION -> OSVERSION
    java.lang.String DEVICE_NAME -> DEVICE_NAME
    java.lang.String RESOLUTION -> RESOLUTION
    java.lang.String SCREEN_SIZE -> SCREEN_SIZE
    java.lang.String IMEI -> IMEI
    java.lang.String DEVICE_TYPE -> DEVICE_TYPE
    java.lang.String USER_PATH -> USER_PATH
    java.lang.String KAOLA_VERSION -> KAOLA_VERSION
    java.lang.String SWITCH_USER_PATH -> SWITCH_USER_PATH
    java.lang.String REQUEST_K_RADIO_ACTIVATE_PAGE -> REQUEST_K_RADIO_ACTIVATE_PAGE
    java.lang.String REQUEST_K_RADIO_ACTIVATION -> REQUEST_K_RADIO_ACTIVATION
    java.lang.String REQUEST_K_RADIO_ACTIVATION_INIT -> REQUEST_K_RADIO_ACTIVATION_INIT
    java.lang.String REQUEST_K_RADIO_REFRESH_TOKEN -> REQUEST_K_RADIO_REFRESH_TOKEN
    java.lang.String REQUEST_K_RADIO_REGISTER -> REQUEST_K_RADIO_REGISTER
    java.lang.String REQUEST_K_RADIO_VALIDATE_CODE -> REQUEST_K_RADIO_VALIDATE_CODE
    java.lang.String REQUEST_K_RADIO_PHONE_IS_REGISTERED -> REQUEST_K_RADIO_PHONE_IS_REGISTERED
    java.lang.String REQUEST_K_RADIO_LOGIN -> REQUEST_K_RADIO_LOGIN
    java.lang.String REQUEST_K_RADIO_LOGOUT -> REQUEST_K_RADIO_LOGOUT
    java.lang.String GET_BRAND_INFO -> GET_BRAND_INFO
    java.lang.String REQUEST_KAOLA_INIT -> REQUEST_KAOLA_INIT
    java.lang.String REQUEST_KAOLA_ACTIVATE -> REQUEST_KAOLA_ACTIVATE
    java.lang.String REQUEST_KAOLA_WX_CONNECT -> REQUEST_KAOLA_WX_CONNECT
    java.lang.String OPERATION_PATH -> OPERATION_PATH
    java.lang.String GET_CATEGORY_TREE -> GET_CATEGORY_TREE
    java.lang.String GET_CATEGORY_ROOT -> GET_CATEGORY_ROOT
    java.lang.String GET_SUBCATEGORY_LIST -> GET_SUBCATEGORY_LIST
    java.lang.String GET_CATEGORY_MEMBER_NUM -> GET_CATEGORY_MEMBER_NUM
    java.lang.String GET_CATEGORY_MEMBER_LIST_NEW -> GET_CATEGORY_MEMBER_LIST_NEW
    java.lang.String GET_COLUMN_TREE -> GET_COLUMN_TREE
    java.lang.String GET_SUBCOLUMN_LIST -> GET_SUBCOLUMN_LIST
    java.lang.String GET_COLUMN_MEMBER_LIST -> GET_COLUMN_MEMBER_LIST
    java.lang.String GET_COLUMN_LIST -> GET_COLUMN_LIST
    java.lang.String GET_CATEGORY_LIST -> GET_CATEGORY_LIST
    java.lang.String GET_CATEGORY_INFO -> GET_CATEGORY_INFO
    java.lang.String GET_CATEGORY_MEMBER_LIST -> GET_CATEGORY_MEMBER_LIST
    java.lang.String SEARCH_BY_SEMANTICS -> SEARCH_BY_SEMANTICS
    java.lang.String SEARCH_ALL -> SEARCH_ALL
    java.lang.String SEARCH_BY_TYPE -> SEARCH_BY_TYPE
    java.lang.String GET_SUGGESTED_WORD -> GET_SUGGESTED_WORD
    java.lang.String GET_ALBUM_DETAILS -> GET_ALBUM_DETAILS
    java.lang.String GET_RADIO_DETAILS -> GET_RADIO_DETAILS
    java.lang.String GET_RADIO_LIST -> GET_RADIO_LIST
    java.lang.String GET_AUDIO_DETAILS_MULTIPLE -> GET_AUDIO_DETAILS_MULTIPLE
    java.lang.String GET_AUDIO_DETAILS_SINGLE -> GET_AUDIO_DETAILS_SINGLE
    java.lang.String GET_AUDIO_LIST -> GET_AUDIO_LIST
    java.lang.String GET_CURRENT_CLOCK_AUDIO -> GET_CURRENT_CLOCK_AUDIO
    java.lang.String GET_BROADCAST_LIST -> GET_BROADCAST_LIST
    java.lang.String GET_BROADCAST_DETAILS -> GET_BROADCAST_DETAILS
    java.lang.String GET_BROADCAST_PROGRAM_LIST -> GET_BROADCAST_PROGRAM_LIST
    java.lang.String GET_BROADCAST_PROGRAM_DETAILS -> GET_BROADCAST_PROGRAM_DETAILS
    java.lang.String GET_BROADCAST_CURRENT_PROGRAM -> GET_BROADCAST_CURRENT_PROGRAM
    java.lang.String GET_BROADCAST_AREA_LIST -> GET_BROADCAST_AREA_LIST
    java.lang.String GET_BROADCAST_AREA -> GET_BROADCAST_AREA
    java.lang.String GET_CITY_BROADCAST_LIST_TOP -> GET_CITY_BROADCAST_LIST_TOP
    java.lang.String GET_QR_CODE -> GET_QR_CODE
    java.lang.String CHECK_QR_STATUS -> CHECK_QR_STATUS
    java.lang.String FETCH_CODE -> FETCH_CODE
    java.lang.String GET_TOKEN_AND_BIND -> GET_TOKEN_AND_BIND
    java.lang.String REFRESH_TOKEN -> REFRESH_TOKEN
    java.lang.String GET_USER_INFO -> GET_USER_INFO
    java.lang.String REVOKE_AUTHORIZATION -> REVOKE_AUTHORIZATION
    java.lang.String KRADIO_PATH -> KRADIO_PATH
    java.lang.String PATH -> PATH
    java.lang.String KRADIO_GET_SUBSCRIBE_LIST -> KRADIO_GET_SUBSCRIBE_LIST
    java.lang.String KRADIO_SUBSCRIBE -> KRADIO_SUBSCRIBE
    java.lang.String KRADIO_UNSUBSCRIBE -> KRADIO_UNSUBSCRIBE
    java.lang.String KRADIO_CHECK_IS_SUBSCRIBE -> KRADIO_CHECK_IS_SUBSCRIBE
    java.lang.String KRADIO_GET_USER_FOLLOW_RADIO -> KRADIO_GET_USER_FOLLOW_RADIO
    java.lang.String GET_SUBSCRIBE_LIST -> GET_SUBSCRIBE_LIST
    java.lang.String SUBSCRIBE -> SUBSCRIBE
    java.lang.String UNSUBSCRIBE -> UNSUBSCRIBE
    java.lang.String CHECK_IS_SUBSCRIBE -> CHECK_IS_SUBSCRIBE
    java.lang.String GET_USER_FOLLOW_RADIO -> GET_USER_FOLLOW_RADIO
    java.lang.String REQUEST_KAOLA_LIVE_INFO -> REQUEST_KAOLA_LIVE_INFO
    java.lang.String REQUEST_KAOLA_CHAT_ROOM_TOKEN -> REQUEST_KAOLA_CHAT_ROOM_TOKEN
    java.lang.String GET_CHAT_ROOM_TOKEN_BY_ID -> GET_CHAT_ROOM_TOKEN_BY_ID
    java.lang.String LINK_ACCOUNT -> LINK_ACCOUNT
    java.lang.String GET_SCENE_INFO -> GET_SCENE_INFO
    java.lang.String KRADIO_SAVE_LISTENING_HISTORY -> KRADIO_SAVE_LISTENING_HISTORY
    java.lang.String KRADIO_GET_HISTORY_LIST -> KRADIO_GET_HISTORY_LIST
    java.lang.String KRADIO_CLEAR_LISTENING_HISTORY -> KRADIO_CLEAR_LISTENING_HISTORY
    java.lang.String SAVE_LISTENING_HISTORY -> SAVE_LISTENING_HISTORY
    java.lang.String GET_HISTORY_LIST -> GET_HISTORY_LIST
    java.lang.String CLEAR_LISTENING_HISTORY -> CLEAR_LISTENING_HISTORY
    java.lang.String RECOMMEND -> RECOMMEND
    java.lang.String AUTHUSER -> AUTHUSER
    java.lang.String GET_INTEREST_TAG_LIST_UNLOGINED -> GET_INTEREST_TAG_LIST_UNLOGINED
    java.lang.String GET_INTEREST_TAG_LIST_LOGINED -> GET_INTEREST_TAG_LIST_LOGINED
    java.lang.String SAVE_USER_ATTRIBUTE -> SAVE_USER_ATTRIBUTE
    java.lang.String SAVE_INTEREST_TAG -> SAVE_INTEREST_TAG
    java.lang.String SAVE_THIRD_USER -> SAVE_THIRD_USER
    java.lang.String SAVE_DEVICE_INFO -> SAVE_DEVICE_INFO
    9:9:void <init>() -> <init>
com.kaolafm.opensdk.api.brand.BrandRequest -> com.kaolafm.opensdk.api.brand.BrandRequest:
    com.kaolafm.opensdk.api.brand.BrandService mBrandService -> mBrandService
    17:19:void <init>() -> <init>
    25:26:void getBrandInfo(HttpCallback) -> getBrandInfo
com.kaolafm.opensdk.api.brand.BrandService -> com.kaolafm.opensdk.api.brand.a:
    io.reactivex.Single getBrandInfo() -> getBrandInfo
com.kaolafm.opensdk.api.brand.model.BrandDetails -> com.kaolafm.opensdk.api.brand.model.BrandDetails:
    java.lang.String brand -> brand
    java.lang.String logo -> logo
    java.lang.String userAgreement -> userAgreement
    android.os.Parcelable$Creator CREATOR -> CREATOR
    26:26:java.lang.String getBrand() -> getBrand
    30:30:java.lang.String getLogo() -> getLogo
    34:34:java.lang.String getUserAgreement() -> getUserAgreement
    38:39:void setBrand(java.lang.String) -> setBrand
    42:43:void setLogo(java.lang.String) -> setLogo
    46:47:void setUserAgreement(java.lang.String) -> setUserAgreement
    49:53:void <init>(android.os.Parcel) -> <init>
    69:69:int describeContents() -> describeContents
    74:77:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    55:55:void <clinit>() -> <clinit>
com.kaolafm.opensdk.api.brand.model.BrandDetails$1 -> com.kaolafm.opensdk.api.brand.model.a:
    55:55:void <init>() -> <init>
    58:58:com.kaolafm.opensdk.api.brand.model.BrandDetails createFromParcel(android.os.Parcel) -> createFromParcel
    63:63:com.kaolafm.opensdk.api.brand.model.BrandDetails[] newArray(int) -> newArray
    55:55:java.lang.Object[] newArray(int) -> newArray
    55:55:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
com.kaolafm.opensdk.api.broadcast.BroadcastArea -> com.kaolafm.opensdk.api.broadcast.BroadcastArea:
    int id -> id
    java.lang.String name -> name
    android.os.Parcelable$Creator CREATOR -> CREATOR
    43:43:java.lang.String getName() -> getName
    47:48:void setName(java.lang.String) -> setName
    51:51:int getId() -> getId
    55:56:void setId(int) -> setId
    61:61:int describeContents() -> describeContents
    66:68:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    70:71:void <init>() -> <init>
    73:76:void <init>(android.os.Parcel) -> <init>
    78:78:void <clinit>() -> <clinit>
com.kaolafm.opensdk.api.broadcast.BroadcastArea$1 -> com.kaolafm.opensdk.api.broadcast.a:
    78:78:void <init>() -> <init>
    81:81:com.kaolafm.opensdk.api.broadcast.BroadcastArea createFromParcel(android.os.Parcel) -> createFromParcel
    86:86:com.kaolafm.opensdk.api.broadcast.BroadcastArea[] newArray(int) -> newArray
    78:78:java.lang.Object[] newArray(int) -> newArray
    78:78:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
com.kaolafm.opensdk.api.broadcast.BroadcastDetails -> com.kaolafm.opensdk.api.broadcast.BroadcastDetails:
    long broadcastId -> broadcastId
    java.lang.String name -> name
    java.lang.String img -> img
    java.lang.String classifyName -> classifyName
    int isSubscribe -> isSubscribe
    java.lang.String playUrl -> playUrl
    int onLineNum -> onLineNum
    int likedNum -> likedNum
    int status -> status
    int classifyId -> classifyId
    int roomId -> roomId
    java.lang.String freq -> freq
    java.lang.String icon -> icon
    android.os.Parcelable$Creator CREATOR -> CREATOR
    78:78:long getBroadcastId() -> getBroadcastId
    82:83:void setBroadcastId(long) -> setBroadcastId
    86:86:java.lang.String getName() -> getName
    90:91:void setName(java.lang.String) -> setName
    94:94:java.lang.String getImg() -> getImg
    98:99:void setImg(java.lang.String) -> setImg
    102:102:java.lang.String getClassifyName() -> getClassifyName
    106:107:void setClassifyName(java.lang.String) -> setClassifyName
    110:110:int getIsSubscribe() -> getIsSubscribe
    114:115:void setIsSubscribe(int) -> setIsSubscribe
    118:118:java.lang.String getPlayUrl() -> getPlayUrl
    122:123:void setPlayUrl(java.lang.String) -> setPlayUrl
    126:126:int getOnLineNum() -> getOnLineNum
    130:131:void setOnLineNum(int) -> setOnLineNum
    134:134:int getLikedNum() -> getLikedNum
    138:139:void setLikedNum(int) -> setLikedNum
    142:142:int getStatus() -> getStatus
    146:147:void setStatus(int) -> setStatus
    150:150:int getClassifyId() -> getClassifyId
    154:155:void setClassifyId(int) -> setClassifyId
    158:158:int getRoomId() -> getRoomId
    162:163:void setRoomId(int) -> setRoomId
    166:166:java.lang.String getFreq() -> getFreq
    170:171:void setFreq(java.lang.String) -> setFreq
    174:174:java.lang.String getIcon() -> getIcon
    178:179:void setIcon(java.lang.String) -> setIcon
    184:184:int describeContents() -> describeContents
    189:202:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    204:205:void <init>() -> <init>
    207:221:void <init>(android.os.Parcel) -> <init>
    223:223:void <clinit>() -> <clinit>
com.kaolafm.opensdk.api.broadcast.BroadcastDetails$1 -> com.kaolafm.opensdk.api.broadcast.b:
    223:223:void <init>() -> <init>
    226:226:com.kaolafm.opensdk.api.broadcast.BroadcastDetails createFromParcel(android.os.Parcel) -> createFromParcel
    231:231:com.kaolafm.opensdk.api.broadcast.BroadcastDetails[] newArray(int) -> newArray
    223:223:java.lang.Object[] newArray(int) -> newArray
    223:223:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
com.kaolafm.opensdk.api.broadcast.BroadcastRequest -> com.kaolafm.opensdk.api.broadcast.BroadcastRequest:
    com.kaolafm.opensdk.api.broadcast.BroadcastService mService -> mService
    33:35:void <init>() -> <init>
    44:45:void getBroadcastDetails(long,HttpCallback) -> getBroadcastDetails
    56:57:void getBroadcastProgramList(long,java.lang.String,HttpCallback) -> getBroadcastProgramList
    67:69:void getBroadcastProgramDetails(long,HttpCallback) -> getBroadcastProgramDetails
    78:79:void getBroadcastCurrentProgramDetails(long,HttpCallback) -> getBroadcastCurrentProgramDetails
com.kaolafm.opensdk.api.broadcast.BroadcastService -> com.kaolafm.opensdk.api.broadcast.c:
    io.reactivex.Single getBroadcastList(int,int,int,int,int) -> getBroadcastList
    io.reactivex.Single getBroadcastDetails(long) -> getBroadcastDetails
    io.reactivex.Single getBroadcastProgramList(long,java.lang.String) -> getBroadcastProgramList
    io.reactivex.Single getBroadcastProgramDetails(long) -> getBroadcastProgramDetails
    io.reactivex.Single getBroadcastCurrentProgramDetails(long) -> getBroadcastCurrentProgramDetails
    io.reactivex.Single getBroadcastAreaList() -> getBroadcastAreaList
    io.reactivex.Single getBroadcastArea(float,float) -> getBroadcastArea
com.kaolafm.opensdk.api.broadcast.ProgramDetails -> com.kaolafm.opensdk.api.broadcast.ProgramDetails:
    int programId -> programId
    long broadcastId -> broadcastId
    int nextProgramId -> nextProgramId
    int preProgramId -> preProgramId
    java.lang.String title -> title
    java.lang.String backLiveUrl -> backLiveUrl
    java.lang.String playUrl -> playUrl
    java.lang.String comperes -> comperes
    java.lang.String beginTime -> beginTime
    java.lang.String endTime -> endTime
    long startTime -> startTime
    long finishTime -> finishTime
    int status -> status
    int isSubscribe -> isSubscribe
    java.lang.String desc -> desc
    java.lang.String broadcastDesc -> broadcastDesc
    java.lang.String broadcastName -> broadcastName
    java.lang.String broadcastImg -> broadcastImg
    java.lang.String icon -> icon
    android.os.Parcelable$Creator CREATOR -> CREATOR
    127:127:int getProgramId() -> getProgramId
    131:132:void setProgramId(int) -> setProgramId
    135:135:long getBroadcastId() -> getBroadcastId
    139:140:void setBroadcastId(long) -> setBroadcastId
    143:143:int getNextProgramId() -> getNextProgramId
    147:148:void setNextProgramId(int) -> setNextProgramId
    151:151:int getPreProgramId() -> getPreProgramId
    155:156:void setPreProgramId(int) -> setPreProgramId
    159:159:java.lang.String getTitle() -> getTitle
    163:164:void setTitle(java.lang.String) -> setTitle
    167:167:java.lang.String getBackLiveUrl() -> getBackLiveUrl
    171:172:void setBackLiveUrl(java.lang.String) -> setBackLiveUrl
    175:175:java.lang.String getPlayUrl() -> getPlayUrl
    179:180:void setPlayUrl(java.lang.String) -> setPlayUrl
    183:183:java.lang.String getComperes() -> getComperes
    187:188:void setComperes(java.lang.String) -> setComperes
    191:191:java.lang.String getBeginTime() -> getBeginTime
    195:196:void setBeginTime(java.lang.String) -> setBeginTime
    199:199:java.lang.String getEndTime() -> getEndTime
    203:204:void setEndTime(java.lang.String) -> setEndTime
    207:207:long getStartTime() -> getStartTime
    211:212:void setStartTime(long) -> setStartTime
    215:215:long getFinishTime() -> getFinishTime
    219:220:void setFinishTime(long) -> setFinishTime
    223:223:int getStatus() -> getStatus
    227:228:void setStatus(int) -> setStatus
    231:231:int getIsSubscribe() -> getIsSubscribe
    235:236:void setIsSubscribe(int) -> setIsSubscribe
    239:239:java.lang.String getDesc() -> getDesc
    243:244:void setDesc(java.lang.String) -> setDesc
    247:247:java.lang.String getBroadcastDesc() -> getBroadcastDesc
    251:252:void setBroadcastDesc(java.lang.String) -> setBroadcastDesc
    255:255:java.lang.String getBroadcastName() -> getBroadcastName
    259:260:void setBroadcastName(java.lang.String) -> setBroadcastName
    263:263:java.lang.String getBroadcastImg() -> getBroadcastImg
    267:268:void setBroadcastImg(java.lang.String) -> setBroadcastImg
    271:271:java.lang.String getIcon() -> getIcon
    275:276:void setIcon(java.lang.String) -> setIcon
    281:281:int describeContents() -> describeContents
    286:305:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    307:308:void <init>() -> <init>
    310:330:void <init>(android.os.Parcel) -> <init>
    332:332:void <clinit>() -> <clinit>
com.kaolafm.opensdk.api.broadcast.ProgramDetails$1 -> com.kaolafm.opensdk.api.broadcast.d:
    332:332:void <init>() -> <init>
    335:335:com.kaolafm.opensdk.api.broadcast.ProgramDetails createFromParcel(android.os.Parcel) -> createFromParcel
    340:340:com.kaolafm.opensdk.api.broadcast.ProgramDetails[] newArray(int) -> newArray
    332:332:java.lang.Object[] newArray(int) -> newArray
    332:332:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
com.kaolafm.opensdk.api.history.HistoryRequest -> com.kaolafm.opensdk.api.history.HistoryRequest:
    com.kaolafm.opensdk.api.history.HistoryService mHistoryService -> mHistoryService
    int STATUS_FAIL -> STATUS_FAIL
    int STATUS_SUCCESS -> STATUS_SUCCESS
    23:25:void <init>() -> <init>
    33:42:void getHistoryList(HttpCallback) -> getHistoryList
    58:70:void saveListeningHistory(java.lang.String,long,long,long,long,long,HttpCallback) -> saveListeningHistory
    78:83:void clearListeningHistory(HttpCallback) -> clearListeningHistory
    80:81:java.lang.Boolean lambda$clearListeningHistory$2(com.kaolafm.opensdk.api.BaseResult) -> lambda$clearListeningHistory$2
    67:68:java.lang.Boolean lambda$saveListeningHistory$1(com.kaolafm.opensdk.api.BaseResult) -> lambda$saveListeningHistory$1
    36:40:java.util.List lambda$getHistoryList$0(com.kaolafm.opensdk.api.BaseResult) -> lambda$getHistoryList$0
com.kaolafm.opensdk.api.history.HistoryService -> com.kaolafm.opensdk.api.history.a:
    io.reactivex.Single getHistoryList() -> getHistoryList
    io.reactivex.Single saveListeningHistory(java.util.Map) -> saveListeningHistory
    io.reactivex.Single clearListeningHistory() -> clearListeningHistory
com.kaolafm.opensdk.api.history.model.ListeningHistory -> com.kaolafm.opensdk.api.history.model.ListeningHistory:
    java.lang.String audioId -> audioId
    java.lang.String audioTitle -> audioTitle
    long createTime -> createTime
    int duration -> duration
    int orderNum -> orderNum
    java.lang.String picUrl -> picUrl
    java.lang.String playUrl -> playUrl
    long playedTime -> playedTime
    java.lang.String radioId -> radioId
    java.lang.String radioTitle -> radioTitle
    java.lang.String shareUrl -> shareUrl
    int status -> status
    int type -> type
    long updateTime -> updateTime
    android.os.Parcelable$Creator CREATOR -> CREATOR
    72:73:void <init>() -> <init>
    76:76:java.lang.String getAudioId() -> getAudioId
    80:80:java.lang.String getAudioTitle() -> getAudioTitle
    84:84:long getCreateTime() -> getCreateTime
    88:88:int getDuration() -> getDuration
    92:92:int getOrderNum() -> getOrderNum
    96:96:java.lang.String getPicUrl() -> getPicUrl
    100:100:java.lang.String getPlayUrl() -> getPlayUrl
    104:104:long getPlayedTime() -> getPlayedTime
    108:108:java.lang.String getRadioId() -> getRadioId
    112:112:java.lang.String getRadioTitle() -> getRadioTitle
    116:116:java.lang.String getShareUrl() -> getShareUrl
    120:120:int getStatus() -> getStatus
    124:124:int getType() -> getType
    128:128:long getUpdateTime() -> getUpdateTime
    132:133:void setAudioId(java.lang.String) -> setAudioId
    136:137:void setAudioTitle(java.lang.String) -> setAudioTitle
    140:141:void setCreateTime(long) -> setCreateTime
    144:145:void setDuration(int) -> setDuration
    148:149:void setOrderNum(int) -> setOrderNum
    152:153:void setPicUrl(java.lang.String) -> setPicUrl
    156:157:void setPlayUrl(java.lang.String) -> setPlayUrl
    160:161:void setPlayedTime(long) -> setPlayedTime
    164:165:void setRadioId(java.lang.String) -> setRadioId
    168:169:void setRadioTitle(java.lang.String) -> setRadioTitle
    172:173:void setShareUrl(java.lang.String) -> setShareUrl
    176:177:void setStatus(int) -> setStatus
    180:181:void setType(int) -> setType
    184:185:void setUpdateTime(long) -> setUpdateTime
    187:202:void <init>(android.os.Parcel) -> <init>
    206:220:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    224:224:int describeContents() -> describeContents
    227:227:void <clinit>() -> <clinit>
com.kaolafm.opensdk.api.history.model.ListeningHistory$1 -> com.kaolafm.opensdk.api.history.model.a:
    227:227:void <init>() -> <init>
    230:230:com.kaolafm.opensdk.api.history.model.ListeningHistory createFromParcel(android.os.Parcel) -> createFromParcel
    235:235:com.kaolafm.opensdk.api.history.model.ListeningHistory[] newArray(int) -> newArray
    227:227:java.lang.Object[] newArray(int) -> newArray
    227:227:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
com.kaolafm.opensdk.api.history.model.SyncHistoryStatus -> com.kaolafm.opensdk.api.history.model.SyncHistoryStatus:
    int status -> status
    7:7:void <init>() -> <init>
    12:12:int getStatus() -> getStatus
    16:17:void setStatus(int) -> setStatus
com.kaolafm.opensdk.api.init.InitRequest -> com.kaolafm.opensdk.api.init.InitRequest:
    int ERROR_CODE_REPEAT_ACTIVATION -> ERROR_CODE_REPEAT_ACTIVATION
    javax.inject.Provider mKaolaInitParams -> mKaolaInitParams
    com.kaolafm.opensdk.api.init.InitService mInitService -> mInitService
    38:42:void <init>() -> <init>
    49:60:void init(HttpCallback) -> init
    67:78:void activate(HttpCallback) -> activate
    86:107:void activateOrInit(HttpCallback) -> activateOrInit
    110:110:io.reactivex.Single activateOrInit() -> activateOrInit
    135:145:void initKaola(HttpCallback) -> initKaola
    154:165:void activeKaola(HttpCallback) -> activeKaola
    172:172:io.reactivex.Single activateOrInitKaola() -> activateOrInitKaola
    195:196:void activateOrInitKaola(HttpCallback) -> activateOrInitKaola
    199:214:void initKaola(io.reactivex.SingleEmitter) -> initKaola
    221:228:void activateAll(HttpCallback) -> activateAll
    231:235:void onSuccess(io.reactivex.SingleEmitter,java.lang.Object) -> onSuccess
    238:241:void onError(io.reactivex.SingleEmitter,ApiException) -> onError
    244:248:void setCarInfoToReport(com.kaolafm.opensdk.api.init.model.KaolaActivateData) -> setCarInfoToReport
    223:225:com.kaolafm.opensdk.account.token.KaolaAccessToken lambda$activateAll$6(java.lang.String,java.lang.String) -> lambda$activateAll$6
    172:172:void lambda$activateOrInitKaola$5(io.reactivex.SingleEmitter) -> lambda$activateOrInitKaola$5
    156:163:java.lang.String lambda$activeKaola$4(com.kaolafm.opensdk.api.BaseResult) -> lambda$activeKaola$4
    136:143:java.lang.String lambda$initKaola$3(com.kaolafm.opensdk.api.BaseResult) -> lambda$initKaola$3
    110:110:void lambda$activateOrInit$2(io.reactivex.SingleEmitter) -> lambda$activateOrInit$2
    68:76:java.lang.String lambda$activate$1(com.kaolafm.opensdk.api.BaseResult) -> lambda$activate$1
    50:58:java.lang.String lambda$init$0(com.kaolafm.opensdk.api.BaseResult) -> lambda$init$0
    23:23:void access$000(com.kaolafm.opensdk.api.init.InitRequest,io.reactivex.SingleEmitter,java.lang.Object) -> access$000
    23:23:void access$100(com.kaolafm.opensdk.api.init.InitRequest,io.reactivex.SingleEmitter,ApiException) -> access$100
    23:23:void access$200(com.kaolafm.opensdk.api.init.InitRequest,io.reactivex.SingleEmitter) -> access$200
com.kaolafm.opensdk.api.init.InitRequest$1 -> com.kaolafm.opensdk.api.init.InitRequest$1:
    HttpCallback val$callback -> val$callback
    com.kaolafm.opensdk.api.init.InitRequest this$0 -> this$0
    86:86:void <init>(com.kaolafm.opensdk.api.init.InitRequest,HttpCallback) -> <init>
    89:95:void onSuccess(java.lang.String) -> onSuccess
    100:105:void onError(ApiException) -> onError
    86:86:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.api.init.InitRequest$2 -> com.kaolafm.opensdk.api.init.InitRequest$2:
    io.reactivex.SingleEmitter val$emitter -> val$emitter
    com.kaolafm.opensdk.api.init.InitRequest this$0 -> this$0
    110:110:void <init>(com.kaolafm.opensdk.api.init.InitRequest,io.reactivex.SingleEmitter) -> <init>
    113:120:void onSuccess(java.lang.String) -> onSuccess
    124:125:void onError(ApiException) -> onError
    110:110:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.api.init.InitRequest$3 -> com.kaolafm.opensdk.api.init.InitRequest$3:
    io.reactivex.SingleEmitter val$emitter -> val$emitter
    com.kaolafm.opensdk.api.init.InitRequest this$0 -> this$0
    172:172:void <init>(com.kaolafm.opensdk.api.init.InitRequest,io.reactivex.SingleEmitter) -> <init>
    175:180:void onSuccess(java.lang.String) -> onSuccess
    185:190:void onError(ApiException) -> onError
    172:172:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.api.init.InitRequest$4 -> com.kaolafm.opensdk.api.init.InitRequest$4:
    io.reactivex.SingleEmitter val$emitter -> val$emitter
    com.kaolafm.opensdk.api.init.InitRequest this$0 -> this$0
    199:199:void <init>(com.kaolafm.opensdk.api.init.InitRequest,io.reactivex.SingleEmitter) -> <init>
    202:207:void onSuccess(java.lang.String) -> onSuccess
    211:212:void onError(ApiException) -> onError
    199:199:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.api.init.InitRequest_Factory -> com.kaolafm.opensdk.api.init.InitRequest_Factory:
    javax.inject.Provider mRepositoryManagerProvider -> mRepositoryManagerProvider
    javax.inject.Provider mRetrofitUrlManagerProvider -> mRetrofitUrlManagerProvider
    javax.inject.Provider mProfileLazyProvider -> mProfileLazyProvider
    javax.inject.Provider mGsonLazyProvider -> mGsonLazyProvider
    javax.inject.Provider mAccessTokenManagerLazyProvider -> mAccessTokenManagerLazyProvider
    javax.inject.Provider mKaolaInitParamsProvider -> mKaolaInitParamsProvider
    34:41:void <init>(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    45:45:com.kaolafm.opensdk.api.init.InitRequest get() -> get
    61:72:com.kaolafm.opensdk.api.init.InitRequest provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> provideInstance
    82:82:com.kaolafm.opensdk.api.init.InitRequest_Factory create(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> create
    92:92:com.kaolafm.opensdk.api.init.InitRequest newInitRequest() -> newInitRequest
    15:15:java.lang.Object get() -> get
com.kaolafm.opensdk.api.init.InitRequest_MembersInjector -> com.kaolafm.opensdk.api.init.InitRequest_MembersInjector:
    javax.inject.Provider mRepositoryManagerProvider -> mRepositoryManagerProvider
    javax.inject.Provider mRetrofitUrlManagerProvider -> mRetrofitUrlManagerProvider
    javax.inject.Provider mProfileLazyProvider -> mProfileLazyProvider
    javax.inject.Provider mGsonLazyProvider -> mGsonLazyProvider
    javax.inject.Provider mAccessTokenManagerLazyProvider -> mAccessTokenManagerLazyProvider
    javax.inject.Provider mKaolaInitParamsProvider -> mKaolaInitParamsProvider
    34:41:void <init>(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    50:50:dagger.MembersInjector create(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> create
    61:71:void injectMembers(com.kaolafm.opensdk.api.init.InitRequest) -> injectMembers
    75:76:void injectMKaolaInitParams(com.kaolafm.opensdk.api.init.InitRequest,javax.inject.Provider) -> injectMKaolaInitParams
    15:15:void injectMembers(java.lang.Object) -> injectMembers
com.kaolafm.opensdk.api.init.InitService -> com.kaolafm.opensdk.api.init.a:
    io.reactivex.Single init() -> init
    retrofit2.Call initSync() -> initSync
    io.reactivex.Single activate() -> activate
    retrofit2.Call activateSync() -> activateSync
    io.reactivex.Single initKaola() -> initKaola
    retrofit2.Call initKaolaSync() -> initKaolaSync
    io.reactivex.Single activeKaola(java.util.Map) -> activeKaola
    retrofit2.Call activeKaolaSync(java.util.Map) -> activeKaolaSync
com.kaolafm.opensdk.api.init.model.KaolaActivateData -> com.kaolafm.opensdk.api.init.model.KaolaActivateData:
    com.kaolafm.opensdk.api.init.model.KaolaActivateData$CarConfigBean carConfig -> carConfig
    java.lang.String openid -> openid
    com.kaolafm.opensdk.api.init.model.KaolaActivateData$CarInfoBean carInfo -> carInfo
    7:7:void <init>() -> <init>
    20:20:com.kaolafm.opensdk.api.init.model.KaolaActivateData$CarConfigBean getCarConfig() -> getCarConfig
    24:25:void setCarConfig(com.kaolafm.opensdk.api.init.model.KaolaActivateData$CarConfigBean) -> setCarConfig
    28:28:java.lang.String getOpenid() -> getOpenid
    32:33:void setOpenid(java.lang.String) -> setOpenid
    36:36:com.kaolafm.opensdk.api.init.model.KaolaActivateData$CarInfoBean getCarInfo() -> getCarInfo
    40:41:void setCarInfo(com.kaolafm.opensdk.api.init.model.KaolaActivateData$CarInfoBean) -> setCarInfo
com.kaolafm.opensdk.api.init.model.KaolaActivateData$CarConfigBean -> com.kaolafm.opensdk.api.init.model.KaolaActivateData$CarConfigBean:
    int reportInterval -> reportInterval
    43:43:void <init>() -> <init>
    51:51:int getReportInterval() -> getReportInterval
    55:56:void setReportInterval(int) -> setReportInterval
com.kaolafm.opensdk.api.init.model.KaolaActivateData$CarInfoBean -> com.kaolafm.opensdk.api.init.model.KaolaActivateData$CarInfoBean:
    java.lang.String firstAppId -> firstAppId
    java.lang.String marketType -> marketType
    java.lang.String firstAppIdName -> firstAppIdName
    java.lang.String appIdType -> appIdType
    java.lang.String oem -> oem
    java.lang.String carBrand -> carBrand
    java.lang.String carType -> carType
    java.lang.String developer -> developer
    59:59:void <init>() -> <init>
    83:83:java.lang.String getFirstAppId() -> getFirstAppId
    87:88:void setFirstAppId(java.lang.String) -> setFirstAppId
    91:91:java.lang.String getMarketType() -> getMarketType
    95:96:void setMarketType(java.lang.String) -> setMarketType
    99:99:java.lang.String getFirstAppIdName() -> getFirstAppIdName
    103:104:void setFirstAppIdName(java.lang.String) -> setFirstAppIdName
    107:107:java.lang.String getAppIdType() -> getAppIdType
    111:112:void setAppIdType(java.lang.String) -> setAppIdType
    115:115:java.lang.String getOem() -> getOem
    119:120:void setOem(java.lang.String) -> setOem
    123:123:java.lang.String getCarBrand() -> getCarBrand
    127:128:void setCarBrand(java.lang.String) -> setCarBrand
    131:131:java.lang.String getCarType() -> getCarType
    135:136:void setCarType(java.lang.String) -> setCarType
    139:139:java.lang.String getDeveloper() -> getDeveloper
    143:144:void setDeveloper(java.lang.String) -> setDeveloper
com.kaolafm.opensdk.api.live.LiveRequest -> com.kaolafm.opensdk.api.live.LiveRequest:
    java.lang.String KEY_PROGRAM_ID -> KEY_PROGRAM_ID
    java.lang.String KEY_KRADIO_ID -> KEY_KRADIO_ID
    java.lang.String KEY_PHONE_NUM -> KEY_PHONE_NUM
    com.kaolafm.opensdk.api.live.LiveService mLiveService -> mLiveService
    25:27:void <init>() -> <init>
    35:36:void getLiveInfo(java.lang.String,HttpCallback) -> getLiveInfo
    45:49:void getChatRoomToken(java.lang.String,java.lang.String,HttpCallback) -> getChatRoomToken
    60:67:void getChatRoomToken(java.lang.String,java.lang.String,java.lang.String,HttpCallback) -> getChatRoomToken
com.kaolafm.opensdk.api.live.LiveService -> com.kaolafm.opensdk.api.live.a:
    io.reactivex.Single getLiveInfo(java.lang.String) -> getLiveInfo
    io.reactivex.Single getChatRoomToken(java.util.HashMap) -> getChatRoomToken
    io.reactivex.Single getChatRoomTokenByUnique(okhttp3.RequestBody) -> getChatRoomTokenByUnique
com.kaolafm.opensdk.api.live.model.ChatRoomTokenDetail -> com.kaolafm.opensdk.api.live.model.ChatRoomTokenDetail:
    java.lang.String accid -> accid
    java.lang.String token -> token
    java.lang.String nickName -> nickName
    android.os.Parcelable$Creator CREATOR -> CREATOR
    21:22:void <init>() -> <init>
    24:28:void <init>(android.os.Parcel) -> <init>
    31:31:java.lang.String getAccid() -> getAccid
    35:35:java.lang.String getToken() -> getToken
    39:39:java.lang.String getNickName() -> getNickName
    56:56:int describeContents() -> describeContents
    61:64:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    42:42:void <clinit>() -> <clinit>
com.kaolafm.opensdk.api.live.model.ChatRoomTokenDetail$1 -> com.kaolafm.opensdk.api.live.model.a:
    42:42:void <init>() -> <init>
    45:45:com.kaolafm.opensdk.api.live.model.ChatRoomTokenDetail createFromParcel(android.os.Parcel) -> createFromParcel
    50:50:com.kaolafm.opensdk.api.live.model.ChatRoomTokenDetail[] newArray(int) -> newArray
    42:42:java.lang.Object[] newArray(int) -> newArray
    42:42:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
com.kaolafm.opensdk.api.live.model.LiveInfoDetail -> com.kaolafm.opensdk.api.live.model.LiveInfoDetail:
    int STATUS_FINISHED -> STATUS_FINISHED
    int STATUS_LIVING -> STATUS_LIVING
    int STATUS_COMING -> STATUS_COMING
    int STATUS_START_TODAY -> STATUS_START_TODAY
    int STATUS_START_TOMORROW -> STATUS_START_TOMORROW
    int STATUS_START_AFTER_TOMORROW -> STATUS_START_AFTER_TOMORROW
    int STATUS_NOT_START -> STATUS_NOT_START
    int STATUS_DELAYED -> STATUS_DELAYED
    int STATUS_LIVE_TO_RECORDING -> STATUS_LIVE_TO_RECORDING
    java.lang.String liveName -> liveName
    long liveId -> liveId
    java.lang.String liveDesc -> liveDesc
    long programId -> programId
    java.lang.String programName -> programName
    java.lang.String programDesc -> programDesc
    java.lang.String beginTime -> beginTime
    java.lang.String endTime -> endTime
    int status -> status
    int period -> period
    java.lang.String comperes -> comperes
    java.lang.String liveUrl -> liveUrl
    java.lang.String programPic -> programPic
    java.lang.String livePic -> livePic
    java.lang.String timeLength -> timeLength
    long startTime -> startTime
    long finishTime -> finishTime
    long serverTime -> serverTime
    int duration -> duration
    java.lang.String roomId -> roomId
    java.lang.String showStartTime -> showStartTime
    android.os.Parcelable$Creator CREATOR -> CREATOR
    138:139:void <init>() -> <init>
    142:142:java.lang.String getLiveName() -> getLiveName
    146:147:void setLiveName(java.lang.String) -> setLiveName
    150:150:long getLiveId() -> getLiveId
    154:155:void setLiveId(long) -> setLiveId
    158:158:java.lang.String getLiveDesc() -> getLiveDesc
    162:163:void setLiveDesc(java.lang.String) -> setLiveDesc
    166:166:long getProgramId() -> getProgramId
    170:171:void setProgramId(long) -> setProgramId
    174:174:java.lang.String getProgramName() -> getProgramName
    178:179:void setProgramName(java.lang.String) -> setProgramName
    182:182:java.lang.String getProgramDesc() -> getProgramDesc
    186:187:void setProgramDesc(java.lang.String) -> setProgramDesc
    190:190:java.lang.String getBeginTime() -> getBeginTime
    194:195:void setBeginTime(java.lang.String) -> setBeginTime
    198:198:java.lang.String getEndTime() -> getEndTime
    202:203:void setEndTime(java.lang.String) -> setEndTime
    206:206:int getStatus() -> getStatus
    210:211:void setStatus(int) -> setStatus
    214:214:int getPeriod() -> getPeriod
    218:219:void setPeriod(int) -> setPeriod
    222:222:java.lang.String getComperes() -> getComperes
    226:227:void setComperes(java.lang.String) -> setComperes
    230:230:java.lang.String getLiveUrl() -> getLiveUrl
    234:235:void setLiveUrl(java.lang.String) -> setLiveUrl
    238:238:java.lang.String getProgramPic() -> getProgramPic
    242:243:void setProgramPic(java.lang.String) -> setProgramPic
    246:246:java.lang.String getLivePic() -> getLivePic
    250:251:void setLivePic(java.lang.String) -> setLivePic
    254:254:java.lang.String getTimeLength() -> getTimeLength
    258:259:void setTimeLength(java.lang.String) -> setTimeLength
    262:262:long getStartTime() -> getStartTime
    266:267:void setStartTime(long) -> setStartTime
    270:270:long getFinishTime() -> getFinishTime
    274:275:void setFinishTime(long) -> setFinishTime
    278:278:long getServerTime() -> getServerTime
    282:283:void setServerTime(long) -> setServerTime
    286:286:int getDuration() -> getDuration
    290:291:void setDuration(int) -> setDuration
    294:294:java.lang.String getRoomId() -> getRoomId
    298:299:void setRoomId(java.lang.String) -> setRoomId
    302:302:java.lang.String getShowStartTime() -> getShowStartTime
    306:307:void setShowStartTime(java.lang.String) -> setShowStartTime
    309:331:void <init>(android.os.Parcel) -> <init>
    347:347:int describeContents() -> describeContents
    352:373:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    333:333:void <clinit>() -> <clinit>
com.kaolafm.opensdk.api.live.model.LiveInfoDetail$1 -> com.kaolafm.opensdk.api.live.model.b:
    333:333:void <init>() -> <init>
    336:336:com.kaolafm.opensdk.api.live.model.LiveInfoDetail createFromParcel(android.os.Parcel) -> createFromParcel
    341:341:com.kaolafm.opensdk.api.live.model.LiveInfoDetail[] newArray(int) -> newArray
    333:333:java.lang.Object[] newArray(int) -> newArray
    333:333:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
com.kaolafm.opensdk.api.login.LoginRequest -> com.kaolafm.opensdk.api.login.LoginRequest:
    com.kaolafm.opensdk.api.login.LoginService mLoginService -> mLoginService
    java.lang.String AUTHOR_SUCCESS_CODE -> AUTHOR_SUCCESS_CODE
    38:40:void <init>() -> <init>
    47:48:void getQRCode(HttpCallback) -> getQRCode
    57:63:void getQRCode(java.lang.String,HttpCallback) -> getQRCode
    71:76:void checkQRCodeStatus(java.lang.String,HttpCallback) -> checkQRCodeStatus
    84:92:void fetchCode(java.lang.String,HttpCallback) -> fetchCode
    101:102:void authorized(java.lang.String,HttpCallback) -> authorized
    110:111:void getUserInfo(HttpCallback) -> getUserInfo
    121:128:void authorizedByCode(java.lang.String,HttpCallback) -> authorizedByCode
    137:148:void authorizedByUuid(java.lang.String,HttpCallback) -> authorizedByUuid
    161:193:void loginWhenAuthorized(java.lang.String,long,HttpCallback) -> loginWhenAuthorized
    205:206:void loginWhenAuthorized(java.lang.String,HttpCallback) -> loginWhenAuthorized
    214:218:void refreshToken(HttpCallback) -> refreshToken
    221:229:io.reactivex.Single refreshToken() -> refreshToken
    238:247:void logoutTingban(HttpCallback) -> logoutTingban
    257:272:void linkAccount(java.lang.String,java.lang.String,long,HttpCallback) -> linkAccount
    281:290:boolean saveToken(com.kaolafm.opensdk.api.BaseResult) -> saveToken
    294:302:com.kaolafm.opensdk.api.login.model.UserInfo saveUserId(com.kaolafm.opensdk.api.BaseResult) -> saveUserId
    309:310:void getKaolaQRCode(HttpCallback) -> getKaolaQRCode
    320:321:void checkPhoneIsRegistered(java.lang.String,HttpCallback) -> checkPhoneIsRegistered
    331:336:void getVerificationCode(java.lang.String,HttpCallback) -> getVerificationCode
    349:356:void register(java.lang.String,java.lang.String,int,java.lang.String,HttpCallback) -> register
    367:369:void login(java.lang.String,java.lang.String,HttpCallback) -> login
    378:385:void logout(HttpCallback) -> logout
    395:396:void bindKradio(java.lang.String,HttpCallback) -> bindKradio
    406:425:void bindKradioByUuid(java.lang.String,HttpCallback) -> bindKradioByUuid
    434:443:void unbindKradio(HttpCallback) -> unbindKradio
    452:453:void isBindKradio(HttpCallback) -> isBindKradio
    456:467:java.lang.Boolean saveAccessToken(com.kaolafm.opensdk.api.login.internal.AuthorInfo) -> saveAccessToken
    435:440:java.lang.Boolean lambda$unbindKradio$18(com.kaolafm.opensdk.api.BaseResult) -> lambda$unbindKradio$18
    379:383:java.lang.Boolean lambda$logout$17(com.kaolafm.opensdk.api.BaseResult) -> lambda$logout$17
    368:368:java.lang.Boolean lambda$login$16(com.kaolafm.opensdk.api.BaseResult) -> lambda$login$16
    355:355:java.lang.Boolean lambda$register$15(com.kaolafm.opensdk.api.BaseResult) -> lambda$register$15
    333:334:java.lang.Boolean lambda$getVerificationCode$14(com.kaolafm.opensdk.api.BaseResult) -> lambda$getVerificationCode$14
    266:270:java.lang.Boolean lambda$linkAccount$13(com.kaolafm.opensdk.api.BaseResult) -> lambda$linkAccount$13
    239:245:java.lang.Boolean lambda$logoutTingban$12(com.kaolafm.opensdk.api.BaseResult) -> lambda$logoutTingban$12
    226:227:io.reactivex.SingleSource lambda$refreshToken$11(com.kaolafm.opensdk.api.BaseResult) -> lambda$refreshToken$11
    189:190:io.reactivex.ObservableSource lambda$loginWhenAuthorized$10(com.kaolafm.opensdk.api.BaseResult) -> lambda$loginWhenAuthorized$10
    184:186:io.reactivex.ObservableSource lambda$loginWhenAuthorized$9(com.kaolafm.opensdk.api.BaseResult) -> lambda$loginWhenAuthorized$9
    177:180:io.reactivex.ObservableSource lambda$loginWhenAuthorized$8(java.lang.String,com.kaolafm.opensdk.api.BaseResult) -> lambda$loginWhenAuthorized$8
    166:166:io.reactivex.ObservableSource lambda$loginWhenAuthorized$7(long,io.reactivex.Observable) -> lambda$loginWhenAuthorized$7
    168:173:io.reactivex.ObservableSource lambda$null$6(long,java.lang.Object) -> lambda$null$6
    163:164:boolean lambda$loginWhenAuthorized$5(com.kaolafm.opensdk.api.BaseResult) -> lambda$loginWhenAuthorized$5
    144:145:io.reactivex.ObservableSource lambda$authorizedByUuid$4(com.kaolafm.opensdk.api.BaseResult) -> lambda$authorizedByUuid$4
    139:141:io.reactivex.ObservableSource lambda$authorizedByUuid$3(com.kaolafm.opensdk.api.BaseResult) -> lambda$authorizedByUuid$3
    124:125:io.reactivex.ObservableSource lambda$authorizedByCode$2(com.kaolafm.opensdk.api.BaseResult) -> lambda$authorizedByCode$2
    85:90:java.lang.String lambda$fetchCode$1(com.kaolafm.opensdk.api.BaseResult) -> lambda$fetchCode$1
    72:73:java.lang.Integer lambda$checkQRCodeStatus$0(com.kaolafm.opensdk.api.BaseResult) -> lambda$checkQRCodeStatus$0
    32:32:void access$000(com.kaolafm.opensdk.api.login.LoginRequest,java.lang.String,HttpCallback) -> access$000
com.kaolafm.opensdk.api.login.LoginRequest$1 -> com.kaolafm.opensdk.api.login.LoginRequest$1:
    HttpCallback val$callback -> val$callback
    com.kaolafm.opensdk.api.login.LoginRequest this$0 -> this$0
    406:406:void <init>(com.kaolafm.opensdk.api.login.LoginRequest,HttpCallback) -> <init>
    409:416:void onSuccess(java.lang.String) -> onSuccess
    420:423:void onError(ApiException) -> onError
    406:406:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.api.login.LoginService -> com.kaolafm.opensdk.api.login.LoginService:
    java.lang.String PARAMETER_PHONE_NUMBER -> PARAMETER_PHONE_NUMBER
    java.lang.String PARAMETER_VALIDATE_CODE -> PARAMETER_VALIDATE_CODE
    io.reactivex.Single checkPhoneIsRegistered(java.lang.String) -> checkPhoneIsRegistered
    io.reactivex.Single getVerificationCode(java.lang.String) -> getVerificationCode
    io.reactivex.Single register(java.util.HashMap) -> register
    io.reactivex.Single login(java.lang.String,java.lang.String) -> login
    io.reactivex.Single logout() -> logout
    io.reactivex.Single bindKradio(java.lang.String) -> bindKradio
    io.reactivex.Single unbindKradio() -> unbindKradio
    io.reactivex.Single isBindKradio() -> isBindKradio
    io.reactivex.Observable authorized(java.lang.String) -> authorized
    io.reactivex.Single logoutTingban() -> logoutTingban
    io.reactivex.Single getQRCode(java.util.Map) -> getQRCode
    io.reactivex.Observable checkQRStatus(java.lang.String) -> checkQRStatus
    io.reactivex.Observable fetchCode(java.lang.String) -> fetchCode
    io.reactivex.Observable authorized(java.lang.String,java.lang.String) -> authorized
    io.reactivex.Single refreshToken(java.lang.String,java.lang.String) -> refreshToken
    io.reactivex.Observable getUserInfo() -> getUserInfo
    io.reactivex.Single revokeAuthorization(java.lang.String) -> revokeAuthorization
    io.reactivex.Single linkAccount(okhttp3.RequestBody) -> linkAccount
com.kaolafm.opensdk.api.login.internal.AuthorInfo -> com.kaolafm.opensdk.api.login.internal.AuthorInfo:
    java.lang.String msg -> msg
    java.lang.String code -> code
    java.lang.String token -> token
    java.lang.String userId -> userId
    java.lang.String wechatDeviceId -> wechatDeviceId
    10:10:void <init>() -> <init>
    24:24:java.lang.String getMsg() -> getMsg
    28:29:void setMsg(java.lang.String) -> setMsg
    32:32:java.lang.String getCode() -> getCode
    36:37:void setCode(java.lang.String) -> setCode
    40:40:java.lang.String getToken() -> getToken
    44:45:void setToken(java.lang.String) -> setToken
    48:48:java.lang.String getUserId() -> getUserId
    52:53:void setUserId(java.lang.String) -> setUserId
    56:56:java.lang.String getWechatDeviceId() -> getWechatDeviceId
    60:61:void setWechatDeviceId(java.lang.String) -> setWechatDeviceId
com.kaolafm.opensdk.api.login.internal.LinkAccountNumberDTO -> com.kaolafm.opensdk.api.login.internal.LinkAccountNumberDTO:
    java.lang.String userId -> userId
    java.lang.String userToken -> userToken
    java.lang.String appId -> appId
    java.lang.String secretKey -> secretKey
    java.lang.String authenticateUrl -> authenticateUrl
    long tokenActiveTime -> tokenActiveTime
    8:8:void <init>() -> <init>
    35:36:void setUserId(java.lang.String) -> setUserId
    39:40:void setUserToken(java.lang.String) -> setUserToken
    43:44:void setAppId(java.lang.String) -> setAppId
    47:48:void setTokenActiveTime(long) -> setTokenActiveTime
com.kaolafm.opensdk.api.login.internal.Sex -> com.kaolafm.opensdk.api.login.internal.a:
com.kaolafm.opensdk.api.login.model.QRCodeInfo -> com.kaolafm.opensdk.api.login.model.QRCodeInfo:
    int STATUS_NORMAL -> STATUS_NORMAL
    int STATUS_LOSE_EFFICACY -> STATUS_LOSE_EFFICACY
    int STATUS_AUTHORIZATION -> STATUS_AUTHORIZATION
    java.lang.String code -> code
    java.lang.String qrCodePath -> qrCodePath
    java.lang.String state -> state
    int status -> status
    java.lang.String uuid -> uuid
    12:12:void <init>() -> <init>
    54:54:java.lang.String getCode() -> getCode
    58:58:java.lang.String getQRCodePath() -> getQRCodePath
    62:62:java.lang.String getState() -> getState
    66:66:int getStatus() -> getStatus
    70:70:java.lang.String getUuid() -> getUuid
    74:75:void setCode(java.lang.String) -> setCode
    78:79:void setQRCodePath(java.lang.String) -> setQRCodePath
    82:83:void setState(java.lang.String) -> setState
    86:87:void setStatus(int) -> setStatus
    90:91:void setUuid(java.lang.String) -> setUuid
    95:95:java.lang.String toString() -> toString
com.kaolafm.opensdk.api.login.model.Success -> com.kaolafm.opensdk.api.login.model.Success:
    java.lang.String CODE_SUCCESS -> CODE_SUCCESS
    java.lang.String PHONE_NUMBER_IS_EXIST -> PHONE_NUMBER_IS_EXIST
    java.lang.String PHONE_NUMBER_IS_NOT_EXIST -> PHONE_NUMBER_IS_NOT_EXIST
    java.lang.String VERIFICATION_SUCCESS -> VERIFICATION_SUCCESS
    int STATUS_SUCCESS -> STATUS_SUCCESS
    java.lang.String code -> code
    java.lang.String msg -> msg
    12:12:void <init>() -> <init>
    36:36:java.lang.String getCode() -> getCode
    40:41:void setCode(java.lang.String) -> setCode
    44:44:java.lang.String getMsg() -> getMsg
    48:49:void setMsg(java.lang.String) -> setMsg
com.kaolafm.opensdk.api.login.model.UserInfo -> com.kaolafm.opensdk.api.login.model.UserInfo:
    java.lang.String nickName -> nickName
    java.lang.String avatar -> avatar
    11:11:void <init>() -> <init>
    22:22:java.lang.String getNickName() -> getNickName
    26:27:void setNickName(java.lang.String) -> setNickName
    30:30:java.lang.String getAvatar() -> getAvatar
    34:35:void setAvatar(java.lang.String) -> setAvatar
com.kaolafm.opensdk.api.media.AlbumRequest -> com.kaolafm.opensdk.api.media.AlbumRequest:
    com.kaolafm.opensdk.api.media.AlbumService mService -> mService
    int SORT_ACS -> SORT_ACS
    int SORT_DESC -> SORT_DESC
    37:39:void <init>() -> <init>
    49:56:void getAlbumDetails(long,HttpCallback) -> getAlbumDetails
    65:67:void getAlbumDetails(java.lang.Long[],HttpCallback) -> getAlbumDetails
    98:99:void getPlaylist(long,long,int,int,int,HttpCallback) -> getPlaylist
    113:114:void getPlaylist(long,int,int,int,HttpCallback) -> getPlaylist
    50:54:com.kaolafm.opensdk.api.media.model.AlbumDetails lambda$getAlbumDetails$0(com.kaolafm.opensdk.api.BaseResult) -> lambda$getAlbumDetails$0
com.kaolafm.opensdk.api.media.AlbumRequest$Sort -> com.kaolafm.opensdk.api.media.AlbumRequest$a:
com.kaolafm.opensdk.api.media.AlbumService -> com.kaolafm.opensdk.api.media.a:
    io.reactivex.Single getAlbumDetails(long) -> getAlbumDetails
    io.reactivex.Single getAlbumDetails(java.lang.String) -> getAlbumDetails
    io.reactivex.Single getPlaylist(long,int,int,int,long) -> getPlaylist
com.kaolafm.opensdk.api.media.AudioRequest -> com.kaolafm.opensdk.api.media.AudioRequest:
    com.kaolafm.opensdk.api.media.AudioService mService -> mService
    34:36:void <init>() -> <init>
    46:47:void getAudioDetails(long,HttpCallback) -> getAudioDetails
    56:57:void getAudioDetails(java.lang.Long[],HttpCallback) -> getAudioDetails
    64:65:void getCurrentClockAudio(HttpCallback) -> getCurrentClockAudio
com.kaolafm.opensdk.api.media.AudioService -> com.kaolafm.opensdk.api.media.b:
    io.reactivex.Single getAudioDetails(long) -> getAudioDetails
    io.reactivex.Single getAudioDetails(java.lang.String) -> getAudioDetails
    io.reactivex.Single getCurrentClockAudio() -> getCurrentClockAudio
com.kaolafm.opensdk.api.media.RadioRequest -> com.kaolafm.opensdk.api.media.RadioRequest:
    com.kaolafm.opensdk.api.media.RadioService mService -> mService
    34:36:void <init>() -> <init>
    46:47:void getRadioDetails(long,HttpCallback) -> getRadioDetails
    58:59:void getPlaylist(long,java.lang.String,HttpCallback) -> getPlaylist
    72:78:void getPlaylist(long,java.lang.String,java.lang.String,java.lang.String,HttpCallback) -> getPlaylist
com.kaolafm.opensdk.api.media.RadioService -> com.kaolafm.opensdk.api.media.c:
    io.reactivex.Single getRadioDetails(long) -> getRadioDetails
    io.reactivex.Single getPlaylist(java.util.Map) -> getPlaylist
com.kaolafm.opensdk.api.media.model.AlbumDetails -> com.kaolafm.opensdk.api.media.model.AlbumDetails:
    int countNum -> countNum
    int sortType -> sortType
    int hasCopyright -> hasCopyright
    java.lang.String produce -> produce
    java.lang.String status -> status
    java.lang.String updateDay -> updateDay
    java.lang.String copyrightLabel -> copyrightLabel
    long lastCheckDate -> lastCheckDate
    android.os.Parcelable$Creator CREATOR -> CREATOR
    77:77:int getCountNum() -> getCountNum
    81:82:void setCountNum(int) -> setCountNum
    85:85:int getSortType() -> getSortType
    89:90:void setSortType(int) -> setSortType
    93:93:int getHasCopyright() -> getHasCopyright
    97:98:void setHasCopyright(int) -> setHasCopyright
    101:101:java.lang.String getProduce() -> getProduce
    105:106:void setProduce(java.lang.String) -> setProduce
    109:109:java.lang.String getStatus() -> getStatus
    113:114:void setStatus(java.lang.String) -> setStatus
    117:117:java.lang.String getUpdateDay() -> getUpdateDay
    121:122:void setUpdateDay(java.lang.String) -> setUpdateDay
    125:125:java.lang.String getCopyrightLabel() -> getCopyrightLabel
    129:130:void setCopyrightLabel(java.lang.String) -> setCopyrightLabel
    133:133:long getLastCheckDate() -> getLastCheckDate
    137:138:void setLastCheckDate(long) -> setLastCheckDate
    143:152:java.lang.String toString() -> toString
    157:157:int describeContents() -> describeContents
    162:171:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    174:183:void <init>(android.os.Parcel) -> <init>
    185:185:void <clinit>() -> <clinit>
com.kaolafm.opensdk.api.media.model.AlbumDetails$1 -> com.kaolafm.opensdk.api.media.model.a:
    185:185:void <init>() -> <init>
    188:188:com.kaolafm.opensdk.api.media.model.AlbumDetails createFromParcel(android.os.Parcel) -> createFromParcel
    193:193:com.kaolafm.opensdk.api.media.model.AlbumDetails[] newArray(int) -> newArray
    185:185:java.lang.Object[] newArray(int) -> newArray
    185:185:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
com.kaolafm.opensdk.api.media.model.AudioDetails -> com.kaolafm.opensdk.api.media.model.AudioDetails:
    long audioId -> audioId
    java.lang.String audioName -> audioName
    java.lang.String audioPic -> audioPic
    java.lang.String audioDes -> audioDes
    long albumId -> albumId
    java.lang.String albumName -> albumName
    java.lang.String albumPic -> albumPic
    int orderNum -> orderNum
    java.lang.String mp3PlayUrl32 -> mp3PlayUrl32
    java.lang.String mp3PlayUrl64 -> mp3PlayUrl64
    java.lang.String aacPlayUrl -> aacPlayUrl
    java.lang.String aacPlayUrl32 -> aacPlayUrl32
    java.lang.String aacPlayUrl64 -> aacPlayUrl64
    java.lang.String aacPlayUrl128 -> aacPlayUrl128
    java.lang.String aacPlayUrl320 -> aacPlayUrl320
    int aacFileSize -> aacFileSize
    int mp3FileSize32 -> mp3FileSize32
    int mp3FileSize64 -> mp3FileSize64
    long updateTime -> updateTime
    java.lang.String clockId -> clockId
    int duration -> duration
    int originalDuration -> originalDuration
    int listenNum -> listenNum
    int likedNum -> likedNum
    int hasCopyright -> hasCopyright
    int commentNum -> commentNum
    int trailerStart -> trailerStart
    int trailerEnd -> trailerEnd
    int categoryId -> categoryId
    java.lang.String source -> source
    int isListened -> isListened
    java.lang.String icon -> icon
    java.util.List host -> host
    int isThirdParty -> isThirdParty
    int contentType -> contentType
    java.lang.String contentTypeName -> contentTypeName
    java.lang.String mainTitleName -> mainTitleName
    java.lang.String subheadName -> subheadName
    android.os.Parcelable$Creator CREATOR -> CREATOR
    187:187:long getAudioId() -> getAudioId
    191:192:void setAudioId(long) -> setAudioId
    195:195:java.lang.String getAudioName() -> getAudioName
    199:200:void setAudioName(java.lang.String) -> setAudioName
    203:203:java.lang.String getAudioPic() -> getAudioPic
    207:208:void setAudioPic(java.lang.String) -> setAudioPic
    211:211:java.lang.String getAudioDes() -> getAudioDes
    215:216:void setAudioDes(java.lang.String) -> setAudioDes
    219:219:long getAlbumId() -> getAlbumId
    223:224:void setAlbumId(long) -> setAlbumId
    227:227:java.lang.String getAlbumName() -> getAlbumName
    231:232:void setAlbumName(java.lang.String) -> setAlbumName
    235:235:java.lang.String getAlbumPic() -> getAlbumPic
    239:240:void setAlbumPic(java.lang.String) -> setAlbumPic
    243:243:int getOrderNum() -> getOrderNum
    247:248:void setOrderNum(int) -> setOrderNum
    251:251:java.lang.String getMp3PlayUrl32() -> getMp3PlayUrl32
    255:256:void setMp3PlayUrl32(java.lang.String) -> setMp3PlayUrl32
    259:259:java.lang.String getMp3PlayUrl64() -> getMp3PlayUrl64
    263:264:void setMp3PlayUrl64(java.lang.String) -> setMp3PlayUrl64
    267:267:java.lang.String getAacPlayUrl() -> getAacPlayUrl
    271:272:void setAacPlayUrl(java.lang.String) -> setAacPlayUrl
    275:275:java.lang.String getAacPlayUrl32() -> getAacPlayUrl32
    279:280:void setAacPlayUrl32(java.lang.String) -> setAacPlayUrl32
    283:283:java.lang.String getAacPlayUrl64() -> getAacPlayUrl64
    287:288:void setAacPlayUrl64(java.lang.String) -> setAacPlayUrl64
    291:291:java.lang.String getAacPlayUrl128() -> getAacPlayUrl128
    295:296:void setAacPlayUrl128(java.lang.String) -> setAacPlayUrl128
    299:299:java.lang.String getAacPlayUrl320() -> getAacPlayUrl320
    303:304:void setAacPlayUrl320(java.lang.String) -> setAacPlayUrl320
    307:307:int getAacFileSize() -> getAacFileSize
    311:312:void setAacFileSize(int) -> setAacFileSize
    315:315:int getMp3FileSize32() -> getMp3FileSize32
    319:320:void setMp3FileSize32(int) -> setMp3FileSize32
    323:323:int getMp3FileSize64() -> getMp3FileSize64
    327:328:void setMp3FileSize64(int) -> setMp3FileSize64
    331:331:long getUpdateTime() -> getUpdateTime
    335:336:void setUpdateTime(long) -> setUpdateTime
    339:339:java.lang.String getClockId() -> getClockId
    343:344:void setClockId(java.lang.String) -> setClockId
    347:347:int getDuration() -> getDuration
    351:352:void setDuration(int) -> setDuration
    355:355:int getOriginalDuration() -> getOriginalDuration
    359:360:void setOriginalDuration(int) -> setOriginalDuration
    363:363:int getListenNum() -> getListenNum
    367:368:void setListenNum(int) -> setListenNum
    371:371:int getLikedNum() -> getLikedNum
    375:376:void setLikedNum(int) -> setLikedNum
    379:379:int getHasCopyright() -> getHasCopyright
    383:384:void setHasCopyright(int) -> setHasCopyright
    387:387:int getCommentNum() -> getCommentNum
    391:392:void setCommentNum(int) -> setCommentNum
    395:395:int getTrailerStart() -> getTrailerStart
    399:400:void setTrailerStart(int) -> setTrailerStart
    403:403:int getTrailerEnd() -> getTrailerEnd
    407:408:void setTrailerEnd(int) -> setTrailerEnd
    411:411:int getCategoryId() -> getCategoryId
    415:416:void setCategoryId(int) -> setCategoryId
    419:419:java.lang.String getSource() -> getSource
    423:424:void setSource(java.lang.String) -> setSource
    427:427:int getIsListened() -> getIsListened
    431:432:void setIsListened(int) -> setIsListened
    435:435:java.lang.String getIcon() -> getIcon
    439:440:void setIcon(java.lang.String) -> setIcon
    443:443:java.util.List getHost() -> getHost
    447:448:void setHost(java.util.List) -> setHost
    451:451:int getIsThirdParty() -> getIsThirdParty
    455:456:void setIsThirdParty(int) -> setIsThirdParty
    459:459:int getContentType() -> getContentType
    463:464:void setContentType(int) -> setContentType
    467:467:java.lang.String getContentTypeName() -> getContentTypeName
    471:472:void setContentTypeName(java.lang.String) -> setContentTypeName
    475:475:java.lang.String getMainTitleName() -> getMainTitleName
    479:480:void setMainTitleName(java.lang.String) -> setMainTitleName
    483:483:java.lang.String getSubheadName() -> getSubheadName
    487:488:void setSubheadName(java.lang.String) -> setSubheadName
    492:492:int describeContents() -> describeContents
    497:534:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    536:537:void <init>() -> <init>
    539:577:void <init>(android.os.Parcel) -> <init>
    579:579:void <clinit>() -> <clinit>
com.kaolafm.opensdk.api.media.model.AudioDetails$1 -> com.kaolafm.opensdk.api.media.model.b:
    579:579:void <init>() -> <init>
    582:582:com.kaolafm.opensdk.api.media.model.AudioDetails createFromParcel(android.os.Parcel) -> createFromParcel
    587:587:com.kaolafm.opensdk.api.media.model.AudioDetails[] newArray(int) -> newArray
    579:579:java.lang.Object[] newArray(int) -> newArray
    579:579:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
com.kaolafm.opensdk.api.media.model.BaseMediaDetails -> com.kaolafm.opensdk.api.media.model.BaseMediaDetails:
    long id -> id
    java.lang.String name -> name
    java.lang.String img -> img
    int followedNum -> followedNum
    int isOnline -> isOnline
    int listenNum -> listenNum
    java.lang.String desc -> desc
    int commentNum -> commentNum
    int isSubscribe -> isSubscribe
    int type -> type
    java.util.List host -> host
    java.util.List keyWords -> keyWords
    android.os.Parcelable$Creator CREATOR -> CREATOR
    68:68:long getId() -> getId
    72:73:void setId(long) -> setId
    76:76:java.lang.String getName() -> getName
    80:81:void setName(java.lang.String) -> setName
    84:84:java.lang.String getImg() -> getImg
    88:89:void setImg(java.lang.String) -> setImg
    92:92:int getFollowedNum() -> getFollowedNum
    96:97:void setFollowedNum(int) -> setFollowedNum
    100:100:int getIsOnline() -> getIsOnline
    104:105:void setIsOnline(int) -> setIsOnline
    108:108:int getListenNum() -> getListenNum
    112:113:void setListenNum(int) -> setListenNum
    116:116:java.lang.String getDesc() -> getDesc
    120:121:void setDesc(java.lang.String) -> setDesc
    124:124:int getCommentNum() -> getCommentNum
    128:129:void setCommentNum(int) -> setCommentNum
    132:132:int getIsSubscribe() -> getIsSubscribe
    136:137:void setIsSubscribe(int) -> setIsSubscribe
    140:140:int getType() -> getType
    144:145:void setType(int) -> setType
    148:148:java.util.List getHost() -> getHost
    152:153:void setHost(java.util.List) -> setHost
    156:156:java.util.List getKeyWords() -> getKeyWords
    160:161:void setKeyWords(java.util.List) -> setKeyWords
    163:164:void <init>() -> <init>
    166:179:void <init>(android.os.Parcel) -> <init>
    195:195:int describeContents() -> describeContents
    200:212:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    216:216:java.lang.String toString() -> toString
    181:181:void <clinit>() -> <clinit>
com.kaolafm.opensdk.api.media.model.BaseMediaDetails$1 -> com.kaolafm.opensdk.api.media.model.c:
    181:181:void <init>() -> <init>
    184:184:com.kaolafm.opensdk.api.media.model.BaseMediaDetails createFromParcel(android.os.Parcel) -> createFromParcel
    189:189:com.kaolafm.opensdk.api.media.model.BaseMediaDetails[] newArray(int) -> newArray
    181:181:java.lang.Object[] newArray(int) -> newArray
    181:181:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
com.kaolafm.opensdk.api.media.model.Host -> com.kaolafm.opensdk.api.media.model.Host:
    java.lang.String name -> name
    java.lang.String des -> des
    java.lang.String img -> img
    android.os.Parcelable$Creator CREATOR -> CREATOR
    31:31:java.lang.String getName() -> getName
    35:36:void setName(java.lang.String) -> setName
    39:39:java.lang.String getDes() -> getDes
    43:44:void setDes(java.lang.String) -> setDes
    47:47:java.lang.String getImg() -> getImg
    51:52:void setImg(java.lang.String) -> setImg
    57:57:java.lang.String toString() -> toString
    67:67:int describeContents() -> describeContents
    72:75:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    77:78:void <init>() -> <init>
    80:84:void <init>(android.os.Parcel) -> <init>
    86:86:void <clinit>() -> <clinit>
com.kaolafm.opensdk.api.media.model.Host$1 -> com.kaolafm.opensdk.api.media.model.d:
    86:86:void <init>() -> <init>
    89:89:com.kaolafm.opensdk.api.media.model.Host createFromParcel(android.os.Parcel) -> createFromParcel
    94:94:com.kaolafm.opensdk.api.media.model.Host[] newArray(int) -> newArray
    86:86:java.lang.Object[] newArray(int) -> newArray
    86:86:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
com.kaolafm.opensdk.api.media.model.RadioDetails -> com.kaolafm.opensdk.api.media.model.RadioDetails:
    27:28:void <init>(android.os.Parcel) -> <init>
com.kaolafm.opensdk.api.music.qq.LoginType -> com.kaolafm.opensdk.api.music.qq.a:
com.kaolafm.opensdk.api.music.qq.QQMusicApiConstant -> com.kaolafm.opensdk.api.music.qq.QQMusicApiConstant:
    java.lang.String QQMUSIC_BIN -> QQMUSIC_BIN
    int CODE_LOGIN_STATUS_FAILED -> CODE_LOGIN_STATUS_FAILED
    int CODE_LOGIN_STATUS_FAILED_WX -> CODE_LOGIN_STATUS_FAILED_WX
    int CODE_LOGIN_STATUS_FAILED_THIRD -> CODE_LOGIN_STATUS_FAILED_THIRD
    int CODE_LOGIN_STATUS_FAILED_CONNECT_WX -> CODE_LOGIN_STATUS_FAILED_CONNECT_WX
    int CODE_LOGIN_STATUS_FAILED_VERIFICATION -> CODE_LOGIN_STATUS_FAILED_VERIFICATION
    java.lang.String GET_LYRICS -> GET_LYRICS
    java.lang.String GET_WECHAT_QR_CODE_FOR_LOGIN -> GET_WECHAT_QR_CODE_FOR_LOGIN
    java.lang.String GET_QQMUSIC_DOCUMENTS_BY_WECHAT -> GET_QQMUSIC_DOCUMENTS_BY_WECHAT
    java.lang.String WECHAT_LOGIN_WITH_THIRD_PARTY -> WECHAT_LOGIN_WITH_THIRD_PARTY
    java.lang.String QQ_LOGIN_FUNCTIONS -> QQ_LOGIN_FUNCTIONS
    java.lang.String GET_SONG_LIST_BATCH -> GET_SONG_LIST_BATCH
    java.lang.String SEARCH_SONG_BY_KEYWORD -> SEARCH_SONG_BY_KEYWORD
    java.lang.String GET_SONGMENU_LIST_OF_SQUARE -> GET_SONGMENU_LIST_OF_SQUARE
    java.lang.String OPERATE_SONGMENU_OF_SQUATE -> OPERATE_SONGMENU_OF_SQUATE
    java.lang.String GET_SELF_SONGMENU_LIST -> GET_SELF_SONGMENU_LIST
    java.lang.String OPERATE_SONG_OF_SONGMENU -> OPERATE_SONG_OF_SONGMENU
    java.lang.String GET_SONG_LIST_OF_SONGMENU -> GET_SONG_LIST_OF_SONGMENU
    java.lang.String GET_PUBLIC_RADIO_LIST -> GET_PUBLIC_RADIO_LIST
    java.lang.String GET_SONG_LIST_OF_RADIO -> GET_SONG_LIST_OF_RADIO
    java.lang.String GET_DAY_RECOMMEND_SONGS -> GET_DAY_RECOMMEND_SONGS
    java.lang.String GET_DAY_RECOMMEND_SONGMENUS -> GET_DAY_RECOMMEND_SONGMENUS
    java.lang.String GET_INDIVIDUAL_SONGS_OF_RADIO -> GET_INDIVIDUAL_SONGS_OF_RADIO
    java.lang.String GET_SONG_CHARTS_LIST -> GET_SONG_CHARTS_LIST
    java.lang.String GET_SONG_LIST_OF_SONG_CHARTS -> GET_SONG_LIST_OF_SONG_CHARTS
    java.lang.String GET_SONG_LIST_OF_ALBUM -> GET_SONG_LIST_OF_ALBUM
    java.lang.String GET_SONG_LIST_OF_SINGER -> GET_SONG_LIST_OF_SINGER
    java.lang.String GET_SINGER_LIST -> GET_SINGER_LIST
    java.lang.String GET_ALBUM_LIST_OF_SINGER -> GET_ALBUM_LIST_OF_SINGER
    java.lang.String GET_CATEGORY_LIBRARY -> GET_CATEGORY_LIBRARY
    java.lang.String GET_USER_VIP_INFO -> GET_USER_VIP_INFO
    10:10:void <init>() -> <init>
com.kaolafm.opensdk.api.music.qq.QQMusicApiService -> com.kaolafm.opensdk.api.music.qq.b:
    io.reactivex.Single getLyrics(long) -> getLyrics
    io.reactivex.Single getWeChatQRCodeForLogin() -> getWeChatQRCodeForLogin
    io.reactivex.Single getQQMusicDocumentsByWeChat(java.util.Map) -> getQQMusicDocumentsByWeChat
    retrofit2.Call getQQMusicDocumentsByWeChatSynch(java.util.Map) -> getQQMusicDocumentsByWeChatSynch
    io.reactivex.Single loginQQMusicByThirdPartyBindWecaht(java.util.Map) -> loginQQMusicByThirdPartyBindWecaht
    io.reactivex.Single qqLoginFunctions(java.util.Map) -> qqLoginFunctions
    retrofit2.Call qqLoginFunctionsSynch(java.util.Map) -> qqLoginFunctionsSynch
    io.reactivex.Single getSongListBatch(java.lang.String) -> getSongListBatch
    retrofit2.Call getSongListBatchSynch(java.lang.String) -> getSongListBatchSynch
    io.reactivex.Single searchSongByKeyword(java.lang.String,int,int) -> searchSongByKeyword
    io.reactivex.Single getSongMenuListOfSquare(int,int,int) -> getSongMenuListOfSquare
    io.reactivex.Single opareteSongMenuOfSquare(int,java.lang.String) -> opareteSongMenuOfSquare
    io.reactivex.Single getSelfSongMenuList() -> getSelfSongMenuList
    io.reactivex.Single opareteSongOfSongMenu(int,long,java.lang.String) -> opareteSongOfSongMenu
    io.reactivex.Single getSongListOfSongMenu(long) -> getSongListOfSongMenu
    io.reactivex.Single getPublicRadioList() -> getPublicRadioList
    io.reactivex.Single getSongListOfRadio(long) -> getSongListOfRadio
    io.reactivex.Single getDayRecommendSongs() -> getDayRecommendSongs
    io.reactivex.Single getDayRecommendSongMenu() -> getDayRecommendSongMenu
    io.reactivex.Single getIndividualSongsOfRadio(java.lang.String) -> getIndividualSongsOfRadio
    io.reactivex.Single reportOperateForRecommand(java.util.Map) -> reportOperateForRecommand
    io.reactivex.Single getSongListOfSongCharts(long) -> getSongListOfSongCharts
    io.reactivex.Single getSongChartsList() -> getSongChartsList
    io.reactivex.Single getSongListOfAlbum(java.lang.String) -> getSongListOfAlbum
    io.reactivex.Single getSongListOfSinger(java.util.Map) -> getSongListOfSinger
    io.reactivex.Single getSingerList(int,int,int) -> getSingerList
    io.reactivex.Single getAlbumListOfSinger(java.util.Map) -> getAlbumListOfSinger
    io.reactivex.Single getCategoryLibrary(java.util.Map) -> getCategoryLibrary
    io.reactivex.Single getMainCategoryLabels(java.lang.String) -> getMainCategoryLabels
    io.reactivex.Single getCategoryDetail(java.lang.String,java.lang.Long) -> getCategoryDetail
    io.reactivex.Single getSongListOfCategoryLabel(java.lang.String,java.lang.Long) -> getSongListOfCategoryLabel
    io.reactivex.Single getAlbumListOfCategoryLabel(java.util.Map) -> getAlbumListOfCategoryLabel
    io.reactivex.Single getUserVipInfo() -> getUserVipInfo
com.kaolafm.opensdk.api.music.qq.QQMusicConstant -> com.kaolafm.opensdk.api.music.qq.QQMusicConstant:
    java.lang.String LOGIN_TYPE -> LOGIN_TYPE
    int LOGIN_TYPE_TICKET -> LOGIN_TYPE_TICKET
    int LOGIN_TYPE_QQ -> LOGIN_TYPE_QQ
    int LOGIN_TYPE_WECHAT -> LOGIN_TYPE_WECHAT
    int LOGIN_TYPE_DEVICE -> LOGIN_TYPE_DEVICE
    int LOGIN_TYPE_QQMUSIC -> LOGIN_TYPE_QQMUSIC
    9:9:void <init>() -> <init>
com.kaolafm.opensdk.api.music.qq.QQMusicRequest -> com.kaolafm.opensdk.api.music.qq.QQMusicRequest:
    com.kaolafm.opensdk.api.music.qq.QQMusicApiService mQQMusicService -> mQQMusicService
    javax.inject.Provider mQQMusicCommonParams -> mQQMusicCommonParams
    javax.inject.Provider mWxRefreshParamsLazy -> mWxRefreshParamsLazy
    javax.inject.Provider mQQRefreshParamsLazy -> mQQRefreshParamsLazy
    javax.inject.Provider mQQUserInfoParamsLazy -> mQQUserInfoParamsLazy
    59:65:void <init>() -> <init>
    74:75:void getLyrics(long,HttpCallback) -> getLyrics
    82:84:void getWeChatQRCodeForLogin(HttpCallback) -> getWeChatQRCodeForLogin
    93:106:void loginQQMusicWithWechatAuthorizationCode(java.lang.String,HttpCallback) -> loginQQMusicWithWechatAuthorizationCode
    114:115:void refreshWeChatTokenForLogin(HttpCallback) -> refreshWeChatTokenForLogin
    124:124:com.kaolafm.opensdk.api.music.qq.model.TencentLoginResult refreshWeChatTokenForLogin() -> refreshWeChatTokenForLogin
    131:144:void loginQQMusicWithQQAuthorizationCode(java.lang.String,HttpCallback) -> loginQQMusicWithQQAuthorizationCode
    150:151:void refreshQQTokenForLogin(HttpCallback) -> refreshQQTokenForLogin
    158:158:com.kaolafm.opensdk.api.music.qq.model.TencentLoginResult refreshQQTokenForLogin() -> refreshQQTokenForLogin
    165:167:void getQQUserInfo(HttpCallback) -> getQQUserInfo
    178:179:void getSongListBatch(java.lang.String,HttpCallback) -> getSongListBatch
    189:189:com.kaolafm.opensdk.api.music.qq.model.BaseMusicResult getSongListBatch(java.lang.String) -> getSongListBatch
    205:207:void getSongMenuListOfSquare(int,int,int,HttpCallback) -> getSongMenuListOfSquare
    217:218:void operateSongMenuOfSquare(int,java.lang.String,HttpCallback) -> operateSongMenuOfSquare
    227:242:void collectSongMenu(boolean,java.lang.String,HttpCallback) -> collectSongMenu
    248:259:void getFavoriteSongMenuList(HttpCallback) -> getFavoriteSongMenuList
    267:268:void getSongListOfSongMenu(long,HttpCallback) -> getSongListOfSongMenu
    274:275:void getSelfSongMenuList(HttpCallback) -> getSelfSongMenuList
    281:305:void getMyLikeId(HttpCallback) -> getMyLikeId
    315:317:void operateSongOfSongMenu(int,long,java.lang.String,HttpCallback) -> operateSongOfSongMenu
    323:330:void likeThis(long,java.lang.String,HttpCallback) -> likeThis
    336:343:void dislikeThis(long,java.lang.String,HttpCallback) -> dislikeThis
    351:352:void getPublicRadioList(HttpCallback) -> getPublicRadioList
    360:361:void getSongListOfRadio(long,HttpCallback) -> getSongListOfRadio
    369:370:void getDayRecommendSongs(HttpCallback) -> getDayRecommendSongs
    376:379:void getIndividualSongsOfRadio(HttpCallback) -> getIndividualSongsOfRadio
    391:399:void reportOperateForRecommand(java.lang.String,long,long,java.lang.String,HttpCallback) -> reportOperateForRecommand
    409:410:void reportCollectSong(long,long,java.lang.String) -> reportCollectSong
    420:421:void reportDeleteSong(long,long,java.lang.String) -> reportDeleteSong
    431:433:void reportPlayNextSong(long,long,java.lang.String) -> reportPlayNextSong
    443:444:void reportListenToEnd(long,long,java.lang.String) -> reportListenToEnd
    452:453:void getSongChartsList(HttpCallback) -> getSongChartsList
    461:462:void getSongListOfSongCharts(long,HttpCallback) -> getSongListOfSongCharts
    485:486:void getCategoryLibrary(java.util.Map,HttpCallback) -> getCategoryLibrary
    492:494:void getMainCategoryLabels(HttpCallback) -> getMainCategoryLabels
    500:502:void getCategoryDetail(long,HttpCallback) -> getCategoryDetail
    509:511:void getSongListOfCategoryLabel(long,HttpCallback) -> getSongListOfCategoryLabel
    519:526:void getAlbumListOfCategoryLabel(long,int,int,HttpCallback) -> getAlbumListOfCategoryLabel
    534:535:void getUserVipInfo(HttpCallback) -> getUserVipInfo
    538:538:java.util.Map getCommonParams() -> getCommonParams
    316:316:java.lang.Boolean lambda$operateSongOfSongMenu$3(com.kaolafm.opensdk.api.music.qq.model.BaseMusicResult) -> lambda$operateSongOfSongMenu$3
    136:142:com.kaolafm.opensdk.api.music.qq.model.TencentUserInfo lambda$loginQQMusicWithQQAuthorizationCode$2(com.kaolafm.opensdk.api.music.qq.model.TencentLoginResult) -> lambda$loginQQMusicWithQQAuthorizationCode$2
    98:104:com.kaolafm.opensdk.api.music.qq.model.TencentUserInfo lambda$loginQQMusicWithWechatAuthorizationCode$1(com.kaolafm.opensdk.api.music.qq.model.TencentLoginResult) -> lambda$loginQQMusicWithWechatAuthorizationCode$1
    64:64:java.util.Map lambda$new$0() -> lambda$new$0
com.kaolafm.opensdk.api.music.qq.QQMusicRequest$1 -> com.kaolafm.opensdk.api.music.qq.QQMusicRequest$1:
    HttpCallback val$callback -> val$callback
    com.kaolafm.opensdk.api.music.qq.QQMusicRequest this$0 -> this$0
    227:227:void <init>(com.kaolafm.opensdk.api.music.qq.QQMusicRequest,HttpCallback) -> <init>
    230:235:void onSuccess(com.kaolafm.opensdk.api.music.qq.model.BaseMusicResult) -> onSuccess
    239:240:void onError(ApiException) -> onError
    227:227:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.api.music.qq.QQMusicRequest$2 -> com.kaolafm.opensdk.api.music.qq.QQMusicRequest$2:
    HttpCallback val$callback -> val$callback
    com.kaolafm.opensdk.api.music.qq.QQMusicRequest this$0 -> this$0
    248:248:void <init>(com.kaolafm.opensdk.api.music.qq.QQMusicRequest,HttpCallback) -> <init>
    251:252:void onSuccess(com.kaolafm.opensdk.api.music.qq.model.BaseMusicResult) -> onSuccess
    256:257:void onError(ApiException) -> onError
    248:248:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.api.music.qq.QQMusicRequest$3 -> com.kaolafm.opensdk.api.music.qq.QQMusicRequest$3:
    HttpCallback val$callback -> val$callback
    com.kaolafm.opensdk.api.music.qq.QQMusicRequest this$0 -> this$0
    281:281:void <init>(com.kaolafm.opensdk.api.music.qq.QQMusicRequest,HttpCallback) -> <init>
    284:296:void onSuccess(java.util.List) -> onSuccess
    300:303:void onError(ApiException) -> onError
    281:281:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.api.music.qq.QQMusicRequest_Factory -> com.kaolafm.opensdk.api.music.qq.QQMusicRequest_Factory:
    javax.inject.Provider mRepositoryManagerProvider -> mRepositoryManagerProvider
    javax.inject.Provider mRetrofitUrlManagerProvider -> mRetrofitUrlManagerProvider
    javax.inject.Provider mProfileLazyProvider -> mProfileLazyProvider
    javax.inject.Provider mGsonLazyProvider -> mGsonLazyProvider
    javax.inject.Provider mAccessTokenManagerLazyProvider -> mAccessTokenManagerLazyProvider
    javax.inject.Provider mQQMusicCommonParamsProvider -> mQQMusicCommonParamsProvider
    javax.inject.Provider mWxRefreshParamsLazyProvider -> mWxRefreshParamsLazyProvider
    javax.inject.Provider mQQRefreshParamsLazyProvider -> mQQRefreshParamsLazyProvider
    javax.inject.Provider mQQUserInfoParamsLazyProvider -> mQQUserInfoParamsLazyProvider
    43:53:void <init>(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    57:57:com.kaolafm.opensdk.api.music.qq.QQMusicRequest get() -> get
    79:97:com.kaolafm.opensdk.api.music.qq.QQMusicRequest provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> provideInstance
    110:110:com.kaolafm.opensdk.api.music.qq.QQMusicRequest_Factory create(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> create
    123:123:com.kaolafm.opensdk.api.music.qq.QQMusicRequest newQQMusicRequest() -> newQQMusicRequest
    15:15:java.lang.Object get() -> get
com.kaolafm.opensdk.api.music.qq.QQMusicRequest_MembersInjector -> com.kaolafm.opensdk.api.music.qq.QQMusicRequest_MembersInjector:
    javax.inject.Provider mRepositoryManagerProvider -> mRepositoryManagerProvider
    javax.inject.Provider mRetrofitUrlManagerProvider -> mRetrofitUrlManagerProvider
    javax.inject.Provider mProfileLazyProvider -> mProfileLazyProvider
    javax.inject.Provider mGsonLazyProvider -> mGsonLazyProvider
    javax.inject.Provider mAccessTokenManagerLazyProvider -> mAccessTokenManagerLazyProvider
    javax.inject.Provider mQQMusicCommonParamsProvider -> mQQMusicCommonParamsProvider
    javax.inject.Provider mWxRefreshParamsLazyProvider -> mWxRefreshParamsLazyProvider
    javax.inject.Provider mQQRefreshParamsLazyProvider -> mQQRefreshParamsLazyProvider
    javax.inject.Provider mQQUserInfoParamsLazyProvider -> mQQUserInfoParamsLazyProvider
    43:53:void <init>(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    65:65:dagger.MembersInjector create(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> create
    79:92:void injectMembers(com.kaolafm.opensdk.api.music.qq.QQMusicRequest) -> injectMembers
    96:97:void injectMQQMusicCommonParams(com.kaolafm.opensdk.api.music.qq.QQMusicRequest,javax.inject.Provider) -> injectMQQMusicCommonParams
    101:102:void injectMWxRefreshParamsLazy(com.kaolafm.opensdk.api.music.qq.QQMusicRequest,javax.inject.Provider) -> injectMWxRefreshParamsLazy
    106:107:void injectMQQRefreshParamsLazy(com.kaolafm.opensdk.api.music.qq.QQMusicRequest,javax.inject.Provider) -> injectMQQRefreshParamsLazy
    111:112:void injectMQQUserInfoParamsLazy(com.kaolafm.opensdk.api.music.qq.QQMusicRequest,javax.inject.Provider) -> injectMQQUserInfoParamsLazy
    15:15:void injectMembers(java.lang.Object) -> injectMembers
com.kaolafm.opensdk.api.music.qq.QQMusicTokenInterceptor -> com.kaolafm.opensdk.api.music.qq.QQMusicTokenInterceptor:
    com.kaolafm.opensdk.account.token.AccessTokenManager mTokenManager -> mTokenManager
    com.kaolafm.opensdk.api.music.qq.QQMusicRequest mMusicRequest -> mMusicRequest
    42:43:void <init>() -> <init>
    47:73:okhttp3.Response intercept(okhttp3.Interceptor$Chain) -> intercept
    77:92:void refreshToken() -> refreshToken
    95:113:boolean isTokenExpires(okhttp3.Response) -> isTokenExpires
    117:118:java.nio.charset.Charset charset(okhttp3.ResponseBody) -> charset
    122:134:boolean isText(okhttp3.MediaType) -> isText
com.kaolafm.opensdk.api.music.qq.QQMusicTokenInterceptor_Factory -> com.kaolafm.opensdk.api.music.qq.QQMusicTokenInterceptor_Factory:
    javax.inject.Provider mTokenManagerProvider -> mTokenManagerProvider
    11:13:void <init>(javax.inject.Provider) -> <init>
    17:17:com.kaolafm.opensdk.api.music.qq.QQMusicTokenInterceptor get() -> get
    22:25:com.kaolafm.opensdk.api.music.qq.QQMusicTokenInterceptor provideInstance(javax.inject.Provider) -> provideInstance
    30:30:com.kaolafm.opensdk.api.music.qq.QQMusicTokenInterceptor_Factory create(javax.inject.Provider) -> create
    34:34:com.kaolafm.opensdk.api.music.qq.QQMusicTokenInterceptor newQQMusicTokenInterceptor() -> newQQMusicTokenInterceptor
    8:8:java.lang.Object get() -> get
com.kaolafm.opensdk.api.music.qq.QQMusicTokenInterceptor_MembersInjector -> com.kaolafm.opensdk.api.music.qq.QQMusicTokenInterceptor_MembersInjector:
    javax.inject.Provider mTokenManagerProvider -> mTokenManagerProvider
    13:15:void <init>(javax.inject.Provider) -> <init>
    19:19:dagger.MembersInjector create(javax.inject.Provider) -> create
    24:25:void injectMembers(com.kaolafm.opensdk.api.music.qq.QQMusicTokenInterceptor) -> injectMembers
    29:30:void injectMTokenManager(com.kaolafm.opensdk.api.music.qq.QQMusicTokenInterceptor,com.kaolafm.opensdk.account.token.AccessTokenManager) -> injectMTokenManager
    8:8:void injectMembers(java.lang.Object) -> injectMembers
com.kaolafm.opensdk.api.music.qq.model.AlbumResult -> com.kaolafm.opensdk.api.music.qq.model.AlbumResult:
    int total -> total
    int order -> order
    12:12:void <init>() -> <init>
    25:25:int getTotal() -> getTotal
    29:30:void setTotal(int) -> setTotal
    33:33:int getOrder() -> getOrder
    37:38:void setOrder(int) -> setOrder
com.kaolafm.opensdk.api.music.qq.model.BaseMusicResult -> com.kaolafm.opensdk.api.music.qq.model.BaseMusicResult:
    int subRet -> subRet
    java.lang.Object result -> result
    13:13:void <init>() -> <init>
    23:23:int getSubRet() -> getSubRet
    27:28:void setSubRet(int) -> setSubRet
    31:31:java.lang.Object getResult() -> getResult
    35:36:void setResult(java.lang.Object) -> setResult
com.kaolafm.opensdk.api.music.qq.model.CategoryDetail -> com.kaolafm.opensdk.api.music.qq.model.CategoryDetail:
    java.lang.String categoryDesc -> categoryDesc
    int categoryId -> categoryId
    java.lang.String categoryImgurl -> categoryImgurl
    int categoryIsHot -> categoryIsHot
    int categoryIsNew -> categoryIsNew
    int categoryIsParent -> categoryIsParent
    java.lang.String categoryName -> categoryName
    java.lang.String categorySharePic -> categorySharePic
    int categoryShowDetail -> categoryShowDetail
    int categoryShowType -> categoryShowType
    int groupId -> groupId
    12:12:void <init>() -> <init>
    62:62:java.lang.String getCategoryDesc() -> getCategoryDesc
    66:66:int getCategoryId() -> getCategoryId
    70:70:java.lang.String getCategoryImgurl() -> getCategoryImgurl
    74:74:int getCategoryIsHot() -> getCategoryIsHot
    78:78:int getCategoryIsNew() -> getCategoryIsNew
    82:82:int getCategoryIsParent() -> getCategoryIsParent
    86:86:java.lang.String getCategoryName() -> getCategoryName
    90:90:java.lang.String getCategorySharePic() -> getCategorySharePic
    94:94:int getCategoryShowDetail() -> getCategoryShowDetail
    98:98:int getCategoryShowType() -> getCategoryShowType
    102:102:int getGroupId() -> getGroupId
    106:107:void setCategoryDesc(java.lang.String) -> setCategoryDesc
    110:111:void setCategoryId(int) -> setCategoryId
    114:115:void setCategoryImgurl(java.lang.String) -> setCategoryImgurl
    118:119:void setCategoryIsHot(int) -> setCategoryIsHot
    122:123:void setCategoryIsNew(int) -> setCategoryIsNew
    126:127:void setCategoryIsParent(int) -> setCategoryIsParent
    130:131:void setCategoryName(java.lang.String) -> setCategoryName
    134:135:void setCategorySharePic(java.lang.String) -> setCategorySharePic
    138:139:void setCategoryShowDetail(int) -> setCategoryShowDetail
    142:143:void setCategoryShowType(int) -> setCategoryShowType
    146:147:void setGroupId(int) -> setGroupId
com.kaolafm.opensdk.api.music.qq.model.CategoryLabel -> com.kaolafm.opensdk.api.music.qq.model.CategoryLabel:
    java.util.List categoryList -> categoryList
    int groupId -> groupId
    java.lang.String groupName -> groupName
    12:12:void <init>() -> <init>
    30:30:java.util.List getCategoryList() -> getCategoryList
    34:34:int getGroupId() -> getGroupId
    38:38:java.lang.String getGroupName() -> getGroupName
    42:43:void setCategoryList(java.util.List) -> setCategoryList
    46:47:void setGroupId(int) -> setGroupId
    50:51:void setGroupName(java.lang.String) -> setGroupName
com.kaolafm.opensdk.api.music.qq.model.MusicAlbum -> com.kaolafm.opensdk.api.music.qq.model.MusicAlbum:
    java.lang.String albumDesc -> albumDesc
    int albumId -> albumId
    java.lang.String albumMid -> albumMid
    java.lang.String albumName -> albumName
    java.lang.String albumPic -> albumPic
    java.lang.String albumTranslatorName -> albumTranslatorName
    java.lang.String companyName -> companyName
    int listenNum -> listenNum
    java.lang.String publicTime -> publicTime
    int singerId -> singerId
    java.lang.String singerName -> singerName
    java.lang.String songIdList -> songIdList
    java.lang.String url -> url
    11:11:void <init>() -> <init>
    74:74:java.lang.String getAlbumDesc() -> getAlbumDesc
    78:78:int getAlbumId() -> getAlbumId
    82:82:java.lang.String getAlbumMid() -> getAlbumMid
    86:86:java.lang.String getAlbumName() -> getAlbumName
    90:90:java.lang.String getAlbumPic() -> getAlbumPic
    94:94:java.lang.String getAlbumTranslatorName() -> getAlbumTranslatorName
    98:98:java.lang.String getCompanyName() -> getCompanyName
    102:102:int getListenNum() -> getListenNum
    106:106:java.lang.String getPublicTime() -> getPublicTime
    110:110:int getSingerId() -> getSingerId
    114:114:java.lang.String getSingerName() -> getSingerName
    118:118:java.lang.String getSongIdList() -> getSongIdList
    122:122:java.lang.String getUrl() -> getUrl
    126:127:void setAlbumDesc(java.lang.String) -> setAlbumDesc
    130:131:void setAlbumId(int) -> setAlbumId
    134:135:void setAlbumMid(java.lang.String) -> setAlbumMid
    138:139:void setAlbumName(java.lang.String) -> setAlbumName
    142:143:void setAlbumPic(java.lang.String) -> setAlbumPic
    146:147:void setAlbumTranslatorName(java.lang.String) -> setAlbumTranslatorName
    150:151:void setCompanyName(java.lang.String) -> setCompanyName
    154:155:void setListenNum(int) -> setListenNum
    159:160:void setPublicTime(java.lang.String) -> setPublicTime
    163:164:void setSingerId(int) -> setSingerId
    167:168:void setSingerName(java.lang.String) -> setSingerName
    171:172:void setSongIdList(java.lang.String) -> setSongIdList
    175:176:void setUrl(java.lang.String) -> setUrl
com.kaolafm.opensdk.api.music.qq.model.MusicInfo -> com.kaolafm.opensdk.api.music.qq.model.MusicInfo:
    long id -> id
    java.lang.String name -> name
    java.lang.String cover -> cover
    long listenNum -> listenNum
    java.lang.String desc -> desc
    int type -> type
    java.lang.String[] songMids -> songMids
    long latestSongMid -> latestSongMid
    boolean isLyricOpen -> isLyricOpen
    12:12:void <init>() -> <init>
    61:61:long getId() -> getId
    65:66:void setId(long) -> setId
    69:69:java.lang.String getName() -> getName
    73:74:void setName(java.lang.String) -> setName
    77:77:java.lang.String getCover() -> getCover
    81:82:void setCover(java.lang.String) -> setCover
    85:85:long getListenNum() -> getListenNum
    89:90:void setListenNum(long) -> setListenNum
    93:93:java.lang.String getDesc() -> getDesc
    97:98:void setDesc(java.lang.String) -> setDesc
    101:101:int getType() -> getType
    105:106:void setType(int) -> setType
    109:109:java.lang.String[] getSongMids() -> getSongMids
    113:114:void setSongMids(java.lang.String[]) -> setSongMids
    117:117:long getLatestSongMid() -> getLatestSongMid
    121:122:void setLatestSongMid(long) -> setLatestSongMid
    125:125:boolean isLyricOpen() -> isLyricOpen
    129:130:void setLyricOpen(boolean) -> setLyricOpen
    133:142:void clear() -> clear
    146:153:java.lang.String toString() -> toString
com.kaolafm.opensdk.api.music.qq.model.MusicRadio -> com.kaolafm.opensdk.api.music.qq.model.MusicRadio:
    int listenNum -> listenNum
    int radioId -> radioId
    java.lang.String radioName -> radioName
    java.lang.String radioPic -> radioPic
    11:11:void <init>() -> <init>
    33:33:int getListenNum() -> getListenNum
    37:37:int getRadioId() -> getRadioId
    41:41:java.lang.String getRadioName() -> getRadioName
    45:45:java.lang.String getRadioPic() -> getRadioPic
    49:50:void setListenNum(int) -> setListenNum
    53:54:void setRadioId(int) -> setRadioId
    57:58:void setRadioName(java.lang.String) -> setRadioName
    61:62:void setRadioPic(java.lang.String) -> setRadioPic
com.kaolafm.opensdk.api.music.qq.model.MusicRadioCategory -> com.kaolafm.opensdk.api.music.qq.model.MusicRadioCategory:
    java.lang.String groupName -> groupName
    int groupTypeId -> groupTypeId
    java.util.List radioList -> radioList
    12:12:void <init>() -> <init>
    30:30:java.lang.String getGroupName() -> getGroupName
    34:34:int getGroupTypeId() -> getGroupTypeId
    38:38:java.util.List getRadioList() -> getRadioList
    42:43:void setGroupName(java.lang.String) -> setGroupName
    46:47:void setGroupTypeId(int) -> setGroupTypeId
    50:51:void setRadioList(java.util.List) -> setRadioList
com.kaolafm.opensdk.api.music.qq.model.MusicSearchResult -> com.kaolafm.opensdk.api.music.qq.model.MusicSearchResult:
    int curNum -> curNum
    int curPage -> curPage
    java.lang.String keyword -> keyword
    int totalNum -> totalNum
    11:11:void <init>() -> <init>
com.kaolafm.opensdk.api.music.qq.model.Singer -> com.kaolafm.opensdk.api.music.qq.model.Singer:
    java.lang.String country -> country
    int singerId -> singerId
    java.lang.String singerMid -> singerMid
    java.lang.String singerName -> singerName
    java.lang.String singerTitle -> singerTitle
    java.lang.String singerTranslatorName -> singerTranslatorName
    11:11:void <init>() -> <init>
    44:44:java.lang.String getCountry() -> getCountry
    48:48:int getSingerId() -> getSingerId
    52:52:java.lang.String getSingerMid() -> getSingerMid
    56:56:java.lang.String getSingerName() -> getSingerName
    60:60:java.lang.String getSingerTitle() -> getSingerTitle
    64:64:java.lang.String getSingerTranslatorName() -> getSingerTranslatorName
    68:69:void setCountry(java.lang.String) -> setCountry
    72:73:void setSingerId(int) -> setSingerId
    76:77:void setSingerMid(java.lang.String) -> setSingerMid
    80:81:void setSingerName(java.lang.String) -> setSingerName
    84:85:void setSingerTitle(java.lang.String) -> setSingerTitle
    88:89:void setSingerTranslatorName(java.lang.String) -> setSingerTranslatorName
com.kaolafm.opensdk.api.music.qq.model.SingerSongs -> com.kaolafm.opensdk.api.music.qq.model.SingerSongs:
    int albumSum -> albumSum
    java.lang.String area -> area
    int singerId -> singerId
    java.lang.String singerMid -> singerMid
    java.lang.String singerName -> singerName
    java.lang.String singerPic -> singerPic
    java.lang.String singerTranslatorName -> singerTranslatorName
    int songSum -> songSum
    12:12:void <init>() -> <init>
com.kaolafm.opensdk.api.music.qq.model.Song -> com.kaolafm.opensdk.api.music.qq.model.Song:
    java.lang.String albumPic150x150 -> albumPic150x150
    java.lang.String albumPic300x300 -> albumPic300x300
    java.lang.String albumPic500x500 -> albumPic500x500
    java.lang.String albumTitle -> albumTitle
    int copyright -> copyright
    int isonly -> isonly
    int mvId -> mvId
    int opiAlertid -> opiAlertid
    int opiMsgid -> opiMsgid
    java.util.List otherSingerList -> otherSingerList
    java.lang.String singerPic150x150 -> singerPic150x150
    java.lang.String singerPic300x300 -> singerPic300x300
    java.lang.String singerPic500x500 -> singerPic500x500
    java.lang.String singerTitle -> singerTitle
    java.lang.String songTitle -> songTitle
    int userOwnRule -> userOwnRule
    int albumId -> albumId
    java.lang.String albumMid -> albumMid
    java.lang.String albumName -> albumName
    java.lang.String albumPic -> albumPic
    java.lang.String genre -> genre
    int hot -> hot
    int isOnly -> isOnly
    int kSongId -> kSongId
    java.lang.String kSongMid -> kSongMid
    java.lang.String language -> language
    java.lang.String pingpong -> pingpong
    int playable -> playable
    java.lang.String publicTime -> publicTime
    java.lang.String recommendReason -> recommendReason
    int singerId -> singerId
    java.lang.String singerMid -> singerMid
    java.lang.String singerName -> singerName
    java.lang.String singerPic -> singerPic
    int sizeTry -> sizeTry
    java.lang.String songH5Url -> songH5Url
    long songId -> songId
    java.lang.String songMid -> songMid
    java.lang.String songName -> songName
    int songPlayTime -> songPlayTime
    java.lang.String songPlayUrl -> songPlayUrl
    java.lang.String songPlayUrlHq -> songPlayUrlHq
    java.lang.String songPlayUrlSq -> songPlayUrlSq
    java.lang.String songPlayUrlStandard -> songPlayUrlStandard
    int songSize -> songSize
    int songSizeHq -> songSizeHq
    int songSizeSq -> songSizeSq
    int songSizeStandard -> songSizeStandard
    int tryBegin -> tryBegin
    int tryEnd -> tryEnd
    int unplayableCode -> unplayableCode
    java.lang.String unplayableMsg -> unplayableMsg
    long timestamps -> timestamps
    252:253:void <init>() -> <init>
    256:256:int getAlbumId() -> getAlbumId
    260:260:java.lang.String getAlbumMid() -> getAlbumMid
    264:264:java.lang.String getAlbumName() -> getAlbumName
    268:268:java.lang.String getAlbumPic() -> getAlbumPic
    272:272:java.lang.String getAlbumPic150x150() -> getAlbumPic150x150
    276:276:java.lang.String getAlbumPic300x300() -> getAlbumPic300x300
    280:280:java.lang.String getAlbumPic500x500() -> getAlbumPic500x500
    284:284:java.lang.String getAlbumTitle() -> getAlbumTitle
    288:288:int getCopyright() -> getCopyright
    292:292:java.lang.String getGenre() -> getGenre
    296:296:int getHot() -> getHot
    300:300:int getIsOnly() -> getIsOnly
    304:304:int getIsonly() -> getIsonly
    308:308:int getKSongId() -> getKSongId
    312:312:java.lang.String getKSongMid() -> getKSongMid
    316:316:java.lang.String getLanguage() -> getLanguage
    320:320:int getMvId() -> getMvId
    324:324:int getOpiAlertid() -> getOpiAlertid
    328:328:int getOpiMsgid() -> getOpiMsgid
    332:332:java.util.List getOtherSingerList() -> getOtherSingerList
    336:336:java.lang.String getPingpong() -> getPingpong
    340:340:int getPlayable() -> getPlayable
    344:344:java.lang.String getPublicTime() -> getPublicTime
    348:348:java.lang.String getRecommendReason() -> getRecommendReason
    352:352:int getSingerId() -> getSingerId
    356:356:java.lang.String getSingerMid() -> getSingerMid
    360:360:java.lang.String getSingerName() -> getSingerName
    364:364:java.lang.String getSingerPic() -> getSingerPic
    368:368:java.lang.String getSingerPic150x150() -> getSingerPic150x150
    372:372:java.lang.String getSingerPic300x300() -> getSingerPic300x300
    376:376:java.lang.String getSingerPic500x500() -> getSingerPic500x500
    380:380:java.lang.String getSingerTitle() -> getSingerTitle
    384:384:int getSizeTry() -> getSizeTry
    388:388:java.lang.String getSongH5Url() -> getSongH5Url
    392:392:long getSongId() -> getSongId
    396:396:java.lang.String getSongMid() -> getSongMid
    400:400:java.lang.String getSongName() -> getSongName
    404:404:int getSongPlayTime() -> getSongPlayTime
    408:408:java.lang.String getSongPlayUrl() -> getSongPlayUrl
    412:412:java.lang.String getSongPlayUrlHq() -> getSongPlayUrlHq
    416:416:java.lang.String getSongPlayUrlSq() -> getSongPlayUrlSq
    420:420:java.lang.String getSongPlayUrlStandard() -> getSongPlayUrlStandard
    424:424:int getSongSize() -> getSongSize
    428:428:int getSongSizeHq() -> getSongSizeHq
    432:432:int getSongSizeSq() -> getSongSizeSq
    436:436:int getSongSizeStandard() -> getSongSizeStandard
    440:440:java.lang.String getSongTitle() -> getSongTitle
    444:444:int getTryBegin() -> getTryBegin
    448:448:int getTryEnd() -> getTryEnd
    452:453:void setAlbumId(int) -> setAlbumId
    456:457:void setAlbumMid(java.lang.String) -> setAlbumMid
    460:461:void setAlbumName(java.lang.String) -> setAlbumName
    464:465:void setAlbumPic(java.lang.String) -> setAlbumPic
    468:469:void setAlbumPic150(java.lang.String) -> setAlbumPic150
    472:473:void setAlbumPic300(java.lang.String) -> setAlbumPic300
    476:477:void setAlbumPic500(java.lang.String) -> setAlbumPic500
    480:481:void setAlbumTitle(java.lang.String) -> setAlbumTitle
    484:485:void setCopyright(int) -> setCopyright
    488:489:void setGenre(java.lang.String) -> setGenre
    492:493:void setHot(int) -> setHot
    496:497:void setIsOnly(int) -> setIsOnly
    500:501:void setIsonly(int) -> setIsonly
    504:505:void setKSongId(int) -> setKSongId
    508:509:void setKSongMid(java.lang.String) -> setKSongMid
    512:513:void setLanguage(java.lang.String) -> setLanguage
    516:517:void setMvId(int) -> setMvId
    520:521:void setOpiAlertid(int) -> setOpiAlertid
    524:525:void setOpiMsgid(int) -> setOpiMsgid
    528:529:void setOtherSingerList(java.util.List) -> setOtherSingerList
    532:533:void setPingpong(java.lang.String) -> setPingpong
    536:537:void setPlayable(int) -> setPlayable
    540:541:void setPublicTime(java.lang.String) -> setPublicTime
    544:545:void setRecommendReason(java.lang.String) -> setRecommendReason
    548:549:void setSingerId(int) -> setSingerId
    552:553:void setSingerMid(java.lang.String) -> setSingerMid
    556:557:void setSingerName(java.lang.String) -> setSingerName
    560:561:void setSingerPic(java.lang.String) -> setSingerPic
    564:565:void setSingerPic150(java.lang.String) -> setSingerPic150
    568:569:void setSingerPic300(java.lang.String) -> setSingerPic300
    572:573:void setSingerPic500(java.lang.String) -> setSingerPic500
    576:577:void setSingerTitle(java.lang.String) -> setSingerTitle
    580:581:void setSizeTry(int) -> setSizeTry
    584:585:void setSongH5Url(java.lang.String) -> setSongH5Url
    588:589:void setSongId(java.lang.Long) -> setSongId
    592:593:void setSongMid(java.lang.String) -> setSongMid
    596:597:void setSongName(java.lang.String) -> setSongName
    600:601:void setSongPlayTime(int) -> setSongPlayTime
    604:605:void setSongPlayUrl(java.lang.String) -> setSongPlayUrl
    608:609:void setSongPlayUrlHq(java.lang.String) -> setSongPlayUrlHq
    612:613:void setSongPlayUrlSq(java.lang.String) -> setSongPlayUrlSq
    616:617:void setSongPlayUrlStandard(java.lang.String) -> setSongPlayUrlStandard
    620:621:void setSongSize(int) -> setSongSize
    624:625:void setSongSizeHq(int) -> setSongSizeHq
    628:629:void setSongSizeSq(int) -> setSongSizeSq
    632:633:void setSongSizeStandard(int) -> setSongSizeStandard
    636:637:void setSongTitle(java.lang.String) -> setSongTitle
    640:641:void setTryBegin(int) -> setTryBegin
    644:645:void setTryEnd(int) -> setTryEnd
    648:648:int getUserOwnRule() -> getUserOwnRule
    652:653:void setUserOwnRule(int) -> setUserOwnRule
    656:656:int getkSongId() -> getkSongId
    660:661:void setkSongId(int) -> setkSongId
    664:664:java.lang.String getkSongMid() -> getkSongMid
    668:669:void setkSongMid(java.lang.String) -> setkSongMid
    672:672:int getUnplayableCode() -> getUnplayableCode
    676:677:void setUnplayableCode(int) -> setUnplayableCode
    680:680:java.lang.String getUnplayableMsg() -> getUnplayableMsg
    684:685:void setUnplayableMsg(java.lang.String) -> setUnplayableMsg
    688:688:long getTimestamps() -> getTimestamps
    692:693:void setTimestamps(long) -> setTimestamps
    696:707:com.kaolafm.sdk.core.mediaplayer.PlayItem transform2PlayItem() -> transform2PlayItem
com.kaolafm.opensdk.api.music.qq.model.SongCharts -> com.kaolafm.opensdk.api.music.qq.model.SongCharts:
    int groupId -> groupId
    java.lang.String groupName -> groupName
    java.util.List groupTopList -> groupTopList
    int groupType -> groupType
    12:12:void <init>() -> <init>
    167:167:int getGroupId() -> getGroupId
    171:171:java.lang.String getGroupName() -> getGroupName
    175:175:java.util.List getGroupTopList() -> getGroupTopList
    179:179:int getGroupType() -> getGroupType
    183:184:void setGroupId(int) -> setGroupId
    187:188:void setGroupName(java.lang.String) -> setGroupName
    191:192:void setGroupTopList(java.util.List) -> setGroupTopList
    195:196:void setGroupType(int) -> setGroupType
com.kaolafm.opensdk.api.music.qq.model.SongCharts$GroupTopListBean -> com.kaolafm.opensdk.api.music.qq.model.SongCharts$GroupTopListBean:
    int listenNum -> listenNum
    java.lang.String showTime -> showTime
    java.util.List songList -> songList
    java.lang.String topBannerPic -> topBannerPic
    java.lang.String topHeaderPic -> topHeaderPic
    int topId -> topId
    java.lang.String topName -> topName
    int topType -> topType
    15:15:void <init>() -> <init>
    83:83:int getListenNum() -> getListenNum
    87:87:java.lang.String getShowTime() -> getShowTime
    91:91:java.util.List getSongList() -> getSongList
    95:95:java.lang.String getTopBannerPic() -> getTopBannerPic
    99:99:java.lang.String getTopHeaderPic() -> getTopHeaderPic
    103:103:int getTopId() -> getTopId
    107:107:java.lang.String getTopName() -> getTopName
    111:111:int getTopType() -> getTopType
    115:116:void setListenNum(int) -> setListenNum
    119:120:void setShowTime(java.lang.String) -> setShowTime
    123:124:void setSongList(java.util.List) -> setSongList
    127:128:void setTopBannerPic(java.lang.String) -> setTopBannerPic
    131:132:void setTopHeaderPic(java.lang.String) -> setTopHeaderPic
    135:136:void setTopId(int) -> setTopId
    139:140:void setTopName(java.lang.String) -> setTopName
    143:144:void setTopType(int) -> setTopType
com.kaolafm.opensdk.api.music.qq.model.SongCharts$GroupTopListBean$SongBean -> com.kaolafm.opensdk.api.music.qq.model.SongCharts$GroupTopListBean$SongBean:
    java.lang.String singerName -> singerName
    java.lang.String songName -> songName
    17:17:void <init>() -> <init>
    31:31:java.lang.String getSingerName() -> getSingerName
    35:35:java.lang.String getSongName() -> getSongName
    39:40:void setSingerName(java.lang.String) -> setSingerName
    43:44:void setSongName(java.lang.String) -> setSongName
com.kaolafm.opensdk.api.music.qq.model.SongChartsResult -> com.kaolafm.opensdk.api.music.qq.model.SongChartsResult:
    int listenNum -> listenNum
    java.lang.String topBannerPic -> topBannerPic
    java.lang.String topDesc -> topDesc
    java.lang.String topHeaderPic -> topHeaderPic
    int topId -> topId
    java.lang.String topName -> topName
    int topType -> topType
    13:13:void <init>() -> <init>
    46:46:int getListenNum() -> getListenNum
    50:50:java.lang.String getTopBannerPic() -> getTopBannerPic
    54:54:java.lang.String getTopDesc() -> getTopDesc
    58:58:java.lang.String getTopHeaderPic() -> getTopHeaderPic
    62:62:int getTopId() -> getTopId
    66:66:java.lang.String getTopName() -> getTopName
    70:70:int getTopType() -> getTopType
    74:75:void setListenNum(int) -> setListenNum
    78:79:void setTopBannerPic(java.lang.String) -> setTopBannerPic
    82:83:void setTopDesc(java.lang.String) -> setTopDesc
    86:87:void setTopHeaderPic(java.lang.String) -> setTopHeaderPic
    90:91:void setTopId(int) -> setTopId
    94:95:void setTopName(java.lang.String) -> setTopName
    98:99:void setTopType(int) -> setTopType
com.kaolafm.opensdk.api.music.qq.model.SongMenu -> com.kaolafm.opensdk.api.music.qq.model.SongMenu:
    long commitTime -> commitTime
    long createTime -> createTime
    com.kaolafm.opensdk.api.music.qq.model.SongMenu$Creator creator -> creator
    long dissId -> dissId
    java.lang.String dissName -> dissName
    java.lang.String introduction -> introduction
    long listenNum -> listenNum
    long songNum -> songNum
    long updateTime -> updateTime
    java.lang.String picUrl -> picUrl
    int dissType -> dissType
    10:10:void <init>() -> <init>
    111:111:long getCommitTime() -> getCommitTime
    115:115:long getCreateTime() -> getCreateTime
    119:119:com.kaolafm.opensdk.api.music.qq.model.SongMenu$Creator getCreator() -> getCreator
    123:123:long getDissId() -> getDissId
    127:127:java.lang.String getDissName() -> getDissName
    131:131:java.lang.String getIntroduction() -> getIntroduction
    135:135:long getListenNum() -> getListenNum
    139:139:java.lang.String getPicUrl() -> getPicUrl
    143:144:void setCommitTime(long) -> setCommitTime
    147:148:void setCreateTime(long) -> setCreateTime
    151:152:void setCreator(com.kaolafm.opensdk.api.music.qq.model.SongMenu$Creator) -> setCreator
    155:156:void setDissId(long) -> setDissId
    159:160:void setDissName(java.lang.String) -> setDissName
    163:164:void setIntroduction(java.lang.String) -> setIntroduction
    167:168:void setListenNum(long) -> setListenNum
    171:172:void setPicUrl(java.lang.String) -> setPicUrl
    175:175:int getDissType() -> getDissType
    179:180:void setDissType(int) -> setDissType
    183:183:long getSongNum() -> getSongNum
    187:188:void setSongNum(long) -> setSongNum
    191:191:long getUpdateTime() -> getUpdateTime
    195:196:void setUpdateTime(long) -> setUpdateTime
com.kaolafm.opensdk.api.music.qq.model.SongMenu$Creator -> com.kaolafm.opensdk.api.music.qq.model.SongMenu$Creator:
    java.lang.String avatarUrl -> avatarUrl
    int isVip -> isVip
    java.lang.String name -> name
    long uin -> uin
    12:12:void <init>() -> <init>
    34:34:java.lang.String getAvatarUrl() -> getAvatarUrl
    38:38:int getIsVip() -> getIsVip
    42:42:java.lang.String getName() -> getName
    46:46:long getUin() -> getUin
    50:51:void setAvatarUrl(java.lang.String) -> setAvatarUrl
    54:55:void setIsVip(int) -> setIsVip
    58:59:void setName(java.lang.String) -> setName
    62:63:void setUin(long) -> setUin
com.kaolafm.opensdk.api.music.qq.model.SubcategoryLabel -> com.kaolafm.opensdk.api.music.qq.model.SubcategoryLabel:
    java.lang.String categoryDesc -> categoryDesc
    int categoryId -> categoryId
    java.lang.String categoryName -> categoryName
    java.lang.String categoryPic -> categoryPic
    java.lang.String categorySharePic -> categorySharePic
    int categoryShowType -> categoryShowType
    java.util.List subCategoryList -> subCategoryList
    12:12:void <init>() -> <init>
    46:46:java.lang.String getCategoryDesc() -> getCategoryDesc
    50:50:int getCategoryId() -> getCategoryId
    54:54:java.lang.String getCategoryName() -> getCategoryName
    58:58:java.lang.String getCategoryPic() -> getCategoryPic
    62:62:java.lang.String getCategorySharePic() -> getCategorySharePic
    66:66:int getCategoryShowType() -> getCategoryShowType
    70:70:java.util.List getSubCategoryList() -> getSubCategoryList
    74:75:void setCategoryDesc(java.lang.String) -> setCategoryDesc
    78:79:void setCategoryId(int) -> setCategoryId
    82:83:void setCategoryName(java.lang.String) -> setCategoryName
    86:87:void setCategoryPic(java.lang.String) -> setCategoryPic
    90:91:void setCategorySharePic(java.lang.String) -> setCategorySharePic
    94:95:void setCategoryShowType(int) -> setCategoryShowType
    98:99:void setSubCategoryList(java.util.List) -> setSubCategoryList
com.kaolafm.opensdk.api.music.qq.model.TencentLoginResult -> com.kaolafm.opensdk.api.music.qq.model.TencentLoginResult:
    java.lang.String qrCode -> qrCode
    long musicId -> musicId
    java.lang.String musicKey -> musicKey
    long refreshTime -> refreshTime
    com.kaolafm.opensdk.api.music.qq.model.TencentUserInfo userInfo -> userInfo
    java.lang.String token -> token
    java.lang.String openId -> openId
    java.lang.String refreshToken -> refreshToken
    java.lang.String unionId -> unionId
    java.lang.String appId -> appId
    int isLoginLost -> isLoginLost
    int loginType -> loginType
    13:13:void <init>() -> <init>
    123:123:int getLoginType() -> getLoginType
    127:128:void setLoginType(int) -> setLoginType
    131:131:long getMusicId() -> getMusicId
    135:135:java.lang.String getMusicKey() -> getMusicKey
    139:139:long getRefreshTime() -> getRefreshTime
    143:143:com.kaolafm.opensdk.api.music.qq.model.TencentUserInfo getUserInfo() -> getUserInfo
    147:147:java.lang.String getToken() -> getToken
    151:151:java.lang.String getOpenId() -> getOpenId
    155:155:java.lang.String getRefreshToken() -> getRefreshToken
    159:159:java.lang.String getQrCode() -> getQrCode
    163:163:java.lang.String getUnionId() -> getUnionId
    167:168:void setMusicId(long) -> setMusicId
    171:172:void setMusicKey(java.lang.String) -> setMusicKey
    175:176:void setRefreshTime(long) -> setRefreshTime
    179:180:void setUserInfo(com.kaolafm.opensdk.api.music.qq.model.TencentUserInfo) -> setUserInfo
    183:184:void setToken(java.lang.String) -> setToken
    187:188:void setOpenId(java.lang.String) -> setOpenId
    191:192:void setRefreshToken(java.lang.String) -> setRefreshToken
    195:196:void setQrCode(java.lang.String) -> setQrCode
    199:200:void setUnionId(java.lang.String) -> setUnionId
    203:203:int getIsLoginLost() -> getIsLoginLost
    207:208:void setIsLoginLost(int) -> setIsLoginLost
    211:211:java.lang.String getAppId() -> getAppId
    215:216:void setAppId(java.lang.String) -> setAppId
    220:220:java.lang.String toString() -> toString
com.kaolafm.opensdk.api.music.qq.model.TencentUserInfo -> com.kaolafm.opensdk.api.music.qq.model.TencentUserInfo:
    java.lang.String city -> city
    java.lang.String country -> country
    java.lang.String gender -> gender
    java.lang.String headImg -> headImg
    int isUserInfoLost -> isUserInfoLost
    java.lang.String nickname -> nickname
    java.lang.String province -> province
    java.lang.String qzoneImg -> qzoneImg
    int sex -> sex
    java.lang.String year -> year
    11:11:void <init>() -> <init>
    69:69:java.lang.String getCity() -> getCity
    73:73:java.lang.String getCountry() -> getCountry
    77:77:java.lang.String getGender() -> getGender
    81:81:java.lang.String getHeadImg() -> getHeadImg
    85:85:int getIsUserInfoLost() -> getIsUserInfoLost
    89:89:java.lang.String getNickname() -> getNickname
    93:93:java.lang.String getProvince() -> getProvince
    97:97:java.lang.String getQzoneImg() -> getQzoneImg
    101:101:int getSex() -> getSex
    105:105:java.lang.String getYear() -> getYear
    109:110:void setCity(java.lang.String) -> setCity
    113:114:void setCountry(java.lang.String) -> setCountry
    117:118:void setGender(java.lang.String) -> setGender
    121:122:void setHeadImg(java.lang.String) -> setHeadImg
    126:127:void setIsUserInfoLost(int) -> setIsUserInfoLost
    130:131:void setNickname(java.lang.String) -> setNickname
    134:135:void setProvince(java.lang.String) -> setProvince
    138:139:void setQzoneImg(java.lang.String) -> setQzoneImg
    142:143:void setSex(int) -> setSex
    146:147:void setYear(java.lang.String) -> setYear
com.kaolafm.opensdk.api.music.qq.model.VipInfo -> com.kaolafm.opensdk.api.music.qq.model.VipInfo:
    java.lang.String endTime -> endTime
    java.lang.String startTime -> startTime
    int vipFlag -> vipFlag
    java.lang.String vipName -> vipName
    java.lang.String vipPayPage -> vipPayPage
    java.lang.String vipTimeLeft -> vipTimeLeft
    11:11:void <init>() -> <init>
    40:40:java.lang.String getEndTime() -> getEndTime
    44:44:java.lang.String getStartTime() -> getStartTime
    48:49:void setEndTime(java.lang.String) -> setEndTime
    52:53:void setStartTime(java.lang.String) -> setStartTime
    56:56:int getVipFlag() -> getVipFlag
    60:61:void setVipFlag(int) -> setVipFlag
    64:64:java.lang.String getVipName() -> getVipName
    68:69:void setVipName(java.lang.String) -> setVipName
    72:72:java.lang.String getVipPayPage() -> getVipPayPage
    76:77:void setVipPayPage(java.lang.String) -> setVipPayPage
    80:80:java.lang.String getVipTimeLeft() -> getVipTimeLeft
    84:85:void setVipTimeLeft(java.lang.String) -> setVipTimeLeft
com.kaolafm.opensdk.api.operation.OperationRequest -> com.kaolafm.opensdk.api.operation.OperationRequest:
    com.kaolafm.opensdk.api.operation.OperationService mOperationService -> mOperationService
    43:45:void <init>() -> <init>
    57:58:void getCategoryTree(int,java.lang.String,java.util.Map,HttpCallback) -> getCategoryTree
    68:69:void getCategoryTree(int,java.lang.String,HttpCallback) -> getCategoryTree
    78:79:void getCategoryTree(int,HttpCallback) -> getCategoryTree
    92:93:void getCategoryRoot(int,java.lang.String,java.util.Map,HttpCallback) -> getCategoryRoot
    103:104:void getCategoryRoot(int,java.lang.String,HttpCallback) -> getCategoryRoot
    113:114:void getCategoryRoot(int,HttpCallback) -> getCategoryRoot
    125:126:void getSubcategoryList(java.lang.String,java.util.Map,HttpCallback) -> getSubcategoryList
    135:136:void getSubcategoryList(java.lang.String,HttpCallback) -> getSubcategoryList
    146:151:void getCategoryMemberNum(java.lang.String,java.util.Map,HttpCallback) -> getCategoryMemberNum
    159:160:void getCategoryMemberNum(java.lang.String,HttpCallback) -> getCategoryMemberNum
    172:176:void getCategoryMemberList(java.lang.String,int,int,HttpCallback) -> getCategoryMemberList
    188:190:void getColumnTree(boolean,java.lang.String,java.util.Map,HttpCallback) -> getColumnTree
    199:200:void getColumnTree(boolean,java.lang.String,HttpCallback) -> getColumnTree
    208:209:void getColumnTree(boolean,HttpCallback) -> getColumnTree
    216:217:void getColumnTree(HttpCallback) -> getColumnTree
    229:230:void getSubcolumnList(java.lang.String,java.lang.String,java.util.Map,HttpCallback) -> getSubcolumnList
    239:240:void getSubcolumnList(java.lang.String,java.lang.String,HttpCallback) -> getSubcolumnList
    248:249:void getSubcolumnList(java.lang.String,HttpCallback) -> getSubcolumnList
    260:261:void getColumnMemberList(java.lang.String,java.util.Map,HttpCallback) -> getColumnMemberList
    269:270:void getColumnMemberList(java.lang.String,HttpCallback) -> getColumnMemberList
    274:289:int getContentType(int) -> getContentType
    308:310:void getColumnList(java.lang.String,boolean,java.lang.String,java.lang.String,java.lang.String,java.util.Map,HttpCallback) -> getColumnList
    326:328:java.util.List getColumnListSync(java.lang.String,boolean,java.lang.String,java.lang.String,java.lang.String,java.util.Map) -> getColumnListSync
    339:341:void getColumnList(java.lang.String,boolean,HttpCallback) -> getColumnList
    351:351:java.util.List getColumnListSync(java.lang.String,boolean) -> getColumnListSync
    365:367:void getColumnList(java.lang.String,boolean,java.lang.String,HttpCallback) -> getColumnList
    379:379:java.util.List getColumnListSync(java.lang.String,boolean,java.lang.String) -> getColumnListSync
    401:419:void getCategoryList(java.lang.String,boolean,int,boolean,int,int,java.lang.String,java.lang.String,java.lang.String,java.util.Map,HttpCallback) -> getCategoryList
    431:433:void getCategoryList(java.lang.String,boolean,int,HttpCallback) -> getCategoryList
    447:449:void getCategoryList(java.lang.String,boolean,int,int,int,HttpCallback) -> getCategoryList
    465:468:void getCategoryList(java.lang.String,boolean,int,boolean,int,int,HttpCallback) -> getCategoryList
    479:480:void getCategoryList(java.lang.String,boolean,HttpCallback) -> getCategoryList
    489:490:void getCategoryInfo(java.lang.String,HttpCallback) -> getCategoryInfo
    500:505:void getCategoryMemberList(java.lang.String,HttpCallback) -> getCategoryMemberList
    509:517:okhttp3.RequestBody getColumnRequestBody(java.lang.String,boolean,java.lang.String,java.lang.String,java.lang.String,java.util.Map) -> getColumnRequestBody
    502:503:java.util.List lambda$getCategoryMemberList$1(com.kaolafm.opensdk.api.BaseResult) -> lambda$getCategoryMemberList$1
    148:149:java.lang.Integer lambda$getCategoryMemberNum$0(com.kaolafm.opensdk.api.BaseResult) -> lambda$getCategoryMemberNum$0
com.kaolafm.opensdk.api.operation.OperationRequest$ContentType -> com.kaolafm.opensdk.api.operation.OperationRequest$a:
com.kaolafm.opensdk.api.operation.OperationRequest_Factory -> com.kaolafm.opensdk.api.operation.OperationRequest_Factory:
    javax.inject.Provider mRepositoryManagerProvider -> mRepositoryManagerProvider
    javax.inject.Provider mRetrofitUrlManagerProvider -> mRetrofitUrlManagerProvider
    javax.inject.Provider mProfileLazyProvider -> mProfileLazyProvider
    javax.inject.Provider mGsonLazyProvider -> mGsonLazyProvider
    javax.inject.Provider mAccessTokenManagerLazyProvider -> mAccessTokenManagerLazyProvider
    30:36:void <init>(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    40:40:com.kaolafm.opensdk.api.operation.OperationRequest get() -> get
    54:64:com.kaolafm.opensdk.api.operation.OperationRequest provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> provideInstance
    73:73:com.kaolafm.opensdk.api.operation.OperationRequest_Factory create(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> create
    82:82:com.kaolafm.opensdk.api.operation.OperationRequest newOperationRequest() -> newOperationRequest
    14:14:java.lang.Object get() -> get
com.kaolafm.opensdk.api.operation.OperationRequest_MembersInjector -> com.kaolafm.opensdk.api.operation.OperationRequest_MembersInjector:
    javax.inject.Provider mRepositoryManagerProvider -> mRepositoryManagerProvider
    javax.inject.Provider mRetrofitUrlManagerProvider -> mRetrofitUrlManagerProvider
    javax.inject.Provider mProfileLazyProvider -> mProfileLazyProvider
    javax.inject.Provider mGsonLazyProvider -> mGsonLazyProvider
    javax.inject.Provider mAccessTokenManagerLazyProvider -> mAccessTokenManagerLazyProvider
    30:36:void <init>(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    44:44:dagger.MembersInjector create(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> create
    54:63:void injectMembers(com.kaolafm.opensdk.api.operation.OperationRequest) -> injectMembers
    14:14:void injectMembers(java.lang.Object) -> injectMembers
com.kaolafm.opensdk.api.operation.OperationSearchParameterDTO -> com.kaolafm.opensdk.api.operation.OperationSearchParameterDTO:
    java.lang.Integer isRecursive -> isRecursive
    java.lang.String parentCode -> parentCode
    java.lang.String lng -> lng
    java.lang.String lat -> lat
    java.lang.String zone -> zone
    java.lang.Integer contentType -> contentType
    java.util.Map extInfo -> extInfo
    java.lang.Integer withMembers -> withMembers
    java.lang.Integer pageNum -> pageNum
    java.lang.Integer pageSize -> pageSize
    8:8:void <init>() -> <init>
    32:32:java.lang.Integer getContentType() -> getContentType
    36:37:void setContentType(java.lang.Integer) -> setContentType
    40:40:java.lang.String getZone() -> getZone
    44:45:void setZone(java.lang.String) -> setZone
    48:48:java.lang.String getLat() -> getLat
    52:53:void setLat(java.lang.String) -> setLat
    56:56:java.lang.String getLng() -> getLng
    60:61:void setLng(java.lang.String) -> setLng
    64:64:java.util.Map getExtInfo() -> getExtInfo
    68:69:void setExtInfo(java.util.Map) -> setExtInfo
    72:72:java.lang.String getParentCode() -> getParentCode
    76:77:void setParentCode(java.lang.String) -> setParentCode
    80:80:java.lang.Integer getIsRecursive() -> getIsRecursive
    84:85:void setIsRecursive(java.lang.Integer) -> setIsRecursive
    88:88:java.lang.Integer getWithMembers() -> getWithMembers
    92:93:void setWithMembers(java.lang.Integer) -> setWithMembers
    96:96:java.lang.Integer getPageNum() -> getPageNum
    100:101:void setPageNum(java.lang.Integer) -> setPageNum
    104:104:java.lang.Integer getPageSize() -> getPageSize
    108:109:void setPageSize(java.lang.Integer) -> setPageSize
    113:113:java.lang.String toString() -> toString
com.kaolafm.opensdk.api.operation.OperationService -> com.kaolafm.opensdk.api.operation.a:
    io.reactivex.Single getCategoryTree(int,java.lang.String,java.util.Map) -> getCategoryTree
    io.reactivex.Single getCategoryRoot(int,java.lang.String,java.util.Map) -> getCategoryRoot
    io.reactivex.Single getSubcategoryList(java.lang.String,java.util.Map) -> getSubcategoryList
    io.reactivex.Single getCategoryMemberNum(java.lang.String,java.util.Map) -> getCategoryMemberNum
    io.reactivex.Single getCategoryMemberList(java.lang.String,int,int) -> getCategoryMemberList
    io.reactivex.Single getColumnTree(int,java.lang.String,java.util.Map) -> getColumnTree
    io.reactivex.Single getSubcolumnList(java.lang.String,java.lang.String,java.util.Map) -> getSubcolumnList
    io.reactivex.Single getColumnMemberList(java.lang.String,java.util.Map) -> getColumnMemberList
    io.reactivex.Single getColumnList(okhttp3.RequestBody) -> getColumnList
    retrofit2.Call getColumnListSync(okhttp3.RequestBody) -> getColumnListSync
    io.reactivex.Single getCategoryList(okhttp3.RequestBody) -> getCategoryList
    io.reactivex.Single getCategoryInfo(java.lang.String) -> getCategoryInfo
    io.reactivex.Single getCategoryMemberList(java.lang.String) -> getCategoryMemberList
com.kaolafm.opensdk.api.operation.model.ImageFile -> com.kaolafm.opensdk.api.operation.model.ImageFile:
    java.lang.String KEY_ICON -> KEY_ICON
    java.lang.String KEY_COVER -> KEY_COVER
    java.lang.String url -> url
    int width -> width
    int height -> height
    java.lang.String type -> type
    8:8:void <init>() -> <init>
    32:32:java.lang.String getType() -> getType
    36:37:void setType(java.lang.String) -> setType
    40:40:java.lang.String getUrl() -> getUrl
    44:45:void setUrl(java.lang.String) -> setUrl
    48:48:int getWidth() -> getWidth
    52:53:void setWidth(int) -> setWidth
    56:56:int getHeight() -> getHeight
    60:61:void setHeight(int) -> setHeight
    66:66:java.lang.String toString() -> toString
com.kaolafm.opensdk.api.operation.model.category.AlbumCategoryMember -> com.kaolafm.opensdk.api.operation.model.category.AlbumCategoryMember:
    long albumId -> albumId
    int playTimes -> playTimes
    7:7:void <init>() -> <init>
    16:16:long getAlbumId() -> getAlbumId
    20:21:void setAlbumId(long) -> setAlbumId
    24:24:int getPlayTimes() -> getPlayTimes
    28:29:void setPlayTimes(int) -> setPlayTimes
    33:36:java.lang.String toString() -> toString
com.kaolafm.opensdk.api.operation.model.category.BroadcastCategoryMember -> com.kaolafm.opensdk.api.operation.model.category.BroadcastCategoryMember:
    long broadcastId -> broadcastId
    int playTimes -> playTimes
    6:6:void <init>() -> <init>
    15:15:long getBroadcastId() -> getBroadcastId
    19:20:void setBroadcastId(long) -> setBroadcastId
    23:23:int getPlayTimes() -> getPlayTimes
    27:28:void setPlayTimes(int) -> setPlayTimes
    32:32:java.lang.String toString() -> toString
com.kaolafm.opensdk.api.operation.model.category.Category -> com.kaolafm.opensdk.api.operation.model.category.Category:
    java.lang.String code -> code
    java.lang.String type -> type
    java.lang.String name -> name
    java.lang.String description -> description
    int contentType -> contentType
    java.util.List childCategories -> childCategories
    java.util.Map imageFiles -> imageFiles
    java.util.Map extInfo -> extInfo
    11:11:void <init>() -> <init>
    38:38:java.lang.String getCode() -> getCode
    42:43:void setCode(java.lang.String) -> setCode
    46:46:java.lang.String getType() -> getType
    50:51:void setType(java.lang.String) -> setType
    54:54:java.lang.String getName() -> getName
    58:59:void setName(java.lang.String) -> setName
    62:62:java.lang.String getDescription() -> getDescription
    66:67:void setDescription(java.lang.String) -> setDescription
    70:70:java.lang.Integer getContentType() -> getContentType
    74:75:void setContentType(java.lang.Integer) -> setContentType
    78:78:java.util.List getChildCategories() -> getChildCategories
    82:83:void setChildCategories(java.util.List) -> setChildCategories
    86:86:java.util.Map getImageFiles() -> getImageFiles
    90:91:void setImageFiles(java.util.Map) -> setImageFiles
    94:94:java.util.Map getExtInfo() -> getExtInfo
    98:99:void setExtInfo(java.util.Map) -> setExtInfo
    104:104:java.lang.String toString() -> toString
com.kaolafm.opensdk.api.operation.model.category.CategoryMember -> com.kaolafm.opensdk.api.operation.model.category.CategoryMember:
    java.lang.String code -> code
    java.lang.String title -> title
    java.lang.String subtitle -> subtitle
    java.lang.String description -> description
    java.lang.String type -> type
    java.util.Map imageFiles -> imageFiles
    java.util.Map extInfo -> extInfo
    12:12:void <init>() -> <init>
    36:36:java.lang.String getCode() -> getCode
    40:41:void setCode(java.lang.String) -> setCode
    44:44:java.lang.String getTitle() -> getTitle
    48:49:void setTitle(java.lang.String) -> setTitle
    52:52:java.lang.String getSubtitle() -> getSubtitle
    56:57:void setSubtitle(java.lang.String) -> setSubtitle
    60:60:java.lang.String getDescription() -> getDescription
    64:65:void setDescription(java.lang.String) -> setDescription
    68:68:java.lang.String getType() -> getType
    72:73:void setType(java.lang.String) -> setType
    76:76:java.util.Map getImageFiles() -> getImageFiles
    80:81:void setImageFiles(java.util.Map) -> setImageFiles
    84:84:java.util.Map getExtInfo() -> getExtInfo
    88:89:void setExtInfo(java.util.Map) -> setExtInfo
    93:93:java.lang.String toString() -> toString
com.kaolafm.opensdk.api.operation.model.category.LeafCategory -> com.kaolafm.opensdk.api.operation.model.category.LeafCategory:
    java.util.List categoryMembers -> categoryMembers
    8:8:void <init>() -> <init>
    15:17:java.lang.String toString() -> toString
    21:21:java.util.List getCategoryMembers() -> getCategoryMembers
    25:26:void setCategoryMembers(java.util.List) -> setCategoryMembers
com.kaolafm.opensdk.api.operation.model.category.LiveProgramCategoryMember -> com.kaolafm.opensdk.api.operation.model.category.LiveProgramCategoryMember:
    long liveProgramId -> liveProgramId
    6:6:void <init>() -> <init>
    13:15:java.lang.String toString() -> toString
    19:19:long getLiveProgramId() -> getLiveProgramId
    23:24:void setLiveProgramId(long) -> setLiveProgramId
com.kaolafm.opensdk.api.operation.model.category.MemberNum -> com.kaolafm.opensdk.api.operation.model.category.MemberNum:
    java.lang.String code -> code
    java.lang.String membersNum -> membersNum
    9:9:void <init>() -> <init>
    23:23:java.lang.String getCode() -> getCode
    27:27:java.lang.String getMembersNum() -> getMembersNum
    31:32:void setCode(java.lang.String) -> setCode
    35:36:void setMembersNum(java.lang.String) -> setMembersNum
com.kaolafm.opensdk.api.operation.model.category.RadioCategoryMember -> com.kaolafm.opensdk.api.operation.model.category.RadioCategoryMember:
    long radioId -> radioId
    int playTimes -> playTimes
    6:6:void <init>() -> <init>
    15:15:long getRadioId() -> getRadioId
    19:20:void setRadioId(long) -> setRadioId
    23:23:int getPlayTimes() -> getPlayTimes
    27:28:void setPlayTimes(int) -> setPlayTimes
    32:32:java.lang.String toString() -> toString
com.kaolafm.opensdk.api.operation.model.category.RadioQQMusicCategoryMember -> com.kaolafm.opensdk.api.operation.model.category.RadioQQMusicCategoryMember:
    long radioQQMusicId -> radioQQMusicId
    int oldId -> oldId
    int radioQQMusicType -> radioQQMusicType
    6:6:void <init>() -> <init>
    17:17:long getRadioQQMusicId() -> getRadioQQMusicId
    21:22:void setRadioQQMusicId(long) -> setRadioQQMusicId
    25:25:int getOldId() -> getOldId
    29:30:void setOldId(int) -> setOldId
    33:33:int getRadioQQMusicType() -> getRadioQQMusicType
    37:38:void setRadioQQMusicType(int) -> setRadioQQMusicType
    42:42:java.lang.String toString() -> toString
com.kaolafm.opensdk.api.operation.model.column.AlbumDetailColumnMember -> com.kaolafm.opensdk.api.operation.model.column.AlbumDetailColumnMember:
    long albumId -> albumId
    int playTimes -> playTimes
    6:6:void <init>() -> <init>
    15:15:long getAlbumId() -> getAlbumId
    19:20:void setAlbumId(long) -> setAlbumId
    23:23:int getPlayTimes() -> getPlayTimes
    27:28:void setPlayTimes(int) -> setPlayTimes
    32:32:java.lang.String toString() -> toString
com.kaolafm.opensdk.api.operation.model.column.AudioDetailColumnMember -> com.kaolafm.opensdk.api.operation.model.column.AudioDetailColumnMember:
    long audioId -> audioId
    int playTimes -> playTimes
    6:6:void <init>() -> <init>
    15:15:long getAudioId() -> getAudioId
    19:20:void setAudioId(long) -> setAudioId
    23:23:int getPlayTimes() -> getPlayTimes
    27:28:void setPlayTimes(int) -> setPlayTimes
    32:32:java.lang.String toString() -> toString
com.kaolafm.opensdk.api.operation.model.column.BroadcastDetailColumnMember -> com.kaolafm.opensdk.api.operation.model.column.BroadcastDetailColumnMember:
    long broadcastId -> broadcastId
    java.lang.String freq -> freq
    int playTimes -> playTimes
    6:6:void <init>() -> <init>
    18:18:long getBroadcastId() -> getBroadcastId
    22:23:void setBroadcastId(long) -> setBroadcastId
    26:26:int getPlayTimes() -> getPlayTimes
    30:31:void setPlayTimes(int) -> setPlayTimes
    35:35:java.lang.String getFreq() -> getFreq
    39:40:void setFreq(java.lang.String) -> setFreq
    44:44:java.lang.String toString() -> toString
com.kaolafm.opensdk.api.operation.model.column.CategoryColumnMember -> com.kaolafm.opensdk.api.operation.model.column.CategoryColumnMember:
    java.lang.String categoryCode -> categoryCode
    int contentType -> contentType
    6:6:void <init>() -> <init>
    15:15:java.lang.String getCategoryCode() -> getCategoryCode
    19:20:void setCategoryCode(java.lang.String) -> setCategoryCode
    23:23:int getContentType() -> getContentType
    27:28:void setContentType(int) -> setContentType
    32:35:java.lang.String toString() -> toString
com.kaolafm.opensdk.api.operation.model.column.Column -> com.kaolafm.opensdk.api.operation.model.column.Column:
    int forwardToMore -> forwardToMore
    com.kaolafm.opensdk.api.operation.model.column.ColumnMember moreColumnMember -> moreColumnMember
    java.util.List columnMembers -> columnMembers
    8:8:void <init>() -> <init>
    20:20:int getForwardToMore() -> getForwardToMore
    24:25:void setForwardToMore(int) -> setForwardToMore
    28:28:com.kaolafm.opensdk.api.operation.model.column.ColumnMember getMoreColumnMember() -> getMoreColumnMember
    32:33:void setMoreColumnMember(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> setMoreColumnMember
    36:36:java.util.List getColumnMembers() -> getColumnMembers
    40:41:void setColumnMembers(java.util.List) -> setColumnMembers
    45:49:java.lang.String toString() -> toString
com.kaolafm.opensdk.api.operation.model.column.ColumnGrp -> com.kaolafm.opensdk.api.operation.model.column.ColumnGrp:
    java.lang.String code -> code
    java.lang.String title -> title
    java.lang.String subtitle -> subtitle
    java.lang.String description -> description
    java.util.Map imageFiles -> imageFiles
    java.util.Map extInfo -> extInfo
    java.lang.String type -> type
    java.util.List childColumns -> childColumns
    11:11:void <init>() -> <init>
    38:39:void setType(java.lang.String) -> setType
    42:42:java.lang.String getType() -> getType
    46:46:java.lang.String getCode() -> getCode
    50:51:void setCode(java.lang.String) -> setCode
    54:54:java.lang.String getTitle() -> getTitle
    58:59:void setTitle(java.lang.String) -> setTitle
    62:62:java.lang.String getSubtitle() -> getSubtitle
    66:67:void setSubtitle(java.lang.String) -> setSubtitle
    70:70:java.lang.String getDescription() -> getDescription
    74:75:void setDescription(java.lang.String) -> setDescription
    78:78:java.util.Map getExtInfo() -> getExtInfo
    82:83:void setExtInfo(java.util.Map) -> setExtInfo
    86:86:java.util.Map getImageFiles() -> getImageFiles
    90:91:void setImageFiles(java.util.Map) -> setImageFiles
    94:94:java.util.List getChildColumns() -> getChildColumns
    98:99:void setChildColumns(java.util.List) -> setChildColumns
    103:103:java.lang.String toString() -> toString
com.kaolafm.opensdk.api.operation.model.column.ColumnMember -> com.kaolafm.opensdk.api.operation.model.column.ColumnMember:
    java.lang.String code -> code
    java.lang.String title -> title
    java.lang.String subtitle -> subtitle
    java.lang.String description -> description
    int cornerMark -> cornerMark
    java.util.Map imageFiles -> imageFiles
    java.util.Map extInfo -> extInfo
    java.lang.String type -> type
    12:12:void <init>() -> <init>
    39:39:java.lang.String getType() -> getType
    43:44:void setType(java.lang.String) -> setType
    47:47:java.lang.String getCode() -> getCode
    51:52:void setCode(java.lang.String) -> setCode
    55:55:java.lang.String getTitle() -> getTitle
    59:60:void setTitle(java.lang.String) -> setTitle
    63:63:java.lang.String getSubtitle() -> getSubtitle
    67:68:void setSubtitle(java.lang.String) -> setSubtitle
    71:71:java.lang.String getDescription() -> getDescription
    75:76:void setDescription(java.lang.String) -> setDescription
    79:79:int getCornerMark() -> getCornerMark
    83:84:void setCornerMark(int) -> setCornerMark
    87:87:java.util.Map getImageFiles() -> getImageFiles
    91:92:void setImageFiles(java.util.Map) -> setImageFiles
    95:95:java.util.Map getExtInfo() -> getExtInfo
    99:100:void setExtInfo(java.util.Map) -> setExtInfo
    103:106:com.kaolafm.opensdk.api.operation.model.ImageFile getIcon() -> getIcon
    111:111:java.lang.String toString() -> toString
com.kaolafm.opensdk.api.operation.model.column.LiveProgramDetailColumnMember -> com.kaolafm.opensdk.api.operation.model.column.LiveProgramDetailColumnMember:
    long liveProgramId -> liveProgramId
    6:6:void <init>() -> <init>
    12:12:long getLiveProgramId() -> getLiveProgramId
    16:17:void setLiveProgramId(long) -> setLiveProgramId
    21:23:java.lang.String toString() -> toString
com.kaolafm.opensdk.api.operation.model.column.RadioDetailColumnMember -> com.kaolafm.opensdk.api.operation.model.column.RadioDetailColumnMember:
    long radioId -> radioId
    int playTimes -> playTimes
    6:6:void <init>() -> <init>
    15:15:long getRadioId() -> getRadioId
    19:20:void setRadioId(long) -> setRadioId
    23:23:int getPlayTimes() -> getPlayTimes
    27:28:void setPlayTimes(int) -> setPlayTimes
    32:32:java.lang.String toString() -> toString
com.kaolafm.opensdk.api.operation.model.column.RadioQQMusicDetailColumnMember -> com.kaolafm.opensdk.api.operation.model.column.RadioQQMusicDetailColumnMember:
    long radioQQMusicId -> radioQQMusicId
    int oldId -> oldId
    int radioQQMusicType -> radioQQMusicType
    6:6:void <init>() -> <init>
    17:17:long getRadioQQMusicId() -> getRadioQQMusicId
    21:22:void setRadioQQMusicId(long) -> setRadioQQMusicId
    25:25:int getOldId() -> getOldId
    29:30:void setOldId(int) -> setOldId
    33:33:int getRadioQQMusicType() -> getRadioQQMusicType
    37:38:void setRadioQQMusicType(int) -> setRadioQQMusicType
    42:42:java.lang.String toString() -> toString
com.kaolafm.opensdk.api.operation.model.column.SearchResultColumnMember -> com.kaolafm.opensdk.api.operation.model.column.SearchResultColumnMember:
    java.lang.String keyword -> keyword
    6:6:void <init>() -> <init>
    12:12:java.lang.String getKeyword() -> getKeyword
    16:17:void setKeyword(java.lang.String) -> setKeyword
    21:23:java.lang.String toString() -> toString
com.kaolafm.opensdk.api.operation.model.column.WebViewColumnMember -> com.kaolafm.opensdk.api.operation.model.column.WebViewColumnMember:
    java.lang.String url -> url
    6:6:void <init>() -> <init>
    12:12:java.lang.String getUrl() -> getUrl
    16:17:void setUrl(java.lang.String) -> setUrl
    21:23:java.lang.String toString() -> toString
com.kaolafm.opensdk.api.personalise.PersonalizedRequest -> com.kaolafm.opensdk.api.personalise.PersonalizedRequest:
    com.kaolafm.opensdk.api.personalise.PersonalizedService mPersonalizedService -> mPersonalizedService
    24:26:void <init>() -> <init>
    34:38:void getInterestTagList(HttpCallback) -> getInterestTagList
    48:49:void saveUserAttribute(java.lang.String,int,HttpCallback) -> saveUserAttribute
    58:59:void saveInterestTags(java.lang.String,HttpCallback) -> saveInterestTags
    74:84:void saveThirdUser(java.lang.String,java.lang.String,java.lang.String,int,int,java.lang.String,HttpCallback) -> saveThirdUser
    95:102:void saveDeviceInfo(int,java.lang.String,java.lang.String,HttpCallback) -> saveDeviceInfo
    105:106:boolean status(com.kaolafm.opensdk.api.BaseResult) -> status
com.kaolafm.opensdk.api.personalise.PersonalizedService -> com.kaolafm.opensdk.api.personalise.a:
    io.reactivex.Single getInterestTagListUnlogined() -> getInterestTagListUnlogined
    io.reactivex.Single getInterestTagListLogined() -> getInterestTagListLogined
    io.reactivex.Single saveUserAttribute(java.lang.String,int) -> saveUserAttribute
    io.reactivex.Single saveInterestTag(java.lang.String) -> saveInterestTag
    io.reactivex.Single saveThirdUser(okhttp3.RequestBody) -> saveThirdUser
    io.reactivex.Single saveDeviceInfo(okhttp3.RequestBody) -> saveDeviceInfo
com.kaolafm.opensdk.api.personalise.internal.Status -> com.kaolafm.opensdk.api.personalise.internal.Status:
    int SUCCESS -> SUCCESS
    int status -> status
    7:7:void <init>() -> <init>
    14:14:int getStatus() -> getStatus
    18:19:void setStatus(int) -> setStatus
com.kaolafm.opensdk.api.personalise.model.InterestTag -> com.kaolafm.opensdk.api.personalise.model.InterestTag:
    java.lang.String name -> name
    android.os.Parcelable$Creator CREATOR -> CREATOR
    14:14:java.lang.String getName() -> getName
    18:19:void setName(java.lang.String) -> setName
    21:23:void <init>(android.os.Parcel) -> <init>
    39:39:int describeContents() -> describeContents
    44:45:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    25:25:void <clinit>() -> <clinit>
com.kaolafm.opensdk.api.personalise.model.InterestTag$1 -> com.kaolafm.opensdk.api.personalise.model.a:
    25:25:void <init>() -> <init>
    28:28:com.kaolafm.opensdk.api.personalise.model.InterestTag createFromParcel(android.os.Parcel) -> createFromParcel
    33:33:com.kaolafm.opensdk.api.personalise.model.InterestTag[] newArray(int) -> newArray
    25:25:java.lang.Object[] newArray(int) -> newArray
    25:25:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
com.kaolafm.opensdk.api.scene.AccScene -> com.kaolafm.opensdk.api.scene.AccScene:
    java.lang.String CODE -> CODE
    java.lang.String TYPE -> TYPE
    14:15:void <init>() -> <init>
    19:19:java.lang.String getCode() -> getCode
    24:24:java.lang.String getType() -> getType
com.kaolafm.opensdk.api.scene.Scene -> com.kaolafm.opensdk.api.scene.Scene:
    9:9:void <init>() -> <init>
    java.lang.String getCode() -> getCode
    java.lang.String getType() -> getType
com.kaolafm.opensdk.api.scene.SceneInfo -> com.kaolafm.opensdk.api.scene.SceneInfo:
    int code -> code
    java.lang.String icon -> icon
    java.lang.String message -> message
    long contentId -> contentId
    java.lang.String contentName -> contentName
    int contentType -> contentType
    int qqOldId -> qqOldId
    int qqMusicType -> qqMusicType
    android.os.Parcelable$Creator CREATOR -> CREATOR
    43:43:int getCode() -> getCode
    47:48:void setCode(int) -> setCode
    51:51:java.lang.String getIcon() -> getIcon
    55:56:void setIcon(java.lang.String) -> setIcon
    59:59:java.lang.String getMessage() -> getMessage
    63:64:void setMessage(java.lang.String) -> setMessage
    67:67:long getContentId() -> getContentId
    71:72:void setContentId(long) -> setContentId
    75:75:java.lang.String getContentName() -> getContentName
    79:80:void setContentName(java.lang.String) -> setContentName
    87:87:int getContentType() -> getContentType
    91:104:int getContentResType() -> getContentResType
    108:109:void setContentType(int) -> setContentType
    112:112:int getQqOldId() -> getQqOldId
    116:117:void setQqOldId(int) -> setQqOldId
    120:120:int getQqMusicType() -> getQqMusicType
    124:125:void setQqMusicType(int) -> setQqMusicType
    130:130:int describeContents() -> describeContents
    135:143:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    145:146:void <init>() -> <init>
    148:157:void <init>(android.os.Parcel) -> <init>
    173:173:java.lang.String toString() -> toString
    159:159:void <clinit>() -> <clinit>
com.kaolafm.opensdk.api.scene.SceneInfo$1 -> com.kaolafm.opensdk.api.scene.a:
    159:159:void <init>() -> <init>
    162:162:com.kaolafm.opensdk.api.scene.SceneInfo createFromParcel(android.os.Parcel) -> createFromParcel
    167:167:com.kaolafm.opensdk.api.scene.SceneInfo[] newArray(int) -> newArray
    159:159:java.lang.Object[] newArray(int) -> newArray
    159:159:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
com.kaolafm.opensdk.api.scene.SceneParam -> com.kaolafm.opensdk.api.scene.SceneParam:
    java.util.Map extInfo -> extInfo
    java.util.Map events -> events
    21:23:void <init>() -> <init>
    26:27:void addScene(com.kaolafm.opensdk.api.scene.Scene) -> addScene
    30:31:void setExtInfo(java.util.Map) -> setExtInfo
com.kaolafm.opensdk.api.scene.SceneRequest -> com.kaolafm.opensdk.api.scene.SceneRequest:
    com.kaolafm.opensdk.api.scene.SceneService mService -> mService
    21:23:void <init>() -> <init>
    32:33:void getSceneInfo(HttpCallback,com.kaolafm.opensdk.api.scene.Scene[]) -> getSceneInfo
    44:62:void getSceneInfo(HttpCallback,java.util.Map,com.kaolafm.opensdk.api.scene.Scene[]) -> getSceneInfo
com.kaolafm.opensdk.api.scene.SceneService -> com.kaolafm.opensdk.api.scene.b:
    io.reactivex.Single getSceneInfo(okhttp3.RequestBody) -> getSceneInfo
com.kaolafm.opensdk.api.scene.SpeedScene -> com.kaolafm.opensdk.api.scene.SpeedScene:
    java.lang.String CODE -> CODE
    java.lang.String TYPE_LOW_SPEED -> TYPE_LOW_SPEED
    java.lang.String TYPE_MEDIUM_SPEED -> TYPE_MEDIUM_SPEED
    java.lang.String TYPE_HIGH_SPEED -> TYPE_HIGH_SPEED
    java.lang.String type -> type
    20:24:void <init>(java.lang.String) -> <init>
    28:28:java.lang.String getCode() -> getCode
    33:33:java.lang.String getType() -> getType
com.kaolafm.opensdk.api.scene.SpeedScene$TYPE -> com.kaolafm.opensdk.api.scene.SpeedScene$a:
com.kaolafm.opensdk.api.search.SearchRequest -> com.kaolafm.opensdk.api.search.SearchRequest:
    com.kaolafm.opensdk.api.search.SearchService mSearchService -> mSearchService
    26:28:void <init>() -> <init>
    58:74:void searchBySemantics(java.lang.String,int,java.lang.String,int,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,HttpCallback) -> searchBySemantics
    110:112:void searchBySemantics(java.lang.String,int,java.lang.String,int,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,HttpCallback) -> searchBySemantics
    115:119:void putNotNullParams(java.util.Map,java.lang.String,java.lang.Object) -> putNotNullParams
    128:129:void searchAll(java.lang.String,HttpCallback) -> searchAll
    143:145:void searchByType(java.lang.String,int,int,int,HttpCallback) -> searchByType
    154:155:void getSuggestedWords(java.lang.String,HttpCallback) -> getSuggestedWords
    158:171:java.lang.String getType(int) -> getType
com.kaolafm.opensdk.api.search.SearchService -> com.kaolafm.opensdk.api.search.a:
    io.reactivex.Single searchBySemantics(java.util.Map) -> searchBySemantics
    io.reactivex.Single searchAll(java.lang.String) -> searchAll
    io.reactivex.Single searchByType(java.lang.String,java.lang.String,int,int) -> searchByType
    io.reactivex.Single getSuggestedWord(java.lang.String) -> getSuggestedWord
com.kaolafm.opensdk.api.search.VoiceSearchRequest -> com.kaolafm.opensdk.api.search.VoiceSearchRequest:
    com.kaolafm.opensdk.api.search.SearchRequest mSearchRequest -> mSearchRequest
    19:21:void <init>() -> <init>
    53:54:void searchBySemantics(java.lang.String,int,java.lang.String,int,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,HttpCallback) -> searchBySemantics
    92:94:void searchBySemantics(java.lang.String,int,java.lang.String,int,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,HttpCallback) -> searchBySemantics
com.kaolafm.opensdk.api.search.internal.Word -> com.kaolafm.opensdk.api.search.internal.Word:
    int field -> field
    int tag -> tag
    java.lang.String artist -> artist
    java.lang.String audioName -> audioName
    java.lang.String albumName -> albumName
    java.lang.String category -> category
    java.lang.String keyword -> keyword
    java.lang.String text -> text
    9:9:void <init>() -> <init>
    20:20:int getField() -> getField
    24:25:void setField(int) -> setField
    28:28:int getTag() -> getTag
    32:33:void setTag(int) -> setTag
    36:36:java.lang.String getArtist() -> getArtist
    40:41:void setArtist(java.lang.String) -> setArtist
    44:44:java.lang.String getAudioName() -> getAudioName
    48:49:void setAudioName(java.lang.String) -> setAudioName
    52:52:java.lang.String getAlbumName() -> getAlbumName
    56:57:void setAlbumName(java.lang.String) -> setAlbumName
    60:60:java.lang.String getCategory() -> getCategory
    64:65:void setCategory(java.lang.String) -> setCategory
    68:68:java.lang.String getKeyword() -> getKeyword
    72:73:void setKeyword(java.lang.String) -> setKeyword
    76:76:java.lang.String getText() -> getText
    80:81:void setText(java.lang.String) -> setText
com.kaolafm.opensdk.api.search.model.HighLightWord -> com.kaolafm.opensdk.api.search.model.HighLightWord:
    java.lang.String field -> field
    int offset -> offset
    int start -> start
    java.lang.String token -> token
    android.os.Parcelable$Creator CREATOR -> CREATOR
    33:33:java.lang.String getField() -> getField
    37:37:int getOffset() -> getOffset
    41:41:int getStart() -> getStart
    45:45:java.lang.String getToken() -> getToken
    49:50:void setField(java.lang.String) -> setField
    53:54:void setOffset(int) -> setOffset
    57:58:void setStart(int) -> setStart
    61:62:void setToken(java.lang.String) -> setToken
    64:69:void <init>(android.os.Parcel) -> <init>
    85:85:int describeContents() -> describeContents
    90:94:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    71:71:void <clinit>() -> <clinit>
com.kaolafm.opensdk.api.search.model.HighLightWord$1 -> com.kaolafm.opensdk.api.search.model.a:
    71:71:void <init>() -> <init>
    74:74:com.kaolafm.opensdk.api.search.model.HighLightWord createFromParcel(android.os.Parcel) -> createFromParcel
    79:79:com.kaolafm.opensdk.api.search.model.HighLightWord[] newArray(int) -> newArray
    71:71:java.lang.Object[] newArray(int) -> newArray
    71:71:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
com.kaolafm.opensdk.api.search.model.SearchProgramBean -> com.kaolafm.opensdk.api.search.model.SearchProgramBean:
    long id -> id
    java.lang.String img -> img
    java.lang.String name -> name
    int oldId -> oldId
    java.lang.String playUrl -> playUrl
    int source -> source
    java.lang.String sourceName -> sourceName
    int type -> type
    java.lang.String albumName -> albumName
    java.util.List comperes -> comperes
    long duration -> duration
    java.lang.String freq -> freq
    java.lang.String callback -> callback
    long listenNum -> listenNum
    java.util.List hightLight -> hightLight
    android.os.Parcelable$Creator CREATOR -> CREATOR
    142:143:void <init>() -> <init>
    145:161:void <init>(android.os.Parcel) -> <init>
    177:177:int describeContents() -> describeContents
    182:197:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    200:200:java.lang.String getAlbumName() -> getAlbumName
    204:204:java.util.List getComperes() -> getComperes
    208:208:long getDuration() -> getDuration
    212:212:java.lang.String getFreq() -> getFreq
    216:216:long getId() -> getId
    220:220:java.lang.String getImg() -> getImg
    224:224:java.lang.String getName() -> getName
    228:228:int getOldId() -> getOldId
    232:232:java.lang.String getPlayUrl() -> getPlayUrl
    236:236:int getSource() -> getSource
    240:240:java.lang.String getSourceName() -> getSourceName
    244:244:int getType() -> getType
    248:249:void setAlbumName(java.lang.String) -> setAlbumName
    252:253:void setComperes(java.util.List) -> setComperes
    256:257:void setDuration(long) -> setDuration
    260:261:void setFreq(java.lang.String) -> setFreq
    264:265:void setId(long) -> setId
    268:269:void setImg(java.lang.String) -> setImg
    272:273:void setName(java.lang.String) -> setName
    276:277:void setOldId(int) -> setOldId
    280:281:void setPlayUrl(java.lang.String) -> setPlayUrl
    284:285:void setSource(int) -> setSource
    288:289:void setSourceName(java.lang.String) -> setSourceName
    292:293:void setType(int) -> setType
    296:296:java.lang.String getCallback() -> getCallback
    300:301:void setCallback(java.lang.String) -> setCallback
    304:304:long getListenNum() -> getListenNum
    308:309:void setListenNum(long) -> setListenNum
    312:312:java.util.List getHightLight() -> getHightLight
    316:317:void setHightLight(java.util.List) -> setHightLight
    163:163:void <clinit>() -> <clinit>
com.kaolafm.opensdk.api.search.model.SearchProgramBean$1 -> com.kaolafm.opensdk.api.search.model.b:
    163:163:void <init>() -> <init>
    166:166:com.kaolafm.opensdk.api.search.model.SearchProgramBean createFromParcel(android.os.Parcel) -> createFromParcel
    171:171:com.kaolafm.opensdk.api.search.model.SearchProgramBean[] newArray(int) -> newArray
    163:163:java.lang.Object[] newArray(int) -> newArray
    163:163:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
com.kaolafm.opensdk.api.search.model.SearchProgramBean$Compere -> com.kaolafm.opensdk.api.search.model.SearchProgramBean$Compere:
    java.lang.String name -> name
    java.lang.String des -> des
    java.lang.String img -> img
    android.os.Parcelable$Creator CREATOR -> CREATOR
    348:348:java.lang.String getName() -> getName
    352:352:java.lang.String getDes() -> getDes
    356:356:java.lang.String getImg() -> getImg
    361:362:void setName(java.lang.String) -> setName
    365:366:void setDes(java.lang.String) -> setDes
    369:370:void setImg(java.lang.String) -> setImg
    375:375:int describeContents() -> describeContents
    380:383:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    385:386:void <init>() -> <init>
    388:392:void <init>(android.os.Parcel) -> <init>
    394:394:void <clinit>() -> <clinit>
com.kaolafm.opensdk.api.search.model.SearchProgramBean$Compere$1 -> com.kaolafm.opensdk.api.search.model.c:
    394:394:void <init>() -> <init>
    397:397:com.kaolafm.opensdk.api.search.model.SearchProgramBean$Compere createFromParcel(android.os.Parcel) -> createFromParcel
    402:402:com.kaolafm.opensdk.api.search.model.SearchProgramBean$Compere[] newArray(int) -> newArray
    394:394:java.lang.Object[] newArray(int) -> newArray
    394:394:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
com.kaolafm.opensdk.api.search.model.VoiceSearchResult -> com.kaolafm.opensdk.api.search.model.VoiceSearchResult:
    java.util.List programList -> programList
    int delayTime -> delayTime
    int playIndex -> playIndex
    int playType -> playType
    13:13:void <init>() -> <init>
    47:47:java.util.List getProgramList() -> getProgramList
    51:51:int getDelayTime() -> getDelayTime
    55:55:int getPlayIndex() -> getPlayIndex
    59:59:int getPlayType() -> getPlayType
    63:64:void setProgramList(java.util.List) -> setProgramList
    67:68:void setDelayTime(int) -> setDelayTime
    71:72:void setPlayIndex(int) -> setPlayIndex
    75:76:void setPlayType(int) -> setPlayType
com.kaolafm.opensdk.api.subscribe.SubscribeInfo -> com.kaolafm.opensdk.api.subscribe.SubscribeInfo:
    long id -> id
    java.lang.String name -> name
    int type -> type
    java.lang.String img -> img
    long updateTime -> updateTime
    int newNum -> newNum
    java.lang.String newTitle -> newTitle
    int updateNum -> updateNum
    int isOnline -> isOnline
    int hasCopyright -> hasCopyright
    java.lang.String timeDuration -> timeDuration
    java.lang.String desc -> desc
    int countNum -> countNum
    java.lang.String comperes -> comperes
    android.os.Parcelable$Creator CREATOR -> CREATOR
    75:76:void <init>() -> <init>
    79:79:long getId() -> getId
    83:84:void setId(long) -> setId
    87:87:java.lang.String getName() -> getName
    91:92:void setName(java.lang.String) -> setName
    95:95:int getType() -> getType
    99:100:void setType(int) -> setType
    107:118:int getResType() -> getResType
    122:122:java.lang.String getImg() -> getImg
    126:127:void setImg(java.lang.String) -> setImg
    130:130:long getUpdateTime() -> getUpdateTime
    134:135:void setUpdateTime(long) -> setUpdateTime
    138:138:int getNewNum() -> getNewNum
    142:143:void setNewNum(int) -> setNewNum
    146:146:java.lang.String getNewTitle() -> getNewTitle
    150:151:void setNewTitle(java.lang.String) -> setNewTitle
    154:154:int getUpdateNum() -> getUpdateNum
    158:159:void setUpdateNum(int) -> setUpdateNum
    162:162:int getIsOnline() -> getIsOnline
    166:167:void setIsOnline(int) -> setIsOnline
    170:170:int getHasCopyright() -> getHasCopyright
    174:175:void setHasCopyright(int) -> setHasCopyright
    178:178:java.lang.String getTime() -> getTime
    182:183:void setTime(java.lang.String) -> setTime
    186:186:java.lang.String getDesc() -> getDesc
    190:191:void setDesc(java.lang.String) -> setDesc
    194:194:int getCountNum() -> getCountNum
    198:199:void setCountNum(int) -> setCountNum
    202:202:java.lang.String getComperes() -> getComperes
    206:207:void setComperes(java.lang.String) -> setComperes
    212:212:int describeContents() -> describeContents
    217:231:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    233:248:void <init>(android.os.Parcel) -> <init>
    250:250:void <clinit>() -> <clinit>
com.kaolafm.opensdk.api.subscribe.SubscribeInfo$1 -> com.kaolafm.opensdk.api.subscribe.a:
    250:250:void <init>() -> <init>
    253:253:com.kaolafm.opensdk.api.subscribe.SubscribeInfo createFromParcel(android.os.Parcel) -> createFromParcel
    258:258:com.kaolafm.opensdk.api.subscribe.SubscribeInfo[] newArray(int) -> newArray
    250:250:java.lang.Object[] newArray(int) -> newArray
    250:250:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
com.kaolafm.opensdk.api.subscribe.SubscribeRequest -> com.kaolafm.opensdk.api.subscribe.SubscribeRequest:
    int TYPE_ALL -> TYPE_ALL
    com.kaolafm.opensdk.api.subscribe.SubscribeService mService -> mService
    37:39:void <init>() -> <init>
    50:51:void getSubscribeList(int,int,HttpCallback) -> getSubscribeList
    61:62:com.kaolafm.opensdk.api.BasePageResult getSubscribeListSync(int,int) -> getSubscribeListSync
    72:73:void subscribe(long,HttpCallback) -> subscribe
    82:83:com.kaolafm.opensdk.api.subscribe.SubscribeStatus subscribeSync(long) -> subscribeSync
    92:96:void unsubscribe(long,HttpCallback) -> unsubscribe
    104:105:com.kaolafm.opensdk.api.subscribe.SubscribeStatus unsubscribeSync(long) -> unsubscribeSync
    115:120:void isSubscribed(long,HttpCallback) -> isSubscribed
    128:129:com.kaolafm.opensdk.api.subscribe.SubscribeStatus isSubscribedSync(long) -> isSubscribedSync
    133:134:void getUserFollowRadio(java.lang.String,HttpCallback) -> getUserFollowRadio
    117:118:java.lang.Boolean lambda$isSubscribed$1(com.kaolafm.opensdk.api.BaseResult) -> lambda$isSubscribed$1
    93:94:java.lang.Boolean lambda$unsubscribe$0(com.kaolafm.opensdk.api.BaseResult) -> lambda$unsubscribe$0
com.kaolafm.opensdk.api.subscribe.SubscribeService -> com.kaolafm.opensdk.api.subscribe.b:
    io.reactivex.Single getSubscribeList(int,int,int) -> getSubscribeList
    retrofit2.Call getSubscribeListSync(int,int,int) -> getSubscribeListSync
    io.reactivex.Single subscribe(long) -> subscribe
    retrofit2.Call subscribeSync(long) -> subscribeSync
    io.reactivex.Single unsubscribe(long) -> unsubscribe
    retrofit2.Call unsubscribeSync(long) -> unsubscribeSync
    io.reactivex.Single isSubscribed(long) -> isSubscribed
    retrofit2.Call isSubscribedSync(long) -> isSubscribedSync
    io.reactivex.Single getUserFollowRadio(java.lang.String) -> getUserFollowRadio
com.kaolafm.opensdk.api.subscribe.SubscribeStatus -> com.kaolafm.opensdk.api.subscribe.SubscribeStatus:
    int STATE_FAILURE -> STATE_FAILURE
    int STATE_SUCCESS -> STATE_SUCCESS
    int STATE_HAD_SUBSCRIBE -> STATE_HAD_SUBSCRIBE
    int status -> status
    android.os.Parcelable$Creator CREATOR -> CREATOR
    39:39:int getStatus() -> getStatus
    43:44:void setStatus(int) -> setStatus
    49:49:int describeContents() -> describeContents
    54:55:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    57:58:void <init>() -> <init>
    60:62:void <init>(android.os.Parcel) -> <init>
    64:64:void <clinit>() -> <clinit>
com.kaolafm.opensdk.api.subscribe.SubscribeStatus$1 -> com.kaolafm.opensdk.api.subscribe.c:
    64:64:void <init>() -> <init>
    67:67:com.kaolafm.opensdk.api.subscribe.SubscribeStatus createFromParcel(android.os.Parcel) -> createFromParcel
    72:72:com.kaolafm.opensdk.api.subscribe.SubscribeStatus[] newArray(int) -> newArray
    64:64:java.lang.Object[] newArray(int) -> newArray
    64:64:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
com.kaolafm.opensdk.api.subscribe.SubscribeStatus$State -> com.kaolafm.opensdk.api.subscribe.SubscribeStatus$a:
com.kaolafm.opensdk.di.component.KradioComponent -> com.kaolafm.opensdk.di.component.a:
    void inject(com.kaolafm.opensdk.KradioSDK) -> inject
    void inject(com.kaolafm.opensdk.account.token.AccessTokenManager) -> inject
    android.app.Application application() -> application
    com.kaolafm.opensdk.di.component.SessionComponent getSessionComponent() -> getSessionComponent
com.kaolafm.opensdk.di.component.KradioComponent$Builder -> com.kaolafm.opensdk.di.component.a$a:
    com.kaolafm.opensdk.di.component.KradioComponent$Builder application(android.app.Application) -> a
    com.kaolafm.opensdk.di.component.KradioComponent$Builder configuration(com.kaolafm.opensdk.Options) -> a
    com.kaolafm.opensdk.di.component.KradioComponent build() -> a
com.kaolafm.opensdk.di.component.DaggerAppComponent -> com.kaolafm.opensdk.di.component.DaggerAppComponent:
    android.app.Application application -> application
    javax.inject.Provider applicationProvider -> applicationProvider
    javax.inject.Provider kaolaProfileManagerProvider -> kaolaProfileManagerProvider
    javax.inject.Provider qQMusicProfileMangerProvider -> qQMusicProfileMangerProvider
    javax.inject.Provider provideAccessTokenManagerProvider -> provideAccessTokenManagerProvider
    javax.inject.Provider setOfTypeAdapterFactoryProvider -> setOfTypeAdapterFactoryProvider
    javax.inject.Provider provideGsonProvider -> provideGsonProvider
    javax.inject.Provider provideCacheFactoryProvider -> provideCacheFactoryProvider
    com.kaolafm.opensdk.di.module.CommonParamModule_ProvideDomainsFactory provideDomainsProvider -> provideDomainsProvider
    com.kaolafm.opensdk.di.module.CommonParamModule_ProvideProfileFactory provideProfileProvider -> provideProfileProvider
    com.kaolafm.opensdk.di.module.CommonParamModule_ProvideKaolaAccessTokenFactory provideKaolaAccessTokenProvider -> provideKaolaAccessTokenProvider
    com.kaolafm.opensdk.di.module.CommonParamModule_ProvideKradioParamsFactory provideKradioParamsProvider -> provideKradioParamsProvider
    com.kaolafm.opensdk.di.module.CommonParamModule_ProvideKaolaParamsFactory provideKaolaParamsProvider -> provideKaolaParamsProvider
    com.kaolafm.opensdk.di.module.CommonParamModule_ProvideOpenKaolaParamsFactory provideOpenKaolaParamsProvider -> provideOpenKaolaParamsProvider
    com.kaolafm.opensdk.di.module.CommonParamModule_ProvideVoiceSearchParamsFactory provideVoiceSearchParamsProvider -> provideVoiceSearchParamsProvider
    com.kaolafm.opensdk.di.module.CommonParamModule_ProvideSubscribeParamsFactory provideSubscribeParamsProvider -> provideSubscribeParamsProvider
    javax.inject.Provider paramQualifierMapOfStringAndProviderOfMapOfStringAndStringProvider -> paramQualifierMapOfStringAndProviderOfMapOfStringAndStringProvider
    javax.inject.Provider retrofitUrlManagerProvider -> retrofitUrlManagerProvider
    javax.inject.Provider httpHandlerProvider -> httpHandlerProvider
    javax.inject.Provider providePrintHttpLogLevelProvider -> providePrintHttpLogLevelProvider
    javax.inject.Provider requestInterceptorProvider -> requestInterceptorProvider
    com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideHttpHandlerInterceptorFactory provideHttpHandlerInterceptorProvider -> provideHttpHandlerInterceptorProvider
    com.kaolafm.opensdk.api.music.qq.QQMusicTokenInterceptor_Factory qQMusicTokenInterceptorProvider -> qQMusicTokenInterceptorProvider
    javax.inject.Provider httpInterceptorSetOfInterceptorProvider -> httpInterceptorSetOfInterceptorProvider
    javax.inject.Provider provideOkHttpClientBuilderProvider -> provideOkHttpClientBuilderProvider
    javax.inject.Provider provideOkHttpClientProvider -> provideOkHttpClientProvider
    javax.inject.Provider provideApiUrlProvider -> provideApiUrlProvider
    javax.inject.Provider provideRetrofitProvider -> provideRetrofitProvider
    javax.inject.Provider setOfResponseErrorListenerProvider -> setOfResponseErrorListenerProvider
    javax.inject.Provider provideResponseErrorListenersProvider -> provideResponseErrorListenersProvider
    javax.inject.Provider repositoryManagerProvider -> repositoryManagerProvider
    com.kaolafm.opensdk.di.module.CommonParamModule_ProvideKaoloInitParamsFactory provideKaoloInitParamsProvider -> provideKaoloInitParamsProvider
    163:165:void <init>(com.kaolafm.opensdk.di.component.DaggerAppComponent$Builder) -> <init>
    168:168:com.kaolafm.opensdk.di.component.KradioComponent$Builder builder() -> builder
    172:173:com.kaolafm.opensdk.account.token.KaolaAccessTokenCache getKaolaAccessTokenCache() -> getKaolaAccessTokenCache
    177:178:com.kaolafm.opensdk.account.token.QQMusicAccessTokenCache getQQMusicAccessTokenCache() -> getQQMusicAccessTokenCache
    182:185:java.util.Map getAccessTokenQualifierMapOfStringAndITokenCache() -> getAccessTokenQualifierMapOfStringAndITokenCache
    190:306:void initialize(com.kaolafm.opensdk.di.component.DaggerAppComponent$Builder) -> initialize
    310:311:void inject(com.kaolafm.opensdk.KradioSDK) -> inject
    315:316:void inject(com.kaolafm.opensdk.account.token.AccessTokenManager) -> inject
    320:320:android.app.Application application() -> application
    325:325:com.kaolafm.opensdk.di.component.SessionComponent getSessionComponent() -> getSessionComponent
    329:334:com.kaolafm.opensdk.KradioSDK injectKradioSDK(com.kaolafm.opensdk.KradioSDK) -> injectKradioSDK
    338:341:com.kaolafm.opensdk.account.token.KaolaAccessTokenCache injectKaolaAccessTokenCache(com.kaolafm.opensdk.account.token.KaolaAccessTokenCache) -> injectKaolaAccessTokenCache
    345:348:com.kaolafm.opensdk.account.token.QQMusicAccessTokenCache injectQQMusicAccessTokenCache(com.kaolafm.opensdk.account.token.QQMusicAccessTokenCache) -> injectQQMusicAccessTokenCache
    352:354:com.kaolafm.opensdk.account.token.AccessTokenManager injectAccessTokenManager(com.kaolafm.opensdk.account.token.AccessTokenManager) -> injectAccessTokenManager
    96:96:void <init>(com.kaolafm.opensdk.di.component.DaggerAppComponent$Builder,com.kaolafm.opensdk.di.component.DaggerAppComponent$1) -> <init>
    96:96:javax.inject.Provider access$900(com.kaolafm.opensdk.di.component.DaggerAppComponent) -> access$900
    96:96:javax.inject.Provider access$1000(com.kaolafm.opensdk.di.component.DaggerAppComponent) -> access$1000
    96:96:com.kaolafm.opensdk.di.module.CommonParamModule_ProvideProfileFactory access$1100(com.kaolafm.opensdk.di.component.DaggerAppComponent) -> access$1100
    96:96:javax.inject.Provider access$1200(com.kaolafm.opensdk.di.component.DaggerAppComponent) -> access$1200
    96:96:javax.inject.Provider access$1300(com.kaolafm.opensdk.di.component.DaggerAppComponent) -> access$1300
    96:96:com.kaolafm.opensdk.di.module.CommonParamModule_ProvideKaoloInitParamsFactory access$1400(com.kaolafm.opensdk.di.component.DaggerAppComponent) -> access$1400
    96:96:javax.inject.Provider access$1500(com.kaolafm.opensdk.di.component.DaggerAppComponent) -> access$1500
    96:96:android.app.Application access$1600(com.kaolafm.opensdk.di.component.DaggerAppComponent) -> access$1600
com.kaolafm.opensdk.di.component.DaggerAppComponent$1 -> com.kaolafm.opensdk.di.component.b:
com.kaolafm.opensdk.di.component.DaggerAppComponent$Builder -> com.kaolafm.opensdk.di.component.DaggerAppComponent$a:
    com.kaolafm.opensdk.di.module.HttpClientModule httpClientModule -> a
    com.kaolafm.opensdk.di.module.CommonParamModule commonParamModule -> b
    android.app.Application application -> c
    com.kaolafm.opensdk.Options configuration -> d
    357:357:void <init>() -> <init>
    368:380:com.kaolafm.opensdk.di.component.KradioComponent build() -> a
    385:386:com.kaolafm.opensdk.di.component.DaggerAppComponent$Builder application(android.app.Application) -> b
    391:392:com.kaolafm.opensdk.di.component.DaggerAppComponent$Builder configuration(com.kaolafm.opensdk.Options) -> b
    357:357:com.kaolafm.opensdk.di.component.KradioComponent$Builder configuration(com.kaolafm.opensdk.Options) -> a
    357:357:com.kaolafm.opensdk.di.component.KradioComponent$Builder application(android.app.Application) -> a
    357:357:void <init>(com.kaolafm.opensdk.di.component.DaggerAppComponent$1) -> <init>
    357:357:android.app.Application access$100(com.kaolafm.opensdk.di.component.DaggerAppComponent$Builder) -> a
    357:357:com.kaolafm.opensdk.di.module.HttpClientModule access$200(com.kaolafm.opensdk.di.component.DaggerAppComponent$Builder) -> b
    357:357:com.kaolafm.opensdk.di.module.CommonParamModule access$300(com.kaolafm.opensdk.di.component.DaggerAppComponent$Builder) -> c
com.kaolafm.opensdk.di.component.DaggerAppComponent$SessionComponentImpl -> com.kaolafm.opensdk.di.component.DaggerAppComponent$b:
    com.kaolafm.opensdk.di.component.DaggerAppComponent this$0 -> a
    397:397:void <init>(com.kaolafm.opensdk.di.component.DaggerAppComponent) -> <init>
    401:401:com.kaolafm.opensdk.di.component.RequestComponent getRequestComponent() -> a
    406:406:com.kaolafm.opensdk.di.component.MusicPlayerComponent getMusicPlayerComponent() -> b
    396:396:void <init>(com.kaolafm.opensdk.di.component.DaggerAppComponent,com.kaolafm.opensdk.di.component.DaggerAppComponent$1) -> <init>
com.kaolafm.opensdk.di.component.DaggerAppComponent$SessionComponentImpl$MusicPlayerComponentImpl -> com.kaolafm.opensdk.di.component.DaggerAppComponent$b$a:
    com.kaolafm.opensdk.di.module.QQMusicModule_ProvideQQMusicAccessTokenFactory provideQQMusicAccessTokenProvider -> b
    com.kaolafm.opensdk.di.module.QQMusicModule_ProvideQQMusicCommonParamFactory provideQQMusicCommonParamProvider -> c
    com.kaolafm.opensdk.di.module.QQMusicModule_ProvideWxRefreshParamsFactory provideWxRefreshParamsProvider -> d
    com.kaolafm.opensdk.di.module.QQMusicModule_ProvideQQRefreshParamsFactory provideQQRefreshParamsProvider -> e
    com.kaolafm.opensdk.di.module.QQMusicModule_ProvideQQUserInfoParamsFactory provideQQUserInfoParamsProvider -> f
    com.kaolafm.opensdk.di.component.DaggerAppComponent$SessionComponentImpl this$1 -> a
    537:539:void <init>(com.kaolafm.opensdk.di.component.DaggerAppComponent$SessionComponentImpl) -> <init>
    542:542:com.kaolafm.opensdk.api.music.qq.QQMusicRequest getQQMusicRequest() -> a
    547:564:void initialize() -> b
    568:569:void inject(com.kaolafm.opensdk.player.MusicPlayerManager) -> a
    572:590:com.kaolafm.opensdk.api.music.qq.QQMusicRequest injectQQMusicRequest(com.kaolafm.opensdk.api.music.qq.QQMusicRequest) -> a
    594:597:com.kaolafm.opensdk.player.MusicPlayerManager injectMusicPlayerManager(com.kaolafm.opensdk.player.MusicPlayerManager) -> b
    526:526:void <init>(com.kaolafm.opensdk.di.component.DaggerAppComponent$SessionComponentImpl,com.kaolafm.opensdk.di.component.DaggerAppComponent$1) -> <init>
com.kaolafm.opensdk.di.component.DaggerAppComponent$SessionComponentImpl$RequestComponentImpl -> com.kaolafm.opensdk.di.component.DaggerAppComponent$b$b:
    com.kaolafm.opensdk.di.component.DaggerAppComponent$SessionComponentImpl this$1 -> a
    410:410:void <init>(com.kaolafm.opensdk.di.component.DaggerAppComponent$SessionComponentImpl) -> <init>
    414:415:void inject(com.kaolafm.opensdk.api.BaseRequest) -> a
    419:420:void inject(com.kaolafm.opensdk.api.init.InitRequest) -> a
    424:424:com.kaolafm.opensdk.di.component.QQMusicRequestComponent getQQMusicRequestComponent() -> a
    428:438:com.kaolafm.opensdk.api.BaseRequest injectBaseRequest(com.kaolafm.opensdk.api.BaseRequest) -> b
    442:454:com.kaolafm.opensdk.api.init.InitRequest injectInitRequest(com.kaolafm.opensdk.api.init.InitRequest) -> b
    409:409:void <init>(com.kaolafm.opensdk.di.component.DaggerAppComponent$SessionComponentImpl,com.kaolafm.opensdk.di.component.DaggerAppComponent$1) -> <init>
com.kaolafm.opensdk.di.component.DaggerAppComponent$SessionComponentImpl$RequestComponentImpl$QQMusicRequestComponentImpl -> com.kaolafm.opensdk.di.component.DaggerAppComponent$b$b$a:
    com.kaolafm.opensdk.di.module.QQMusicModule_ProvideQQMusicAccessTokenFactory provideQQMusicAccessTokenProvider -> b
    com.kaolafm.opensdk.di.module.QQMusicModule_ProvideQQMusicCommonParamFactory provideQQMusicCommonParamProvider -> c
    com.kaolafm.opensdk.di.module.QQMusicModule_ProvideWxRefreshParamsFactory provideWxRefreshParamsProvider -> d
    com.kaolafm.opensdk.di.module.QQMusicModule_ProvideQQRefreshParamsFactory provideQQRefreshParamsProvider -> e
    com.kaolafm.opensdk.di.module.QQMusicModule_ProvideQQUserInfoParamsFactory provideQQUserInfoParamsProvider -> f
    com.kaolafm.opensdk.di.component.DaggerAppComponent$SessionComponentImpl$RequestComponentImpl this$2 -> a
    468:470:void <init>(com.kaolafm.opensdk.di.component.DaggerAppComponent$SessionComponentImpl$RequestComponentImpl) -> <init>
    474:494:void initialize() -> a
    498:499:void inject(com.kaolafm.opensdk.api.music.qq.QQMusicRequest) -> a
    502:521:com.kaolafm.opensdk.api.music.qq.QQMusicRequest injectQQMusicRequest(com.kaolafm.opensdk.api.music.qq.QQMusicRequest) -> b
    457:457:void <init>(com.kaolafm.opensdk.di.component.DaggerAppComponent$SessionComponentImpl$RequestComponentImpl,com.kaolafm.opensdk.di.component.DaggerAppComponent$1) -> <init>
com.kaolafm.opensdk.di.component.DaggerOperationComponent -> com.kaolafm.opensdk.di.component.c:
    7:7:void <init>(com.kaolafm.opensdk.di.component.DaggerOperationComponent$Builder) -> <init>
    10:10:com.kaolafm.opensdk.di.component.DaggerOperationComponent$Builder builder() -> a
    14:14:com.kaolafm.opensdk.di.component.OperationComponent create() -> b
    18:18:void inject(com.kaolafm.opensdk.utils.operation.OperationAssister) -> a
    6:6:void <init>(com.kaolafm.opensdk.di.component.DaggerOperationComponent$Builder,com.kaolafm.opensdk.di.component.DaggerOperationComponent$1) -> <init>
com.kaolafm.opensdk.di.component.DaggerOperationComponent$1 -> com.kaolafm.opensdk.di.component.d:
com.kaolafm.opensdk.di.component.DaggerOperationComponent$Builder -> com.kaolafm.opensdk.di.component.c$a:
    21:21:void <init>() -> <init>
    24:24:com.kaolafm.opensdk.di.component.OperationComponent build() -> a
    20:20:void <init>(com.kaolafm.opensdk.di.component.DaggerOperationComponent$1) -> <init>
com.kaolafm.opensdk.di.component.MusicPlayerComponent -> com.kaolafm.opensdk.di.component.e:
    void inject(com.kaolafm.opensdk.player.MusicPlayerManager) -> a
com.kaolafm.opensdk.di.component.OperationComponent -> com.kaolafm.opensdk.di.component.f:
    void inject(com.kaolafm.opensdk.utils.operation.OperationAssister) -> a
com.kaolafm.opensdk.di.component.QQMusicRequestComponent -> com.kaolafm.opensdk.di.component.g:
    void inject(com.kaolafm.opensdk.api.music.qq.QQMusicRequest) -> a
com.kaolafm.opensdk.di.component.RequestComponent -> com.kaolafm.opensdk.di.component.h:
    void inject(com.kaolafm.opensdk.api.BaseRequest) -> a
    void inject(com.kaolafm.opensdk.api.init.InitRequest) -> a
    com.kaolafm.opensdk.di.component.QQMusicRequestComponent getQQMusicRequestComponent() -> a
com.kaolafm.opensdk.di.component.SessionComponent -> com.kaolafm.opensdk.di.component.i:
    com.kaolafm.opensdk.di.component.RequestComponent getRequestComponent() -> a
    com.kaolafm.opensdk.di.component.MusicPlayerComponent getMusicPlayerComponent() -> b
com.kaolafm.opensdk.di.module.AppModule -> com.kaolafm.opensdk.di.a.a:
    23:23:void <init>() -> <init>
    IRepositoryManager bindRepositoryManager(RepositoryManager) -> a
    38:38:com.kaolafm.opensdk.account.token.AccessTokenManager provideAccessTokenManager() -> a
    com.kaolafm.opensdk.account.token.TokenCache provideKaolaAccessTokenCache(com.kaolafm.opensdk.account.token.KaolaAccessTokenCache) -> a
    com.kaolafm.opensdk.account.token.TokenCache bindQQMusicAccessTokenCache(com.kaolafm.opensdk.account.token.QQMusicAccessTokenCache) -> a
com.kaolafm.opensdk.di.module.AppModule_ProvideAccessTokenManagerFactory -> com.kaolafm.opensdk.di.a.b:
    com.kaolafm.opensdk.di.module.AppModule_ProvideAccessTokenManagerFactory INSTANCE -> a
    8:8:void <init>() -> <init>
    15:15:com.kaolafm.opensdk.account.token.AccessTokenManager get() -> get
    19:19:com.kaolafm.opensdk.account.token.AccessTokenManager provideInstance() -> a
    23:23:com.kaolafm.opensdk.di.module.AppModule_ProvideAccessTokenManagerFactory create() -> create
    27:28:com.kaolafm.opensdk.account.token.AccessTokenManager proxyProvideAccessTokenManager() -> b
    8:8:java.lang.Object get() -> get
    10:10:void <clinit>() -> <clinit>
com.kaolafm.opensdk.di.module.CommonParamModule -> com.kaolafm.opensdk.di.a.c:
    36:36:void <init>() -> <init>
    47:69:java.util.Map provideKradioParams(com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken) -> a
    79:117:java.util.Map provideKaolaParams(com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken) -> b
    125:125:java.util.Map provideOpenKaolaParams(java.util.Map) -> a
    141:141:java.util.Map provideVoiceSearchParams(java.util.Map) -> b
    149:149:java.util.Map provideSubscribeParams(java.util.Map) -> c
    155:155:com.kaolafm.opensdk.account.profile.KaolaProfile provideProfile(com.kaolafm.opensdk.account.profile.KaolaProfileManager) -> a
    161:161:com.kaolafm.opensdk.account.token.KaolaAccessToken provideKaolaAccessToken(com.kaolafm.opensdk.account.token.AccessTokenManager) -> a
    167:183:java.util.Map provideKaoloInitParams(android.app.Application,com.kaolafm.opensdk.account.profile.KaolaProfile) -> a
    187:190:void putNotNull(java.util.HashMap,java.lang.String,java.lang.String) -> a
    195:200:java.util.Map provideDomains() -> a
com.kaolafm.opensdk.di.module.CommonParamModule_ProvideDomainsFactory -> com.kaolafm.opensdk.di.a.d:
    com.kaolafm.opensdk.di.module.CommonParamModule module -> a
    11:13:void <init>(com.kaolafm.opensdk.di.module.CommonParamModule) -> <init>
    17:17:java.util.Map get() -> get
    21:21:java.util.Map provideInstance(com.kaolafm.opensdk.di.module.CommonParamModule) -> a
    25:25:com.kaolafm.opensdk.di.module.CommonParamModule_ProvideDomainsFactory create(com.kaolafm.opensdk.di.module.CommonParamModule) -> create
    29:30:java.util.Map proxyProvideDomains(com.kaolafm.opensdk.di.module.CommonParamModule) -> b
    8:8:java.lang.Object get() -> get
com.kaolafm.opensdk.di.module.CommonParamModule_ProvideKaolaAccessTokenFactory -> com.kaolafm.opensdk.di.a.e:
    com.kaolafm.opensdk.di.module.CommonParamModule module -> a
    javax.inject.Provider accessTokenManagerProvider -> b
    17:20:void <init>(com.kaolafm.opensdk.di.module.CommonParamModule,javax.inject.Provider) -> <init>
    24:24:com.kaolafm.opensdk.account.token.KaolaAccessToken get() -> get
    29:29:com.kaolafm.opensdk.account.token.KaolaAccessToken provideInstance(com.kaolafm.opensdk.di.module.CommonParamModule,javax.inject.Provider) -> a
    34:34:com.kaolafm.opensdk.di.module.CommonParamModule_ProvideKaolaAccessTokenFactory create(com.kaolafm.opensdk.di.module.CommonParamModule,javax.inject.Provider) -> create
    39:40:com.kaolafm.opensdk.account.token.KaolaAccessToken proxyProvideKaolaAccessToken(com.kaolafm.opensdk.di.module.CommonParamModule,com.kaolafm.opensdk.account.token.AccessTokenManager) -> a
    10:10:java.lang.Object get() -> get
com.kaolafm.opensdk.di.module.CommonParamModule_ProvideKaolaParamsFactory -> com.kaolafm.opensdk.di.a.f:
    com.kaolafm.opensdk.di.module.CommonParamModule module -> a
    javax.inject.Provider profileProvider -> b
    javax.inject.Provider accessTokenProvider -> c
    22:26:void <init>(com.kaolafm.opensdk.di.module.CommonParamModule,javax.inject.Provider,javax.inject.Provider) -> <init>
    30:30:java.util.Map get() -> get
    37:37:java.util.Map provideInstance(com.kaolafm.opensdk.di.module.CommonParamModule,javax.inject.Provider,javax.inject.Provider) -> a
    44:44:com.kaolafm.opensdk.di.module.CommonParamModule_ProvideKaolaParamsFactory create(com.kaolafm.opensdk.di.module.CommonParamModule,javax.inject.Provider,javax.inject.Provider) -> create
    50:51:java.util.Map proxyProvideKaolaParams(com.kaolafm.opensdk.di.module.CommonParamModule,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken) -> a
    11:11:java.lang.Object get() -> get
com.kaolafm.opensdk.di.module.CommonParamModule_ProvideKaoloInitParamsFactory -> com.kaolafm.opensdk.di.a.g:
    com.kaolafm.opensdk.di.module.CommonParamModule module -> a
    javax.inject.Provider contextProvider -> b
    javax.inject.Provider profileProvider -> c
    22:26:void <init>(com.kaolafm.opensdk.di.module.CommonParamModule,javax.inject.Provider,javax.inject.Provider) -> <init>
    30:30:java.util.Map get() -> get
    37:37:java.util.Map provideInstance(com.kaolafm.opensdk.di.module.CommonParamModule,javax.inject.Provider,javax.inject.Provider) -> a
    44:44:com.kaolafm.opensdk.di.module.CommonParamModule_ProvideKaoloInitParamsFactory create(com.kaolafm.opensdk.di.module.CommonParamModule,javax.inject.Provider,javax.inject.Provider) -> create
    50:51:java.util.Map proxyProvideKaoloInitParams(com.kaolafm.opensdk.di.module.CommonParamModule,android.app.Application,com.kaolafm.opensdk.account.profile.KaolaProfile) -> a
    11:11:java.lang.Object get() -> get
com.kaolafm.opensdk.di.module.CommonParamModule_ProvideKradioParamsFactory -> com.kaolafm.opensdk.di.a.h:
    com.kaolafm.opensdk.di.module.CommonParamModule module -> a
    javax.inject.Provider profileProvider -> b
    javax.inject.Provider accessTokenProvider -> c
    22:26:void <init>(com.kaolafm.opensdk.di.module.CommonParamModule,javax.inject.Provider,javax.inject.Provider) -> <init>
    30:30:java.util.Map get() -> get
    37:37:java.util.Map provideInstance(com.kaolafm.opensdk.di.module.CommonParamModule,javax.inject.Provider,javax.inject.Provider) -> a
    44:44:com.kaolafm.opensdk.di.module.CommonParamModule_ProvideKradioParamsFactory create(com.kaolafm.opensdk.di.module.CommonParamModule,javax.inject.Provider,javax.inject.Provider) -> create
    50:51:java.util.Map proxyProvideKradioParams(com.kaolafm.opensdk.di.module.CommonParamModule,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken) -> a
    11:11:java.lang.Object get() -> get
com.kaolafm.opensdk.di.module.CommonParamModule_ProvideOpenKaolaParamsFactory -> com.kaolafm.opensdk.di.a.i:
    com.kaolafm.opensdk.di.module.CommonParamModule module -> a
    javax.inject.Provider paramsProvider -> b
    16:19:void <init>(com.kaolafm.opensdk.di.module.CommonParamModule,javax.inject.Provider) -> <init>
    23:23:java.util.Map get() -> get
    28:28:java.util.Map provideInstance(com.kaolafm.opensdk.di.module.CommonParamModule,javax.inject.Provider) -> a
    33:33:com.kaolafm.opensdk.di.module.CommonParamModule_ProvideOpenKaolaParamsFactory create(com.kaolafm.opensdk.di.module.CommonParamModule,javax.inject.Provider) -> create
    38:39:java.util.Map proxyProvideOpenKaolaParams(com.kaolafm.opensdk.di.module.CommonParamModule,java.util.Map) -> a
    9:9:java.lang.Object get() -> get
com.kaolafm.opensdk.di.module.CommonParamModule_ProvideProfileFactory -> com.kaolafm.opensdk.di.a.j:
    com.kaolafm.opensdk.di.module.CommonParamModule module -> a
    javax.inject.Provider profileManagerProvider -> b
    16:19:void <init>(com.kaolafm.opensdk.di.module.CommonParamModule,javax.inject.Provider) -> <init>
    23:23:com.kaolafm.opensdk.account.profile.KaolaProfile get() -> get
    28:28:com.kaolafm.opensdk.account.profile.KaolaProfile provideInstance(com.kaolafm.opensdk.di.module.CommonParamModule,javax.inject.Provider) -> a
    33:33:com.kaolafm.opensdk.di.module.CommonParamModule_ProvideProfileFactory create(com.kaolafm.opensdk.di.module.CommonParamModule,javax.inject.Provider) -> create
    38:39:com.kaolafm.opensdk.account.profile.KaolaProfile proxyProvideProfile(com.kaolafm.opensdk.di.module.CommonParamModule,com.kaolafm.opensdk.account.profile.KaolaProfileManager) -> a
    10:10:java.lang.Object get() -> get
com.kaolafm.opensdk.di.module.CommonParamModule_ProvideSubscribeParamsFactory -> com.kaolafm.opensdk.di.a.k:
    com.kaolafm.opensdk.di.module.CommonParamModule module -> a
    javax.inject.Provider paramsProvider -> b
    16:19:void <init>(com.kaolafm.opensdk.di.module.CommonParamModule,javax.inject.Provider) -> <init>
    23:23:java.util.Map get() -> get
    28:28:java.util.Map provideInstance(com.kaolafm.opensdk.di.module.CommonParamModule,javax.inject.Provider) -> a
    33:33:com.kaolafm.opensdk.di.module.CommonParamModule_ProvideSubscribeParamsFactory create(com.kaolafm.opensdk.di.module.CommonParamModule,javax.inject.Provider) -> create
    38:39:java.util.Map proxyProvideSubscribeParams(com.kaolafm.opensdk.di.module.CommonParamModule,java.util.Map) -> a
    9:9:java.lang.Object get() -> get
com.kaolafm.opensdk.di.module.CommonParamModule_ProvideVoiceSearchParamsFactory -> com.kaolafm.opensdk.di.a.l:
    com.kaolafm.opensdk.di.module.CommonParamModule module -> a
    javax.inject.Provider paramsProvider -> b
    16:19:void <init>(com.kaolafm.opensdk.di.module.CommonParamModule,javax.inject.Provider) -> <init>
    23:23:java.util.Map get() -> get
    28:28:java.util.Map provideInstance(com.kaolafm.opensdk.di.module.CommonParamModule,javax.inject.Provider) -> a
    33:33:com.kaolafm.opensdk.di.module.CommonParamModule_ProvideVoiceSearchParamsFactory create(com.kaolafm.opensdk.di.module.CommonParamModule,javax.inject.Provider) -> create
    38:39:java.util.Map proxyProvideVoiceSearchParams(com.kaolafm.opensdk.di.module.CommonParamModule,java.util.Map) -> a
    9:9:java.lang.Object get() -> get
com.kaolafm.opensdk.di.module.HttpClientModule -> com.kaolafm.opensdk.di.a.m:
    long TIME_OUT -> a
    27:27:void <init>() -> <init>
    34:39:retrofit2.Retrofit provideRetrofit(okhttp3.OkHttpClient,okhttp3.HttpUrl,com.google.gson.Gson) -> a
    45:45:okhttp3.OkHttpClient provideOkHttpClient(okhttp3.OkHttpClient$Builder) -> a
    51:63:okhttp3.OkHttpClient$Builder provideOkHttpClientBuilder(okhttp3.Interceptor,okhttp3.Interceptor,java.util.Set) -> a
    74:80:com.google.gson.Gson provideGson(java.util.Set) -> a
com.kaolafm.opensdk.di.module.HttpClientModule$GsonConfiguration -> com.kaolafm.opensdk.di.a.m$a:
    void configGson(com.google.gson.GsonBuilder) -> a
com.kaolafm.opensdk.di.module.HttpClientModule_ProvideGsonFactory -> com.kaolafm.opensdk.di.a.n:
    com.kaolafm.opensdk.di.module.HttpClientModule module -> a
    javax.inject.Provider typeAdapterFactorySetProvider -> b
    17:20:void <init>(com.kaolafm.opensdk.di.module.HttpClientModule,javax.inject.Provider) -> <init>
    24:24:com.google.gson.Gson get() -> get
    29:29:com.google.gson.Gson provideInstance(com.kaolafm.opensdk.di.module.HttpClientModule,javax.inject.Provider) -> a
    34:34:com.kaolafm.opensdk.di.module.HttpClientModule_ProvideGsonFactory create(com.kaolafm.opensdk.di.module.HttpClientModule,javax.inject.Provider) -> create
    39:40:com.google.gson.Gson proxyProvideGson(com.kaolafm.opensdk.di.module.HttpClientModule,java.util.Set) -> a
    11:11:java.lang.Object get() -> get
com.kaolafm.opensdk.di.module.HttpClientModule_ProvideOkHttpClientBuilderFactory -> com.kaolafm.opensdk.di.a.o:
    com.kaolafm.opensdk.di.module.HttpClientModule module -> a
    javax.inject.Provider interceptorProvider -> b
    javax.inject.Provider handlerInterceptorProvider -> c
    javax.inject.Provider interceptorSetProvider -> d
    25:30:void <init>(com.kaolafm.opensdk.di.module.HttpClientModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    34:34:okhttp3.OkHttpClient$Builder get() -> get
    43:47:okhttp3.OkHttpClient$Builder provideInstance(com.kaolafm.opensdk.di.module.HttpClientModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> a
    55:55:com.kaolafm.opensdk.di.module.HttpClientModule_ProvideOkHttpClientBuilderFactory create(com.kaolafm.opensdk.di.module.HttpClientModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> create
    64:65:okhttp3.OkHttpClient$Builder proxyProvideOkHttpClientBuilder(com.kaolafm.opensdk.di.module.HttpClientModule,okhttp3.Interceptor,okhttp3.Interceptor,java.util.Set) -> a
    11:11:java.lang.Object get() -> get
com.kaolafm.opensdk.di.module.HttpClientModule_ProvideOkHttpClientFactory -> com.kaolafm.opensdk.di.a.p:
    com.kaolafm.opensdk.di.module.HttpClientModule module -> a
    javax.inject.Provider builderProvider -> b
    15:18:void <init>(com.kaolafm.opensdk.di.module.HttpClientModule,javax.inject.Provider) -> <init>
    22:22:okhttp3.OkHttpClient get() -> get
    27:27:okhttp3.OkHttpClient provideInstance(com.kaolafm.opensdk.di.module.HttpClientModule,javax.inject.Provider) -> a
    32:32:com.kaolafm.opensdk.di.module.HttpClientModule_ProvideOkHttpClientFactory create(com.kaolafm.opensdk.di.module.HttpClientModule,javax.inject.Provider) -> create
    37:38:okhttp3.OkHttpClient proxyProvideOkHttpClient(com.kaolafm.opensdk.di.module.HttpClientModule,okhttp3.OkHttpClient$Builder) -> a
    9:9:java.lang.Object get() -> get
com.kaolafm.opensdk.di.module.HttpClientModule_ProvideRetrofitFactory -> com.kaolafm.opensdk.di.a.q:
    com.kaolafm.opensdk.di.module.HttpClientModule module -> a
    javax.inject.Provider httpClientProvider -> b
    javax.inject.Provider httpUrlProvider -> c
    javax.inject.Provider gsonProvider -> d
    25:30:void <init>(com.kaolafm.opensdk.di.module.HttpClientModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    34:34:retrofit2.Retrofit get() -> get
    42:43:retrofit2.Retrofit provideInstance(com.kaolafm.opensdk.di.module.HttpClientModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> a
    51:51:com.kaolafm.opensdk.di.module.HttpClientModule_ProvideRetrofitFactory create(com.kaolafm.opensdk.di.module.HttpClientModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> create
    57:58:retrofit2.Retrofit proxyProvideRetrofit(com.kaolafm.opensdk.di.module.HttpClientModule,okhttp3.OkHttpClient,okhttp3.HttpUrl,com.google.gson.Gson) -> a
    12:12:java.lang.Object get() -> get
com.kaolafm.opensdk.di.module.HttpConfigModule -> com.kaolafm.opensdk.di.a.r:
    62:62:void <init>() -> <init>
    67:67:okhttp3.HttpUrl provideApiUrl() -> a
    73:73:okhttp3.Interceptor provideHttpHandlerInterceptor(HttpHandler) -> a
    okhttp3.Interceptor bindInterceptor(RequestInterceptor) -> a
    okhttp3.Interceptor bindQQMusicTokenInterceptor(com.kaolafm.opensdk.api.music.qq.QQMusicTokenInterceptor) -> a
    88:88:ResponseErrorListener provideErrorListenerList() -> b
    94:94:java.util.List provideResponseErrorListeners(java.util.Set) -> a
    100:102:com.google.gson.TypeAdapterFactory provideCategoryTypeFactory() -> c
    108:113:com.google.gson.TypeAdapterFactory provideCategoryMemberTypeFactory() -> d
    119:121:com.google.gson.TypeAdapterFactory provideColumnGrpTypeFactory() -> e
    127:136:com.google.gson.TypeAdapterFactory provideColumnMemberTypeFactory() -> f
    143:153:java.io.File provideCacheFile(android.app.Application) -> a
    160:160:LogLevel$RequestLevel providePrintHttpLogLevel() -> g
    166:166:Cache$Factory provideCacheFactory(android.app.Application) -> b
    73:73:okhttp3.Response lambda$provideHttpHandlerInterceptor$0(HttpHandler,okhttp3.Interceptor$Chain) -> a
com.kaolafm.opensdk.di.module.HttpConfigModule$1 -> com.kaolafm.opensdk.di.a.s:
    android.app.Application val$application -> a
    166:166:void <init>(android.app.Application) -> <init>
    170:176:Cache build(CacheType) -> a
com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideApiUrlFactory -> com.kaolafm.opensdk.di.a.t:
    com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideApiUrlFactory INSTANCE -> a
    8:8:void <init>() -> <init>
    14:14:okhttp3.HttpUrl get() -> get
    18:18:okhttp3.HttpUrl provideInstance() -> a
    22:22:com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideApiUrlFactory create() -> create
    26:27:okhttp3.HttpUrl proxyProvideApiUrl() -> b
    8:8:java.lang.Object get() -> get
    9:9:void <clinit>() -> <clinit>
com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideCacheFactoryFactory -> com.kaolafm.opensdk.di.a.u:
    javax.inject.Provider applicationProvider -> a
    13:15:void <init>(javax.inject.Provider) -> <init>
    19:19:Cache$Factory get() -> get
    23:23:Cache$Factory provideInstance(javax.inject.Provider) -> a
    28:28:com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideCacheFactoryFactory create(javax.inject.Provider) -> create
    32:33:Cache$Factory proxyProvideCacheFactory(android.app.Application) -> a
    10:10:java.lang.Object get() -> get
com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideCacheFileFactory -> com.kaolafm.opensdk.di.a.v:
    javax.inject.Provider applicationProvider -> a
    13:15:void <init>(javax.inject.Provider) -> <init>
    19:19:java.io.File get() -> get
    23:23:java.io.File provideInstance(javax.inject.Provider) -> a
    28:28:com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideCacheFileFactory create(javax.inject.Provider) -> create
    32:33:java.io.File proxyProvideCacheFile(android.app.Application) -> a
    10:10:java.lang.Object get() -> get
com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideCategoryMemberTypeFactoryFactory -> com.kaolafm.opensdk.di.a.w:
    com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideCategoryMemberTypeFactoryFactory INSTANCE -> a
    8:8:void <init>() -> <init>
    15:15:com.google.gson.TypeAdapterFactory get() -> get
    19:19:com.google.gson.TypeAdapterFactory provideInstance() -> a
    23:23:com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideCategoryMemberTypeFactoryFactory create() -> create
    27:28:com.google.gson.TypeAdapterFactory proxyProvideCategoryMemberTypeFactory() -> b
    8:8:java.lang.Object get() -> get
    10:10:void <clinit>() -> <clinit>
com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideCategoryTypeFactoryFactory -> com.kaolafm.opensdk.di.a.x:
    com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideCategoryTypeFactoryFactory INSTANCE -> a
    8:8:void <init>() -> <init>
    15:15:com.google.gson.TypeAdapterFactory get() -> get
    19:19:com.google.gson.TypeAdapterFactory provideInstance() -> a
    23:23:com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideCategoryTypeFactoryFactory create() -> create
    27:28:com.google.gson.TypeAdapterFactory proxyProvideCategoryTypeFactory() -> b
    8:8:java.lang.Object get() -> get
    10:10:void <clinit>() -> <clinit>
com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideColumnGrpTypeFactoryFactory -> com.kaolafm.opensdk.di.a.y:
    com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideColumnGrpTypeFactoryFactory INSTANCE -> a
    8:8:void <init>() -> <init>
    15:15:com.google.gson.TypeAdapterFactory get() -> get
    19:19:com.google.gson.TypeAdapterFactory provideInstance() -> a
    23:23:com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideColumnGrpTypeFactoryFactory create() -> create
    27:28:com.google.gson.TypeAdapterFactory proxyProvideColumnGrpTypeFactory() -> b
    8:8:java.lang.Object get() -> get
    10:10:void <clinit>() -> <clinit>
com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideColumnMemberTypeFactoryFactory -> com.kaolafm.opensdk.di.a.z:
    com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideColumnMemberTypeFactoryFactory INSTANCE -> a
    8:8:void <init>() -> <init>
    15:15:com.google.gson.TypeAdapterFactory get() -> get
    19:19:com.google.gson.TypeAdapterFactory provideInstance() -> a
    23:23:com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideColumnMemberTypeFactoryFactory create() -> create
    27:28:com.google.gson.TypeAdapterFactory proxyProvideColumnMemberTypeFactory() -> b
    8:8:java.lang.Object get() -> get
    10:10:void <clinit>() -> <clinit>
com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideErrorListenerListFactory -> com.kaolafm.opensdk.di.a.aa:
    com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideErrorListenerListFactory INSTANCE -> a
    8:8:void <init>() -> <init>
    15:15:ResponseErrorListener get() -> get
    19:19:ResponseErrorListener provideInstance() -> a
    23:23:com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideErrorListenerListFactory create() -> create
    27:28:ResponseErrorListener proxyProvideErrorListenerList() -> b
    8:8:java.lang.Object get() -> get
    10:10:void <clinit>() -> <clinit>
com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideHttpHandlerInterceptorFactory -> com.kaolafm.opensdk.di.a.ab:
    javax.inject.Provider httpHandlerProvider -> a
    15:17:void <init>(javax.inject.Provider) -> <init>
    21:21:okhttp3.Interceptor get() -> get
    25:25:okhttp3.Interceptor provideInstance(javax.inject.Provider) -> a
    30:30:com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideHttpHandlerInterceptorFactory create(javax.inject.Provider) -> create
    34:35:okhttp3.Interceptor proxyProvideHttpHandlerInterceptor(HttpHandler) -> a
    10:10:java.lang.Object get() -> get
com.kaolafm.opensdk.di.module.HttpConfigModule_ProvidePrintHttpLogLevelFactory -> com.kaolafm.opensdk.di.a.ac:
    com.kaolafm.opensdk.di.module.HttpConfigModule_ProvidePrintHttpLogLevelFactory INSTANCE -> a
    8:8:void <init>() -> <init>
    15:15:LogLevel$RequestLevel get() -> get
    19:19:LogLevel$RequestLevel provideInstance() -> a
    23:23:com.kaolafm.opensdk.di.module.HttpConfigModule_ProvidePrintHttpLogLevelFactory create() -> create
    27:28:LogLevel$RequestLevel proxyProvidePrintHttpLogLevel() -> b
    8:8:java.lang.Object get() -> get
    10:10:void <clinit>() -> <clinit>
com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideResponseErrorListenersFactory -> com.kaolafm.opensdk.di.a.ad:
    javax.inject.Provider listenerSetProvider -> a
    16:18:void <init>(javax.inject.Provider) -> <init>
    22:22:java.util.List get() -> get
    27:27:java.util.List provideInstance(javax.inject.Provider) -> a
    32:32:com.kaolafm.opensdk.di.module.HttpConfigModule_ProvideResponseErrorListenersFactory create(javax.inject.Provider) -> create
    37:38:java.util.List proxyProvideResponseErrorListeners(java.util.Set) -> a
    11:11:java.lang.Object get() -> get
com.kaolafm.opensdk.di.module.OperationModule -> com.kaolafm.opensdk.di.a.ae:
    23:23:void <init>() -> <init>
    com.kaolafm.opensdk.utils.operation.IOperationProcessor bindAlbumProcessor(com.kaolafm.opensdk.utils.operation.AlbumProcessor) -> a
    com.kaolafm.opensdk.utils.operation.IOperationProcessor bindBroadcastProcessor(com.kaolafm.opensdk.utils.operation.BroadcastProcessor) -> a
    com.kaolafm.opensdk.utils.operation.IOperationProcessor bindLiveProcessor(com.kaolafm.opensdk.utils.operation.LiveProcessor) -> a
    com.kaolafm.opensdk.utils.operation.IOperationProcessor bindQQMusicProcessor(com.kaolafm.opensdk.utils.operation.QQMusicProcessor) -> a
    com.kaolafm.opensdk.utils.operation.IOperationProcessor bindRadioProcessor(com.kaolafm.opensdk.utils.operation.RadioProcessor) -> a
    com.kaolafm.opensdk.utils.operation.IOperationProcessor bindCategoryProcessor(com.kaolafm.opensdk.utils.operation.CategoryProcessor) -> a
    51:51:java.util.List provideOperationProcessorList(java.util.Set) -> a
com.kaolafm.opensdk.di.module.OperationModule_ProvideOperationProcessorListFactory -> com.kaolafm.opensdk.di.a.af:
    javax.inject.Provider processorSetProvider -> a
    16:18:void <init>(javax.inject.Provider) -> <init>
    22:22:java.util.List get() -> get
    27:27:java.util.List provideInstance(javax.inject.Provider) -> a
    32:32:com.kaolafm.opensdk.di.module.OperationModule_ProvideOperationProcessorListFactory create(javax.inject.Provider) -> create
    37:38:java.util.List proxyProvideOperationProcessorList(java.util.Set) -> a
    11:11:java.lang.Object get() -> get
com.kaolafm.opensdk.di.module.QQMusicModule -> com.kaolafm.opensdk.di.a.ag:
    27:27:void <init>() -> <init>
    33:53:java.util.Map provideQQMusicCommonParam(com.kaolafm.opensdk.account.profile.QQMusicProfileManger,java.util.Map,com.kaolafm.opensdk.account.token.QQMusicAccessToken) -> a
    60:66:java.util.Map provideWxRefreshParams(com.kaolafm.opensdk.account.token.QQMusicAccessToken,java.util.Map) -> a
    73:79:java.util.Map provideQQRefreshParams(com.kaolafm.opensdk.account.token.QQMusicAccessToken,java.util.Map) -> b
    86:89:java.util.Map provideQQUserInfoParams(com.kaolafm.opensdk.account.token.QQMusicAccessToken,java.util.Map) -> c
    95:95:java.util.Map provideNewHashMap() -> a
    101:101:com.kaolafm.opensdk.account.token.QQMusicAccessToken provideQQMusicAccessToken(com.kaolafm.opensdk.account.token.AccessTokenManager) -> a
com.kaolafm.opensdk.di.module.QQMusicModule_ProvideNewHashMapFactory -> com.kaolafm.opensdk.di.a.ah:
    com.kaolafm.opensdk.di.module.QQMusicModule_ProvideNewHashMapFactory INSTANCE -> a
    8:8:void <init>() -> <init>
    14:14:java.util.Map get() -> get
    18:18:java.util.Map provideInstance() -> a
    22:22:com.kaolafm.opensdk.di.module.QQMusicModule_ProvideNewHashMapFactory create() -> create
    26:27:java.util.Map proxyProvideNewHashMap() -> b
    8:8:java.lang.Object get() -> get
    9:9:void <clinit>() -> <clinit>
com.kaolafm.opensdk.di.module.QQMusicModule_ProvideQQMusicAccessTokenFactory -> com.kaolafm.opensdk.di.a.ai:
    javax.inject.Provider accessTokenManagerProvider -> a
    15:17:void <init>(javax.inject.Provider) -> <init>
    21:21:com.kaolafm.opensdk.account.token.QQMusicAccessToken get() -> get
    26:26:com.kaolafm.opensdk.account.token.QQMusicAccessToken provideInstance(javax.inject.Provider) -> a
    31:31:com.kaolafm.opensdk.di.module.QQMusicModule_ProvideQQMusicAccessTokenFactory create(javax.inject.Provider) -> create
    36:37:com.kaolafm.opensdk.account.token.QQMusicAccessToken proxyProvideQQMusicAccessToken(com.kaolafm.opensdk.account.token.AccessTokenManager) -> a
    10:10:java.lang.Object get() -> get
com.kaolafm.opensdk.di.module.QQMusicModule_ProvideQQMusicCommonParamFactory -> com.kaolafm.opensdk.di.a.aj:
    javax.inject.Provider profileMangerProvider -> a
    javax.inject.Provider paramsProvider -> b
    javax.inject.Provider accessTokenProvider -> c
    22:26:void <init>(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    30:30:java.util.Map get() -> get
    37:38:java.util.Map provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> a
    45:45:com.kaolafm.opensdk.di.module.QQMusicModule_ProvideQQMusicCommonParamFactory create(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> create
    53:54:java.util.Map proxyProvideQQMusicCommonParam(com.kaolafm.opensdk.account.profile.QQMusicProfileManger,java.util.Map,com.kaolafm.opensdk.account.token.QQMusicAccessToken) -> a
    11:11:java.lang.Object get() -> get
com.kaolafm.opensdk.di.module.QQMusicModule_ProvideQQRefreshParamsFactory -> com.kaolafm.opensdk.di.a.ak:
    javax.inject.Provider accessTokenProvider -> a
    javax.inject.Provider paramsProvider -> b
    18:21:void <init>(javax.inject.Provider,javax.inject.Provider) -> <init>
    25:25:java.util.Map get() -> get
    31:31:java.util.Map provideInstance(javax.inject.Provider,javax.inject.Provider) -> a
    37:37:com.kaolafm.opensdk.di.module.QQMusicModule_ProvideQQRefreshParamsFactory create(javax.inject.Provider,javax.inject.Provider) -> create
    42:43:java.util.Map proxyProvideQQRefreshParams(com.kaolafm.opensdk.account.token.QQMusicAccessToken,java.util.Map) -> a
    10:10:java.lang.Object get() -> get
com.kaolafm.opensdk.di.module.QQMusicModule_ProvideQQUserInfoParamsFactory -> com.kaolafm.opensdk.di.a.al:
    javax.inject.Provider accessTokenProvider -> a
    javax.inject.Provider paramsProvider -> b
    18:21:void <init>(javax.inject.Provider,javax.inject.Provider) -> <init>
    25:25:java.util.Map get() -> get
    31:31:java.util.Map provideInstance(javax.inject.Provider,javax.inject.Provider) -> a
    37:37:com.kaolafm.opensdk.di.module.QQMusicModule_ProvideQQUserInfoParamsFactory create(javax.inject.Provider,javax.inject.Provider) -> create
    42:43:java.util.Map proxyProvideQQUserInfoParams(com.kaolafm.opensdk.account.token.QQMusicAccessToken,java.util.Map) -> a
    10:10:java.lang.Object get() -> get
com.kaolafm.opensdk.di.module.QQMusicModule_ProvideWxRefreshParamsFactory -> com.kaolafm.opensdk.di.a.am:
    javax.inject.Provider accessTokenProvider -> a
    javax.inject.Provider paramsProvider -> b
    18:21:void <init>(javax.inject.Provider,javax.inject.Provider) -> <init>
    25:25:java.util.Map get() -> get
    31:31:java.util.Map provideInstance(javax.inject.Provider,javax.inject.Provider) -> a
    37:37:com.kaolafm.opensdk.di.module.QQMusicModule_ProvideWxRefreshParamsFactory create(javax.inject.Provider,javax.inject.Provider) -> create
    42:43:java.util.Map proxyProvideWxRefreshParams(com.kaolafm.opensdk.account.token.QQMusicAccessToken,java.util.Map) -> a
    10:10:java.lang.Object get() -> get
com.kaolafm.opensdk.di.qualifier.AccessTokenQualifier -> com.kaolafm.opensdk.di.b.a:
com.kaolafm.opensdk.di.qualifier.CacheDir -> com.kaolafm.opensdk.di.b.b:
com.kaolafm.opensdk.di.qualifier.DomainQualifier -> com.kaolafm.opensdk.di.b.c:
com.kaolafm.opensdk.di.qualifier.HandlerInterceptor -> com.kaolafm.opensdk.di.b.d:
com.kaolafm.opensdk.di.qualifier.HttpInterceptor -> com.kaolafm.opensdk.di.b.e:
com.kaolafm.opensdk.di.qualifier.KaolaInitParam -> com.kaolafm.opensdk.di.b.f:
com.kaolafm.opensdk.di.qualifier.KaolaParam -> com.kaolafm.opensdk.di.b.g:
com.kaolafm.opensdk.di.qualifier.KradioParam -> com.kaolafm.opensdk.di.b.h:
com.kaolafm.opensdk.di.qualifier.ParamQualifier -> com.kaolafm.opensdk.di.b.i:
com.kaolafm.opensdk.di.qualifier.PathQualifier -> com.kaolafm.opensdk.di.b.j:
com.kaolafm.opensdk.di.qualifier.ProfileQualifier -> com.kaolafm.opensdk.di.b.k:
com.kaolafm.opensdk.di.qualifier.QQMusicNewParam -> com.kaolafm.opensdk.di.b.l:
com.kaolafm.opensdk.di.qualifier.QQMusicParam -> com.kaolafm.opensdk.di.b.m:
com.kaolafm.opensdk.di.qualifier.QQRefreshParam -> com.kaolafm.opensdk.di.b.n:
com.kaolafm.opensdk.di.qualifier.QQUserInfoParam -> com.kaolafm.opensdk.di.b.o:
com.kaolafm.opensdk.di.qualifier.WeChatRefreshParam -> com.kaolafm.opensdk.di.b.p:
AppScope -> com.kaolafm.opensdk.di.c.a:
com.kaolafm.opensdk.di.scope.QQMusicScope -> com.kaolafm.opensdk.di.c.b:
com.kaolafm.opensdk.di.scope.RequestScope -> com.kaolafm.opensdk.di.c.c:
com.kaolafm.opensdk.di.scope.SessionScope -> com.kaolafm.opensdk.di.c.d:
Cache -> com.kaolafm.opensdk.http.a.a:
    int size() -> a
    int getMaxSize() -> b
    java.lang.Object get(java.lang.Object) -> a
    java.lang.Object put(java.lang.Object,java.lang.Object) -> a
    java.lang.Object remove(java.lang.Object) -> b
    boolean containsKey(java.lang.Object) -> c
    java.util.Set keySet() -> c
    void clear() -> d
Cache$Factory -> com.kaolafm.opensdk.http.a.a$a:
    Cache build(CacheType) -> a
CacheType -> com.kaolafm.opensdk.http.a.b:
    int RETROFIT_SERVICE_CACHE_TYPE_ID -> a
    int CACHE_SERVICE_CACHE_TYPE_ID -> b
    int EXTRAS_TYPE_ID -> c
    int ACTIVITY_CACHE_TYPE_ID -> d
    int FRAGMENT_CACHE_TYPE_ID -> e
    CacheType RETROFIT_SERVICE_CACHE -> f
    CacheType CACHE_SERVICE_CACHE -> g
    CacheType EXTRAS -> h
    CacheType ACTIVITY_CACHE -> i
    CacheType FRAGMENT_CACHE -> j
    int getCacheTypeId() -> a
    int calculateCacheSize(android.content.Context) -> a
    26:117:void <clinit>() -> <clinit>
CacheType$1 -> com.kaolafm.opensdk.http.a.c:
    int MAX_SIZE -> k
    float MAX_SIZE_MULTIPLIER -> l
    26:26:void <init>() -> <init>
    32:32:int getCacheTypeId() -> a
    37:42:int calculateCacheSize(android.content.Context) -> a
CacheType$2 -> com.kaolafm.opensdk.http.a.d:
    int MAX_SIZE -> k
    float MAX_SIZE_MULTIPLIER -> l
    48:48:void <init>() -> <init>
    54:54:int getCacheTypeId() -> a
    59:64:int calculateCacheSize(android.content.Context) -> a
CacheType$3 -> com.kaolafm.opensdk.http.a.e:
    int MAX_SIZE -> k
    float MAX_SIZE_MULTIPLIER -> l
    71:71:void <init>() -> <init>
    77:77:int getCacheTypeId() -> a
    82:87:int calculateCacheSize(android.content.Context) -> a
CacheType$4 -> com.kaolafm.opensdk.http.a.f:
    int MAX_SIZE -> k
    float MAX_SIZE_MULTIPLIER -> l
    94:94:void <init>() -> <init>
    100:100:int getCacheTypeId() -> a
    105:110:int calculateCacheSize(android.content.Context) -> a
CacheType$5 -> com.kaolafm.opensdk.http.a.g:
    int MAX_SIZE -> k
    float MAX_SIZE_MULTIPLIER -> l
    117:117:void <init>() -> <init>
    123:123:int getCacheTypeId() -> a
    128:133:int calculateCacheSize(android.content.Context) -> a
IntelligentCache -> com.kaolafm.opensdk.http.a.h:
    java.lang.String KEY_KEEP -> a
    LruCache mCache -> b
    java.util.HashMap mMap -> c
    29:32:void <init>(int) -> <init>
    36:36:int size() -> a
    41:41:int getMaxSize() -> b
    52:55:java.lang.Object get(java.lang.String) -> a
    61:64:java.lang.Object put(java.lang.String,java.lang.Object) -> a
    70:73:java.lang.Object remove(java.lang.String) -> b
    78:81:boolean containsKey(java.lang.String) -> c
    90:92:java.util.Set keySet() -> c
    97:99:void clear() -> d
    21:21:boolean containsKey(java.lang.Object) -> c
    21:21:java.lang.Object remove(java.lang.Object) -> b
    21:21:java.lang.Object put(java.lang.Object,java.lang.Object) -> a
    21:21:java.lang.Object get(java.lang.Object) -> a
LruCache -> com.kaolafm.opensdk.http.a.i:
    java.util.LinkedHashMap mCache -> a
    int mCurrentSize -> b
    int mInitialMaxSize -> c
    int mMaxSize -> d
    15:26:void <init>(int) -> <init>
    30:30:int size() -> a
    35:35:int getMaxSize() -> b
    41:41:java.lang.Object get(java.lang.Object) -> a
    47:60:java.lang.Object put(java.lang.Object,java.lang.Object) -> a
    67:68:void evict() -> e
    77:85:void trimToSize(int) -> a
    95:95:void onItemEvicted(java.lang.Object,java.lang.Object) -> b
    105:105:int getItemSize(java.lang.Object) -> d
    111:111:java.lang.Object remove(java.lang.Object) -> b
    116:116:boolean containsKey(java.lang.Object) -> c
    121:121:java.util.Set keySet() -> c
    126:127:void clear() -> d
AutoDisposeObserver -> AutoDisposeObserver:
    java.util.concurrent.atomic.AtomicReference mDisposableAtomicReference -> a
    HttpCallback mCallback -> b
    19:25:void <init>(HttpCallback) -> <init>
    29:30:void dispose() -> dispose
    34:34:boolean isDisposed() -> isDisposed
    39:42:void onSubscribe(io.reactivex.disposables.Disposable) -> onSubscribe
    45:45:void onStart() -> a
    49:50:void onSuccess(java.lang.Object) -> onSuccess
    54:55:void onNext(java.lang.Object) -> onNext
    59:60:void onError(java.lang.Throwable) -> onError
    64:65:void onComplete() -> onComplete
    68:72:void success(java.lang.Object) -> a
    75:83:void error(java.lang.Throwable) -> a
FormatPrinter -> com.kaolafm.opensdk.http.core.a:
    java.lang.String TAG -> a
    java.lang.String LINE_SEPARATOR -> b
    java.lang.String DOUBLE_SEPARATOR -> c
    java.lang.String[] OMITTED_RESPONSE -> d
    java.lang.String[] OMITTED_REQUEST -> e
    java.lang.String N -> f
    java.lang.String T -> g
    java.lang.String REQUEST_UP_LINE -> h
    java.lang.String END_LINE -> i
    java.lang.String RESPONSE_UP_LINE -> j
    java.lang.String BODY_TAG -> k
    java.lang.String URL_TAG -> l
    java.lang.String METHOD_TAG -> m
    java.lang.String HEADERS_TAG -> n
    java.lang.String STATUS_CODE_TAG -> o
    java.lang.String RECEIVED_TAG -> p
    java.lang.String CORNER_UP -> q
    java.lang.String CORNER_BOTTOM -> r
    java.lang.String CENTER_LINE -> s
    java.lang.String DEFAULT_LINE -> t
    java.lang.ThreadLocal last -> u
    java.lang.String[] ARMS -> v
    65:66:void <init>() -> <init>
    69:69:boolean isEmpty(java.lang.String) -> a
    79:86:void printJsonRequest(okhttp3.Request,java.lang.String) -> a
    94:100:void printFileRequest(okhttp3.Request) -> a
    118:129:void printJsonResponse(long,boolean,int,java.lang.String,okhttp3.MediaType,java.lang.String,java.util.List,java.lang.String,java.lang.String) -> a
    144:151:void printFileResponse(long,boolean,int,java.lang.String,java.util.List,java.lang.String,java.lang.String) -> a
    162:174:java.lang.String logLines(java.lang.String[],boolean) -> a
    187:192:java.lang.String computeKey() -> a
    206:206:java.lang.String resolveTag(java.lang.String) -> b
    212:215:java.lang.String[] getRequest(okhttp3.Request) -> b
    221:226:java.lang.String[] getResponse(java.lang.String,long,int,boolean,java.util.List,java.lang.String) -> a
    230:234:java.lang.String slashSegments(java.util.List) -> a
    244:263:java.lang.String dotHeaders(java.lang.String) -> c
    268:271:java.lang.String getTag(boolean) -> a
    276:277:void logInfo(java.lang.String,java.lang.String) -> a
    23:184:void <clinit>() -> <clinit>
FormatPrinter$1 -> com.kaolafm.opensdk.http.core.b:
    177:177:void <init>() -> <init>
    180:180:java.lang.Integer initialValue() -> a
    177:177:java.lang.Object initialValue() -> initialValue
com.kaolafm.opensdk.http.core.FormatPrinter_Factory -> com.kaolafm.opensdk.http.core.c:
    com.kaolafm.opensdk.http.core.FormatPrinter_Factory INSTANCE -> a
    6:6:void <init>() -> <init>
    11:11:FormatPrinter get() -> get
    15:15:FormatPrinter provideInstance() -> a
    19:19:com.kaolafm.opensdk.http.core.FormatPrinter_Factory create() -> create
    23:23:FormatPrinter newFormatPrinter() -> b
    6:6:java.lang.Object get() -> get
    7:7:void <clinit>() -> <clinit>
HttpCallback -> HttpCallback:
    void onSuccess(java.lang.Object) -> onSuccess
    void onError(ApiException) -> onError
HttpHandler -> com.kaolafm.opensdk.http.core.d:
    RetrofitUrlManager mRetrofitUrlManager -> a
    25:26:void <init>() -> <init>
    34:35:okhttp3.Request onHttpRequestBefore(okhttp3.Interceptor$Chain,okhttp3.Request) -> a
    42:46:okhttp3.Response onHttpResultResponse(okhttp3.Interceptor$Chain,okhttp3.Response) -> a
com.kaolafm.opensdk.http.core.HttpHandler_Factory -> com.kaolafm.opensdk.http.core.e:
    javax.inject.Provider mRetrofitUrlManagerProvider -> a
    10:12:void <init>(javax.inject.Provider) -> <init>
    16:16:HttpHandler get() -> get
    21:24:HttpHandler provideInstance(javax.inject.Provider) -> a
    29:29:com.kaolafm.opensdk.http.core.HttpHandler_Factory create(javax.inject.Provider) -> create
    33:33:HttpHandler newHttpHandler() -> a
    7:7:java.lang.Object get() -> get
com.kaolafm.opensdk.http.core.HttpHandler_MembersInjector -> com.kaolafm.opensdk.http.core.f:
    javax.inject.Provider mRetrofitUrlManagerProvider -> a
    10:12:void <init>(javax.inject.Provider) -> <init>
    16:16:dagger.MembersInjector create(javax.inject.Provider) -> a
    21:22:void injectMembers(HttpHandler) -> a
    26:27:void injectMRetrofitUrlManager(HttpHandler,RetrofitUrlManager) -> a
    7:7:void injectMembers(java.lang.Object) -> a
IRepositoryManager -> com.kaolafm.opensdk.http.core.g:
    java.lang.Object obtainRetrofitService(java.lang.Class) -> a
    java.lang.Object obtainCacheService(java.lang.Class) -> b
    void clearAllCache() -> a
    void doHttpDeal(io.reactivex.Observable) -> a
    void doHttpDeal(java.lang.Object,io.reactivex.Observable,HttpCallback) -> a
    void doHttpDeal(java.lang.Object,io.reactivex.Observable,io.reactivex.functions.Function,HttpCallback) -> a
    void doHttpDeal(io.reactivex.Observable,io.reactivex.functions.Function) -> a
    void doHttpDeal(io.reactivex.ObservableTransformer,io.reactivex.Observable,io.reactivex.functions.Function,HttpCallback) -> a
    void doHttpDeal(io.reactivex.ObservableTransformer,io.reactivex.Observable,HttpCallback) -> a
    void doHttpDeal(io.reactivex.Single,io.reactivex.functions.Function,HttpCallback) -> a
    io.reactivex.Single doHttpDeal(io.reactivex.Single,io.reactivex.functions.Function) -> a
    void doHttpDeal(io.reactivex.Single,HttpCallback) -> a
    void doHttpDeal(io.reactivex.Single) -> a
    void doHttpDeal(io.reactivex.SingleTransformer,io.reactivex.Single,HttpCallback) -> a
    void doHttpDeal(java.lang.Object,io.reactivex.Single,HttpCallback) -> a
    void doHttpDeal(io.reactivex.SingleTransformer,io.reactivex.Single,io.reactivex.functions.Function,HttpCallback) -> a
    void doHttpDeal(java.lang.Object,io.reactivex.Single,io.reactivex.functions.Function,HttpCallback) -> a
    java.lang.Object doHttpDealSync(retrofit2.Call) -> a
    retrofit2.Response doHttpDealSyncResponse(retrofit2.Call) -> b
    void cancel(java.lang.Object) -> a
ObservableTokenRefresh -> ObservableTokenRefresh:
    13:13:void <init>() -> <init>
    19:19:io.reactivex.ObservableSource apply(io.reactivex.Observable) -> apply
    13:13:java.lang.Object apply(java.lang.Object) -> apply
    20:21:io.reactivex.ObservableSource lambda$apply$0(java.lang.Throwable) -> b
RepositoryManager -> com.kaolafm.opensdk.http.core.h:
    Cache$Factory mCacheFactory -> a
    dagger.Lazy mRetrofit -> b
    java.util.List mResponseErrorListenerList -> c
    Cache mRetrofitServiceCache -> f
    Cache mCacheServiceCache -> g
    java.util.HashMap mDisposables -> d
    java.util.Map mLifeCycleMap -> e
    62:68:void <init>() -> <init>
    72:80:java.lang.Object obtainRetrofitService(java.lang.Class) -> a
    85:93:java.lang.Object obtainCacheService(java.lang.Class) -> b
    99:99:void clearAllCache() -> a
    117:117:void doHttpDeal(io.reactivex.Observable) -> a
    129:130:void doHttpDeal(java.lang.Object,io.reactivex.Observable,HttpCallback) -> a
    145:148:void doHttpDeal(java.lang.Object,io.reactivex.Observable,io.reactivex.functions.Function,HttpCallback) -> a
    160:161:void doHttpDeal(io.reactivex.Observable,io.reactivex.functions.Function) -> a
    166:170:void doHttpDeal(io.reactivex.ObservableTransformer,io.reactivex.Observable,io.reactivex.functions.Function,HttpCallback) -> a
    175:179:void doHttpDeal(io.reactivex.ObservableTransformer,io.reactivex.Observable,HttpCallback) -> a
    185:190:io.reactivex.Observable configObservable(io.reactivex.Observable) -> b
    194:195:void subscribeObservable(io.reactivex.Observable,HttpCallback) -> a
    198:203:void subscribeObservable(java.lang.Object,io.reactivex.Observable,HttpCallback) -> b
    218:221:void doHttpDeal(io.reactivex.Single,io.reactivex.functions.Function,HttpCallback) -> a
    225:229:io.reactivex.Single doHttpDeal(io.reactivex.Single,io.reactivex.functions.Function) -> a
    235:239:void doHttpDeal(io.reactivex.SingleTransformer,io.reactivex.Single,io.reactivex.functions.Function,HttpCallback) -> a
    243:244:void doHttpDeal(java.lang.Object,io.reactivex.Single,io.reactivex.functions.Function,HttpCallback) -> a
    255:256:void doHttpDeal(io.reactivex.Single,HttpCallback) -> a
    265:266:void doHttpDeal(io.reactivex.Single) -> a
    277:281:void doHttpDeal(io.reactivex.SingleTransformer,io.reactivex.Single,HttpCallback) -> a
    285:286:void doHttpDeal(java.lang.Object,io.reactivex.Single,HttpCallback) -> a
    289:294:io.reactivex.Single configSingle(io.reactivex.Single) -> b
    298:299:void subscribeSingle(io.reactivex.Single,HttpCallback) -> b
    302:307:void subscribeSingle(java.lang.Object,io.reactivex.Single,HttpCallback) -> b
    314:324:void removeDisposable(java.lang.Object,io.reactivex.disposables.Disposable) -> a
    330:338:void addDisposable(java.lang.Object,io.reactivex.disposables.Disposable) -> b
    343:350:void cancel(java.lang.Object) -> a
    353:363:void cancel() -> b
    370:379:java.lang.Object handleResultError(java.lang.Object) -> b
    389:390:java.lang.Object doHttpDealSync(retrofit2.Call) -> a
    396:400:retrofit2.Response doHttpDealSyncResponse(retrofit2.Call) -> b
    304:304:void lambda$subscribeSingle$3(java.lang.Object,AutoDisposeObserver) -> a
    289:289:io.reactivex.SingleSource lambda$configSingle$2(io.reactivex.Single) -> c
    200:200:void lambda$subscribeObservable$1(java.lang.Object,AutoDisposeObserver) -> b
    185:185:io.reactivex.ObservableSource lambda$configObservable$0(io.reactivex.Observable) -> c
com.kaolafm.opensdk.http.core.RepositoryManager_Factory -> com.kaolafm.opensdk.http.core.i:
    javax.inject.Provider mCacheFactoryProvider -> a
    javax.inject.Provider mRetrofitProvider -> b
    javax.inject.Provider mResponseErrorListenerListProvider -> c
    22:26:void <init>(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    30:30:RepositoryManager get() -> get
    38:44:RepositoryManager provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> a
    51:51:com.kaolafm.opensdk.http.core.RepositoryManager_Factory create(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> create
    56:56:RepositoryManager newRepositoryManager() -> a
    12:12:java.lang.Object get() -> get
com.kaolafm.opensdk.http.core.RepositoryManager_MembersInjector -> com.kaolafm.opensdk.http.core.j:
    javax.inject.Provider mCacheFactoryProvider -> a
    javax.inject.Provider mRetrofitProvider -> b
    javax.inject.Provider mResponseErrorListenerListProvider -> c
    23:27:void <init>(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    33:33:dagger.MembersInjector create(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> a
    39:42:void injectMembers(RepositoryManager) -> a
    45:46:void injectMCacheFactory(RepositoryManager,Cache$Factory) -> a
    49:50:void injectMRetrofit(RepositoryManager,dagger.Lazy) -> a
    54:55:void injectMResponseErrorListenerList(RepositoryManager,java.util.List) -> a
    13:13:void injectMembers(java.lang.Object) -> a
RequestInterceptor -> com.kaolafm.opensdk.http.core.k:
    HttpHandler mHttpHandler -> a
    FormatPrinter mPrinter -> b
    LogLevel$RequestLevel mLogLevel -> c
    47:47:void <init>() -> <init>
    51:99:okhttp3.Response intercept(okhttp3.Interceptor$Chain) -> intercept
    109:125:java.lang.String printResult(okhttp3.Response) -> a
    133:147:java.lang.String parseContent(okhttp3.ResponseBody,java.lang.String,okio.Buffer) -> a
    156:171:java.lang.String parseParams(okhttp3.Request) -> a
    179:181:boolean isParsable(okhttp3.MediaType) -> a
    185:188:boolean isText(okhttp3.MediaType) -> b
    192:195:boolean isPlain(okhttp3.MediaType) -> c
    199:202:boolean isJson(okhttp3.MediaType) -> d
    206:209:boolean isXml(okhttp3.MediaType) -> e
    213:216:boolean isHtml(okhttp3.MediaType) -> f
    220:223:boolean isForm(okhttp3.MediaType) -> g
    227:232:java.lang.String convertCharset(java.nio.charset.Charset) -> a
com.kaolafm.opensdk.http.core.RequestInterceptor_Factory -> com.kaolafm.opensdk.http.core.l:
    javax.inject.Provider mHttpHandlerProvider -> a
    javax.inject.Provider mPrinterProvider -> b
    javax.inject.Provider mLogLevelProvider -> c
    18:22:void <init>(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    26:26:RequestInterceptor get() -> get
    33:37:RequestInterceptor provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> a
    44:44:com.kaolafm.opensdk.http.core.RequestInterceptor_Factory create(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> create
    49:49:RequestInterceptor newRequestInterceptor() -> a
    8:8:java.lang.Object get() -> get
com.kaolafm.opensdk.http.core.RequestInterceptor_MembersInjector -> com.kaolafm.opensdk.http.core.m:
    javax.inject.Provider mHttpHandlerProvider -> a
    javax.inject.Provider mPrinterProvider -> b
    javax.inject.Provider mLogLevelProvider -> c
    19:23:void <init>(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    29:29:dagger.MembersInjector create(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> a
    35:38:void injectMembers(RequestInterceptor) -> a
    41:42:void injectMHttpHandler(RequestInterceptor,HttpHandler) -> a
    45:46:void injectMPrinter(RequestInterceptor,FormatPrinter) -> a
    49:50:void injectMLogLevel(RequestInterceptor,LogLevel$RequestLevel) -> a
    8:8:void injectMembers(java.lang.Object) -> a
Response -> com.kaolafm.opensdk.http.core.n:
    int code -> code
    java.lang.String message -> message
    12:12:void <init>() -> <init>
    21:21:int getCode() -> getCode
    25:26:void setCode(int) -> setCode
    29:29:java.lang.String getMessage() -> getMessage
    33:34:void setMessage(java.lang.String) -> setMessage
RetrofitUrlManager -> RetrofitUrlManager:
    java.lang.String POST -> POST
    java.lang.String GLOBAL_DOMAIN_NAME -> GLOBAL_DOMAIN_NAME
    java.util.Map mDomainNames -> mDomainNames
    java.util.Map mCommonParams -> mCommonParams
    java.util.Map mRealtimeParams -> mRealtimeParams
    java.lang.String IDENTIFICATION_IGNORE -> IDENTIFICATION_IGNORE
    java.lang.String DOMAIN_NAME -> DOMAIN_NAME
    java.lang.String DEMAIN_NAME_HEADER -> DEMAIN_NAME_HEADER
    com.kaolafm.opensdk.account.profile.KaolaProfile mKaolaProfile -> mKaolaProfile
    54:71:void <init>() -> <init>
    77:134:okhttp3.Request processRequest(okhttp3.Request) -> processRequest
    142:177:okhttp3.HttpUrl$Builder buildUrl(okhttp3.Request,okhttp3.Request$Builder,okhttp3.HttpUrl) -> buildUrl
    184:194:java.util.Map createKradioSign(java.util.Map,okhttp3.HttpUrl,java.lang.String) -> createKradioSign
    202:235:okhttp3.Request$Builder processPost(okhttp3.Request,java.util.Map) -> processPost
    240:249:java.lang.String bodyToString(okhttp3.RequestBody) -> bodyToString
    254:258:okhttp3.HttpUrl getGlobalDomain() -> getGlobalDomain
    264:270:okhttp3.HttpUrl fetchDomain(java.util.List) -> fetchDomain
    274:278:okhttp3.HttpUrl fetchDomain(java.lang.String) -> fetchDomain
    290:303:void putDomainAndParams(java.lang.String,java.lang.String,java.util.Map) -> putDomainAndParams
    313:320:void putDomain(java.lang.String,java.lang.String) -> putDomain
    330:337:void putCommonParams(java.lang.String,javax.inject.Provider) -> putCommonParams
    347:358:void putCommonParams(java.lang.String,java.lang.String,java.lang.String) -> putCommonParams
    364:364:java.lang.String setIdentificationIgnore(java.lang.String) -> setIdentificationIgnore
    371:376:okhttp3.Request pruneIdentification(okhttp3.Request$Builder,java.lang.String) -> pruneIdentification
    383:386:void addRealtimeParamListener(java.lang.String,RetrofitUrlManager$RealtimeParamListener) -> addRealtimeParamListener
    400:400:boolean useHttps() -> useHttps
    355:355:java.util.Map lambda$putCommonParams$1(java.util.Map) -> lambda$putCommonParams$1
    299:299:java.util.Map lambda$putDomainAndParams$0(java.util.Map) -> lambda$putDomainAndParams$0
RetrofitUrlManager$RealtimeParamListener -> RetrofitUrlManager$a:
    java.util.Map getParam() -> getParam
com.kaolafm.opensdk.http.core.RetrofitUrlManager_Factory -> com.kaolafm.opensdk.http.core.o:
    javax.inject.Provider mDomainNamesProvider -> a
    javax.inject.Provider mCommonParamsProvider -> b
    javax.inject.Provider mKaolaProfileProvider -> c
    19:23:void <init>(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    27:27:RetrofitUrlManager get() -> get
    34:38:RetrofitUrlManager provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> a
    45:45:com.kaolafm.opensdk.http.core.RetrofitUrlManager_Factory create(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> create
    50:50:RetrofitUrlManager newRetrofitUrlManager() -> a
    9:9:java.lang.Object get() -> get
com.kaolafm.opensdk.http.core.RetrofitUrlManager_MembersInjector -> com.kaolafm.opensdk.http.core.p:
    javax.inject.Provider mDomainNamesProvider -> a
    javax.inject.Provider mCommonParamsProvider -> b
    javax.inject.Provider mKaolaProfileProvider -> c
    20:24:void <init>(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    30:30:dagger.MembersInjector create(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> a
    36:39:void injectMembers(RetrofitUrlManager) -> a
    43:44:void injectMDomainNames(RetrofitUrlManager,java.util.Map) -> a
    48:49:void injectMCommonParams(RetrofitUrlManager,java.util.Map) -> b
    52:53:void injectMKaolaProfile(RetrofitUrlManager,com.kaolafm.opensdk.account.profile.KaolaProfile) -> a
    9:9:void injectMembers(java.lang.Object) -> a
RuntimeTypeAdapterFactory -> RuntimeTypeAdapterFactory:
    java.lang.Class baseType -> baseType
    java.lang.String typeFieldName -> typeFieldName
    java.util.Map labelToSubtype -> labelToSubtype
    java.util.Map subtypeToLabel -> subtypeToLabel
    boolean maintainType -> maintainType
    130:143:void <init>(java.lang.Class,java.lang.String,boolean) -> <init>
    151:151:RuntimeTypeAdapterFactory of(java.lang.Class,java.lang.String,boolean) -> of
    159:159:RuntimeTypeAdapterFactory of(java.lang.Class,java.lang.String) -> of
    167:167:RuntimeTypeAdapterFactory of(java.lang.Class) -> of
    178:186:RuntimeTypeAdapterFactory registerSubtype(java.lang.Class,java.lang.String) -> registerSubtype
    197:197:RuntimeTypeAdapterFactory registerSubtype(java.lang.Class) -> registerSubtype
    202:268:com.google.gson.TypeAdapter create(com.google.gson.Gson,com.google.gson.reflect.TypeToken) -> create
    124:124:boolean access$000(RuntimeTypeAdapterFactory) -> access$000
    124:124:java.lang.String access$100(RuntimeTypeAdapterFactory) -> access$100
    124:124:java.lang.Class access$200(RuntimeTypeAdapterFactory) -> access$200
    124:124:java.util.Map access$300(RuntimeTypeAdapterFactory) -> access$300
RuntimeTypeAdapterFactory$1 -> RuntimeTypeAdapterFactory$1:
    java.util.Map val$labelToDelegate -> a
    java.util.Map val$subtypeToDelegate -> b
    RuntimeTypeAdapterFactory this$0 -> c
    216:216:void <init>(RuntimeTypeAdapterFactory,java.util.Map,java.util.Map) -> <init>
    220:239:java.lang.Object read(com.google.gson.stream.JsonReader) -> read
    244:267:void write(com.google.gson.stream.JsonWriter,java.lang.Object) -> write
SingleTokenRefresh -> SingleTokenRefresh:
    13:13:void <init>() -> <init>
    18:18:org.reactivestreams.Publisher apply(io.reactivex.Flowable) -> apply
    13:13:java.lang.Object apply(java.lang.Object) -> apply
    19:20:org.reactivestreams.Publisher lambda$apply$0(java.lang.Throwable) -> b
TokenRefresh -> com.kaolafm.opensdk.http.core.q:
    14:14:void <init>() -> <init>
    19:28:io.reactivex.Single refreshToken(java.lang.Throwable) -> a
ApiException -> ApiException:
    int code -> code
    java.lang.String message -> message
    boolean haveShow -> haveShow
    18:21:void <init>() -> <init>
    18:25:void <init>(java.lang.String) -> <init>
    18:30:void <init>(int,java.lang.String) -> <init>
    18:34:void <init>(java.lang.Throwable) -> <init>
    37:37:int getCode() -> getCode
    41:42:void setCode(int) -> setCode
    46:46:java.lang.String getMessage() -> getMessage
    50:51:void setMessage(java.lang.String) -> setMessage
    54:54:boolean isHaveShow() -> isHaveShow
    58:59:void setHaveShow(boolean) -> setHaveShow
    63:63:java.lang.String toString() -> toString
BaseErrorFunc -> BaseErrorFunc:
    java.util.List mErrorListenerList -> a
    19:21:void <init>(java.util.List) -> <init>
    25:34:ApiException handleError(java.lang.Throwable) -> a
    40:40:java.lang.Object apply(java.lang.Throwable) -> apply
    java.lang.Object getError(ApiException) -> a
    15:15:java.lang.Object apply(java.lang.Object) -> apply
ErrorCode -> com.kaolafm.opensdk.http.error.a:
    int HTTP_SERVICE_ERROR -> a
    int HTTP_SERVICE_UNAVAILABLE -> b
    int HTTP_HOST_NOT_EXIST -> c
    int HTTP_SERVICE_REJECTED -> d
    int HTTP_REQUEST_TIME_OUT -> e
    int HTTP_REQUEST_REDIRECTED -> f
    int UNKNOWN_ERROR -> g
    int JSON_PARSE_ERROR -> h
    int HTTP_UNKNOWN_HOST -> i
    int HTTP_CONNECT_ERROR -> j
    int HTTP_CONNECT_TIMEOUT -> k
    int HTTP_RESULT_ERROR -> l
    int HTTP_RESULT_NULL -> m
    int HTTP_NET_NOT_AVAILABLE -> n
    int TOKEN_INVALID -> o
    int TOKEN_EXPIRED -> p
    int REFRESH_TOKEN_EXPIRED -> q
    7:7:void <init>() -> <init>
HandleResponseError -> com.kaolafm.opensdk.http.error.b:
    26:27:void <init>() -> <init>
    33:35:void handleError(java.lang.Throwable,HttpCallback) -> a
    39:73:ApiException getApiException(java.lang.Throwable) -> b
    78:99:java.lang.String convertStatusCode(retrofit2.HttpException) -> a
    103:103:ApiException handleError(java.lang.Throwable) -> a
com.kaolafm.opensdk.http.error.HandleResponseError_Factory -> com.kaolafm.opensdk.http.error.c:
    com.kaolafm.opensdk.http.error.HandleResponseError_Factory INSTANCE -> a
    6:6:void <init>() -> <init>
    11:11:HandleResponseError get() -> get
    15:15:HandleResponseError provideInstance() -> a
    19:19:com.kaolafm.opensdk.http.error.HandleResponseError_Factory create() -> create
    23:23:HandleResponseError newHandleResponseError() -> b
    6:6:java.lang.Object get() -> get
    7:7:void <clinit>() -> <clinit>
ObservableErrorFunc -> com.kaolafm.opensdk.http.error.d:
    14:15:void <init>(java.util.List) -> <init>
    19:19:io.reactivex.ObservableSource getError(ApiException) -> b
    11:11:java.lang.Object getError(ApiException) -> a
ResponseErrorListener -> com.kaolafm.opensdk.http.error.e:
    ResponseErrorListener EMPTY -> EMPTY
    void handleError(ApiException) -> handleError
    14:14:void lambda$static$0(ApiException) -> lambda$static$0
    14:14:void <clinit>() -> <clinit>
SingleErrorFunc -> com.kaolafm.opensdk.http.error.f:
    14:15:void <init>(java.util.List) -> <init>
    19:19:io.reactivex.SingleSource getError(ApiException) -> b
    11:11:java.lang.Object getError(ApiException) -> a
DefaultPrinter -> DefaultPrinter:
    int CHUNK_SIZE -> CHUNK_SIZE
    int MIN_STACK_OFFSET -> MIN_STACK_OFFSET
    char TOP_LEFT_CORNER -> TOP_LEFT_CORNER
    char BOTTOM_LEFT_CORNER -> BOTTOM_LEFT_CORNER
    char MIDDLE_CORNER -> MIDDLE_CORNER
    char HORIZONTAL_LINE -> HORIZONTAL_LINE
    java.lang.String DOUBLE_DIVIDER -> DOUBLE_DIVIDER
    java.lang.String SINGLE_DIVIDER -> SINGLE_DIVIDER
    java.lang.String TOP_BORDER -> TOP_BORDER
    java.lang.String BOTTOM_BORDER -> BOTTOM_BORDER
    java.lang.String MIDDLE_BORDER -> MIDDLE_BORDER
    int JSON_INDENT -> JSON_INDENT
    int methodCount -> methodCount
    int methodOffset -> methodOffset
    boolean showThreadInfo -> showThreadInfo
    java.lang.String tag -> tag
    74:79:void <init>() -> <init>
    81:86:void <init>(java.lang.String) -> <init>
    91:91:Printer tag(java.lang.String) -> tag
    96:97:void d(java.lang.String,java.lang.Object[]) -> d
    101:102:void d(java.lang.Object) -> d
    106:107:void e(java.lang.String,java.lang.Object[]) -> e
    111:112:void e(java.lang.Throwable,java.lang.String,java.lang.Object[]) -> e
    116:117:void w(java.lang.String,java.lang.Object[]) -> w
    121:122:void i(java.lang.String,java.lang.Object[]) -> i
    126:127:void v(java.lang.String,java.lang.Object[]) -> v
    131:132:void wtf(java.lang.String,java.lang.Object[]) -> wtf
    136:158:void json(java.lang.String) -> json
    162:177:void xml(java.lang.String) -> xml
    181:190:void println(int,java.lang.String,java.lang.String,java.lang.Throwable) -> println
    193:193:java.lang.String formatMessage(java.lang.String,java.lang.Object[]) -> formatMessage
    198:200:void log(int,java.lang.Throwable,java.lang.String,java.lang.Object[]) -> log
    203:230:void log(int,java.lang.String,java.lang.String) -> log
    233:234:void logTopBorder(int,java.lang.String) -> logTopBorder
    238:273:void logHeaderContent(int,java.lang.String,int) -> logHeaderContent
    276:277:void logBottomBorder(int,java.lang.String) -> logBottomBorder
    280:281:void logDivider(int,java.lang.String) -> logDivider
    284:291:void logContent(int,java.lang.String,java.lang.String) -> logContent
    294:297:void logChunk(int,java.lang.String,java.lang.String) -> logChunk
    300:304:java.lang.String getSimpleClassName(java.lang.String) -> getSimpleClassName
    314:321:int getStackOffset(java.lang.StackTraceElement[]) -> getStackOffset
    326:329:java.lang.String formatTag(java.lang.String) -> formatTag
LogLevel -> LogLevel:
    int VERBOSE -> VERBOSE
    int DEBUG -> DEBUG
    int INFO -> INFO
    int WARN -> WARN
    int ERROR -> ERROR
    int ASSERT -> ASSERT
    int ALL -> ALL
    int NONE -> NONE
    9:9:void <init>() -> <init>
LogLevel$RequestLevel -> LogLevel$RequestLevel:
    LogLevel$RequestLevel NONE -> NONE
    LogLevel$RequestLevel REQUEST -> REQUEST
    LogLevel$RequestLevel RESPONSE -> RESPONSE
    LogLevel$RequestLevel ALL -> ALL
    LogLevel$RequestLevel[] $VALUES -> $VALUES
    51:51:LogLevel$RequestLevel[] values() -> values
    51:51:LogLevel$RequestLevel valueOf(java.lang.String) -> valueOf
    51:51:void <init>(java.lang.String,int) -> <init>
    51:67:void <clinit>() -> <clinit>
Logging -> Logging:
    Printer printer -> printer
    boolean isDebug -> isDebug
    LogLevel$RequestLevel mRequestLevel -> mRequestLevel
    22:23:void <init>() -> <init>
    26:27:void printer(Printer) -> printer
    32:32:Printer tag(java.lang.String) -> tag
    40:43:void log(int,java.lang.String,java.lang.String,java.lang.Throwable) -> log
    49:52:void log(int,java.lang.String,java.lang.String) -> log
    55:58:void d(java.lang.String,java.lang.Object[]) -> d
    61:64:void d(java.lang.Object) -> d
    67:70:void e(java.lang.String,java.lang.Object[]) -> e
    73:76:void e(java.lang.Throwable,java.lang.String,java.lang.Object[]) -> e
    79:82:void i(java.lang.String,java.lang.Object[]) -> i
    85:88:void v(java.lang.String,java.lang.Object[]) -> v
    91:94:void w(java.lang.String,java.lang.Object[]) -> w
    100:103:void wtf(java.lang.String,java.lang.Object[]) -> wtf
    109:112:void json(java.lang.String) -> json
    118:121:void xml(java.lang.String) -> xml
    128:129:void setDebug(boolean) -> setDebug
    132:132:boolean isDebug() -> isDebug
    136:136:LogLevel$RequestLevel getRequestLevel() -> getRequestLevel
    140:141:void setRequestLevel(LogLevel$RequestLevel) -> setRequestLevel
    16:20:void <clinit>() -> <clinit>
Printer -> Printer:
    Printer tag(java.lang.String) -> tag
    void d(java.lang.String,java.lang.Object[]) -> d
    void d(java.lang.Object) -> d
    void e(java.lang.String,java.lang.Object[]) -> e
    void e(java.lang.Throwable,java.lang.String,java.lang.Object[]) -> e
    void w(java.lang.String,java.lang.Object[]) -> w
    void i(java.lang.String,java.lang.Object[]) -> i
    void v(java.lang.String,java.lang.Object[]) -> v
    void wtf(java.lang.String,java.lang.Object[]) -> wtf
    void json(java.lang.String) -> json
    void xml(java.lang.String) -> xml
    void println(int,java.lang.String,java.lang.String,java.lang.Throwable) -> println
com.kaolafm.opensdk.observers.IInitObserver -> com.kaolafm.opensdk.a.a:
    void init() -> a
    void activate() -> b
    void release() -> c
com.kaolafm.opensdk.observers.IPlayerStateObserver -> com.kaolafm.opensdk.a.b:
com.kaolafm.opensdk.player.LivePlayerManager -> com.kaolafm.opensdk.player.LivePlayerManager:
    int SEC_DIVISOR -> SEC_DIVISOR
    com.kaolafm.sdk.core.mediaplayer.PlayerService$PlayerBinder mPlayerBinder -> mPlayerBinder
    boolean isLiveBroadcastPlayerEnable -> isLiveBroadcastPlayerEnable
    boolean mReplay -> mReplay
    boolean isSeekEvent -> isSeekEvent
    long mPrePositionGottedTime -> mPrePositionGottedTime
    com.kaolafm.sdk.core.mediaplayer.PlayItem mPlayItem -> mPlayItem
    java.util.List mUnAddedPlayerStateListeners -> mUnAddedPlayerStateListeners
    int mCurrentPosition -> mCurrentPosition
    long mAudioPlayedTime -> mAudioPlayedTime
    com.kaolafm.sdk.core.mediaplayer.IPlayerStateListener mPlayerStateListener -> mPlayerStateListener
    java.util.ArrayList mGetContentGeneralCallbacks -> mGetContentGeneralCallbacks
    32:359:void <init>() -> <init>
    71:71:com.kaolafm.opensdk.player.LivePlayerManager getInstance() -> getInstance
    80:81:void init(com.kaolafm.sdk.core.mediaplayer.PlayerService$PlayerBinder) -> init
    85:85:void start(com.kaolafm.sdk.core.mediaplayer.PlayItem) -> start
    89:89:void start() -> start
    92:92:com.kaolafm.sdk.core.mediaplayer.PlayItem getPlayItem() -> getPlayItem
    101:102:void play(com.kaolafm.sdk.core.mediaplayer.PlayItem) -> play
    106:109:java.lang.String getRadioId() -> getRadioId
    114:125:void start(com.kaolafm.sdk.core.mediaplayer.PlayItem,boolean) -> start
    129:138:void play() -> play
    142:153:void pause() -> pause
    156:156:com.kaolafm.sdk.core.mediaplayer.PlayItem getCurrentPlayItem() -> getCurrentPlayItem
    161:166:void stop() -> stop
    170:179:void reset() -> reset
    183:184:void release() -> release
    188:202:void switchPlayerStatus() -> switchPlayerStatus
    206:210:boolean isPlaying() -> isPlaying
    215:223:void seek(int) -> seek
    228:228:void playPre() -> playPre
    233:233:void playNext() -> playNext
    237:246:void destroy() -> destroy
    250:256:void enablePlayer() -> enablePlayer
    260:264:void disableOtherPlayer() -> disableOtherPlayer
    268:272:void setVolume(float,float) -> setVolume
    276:283:boolean hasNext() -> hasNext
    288:289:boolean hasPre() -> hasPre
    293:295:void disablePlayer() -> disablePlayer
    298:298:boolean isPlayerEnable() -> isPlayerEnable
    307:307:boolean checkBinderIsNull() -> checkBinderIsNull
    312:313:void removeSelfPlayerStateListener() -> removeSelfPlayerStateListener
    320:328:void removePlayerStateListener(com.kaolafm.sdk.core.mediaplayer.IPlayerStateListener) -> removePlayerStateListener
    334:345:void addPlayerStateListener(com.kaolafm.sdk.core.mediaplayer.IPlayerStateListener) -> addPlayerStateListener
    349:349:com.kaolafm.sdk.core.mediaplayer.IPlayerStateListener getPlayerStateListener() -> getPlayerStateListener
    459:468:void manageCallHandUp() -> manageCallHandUp
    474:483:void manageCalling() -> manageCalling
    491:502:boolean canReStartPlayer() -> canReStartPlayer
    513:520:void addGetContentListener(com.kaolafm.sdk.vehicle.GeneralCallback) -> addGetContentListener
    528:534:void removeGetContentListener(com.kaolafm.sdk.vehicle.GeneralCallback) -> removeGetContentListener
    540:556:void addAllPlayerListener() -> addAllPlayerListener
    564:575:void addIPlayerStateListener(com.kaolafm.sdk.core.mediaplayer.IPlayerStateListener) -> addIPlayerStateListener
    583:590:void removeIPlayerStateListener(com.kaolafm.sdk.core.mediaplayer.IPlayerStateListener) -> removeIPlayerStateListener
    593:604:void notifyGetContent(boolean) -> notifyGetContent
    629:632:void initCacheVar() -> initCacheVar
    30:30:void <init>(com.kaolafm.opensdk.player.LivePlayerManager$1) -> <init>
    30:30:boolean access$200(com.kaolafm.opensdk.player.LivePlayerManager) -> access$200
    30:30:int access$300(com.kaolafm.opensdk.player.LivePlayerManager) -> access$300
    30:30:long access$400(com.kaolafm.opensdk.player.LivePlayerManager) -> access$400
    30:30:long access$402(com.kaolafm.opensdk.player.LivePlayerManager,long) -> access$402
    30:30:int access$302(com.kaolafm.opensdk.player.LivePlayerManager,int) -> access$302
    30:30:boolean access$502(com.kaolafm.opensdk.player.LivePlayerManager,boolean) -> access$502
    30:30:long access$602(com.kaolafm.opensdk.player.LivePlayerManager,long) -> access$602
    30:30:boolean access$500(com.kaolafm.opensdk.player.LivePlayerManager) -> access$500
    30:30:long access$600(com.kaolafm.opensdk.player.LivePlayerManager) -> access$600
com.kaolafm.opensdk.player.LivePlayerManager$1 -> com.kaolafm.opensdk.player.LivePlayerManager$1:
    com.kaolafm.opensdk.player.LivePlayerManager this$0 -> this$0
    359:359:void <init>(com.kaolafm.opensdk.player.LivePlayerManager) -> <init>
    362:371:void onProgress(java.lang.String,int,int,boolean) -> onProgress
    375:378:void onIdle(com.kaolafm.sdk.core.mediaplayer.PlayItem) -> onIdle
    382:385:void onPlayerPreparing(com.kaolafm.sdk.core.mediaplayer.PlayItem) -> onPlayerPreparing
    389:389:void onPlayerPlaying(com.kaolafm.sdk.core.mediaplayer.PlayItem) -> onPlayerPlaying
    393:396:void onPlayerPaused(com.kaolafm.sdk.core.mediaplayer.PlayItem) -> onPlayerPaused
    400:411:void onPlayerFailed(com.kaolafm.sdk.core.mediaplayer.PlayItem,int,int) -> onPlayerFailed
    415:421:void onPlayerEnd(com.kaolafm.sdk.core.mediaplayer.PlayItem) -> onPlayerEnd
    425:429:void onSeekStart(java.lang.String) -> onSeekStart
    433:433:void onSeekComplete(java.lang.String) -> onSeekComplete
    437:442:void onBufferingStart(com.kaolafm.sdk.core.mediaplayer.PlayItem) -> onBufferingStart
    446:451:void onBufferingEnd(com.kaolafm.sdk.core.mediaplayer.PlayItem) -> onBufferingEnd
com.kaolafm.opensdk.player.LivePlayerManager$PLAYER_LIVE_BROADCAST_MANAGER_INSTANCE -> com.kaolafm.opensdk.player.LivePlayerManager$PLAYER_LIVE_BROADCAST_MANAGER_INSTANCE:
    com.kaolafm.opensdk.player.LivePlayerManager PLAYER_LIVE_BROADCAST_MANAGER -> PLAYER_LIVE_BROADCAST_MANAGER
    66:66:void <init>() -> <init>
    66:66:com.kaolafm.opensdk.player.LivePlayerManager access$100() -> access$100
    67:67:void <clinit>() -> <clinit>
com.kaolafm.opensdk.player.MusicListManager -> com.kaolafm.opensdk.player.MusicListManager:
    java.lang.String TAG -> TAG
    int INVALID_NUM -> INVALID_NUM
    java.util.ArrayList mPlayList -> mPlayList
    java.util.List mSongList -> mSongList
    int mCurPosition -> mCurPosition
    java.util.ArrayList mPlayedList -> mPlayedList
    java.util.ArrayList mUnPlayedList -> mUnPlayedList
    int mCurrentPlayMode -> mCurrentPlayMode
    java.util.ArrayList mIPlayerListChangedListener -> mIPlayerListChangedListener
    int LEFT_DIRECTION -> LEFT_DIRECTION
    int RIGHT_DIRECTION -> RIGHT_DIRECTION
    int NO_DIRECTION -> NO_DIRECTION
    int mPlayDirection -> mPlayDirection
    42:298:void <init>() -> <init>
    73:73:com.kaolafm.opensdk.player.MusicListManager getInstance() -> getInstance
    80:82:void addPlayList(java.util.ArrayList) -> addPlayList
    90:95:void addPlayList(int,java.util.List) -> addPlayList
    103:106:void addSongList(java.util.List) -> addSongList
    109:115:void addSongList(int,java.util.List) -> addSongList
    122:123:void setPlayList(java.util.ArrayList) -> setPlayList
    126:129:void setSongList(java.util.List) -> setSongList
    132:137:void clearPlayList() -> clearPlayList
    147:180:int delPlayItem(com.kaolafm.sdk.core.mediaplayer.PlayItem,boolean) -> delPlayItem
    185:185:void deleteHistory(long) -> deleteHistory
    193:196:com.kaolafm.sdk.core.mediaplayer.PlayItem getCurPlayItem() -> getCurPlayItem
    200:203:com.kaolafm.opensdk.api.music.qq.model.Song getCurrentSong() -> getCurrentSong
    212:212:int getCurPosition() -> getCurPosition
    216:225:com.kaolafm.sdk.core.mediaplayer.PlayItem getPlayItemById(long) -> getPlayItemById
    235:240:void setCurPosition(int) -> setCurPosition
    248:257:boolean hasPre() -> hasPre
    266:279:boolean hasNext() -> hasNext
    288:288:java.util.ArrayList getPlayList() -> getPlayList
    308:364:com.kaolafm.sdk.core.mediaplayer.PlayItem getNextPlayItem(boolean) -> getNextPlayItem
    373:410:com.kaolafm.sdk.core.mediaplayer.PlayItem getPrePlayItem() -> getPrePlayItem
    419:433:void setCurPlayItemIndex(com.kaolafm.sdk.core.mediaplayer.PlayItem) -> setCurPlayItemIndex
    445:445:int getCurrentPlayMode() -> getCurrentPlayMode
    454:454:boolean isDefaultMode() -> isDefaultMode
    463:470:void setCurrentPlayMode(int) -> setCurrentPlayMode
    476:482:void initRandomPool() -> initRandomPool
    499:505:void initRandomPool(java.util.List) -> initRandomPool
    511:517:void clearRandomPool() -> clearRandomPool
    520:549:com.kaolafm.sdk.core.mediaplayer.PlayItem makeRandomPlayItem() -> makeRandomPlayItem
    553:557:void registerPlayerListChangedListener(com.kaolafm.sdk.core.mediaplayer.IPlayerListChangedListener) -> registerPlayerListChangedListener
    560:563:void unRegisterPlayerListChangedListener(com.kaolafm.sdk.core.mediaplayer.IPlayerListChangedListener) -> unRegisterPlayerListChangedListener
    566:578:void notifyPlayerListChanged() -> notifyPlayerListChanged
    581:594:boolean canCarePlayModel() -> canCarePlayModel
    24:24:void <init>(com.kaolafm.opensdk.player.MusicListManager$1) -> <init>
com.kaolafm.opensdk.player.MusicListManager$1 -> com.kaolafm.opensdk.player.MusicListManager$1:
com.kaolafm.opensdk.player.MusicListManager$MusicListManagerHolder -> com.kaolafm.opensdk.player.MusicListManager$MusicListManagerHolder:
    com.kaolafm.opensdk.player.MusicListManager INSTANCE -> INSTANCE
    68:68:void <init>() -> <init>
    68:68:com.kaolafm.opensdk.player.MusicListManager access$100() -> access$100
    69:69:void <clinit>() -> <clinit>
com.kaolafm.opensdk.player.MusicPlayerManager -> com.kaolafm.opensdk.player.MusicPlayerManager:
    int SEC_DIVISOR -> SEC_DIVISOR
    int PAGE_SIZE -> PAGE_SIZE
    int PAGE_NUM -> PAGE_NUM
    int MAX_FETCH_SONGS_NUM -> MAX_FETCH_SONGS_NUM
    boolean isClearList -> isClearList
    long mLatestSongId -> mLatestSongId
    int mLatestPosition -> mLatestPosition
    com.kaolafm.opensdk.player.MusicListManager mListManager -> mListManager
    com.kaolafm.opensdk.api.music.qq.model.MusicInfo mMusicInfo -> mMusicInfo
    com.kaolafm.opensdk.api.music.qq.QQMusicRequest mMusicRequest -> mMusicRequest
    android.app.Application mApplication -> mApplication
    com.kaolafm.sdk.core.mediaplayer.PlayerService$PlayerBinder mPlayerBinder -> mPlayerBinder
    java.util.List mUnAddedPlayerStateListeners -> mUnAddedPlayerStateListeners
    java.util.ArrayList mGetContentGeneralCallbacks -> mGetContentGeneralCallbacks
    com.kaolafm.sdk.core.mediaplayer.PlayItem mPrePlayItem -> mPrePlayItem
    java.lang.String mPreRadioId -> mPreRadioId
    boolean isMusicPlayerEnable -> isMusicPlayerEnable
    int mCurrentPosition -> mCurrentPosition
    int mHavePlayedTime -> mHavePlayedTime
    boolean isSeekEvent -> isSeekEvent
    long mPrePositionTime -> mPrePositionTime
    long mAudioStartPlayTime -> mAudioStartPlayTime
    boolean isFirstPlay -> isFirstPlay
    boolean mCurPlayStartReported -> mCurPlayStartReported
    com.kaolafm.sdk.core.mediaplayer.IPlayerStateListener mPlayerStateListener -> mPlayerStateListener
    int mCurrentMusicType -> mCurrentMusicType
    65:979:void <init>() -> <init>
    158:158:com.kaolafm.opensdk.player.MusicPlayerManager getInstance() -> getInstance
    162:176:void init(com.kaolafm.sdk.core.mediaplayer.PlayerService$PlayerBinder) -> init
    286:288:void clearPlayerList() -> clearPlayerList
    293:296:boolean isState(int) -> isState
    303:310:void play() -> play
    313:314:void play(java.util.ArrayList) -> play
    317:336:void play(java.util.ArrayList,java.util.List) -> play
    345:347:void play(com.kaolafm.sdk.core.mediaplayer.PlayItem) -> play
    351:356:void play(int) -> play
    360:361:void play(com.kaolafm.opensdk.api.music.qq.model.Song) -> play
    364:365:void play(java.util.List) -> play
    368:387:java.util.ArrayList getPlayItems(java.util.List) -> getPlayItems
    391:406:void play(java.lang.String[]) -> play
    416:444:void play(long,int,java.lang.String) -> play
    450:480:void playVoiceResult(java.lang.String,java.lang.String[]) -> playVoiceResult
    483:484:void playByMids(java.lang.String[]) -> playByMids
    490:542:void processPlaySingleSong(java.util.List) -> processPlaySingleSong
    549:568:void playCharts(long) -> playCharts
    574:575:void playSongMenu(long,java.lang.String) -> playSongMenu
    581:619:void playSongMenu(long,java.lang.String,int) -> playSongMenu
    622:639:void batchSongList(int) -> batchSongList
    645:646:void playMyLike(long,java.lang.String) -> playMyLike
    652:668:void playMyLike(java.lang.String) -> playMyLike
    674:677:void playSceneRadio() -> playSceneRadio
    683:684:void playSceneRadio(long,java.lang.String) -> playSceneRadio
    687:706:void playSceneRadio(long,java.lang.String,boolean) -> playSceneRadio
    712:715:void playLabelRadio() -> playLabelRadio
    721:722:void playLabelRadio(long,java.lang.String) -> playLabelRadio
    730:750:void playLabelRadio(long,java.lang.String,boolean) -> playLabelRadio
    757:771:void playRecommendSongs(java.lang.String) -> playRecommendSongs
    777:778:void playPrivateFM() -> playPrivateFM
    781:802:void playPrivateFM(boolean) -> playPrivateFM
    808:816:void playHistory() -> playHistory
    822:827:void playLatest() -> playLatest
    832:840:void pause() -> pause
    845:849:void stop() -> stop
    853:862:void reset() -> reset
    867:867:void release() -> release
    871:883:void switchPlayerStatus() -> switchPlayerStatus
    889:900:void playStart() -> playStart
    903:904:void start(com.kaolafm.sdk.core.mediaplayer.PlayItem) -> start
    912:918:boolean isPlayThis(long,int) -> isPlayThis
    923:923:boolean isPlaying() -> isPlaying
    928:935:void seek(int) -> seek
    939:940:void playPre() -> playPre
    947:948:void playNext() -> playNext
    954:974:void playNext(boolean) -> playNext
    982:1022:void start(com.kaolafm.sdk.core.mediaplayer.PlayItem,boolean) -> start
    1030:1040:boolean isPaused() -> isPaused
    1049:1053:void destroy() -> destroy
    1057:1064:void enablePlayer() -> enablePlayer
    1070:1085:void disablePlayer() -> disablePlayer
    1091:1102:void addPlayerStateListener(com.kaolafm.sdk.core.mediaplayer.IPlayerStateListener) -> addPlayerStateListener
    1106:1114:void disableOtherPlayer() -> disableOtherPlayer
    1118:1121:void setVolume(float,float) -> setVolume
    1126:1133:boolean hasNext() -> hasNext
    1138:1141:boolean hasPre() -> hasPre
    1151:1162:void addIPlayerStateListener(com.kaolafm.sdk.core.mediaplayer.IPlayerStateListener) -> addIPlayerStateListener
    1166:1177:void removeIPlayerStateListener(com.kaolafm.sdk.core.mediaplayer.IPlayerStateListener) -> removeIPlayerStateListener
    1180:1181:void removePlayerStateListener() -> removePlayerStateListener
    1202:1202:void notifyIPlayChangedListener(com.kaolafm.sdk.core.mediaplayer.PlayItem) -> notifyIPlayChangedListener
    1207:1208:void updatePlayListPosition(int) -> updatePlayListPosition
    1214:1215:boolean haveNextPage() -> haveNextPage
    1222:1226:void playNextPage() -> playNextPage
    1232:1252:void loadNextPage(com.kaolafm.sdk.vehicle.GeneralCallback) -> loadNextPage
    1258:1267:java.lang.String[] getPlaySongMids(int) -> getPlaySongMids
    1274:1277:void initPlayPosition() -> initPlayPosition
    1283:1283:boolean isPlayerEnable() -> isPlayerEnable
    1291:1291:java.util.ArrayList getPlayList() -> getPlayList
    1296:1296:int getCurrentPlayListPosition() -> getCurrentPlayListPosition
    1301:1301:com.kaolafm.sdk.core.mediaplayer.PlayItem getCurrentPlayItem() -> getCurrentPlayItem
    1306:1315:com.kaolafm.sdk.core.mediaplayer.PlayItem getPlayItemByPosition(int) -> getPlayItemByPosition
    1320:1320:java.lang.String getRadioType() -> getRadioType
    1325:1328:java.lang.String getRadioId() -> getRadioId
    1335:1335:com.kaolafm.opensdk.api.music.qq.model.MusicInfo getMusicInfo() -> getMusicInfo
    1342:1350:void saveInfo(long,int,java.lang.String) -> saveInfo
    1356:1363:void addGetContentListener(com.kaolafm.sdk.vehicle.GeneralCallback) -> addGetContentListener
    1369:1375:void removeGetContentListener(com.kaolafm.sdk.vehicle.GeneralCallback) -> removeGetContentListener
    1378:1390:void notifyGetContent(boolean) -> notifyGetContent
    1444:1448:void reportPlayNextSong(com.kaolafm.sdk.core.mediaplayer.PlayItem) -> reportPlayNextSong
    1451:1465:void reportStartListen(com.kaolafm.sdk.core.mediaplayer.PlayItem,int,java.lang.String) -> reportStartListen
    1468:1475:java.lang.String getAudioId(int) -> getAudioId
    46:46:void <init>(com.kaolafm.opensdk.player.MusicPlayerManager$1) -> <init>
    46:46:boolean access$200(com.kaolafm.opensdk.player.MusicPlayerManager) -> access$200
    46:46:int access$302(com.kaolafm.opensdk.player.MusicPlayerManager,int) -> access$302
    46:46:boolean access$400(com.kaolafm.opensdk.player.MusicPlayerManager) -> access$400
    46:46:long access$502(com.kaolafm.opensdk.player.MusicPlayerManager,long) -> access$502
    46:46:boolean access$402(com.kaolafm.opensdk.player.MusicPlayerManager,boolean) -> access$402
    46:46:int access$300(com.kaolafm.opensdk.player.MusicPlayerManager) -> access$300
    46:46:int access$600(com.kaolafm.opensdk.player.MusicPlayerManager) -> access$600
    46:46:int access$602(com.kaolafm.opensdk.player.MusicPlayerManager,int) -> access$602
    46:46:void access$700(com.kaolafm.opensdk.player.MusicPlayerManager,boolean) -> access$700
    46:46:boolean access$802(com.kaolafm.opensdk.player.MusicPlayerManager,boolean) -> access$802
    46:46:long access$902(com.kaolafm.opensdk.player.MusicPlayerManager,long) -> access$902
    46:46:boolean access$800(com.kaolafm.opensdk.player.MusicPlayerManager) -> access$800
    46:46:long access$900(com.kaolafm.opensdk.player.MusicPlayerManager) -> access$900
    46:46:com.kaolafm.opensdk.api.music.qq.model.MusicInfo access$1000(com.kaolafm.opensdk.player.MusicPlayerManager) -> access$1000
    46:46:void access$1100(com.kaolafm.opensdk.player.MusicPlayerManager,java.util.List) -> access$1100
    46:46:void access$1200(com.kaolafm.opensdk.player.MusicPlayerManager,long,int,java.lang.String) -> access$1200
    46:46:boolean access$1302(com.kaolafm.opensdk.player.MusicPlayerManager,boolean) -> access$1302
    46:46:long access$1400(com.kaolafm.opensdk.player.MusicPlayerManager) -> access$1400
    46:46:java.lang.String[] access$1500(com.kaolafm.opensdk.player.MusicPlayerManager,int) -> access$1500
    46:46:void access$1600(com.kaolafm.opensdk.player.MusicPlayerManager,com.kaolafm.sdk.core.mediaplayer.PlayItem) -> access$1600
    46:46:java.util.ArrayList access$1700(com.kaolafm.opensdk.player.MusicPlayerManager,java.util.List) -> access$1700
    46:46:com.kaolafm.opensdk.player.MusicListManager access$1800(com.kaolafm.opensdk.player.MusicPlayerManager) -> access$1800
com.kaolafm.opensdk.player.MusicPlayerManager$1 -> com.kaolafm.opensdk.player.MusicPlayerManager$1:
    com.kaolafm.opensdk.player.MusicPlayerManager this$0 -> this$0
    179:179:void <init>(com.kaolafm.opensdk.player.MusicPlayerManager) -> <init>
    182:185:void onIdle(com.kaolafm.sdk.core.mediaplayer.PlayItem) -> onIdle
    189:192:void onPlayerPreparing(com.kaolafm.sdk.core.mediaplayer.PlayItem) -> onPlayerPreparing
    196:205:void onPlayerPlaying(com.kaolafm.sdk.core.mediaplayer.PlayItem) -> onPlayerPlaying
    209:212:void onPlayerPaused(com.kaolafm.sdk.core.mediaplayer.PlayItem) -> onPlayerPaused
    216:226:void onProgress(java.lang.String,int,int,boolean) -> onProgress
    230:233:void onPlayerFailed(com.kaolafm.sdk.core.mediaplayer.PlayItem,int,int) -> onPlayerFailed
    237:249:void onPlayerEnd(com.kaolafm.sdk.core.mediaplayer.PlayItem) -> onPlayerEnd
    253:257:void onSeekStart(java.lang.String) -> onSeekStart
    262:262:void onSeekComplete(java.lang.String) -> onSeekComplete
    266:272:void onBufferingStart(com.kaolafm.sdk.core.mediaplayer.PlayItem) -> onBufferingStart
    276:281:void onBufferingEnd(com.kaolafm.sdk.core.mediaplayer.PlayItem) -> onBufferingEnd
com.kaolafm.opensdk.player.MusicPlayerManager$10 -> com.kaolafm.opensdk.player.MusicPlayerManager$10:
    java.lang.String val$name -> val$name
    com.kaolafm.opensdk.player.MusicPlayerManager this$0 -> this$0
    758:758:void <init>(com.kaolafm.opensdk.player.MusicPlayerManager,java.lang.String) -> <init>
    761:763:void onSuccess(java.util.List) -> onSuccess
    768:768:void onError(ApiException) -> onError
    758:758:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.player.MusicPlayerManager$11 -> com.kaolafm.opensdk.player.MusicPlayerManager$11:
    boolean val$isFirstPlay -> val$isFirstPlay
    com.kaolafm.opensdk.player.MusicPlayerManager this$0 -> this$0
    784:784:void <init>(com.kaolafm.opensdk.player.MusicPlayerManager,boolean) -> <init>
    787:794:void onSuccess(java.util.List) -> onSuccess
    799:799:void onError(ApiException) -> onError
    784:784:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.player.MusicPlayerManager$12 -> com.kaolafm.opensdk.player.MusicPlayerManager$12:
    com.kaolafm.sdk.vehicle.GeneralCallback val$generalCallback -> val$generalCallback
    com.kaolafm.opensdk.player.MusicPlayerManager this$0 -> this$0
    1234:1234:void <init>(com.kaolafm.opensdk.player.MusicPlayerManager,com.kaolafm.sdk.vehicle.GeneralCallback) -> <init>
    1237:1244:void onSuccess(java.util.List) -> onSuccess
    1248:1249:void onError(ApiException) -> onError
    1234:1234:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.player.MusicPlayerManager$2 -> com.kaolafm.opensdk.player.MusicPlayerManager$2:
    com.kaolafm.opensdk.player.MusicPlayerManager this$0 -> this$0
    395:395:void <init>(com.kaolafm.opensdk.player.MusicPlayerManager) -> <init>
    398:399:void onSuccess(java.util.List) -> onSuccess
    404:404:void onError(ApiException) -> onError
    395:395:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.player.MusicPlayerManager$3 -> com.kaolafm.opensdk.player.MusicPlayerManager$3:
    com.kaolafm.opensdk.player.MusicPlayerManager this$0 -> this$0
    453:453:void <init>(com.kaolafm.opensdk.player.MusicPlayerManager) -> <init>
    456:473:void onSuccess(java.util.List) -> onSuccess
    478:478:void onError(ApiException) -> onError
    453:453:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.player.MusicPlayerManager$4 -> com.kaolafm.opensdk.player.MusicPlayerManager$4:
    com.kaolafm.opensdk.player.MusicPlayerManager this$0 -> this$0
    550:550:void <init>(com.kaolafm.opensdk.player.MusicPlayerManager) -> <init>
    553:560:void onSuccess(com.kaolafm.opensdk.api.music.qq.model.SongChartsResult) -> onSuccess
    565:565:void onError(ApiException) -> onError
    550:550:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.player.MusicPlayerManager$5 -> com.kaolafm.opensdk.player.MusicPlayerManager$5:
    long val$songMenuId -> val$songMenuId
    int val$type -> val$type
    java.lang.String val$source -> val$source
    com.kaolafm.opensdk.player.MusicPlayerManager this$0 -> this$0
    583:583:void <init>(com.kaolafm.opensdk.player.MusicPlayerManager,long,int,java.lang.String) -> <init>
    586:611:void onSuccess(java.util.List) -> onSuccess
    616:616:void onError(ApiException) -> onError
    583:583:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.player.MusicPlayerManager$6 -> com.kaolafm.opensdk.player.MusicPlayerManager$6:
    int val$page -> val$page
    com.kaolafm.opensdk.player.MusicPlayerManager this$0 -> this$0
    627:627:void <init>(com.kaolafm.opensdk.player.MusicPlayerManager,int) -> <init>
    630:631:void onSuccess(java.util.List) -> onSuccess
    636:636:void onError(ApiException) -> onError
    627:627:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.player.MusicPlayerManager$7 -> com.kaolafm.opensdk.player.MusicPlayerManager$7:
    java.lang.String val$source -> val$source
    com.kaolafm.opensdk.player.MusicPlayerManager this$0 -> this$0
    652:652:void <init>(com.kaolafm.opensdk.player.MusicPlayerManager,java.lang.String) -> <init>
    655:661:void onSuccess(java.util.List) -> onSuccess
    666:666:void onError(ApiException) -> onError
    652:652:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.player.MusicPlayerManager$8 -> com.kaolafm.opensdk.player.MusicPlayerManager$8:
    boolean val$isFirstPlay -> val$isFirstPlay
    long val$radioId -> val$radioId
    java.lang.String val$source -> val$source
    com.kaolafm.opensdk.player.MusicPlayerManager this$0 -> this$0
    690:690:void <init>(com.kaolafm.opensdk.player.MusicPlayerManager,boolean,long,java.lang.String) -> <init>
    693:698:void onSuccess(java.util.List) -> onSuccess
    702:703:void onError(ApiException) -> onError
    690:690:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.player.MusicPlayerManager$9 -> com.kaolafm.opensdk.player.MusicPlayerManager$9:
    boolean val$isFirstPlay -> val$isFirstPlay
    long val$radioId -> val$radioId
    java.lang.String val$source -> val$source
    com.kaolafm.opensdk.player.MusicPlayerManager this$0 -> this$0
    733:733:void <init>(com.kaolafm.opensdk.player.MusicPlayerManager,boolean,long,java.lang.String) -> <init>
    737:742:void onSuccess(java.util.List) -> onSuccess
    747:747:void onError(ApiException) -> onError
    733:733:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.opensdk.player.MusicPlayerManager$MusicPlayerManagerHolder -> com.kaolafm.opensdk.player.MusicPlayerManager$MusicPlayerManagerHolder:
    com.kaolafm.opensdk.player.MusicPlayerManager INSTANCE -> INSTANCE
    152:152:void <init>() -> <init>
    152:152:com.kaolafm.opensdk.player.MusicPlayerManager access$100() -> access$100
    154:154:void <clinit>() -> <clinit>
com.kaolafm.opensdk.player.MusicPlayerManager_MembersInjector -> com.kaolafm.opensdk.player.MusicPlayerManager_MembersInjector:
    javax.inject.Provider mMusicRequestProvider -> mMusicRequestProvider
    javax.inject.Provider mApplicationProvider -> mApplicationProvider
    16:19:void <init>(javax.inject.Provider,javax.inject.Provider) -> <init>
    23:23:dagger.MembersInjector create(javax.inject.Provider,javax.inject.Provider) -> create
    28:30:void injectMembers(com.kaolafm.opensdk.player.MusicPlayerManager) -> injectMembers
    34:35:void injectMMusicRequest(com.kaolafm.opensdk.player.MusicPlayerManager,com.kaolafm.opensdk.api.music.qq.QQMusicRequest) -> injectMMusicRequest
    38:39:void injectMApplication(com.kaolafm.opensdk.player.MusicPlayerManager,android.app.Application) -> injectMApplication
    9:9:void injectMembers(java.lang.Object) -> injectMembers
com.kaolafm.opensdk.demo.BeanUtil -> com.kaolafm.opensdk.demo.BeanUtil:
    19:19:void <init>() -> <init>
    28:75:com.kaolafm.sdk.core.mediaplayer.PlayItem translateToPlayItem(com.kaolafm.opensdk.api.media.model.AudioDetails) -> translateToPlayItem
    85:126:com.kaolafm.sdk.core.mediaplayer.PlayItem translateToPlayItem(com.kaolafm.opensdk.api.broadcast.ProgramDetails) -> translateToPlayItem
com.kaolafm.opensdk.utils.operation.AlbumProcessor -> com.kaolafm.opensdk.utils.operation.a:
    22:23:void <init>() -> <init>
    27:27:boolean accept(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> a
    32:32:boolean accept(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> a
    37:37:long getId(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> b
    42:42:long getId(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> b
    47:47:long getListenNum(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> c
    52:52:int getType(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> d
    57:57:int getType(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> c
    62:63:void play(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> e
    67:68:void play(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> d
com.kaolafm.opensdk.utils.operation.AlbumProcessor_Factory -> com.kaolafm.opensdk.utils.operation.b:
    com.kaolafm.opensdk.utils.operation.AlbumProcessor_Factory INSTANCE -> a
    6:6:void <init>() -> <init>
    11:11:com.kaolafm.opensdk.utils.operation.AlbumProcessor get() -> get
    15:15:com.kaolafm.opensdk.utils.operation.AlbumProcessor provideInstance() -> a
    19:19:com.kaolafm.opensdk.utils.operation.AlbumProcessor_Factory create() -> create
    23:23:com.kaolafm.opensdk.utils.operation.AlbumProcessor newAlbumProcessor() -> b
    6:6:java.lang.Object get() -> get
    7:7:void <clinit>() -> <clinit>
com.kaolafm.opensdk.utils.operation.BroadcastProcessor -> com.kaolafm.opensdk.utils.operation.c:
    19:20:void <init>() -> <init>
    24:24:boolean accept(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> a
    29:29:boolean accept(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> a
    34:34:long getId(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> b
    40:40:long getId(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> b
    45:45:long getListenNum(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> c
    50:50:int getType(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> d
    55:55:int getType(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> c
    61:61:void play(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> e
    66:66:void play(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> d
com.kaolafm.opensdk.utils.operation.BroadcastProcessor_Factory -> com.kaolafm.opensdk.utils.operation.d:
    com.kaolafm.opensdk.utils.operation.BroadcastProcessor_Factory INSTANCE -> a
    6:6:void <init>() -> <init>
    11:11:com.kaolafm.opensdk.utils.operation.BroadcastProcessor get() -> get
    15:15:com.kaolafm.opensdk.utils.operation.BroadcastProcessor provideInstance() -> a
    19:19:com.kaolafm.opensdk.utils.operation.BroadcastProcessor_Factory create() -> create
    23:23:com.kaolafm.opensdk.utils.operation.BroadcastProcessor newBroadcastProcessor() -> b
    6:6:java.lang.Object get() -> get
    7:7:void <clinit>() -> <clinit>
com.kaolafm.opensdk.utils.operation.CategoryProcessor -> com.kaolafm.opensdk.utils.operation.e:
    18:19:void <init>() -> <init>
    23:23:boolean accept(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> a
    28:28:boolean accept(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> a
    33:33:long getId(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> b
    38:39:long getId(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> b
    44:44:long getListenNum(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> c
    49:49:int getType(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> d
    54:54:int getType(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> c
    60:60:void play(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> e
    65:65:void play(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> d
com.kaolafm.opensdk.utils.operation.CategoryProcessor_Factory -> com.kaolafm.opensdk.utils.operation.f:
    com.kaolafm.opensdk.utils.operation.CategoryProcessor_Factory INSTANCE -> a
    6:6:void <init>() -> <init>
    11:11:com.kaolafm.opensdk.utils.operation.CategoryProcessor get() -> get
    15:15:com.kaolafm.opensdk.utils.operation.CategoryProcessor provideInstance() -> a
    19:19:com.kaolafm.opensdk.utils.operation.CategoryProcessor_Factory create() -> create
    23:23:com.kaolafm.opensdk.utils.operation.CategoryProcessor newCategoryProcessor() -> b
    6:6:java.lang.Object get() -> get
    7:7:void <clinit>() -> <clinit>
com.kaolafm.opensdk.utils.operation.IOperationProcessor -> com.kaolafm.opensdk.utils.operation.g:
    boolean accept(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> a
    boolean accept(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> a
    long getId(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> b
    long getId(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> b
    long getListenNum(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> c
    int getType(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> d
    int getType(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> c
    void play(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> e
    void play(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> d
com.kaolafm.opensdk.utils.operation.LiveProcessor -> com.kaolafm.opensdk.utils.operation.h:
    20:21:void <init>() -> <init>
    25:25:boolean accept(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> a
    30:30:boolean accept(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> a
    35:35:long getId(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> b
    40:40:long getId(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> b
    45:45:long getListenNum(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> c
    50:50:int getType(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> d
    55:55:int getType(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> c
    61:61:void play(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> e
    66:66:void play(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> d
com.kaolafm.opensdk.utils.operation.LiveProcessor_Factory -> com.kaolafm.opensdk.utils.operation.i:
    com.kaolafm.opensdk.utils.operation.LiveProcessor_Factory INSTANCE -> a
    6:6:void <init>() -> <init>
    11:11:com.kaolafm.opensdk.utils.operation.LiveProcessor get() -> get
    15:15:com.kaolafm.opensdk.utils.operation.LiveProcessor provideInstance() -> a
    19:19:com.kaolafm.opensdk.utils.operation.LiveProcessor_Factory create() -> create
    23:23:com.kaolafm.opensdk.utils.operation.LiveProcessor newLiveProcessor() -> b
    6:6:java.lang.Object get() -> get
    7:7:void <clinit>() -> <clinit>
com.kaolafm.opensdk.utils.operation.OperationAssister -> com.kaolafm.opensdk.utils.operation.OperationAssister:
    java.util.List processorList -> processorList
    31:32:void <init>() -> <init>
    41:46:long getId(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> getId
    56:61:long getId(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> getId
    71:76:long getListenNum(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> getListenNum
    85:90:int getType(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> getType
    99:104:int getType(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> getType
    116:119:java.lang.String getIcon(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> getIcon
    129:132:java.lang.String getIcon(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> getIcon
    142:145:java.lang.String getCover(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> getCover
    155:158:java.lang.String getCover(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> getCover
    168:174:java.lang.String getImage(java.lang.String,java.util.Map) -> getImage
    183:187:java.lang.String getImage(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> getImage
    196:200:java.lang.String getImage(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> getImage
    20:29:void <clinit>() -> <clinit>
com.kaolafm.opensdk.utils.operation.QQMusicProcessor -> com.kaolafm.opensdk.utils.operation.j:
    20:21:void <init>() -> <init>
    25:25:boolean accept(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> a
    30:30:boolean accept(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> a
    35:35:long getId(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> b
    40:40:long getId(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> b
    45:45:long getListenNum(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> c
    50:59:int getType(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> d
    64:73:int getType(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> c
    79:79:void play(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> e
    84:84:void play(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> d
com.kaolafm.opensdk.utils.operation.QQMusicProcessor_Factory -> com.kaolafm.opensdk.utils.operation.k:
    com.kaolafm.opensdk.utils.operation.QQMusicProcessor_Factory INSTANCE -> a
    6:6:void <init>() -> <init>
    11:11:com.kaolafm.opensdk.utils.operation.QQMusicProcessor get() -> get
    15:15:com.kaolafm.opensdk.utils.operation.QQMusicProcessor provideInstance() -> a
    19:19:com.kaolafm.opensdk.utils.operation.QQMusicProcessor_Factory create() -> create
    23:23:com.kaolafm.opensdk.utils.operation.QQMusicProcessor newQQMusicProcessor() -> b
    6:6:java.lang.Object get() -> get
    7:7:void <clinit>() -> <clinit>
com.kaolafm.opensdk.utils.operation.RadioProcessor -> com.kaolafm.opensdk.utils.operation.l:
    19:20:void <init>() -> <init>
    24:24:boolean accept(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> a
    29:29:boolean accept(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> a
    34:34:long getId(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> b
    39:39:long getId(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> b
    44:44:long getListenNum(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> c
    49:49:int getType(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> d
    54:54:int getType(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> c
    60:60:void play(com.kaolafm.opensdk.api.operation.model.category.CategoryMember) -> e
    65:65:void play(com.kaolafm.opensdk.api.operation.model.column.ColumnMember) -> d
com.kaolafm.opensdk.utils.operation.RadioProcessor_Factory -> com.kaolafm.opensdk.utils.operation.m:
    com.kaolafm.opensdk.utils.operation.RadioProcessor_Factory INSTANCE -> a
    6:6:void <init>() -> <init>
    11:11:com.kaolafm.opensdk.utils.operation.RadioProcessor get() -> get
    15:15:com.kaolafm.opensdk.utils.operation.RadioProcessor provideInstance() -> a
    19:19:com.kaolafm.opensdk.utils.operation.RadioProcessor_Factory create() -> create
    23:23:com.kaolafm.opensdk.utils.operation.RadioProcessor newRadioProcessor() -> b
    6:6:java.lang.Object get() -> get
    7:7:void <clinit>() -> <clinit>
com.kaolafm.report.ReportHelper -> com.kaolafm.report.ReportHelper:
    boolean isUseBySDK -> isUseBySDK
    boolean isInitSuccess -> isInitSuccess
    java.lang.String mCarType -> mCarType
    com.kaolafm.report.ReportHelper reportHelper -> reportHelper
    android.content.Context mContext -> mContext
    com.kaolafm.report.util.PlayReportManager playReportManager -> playReportManager
    34:48:void <init>() -> <init>
    51:58:com.kaolafm.report.ReportHelper getInstance() -> getInstance
    65:66:void initBySdk() -> initBySdk
    69:78:void init(android.content.Context,com.kaolafm.report.model.ReportParameter) -> init
    90:94:void initUid(java.lang.String) -> initUid
    98:102:void setCarParameter(com.kaolafm.report.model.ReportCarParameter) -> setCarParameter
    105:127:void setCarParameter(com.kaolafm.opensdk.api.init.model.KaolaActivateData) -> setCarParameter
    135:139:void setPlayPosition(long,long) -> setPlayPosition
    142:146:void setSearchAudioPlayCallBack(java.lang.String,java.lang.String) -> setSearchAudioPlayCallBack
    150:154:void setLat(java.lang.String) -> setLat
    157:161:void setLon(java.lang.String) -> setLon
    164:168:void setPage(java.lang.String) -> setPage
    171:171:android.content.Context getContext() -> getContext
    180:181:void addEvent(com.kaolafm.report.event.BaseReportEventBean) -> addEvent
    190:219:void addEvent(com.kaolafm.report.event.BaseReportEventBean,boolean) -> addEvent
    227:231:void addStartListenReport(com.kaolafm.report.model.PlayReportParameter) -> addStartListenReport
    240:244:void addEndListenReport(java.lang.String,boolean) -> addEndListenReport
    248:252:void addAppStart(java.lang.String) -> addAppStart
    255:261:void setCarType(java.lang.String) -> setCarType
    264:273:void release() -> release
com.kaolafm.report.api.carinfo.CarInfoRequest -> com.kaolafm.report.api.carinfo.CarInfoRequest:
    com.kaolafm.report.api.carinfo.CarInfoService reportInfoService -> reportInfoService
    15:17:void <init>() -> <init>
    21:24:void getReportInfo(HttpCallback) -> getReportInfo
com.kaolafm.report.api.carinfo.CarInfoService -> com.kaolafm.report.api.carinfo.CarInfoService:
    io.reactivex.Single getReportInfo() -> getReportInfo
com.kaolafm.report.api.carinfo.model.CarInfoData -> com.kaolafm.report.api.carinfo.model.CarInfoData:
    com.kaolafm.report.api.carinfo.model.CarInfoData$CarConfigBean carConfig -> carConfig
    com.kaolafm.report.api.carinfo.model.CarInfoData$CarInfoBean carInfo -> carInfo
    8:8:void <init>() -> <init>
    19:19:com.kaolafm.report.api.carinfo.model.CarInfoData$CarConfigBean getCarConfig() -> getCarConfig
    23:24:void setCarConfig(com.kaolafm.report.api.carinfo.model.CarInfoData$CarConfigBean) -> setCarConfig
    27:27:com.kaolafm.report.api.carinfo.model.CarInfoData$CarInfoBean getCarInfo() -> getCarInfo
    31:32:void setCarInfo(com.kaolafm.report.api.carinfo.model.CarInfoData$CarInfoBean) -> setCarInfo
com.kaolafm.report.api.carinfo.model.CarInfoData$CarConfigBean -> com.kaolafm.report.api.carinfo.model.CarInfoData$CarConfigBean:
    int reportInterval -> reportInterval
    34:34:void <init>() -> <init>
    42:42:int getReportInterval() -> getReportInterval
    46:47:void setReportInterval(int) -> setReportInterval
com.kaolafm.report.api.carinfo.model.CarInfoData$CarInfoBean -> com.kaolafm.report.api.carinfo.model.CarInfoData$CarInfoBean:
    java.lang.String firstAppId -> firstAppId
    java.lang.String marketType -> marketType
    java.lang.String firstAppIdName -> firstAppIdName
    java.lang.String appIdType -> appIdType
    java.lang.String oem -> oem
    java.lang.String carBrand -> carBrand
    java.lang.String carType -> carType
    java.lang.String developer -> developer
    50:50:void <init>() -> <init>
    92:92:java.lang.String getFirstAppId() -> getFirstAppId
    96:97:void setFirstAppId(java.lang.String) -> setFirstAppId
    100:100:java.lang.String getMarketType() -> getMarketType
    104:105:void setMarketType(java.lang.String) -> setMarketType
    108:108:java.lang.String getFirstAppIdName() -> getFirstAppIdName
    112:113:void setFirstAppIdName(java.lang.String) -> setFirstAppIdName
    116:116:java.lang.String getAppIdType() -> getAppIdType
    120:121:void setAppIdType(java.lang.String) -> setAppIdType
    124:124:java.lang.String getOem() -> getOem
    128:129:void setOem(java.lang.String) -> setOem
    132:132:java.lang.String getCarBrand() -> getCarBrand
    136:137:void setCarBrand(java.lang.String) -> setCarBrand
    140:140:java.lang.String getCarType() -> getCarType
    144:145:void setCarType(java.lang.String) -> setCarType
    148:148:java.lang.String getDeveloper() -> getDeveloper
    152:153:void setDeveloper(java.lang.String) -> setDeveloper
com.kaolafm.report.api.report.ReportShenCeService -> com.kaolafm.report.api.report.ReportShenCeService:
    retrofit2.Call report(okhttp3.RequestBody) -> report
com.kaolafm.report.api.report.ReportShenCeRequest -> com.kaolafm.report.api.report.ReportShenCeRequest:
    com.kaolafm.report.api.report.ReportShenCeService reportApiService -> reportApiService
    17:20:void <init>() -> <init>
    24:35:boolean postReport(java.lang.String) -> postReport
com.kaolafm.report.database.ConfigData -> com.kaolafm.report.database.ConfigData:
    java.lang.Long id -> id
    int type -> type
    java.lang.String json -> json
    21:25:void <init>(java.lang.Long,int,java.lang.String) -> <init>
    28:29:void <init>() -> <init>
    32:32:java.lang.Long getId() -> getId
    36:37:void setId(java.lang.Long) -> setId
    40:40:int getType() -> getType
    44:45:void setType(int) -> setType
    48:48:java.lang.String getJson() -> getJson
    52:53:void setJson(java.lang.String) -> setJson
com.kaolafm.report.database.ReportData -> com.kaolafm.report.database.ReportData:
    java.lang.Long id -> id
    int type -> type
    java.lang.String sendStr -> sendStr
    19:23:void <init>(java.lang.Long,int,java.lang.String) -> <init>
    25:26:void <init>() -> <init>
    28:28:java.lang.Long getId() -> getId
    31:32:void setId(java.lang.Long) -> setId
    34:34:int getType() -> getType
    37:38:void setType(int) -> setType
    40:40:java.lang.String getSendStr() -> getSendStr
    43:44:void setSendStr(java.lang.String) -> setSendStr
com.kaolafm.report.database.greendao.ConfigDataDao -> com.kaolafm.report.database.greendao.ConfigDataDao:
    java.lang.String TABLENAME -> TABLENAME
    34:35:void <init>(org.greenrobot.greendao.internal.DaoConfig) -> <init>
    38:39:void <init>(org.greenrobot.greendao.internal.DaoConfig,com.kaolafm.report.database.greendao.DaoSession) -> <init>
    43:48:void createTable(org.greenrobot.greendao.database.Database,boolean) -> createTable
    52:54:void dropTable(org.greenrobot.greendao.database.Database,boolean) -> dropTable
    58:70:void bindValues(org.greenrobot.greendao.database.DatabaseStatement,com.kaolafm.report.database.ConfigData) -> bindValues
    74:86:void bindValues(android.database.sqlite.SQLiteStatement,com.kaolafm.report.database.ConfigData) -> bindValues
    90:90:java.lang.Long readKey(android.database.Cursor,int) -> readKey
    95:100:com.kaolafm.report.database.ConfigData readEntity(android.database.Cursor,int) -> readEntity
    105:108:void readEntity(android.database.Cursor,com.kaolafm.report.database.ConfigData,int) -> readEntity
    112:113:java.lang.Long updateKeyAfterInsert(com.kaolafm.report.database.ConfigData,long) -> updateKeyAfterInsert
    118:121:java.lang.Long getKey(com.kaolafm.report.database.ConfigData) -> getKey
    127:127:boolean hasKey(com.kaolafm.report.database.ConfigData) -> hasKey
    132:132:boolean isEntityUpdateable() -> isEntityUpdateable
    18:18:boolean hasKey(java.lang.Object) -> hasKey
    18:18:java.lang.Object getKey(java.lang.Object) -> getKey
    18:18:java.lang.Object updateKeyAfterInsert(java.lang.Object,long) -> updateKeyAfterInsert
    18:18:void bindValues(android.database.sqlite.SQLiteStatement,java.lang.Object) -> bindValues
    18:18:void bindValues(org.greenrobot.greendao.database.DatabaseStatement,java.lang.Object) -> bindValues
    18:18:void readEntity(android.database.Cursor,java.lang.Object,int) -> readEntity
    18:18:java.lang.Object readKey(android.database.Cursor,int) -> readKey
    18:18:java.lang.Object readEntity(android.database.Cursor,int) -> readEntity
com.kaolafm.report.database.greendao.ConfigDataDao$Properties -> com.kaolafm.report.database.greendao.ConfigDataDao$Properties:
    org.greenrobot.greendao.Property Id -> Id
    org.greenrobot.greendao.Property Type -> Type
    org.greenrobot.greendao.Property Json -> Json
    26:26:void <init>() -> <init>
    27:29:void <clinit>() -> <clinit>
com.kaolafm.report.database.greendao.DaoMaster -> com.kaolafm.report.database.greendao.DaoMaster:
    int SCHEMA_VERSION -> SCHEMA_VERSION
    24:26:void createAllTables(org.greenrobot.greendao.database.Database,boolean) -> createAllTables
    30:32:void dropAllTables(org.greenrobot.greendao.database.Database,boolean) -> dropAllTables
    39:41:com.kaolafm.report.database.greendao.DaoSession newDevSession(android.content.Context,java.lang.String) -> newDevSession
    45:46:void <init>(android.database.sqlite.SQLiteDatabase) -> <init>
    49:52:void <init>(org.greenrobot.greendao.database.Database) -> <init>
    55:55:com.kaolafm.report.database.greendao.DaoSession newSession() -> newSession
    59:59:com.kaolafm.report.database.greendao.DaoSession newSession(org.greenrobot.greendao.identityscope.IdentityScopeType) -> newSession
    19:19:org.greenrobot.greendao.AbstractDaoSession newSession(org.greenrobot.greendao.identityscope.IdentityScopeType) -> newSession
    19:19:org.greenrobot.greendao.AbstractDaoSession newSession() -> newSession
com.kaolafm.report.database.greendao.DaoMaster$DevOpenHelper -> com.kaolafm.report.database.greendao.DaoMaster$DevOpenHelper:
    84:85:void <init>(android.content.Context,java.lang.String) -> <init>
    88:89:void <init>(android.content.Context,java.lang.String,android.database.sqlite.SQLiteDatabase$CursorFactory) -> <init>
    93:96:void onUpgrade(org.greenrobot.greendao.database.Database,int,int) -> onUpgrade
com.kaolafm.report.database.greendao.DaoMaster$OpenHelper -> com.kaolafm.report.database.greendao.DaoMaster$OpenHelper:
    67:68:void <init>(android.content.Context,java.lang.String) -> <init>
    71:72:void <init>(android.content.Context,java.lang.String,android.database.sqlite.SQLiteDatabase$CursorFactory) -> <init>
    76:78:void onCreate(org.greenrobot.greendao.database.Database) -> onCreate
com.kaolafm.report.database.greendao.DaoSession -> com.kaolafm.report.database.greendao.DaoSession:
    org.greenrobot.greendao.internal.DaoConfig configDataDaoConfig -> configDataDaoConfig
    org.greenrobot.greendao.internal.DaoConfig reportDataDaoConfig -> reportDataDaoConfig
    com.kaolafm.report.database.greendao.ConfigDataDao configDataDao -> configDataDao
    com.kaolafm.report.database.greendao.ReportDataDao reportDataDao -> reportDataDao
    34:47:void <init>(org.greenrobot.greendao.database.Database,org.greenrobot.greendao.identityscope.IdentityScopeType,java.util.Map) -> <init>
    50:52:void clear() -> clear
    55:55:com.kaolafm.report.database.greendao.ConfigDataDao getConfigDataDao() -> getConfigDataDao
    59:59:com.kaolafm.report.database.greendao.ReportDataDao getReportDataDao() -> getReportDataDao
com.kaolafm.report.database.greendao.ReportDataDao -> com.kaolafm.report.database.greendao.ReportDataDao:
    java.lang.String TABLENAME -> TABLENAME
    34:35:void <init>(org.greenrobot.greendao.internal.DaoConfig) -> <init>
    38:39:void <init>(org.greenrobot.greendao.internal.DaoConfig,com.kaolafm.report.database.greendao.DaoSession) -> <init>
    43:48:void createTable(org.greenrobot.greendao.database.Database,boolean) -> createTable
    52:54:void dropTable(org.greenrobot.greendao.database.Database,boolean) -> dropTable
    58:70:void bindValues(org.greenrobot.greendao.database.DatabaseStatement,com.kaolafm.report.database.ReportData) -> bindValues
    74:86:void bindValues(android.database.sqlite.SQLiteStatement,com.kaolafm.report.database.ReportData) -> bindValues
    90:90:java.lang.Long readKey(android.database.Cursor,int) -> readKey
    95:100:com.kaolafm.report.database.ReportData readEntity(android.database.Cursor,int) -> readEntity
    105:108:void readEntity(android.database.Cursor,com.kaolafm.report.database.ReportData,int) -> readEntity
    112:113:java.lang.Long updateKeyAfterInsert(com.kaolafm.report.database.ReportData,long) -> updateKeyAfterInsert
    118:121:java.lang.Long getKey(com.kaolafm.report.database.ReportData) -> getKey
    127:127:boolean hasKey(com.kaolafm.report.database.ReportData) -> hasKey
    132:132:boolean isEntityUpdateable() -> isEntityUpdateable
    18:18:boolean hasKey(java.lang.Object) -> hasKey
    18:18:java.lang.Object getKey(java.lang.Object) -> getKey
    18:18:java.lang.Object updateKeyAfterInsert(java.lang.Object,long) -> updateKeyAfterInsert
    18:18:void bindValues(android.database.sqlite.SQLiteStatement,java.lang.Object) -> bindValues
    18:18:void bindValues(org.greenrobot.greendao.database.DatabaseStatement,java.lang.Object) -> bindValues
    18:18:void readEntity(android.database.Cursor,java.lang.Object,int) -> readEntity
    18:18:java.lang.Object readKey(android.database.Cursor,int) -> readKey
    18:18:java.lang.Object readEntity(android.database.Cursor,int) -> readEntity
com.kaolafm.report.database.greendao.ReportDataDao$Properties -> com.kaolafm.report.database.greendao.ReportDataDao$Properties:
    org.greenrobot.greendao.Property Id -> Id
    org.greenrobot.greendao.Property Type -> Type
    org.greenrobot.greendao.Property SendStr -> SendStr
    26:26:void <init>() -> <init>
    27:29:void <clinit>() -> <clinit>
com.kaolafm.report.event.AudioSearchReportEvent -> com.kaolafm.report.event.AudioSearchReportEvent:
    java.lang.String RESULT_TIME_OUT -> RESULT_TIME_OUT
    java.lang.String RESULT_ERROR -> RESULT_ERROR
    java.lang.String RESULT_EMPTY -> RESULT_EMPTY
    java.lang.String RESULT_SUCCESS -> RESULT_SUCCESS
    java.lang.String text -> text
    java.lang.String result -> result
    java.lang.String playtype -> playtype
    java.lang.String remarks1 -> remarks1
    java.lang.String remarks9 -> remarks9
    39:41:void <init>() -> <init>
    44:44:java.lang.String getText() -> getText
    48:49:void setText(java.lang.String) -> setText
    52:52:java.lang.String getResult() -> getResult
    56:57:void setResult(java.lang.String) -> setResult
    60:60:java.lang.String getPlaytype() -> getPlaytype
    64:65:void setPlaytype(java.lang.String) -> setPlaytype
    68:68:java.lang.String getRemarks1() -> getRemarks1
    72:73:void setRemarks1(java.lang.String) -> setRemarks1
    76:76:java.lang.String getRemarks9() -> getRemarks9
    80:81:void setRemarks9(java.lang.String) -> setRemarks9
com.kaolafm.report.event.BaseReportEventBean -> com.kaolafm.report.event.BaseReportEventBean:
    java.lang.String appid -> appid
    java.lang.String udid -> udid
    java.lang.String imsi -> imsi
    java.lang.String uid -> uid
    java.lang.String eventcode -> eventcode
    java.lang.String timestamp -> timestamp
    java.lang.String lon -> lon
    java.lang.String lat -> lat
    java.lang.String openid -> openid
    java.lang.String page -> page
    java.lang.String os -> os
    java.lang.String screen_height -> screen_height
    java.lang.String screen_width -> screen_width
    java.lang.String osversion -> osversion
    java.lang.String app_version -> app_version
    java.lang.String app_version2 -> app_version2
    java.lang.String lib_version -> lib_version
    java.lang.String sessionid -> sessionid
    java.lang.String action_id -> action_id
    java.lang.String wifi -> wifi
    java.lang.String network -> network
    java.lang.String carrier -> carrier
    java.lang.String car_id -> car_id
    java.lang.String market_type -> market_type
    java.lang.String appid_type -> appid_type
    java.lang.String oem -> oem
    java.lang.String car_brand -> car_brand
    java.lang.String car_type -> car_type
    java.lang.String screen_direction -> screen_direction
    java.lang.String manufacturer -> manufacturer
    java.lang.String model -> model
    java.lang.String playid -> playid
    java.lang.String report_timely -> report_timely
    java.lang.String channel -> channel
    java.lang.String developer -> developer
    100:151:void <init>() -> <init>
    154:154:java.lang.String getAppid() -> getAppid
    158:159:void setAppid(java.lang.String) -> setAppid
    162:162:java.lang.String getImsi() -> getImsi
    166:167:void setImsi(java.lang.String) -> setImsi
    170:170:java.lang.String getUid() -> getUid
    174:175:void setUid(java.lang.String) -> setUid
    178:178:java.lang.String getEventcode() -> getEventcode
    182:183:void setEventcode(java.lang.String) -> setEventcode
    186:186:java.lang.String getTimestamp() -> getTimestamp
    190:191:void setTimestamp(java.lang.String) -> setTimestamp
    194:194:java.lang.String getLon() -> getLon
    198:199:void setLon(java.lang.String) -> setLon
    202:202:java.lang.String getLat() -> getLat
    206:207:void setLat(java.lang.String) -> setLat
    210:210:java.lang.String getOpenid() -> getOpenid
    214:215:void setOpenid(java.lang.String) -> setOpenid
    218:218:java.lang.String getPage() -> getPage
    222:223:void setPage(java.lang.String) -> setPage
    226:226:java.lang.String getOs() -> getOs
    230:231:void setOs(java.lang.String) -> setOs
    234:234:java.lang.String getScreen_height() -> getScreen_height
    238:239:void setScreen_height(java.lang.String) -> setScreen_height
    242:242:java.lang.String getScreen_width() -> getScreen_width
    246:247:void setScreen_width(java.lang.String) -> setScreen_width
    251:251:java.lang.String getApp_version() -> getApp_version
    255:256:void setApp_version(java.lang.String) -> setApp_version
    259:259:java.lang.String getApp_version2() -> getApp_version2
    263:264:void setApp_version2(java.lang.String) -> setApp_version2
    267:267:java.lang.String getLib_version() -> getLib_version
    271:272:void setLib_version(java.lang.String) -> setLib_version
    275:275:java.lang.String getSessionid() -> getSessionid
    279:280:void setSessionid(java.lang.String) -> setSessionid
    283:283:java.lang.String getAction_id() -> getAction_id
    287:288:void setAction_id(java.lang.String) -> setAction_id
    291:291:java.lang.String getWifi() -> getWifi
    295:296:void setWifi(java.lang.String) -> setWifi
    299:299:java.lang.String getNetwork() -> getNetwork
    303:304:void setNetwork(java.lang.String) -> setNetwork
    307:307:java.lang.String getCarrier() -> getCarrier
    311:312:void setCarrier(java.lang.String) -> setCarrier
    315:315:java.lang.String getCar_id() -> getCar_id
    319:320:void setCar_id(java.lang.String) -> setCar_id
    323:323:java.lang.String getMarket_type() -> getMarket_type
    327:328:void setMarket_type(java.lang.String) -> setMarket_type
    331:331:java.lang.String getAppid_type() -> getAppid_type
    335:336:void setAppid_type(java.lang.String) -> setAppid_type
    339:339:java.lang.String getOem() -> getOem
    343:344:void setOem(java.lang.String) -> setOem
    347:347:java.lang.String getCar_brand() -> getCar_brand
    351:352:void setCar_brand(java.lang.String) -> setCar_brand
    355:355:java.lang.String getCar_type() -> getCar_type
    359:360:void setCar_type(java.lang.String) -> setCar_type
    363:363:java.lang.String getScreen_direction() -> getScreen_direction
    367:368:void setScreen_direction(java.lang.String) -> setScreen_direction
    371:371:java.lang.String getManufacturer() -> getManufacturer
    375:376:void setManufacturer(java.lang.String) -> setManufacturer
    379:379:java.lang.String getModel() -> getModel
    383:384:void setModel(java.lang.String) -> setModel
    387:387:java.lang.String getPlayid() -> getPlayid
    391:392:void setPlayid(java.lang.String) -> setPlayid
    395:395:java.lang.String getReport_timely() -> getReport_timely
    399:400:void setReport_timely(java.lang.String) -> setReport_timely
    403:403:java.lang.String getUdid() -> getUdid
    407:408:void setUdid(java.lang.String) -> setUdid
    411:411:java.lang.String getOsversion() -> getOsversion
    415:416:void setOsversion(java.lang.String) -> setOsversion
    419:419:java.lang.String getChannel() -> getChannel
    423:424:void setChannel(java.lang.String) -> setChannel
    427:427:java.lang.String getDeveloper() -> getDeveloper
    431:432:void setDeveloper(java.lang.String) -> setDeveloper
com.kaolafm.report.event.BufferEndReportEvent -> com.kaolafm.report.event.BufferEndReportEvent:
    java.lang.String TYPE_NORMAL -> TYPE_NORMAL
    java.lang.String TYPE_PUSH -> TYPE_PUSH
    java.lang.String audioid -> audioid
    java.lang.String radioid -> radioid
    java.lang.String albumid -> albumid
    java.lang.String type -> type
    java.lang.String playtime -> playtime
    java.lang.String remarks1 -> remarks1
    java.lang.String remarks2 -> remarks2
    24:41:void <init>() -> <init>
    44:44:java.lang.String getAudioid() -> getAudioid
    48:49:void setAudioid(java.lang.String) -> setAudioid
    52:52:java.lang.String getRadioid() -> getRadioid
    56:57:void setRadioid(java.lang.String) -> setRadioid
    60:60:java.lang.String getAlbumid() -> getAlbumid
    64:65:void setAlbumid(java.lang.String) -> setAlbumid
    68:68:java.lang.String getType() -> getType
    72:73:void setType(java.lang.String) -> setType
    76:76:java.lang.String getRemarks1() -> getRemarks1
    80:81:void setRemarks1(java.lang.String) -> setRemarks1
    84:84:java.lang.String getRemarks2() -> getRemarks2
    88:89:void setRemarks2(java.lang.String) -> setRemarks2
    92:92:java.lang.String getPlaytime() -> getPlaytime
    96:97:void setPlaytime(java.lang.String) -> setPlaytime
com.kaolafm.report.event.BufferStartReportEvent -> com.kaolafm.report.event.BufferStartReportEvent:
    java.lang.String TYPE_NORMAL -> TYPE_NORMAL
    java.lang.String TYPE_PUSH -> TYPE_PUSH
    java.lang.String audioid -> audioid
    java.lang.String radioid -> radioid
    java.lang.String albumid -> albumid
    java.lang.String type -> type
    java.lang.String remarks1 -> remarks1
    java.lang.String remarks2 -> remarks2
    25:36:void <init>() -> <init>
    40:40:java.lang.String getAudioid() -> getAudioid
    44:45:void setAudioid(java.lang.String) -> setAudioid
    48:48:java.lang.String getRadioid() -> getRadioid
    52:53:void setRadioid(java.lang.String) -> setRadioid
    56:56:java.lang.String getAlbumid() -> getAlbumid
    60:61:void setAlbumid(java.lang.String) -> setAlbumid
    64:64:java.lang.String getType() -> getType
    68:69:void setType(java.lang.String) -> setType
    72:72:java.lang.String getRemarks1() -> getRemarks1
    76:77:void setRemarks1(java.lang.String) -> setRemarks1
    80:80:java.lang.String getRemarks2() -> getRemarks2
    84:85:void setRemarks2(java.lang.String) -> setRemarks2
com.kaolafm.report.event.CrashReportEvent -> com.kaolafm.report.event.CrashReportEvent:
    java.lang.String result -> result
    java.lang.String message -> message
    22:24:void <init>() -> <init>
    27:27:java.lang.String getResult() -> getResult
    31:32:void setResult(java.lang.String) -> setResult
    35:35:java.lang.String getMessage() -> getMessage
    39:40:void setMessage(java.lang.String) -> setMessage
com.kaolafm.report.event.EndListenReportEvent -> com.kaolafm.report.event.EndListenReportEvent:
    java.lang.String audioid -> audioid
    java.lang.String radioid -> radioid
    java.lang.String albumid -> albumid
    java.lang.String type -> type
    java.lang.String position -> position
    java.lang.String playtime -> playtime
    java.lang.String playrate -> playrate
    java.lang.String length -> length
    java.lang.String remarks2 -> remarks2
    java.lang.String remarks4 -> remarks4
    java.lang.String remarks6 -> remarks6
    java.lang.String remarks7 -> remarks7
    java.lang.String remarks8 -> remarks8
    java.lang.String remarks9 -> remarks9
    java.lang.String remarks10 -> remarks10
    java.lang.String ai_mz_location -> ai_mz_location
    35:92:void <init>() -> <init>
    95:128:void playParameterToEvent(com.kaolafm.report.model.PlayReportParameter) -> playParameterToEvent
    131:131:java.lang.String getAudioid() -> getAudioid
    135:136:void setAudioid(java.lang.String) -> setAudioid
    139:139:java.lang.String getRadioid() -> getRadioid
    143:144:void setRadioid(java.lang.String) -> setRadioid
    147:147:java.lang.String getAlbumid() -> getAlbumid
    151:152:void setAlbumid(java.lang.String) -> setAlbumid
    155:155:java.lang.String getType() -> getType
    159:160:void setType(java.lang.String) -> setType
    163:163:java.lang.String getPosition() -> getPosition
    167:168:void setPosition(java.lang.String) -> setPosition
    171:171:java.lang.String getPlaytime() -> getPlaytime
    175:176:void setPlaytime(java.lang.String) -> setPlaytime
    179:179:java.lang.String getPlayrate() -> getPlayrate
    183:184:void setPlayrate(java.lang.String) -> setPlayrate
    187:187:java.lang.String getLength() -> getLength
    191:192:void setLength(java.lang.String) -> setLength
    195:195:java.lang.String getRemarks2() -> getRemarks2
    199:200:void setRemarks2(java.lang.String) -> setRemarks2
    204:204:java.lang.String getRemarks4() -> getRemarks4
    208:209:void setRemarks4(java.lang.String) -> setRemarks4
    213:213:java.lang.String getRemarks6() -> getRemarks6
    217:218:void setRemarks6(java.lang.String) -> setRemarks6
    221:221:java.lang.String getRemarks7() -> getRemarks7
    225:226:void setRemarks7(java.lang.String) -> setRemarks7
    229:229:java.lang.String getRemarks8() -> getRemarks8
    233:234:void setRemarks8(java.lang.String) -> setRemarks8
    237:237:java.lang.String getRemarks9() -> getRemarks9
    241:242:void setRemarks9(java.lang.String) -> setRemarks9
    245:245:java.lang.String getAi_mz_location() -> getAi_mz_location
    249:250:void setAi_mz_location(java.lang.String) -> setAi_mz_location
    253:253:java.lang.String getRemarks10() -> getRemarks10
    257:258:void setRemarks10(java.lang.String) -> setRemarks10
com.kaolafm.report.event.RequetErrorReportEvent -> com.kaolafm.report.event.RequetErrorReportEvent:
    java.lang.String result -> result
    java.lang.String speed -> speed
    java.lang.String url -> url
    java.lang.String message -> message
    java.lang.String audioid -> audioid
    java.lang.String radioid -> radioid
    java.lang.String remarks1 -> remarks1
    java.lang.String remarks2 -> remarks2
    java.lang.String remarks3 -> remarks3
    19:45:void <init>() -> <init>
    48:48:java.lang.String getResult() -> getResult
    52:53:void setResult(java.lang.String) -> setResult
    56:56:java.lang.String getSpeed() -> getSpeed
    60:61:void setSpeed(java.lang.String) -> setSpeed
    64:64:java.lang.String getUrl() -> getUrl
    68:69:void setUrl(java.lang.String) -> setUrl
    72:72:java.lang.String getMessage() -> getMessage
    76:77:void setMessage(java.lang.String) -> setMessage
    80:80:java.lang.String getAudioid() -> getAudioid
    84:85:void setAudioid(java.lang.String) -> setAudioid
    88:88:java.lang.String getRadioid() -> getRadioid
    92:93:void setRadioid(java.lang.String) -> setRadioid
    96:96:java.lang.String getRemarks1() -> getRemarks1
    100:101:void setRemarks1(java.lang.String) -> setRemarks1
    104:104:java.lang.String getRemarks2() -> getRemarks2
    108:109:void setRemarks2(java.lang.String) -> setRemarks2
    112:112:java.lang.String getRemarks3() -> getRemarks3
    116:117:void setRemarks3(java.lang.String) -> setRemarks3
com.kaolafm.report.event.SearchResultReportEvent -> com.kaolafm.report.event.SearchResultReportEvent:
    java.lang.String SEARCH_TYPE_HAND -> SEARCH_TYPE_HAND
    java.lang.String SEARCH_TYPE_HISTORY -> SEARCH_TYPE_HISTORY
    java.lang.String SEARCH_TYPE_THINK_WORD -> SEARCH_TYPE_THINK_WORD
    java.lang.String SEARCH_TYPE_AUDIO -> SEARCH_TYPE_AUDIO
    java.lang.String SEARCH_RESULT_TYPE_TIME_OUT -> SEARCH_RESULT_TYPE_TIME_OUT
    java.lang.String SEARCH_RESULT_TYPE_PARAM_ERROR -> SEARCH_RESULT_TYPE_PARAM_ERROR
    java.lang.String SEARCH_RESULT_TYPE_NO_RESULT -> SEARCH_RESULT_TYPE_NO_RESULT
    java.lang.String SEARCH_RESULT_TYPE_SUCCESS -> SEARCH_RESULT_TYPE_SUCCESS
    java.lang.String request_agent -> request_agent
    java.lang.String type -> type
    java.lang.String result -> result
    java.lang.String playtype -> playtype
    java.lang.String remarks1 -> remarks1
    29:45:void <init>() -> <init>
    49:49:java.lang.String getRequest_agent() -> getRequest_agent
    53:54:void setRequest_agent(java.lang.String) -> setRequest_agent
    57:57:java.lang.String getType() -> getType
    61:62:void setType(java.lang.String) -> setType
    65:65:java.lang.String getResult() -> getResult
    69:70:void setResult(java.lang.String) -> setResult
    73:73:java.lang.String getPlaytype() -> getPlaytype
    77:78:void setPlaytype(java.lang.String) -> setPlaytype
    81:81:java.lang.String getRemarks1() -> getRemarks1
    85:86:void setRemarks1(java.lang.String) -> setRemarks1
com.kaolafm.report.event.SearchResultSelectReportEvent -> com.kaolafm.report.event.SearchResultSelectReportEvent:
    java.lang.String WAY_TYPE_AUDIO -> WAY_TYPE_AUDIO
    java.lang.String WAY_TYPE_HAND -> WAY_TYPE_HAND
    java.lang.String WAY_TYPE_UN_KNOW -> WAY_TYPE_UN_KNOW
    java.lang.String way -> way
    java.lang.String radioid -> radioid
    java.lang.String remarks2 -> remarks2
    java.lang.String remarks3 -> remarks3
    java.lang.String remarks9 -> remarks9
    19:39:void <init>() -> <init>
    42:42:java.lang.String getWay() -> getWay
    46:47:void setWay(java.lang.String) -> setWay
    50:50:java.lang.String getRadioid() -> getRadioid
    54:55:void setRadioid(java.lang.String) -> setRadioid
    58:58:java.lang.String getRemarks2() -> getRemarks2
    62:63:void setRemarks2(java.lang.String) -> setRemarks2
    66:66:java.lang.String getRemarks3() -> getRemarks3
    70:71:void setRemarks3(java.lang.String) -> setRemarks3
    74:74:java.lang.String getRemarks9() -> getRemarks9
    78:79:void setRemarks9(java.lang.String) -> setRemarks9
com.kaolafm.report.event.StartListenReportEvent -> com.kaolafm.report.event.StartListenReportEvent:
    java.lang.String audioid -> audioid
    java.lang.String radioid -> radioid
    java.lang.String albumid -> albumid
    java.lang.String type -> type
    java.lang.String position -> position
    java.lang.String remarks1 -> remarks1
    java.lang.String remarks2 -> remarks2
    java.lang.String remarks9 -> remarks9
    java.lang.String remarks4 -> remarks4
    java.lang.String ai_mz_location -> ai_mz_location
    java.lang.String remarks6 -> remarks6
    32:65:void <init>() -> <init>
    68:68:java.lang.String getAudioid() -> getAudioid
    72:73:void setAudioid(java.lang.String) -> setAudioid
    76:76:java.lang.String getRadioid() -> getRadioid
    80:81:void setRadioid(java.lang.String) -> setRadioid
    84:84:java.lang.String getAlbumid() -> getAlbumid
    88:89:void setAlbumid(java.lang.String) -> setAlbumid
    92:92:java.lang.String getType() -> getType
    96:97:void setType(java.lang.String) -> setType
    100:100:java.lang.String getPosition() -> getPosition
    104:105:void setPosition(java.lang.String) -> setPosition
    108:108:java.lang.String getRemarks1() -> getRemarks1
    112:113:void setRemarks1(java.lang.String) -> setRemarks1
    116:116:java.lang.String getRemarks2() -> getRemarks2
    120:121:void setRemarks2(java.lang.String) -> setRemarks2
    124:124:java.lang.String getRemarks4() -> getRemarks4
    128:129:void setRemarks4(java.lang.String) -> setRemarks4
    132:132:java.lang.String getRemarks9() -> getRemarks9
    136:137:void setRemarks9(java.lang.String) -> setRemarks9
    140:140:java.lang.String getAi_mz_location() -> getAi_mz_location
    144:145:void setAi_mz_location(java.lang.String) -> setAi_mz_location
    148:148:java.lang.String getRemarks6() -> getRemarks6
    152:153:void setRemarks6(java.lang.String) -> setRemarks6
com.kaolafm.report.event.StartReportEvent -> com.kaolafm.report.event.StartReportEvent:
    java.lang.String TYPE_AUDIO -> TYPE_AUDIO
    java.lang.String TYPE_LAUNCH -> TYPE_LAUNCH
    java.lang.String TYPE_WIDGET -> TYPE_WIDGET
    java.lang.String type -> type
    java.lang.String flow -> flow
    24:33:void <init>() -> <init>
    36:36:java.lang.String getType() -> getType
    40:41:void setType(java.lang.String) -> setType
    44:44:java.lang.String getFlow() -> getFlow
    48:49:void setFlow(java.lang.String) -> setFlow
com.kaolafm.report.event.UpdateReportEvent -> com.kaolafm.report.event.UpdateReportEvent:
    java.lang.String TYPE_BY_SELF -> TYPE_BY_SELF
    java.lang.String TYPE_BACK_GROUND -> TYPE_BACK_GROUND
    java.lang.String remarks1 -> remarks1
    java.lang.String remarks2 -> remarks2
    java.lang.String type -> type
    25:31:void <init>() -> <init>
    34:34:java.lang.String getType() -> getType
    38:39:void setType(java.lang.String) -> setType
    42:42:java.lang.String getRemarks1() -> getRemarks1
    46:47:void setRemarks1(java.lang.String) -> setRemarks1
    50:50:java.lang.String getRemarks2() -> getRemarks2
    54:55:void setRemarks2(java.lang.String) -> setRemarks2
com.kaolafm.report.inner.InnerPlayReportParameter -> com.kaolafm.report.inner.InnerPlayReportParameter:
    9:9:void <init>() -> <init>
    12:13:void setInnerPlayer() -> setInnerPlayer
com.kaolafm.report.model.PlayReportParameter -> com.kaolafm.report.model.PlayReportParameter:
    java.lang.String audioid -> audioid
    java.lang.String radioid -> radioid
    java.lang.String albumid -> albumid
    java.lang.String type -> type
    java.lang.String position -> position
    java.lang.String isStartFirstPlay -> isStartFirstPlay
    java.lang.String contentObtainType -> contentObtainType
    java.lang.String searchResultContent -> searchResultContent
    java.lang.String isFirst -> isFirst
    long playPosition -> playPosition
    long totalLength -> totalLength
    java.lang.String startTime -> startTime
    java.lang.String changeType -> changeType
    java.lang.String playId -> playId
    java.lang.String innerPlayer -> innerPlayer
    java.lang.String radioType -> radioType
    int isThirdParty -> isThirdParty
    10:90:void <init>() -> <init>
    93:93:java.lang.String getAudioid() -> getAudioid
    97:98:void setAudioid(java.lang.String) -> setAudioid
    101:101:java.lang.String getRadioid() -> getRadioid
    105:106:void setRadioid(java.lang.String) -> setRadioid
    109:109:java.lang.String getAlbumid() -> getAlbumid
    113:114:void setAlbumid(java.lang.String) -> setAlbumid
    117:117:java.lang.String getType() -> getType
    121:122:void setType(java.lang.String) -> setType
    125:125:java.lang.String getPosition() -> getPosition
    129:130:void setPosition(java.lang.String) -> setPosition
    134:134:long getPlayPosition() -> getPlayPosition
    138:139:void setPlayPosition(long) -> setPlayPosition
    142:142:long getTotalLength() -> getTotalLength
    146:147:void setTotalLength(long) -> setTotalLength
    150:150:java.lang.String getIsStartFirstPlay() -> getIsStartFirstPlay
    154:155:void setIsStartFirstPlay(java.lang.String) -> setIsStartFirstPlay
    158:158:java.lang.String getContentObtainType() -> getContentObtainType
    162:163:void setContentObtainType(java.lang.String) -> setContentObtainType
    166:166:java.lang.String getSearchResultContent() -> getSearchResultContent
    170:171:void setSearchResultContent(java.lang.String) -> setSearchResultContent
    174:174:java.lang.String getIsFirst() -> getIsFirst
    178:179:void setIsFirst(java.lang.String) -> setIsFirst
    182:182:java.lang.String getStartTime() -> getStartTime
    186:187:void setStartTime(java.lang.String) -> setStartTime
    190:190:java.lang.String getChangeType() -> getChangeType
    194:195:void setChangeType(java.lang.String) -> setChangeType
    198:198:java.lang.String getPlayId() -> getPlayId
    202:203:void setPlayId(java.lang.String) -> setPlayId
    206:206:int getIsThirdParty() -> getIsThirdParty
    210:211:void setIsThirdParty(int) -> setIsThirdParty
    214:214:boolean isSendEventNow() -> isSendEventNow
    218:218:java.lang.String getInnerPlayer() -> getInnerPlayer
    222:222:java.lang.String getRadioType() -> getRadioType
    226:227:void setRadioType(java.lang.String) -> setRadioType
com.kaolafm.report.model.ReportBean -> com.kaolafm.report.model.ReportBean:
    int mType -> mType
    java.util.List mIdList -> mIdList
    org.json.JSONArray jsonArray -> jsonArray
    16:19:void <init>() -> <init>
    22:24:void addData(java.lang.Long,java.lang.String) -> addData
    27:28:void addData(java.lang.String) -> addData
    31:31:java.util.List getIdList() -> getIdList
    35:35:java.lang.String getReportValue() -> getReportValue
    39:39:int getType() -> getType
    43:44:void setType(int) -> setType
com.kaolafm.report.model.ReportCarParameter -> com.kaolafm.report.model.ReportCarParameter:
    java.lang.String firstAppId -> firstAppId
    java.lang.String marketType -> marketType
    java.lang.String firstAppIdName -> firstAppIdName
    java.lang.String appIdType -> appIdType
    java.lang.String oem -> oem
    java.lang.String carBrand -> carBrand
    java.lang.String carType -> carType
    int timer -> timer
    long systemTime -> systemTime
    java.lang.String developer -> developer
    10:49:void <init>() -> <init>
    63:63:int getTimer() -> getTimer
    67:68:void setTimer(int) -> setTimer
    71:71:long getSystemTime() -> getSystemTime
    75:76:void setSystemTime(long) -> setSystemTime
    79:79:java.lang.String getFirstAppId() -> getFirstAppId
    83:84:void setFirstAppId(java.lang.String) -> setFirstAppId
    87:87:java.lang.String getMarketType() -> getMarketType
    91:92:void setMarketType(java.lang.String) -> setMarketType
    95:95:java.lang.String getFirstAppIdName() -> getFirstAppIdName
    99:100:void setFirstAppIdName(java.lang.String) -> setFirstAppIdName
    103:103:java.lang.String getAppIdType() -> getAppIdType
    107:108:void setAppIdType(java.lang.String) -> setAppIdType
    111:111:java.lang.String getOem() -> getOem
    115:116:void setOem(java.lang.String) -> setOem
    119:119:java.lang.String getCarBrand() -> getCarBrand
    123:124:void setCarBrand(java.lang.String) -> setCarBrand
    127:127:java.lang.String getCarType() -> getCarType
    131:132:void setCarType(java.lang.String) -> setCarType
    135:135:java.lang.String getDeveloper() -> getDeveloper
    139:140:void setDeveloper(java.lang.String) -> setDeveloper
com.kaolafm.report.model.ReportParameter -> com.kaolafm.report.model.ReportParameter:
    java.lang.String deviceId -> deviceId
    java.lang.String appid -> appid
    java.lang.String uid -> uid
    java.lang.String openid -> openid
    java.lang.String lib_version -> lib_version
    java.lang.String channel -> channel
    8:8:void <init>() -> <init>
    20:20:java.lang.String getAppid() -> getAppid
    24:25:void setAppid(java.lang.String) -> setAppid
    28:28:java.lang.String getUid() -> getUid
    32:33:void setUid(java.lang.String) -> setUid
    36:36:java.lang.String getOpenid() -> getOpenid
    40:41:void setOpenid(java.lang.String) -> setOpenid
    44:44:java.lang.String getLib_version() -> getLib_version
    48:49:void setLib_version(java.lang.String) -> setLib_version
    52:52:java.lang.String getDeviceId() -> getDeviceId
    56:57:void setDeviceId(java.lang.String) -> setDeviceId
    60:60:java.lang.String getChannel() -> getChannel
    64:65:void setChannel(java.lang.String) -> setChannel
com.kaolafm.report.model.ReportPrivateParameter -> com.kaolafm.report.model.ReportPrivateParameter:
    long action_id -> action_id
    int mSessionId -> mSessionId
    java.lang.String app_version -> app_version
    boolean isFirstListen -> isFirstListen
    java.lang.String carType -> carType
    7:11:void <init>() -> <init>
    15:16:long getAction_id() -> getAction_id
    20:21:void setAction_id(long) -> setAction_id
    24:24:int getmSessionId() -> getmSessionId
    28:29:void setmSessionId(int) -> setmSessionId
    32:32:java.lang.String getApp_version() -> getApp_version
    36:37:void setApp_version(java.lang.String) -> setApp_version
    40:40:boolean isFirstListen() -> isFirstListen
    44:45:void setFirstListen(boolean) -> setFirstListen
    48:48:java.lang.String getCarType() -> getCarType
    52:53:void setCarType(java.lang.String) -> setCarType
com.kaolafm.report.model.ReportTask -> com.kaolafm.report.model.ReportTask:
    int type -> type
    io.reactivex.Single singleTask -> singleTask
    9:9:void <init>() -> <init>
    14:14:io.reactivex.Single getSingleTask() -> getSingleTask
    18:19:void setSingleTask(io.reactivex.Single) -> setSingleTask
    22:22:int getType() -> getType
    26:27:void setType(int) -> setType
com.kaolafm.report.util.ConfigDBHelper -> com.kaolafm.report.util.ConfigDBHelper:
    int TYPE_PLAY_PARAMETER -> TYPE_PLAY_PARAMETER
    int TYPE_REPORT_PRIVATE_PARAMETER -> TYPE_REPORT_PRIVATE_PARAMETER
    int TYPE_REPORT_CAR_PARAMETER -> TYPE_REPORT_CAR_PARAMETER
    java.lang.String DATA_BASE_NAME -> DATA_BASE_NAME
    com.kaolafm.report.util.ConfigDBHelper configDBHelper -> configDBHelper
    com.kaolafm.report.database.greendao.DaoSession daoSession -> daoSession
    28:29:void <init>() -> <init>
    33:40:com.kaolafm.report.util.ConfigDBHelper getInstance() -> getInstance
    44:48:void init() -> init
    52:56:io.reactivex.Single insertData(com.kaolafm.report.database.ConfigData) -> insertData
    60:63:io.reactivex.Single deleteData(java.lang.Long) -> deleteData
    67:73:io.reactivex.Single read(java.lang.String) -> read
    78:78:io.reactivex.Single readAll() -> readAll
    78:78:java.util.List lambda$readAll$3() -> lambda$readAll$3
    68:72:com.kaolafm.report.database.ConfigData lambda$read$2(java.lang.String) -> lambda$read$2
    61:62:java.lang.Long lambda$deleteData$1(java.lang.Long) -> lambda$deleteData$1
    53:54:java.lang.Long lambda$insertData$0(com.kaolafm.report.database.ConfigData) -> lambda$insertData$0
com.kaolafm.report.util.CrashHandler -> com.kaolafm.report.util.CrashHandler:
    java.lang.Thread$UncaughtExceptionHandler mDefaultHandler -> mDefaultHandler
    com.kaolafm.report.util.CrashHandler sInstance -> sInstance
    android.content.Context mContext -> mContext
    com.kaolafm.report.util.CrashHandler$OnCrashListener mCrashListener -> mCrashListener
    19:20:void <init>() -> <init>
    23:30:com.kaolafm.report.util.CrashHandler getInstance() -> getInstance
    34:37:void programStart(android.content.Context) -> programStart
    40:41:void setOnCrashListener(com.kaolafm.report.util.CrashHandler$OnCrashListener) -> setOnCrashListener
    45:60:void uncaughtException(java.lang.Thread,java.lang.Throwable) -> uncaughtException
com.kaolafm.report.util.CrashHandler$OnCrashListener -> com.kaolafm.report.util.CrashHandler$OnCrashListener:
    void onCrash(java.lang.Throwable) -> onCrash
com.kaolafm.report.util.InitUtil -> com.kaolafm.report.util.InitUtil:
    16:16:void <init>() -> <init>
    19:49:void initCarInfo() -> initCarInfo
    52:59:com.kaolafm.report.model.ReportParameter init(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String) -> init
    68:77:boolean isNeedUpdateReportInfo(long) -> isNeedUpdateReportInfo
com.kaolafm.report.util.InitUtil$1 -> com.kaolafm.report.util.InitUtil$1:
    20:20:void <init>() -> <init>
    23:42:void onSuccess(com.kaolafm.report.api.carinfo.model.CarInfoData) -> onSuccess
    46:46:void onError(ApiException) -> onError
    20:20:void onSuccess(java.lang.Object) -> onSuccess
com.kaolafm.report.util.PlayReportManager -> com.kaolafm.report.util.PlayReportManager:
    int SAVE_POSITION_TIMER -> SAVE_POSITION_TIMER
    com.kaolafm.report.model.PlayReportParameter mPlayReportParameter -> mPlayReportParameter
    java.lang.String mPlayType -> mPlayType
    java.lang.String mPlayCallBack -> mPlayCallBack
    io.reactivex.disposables.Disposable mDisposable -> mDisposable
    30:32:void <init>() -> <init>
    35:61:void setPlayReportParameter(com.kaolafm.report.model.PlayReportParameter) -> setPlayReportParameter
    64:77:void addStartEvent() -> addStartEvent
    80:83:void addEndEvent() -> addEndEvent
    90:98:void addEndEvent(java.lang.String,boolean) -> addEndEvent
    101:104:void clearPlayReportParameter() -> clearPlayReportParameter
    112:117:void setPosition(long,long) -> setPosition
    122:128:void savePlayPosition() -> savePlayPosition
    131:148:void saveDB() -> saveDB
    151:155:void stopSave() -> stopSave
    158:160:void setSearchPlayCallBack(java.lang.String,java.lang.String) -> setSearchPlayCallBack
    163:172:void makePlayId(java.lang.String) -> makePlayId
    175:183:void initPlayReportParameter(com.kaolafm.report.model.PlayReportParameter) -> initPlayReportParameter
    187:189:void release() -> release
    146:146:void lambda$saveDB$1(java.lang.Long) -> lambda$saveDB$1
    127:127:void lambda$savePlayPosition$0(java.lang.Long) -> lambda$savePlayPosition$0
com.kaolafm.report.util.ReportConstants -> com.kaolafm.report.util.ReportConstants:
    java.lang.String REPORT_TAG -> REPORT_TAG
    java.lang.String REPORT_BASE_URL -> REPORT_BASE_URL
    java.lang.String REPORT_DOMAIN_NAME -> REPORT_DOMAIN_NAME
    java.lang.String DOMAIN_HEADER_REPORT -> DOMAIN_HEADER_REPORT
    java.lang.String GET_CAR_INFO -> GET_CAR_INFO
    int READ_DATE_BASE_TIMER -> READ_DATE_BASE_TIMER
    int READ_DATE_BASE_MAX_TIMER -> READ_DATE_BASE_MAX_TIMER
    int READ_DATA_BASE_MAX_COUNT -> READ_DATA_BASE_MAX_COUNT
    int TASK_TYPE_INSERT -> TASK_TYPE_INSERT
    int TASK_TYPE_SEND_DATA -> TASK_TYPE_SEND_DATA
    int TASK_TYPE_DELETE -> TASK_TYPE_DELETE
    int UPLOAD_TASK_TYPE_NORMAL -> UPLOAD_TASK_TYPE_NORMAL
    int UPLOAD_TASK_TYPE_BY_DATA_BASE -> UPLOAD_TASK_TYPE_BY_DATA_BASE
    java.lang.String EVENT_ID_START -> EVENT_ID_START
    java.lang.String EVENT_ID_BUFFER_START -> EVENT_ID_BUFFER_START
    java.lang.String EVENT_ID_BUFFER_END -> EVENT_ID_BUFFER_END
    java.lang.String EVENT_ID_UPDATE -> EVENT_ID_UPDATE
    java.lang.String EVENT_ID_LISTEN_START -> EVENT_ID_LISTEN_START
    java.lang.String EVENT_ID_LISTEN_END -> EVENT_ID_LISTEN_END
    java.lang.String EVENT_ID_REQUEST_ERROR -> EVENT_ID_REQUEST_ERROR
    java.lang.String EVENT_ID_CRASH -> EVENT_ID_CRASH
    java.lang.String EVENT_ID_AUDIO_SEARCH -> EVENT_ID_AUDIO_SEARCH
    java.lang.String EVENT_ID_SEARCH_RESULT_SELECT -> EVENT_ID_SEARCH_RESULT_SELECT
    java.lang.String EVENT_ID_SEARCH_RESULT -> EVENT_ID_SEARCH_RESULT
    java.lang.String VALUE_OS_ANDROID -> VALUE_OS_ANDROID
    java.lang.String PLAY_TYPE_OFF_LINE -> PLAY_TYPE_OFF_LINE
    java.lang.String PLAY_TYPE_ON_LINE -> PLAY_TYPE_ON_LINE
    java.lang.String PLAY_CHANGE_BY_AUTO -> PLAY_CHANGE_BY_AUTO
    java.lang.String PLAY_CHANGE_BY_OTHER -> PLAY_CHANGE_BY_OTHER
    java.lang.String POSITION_INNER_APP -> POSITION_INNER_APP
    java.lang.String POSITION_OUT_APP -> POSITION_OUT_APP
    java.lang.String COTENT_BY_AUDIO -> COTENT_BY_AUDIO
    java.lang.String COTENT_BY_SEARCH -> COTENT_BY_SEARCH
    java.lang.String COTENT_BY_OTHER -> COTENT_BY_OTHER
    java.lang.String CARRIER_UN_KNOW -> CARRIER_UN_KNOW
    java.lang.String CARRIER_YIDONG -> CARRIER_YIDONG
    java.lang.String CARRIER_LIANTONG -> CARRIER_LIANTONG
    java.lang.String CARRIER_DIANXIN -> CARRIER_DIANXIN
    java.lang.String FIRST_LISTEN -> FIRST_LISTEN
    java.lang.String QQ_API_AUDIO_ID -> QQ_API_AUDIO_ID
    java.lang.String QQ_RADIO_AUDIO_ID -> QQ_RADIO_AUDIO_ID
    int UPDATE_REPORT_INFO_MAX_DAY -> UPDATE_REPORT_INFO_MAX_DAY
    int REPORT_EVENT_NORMAL -> REPORT_EVENT_NORMAL
    int REPORT_EVENT_RIGHT_NOW -> REPORT_EVENT_RIGHT_NOW
    9:9:void <init>() -> <init>
com.kaolafm.report.util.ReportShenCeDBHelper -> com.kaolafm.report.util.ReportShenCeDBHelper:
    java.lang.String DATA_BASE_NAME -> DATA_BASE_NAME
    com.kaolafm.report.util.ReportShenCeDBHelper reportDBHelper -> reportDBHelper
    com.kaolafm.report.database.greendao.DaoSession daoSession -> daoSession
    25:26:void <init>() -> <init>
    30:37:com.kaolafm.report.util.ReportShenCeDBHelper getInstance() -> getInstance
    41:45:void init() -> init
    48:54:io.reactivex.Single insertData(java.lang.String) -> insertData
    58:61:io.reactivex.Single deleteDataList(java.util.List) -> deleteDataList
    65:75:io.reactivex.Single read() -> read
    69:74:com.kaolafm.report.model.ReportBean lambda$read$3(java.util.List) -> lambda$read$3
    66:67:java.util.List lambda$read$2() -> lambda$read$2
    59:60:java.lang.Long lambda$deleteDataList$1(java.util.List) -> lambda$deleteDataList$1
    51:52:java.lang.Long lambda$insertData$0(com.kaolafm.report.database.ReportData) -> lambda$insertData$0
com.kaolafm.report.util.ReportNetworkHelper -> com.kaolafm.report.util.ReportNetworkHelper:
    com.kaolafm.report.util.ReportNetworkHelper reportNetworkHelper -> reportNetworkHelper
    17:19:void <init>() -> <init>
    22:29:com.kaolafm.report.util.ReportNetworkHelper getInstance() -> getInstance
    41:48:void request(java.lang.String,com.kaolafm.report.util.ReportNetworkHelper$IRequestCallBack) -> request
    46:47:void lambda$request$2(com.kaolafm.report.util.ReportNetworkHelper$IRequestCallBack,java.lang.Throwable) -> lambda$request$2
    44:45:void lambda$request$1(com.kaolafm.report.util.ReportNetworkHelper$IRequestCallBack,java.lang.Boolean) -> lambda$request$1
    41:41:java.lang.Boolean lambda$request$0(java.lang.String) -> lambda$request$0
com.kaolafm.report.util.ReportNetworkHelper$IRequestCallBack -> com.kaolafm.report.util.ReportNetworkHelper$IRequestCallBack:
    void result(boolean) -> result
com.kaolafm.report.util.ReportParameterManager -> com.kaolafm.report.util.ReportParameterManager:
    java.lang.String mStartFirst -> mStartFirst
    java.lang.String imsi -> imsi
    java.lang.String os -> os
    java.lang.String screen_height -> screen_height
    java.lang.String screen_width -> screen_width
    java.lang.String os_version -> os_version
    java.lang.String wifi -> wifi
    java.lang.String network_type -> network_type
    java.lang.String carrier -> carrier
    java.lang.String screen_direction -> screen_direction
    java.lang.String page -> page
    java.lang.String model -> model
    java.lang.String manufacturer -> manufacturer
    java.lang.String lon -> lon
    java.lang.String lat -> lat
    java.lang.String playId -> playId
    java.lang.String versionName -> versionName
    java.lang.String appStartType -> appStartType
    com.kaolafm.report.model.ReportCarParameter mReportCarParameter -> mReportCarParameter
    com.kaolafm.report.model.ReportParameter mReportParameter -> mReportParameter
    com.kaolafm.report.model.ReportPrivateParameter mPrivateParameter -> mPrivateParameter
    com.kaolafm.report.util.ReportParameterManager reportParameterManager -> reportParameterManager
    boolean isInit -> isInit
    27:75:void <init>() -> <init>
    78:85:com.kaolafm.report.util.ReportParameterManager getInstance() -> getInstance
    89:92:void init(com.kaolafm.report.model.ReportParameter,com.kaolafm.report.util.PlayReportManager) -> init
    95:100:void initDBRecord(com.kaolafm.report.util.PlayReportManager) -> initDBRecord
    103:146:void initDBRecord(java.util.List,com.kaolafm.report.util.PlayReportManager) -> initDBRecord
    149:159:void initOther() -> initOther
    162:168:void initNetwork() -> initNetwork
    172:175:int getmSessionId() -> getmSessionId
    180:183:int getTimer() -> getTimer
    188:188:java.lang.String getImsi() -> getImsi
    192:193:void setImsi(java.lang.String) -> setImsi
    196:196:java.lang.String getOs() -> getOs
    200:201:void setOs(java.lang.String) -> setOs
    204:204:java.lang.String getScreen_height() -> getScreen_height
    208:209:void setScreen_height(java.lang.String) -> setScreen_height
    212:212:java.lang.String getScreen_width() -> getScreen_width
    216:217:void setScreen_width(java.lang.String) -> setScreen_width
    220:220:java.lang.String getOs_version() -> getOs_version
    224:225:void setOs_version(java.lang.String) -> setOs_version
    228:231:java.lang.String getApp_version() -> getApp_version
    236:236:java.lang.String getWifi() -> getWifi
    240:240:java.lang.String getNetwork_type() -> getNetwork_type
    244:244:java.lang.String getCarrier() -> getCarrier
    248:249:void setCarrier(java.lang.String) -> setCarrier
    253:253:java.lang.String getScreen_direction() -> getScreen_direction
    257:258:void setScreen_direction(java.lang.String) -> setScreen_direction
    261:261:java.lang.String getPage() -> getPage
    265:266:void setPage(java.lang.String) -> setPage
    269:269:com.kaolafm.report.model.ReportParameter getReportParameter() -> getReportParameter
    273:274:void setReportParameter(com.kaolafm.report.model.ReportParameter) -> setReportParameter
    277:278:void setWifi(java.lang.String) -> setWifi
    281:282:void setNetwork_type(java.lang.String) -> setNetwork_type
    285:285:java.lang.String getManufacturer() -> getManufacturer
    289:290:void setManufacturer(java.lang.String) -> setManufacturer
    293:293:com.kaolafm.report.model.ReportCarParameter getReportCarParameter() -> getReportCarParameter
    297:299:void setReportCarParameter(com.kaolafm.report.model.ReportCarParameter) -> setReportCarParameter
    302:302:java.lang.String getLon() -> getLon
    306:307:void setLon(java.lang.String) -> setLon
    310:310:java.lang.String getLat() -> getLat
    314:315:void setLat(java.lang.String) -> setLat
    318:323:long getActionId() -> getActionId
    327:330:boolean isFirstStart() -> isFirstStart
    335:336:void initModel() -> initModel
    339:339:java.lang.String getModel() -> getModel
    344:346:java.lang.String getStartFirst() -> getStartFirst
    351:366:void initUpdate() -> initUpdate
    369:381:boolean isNeedReportUpdate(java.lang.String) -> isNeedReportUpdate
    386:386:java.lang.String getPlayId() -> getPlayId
    390:391:void setPlayId(java.lang.String) -> setPlayId
    394:397:void setUid(java.lang.String) -> setUid
    400:408:void savePrivateParameter() -> savePrivateParameter
    411:418:void saveCarParameter() -> saveCarParameter
    421:421:java.lang.String getAppStartType() -> getAppStartType
    425:429:void setAppStartType(java.lang.String) -> setAppStartType
    432:441:void initAppStart() -> initAppStart
    444:452:boolean isFirstListen() -> isFirstListen
    456:456:java.lang.String getVersionName() -> getVersionName
    460:461:void setVersionName(java.lang.String) -> setVersionName
    464:468:void setCarType(java.lang.String) -> setCarType
    471:474:java.lang.String getCarType() -> getCarType
    417:417:void lambda$saveCarParameter$5(java.lang.Throwable) -> lambda$saveCarParameter$5
    416:416:void lambda$saveCarParameter$4(java.lang.Long) -> lambda$saveCarParameter$4
    407:407:void lambda$savePrivateParameter$3(java.lang.Throwable) -> lambda$savePrivateParameter$3
    406:406:void lambda$savePrivateParameter$2(java.lang.Long) -> lambda$savePrivateParameter$2
    98:99:void lambda$initDBRecord$1(com.kaolafm.report.util.PlayReportManager,java.lang.Throwable) -> lambda$initDBRecord$1
    96:97:void lambda$initDBRecord$0(com.kaolafm.report.util.PlayReportManager,java.util.List) -> lambda$initDBRecord$0
com.kaolafm.report.util.ReportParameterUtil -> com.kaolafm.report.util.ReportParameterUtil:
    15:15:void <init>() -> <init>
    18:24:java.lang.String getIMSI(android.content.Context) -> getIMSI
    34:42:java.lang.String getVersionName() -> getVersionName
    52:59:java.lang.String getVersionCode() -> getVersionCode
    66:72:java.lang.String getScreenHeight() -> getScreenHeight
    79:85:java.lang.String getScreenWidth() -> getScreenWidth
    89:94:java.lang.String getOsVersion() -> getOsVersion
    104:120:java.lang.String getOperator(android.content.Context) -> getOperator
com.kaolafm.report.util.ReportShenCeTaskHelper -> com.kaolafm.report.util.ReportShenCeTaskHelper:
    com.kaolafm.report.util.ReportShenCeTaskHelper reportTaskHelper -> reportTaskHelper
    boolean isRunning -> isRunning
    java.util.LinkedList reportTaskLinkedList -> reportTaskLinkedList
    boolean isClose -> isClose
    17:24:void <init>() -> <init>
    27:34:com.kaolafm.report.util.ReportShenCeTaskHelper getInstance() -> getInstance
    38:46:void insertTask(com.kaolafm.report.model.ReportTask) -> insertTask
    49:50:void taskDone() -> taskDone
    53:57:com.kaolafm.report.model.ReportTask getTask() -> getTask
    61:121:void run() -> run
    129:144:boolean isHasSendTask() -> isHasSendTask
    148:150:void release() -> release
    113:114:void lambda$run$5(java.lang.Throwable) -> lambda$run$5
    107:112:void lambda$run$4(java.lang.Long) -> lambda$run$4
    97:99:void lambda$run$3(java.lang.Throwable) -> lambda$run$3
    89:96:void lambda$run$2(com.kaolafm.report.model.ReportBean) -> lambda$run$2
    79:81:void lambda$run$1(java.lang.Throwable) -> lambda$run$1
    73:78:void lambda$run$0(java.lang.Long) -> lambda$run$0
com.kaolafm.report.util.ReportShenCeTimerManager -> com.kaolafm.report.util.ReportShenCeTimerManager:
    io.reactivex.disposables.Disposable mDisposable -> mDisposable
    com.kaolafm.report.util.ReportShenCeTimerManager reportTimerManager -> reportTimerManager
    com.kaolafm.base.utils.NetworkMonitor$OnNetworkStatusChangedListener onNetworkStatusChangedListener -> onNetworkStatusChangedListener
    22:27:void <init>() -> <init>
    30:37:com.kaolafm.report.util.ReportShenCeTimerManager getInstance() -> getInstance
    42:47:void init() -> init
    50:54:void runTimer() -> runTimer
    57:68:void addSendTask() -> addSendTask
    74:81:void initCrashHandler() -> initCrashHandler
    84:89:int getTimer() -> getTimer
    93:102:void release() -> release
    77:80:void lambda$initCrashHandler$2(java.lang.Throwable) -> lambda$initCrashHandler$2
    70:71:void lambda$static$1(int,int) -> lambda$static$1
    53:53:void lambda$runTimer$0(java.lang.Long) -> lambda$runTimer$0
    70:70:void <clinit>() -> <clinit>
com.kaolafm.report.util.ReportUploadShenCeTask -> com.kaolafm.report.util.ReportUploadShenCeTask:
    com.kaolafm.report.model.ReportBean mReportBean -> mReportBean
    15:17:void <init>(com.kaolafm.report.model.ReportBean) -> <init>
    20:27:void report() -> report
    30:39:void disposeErrorResult() -> disposeErrorResult
    42:52:void disposeSuccessResult() -> disposeSuccessResult
    21:26:void lambda$report$0(boolean) -> lambda$report$0
