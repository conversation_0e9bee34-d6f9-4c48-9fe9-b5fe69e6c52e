package com.kaolafm.ad.di.component;

import com.kaolafm.ad.AdvertOptions;
import com.kaolafm.ad.AdvertisingEngine;
import com.kaolafm.ad.di.module.AdCommonParamModule;
import com.kaolafm.ad.di.module.AdvertConfigModule;
import com.kaolafm.ad.di.module.EmptyModule;
import com.kaolafm.opensdk.di.component.CoreComponent;
import com.kaolafm.opensdk.di.component.Injector;
import com.kaolafm.opensdk.di.module.CommonHttpCfgModule;
import com.kaolafm.opensdk.di.module.HttpClientModule;
import com.kaolafm.opensdk.di.scope.AppScope;

import dagger.Component;

/**
 * <AUTHOR>
 * @date 2020-01-10
 */
@AppScope
@Component(modules = {
        AdCommonParamModule.class,
        AdvertConfigModule.class,
        EmptyModule.class,
        CommonHttpCfgModule.class,
        HttpClientModule.class})
public interface AdvertisingComponent extends CoreComponent<AdvertSubcomponent>, Injector<AdvertisingEngine> {

    @Component.Builder
    interface Builder extends CoreComponent.Builder<AdvertisingComponent, AdvertOptions> {
    }

}
