package com.kaolafm.report.model;

import org.json.JSONArray;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> on 2019/1/10.
 */

public class ReportBeanBigData {
    private int mType;
    private Long mId;
    private String mReportJson;

    public ReportBeanBigData() {
    }

    public void addData(Long id, String reportJson) {
        mId=id;
        mReportJson=reportJson;
    }

    public void addData(String reportJson) {
        mReportJson=reportJson;
    }


    public int getType() {
        return mType;
    }

    public void setType(int mType) {
        this.mType = mType;
    }

    public Long getId() {
        return mId;
    }

    public void setId(Long mId) {
        this.mId = mId;
    }

    public String getReportJson() {
        return mReportJson;
    }

    public void setReportJson(String mReportJson) {
        this.mReportJson = mReportJson;
    }
}
