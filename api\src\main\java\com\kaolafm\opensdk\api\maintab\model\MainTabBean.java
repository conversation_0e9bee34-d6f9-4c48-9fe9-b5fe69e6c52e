package com.kaolafm.opensdk.api.maintab.model;

/**
 * 导航条数据
 */
public class MainTabBean {
    private String name;//导航名
    private String pageId;//导航跳转id
    private String icon;//导航图标
    private int isLandingPage;//是否是落地页  0-否 1-是
    private String linkZone;

    public String getLinkZone(){
        return this.linkZone;
    }
    public void setLinkZone(String linkZone){
        this.linkZone = linkZone;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPageId() {
        return pageId;
    }

    public void setPageId(String pageId) {
        this.pageId = pageId;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public int getIsLandingPage() {
        return isLandingPage;
    }

    public void setIsLandingPage(int isLandingPage) {
        this.isLandingPage = isLandingPage;
    }

    @Override
    public String toString() {
        return "MainTabBean{" +
                "name='" + name + '\'' +
                ", pageId='" + pageId + '\'' +
                ", icon='" + icon + '\'' +
                ", isLandingPage=" + isLandingPage +
                ", linkZone=" + linkZone +
                '}';
    }
}
