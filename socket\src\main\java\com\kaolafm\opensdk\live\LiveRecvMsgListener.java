package com.kaolafm.opensdk.live;

import android.util.Log;

import com.kaolafm.opensdk.api.live.model.LiveChatRoomMessageNew;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.socket.SocketEvent;
import com.kaolafm.opensdk.socket.SocketListener;

import java.util.Map;

/**
 * 直播 - 实时接收聊天室消息（用户消息以及直播间通知） - 客户端监听事件
 */
public class LiveRecvMsgListener implements SocketListener<LiveChatRoomMessageNew> {

    private static final String TAG = "LiveRecvMsg";

    private LiveRecvMsgListener() {
    }

    public static LiveRecvMsgListener INSTANCE = new LiveRecvMsgListener();

    @Override
    public String getEvent() {
        return SocketEvent.LIVE_RECV_MSG;
    }

    @Override
    public Map<String, Object> getParams(Map<String, Object> params) {
        return params;
    }

    @Override
    public boolean isNeedParams() {
        return true;
    }

    @Override
    public boolean isNeedRequest() {
        return false;
    }

    @Override
    public void onSuccess(LiveChatRoomMessageNew liveChatRoomMessageNew) {
        // 直播 - 实时接收聊天室消息（用户消息以及直播间通知） - 客户端监听事件
        Log.e(TAG, "---live   LiveRecvMsgListener onSuccess liveChatRoomMessageNew=" + liveChatRoomMessageNew);
        LiveSocketManager.getInstance().callLiveRecvMsgListener(liveChatRoomMessageNew);
    }

    @Override
    public void onError(ApiException exception) {
        Log.e(TAG, "---live   LiveRecvMsgListener onError exception=" + exception);
    }
}
