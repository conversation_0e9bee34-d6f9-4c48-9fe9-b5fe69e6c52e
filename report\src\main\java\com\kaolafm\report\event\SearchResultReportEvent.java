package com.kaolafm.report.event;


import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> on 2019/1/21.
 * 搜索结果选择
 */

public class SearchResultReportEvent extends BaseReportEventBean {
    public static final String SEARCH_TYPE_HAND = "1";
    public static final String SEARCH_TYPE_HISTORY = "2";
    public static final String SEARCH_TYPE_THINK_WORD = "3";
    public static final String SEARCH_TYPE_AUDIO = "4";
    public static final String SEARCH_TYPE_HOT_KEYWORD = "5";

    public static final String SEARCH_RESULT_TYPE_TIME_OUT = "0";
    public static final String SEARCH_RESULT_TYPE_PARAM_ERROR = "1";
    public static final String SEARCH_RESULT_TYPE_NO_RESULT = "2";
    public static final String SEARCH_RESULT_TYPE_SUCCESS = "3";

    /**
     * 用户原始文本，比如 ‘我要听刘德华的冰雨
     */
    private String request_agent;
    /**
     * 1.手动输入；2.点击历史记录；3.点击联想词；4.语音搜索；5.点击热门搜索
     */
    private String type = SEARCH_TYPE_HAND;
    /**
     * 搜索返回情况 0：超时；1：参数有误；2：无结果；3：正常
     */
    private String result = SEARCH_RESULT_TYPE_SUCCESS;
    /**
     * 直播播放类型 0:否，1：是
     */
    private String playtype;
    /**
     * 搜索关键词 语义理解的结果，比如‘刘德华，冰雨’
     */
    private String remarks1;

    /**
     * 内容分类
     */
    private String remarks2;

    public SearchResultReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_SEARCH_RESULT);
    }


    public String getRequest_agent() {
        return request_agent;
    }

    public void setRequest_agent(String request_agent) {
        this.request_agent = request_agent;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getPlaytype() {
        return playtype;
    }

    public void setPlaytype(String playtype) {
        this.playtype = playtype;
    }

    public String getRemarks1() {
        return remarks1;
    }

    public void setRemarks1(String remarks1) {
        this.remarks1 = remarks1;
    }

    public String getRemarks2() {
        return remarks2;
    }

    public void setRemarks2(String remarks2) {
        this.remarks2 = remarks2;
    }
}
