package com.kaolafm.opensdk.demo.live.chat;


public class MessageBean {
    /**
     * 消息发送中
     */
    public static final int MSG_SENDING = 1;
    /**
     * 消息发送成功
     */
    public static final int MSG_SEND_SUCCESS = 2;
    /**
     * 消息发失败
     */
    public static final int MSG_SEND_FAILED = 3;

    public NimManager.SendChatMsgData sendChatMsgData;
    /**
     * 消息类型
     */
    public int type;
    public boolean isForbidden;
    /**
     * 是否被加入黑名单 true为是，false为否
     */
    public boolean isInBlackList;
    public int sendStatus;

    public String account;
    //    public String messageID;
    public String chatTime;
    public String userIconUrl;
    public String contentString;
    public String nickName;
    public String localThumbPath;

    /**
     * 存储 当前item中的图像高度
     */
//    public int itemHeight;

    /**
     * 其他类型数据
     */
//    public Object exData;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }


    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }


}