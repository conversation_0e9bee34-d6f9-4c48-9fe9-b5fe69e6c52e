package com.kaolafm.opensdk.socket;

import io.reactivex.Observable;
import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

/**
 * 线程相关工具类
 * <AUTHOR>
 * @date 2019-07-28
 */
public class ThreadUtil {

    /**
     * rxjava实现在UI线程执行
     */
    public static void runOnUI(Runnable task) {
        Observable.just(task)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(getObserver());
    }

    /**
     * rxjava实现在子线程执行
     */
    public static void runOnThread(Runnable task) {
        Observable.just(task)
                .observeOn(Schedulers.io())
                .subscribe(getObserver());
    }

    private static void dispose(Disposable disposable) {
        if (disposable != null && !disposable.isDisposed()) {
            disposable.dispose();
        }
    }

    private static Observer<Runnable> getObserver() {
        return new Observer<Runnable>() {

            private Disposable mDisposable;

            @Override
            public void onSubscribe(Disposable d) {
                mDisposable = d;
            }

            @Override
            public void onNext(Runnable task) {
                if (task != null) {
                    task.run();
                }
                dispose(mDisposable);

            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onComplete() {
                dispose(mDisposable);
            }
        };
    }
}
