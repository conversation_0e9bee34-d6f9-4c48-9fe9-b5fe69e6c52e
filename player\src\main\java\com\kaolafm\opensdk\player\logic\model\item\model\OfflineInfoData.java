package com.kaolafm.opensdk.player.logic.model.item.model;

/**
 * 离线信息-数据类
 */
public class OfflineInfoData {
    /**
     * 离线url
     */
    private String offlineUrl;

    /**
     * 是否为离线
     */
    private boolean isOffline;

    /**
     * 离线地址
     */
    private String offlinePlayUrl;

    /**
     * 文件大小
     */
    private long fileSize;

    /**
     * 专辑下载图片地址
     */
    private String albumOfflinePic;

    public String getOfflineUrl() {
        return offlineUrl;
    }

    public void setOfflineUrl(String offlineUrl) {
        this.offlineUrl = offlineUrl;
    }

    public boolean isOffline() {
        return isOffline;
    }

    public void setOffline(boolean offline) {
        isOffline = offline;
    }

    public String getOfflinePlayUrl() {
        return offlinePlayUrl;
    }

    public void setOfflinePlayUrl(String offlinePlayUrl) {
        this.offlinePlayUrl = offlinePlayUrl;
    }

    public long getFileSize() {
        return fileSize;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }

    public String getAlbumOfflinePic() {
        return albumOfflinePic;
    }

    public void setAlbumOfflinePic(String albumOfflinePic) {
        this.albumOfflinePic = albumOfflinePic;
    }
}
