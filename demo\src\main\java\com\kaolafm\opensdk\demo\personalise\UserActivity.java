package com.kaolafm.opensdk.demo.personalise;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.NumberPicker;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.OnClick;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.api.personalise.PersonalizedRequest;
import com.kaolafm.opensdk.api.personalise.model.InterestTag;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/4/29
 */
public class UserActivity extends BaseActivity {

    @BindView(R.id.et_user_input_age)
    EditText mEtUserInputAge;

    @BindView(R.id.et_user_input_avatar)
    EditText mEtUserInputAvatar;

    @BindView(R.id.et_user_input_city)
    EditText mEtUserInputCity;

    @BindView(R.id.et_user_input_nickname)
    EditText mEtUserInputNickname;

    @BindView(R.id.et_user_input_phone)
    EditText mEtUserInputPhone;

    @BindView(R.id.np_user_year)
    NumberPicker mNpUserYear;

    @BindView(R.id.rb_user_gender_female)
    RadioButton mRbUserGenderFemale;

    @BindView(R.id.rb_user_gender_male)
    RadioButton mRbUserGenderMale;

    @BindView(R.id.rb_user_input_female)
    RadioButton mRbUserInputFemale;

    @BindView(R.id.rb_user_input_male)
    RadioButton mRbUserInputMale;

    @BindView(R.id.rg_user_gender)
    RadioGroup mRgUserGender;

    @BindView(R.id.rg_user_input_gender)
    RadioGroup mRgUserInputGender;

    @BindView(R.id.tv_user_gender)
    TextView mTvUserGender;

    @Override
    public int getLayoutId() {
        return R.layout.activity_user;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        setTitle("保存用户相关信息");
        mNpUserYear.setMinValue(1900);
        mNpUserYear.setMaxValue(2019);
        mNpUserYear.setValue(2019);
        mNpUserYear.setDescendantFocusability(NumberPicker.FOCUS_BLOCK_DESCENDANTS);
    }

    @Override
    public void initData() {

    }

    @OnClick({R.id.et_user_commit_info, R.id.btn_user_commit_third_info})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.et_user_commit_info:
                saveUserInfo();
                break;
            case R.id.btn_user_commit_third_info:
                saveThirdInfo();
                break;
            default:
        }
    }

    /**
     * 保存车厂信息，必须登录状态下
     */
    private void saveThirdInfo() {
        if (!AccessTokenManager.getInstance().getKaolaAccessToken().isLogin()) {
            showToast("未登录，请登录！");
            return;
        }
        Editable phoneText = mEtUserInputPhone.getText();
        String phone = null;
        if (!TextUtils.isEmpty(phoneText)) {
            phone = phoneText.toString().trim();
        }
        if (TextUtils.isEmpty(phone)) {
            showToast("请输入手机号");
            return;
        }
        Editable avatarText = mEtUserInputAvatar.getText();
        String avatar = null;
        if (!TextUtils.isEmpty(avatarText)) {
            avatar = avatarText.toString().trim();
        }
        Editable nicknameText = mEtUserInputNickname.getText();
        String nickname = null;
        if (!TextUtils.isEmpty(nicknameText)) {
            nickname = nicknameText.toString().trim();
        }
        int checkedRadioButtonId = mRgUserInputGender.getCheckedRadioButtonId();
        int gender = 0;
        if (checkedRadioButtonId == R.id.rb_user_input_female) {
            gender = 1;
        }
        int age = -1;
        try {
            age = Integer.parseInt(mEtUserInputAge.getText().toString().trim());
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        String city = mEtUserInputCity.getText().toString().trim();
        new PersonalizedRequest().saveThirdUser(phone, avatar, nickname, gender, age, city,
                new HttpCallback<Boolean>() {
                    @Override
                    public void onSuccess(Boolean aBoolean) {
                        showToast("保存车厂用户信息"+(aBoolean?"成功":"失败"));
                    }

                    @Override
                    public void onError(ApiException exception) {
                        showError("保存车厂用户信息错误", exception);
                    }
                });
    }

    /**
     * 保存用户属性
     */
    private void saveUserInfo() {
        int checkedRadioButtonId = mRgUserGender.getCheckedRadioButtonId();
        int gender = 0;
        if (checkedRadioButtonId == R.id.rb_user_gender_female) {
            gender = 1;
        }
        String year = String.valueOf(mNpUserYear.getValue());
        new PersonalizedRequest().saveUserAttribute(year, gender, new HttpCallback<List<InterestTag>>() {
            @Override
            public void onSuccess(List<InterestTag> interestTags) {
                Intent intent = new Intent(UserActivity.this, InterestActivity.class);
                intent.putParcelableArrayListExtra("interestTags", new ArrayList<>(interestTags));
                startActivity(intent);
            }

            @Override
            public void onError(ApiException exception) {
                showError("保存用户属性出错", exception);
            }
        });
    }
}
