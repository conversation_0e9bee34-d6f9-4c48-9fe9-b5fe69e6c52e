package com.kaolafm.report.event;


import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> on 2019/1/21.
 * 收听开始上报
 */

public class StartListenReportEvent extends BaseReportEventBean {
    /**
     * 1.当前通过qqapi接口获取的内容（排行榜等）单曲id为qqapi
     * 2.QQ音乐电台（一人一首招牌歌等）中单曲，上报的audioid 为 qqradio
     */
    private String audioid;
    /**
     * 1.通过api接口获得qq音乐内容，radioid为空
     * 2.qq音乐电台中单曲的radio为qq音乐电台的id
     */
    private String radioid;
    /**
     * 专辑同radioid；电台流为当前播放单曲所属专辑的id
     * qq api接口获取内容，为空
     * qq音乐电台的单曲，为空
     */
    private String albumid;

    /**
     * 播放类型	0：离线播放；1：在线播放
     */
    private String type = ReportConstants.PLAY_TYPE_ON_LINE;
    /**
     * 播放器位置	1：app播放器；2：外部播放器
     */
    private String position = ReportConstants.POSITION_INNER_APP;
    /**
     * 开机收听	0:否；1：是
     */
    private String remarks1 = "0";
    /**
     * 播放内容获取方式	1语音点播；2搜索结果选择；3其他
     */
    private String remarks2 = ReportConstants.COTENT_BY_OTHER;
    /**
     * 搜索结果追踪号	搜索服务端透传的数据
     */
    private String remarks9;
    /**
     * 首次收听
     */
    private String remarks4 = "0";
    /**
     * AI电台编排类型 AI电台编排位类型，只有radioid为AI电台时传
     */
    private String ai_mz_location;

    /**
     * 内置播放器必传，固定值：player
     */
    private String remarks6;

    /**
     * 播放来源
     */
    private String source;

    /**
     * 推荐结果追踪号
     */
    private String remarks11;
    /**
     * 1语音点播；2搜索结果选择；3其他 4断点续播 5一键收听
     */
    private String remarks12;

    /**
     * 精品 VIP 无
     */
    private String tag;
    /**
     * 0免费 1付费 2试听
     */
    private int audioid_type;
    /**
     * 广播/电视 播放状态 1：直播中；2：回放中
     */
    private String status;


    public StartListenReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_LISTEN_START);
    }

    public String getAudioid() {
        return audioid;
    }

    public void setAudioid(String audioid) {
        this.audioid = audioid;
    }

    public String getRadioid() {
        return radioid;
    }

    public void setRadioid(String radioid) {
        this.radioid = radioid;
    }

    public String getAlbumid() {
        return albumid;
    }

    public void setAlbumid(String albumid) {
        this.albumid = albumid;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getRemarks1() {
        return remarks1;
    }

    public void setRemarks1(String remarks1) {
        this.remarks1 = remarks1;
    }

    public String getRemarks2() {
        return remarks2;
    }

    public void setRemarks2(String remarks2) {
        this.remarks2 = remarks2;
    }

    public String getRemarks4() {
        return remarks4;
    }

    public void setRemarks4(String remarks4) {
        this.remarks4 = remarks4;
    }

    public String getRemarks9() {
        return remarks9;
    }

    public void setRemarks9(String remarks9) {
        this.remarks9 = remarks9;
    }

    public String getAi_mz_location() {
        return ai_mz_location;
    }

    public void setAi_mz_location(String ai_mz_location) {
        this.ai_mz_location = ai_mz_location;
    }

    public String getRemarks6() {
        return remarks6;
    }

    public void setRemarks6(String remarks6) {
        this.remarks6 = remarks6;
    }


    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getRemarks11() {
        return remarks11;
    }

    public void setRemarks11(String remarks11) {
        this.remarks11 = remarks11;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public int getAudioid_type() {
        return audioid_type;
    }

    public void setAudioid_type(int audioid_type) {
        this.audioid_type = audioid_type;
    }

    public String getRemarks12() {
        return remarks12;
    }

    public void setRemarks12(String remarks12) {
        this.remarks12 = remarks12;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}


