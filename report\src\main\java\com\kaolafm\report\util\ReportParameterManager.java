package com.kaolafm.report.util;

import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Build;

import com.google.gson.Gson;
import com.kaolafm.base.utils.NetworkUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.database.ConfigData;
import com.kaolafm.report.event.StartReportEvent;
import com.kaolafm.report.event.UpdateReportEvent;
import com.kaolafm.report.model.PlayReportParameter;
import com.kaolafm.report.model.ReportCarParameter;
import com.kaolafm.report.model.ReportParameter;
import com.kaolafm.report.model.ReportPrivateParameter;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;
import java.util.List;

import io.reactivex.Single;

import static android.content.Context.CONNECTIVITY_SERVICE;


/**
 * <AUTHOR> on 2019/1/21.
 */

public class ReportParameterManager {
    /**
     * 是否是启动后第一次收听
     */
    private String mStartFirst = "1";
    private String imsi;
    private String os;
    private String screen_height;
    private String screen_width;
    private String os_version;

    /**
     * 0：否；1：真
     */
    private String wifi;

    /**
     * -1：未知，0：无网，1：wifi，2：2g，3：3g，4：4g
     */
    private String network_type;

    /**
     * 0、未知，1、移动，2、联通，3、电信
     */
    private String carrier;

    /**
     * 屏幕状态0：横屏；1：竖屏
     */
    private String screen_direction = "0";
    private String page;
    private String model;
    private String manufacturer;
    private String lon;
    private String lat;
    private String playId = "0000";
    private String versionName;

    private String appStartType;
    /**
     * 旧版本号
     */
    private String mOldVersion;

    private ReportCarParameter mReportCarParameter;
    private ReportParameter mReportParameter;
    private ReportPrivateParameter mPrivateParameter;

    private static ReportParameterManager reportParameterManager;

    private boolean isInit = false;

    private String mRealVersionCode, mRealVersionName;
    /**
     * 升级类型,
     */
    private String mUpdateType;

    /**
     * 应用表的产品id：1-车载综合版；6-车载在线电台版
     */
    private String product_id;

    /**
     * 应用模式：0-综合版；1-在线电台版
     */
    private String app_mode;
    /**
     * 坐标类型
     */
    private String coordType;

    private ReportParameterManager() {
        mReportParameter = new ReportParameter();
        mReportCarParameter = new ReportCarParameter();
        mPrivateParameter = new ReportPrivateParameter();
    }

    public static ReportParameterManager getInstance() {
        if (reportParameterManager == null) {
            synchronized (ReportParameterManager.class) {
                if (reportParameterManager == null) {
                    reportParameterManager = new ReportParameterManager();
                }
            }
        }
        return reportParameterManager;
    }

    public void init(ReportParameter reportParameter, PlayReportManager playReportManager) {
        mReportParameter = reportParameter;
        initOther();
        initDBRecord(playReportManager);
    }

    private void initDBRecord(PlayReportManager playReportManager) {
        Single<List<ConfigData>> single = ReportHelper.getInstance().readDB();
        if (single == null) {
            return;
        }
        single.subscribe(configData -> {
            initDBRecord(configData, playReportManager);
        }, throwable -> {
            initDBRecord(null, playReportManager);
        });
    }

    private void initDBRecord(List<ConfigData> configDataList, PlayReportManager playReportManager) {
        PlayReportParameter playReportParameter = null;
        if (configDataList != null) {
            int size = configDataList.size();
            for (int i = 0; i < size; i++) {
                ConfigData configData = configDataList.get(i);
                if (configData == null) {
                    continue;
                }
                if (configData.getType() == ConfigConstant.TYPE_REPORT_CAR_PARAMETER) {
                    try {
                        ReportCarParameter reportCarParameter = new Gson().fromJson(configData.getJson(), ReportCarParameter.class);
                        if (reportCarParameter != null) {
                            mReportCarParameter = reportCarParameter;
                        }
                    } catch (Exception e) {

                    }
                }
                if (configData.getType() == ConfigConstant.TYPE_REPORT_PRIVATE_PARAMETER) {
                    try {
                        mPrivateParameter = new Gson().fromJson(configData.getJson(), ReportPrivateParameter.class);
                    } catch (Exception e) {

                    }
                }
                if (configData.getType() == ConfigConstant.TYPE_PLAY_PARAMETER) {
                    try {
                        playReportParameter = new Gson().fromJson(configData.getJson(), PlayReportParameter.class);
                    } catch (Exception e) {

                    }
                }
            }
        }
        int sessionId = mPrivateParameter.getmSessionId();
        mPrivateParameter.setmSessionId(++sessionId);
        initUpdate();
        initAppStart();
        playReportManager.initPlayReportParameter(playReportParameter);
        isInit = true;
        InitUtil.initCarInfo();
    }

    public void initOther() {
        os = ReportConstants.VALUE_OS_ANDROID;
        os_version = ReportParameterUtil.getOsVersion();
        imsi = ReportParameterUtil.getIMSI(ReportHelper.getInstance().getContext());
        manufacturer = Build.MANUFACTURER;
        screen_height = ReportParameterUtil.getScreenHeight();
        screen_width = ReportParameterUtil.getScreenWidth();
        carrier = ReportParameterUtil.getOperator(ReportHelper.getInstance().getContext());
        if (ReportHelper.getInstance().isUseBySDK) {
            versionName = ReportParameterUtil.getVersionName();
        }
        screen_direction = ReportParameterUtil.getConfiguration();
        initNetwork();
        initModel();
    }

    public void initNetwork() {
        if (NetworkUtil.isWifiNetworkAvailable(ReportHelper.getInstance().getContext())) {
            wifi = "1";
        } else {
            wifi = "0";
        }
        network_type = String.valueOf(NetworkUtil.getNetwork(ReportHelper.getInstance().getContext()));
    }

    public String getIP() {
        return NetworkUtil.getIPAddress(ReportHelper.getInstance().getContext());
    }

    public int getmSessionId() {
        if (mPrivateParameter != null) {
            return mPrivateParameter.getmSessionId();
        }
        return 1;
    }


    public int getTimer() {
        if (mReportCarParameter != null) {
            return mReportCarParameter.getTimer();
        }
        return ReportConstants.READ_DATE_BASE_TIMER;
    }

    public String getProduct_id() {
        return product_id;
    }

    public void setProduct_id(String product_id) {
        this.product_id = product_id;
    }

    public String getApp_mode() {
        return app_mode;
    }

    public void setApp_mode(String app_mode) {
        this.app_mode = app_mode;
    }

    public String getImsi() {
        return imsi;
    }

    public void setImsi(String imsi) {
        this.imsi = imsi;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public String getScreen_height() {
        return screen_height;
    }

    public void setScreen_height(String screen_height) {
        this.screen_height = screen_height;
    }

    public String getScreen_width() {
        return screen_width;
    }

    public void setScreen_width(String screen_width) {
        this.screen_width = screen_width;
    }

    public String getOs_version() {
        return os_version;
    }

    public void setOs_version(String os_version) {
        this.os_version = os_version;
    }

    public String getApp_version() {
        if (!StringUtil.isEmpty(mRealVersionCode)) {
            return mRealVersionCode;
        }
        if (mPrivateParameter != null && !StringUtil.isEmpty(mPrivateParameter.getApp_version())) {
            return mPrivateParameter.getApp_version();
        }
        return ReportParameterUtil.getVersionCode();
    }


    public String getWifi() {
        return wifi;
    }

    public String getNetwork_type() {
        return network_type;
    }

    public String getCarrier() {
        return carrier;
    }

    public void setCarrier(String carrier) {
        this.carrier = carrier;
    }


    public String getScreen_direction() {
        return screen_direction;
    }

    public void setScreen_direction(String screen_direction) {
        this.screen_direction = screen_direction;
    }

    public String getPage() {
        return page;
    }

    public void setPage(String page) {
        this.page = page;
    }

    public ReportParameter getReportParameter() {
        return mReportParameter;
    }

    public void setReportParameter(ReportParameter reportParameter) {
        this.mReportParameter = reportParameter;
    }

    public void setWifi(String wifi) {
        this.wifi = wifi;
    }

    public void setNetwork_type(String network_type) {
        this.network_type = network_type;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public ReportCarParameter getReportCarParameter() {
        return mReportCarParameter;
    }


    public void setReportCarParameter(ReportCarParameter reportCarParameter) {
        this.mReportCarParameter = reportCarParameter;
        saveCarParameter();
    }

    public String getLon() {
        return lon;
    }

    public void setLon(String lon) {
        this.lon = lon;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getCoordType() {
        return coordType;
    }

    public void setCoordType(String coordType) {
        this.coordType = coordType;
    }

    public long getActionId() {
        if (mPrivateParameter != null) {
            long action = mPrivateParameter.getAction_id();
            savePrivateParameter();
            return action;
        }
        return 0;
    }

    public boolean isFirstStart() {
        if (mPrivateParameter != null) {
            return mPrivateParameter.getmSessionId() <= 1;
        }
        return false;
    }


    private void initModel() {
        model = Build.MODEL;
    }

    public String getModel() {
        return model;
    }


    public String getStartFirst() {
        String startFirst = mStartFirst;
        mStartFirst = "0";
        return startFirst;
    }


    /**
     * 初始化升级逻辑
     */
    public void initUpdate() {
        String app_version = ReportParameterUtil.getVersionCode();
        if (ReportHelper.getInstance().isUseBySDK) {
            mPrivateParameter.setApp_version(app_version);
        } else {
            if (!StringUtil.isEmpty(mRealVersionCode)) {
                initUpdateEvent(mUpdateType);
            }
        }
        savePrivateParameter();
    }

    /**
     * 原来的升级上报逻辑是内部逻辑.
     * 现在的升级上报需要外部传入升级类型.所以提供对外方法
     */
    public void initUpdateEvent(String type) {
        Logging.d(ReportConstants.REPORT_TAG, "初始化升级事件: " + type);
        mUpdateType = type;
        if (mPrivateParameter == null) {
            return;
        }
        mOldVersion = mPrivateParameter.getApp_version();
        Logging.d(ReportConstants.REPORT_TAG, "现在版本号: " + mRealVersionCode + " 旧版本号: " + mOldVersion);
        if (isNeedReportUpdate(mRealVersionCode)) {
            UpdateReportEvent reportEvent = new UpdateReportEvent();
            reportEvent.setRemarks2(mRealVersionCode);
            reportEvent.setRemarks1(mOldVersion);
            reportEvent.setType(type);
            ReportHelper.getInstance().addEvent(reportEvent, false);
        }
        mPrivateParameter.setApp_version(mRealVersionCode);
        mPrivateParameter.setAppVersionName(mRealVersionName);
        savePrivateParameter();
    }

    private boolean isNeedReportUpdate(String app_version) {
        if (mPrivateParameter == null || StringUtil.isEmpty(mOldVersion)) {
            return false;
        }
        if (StringUtil.isEmpty(mPrivateParameter.getApp_version())) {
            mPrivateParameter.setApp_version(app_version);
            return false;
        }
        if (!StringUtil.isEmpty(app_version) && !mOldVersion.equals(app_version)) {
            return true;
        }
        return false;
    }


    public String getPlayId() {
        return playId;
    }

    public void setPlayId(String playId) {
        this.playId = playId;
    }

    public void setUid(String uid) {
        if (mReportParameter != null) {
            mReportParameter.setUid(uid);
        }
    }

    private void savePrivateParameter() {
        ReportHelper.getInstance().savePrivateParameter(mPrivateParameter);
    }

    private void saveCarParameter() {
        ReportHelper.getInstance().saveCarParameter(mReportCarParameter);
    }

    public String getAppStartType() {
        return appStartType;
    }

    // 直接添加启动事件，不判断isUseBySDK
    public void forceSetAppStartType(String appStartType) {
        this.appStartType = appStartType;
        if (isInit && !StringUtil.isEmpty(appStartType) && ReportHelper.getInstance().isInitSuccess) {
            StartReportEvent startReportEvent = new StartReportEvent();
            startReportEvent.setType(appStartType);
            ReportHelper.getInstance().addEvent(startReportEvent, false);
        }
    }

    public void setAppStartType(String appStartType) {
        this.appStartType = appStartType;
        if (isInit) {
            initAppStart();
        }
    }

    public void initAppStart() {
        //新需求，sdk也需要上报app的启动事件，因此此处不需要判断是否是sdk
//        if (ReportHelper.getInstance().isUseBySDK) {
//            return;
//        }
        if (StringUtil.isEmpty(appStartType)) {
            return;
        }
        StartReportEvent startReportEvent = new StartReportEvent();
        startReportEvent.setType(appStartType);
        ReportHelper.getInstance().addEvent(startReportEvent, false);
    }

    public boolean isFirstListen() {
        if (mPrivateParameter != null) {
            boolean isFirstListen = mPrivateParameter.isFirstListen();
            if (isFirstListen) {
                mPrivateParameter.setFirstListen(false);
                savePrivateParameter();
            }
            return isFirstListen;
        }
        return false;
    }

    public String getVersionName() {
        if (ReportHelper.getInstance().isUseBySDK) {
            return versionName;
        }
        if (!StringUtil.isEmpty(mRealVersionName)) {
            return mRealVersionName;
        }
        if (mPrivateParameter != null) {
            return mPrivateParameter.getAppVersionName();
        }
        return "";
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public void setCarType(String carType) {
        if (mPrivateParameter != null) {
            mPrivateParameter.setCarType(carType);
        }
    }

    public String getCarType() {
        if (mPrivateParameter != null) {
            return mPrivateParameter.getCarType();
        }
        return null;
    }

    public void setRealVersion(String realVersionName, String realVersionCode) {
        Logging.d(ReportConstants.REPORT_TAG, "设置真实版本: " + realVersionName + " , 版本号: " + realVersionCode);
        mRealVersionCode = realVersionCode;
        mRealVersionName = realVersionName;
    }
}
