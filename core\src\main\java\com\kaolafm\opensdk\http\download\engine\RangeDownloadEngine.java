package com.kaolafm.opensdk.http.download.engine;

import android.util.Pair;

import com.kaolafm.base.utils.FileUtil;
import com.kaolafm.opensdk.http.download.DownloadProgress;
import com.kaolafm.opensdk.http.download.DownloadRequest;
import com.kaolafm.opensdk.utils.HttpUtil;

import org.reactivestreams.Publisher;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.util.ArrayList;
import java.util.List;

import io.reactivex.Flowable;
import io.reactivex.functions.Function;
import okhttp3.ResponseBody;
import retrofit2.Response;

import static java.nio.channels.FileChannel.MapMode.READ_WRITE;

/**
 * <AUTHOR>
 * @date 2020-02-11
 */
public class RangeDownloadEngine extends BaseDownloadEngine {

    private DownloadRequest mRequest;
    private File mTmp;
    private Response<ResponseBody> mResponse;
    private RangeTempHelper mRangeTempHelper;
    private RealDownloadRequest mDownloadRequest;

    RangeDownloadEngine(RealDownloadRequest downloadRequest) {
        mDownloadRequest = downloadRequest;
    }

    @Override
    protected void beforeDownload(DownloadRequest request, Response<ResponseBody> response) {
        this.mRequest = request;
        this.mResponse = response;
        super.beforeDownload(request, response);
    }

    @Override
    protected void recreate() {
        createFiles();
    }

    @Override
    protected void create() {
        mTmp = mRequest.getTmp();
        if (shadow.exists() && mTmp.exists()) {
            mRangeTempHelper = new RangeTempHelper(mTmp);
            if (!mRangeTempHelper.read(mResponse, mRequest)) {
                createFiles();
            }
        } else {
            createFiles();
        }
    }

    private void createFiles() {
        mTmp = mRequest.getTmp();
        FileUtil.recreate(mTmp);
        FileUtil.recreate(shadow, HttpUtil.getContentLength(mResponse));
        mRangeTempHelper = new RangeTempHelper(mTmp);
        mRangeTempHelper.write(mResponse, mRequest);
    }

    @Override
    Flowable<DownloadProgress> startDownload(DownloadRequest request, Response<ResponseBody> response) {
        String url = HttpUtil.getUrl(response);
        Pair<Long, Long> lastProgress = mRangeTempHelper.lastProgress();
        DownloadProgress progress = new DownloadProgress(lastProgress.first, lastProgress.second);
        List<RangeTempHelper.Segment> segments = mRangeTempHelper.getSegments();
        ArrayList<Flowable<Long>> sources = new ArrayList<>();
        for (RangeTempHelper.Segment segment : segments) {
            if (!segment.isComplete()) {
                sources.add(download(url, segment));
            }
        }
        return Flowable.mergeDelayError(sources, 3).
                map(progress::increaseSize)
                .doOnComplete(() -> {
                    shadow.renameTo(file);
                    mTmp.delete();
                    FileUtil.closeIO(response.body());
                });
    }

    private Flowable<Long> download(String url, RangeTempHelper.Segment segment) {
        return mDownloadRequest.download(segment.current, segment.end, url)
                .flatMap((Function<Response<ResponseBody>, Publisher<Long>>) response -> saveRange(segment, response));
    }

    private Flowable<Long> saveRange(RangeTempHelper.Segment segment, Response<ResponseBody> response) {
        ResponseBody body = response.body();
        if (body == null) {
            throw new RuntimeException("ResponseBody is Null");
        }
        return Flowable.generate(() -> createBuffer(segment, body),
                (buffer, emitter) -> {
                    try {
                        byte[] bytes = new byte[8 * 1024];
                        int len = buffer.source.read(bytes);
                        if (len == -1) {
                            emitter.onComplete();
                        } else {
                            buffer.shadowBuffer.put(bytes, 0, len);
                            buffer.current += len;
                            buffer.tempBuffer.putLong(16, buffer.current);
                            emitter.onNext((long) len);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }, Buffer::close);
    }

    private Buffer createBuffer(RangeTempHelper.Segment segment, ResponseBody body) {
        InputStream source = body.byteStream();
        FileChannel tempChannel = FileUtil.getChannel(mTmp);
        FileChannel shadowChannel = FileUtil.getChannel(shadow);
        try {
            MappedByteBuffer tempBuffer = tempChannel.map(READ_WRITE, segment.startByte(), RangeTempHelper.Segment.SEGMENT_SIZE);
            MappedByteBuffer shadowBuffer = shadowChannel.map(READ_WRITE, segment.current, segment.remainSize());
            return new Buffer(source, shadowChannel, tempChannel, shadowBuffer, tempBuffer, segment.current);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    private class Buffer {
        private InputStream source;
        private FileChannel shadowChannel;
        private FileChannel tempChannel;
        private MappedByteBuffer shadowBuffer;
        private MappedByteBuffer tempBuffer;
        private long current = 0;

        public Buffer(InputStream source, FileChannel shadowChannel, FileChannel tempChannel, MappedByteBuffer shadowBuffer, MappedByteBuffer tempBuffer, long current) {
            this.source = source;
            this.shadowChannel = shadowChannel;
            this.tempChannel = tempChannel;
            this.shadowBuffer = shadowBuffer;
            this.tempBuffer = tempBuffer;
            this.current = current;
        }

        public void close() {
            FileUtil.closeIO(tempChannel, shadowChannel, source);
        }
    }
}
