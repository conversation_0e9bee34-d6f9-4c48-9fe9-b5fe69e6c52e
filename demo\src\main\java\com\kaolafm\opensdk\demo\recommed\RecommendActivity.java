package com.kaolafm.opensdk.demo.recommed;

import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.widget.EditText;
import android.widget.TextView;

import com.google.gson.Gson;
import com.kaolafm.opensdk.api.recommend.RecRequest;
import com.kaolafm.opensdk.api.recommend.model.BaseSceneListData;
import com.kaolafm.opensdk.api.recommend.model.SceneDataList;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RecommendActivity extends BaseActivity {

    public static final String TAG = "RecommendActivity";

    public EditText resultEt;

    @Override
    public int getLayoutId() {
        return R.layout.activity_recommend;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        resultEt = findViewById(R.id.et_result);

        findViewById(R.id.btn_code_recommend).setOnClickListener(view -> {
            String code = ((EditText) findViewById(R.id.et_code)).getText().toString();
            if (TextUtils.isEmpty(code)) {
                code = "1061";
            }
            doRequestCodeRecommend(code);
        });

        findViewById(R.id.btn_present_recommend).setOnClickListener(view -> {
            String carType = ((EditText) findViewById(R.id.et_car_type)).getText().toString();
            String time = ((EditText) findViewById(R.id.et_time)).getText().toString();
            String age = ((EditText) findViewById(R.id.et_age)).getText().toString();
            String weather = ((EditText) findViewById(R.id.et_weather)).getText().toString();
            String speed = ((EditText) findViewById(R.id.et_speed)).getText().toString();
            String sex = ((EditText) findViewById(R.id.et_sex)).getText().toString();
            String special = ((EditText) findViewById(R.id.et_special)).getText().toString();
            doRequestPresentRecommend(carType, time, sex, age, weather, speed, special);
        });
    }

    @Override
    public void initData() {

    }

    private void doRequestCodeRecommend(String code) {
        new RecRequest().getSceneRadioList(code, "10", null, new HttpCallback<BaseSceneListData<List<SceneDataList>>>() {
            @Override
            public void onSuccess(BaseSceneListData<List<SceneDataList>> listBaseSceneListData) {
                Log.i(TAG, "success: " + listBaseSceneListData.toString());
                try {
                    Gson gson = new Gson();
                    String json = gson.toJson(listBaseSceneListData);
                    resultEt.setText(json);
                } catch (Exception e) {
                    resultEt.setText("GSON:" + e.toString());
                }
            }

            @Override
            public void onError(ApiException e) {
                Log.i(TAG, "error: " + e.toString());
                resultEt.setText(e.toString());
            }
        });
    }

    private void doRequestPresentRecommend(String carTypeCode
            , String timeCode
            , String sexCode
            , String ageCode
            , String weatherCode
            , String speedCode
            , String specialScenesCode) {
        new RecRequest().getPresentRadioList(carTypeCode, timeCode, sexCode, ageCode, weatherCode, speedCode, specialScenesCode
                , "10", null, new HttpCallback<BaseSceneListData<List<SceneDataList>>>() {
                    @Override
                    public void onSuccess(BaseSceneListData<List<SceneDataList>> listBaseSceneListData) {
                        Log.i(TAG, "success: " + listBaseSceneListData.toString());
                        try {
                            Gson gson = new Gson();
                            String json = gson.toJson(listBaseSceneListData);
                            resultEt.setText(json);
                        } catch (Exception e) {
                            resultEt.setText("GSON:" + e.toString());
                        }
                    }

                    @Override
                    public void onError(ApiException e) {
                        Log.i(TAG, "error: " + e.toString());
                        resultEt.setText(e.toString());
                    }
                });
    }
}