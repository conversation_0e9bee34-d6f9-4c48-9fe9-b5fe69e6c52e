com.kaolafm.ad.AdConstant -> com.kaolafm.ad.a:
    java.lang.String AD_ENGINE_HOST -> a
    java.lang.String AD_ENGINE_HOST_HTTP -> b
    java.lang.String AD_ENGINE_VERSION -> c
    java.lang.String AD_SOCKET_HOST -> d
    java.lang.String AD_SOCKET_HOST_HTTP -> e
    java.lang.String AD_REPROT_HOST -> f
    java.lang.String AD_REPROT_HOST_HTTP -> g
    java.lang.String DOMAIN_NAME_AD_REPORT -> h
    java.lang.String DOMAIN_HEADER_AD_REPORT -> i
    java.lang.String DOMAIN_NAME_AD -> j
    java.lang.String DOMAIN_HEADER_AD -> k
    java.lang.String DOMAIN_NAME_AD_SOCKET -> l
    java.lang.String ENGINE -> m
    java.lang.String SOCKET_ENGINE_URL -> n
    java.lang.String REPORT_DISPLAY -> o
    java.lang.String REPORT_PLAY -> p
    java.lang.String REPORT_CLICK -> q
    java.lang.String REPORT_ERROR -> r
    java.lang.String REPORT_ACTIVE -> s
    java.lang.String REPORT_SKIP -> t
    java.lang.String REPORT_DISPLAY_END -> u
    java.lang.String REPORT_DISPLAY_INTERRUPT -> v
    java.lang.String REPORT_MORE_INTERACTION_DISPLAY -> w
    java.lang.String REPORT_MORE_INTERACTION_DISPLAY_END -> x
    int TYPE_TIMED_ADVERT -> y
    java.lang.String KEY_EXTRA_MONITOR_TYPE -> z
    java.lang.String KEY_EXTRA_CLICK_MONITOR_URL -> A
    java.lang.String KEY_EXTRA_PV_MONITOR_URL -> B
    java.lang.String KEY_EXTRA_REPORT -> C
    java.lang.String TIMED_ADVERT_EVENT -> D
    java.lang.String SP_NAME -> E
    9:9:void <init>() -> <init>
com.kaolafm.ad.AdvertOptions -> com.kaolafm.ad.b:
    com.kaolafm.ad.AdvertOptions DEFAULT -> a
    java.lang.String brand -> b
    int deviceType -> c
    25:26:void <init>() -> <init>
    29:29:java.lang.String brand() -> a
    33:33:int deviceType() -> b
    37:40:void <init>(com.kaolafm.ad.AdvertOptions$Builder) -> <init>
    13:13:void <init>(com.kaolafm.ad.AdvertOptions$Builder,byte) -> <init>
    15:15:void <clinit>() -> <clinit>
com.kaolafm.ad.AdvertOptions$1 -> com.kaolafm.ad.c:
com.kaolafm.ad.AdvertOptions$Builder -> com.kaolafm.ad.b$a:
    java.lang.String brand -> a
    int deviceType -> b
    42:42:void <init>() -> <init>
    56:57:com.kaolafm.ad.AdvertOptions$Builder brand(java.lang.String) -> a
    64:65:com.kaolafm.ad.AdvertOptions$Builder deviceType(int) -> a
    70:71:com.kaolafm.ad.AdvertOptions$Builder versionName(java.lang.String) -> b
    76:77:com.kaolafm.ad.AdvertOptions$Builder httpTimeout(long) -> a
    82:83:com.kaolafm.ad.AdvertOptions$Builder interceptor(okhttp3.Interceptor) -> a
    88:89:com.kaolafm.ad.AdvertOptions$Builder carType(java.lang.String) -> c
    94:95:com.kaolafm.ad.AdvertOptions$Builder useHttps(com.kaolafm.opensdk.utils.BaseHttpsStrategy) -> a
    100:100:com.kaolafm.ad.AdvertOptions build() -> a
    42:42:com.kaolafm.opensdk.Options build() -> b
    42:42:com.kaolafm.opensdk.Options$Builder useHttps(com.kaolafm.opensdk.utils.BaseHttpsStrategy) -> b
    1094:1095:com.kaolafm.ad.AdvertOptions$Builder useHttps(com.kaolafm.opensdk.utils.BaseHttpsStrategy):94:95 -> b
    1094:1095:com.kaolafm.opensdk.Options$Builder useHttps(com.kaolafm.opensdk.utils.BaseHttpsStrategy):42 -> b
    42:42:com.kaolafm.opensdk.Options$Builder carType(java.lang.String) -> d
    2088:2089:com.kaolafm.ad.AdvertOptions$Builder carType(java.lang.String):88:89 -> d
    2088:2089:com.kaolafm.opensdk.Options$Builder carType(java.lang.String):42 -> d
    42:42:com.kaolafm.opensdk.Options$Builder interceptor(okhttp3.Interceptor) -> b
    3082:3083:com.kaolafm.ad.AdvertOptions$Builder interceptor(okhttp3.Interceptor):82:83 -> b
    3082:3083:com.kaolafm.opensdk.Options$Builder interceptor(okhttp3.Interceptor):42 -> b
    42:42:com.kaolafm.opensdk.Options$Builder httpTimeout(long) -> b
    4076:4077:com.kaolafm.ad.AdvertOptions$Builder httpTimeout(long):76:77 -> b
    4076:4077:com.kaolafm.opensdk.Options$Builder httpTimeout(long):42 -> b
    42:42:com.kaolafm.opensdk.Options$Builder versionName(java.lang.String) -> e
    5070:5071:com.kaolafm.ad.AdvertOptions$Builder versionName(java.lang.String):70:71 -> e
    5070:5071:com.kaolafm.opensdk.Options$Builder versionName(java.lang.String):42 -> e
    42:42:java.lang.String access$000(com.kaolafm.ad.AdvertOptions$Builder) -> a
    42:42:int access$100(com.kaolafm.ad.AdvertOptions$Builder) -> b
com.kaolafm.ad.Advertisement -> com.kaolafm.ad.Advertisement:
    android.app.Application context -> context
    com.kaolafm.opensdk.Engine engine -> engine
    18:20:void <init>() -> <init>
    23:25:void init(android.app.Application) -> init
    28:28:android.app.Application getApplication() -> getApplication
    32:33:void release() -> release
    16:16:void <clinit>() -> <clinit>
com.kaolafm.ad.AdvertisingEngine -> com.kaolafm.ad.d:
    14:14:void <init>() -> <init>
    18:20:void internalInit(android.app.Application,com.kaolafm.ad.AdvertOptions,com.kaolafm.opensdk.http.core.HttpCallback) -> a
    14:14:void internalInit(android.app.Application,com.kaolafm.opensdk.Options,com.kaolafm.opensdk.http.core.HttpCallback) -> a
com.kaolafm.ad.AdvertisingEngine_MembersInjector -> com.kaolafm.ad.e:
    javax.inject.Provider mProfileManagerProvider -> a
    javax.inject.Provider reportRequestLazyProvider -> b
    18:21:void <init>(javax.inject.Provider,javax.inject.Provider) -> <init>
    26:26:dagger.MembersInjector create(javax.inject.Provider,javax.inject.Provider) -> a
    32:35:void injectMembers(com.kaolafm.ad.AdvertisingEngine) -> a
    1040:1041:void com.kaolafm.ad.AdvertisingInternalEngine_MembersInjector.injectReportRequestLazy(com.kaolafm.ad.AdvertisingInternalEngine,dagger.Lazy):40:41 -> a
    1040:1041:void injectMembers(com.kaolafm.ad.AdvertisingEngine):33 -> a
    11:11:void injectMembers(java.lang.Object) -> a
    2032:2035:void injectMembers(com.kaolafm.ad.AdvertisingEngine):32:35 -> a
    2032:2035:void injectMembers(java.lang.Object):11 -> a
    2040:2041:void com.kaolafm.ad.AdvertisingInternalEngine_MembersInjector.injectReportRequestLazy(com.kaolafm.ad.AdvertisingInternalEngine,dagger.Lazy):40:41 -> a
    2040:2041:void injectMembers(com.kaolafm.ad.AdvertisingEngine):33 -> a
    2040:2041:void injectMembers(java.lang.Object):11 -> a
com.kaolafm.ad.AdvertisingInternalEngine -> com.kaolafm.ad.f:
    java.lang.String ADVERT_ACTIVATE -> b
    dagger.Lazy reportRequestLazy -> a
    36:37:void <init>() -> <init>
    41:45:void internalInit(android.app.Application,com.kaolafm.ad.AdvertOptions,com.kaolafm.opensdk.http.core.HttpCallback) -> a
    49:65:void internalActivate(com.kaolafm.opensdk.http.core.HttpCallback) -> a
    69:69:boolean isActivated() -> b
    74:77:void config(android.app.Application,com.kaolafm.ad.AdvertOptions,com.kaolafm.opensdk.http.core.HttpCallback) -> b
    80:81:void saveActivation() -> c
    85:86:void release() -> d
    90:92:void setLocation(java.lang.String,java.lang.String) -> a
    94:95:void setBrand(java.lang.String) -> a
    26:26:void internalInit(android.app.Application,com.kaolafm.opensdk.Options,com.kaolafm.opensdk.http.core.HttpCallback) -> a
    26:26:void config(android.app.Application,com.kaolafm.opensdk.Options,com.kaolafm.opensdk.http.core.HttpCallback) -> b
    1074:1077:void config(android.app.Application,com.kaolafm.ad.AdvertOptions,com.kaolafm.opensdk.http.core.HttpCallback):74:77 -> b
    1074:1077:void config(android.app.Application,com.kaolafm.opensdk.Options,com.kaolafm.opensdk.http.core.HttpCallback):26 -> b
    27:27:void access$000$1243df1b() -> a
    1080:1081:void saveActivation():80:81 -> a
    1080:1081:void access$000$1243df1b():27 -> a
com.kaolafm.ad.AdvertisingInternalEngine$1 -> com.kaolafm.ad.g:
    com.kaolafm.opensdk.http.core.HttpCallback val$callback -> a
    com.kaolafm.ad.AdvertisingInternalEngine this$0 -> b
    49:49:void <init>(com.kaolafm.ad.AdvertisingInternalEngine,com.kaolafm.opensdk.http.core.HttpCallback) -> <init>
    52:56:void onSuccess$171db248() -> a
    60:63:void onError(com.kaolafm.opensdk.http.error.ApiException) -> a
    49:49:void onSuccess$5d527811() -> b
    1052:1056:void onSuccess$171db248():52:56 -> b
    1052:1056:void onSuccess$5d527811():49 -> b
com.kaolafm.ad.AdvertisingInternalEngine_Factory -> com.kaolafm.ad.h:
    javax.inject.Provider mProfileManagerProvider -> a
    javax.inject.Provider reportRequestLazyProvider -> b
    18:21:void <init>(javax.inject.Provider,javax.inject.Provider) -> <init>
    25:25:com.kaolafm.ad.AdvertisingInternalEngine get() -> get
    1031:1035:com.kaolafm.ad.AdvertisingInternalEngine provideInstance(javax.inject.Provider,javax.inject.Provider):31:35 -> get
    1031:1035:com.kaolafm.ad.AdvertisingInternalEngine get():25 -> get
    1040:1041:void com.kaolafm.ad.AdvertisingInternalEngine_MembersInjector.injectReportRequestLazy(com.kaolafm.ad.AdvertisingInternalEngine,dagger.Lazy):40:41 -> get
    1040:1041:com.kaolafm.ad.AdvertisingInternalEngine provideInstance(javax.inject.Provider,javax.inject.Provider):33 -> get
    1040:1041:com.kaolafm.ad.AdvertisingInternalEngine get():25 -> get
    31:35:com.kaolafm.ad.AdvertisingInternalEngine provideInstance(javax.inject.Provider,javax.inject.Provider) -> a
    2040:2041:void com.kaolafm.ad.AdvertisingInternalEngine_MembersInjector.injectReportRequestLazy(com.kaolafm.ad.AdvertisingInternalEngine,dagger.Lazy):40:41 -> a
    2040:2041:com.kaolafm.ad.AdvertisingInternalEngine provideInstance(javax.inject.Provider,javax.inject.Provider):33 -> a
    41:41:com.kaolafm.ad.AdvertisingInternalEngine_Factory create(javax.inject.Provider,javax.inject.Provider) -> create
    46:46:com.kaolafm.ad.AdvertisingInternalEngine newAdvertisingInternalEngine() -> a
    11:11:java.lang.Object get() -> get
com.kaolafm.ad.AdvertisingInternalEngine_MembersInjector -> com.kaolafm.ad.i:
    javax.inject.Provider mProfileManagerProvider -> a
    javax.inject.Provider reportRequestLazyProvider -> b
    20:23:void <init>(javax.inject.Provider,javax.inject.Provider) -> <init>
    28:28:dagger.MembersInjector create(javax.inject.Provider,javax.inject.Provider) -> a
    34:36:void injectMembers(com.kaolafm.ad.AdvertisingInternalEngine) -> a
    1040:1041:void injectReportRequestLazy(com.kaolafm.ad.AdvertisingInternalEngine,dagger.Lazy):40:41 -> a
    1040:1041:void injectMembers(com.kaolafm.ad.AdvertisingInternalEngine):35 -> a
    40:41:void injectReportRequestLazy(com.kaolafm.ad.AdvertisingInternalEngine,dagger.Lazy) -> a
    12:12:void injectMembers(java.lang.Object) -> a
    2034:2036:void injectMembers(com.kaolafm.ad.AdvertisingInternalEngine):34:36 -> a
    2034:2036:void injectMembers(java.lang.Object):12 -> a
    2040:2041:void injectReportRequestLazy(com.kaolafm.ad.AdvertisingInternalEngine,dagger.Lazy):40:41 -> a
    2040:2041:void injectMembers(com.kaolafm.ad.AdvertisingInternalEngine):35 -> a
    2040:2041:void injectMembers(java.lang.Object):12 -> a
com.kaolafm.ad.api.AdvertisingRequest -> com.kaolafm.ad.api.AdvertisingRequest:
    com.kaolafm.ad.api.internal.AdInternalRequest request -> a
    21:23:void <init>() -> <init>
    39:40:void getAdvertisingList(java.lang.String,java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> getAdvertisingList
    49:50:void getAudioAdvertList(java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> getAudioAdvertList
    1228:1229:void com.kaolafm.ad.api.internal.AdInternalRequest.getAudioAdvertList(java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback):228:229 -> getAudioAdvertList
    1228:1229:void getAudioAdvertList(java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback):49 -> getAudioAdvertList
    63:64:void getImageAdvertList(java.lang.String,java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> getImageAdvertList
    77:78:void getAudioImageAdvertList(java.lang.String,java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> getAudioImageAdvertList
com.kaolafm.ad.api.internal.AdInternalRequest -> com.kaolafm.ad.api.internal.a:
    com.kaolafm.ad.api.internal.AdInternalService mAdInternalService -> d
    dagger.Lazy reportRequestLazy -> a
    java.util.Map mCommonParams -> b
    java.lang.String mSocketHost -> c
    com.kaolafm.opensdk.http.socket.SocketListener mListener -> e
    69:73:void <init>() -> <init>
    102:114:void getAdvertisingList(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,io.reactivex.functions.Function,com.kaolafm.opensdk.http.core.HttpCallback) -> a
    123:135:void getAdvertisingList(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,io.reactivex.functions.Predicate,com.kaolafm.opensdk.http.core.HttpCallback) -> a
    143:144:void getAdvertList(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> a
    153:153:int getCarousel(java.lang.String) -> a
    182:183:void getAdvertisingList(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> b
    192:198:java.util.List filterMap(com.kaolafm.ad.api.internal.model.AdvertisingResult) -> a
    218:219:void getAudioAdvertList$1dee3d5b(java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> a
    228:229:void getAudioAdvertList(java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> b
    251:252:void getImageAdvertList$3732ac11(java.lang.String,java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> c
    265:266:void getImageAdvertList(java.lang.String,java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> a
    1251:1252:void getImageAdvertList$3732ac11(java.lang.String,java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback):251:252 -> a
    1251:1252:void getImageAdvertList(java.lang.String,java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback):265 -> a
    269:270:void getImageAdvertList(java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> c
    296:297:void getAudioImageAdvertList(java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> a
    300:301:void getAudioImageAdvertList(java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> a
    314:315:void getAudioImageAdvertList(java.lang.String,java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> b
    318:319:void getAudioImageAdvertList(java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> d
    328:351:void saveReportParams(com.kaolafm.ad.api.internal.model.AdvertisingResult,com.kaolafm.ad.api.internal.model.AdCreative) -> b
    361:364:void reportError(java.lang.String,com.kaolafm.opensdk.api.BaseResult) -> a
    372:403:void getTimedAdvertList$2be33d4d(java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.kaolafm.opensdk.http.socket.SocketListener) -> a
    406:406:java.util.HashMap getParamMap(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String) -> a
    425:426:void stopSocket() -> a
    296:296:boolean lambda$getAudioImageAdvertList$6(com.kaolafm.ad.api.model.Advert) -> a
    251:251:boolean lambda$getImageAdvertList$5(com.kaolafm.ad.api.model.Advert) -> b
    218:218:boolean lambda$getAudioAdvertList$4(com.kaolafm.ad.api.model.Advert) -> c
    143:143:boolean lambda$getAdvertList$3(com.kaolafm.ad.api.model.Advert) -> d
    124:133:java.util.List lambda$getAdvertisingList$2(io.reactivex.functions.Predicate,com.kaolafm.ad.api.internal.model.AdvertisingResult) -> a
    108:112:java.lang.Object lambda$getAdvertisingList$1(io.reactivex.functions.Function,com.kaolafm.opensdk.api.BaseResult) -> a
    104:105:com.kaolafm.opensdk.api.BaseResult lambda$getAdvertisingList$0(java.lang.String,com.kaolafm.opensdk.api.BaseResult) -> b
    1361:1364:void reportError(java.lang.String,com.kaolafm.opensdk.api.BaseResult):361:364 -> b
    1361:1364:com.kaolafm.opensdk.api.BaseResult lambda$getAdvertisingList$0(java.lang.String,com.kaolafm.opensdk.api.BaseResult):104 -> b
    51:51:void access$000$4e5a419(com.kaolafm.ad.api.internal.model.AdvertisingResult,com.kaolafm.ad.api.internal.model.AdCreative) -> a
    51:51:int access$100$52075b4d(java.lang.String) -> b
    2153:2153:int getCarousel(java.lang.String):153:153 -> b
    2153:2153:int access$100$52075b4d(java.lang.String):51 -> b
com.kaolafm.ad.api.internal.AdInternalRequest$1 -> com.kaolafm.ad.api.internal.b:
    com.kaolafm.opensdk.http.socket.SocketListener val$listener -> a
    com.kaolafm.ad.api.internal.AdInternalRequest this$0 -> b
    375:375:void <init>(com.kaolafm.ad.api.internal.AdInternalRequest,com.kaolafm.opensdk.http.socket.SocketListener) -> <init>
    378:392:void onSuccess(com.kaolafm.ad.api.internal.model.AdvertisingResult) -> a
    396:399:void onError(com.kaolafm.opensdk.http.error.ApiException) -> a
    375:375:void onSuccess(java.lang.Object) -> a
    1378:1392:void onSuccess(com.kaolafm.ad.api.internal.model.AdvertisingResult):378:392 -> a
    1378:1392:void onSuccess(java.lang.Object):375 -> a
com.kaolafm.ad.api.internal.AdInternalRequest$2 -> com.kaolafm.ad.api.internal.c:
    java.lang.String val$adZoneId -> val$adZoneId
    java.lang.String val$picWidth -> val$picWidth
    java.lang.String val$picHeight -> val$picHeight
    java.lang.String val$advancedAttrs -> val$advancedAttrs
    java.lang.String val$acceptedAdTypes -> val$acceptedAdTypes
    com.kaolafm.ad.api.internal.AdInternalRequest this$0 -> this$0
    406:421:void <init>(com.kaolafm.ad.api.internal.AdInternalRequest,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String) -> <init>
    1051:1051:int com.kaolafm.ad.api.internal.AdInternalRequest.access$100$52075b4d(java.lang.String):51:51 -> <init>
    1051:1051:void <init>(com.kaolafm.ad.api.internal.AdInternalRequest,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String):420 -> <init>
    1153:1153:int com.kaolafm.ad.api.internal.AdInternalRequest.getCarousel(java.lang.String):153:153 -> <init>
    1153:1153:int com.kaolafm.ad.api.internal.AdInternalRequest.access$100$52075b4d(java.lang.String):51 -> <init>
    1153:1153:void <init>(com.kaolafm.ad.api.internal.AdInternalRequest,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String):420 -> <init>
com.kaolafm.ad.api.internal.AdInternalRequest_Factory -> com.kaolafm.ad.api.internal.d:
    javax.inject.Provider mRepositoryManagerProvider -> a
    javax.inject.Provider mProfileLazyProvider -> b
    javax.inject.Provider mGsonLazyProvider -> c
    javax.inject.Provider mUrlManagerProvider -> d
    javax.inject.Provider reportRequestLazyProvider -> e
    javax.inject.Provider mCommonParamsProvider -> f
    javax.inject.Provider mSocketHostProvider -> g
    37:45:void <init>(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    49:49:com.kaolafm.ad.api.internal.AdInternalRequest get() -> get
    1067:1078:com.kaolafm.ad.api.internal.AdInternalRequest provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):67:78 -> get
    1067:1078:com.kaolafm.ad.api.internal.AdInternalRequest get():49 -> get
    1081:1082:void com.kaolafm.ad.api.internal.AdInternalRequest_MembersInjector.injectReportRequestLazy(com.kaolafm.ad.api.internal.AdInternalRequest,dagger.Lazy):81:82 -> get
    1081:1082:com.kaolafm.ad.api.internal.AdInternalRequest provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):74 -> get
    1081:1082:com.kaolafm.ad.api.internal.AdInternalRequest get():49 -> get
    1086:1087:void com.kaolafm.ad.api.internal.AdInternalRequest_MembersInjector.injectMCommonParams(com.kaolafm.ad.api.internal.AdInternalRequest,java.util.Map):86:87 -> get
    1086:1087:com.kaolafm.ad.api.internal.AdInternalRequest provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):76 -> get
    1086:1087:com.kaolafm.ad.api.internal.AdInternalRequest get():49 -> get
    1090:1091:void com.kaolafm.ad.api.internal.AdInternalRequest_MembersInjector.injectMSocketHost(com.kaolafm.ad.api.internal.AdInternalRequest,java.lang.String):90:91 -> get
    1090:1091:com.kaolafm.ad.api.internal.AdInternalRequest provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):77 -> get
    1090:1091:com.kaolafm.ad.api.internal.AdInternalRequest get():49 -> get
    67:78:com.kaolafm.ad.api.internal.AdInternalRequest provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> a
    2081:2082:void com.kaolafm.ad.api.internal.AdInternalRequest_MembersInjector.injectReportRequestLazy(com.kaolafm.ad.api.internal.AdInternalRequest,dagger.Lazy):81:82 -> a
    2081:2082:com.kaolafm.ad.api.internal.AdInternalRequest provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):74 -> a
    2086:2087:void com.kaolafm.ad.api.internal.AdInternalRequest_MembersInjector.injectMCommonParams(com.kaolafm.ad.api.internal.AdInternalRequest,java.util.Map):86:87 -> a
    2086:2087:com.kaolafm.ad.api.internal.AdInternalRequest provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):76 -> a
    2090:2091:void com.kaolafm.ad.api.internal.AdInternalRequest_MembersInjector.injectMSocketHost(com.kaolafm.ad.api.internal.AdInternalRequest,java.lang.String):90:91 -> a
    2090:2091:com.kaolafm.ad.api.internal.AdInternalRequest provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):77 -> a
    89:89:com.kaolafm.ad.api.internal.AdInternalRequest_Factory create(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> create
    100:100:com.kaolafm.ad.api.internal.AdInternalRequest newAdInternalRequest() -> a
    15:15:java.lang.Object get() -> get
com.kaolafm.ad.api.internal.AdInternalRequest_MembersInjector -> com.kaolafm.ad.api.internal.e:
    javax.inject.Provider mRepositoryManagerProvider -> a
    javax.inject.Provider mProfileLazyProvider -> b
    javax.inject.Provider mGsonLazyProvider -> c
    javax.inject.Provider mUrlManagerProvider -> d
    javax.inject.Provider reportRequestLazyProvider -> e
    javax.inject.Provider mCommonParamsProvider -> f
    javax.inject.Provider mSocketHostProvider -> g
    38:46:void <init>(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    56:56:dagger.MembersInjector create(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> a
    68:77:void injectMembers(com.kaolafm.ad.api.internal.AdInternalRequest) -> a
    1081:1082:void injectReportRequestLazy(com.kaolafm.ad.api.internal.AdInternalRequest,dagger.Lazy):81:82 -> a
    1081:1082:void injectMembers(com.kaolafm.ad.api.internal.AdInternalRequest):74 -> a
    1086:1087:void injectMCommonParams(com.kaolafm.ad.api.internal.AdInternalRequest,java.util.Map):86:87 -> a
    1086:1087:void injectMembers(com.kaolafm.ad.api.internal.AdInternalRequest):75 -> a
    1090:1091:void injectMSocketHost(com.kaolafm.ad.api.internal.AdInternalRequest,java.lang.String):90:91 -> a
    1090:1091:void injectMembers(com.kaolafm.ad.api.internal.AdInternalRequest):76 -> a
    81:82:void injectReportRequestLazy(com.kaolafm.ad.api.internal.AdInternalRequest,dagger.Lazy) -> a
    86:87:void injectMCommonParams(com.kaolafm.ad.api.internal.AdInternalRequest,java.util.Map) -> a
    90:91:void injectMSocketHost(com.kaolafm.ad.api.internal.AdInternalRequest,java.lang.String) -> a
    16:16:void injectMembers(java.lang.Object) -> a
    2068:2077:void injectMembers(com.kaolafm.ad.api.internal.AdInternalRequest):68:77 -> a
    2068:2077:void injectMembers(java.lang.Object):16 -> a
    2081:2082:void injectReportRequestLazy(com.kaolafm.ad.api.internal.AdInternalRequest,dagger.Lazy):81:82 -> a
    2081:2082:void injectMembers(com.kaolafm.ad.api.internal.AdInternalRequest):74 -> a
    2081:2082:void injectMembers(java.lang.Object):16 -> a
    2086:2087:void injectMCommonParams(com.kaolafm.ad.api.internal.AdInternalRequest,java.util.Map):86:87 -> a
    2086:2087:void injectMembers(com.kaolafm.ad.api.internal.AdInternalRequest):75 -> a
    2086:2087:void injectMembers(java.lang.Object):16 -> a
    2090:2091:void injectMSocketHost(com.kaolafm.ad.api.internal.AdInternalRequest,java.lang.String):90:91 -> a
    2090:2091:void injectMembers(com.kaolafm.ad.api.internal.AdInternalRequest):76 -> a
    2090:2091:void injectMembers(java.lang.Object):16 -> a
com.kaolafm.ad.api.internal.AdInternalService -> com.kaolafm.ad.api.internal.f:
    io.reactivex.Single getAdvertisings(java.util.Map) -> a
com.kaolafm.ad.api.internal.AdReportRequest -> com.kaolafm.ad.api.internal.g:
    com.kaolafm.ad.api.internal.AdReportService mAdReportService -> e
    dagger.Lazy activeParam -> a
    dagger.Lazy profile -> b
    javax.inject.Provider commonParams -> c
    javax.inject.Provider basicParams -> d
    59:63:void <init>() -> <init>
    66:66:io.reactivex.functions.Function map() -> a
    70:71:void active(com.kaolafm.opensdk.http.core.HttpCallback) -> a
    1066:1066:io.reactivex.functions.Function map():66:66 -> a
    1066:1066:void active(com.kaolafm.opensdk.http.core.HttpCallback):70 -> a
    82:84:void display(java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> a
    2066:2066:io.reactivex.functions.Function map():66:66 -> a
    2066:2066:void display(java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback):83 -> a
    95:97:void endDisplay(java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> b
    3066:3066:io.reactivex.functions.Function map():66:66 -> b
    3066:3066:void endDisplay(java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback):96 -> b
    108:110:void interruptDisplay(java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> c
    4066:4066:io.reactivex.functions.Function map():66:66 -> c
    4066:4066:void interruptDisplay(java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback):109 -> c
    121:126:void play(java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> d
    5066:5066:io.reactivex.functions.Function map():66:66 -> d
    5066:5066:void play(java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback):125 -> d
    138:149:void endPlay(java.lang.String,java.lang.String,long,com.kaolafm.opensdk.http.core.HttpCallback) -> a
    6066:6066:io.reactivex.functions.Function map():66:66 -> a
    6066:6066:void endPlay(java.lang.String,java.lang.String,long,com.kaolafm.opensdk.http.core.HttpCallback):148 -> a
    160:162:void skip(java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> e
    7066:7066:io.reactivex.functions.Function map():66:66 -> e
    7066:7066:void skip(java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback):161 -> e
    173:175:void displayMoreInteraction(java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> f
    8066:8066:io.reactivex.functions.Function map():66:66 -> f
    8066:8066:void displayMoreInteraction(java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback):174 -> f
    186:188:void displayMoreInteractionEnd(java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> g
    9066:9066:io.reactivex.functions.Function map():66:66 -> g
    9066:9066:void displayMoreInteractionEnd(java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback):187 -> g
    199:201:void click(java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> h
    10066:10066:io.reactivex.functions.Function map():66:66 -> h
    10066:10066:void click(java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback):200 -> h
    210:229:void error$215a26ca(java.lang.String,com.kaolafm.opensdk.api.BaseResult) -> a
    11066:11066:io.reactivex.functions.Function map():66:66 -> a
    11066:11066:void error$215a26ca(java.lang.String,com.kaolafm.opensdk.api.BaseResult):227 -> a
    233:234:io.reactivex.Single getParamsSet(java.lang.String,java.lang.String) -> a
    257:258:void add(java.util.Set,int,java.lang.String,java.lang.Object) -> a
    261:262:void remove(java.util.Set,java.lang.String) -> a
    265:265:io.reactivex.Single getParam(java.lang.String,java.lang.String) -> b
    265:265:java.lang.String lambda$getParam$10(java.util.Set) -> a
    235:252:java.util.Set lambda$getParamsSet$9(java.lang.String,java.lang.String,com.kaolafm.ad.api.internal.model.ReportParamEntity) -> a
    199:199:io.reactivex.SingleSource lambda$click$8(java.lang.String) -> a
    186:186:io.reactivex.SingleSource lambda$displayMoreInteractionEnd$7(java.lang.String) -> b
    173:173:io.reactivex.SingleSource lambda$displayMoreInteraction$6(java.lang.String) -> c
    160:160:io.reactivex.SingleSource lambda$skip$5(java.lang.String) -> d
    139:146:io.reactivex.SingleSource lambda$endPlay$4(java.lang.String,long,com.kaolafm.ad.api.internal.model.ReportParamEntity) -> a
    122:123:io.reactivex.SingleSource lambda$play$3(java.util.Set) -> b
    108:108:io.reactivex.SingleSource lambda$interruptDisplay$2(java.lang.String) -> e
    95:95:io.reactivex.SingleSource lambda$endDisplay$1(java.lang.String) -> f
    82:82:io.reactivex.SingleSource lambda$display$0(java.lang.String) -> g
com.kaolafm.ad.api.internal.AdReportRequest_Factory -> com.kaolafm.ad.api.internal.h:
    javax.inject.Provider mRepositoryManagerProvider -> a
    javax.inject.Provider mProfileLazyProvider -> b
    javax.inject.Provider mGsonLazyProvider -> c
    javax.inject.Provider activeParamProvider -> d
    javax.inject.Provider profileProvider -> e
    javax.inject.Provider commonParamsProvider -> f
    javax.inject.Provider basicParamsProvider -> g
    36:44:void <init>(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    48:48:com.kaolafm.ad.api.internal.AdReportRequest get() -> get
    1066:1077:com.kaolafm.ad.api.internal.AdReportRequest provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):66:77 -> get
    1066:1077:com.kaolafm.ad.api.internal.AdReportRequest get():48 -> get
    1079:1080:void com.kaolafm.ad.api.internal.AdReportRequest_MembersInjector.injectActiveParam(com.kaolafm.ad.api.internal.AdReportRequest,dagger.Lazy):79:80 -> get
    1079:1080:com.kaolafm.ad.api.internal.AdReportRequest provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):72 -> get
    1079:1080:com.kaolafm.ad.api.internal.AdReportRequest get():48 -> get
    1083:1084:void com.kaolafm.ad.api.internal.AdReportRequest_MembersInjector.injectProfile(com.kaolafm.ad.api.internal.AdReportRequest,dagger.Lazy):83:84 -> get
    1083:1084:com.kaolafm.ad.api.internal.AdReportRequest provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):74 -> get
    1083:1084:com.kaolafm.ad.api.internal.AdReportRequest get():48 -> get
    1088:1089:void com.kaolafm.ad.api.internal.AdReportRequest_MembersInjector.injectCommonParams(com.kaolafm.ad.api.internal.AdReportRequest,javax.inject.Provider):88:89 -> get
    1088:1089:com.kaolafm.ad.api.internal.AdReportRequest provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):75 -> get
    1088:1089:com.kaolafm.ad.api.internal.AdReportRequest get():48 -> get
    1093:1094:void com.kaolafm.ad.api.internal.AdReportRequest_MembersInjector.injectBasicParams(com.kaolafm.ad.api.internal.AdReportRequest,javax.inject.Provider):93:94 -> get
    1093:1094:com.kaolafm.ad.api.internal.AdReportRequest provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):76 -> get
    1093:1094:com.kaolafm.ad.api.internal.AdReportRequest get():48 -> get
    66:77:com.kaolafm.ad.api.internal.AdReportRequest provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> a
    2079:2080:void com.kaolafm.ad.api.internal.AdReportRequest_MembersInjector.injectActiveParam(com.kaolafm.ad.api.internal.AdReportRequest,dagger.Lazy):79:80 -> a
    2079:2080:com.kaolafm.ad.api.internal.AdReportRequest provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):72 -> a
    2083:2084:void com.kaolafm.ad.api.internal.AdReportRequest_MembersInjector.injectProfile(com.kaolafm.ad.api.internal.AdReportRequest,dagger.Lazy):83:84 -> a
    2083:2084:com.kaolafm.ad.api.internal.AdReportRequest provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):74 -> a
    2088:2089:void com.kaolafm.ad.api.internal.AdReportRequest_MembersInjector.injectCommonParams(com.kaolafm.ad.api.internal.AdReportRequest,javax.inject.Provider):88:89 -> a
    2088:2089:com.kaolafm.ad.api.internal.AdReportRequest provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):75 -> a
    2093:2094:void com.kaolafm.ad.api.internal.AdReportRequest_MembersInjector.injectBasicParams(com.kaolafm.ad.api.internal.AdReportRequest,javax.inject.Provider):93:94 -> a
    2093:2094:com.kaolafm.ad.api.internal.AdReportRequest provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):76 -> a
    88:88:com.kaolafm.ad.api.internal.AdReportRequest_Factory create(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> create
    99:99:com.kaolafm.ad.api.internal.AdReportRequest newAdReportRequest() -> a
    14:14:java.lang.Object get() -> get
com.kaolafm.ad.api.internal.AdReportRequest_MembersInjector -> com.kaolafm.ad.api.internal.i:
    javax.inject.Provider mRepositoryManagerProvider -> a
    javax.inject.Provider mProfileLazyProvider -> b
    javax.inject.Provider mGsonLazyProvider -> c
    javax.inject.Provider activeParamProvider -> d
    javax.inject.Provider profileProvider -> e
    javax.inject.Provider commonParamsProvider -> f
    javax.inject.Provider basicParamsProvider -> g
    37:45:void <init>(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    55:55:dagger.MembersInjector create(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> a
    67:76:void injectMembers(com.kaolafm.ad.api.internal.AdReportRequest) -> a
    1079:1080:void injectActiveParam(com.kaolafm.ad.api.internal.AdReportRequest,dagger.Lazy):79:80 -> a
    1079:1080:void injectMembers(com.kaolafm.ad.api.internal.AdReportRequest):72 -> a
    1083:1084:void injectProfile(com.kaolafm.ad.api.internal.AdReportRequest,dagger.Lazy):83:84 -> a
    1083:1084:void injectMembers(com.kaolafm.ad.api.internal.AdReportRequest):73 -> a
    1088:1089:void injectCommonParams(com.kaolafm.ad.api.internal.AdReportRequest,javax.inject.Provider):88:89 -> a
    1088:1089:void injectMembers(com.kaolafm.ad.api.internal.AdReportRequest):74 -> a
    1093:1094:void injectBasicParams(com.kaolafm.ad.api.internal.AdReportRequest,javax.inject.Provider):93:94 -> a
    1093:1094:void injectMembers(com.kaolafm.ad.api.internal.AdReportRequest):75 -> a
    79:80:void injectActiveParam(com.kaolafm.ad.api.internal.AdReportRequest,dagger.Lazy) -> a
    83:84:void injectProfile(com.kaolafm.ad.api.internal.AdReportRequest,dagger.Lazy) -> b
    88:89:void injectCommonParams(com.kaolafm.ad.api.internal.AdReportRequest,javax.inject.Provider) -> a
    93:94:void injectBasicParams(com.kaolafm.ad.api.internal.AdReportRequest,javax.inject.Provider) -> b
    15:15:void injectMembers(java.lang.Object) -> a
    2067:2076:void injectMembers(com.kaolafm.ad.api.internal.AdReportRequest):67:76 -> a
    2067:2076:void injectMembers(java.lang.Object):15 -> a
    2079:2080:void injectActiveParam(com.kaolafm.ad.api.internal.AdReportRequest,dagger.Lazy):79:80 -> a
    2079:2080:void injectMembers(com.kaolafm.ad.api.internal.AdReportRequest):72 -> a
    2079:2080:void injectMembers(java.lang.Object):15 -> a
    2083:2084:void injectProfile(com.kaolafm.ad.api.internal.AdReportRequest,dagger.Lazy):83:84 -> a
    2083:2084:void injectMembers(com.kaolafm.ad.api.internal.AdReportRequest):73 -> a
    2083:2084:void injectMembers(java.lang.Object):15 -> a
    2088:2089:void injectCommonParams(com.kaolafm.ad.api.internal.AdReportRequest,javax.inject.Provider):88:89 -> a
    2088:2089:void injectMembers(com.kaolafm.ad.api.internal.AdReportRequest):74 -> a
    2088:2089:void injectMembers(java.lang.Object):15 -> a
    2093:2094:void injectBasicParams(com.kaolafm.ad.api.internal.AdReportRequest,javax.inject.Provider):93:94 -> a
    2093:2094:void injectMembers(com.kaolafm.ad.api.internal.AdReportRequest):75 -> a
    2093:2094:void injectMembers(java.lang.Object):15 -> a
com.kaolafm.ad.api.internal.AdReportService -> com.kaolafm.ad.api.internal.j:
    io.reactivex.Single skip(java.lang.String) -> a
    io.reactivex.Single display(java.lang.String) -> b
    io.reactivex.Single endDisplay(java.lang.String) -> c
    io.reactivex.Single interruptDisplay(java.lang.String) -> d
    io.reactivex.Single displayMoreInteraction(java.lang.String) -> e
    io.reactivex.Single displayMoreInteractionEnd(java.lang.String) -> f
    io.reactivex.Single play(java.lang.String) -> g
    io.reactivex.Single click(java.lang.String) -> h
    io.reactivex.Single error(java.lang.String) -> i
    io.reactivex.Single active(java.lang.String) -> j
com.kaolafm.ad.api.internal.OrderedPair -> com.kaolafm.ad.api.internal.k:
    int index -> a
    java.lang.String key -> b
    java.lang.Object value -> c
    30:36:void <init>(int,java.lang.String,java.lang.Object) -> <init>
    30:40:void <init>(java.lang.String) -> <init>
    43:43:int getIndex() -> c
    47:48:void setIndex(int) -> a
    51:51:java.lang.String getKey() -> a
    55:56:void setKey(java.lang.String) -> a
    59:59:java.lang.Object getValue() -> b
    63:64:void setValue(java.lang.Object) -> a
    68:68:java.lang.String toString() -> toString
    77:77:int compareTo(com.kaolafm.ad.api.internal.OrderedPair) -> a
    82:94:boolean equals(java.lang.Object) -> equals
    99:99:int hashCode() -> hashCode
    1103:1103:int hash(java.lang.Object[]):103:103 -> hashCode
    1103:1103:int hashCode():99 -> hashCode
    103:103:int hash(java.lang.Object[]) -> a
    15:15:int compareTo(java.lang.Object) -> compareTo
    2077:2077:int compareTo(com.kaolafm.ad.api.internal.OrderedPair):77:77 -> compareTo
    2077:2077:int compareTo(java.lang.Object):15 -> compareTo
com.kaolafm.ad.api.internal.model.AdCreative -> com.kaolafm.ad.api.internal.model.AdCreative:
    int customerId -> customerId
    int campaignId -> campaignId
    int adGroupId -> adGroupId
    int mediaId -> mediaId
    40:40:int getCustomerId() -> getCustomerId
    44:45:void setCustomerId(int) -> setCustomerId
    48:48:int getCampaignId() -> getCampaignId
    52:53:void setCampaignId(int) -> setCampaignId
    56:56:int getAdGroupId() -> getAdGroupId
    60:61:void setAdGroupId(int) -> setAdGroupId
    64:64:int getMediaId() -> getMediaId
    68:69:void setMediaId(int) -> setMediaId
    72:73:void <init>(android.os.Parcel) -> <init>
com.kaolafm.ad.api.internal.model.AdvertReportEntity -> com.kaolafm.ad.api.internal.model.AdvertReportEntity:
    int MONITOR_TYPE_MIAOZHEN -> MONITOR_TYPE_MIAOZHEN
    int MONITOR_TYPE_TALKINGDATA -> MONITOR_TYPE_TALKINGDATA
    java.lang.String sessionId -> sessionId
    int monitorType -> monitorType
    java.lang.String clickMonitorUrl -> clickMonitorUrl
    java.lang.String pvMonitorUrl -> pvMonitorUrl
    android.os.Parcelable$Creator CREATOR -> CREATOR
    34:34:java.lang.String getSessionId() -> getSessionId
    38:39:void setSessionId(java.lang.String) -> setSessionId
    42:42:int getMonitorType() -> getMonitorType
    46:47:void setMonitorType(int) -> setMonitorType
    50:50:java.lang.String getClickMonitorUrl() -> getClickMonitorUrl
    54:55:void setClickMonitorUrl(java.lang.String) -> setClickMonitorUrl
    58:58:java.lang.String getPvMonitorUrl() -> getPvMonitorUrl
    62:63:void setPvMonitorUrl(java.lang.String) -> setPvMonitorUrl
    65:66:void <init>() -> <init>
    68:73:void <init>(android.os.Parcel) -> <init>
    77:81:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    85:85:int describeContents() -> describeContents
    88:88:void <clinit>() -> <clinit>
com.kaolafm.ad.api.internal.model.AdvertReportEntity$1 -> com.kaolafm.ad.api.internal.model.a:
    88:88:void <init>() -> <init>
    91:91:com.kaolafm.ad.api.internal.model.AdvertReportEntity createFromParcel(android.os.Parcel) -> a
    96:96:com.kaolafm.ad.api.internal.model.AdvertReportEntity[] newArray(int) -> a
    88:88:java.lang.Object[] newArray(int) -> newArray
    1096:1096:com.kaolafm.ad.api.internal.model.AdvertReportEntity[] newArray(int):96:96 -> newArray
    1096:1096:java.lang.Object[] newArray(int):88 -> newArray
    88:88:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
    2091:2091:com.kaolafm.ad.api.internal.model.AdvertReportEntity createFromParcel(android.os.Parcel):91:91 -> createFromParcel
    2091:2091:java.lang.Object createFromParcel(android.os.Parcel):88 -> createFromParcel
com.kaolafm.ad.api.internal.model.AdvertisingResult -> com.kaolafm.ad.api.internal.model.AdvertisingResult:
    java.lang.String sessionId -> sessionId
    int transType -> transType
    int cost -> cost
    int memberId -> memberId
    java.lang.String secondaryMemberId -> secondaryMemberId
    java.lang.String md5Code -> md5Code
    java.lang.String appid -> appid
    int originalWidth -> originalWidth
    int originalHeight -> originalHeight
    int cbCarousel -> cbCarousel
    java.util.List adCreatives -> adCreatives
    11:11:void <init>() -> <init>
    71:71:java.lang.String getSessionId() -> getSessionId
    75:76:void setSessionId(java.lang.String) -> setSessionId
    79:79:int getTransType() -> getTransType
    83:84:void setTransType(int) -> setTransType
    87:87:int getCost() -> getCost
    91:92:void setCost(int) -> setCost
    95:95:int getMemberId() -> getMemberId
    99:100:void setMemberId(int) -> setMemberId
    103:103:java.lang.String getSecondaryMemberId() -> getSecondaryMemberId
    107:108:void setSecondaryMemberId(java.lang.String) -> setSecondaryMemberId
    111:111:java.lang.String getMd5Code() -> getMd5Code
    115:116:void setMd5Code(java.lang.String) -> setMd5Code
    119:119:java.lang.String getAppid() -> getAppid
    123:124:void setAppid(java.lang.String) -> setAppid
    127:127:int getOriginalWidth() -> getOriginalWidth
    131:132:void setOriginalWidth(int) -> setOriginalWidth
    135:135:int getOriginalHeight() -> getOriginalHeight
    139:140:void setOriginalHeight(int) -> setOriginalHeight
    143:143:int getCbCarousel() -> getCbCarousel
    147:148:void setCbCarousel(int) -> setCbCarousel
    151:151:java.util.List getAdCreatives() -> getAdCreatives
    155:156:void setAdCreatives(java.util.List) -> setAdCreatives
com.kaolafm.ad.api.internal.model.ReportParamEntity -> com.kaolafm.ad.api.internal.model.ReportParamEntity:
    java.lang.Long id -> id
    java.lang.Long creativeId -> creativeId
    java.lang.String sessionId -> sessionId
    long memberId -> memberId
    java.lang.String secondaryMemberIds -> secondaryMemberIds
    long mediaId -> mediaId
    long adZoneId -> adZoneId
    long campaignId -> campaignId
    long adGroupId -> adGroupId
    long customerId -> customerId
    int transType -> transType
    long cost -> cost
    int cbCarousel -> cbCarousel
    53:54:void <init>() -> <init>
    65:79:void <init>(java.lang.Long,java.lang.Long,java.lang.String,long,java.lang.String,long,long,long,long,long,int,long,int) -> <init>
    87:87:java.lang.Long getCreativeId() -> getCreativeId
    91:92:void setCreativeId(java.lang.Long) -> setCreativeId
    95:95:java.lang.String getSessionId() -> getSessionId
    99:100:void setSessionId(java.lang.String) -> setSessionId
    103:103:long getMemberId() -> getMemberId
    107:108:void setMemberId(long) -> setMemberId
    111:111:java.lang.String getSecondaryMemberIds() -> getSecondaryMemberIds
    115:116:void setSecondaryMemberIds(java.lang.String) -> setSecondaryMemberIds
    119:119:long getMediaId() -> getMediaId
    123:124:void setMediaId(long) -> setMediaId
    127:127:long getAdZoneId() -> getAdZoneId
    131:132:void setAdZoneId(long) -> setAdZoneId
    135:135:long getCampaignId() -> getCampaignId
    139:140:void setCampaignId(long) -> setCampaignId
    143:143:long getAdGroupId() -> getAdGroupId
    147:148:void setAdGroupId(long) -> setAdGroupId
    151:151:long getCustomerId() -> getCustomerId
    155:156:void setCustomerId(long) -> setCustomerId
    159:159:int getTransType() -> getTransType
    163:164:void setTransType(int) -> setTransType
    167:167:long getCost() -> getCost
    171:172:void setCost(long) -> setCost
    175:175:int getCbCarousel() -> getCbCarousel
    179:180:void setCbCarousel(int) -> setCbCarousel
    183:183:java.lang.Long getId() -> getId
    187:188:void setId(java.lang.Long) -> setId
com.kaolafm.ad.api.model.Advert -> com.kaolafm.ad.api.model.Advert:
    long id -> id
    java.lang.String sessionId -> sessionId
    int type -> type
    int subtype -> subtype
    java.lang.String url -> url
    long duration -> duration
    int exposeDuration -> exposeDuration
    boolean jump -> jump
    int jumpSeconds -> jumpSeconds
    java.util.Map extra -> extra
    java.lang.String localPath -> localPath
    android.os.Parcelable$Creator CREATOR -> CREATOR
    71:72:void <init>() -> <init>
    75:75:long getId() -> getId
    79:80:void setId(long) -> setId
    83:83:java.lang.String getSessionId() -> getSessionId
    87:88:void setSessionId(java.lang.String) -> setSessionId
    91:91:int getType() -> getType
    95:96:void setType(int) -> setType
    99:99:java.lang.String getUrl() -> getUrl
    103:104:void setUrl(java.lang.String) -> setUrl
    107:107:long getDuration() -> getDuration
    111:112:void setDuration(long) -> setDuration
    115:115:int getExposeDuration() -> getExposeDuration
    119:120:void setExposeDuration(int) -> setExposeDuration
    123:123:boolean isJump() -> isJump
    127:128:void setJump(boolean) -> setJump
    131:131:int getJumpSeconds() -> getJumpSeconds
    135:136:void setJumpSeconds(int) -> setJumpSeconds
    139:139:int getSubtype() -> getSubtype
    143:144:void setSubtype(int) -> setSubtype
    147:147:java.util.Map getExtra() -> getExtra
    151:152:void setExtra(java.util.Map) -> setExtra
    155:155:java.lang.String getLocalPath() -> getLocalPath
    159:160:void setLocalPath(java.lang.String) -> setLocalPath
    169:173:void putExtra(java.lang.String,java.lang.Object) -> putExtra
    182:185:java.lang.Object getExtraValue(java.lang.String) -> getExtraValue
    188:200:void <init>(android.os.Parcel) -> <init>
    216:216:int describeContents() -> describeContents
    221:232:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    202:202:void <clinit>() -> <clinit>
com.kaolafm.ad.api.model.Advert$1 -> com.kaolafm.ad.api.model.a:
    202:202:void <init>() -> <init>
    205:205:com.kaolafm.ad.api.model.Advert createFromParcel(android.os.Parcel) -> a
    210:210:com.kaolafm.ad.api.model.Advert[] newArray(int) -> a
    202:202:java.lang.Object[] newArray(int) -> newArray
    1210:1210:com.kaolafm.ad.api.model.Advert[] newArray(int):210:210 -> newArray
    1210:1210:java.lang.Object[] newArray(int):202 -> newArray
    202:202:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
    2205:2205:com.kaolafm.ad.api.model.Advert createFromParcel(android.os.Parcel):205:205 -> createFromParcel
    2205:2205:java.lang.Object createFromParcel(android.os.Parcel):202 -> createFromParcel
com.kaolafm.ad.api.model.AdvertisingDetails -> com.kaolafm.ad.api.model.AdvertisingDetails:
    java.lang.Long creativeId -> creativeId
    int adZoneId -> adZoneId
    int adType -> adType
    int width -> width
    int height -> height
    java.lang.String imageUrl -> imageUrl
    java.lang.String attachImageUrl -> attachImageUrl
    java.lang.String audioUrl -> audioUrl
    java.lang.String clickMonitorUrl -> clickMonitorUrl
    java.lang.String pvMonitorUrl -> pvMonitorUrl
    int jumpSeconds -> jumpSeconds
    int monitorType -> monitorType
    int imageDuration -> imageDuration
    int duration -> duration
    int audioDuration -> audioDuration
    int attachImageDuration -> attachImageDuration
    int moreInteraction -> moreInteraction
    java.lang.String moreInteractionIcon -> moreInteractionIcon
    int moreInteractionType -> moreInteractionType
    java.lang.String moreInteractionImage -> moreInteractionImage
    java.lang.String moreInteractionDestUrl -> moreInteractionDestUrl
    int moreInteractionDisplayDuration -> moreInteractionDisplayDuration
    int moreInteractionIconDisplayOption -> moreInteractionIconDisplayOption
    java.lang.String moreInteractionText -> moreInteractionText
    int jump -> jump
    java.util.List adPlayTimestamps -> adPlayTimestamps
    int subtype -> subtype
    java.lang.String sessionId -> sessionId
    int attachWidth -> attachWidth
    int attachHeight -> attachHeight
    int moreInteractionWidth -> moreInteractionWidth
    int moreInteractionHeight -> moreInteractionHeight
    android.os.Parcelable$Creator CREATOR -> CREATOR
    215:236:void <init>() -> <init>
    240:240:java.lang.Long getCreativeId() -> getCreativeId
    244:245:void setCreativeId(long) -> setCreativeId
    248:248:int getAdType() -> getAdType
    252:253:void setAdType(int) -> setAdType
    256:256:int getWidth() -> getWidth
    260:261:void setWidth(int) -> setWidth
    264:264:int getHeight() -> getHeight
    268:269:void setHeight(int) -> setHeight
    272:272:java.lang.String getImageUrl() -> getImageUrl
    276:277:void setImageUrl(java.lang.String) -> setImageUrl
    280:280:java.lang.String getAttachImageUrl() -> getAttachImageUrl
    284:285:void setAttachImageUrl(java.lang.String) -> setAttachImageUrl
    288:288:java.lang.String getAudioUrl() -> getAudioUrl
    292:293:void setAudioUrl(java.lang.String) -> setAudioUrl
    296:296:java.lang.String getClickMonitorUrl() -> getClickMonitorUrl
    300:301:void setClickMonitorUrl(java.lang.String) -> setClickMonitorUrl
    304:304:java.lang.String getPvMonitorUrl() -> getPvMonitorUrl
    308:309:void setPvMonitorUrl(java.lang.String) -> setPvMonitorUrl
    312:312:int getJumpSeconds() -> getJumpSeconds
    316:317:void setJumpSeconds(int) -> setJumpSeconds
    320:320:int getMonitorType() -> getMonitorType
    324:325:void setMonitorType(int) -> setMonitorType
    328:328:int getImageDuration() -> getImageDuration
    332:333:void setImageDuration(int) -> setImageDuration
    336:336:int getDuration() -> getDuration
    340:341:void setDuration(int) -> setDuration
    344:344:int getAudioDuration() -> getAudioDuration
    348:349:void setAudioDuration(int) -> setAudioDuration
    352:352:int getAttachImageDuration() -> getAttachImageDuration
    356:357:void setAttachImageDuration(int) -> setAttachImageDuration
    360:360:int getMoreInteraction() -> getMoreInteraction
    364:365:void setMoreInteraction(int) -> setMoreInteraction
    368:368:java.lang.String getMoreInteractionIcon() -> getMoreInteractionIcon
    372:373:void setMoreInteractionIcon(java.lang.String) -> setMoreInteractionIcon
    376:376:int getMoreInteractionType() -> getMoreInteractionType
    380:381:void setMoreInteractionType(int) -> setMoreInteractionType
    384:384:java.lang.String getMoreInteractionImage() -> getMoreInteractionImage
    388:389:void setMoreInteractionImage(java.lang.String) -> setMoreInteractionImage
    392:392:java.lang.String getMoreInteractionDestUrl() -> getMoreInteractionDestUrl
    396:397:void setMoreInteractionDestUrl(java.lang.String) -> setMoreInteractionDestUrl
    400:400:int getMoreInteractionDisplayDuration() -> getMoreInteractionDisplayDuration
    404:405:void setMoreInteractionDisplayDuration(int) -> setMoreInteractionDisplayDuration
    408:408:int getMoreInteractionIconDisplayOption() -> getMoreInteractionIconDisplayOption
    412:413:void setMoreInteractionIconDisplayOption(int) -> setMoreInteractionIconDisplayOption
    416:416:java.lang.String getMoreInteractionText() -> getMoreInteractionText
    420:421:void setMoreInteractionText(java.lang.String) -> setMoreInteractionText
    424:424:int isJump() -> isJump
    428:429:void setJump(int) -> setJump
    432:432:java.util.List getAdPlayTimestamps() -> getAdPlayTimestamps
    436:437:void setAdPlayTimestamps(java.util.List) -> setAdPlayTimestamps
    440:440:int getSubtype() -> getSubtype
    444:445:void setSubtype(int) -> setSubtype
    448:448:java.lang.String getSessionId() -> getSessionId
    452:453:void setSessionId(java.lang.String) -> setSessionId
    456:456:int getAdZoneId() -> getAdZoneId
    460:461:void setAdZoneId(int) -> setAdZoneId
    465:465:int getJump() -> getJump
    470:471:void setCreativeId(java.lang.Long) -> setCreativeId
    475:475:java.lang.String toString() -> toString
    508:508:int getAttachWidth() -> getAttachWidth
    512:513:void setAttachWidth(int) -> setAttachWidth
    516:516:int getAttachHeight() -> getAttachHeight
    520:521:void setAttachHeight(int) -> setAttachHeight
    524:524:int getMoreInteractionWidth() -> getMoreInteractionWidth
    528:529:void setMoreInteractionWidth(int) -> setMoreInteractionWidth
    532:532:int getMoreInteractionHeight() -> getMoreInteractionHeight
    536:537:void setMoreInteractionHeight(int) -> setMoreInteractionHeight
    541:541:int describeContents() -> describeContents
    546:578:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    215:613:void <init>(android.os.Parcel) -> <init>
    215:658:void <init>(java.lang.Long,int,int,int,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,int,int,int,int,int,int,int,java.lang.String,int,java.lang.String,java.lang.String,int,int,java.lang.String,int,java.util.List,int,java.lang.String,int,int,int,int) -> <init>
    660:660:void <clinit>() -> <clinit>
com.kaolafm.ad.api.model.AdvertisingDetails$1 -> com.kaolafm.ad.api.model.b:
    660:660:void <init>() -> <init>
    663:663:com.kaolafm.ad.api.model.AdvertisingDetails createFromParcel(android.os.Parcel) -> a
    668:668:com.kaolafm.ad.api.model.AdvertisingDetails[] newArray(int) -> a
    660:660:java.lang.Object[] newArray(int) -> newArray
    1668:1668:com.kaolafm.ad.api.model.AdvertisingDetails[] newArray(int):668:668 -> newArray
    1668:1668:java.lang.Object[] newArray(int):660 -> newArray
    660:660:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
    2663:2663:com.kaolafm.ad.api.model.AdvertisingDetails createFromParcel(android.os.Parcel):663:663 -> createFromParcel
    2663:2663:java.lang.Object createFromParcel(android.os.Parcel):660 -> createFromParcel
com.kaolafm.ad.api.model.AttachImage -> com.kaolafm.ad.api.model.AttachImage:
    java.lang.String url -> url
    java.lang.String localPath -> localPath
    int exposeDuration -> exposeDuration
    int width -> width
    int height -> height
    android.os.Parcelable$Creator CREATOR -> CREATOR
    37:38:void <init>() -> <init>
    41:41:java.lang.String getUrl() -> getUrl
    45:46:void setUrl(java.lang.String) -> setUrl
    49:49:java.lang.String getLocalPath() -> getLocalPath
    53:54:void setLocalPath(java.lang.String) -> setLocalPath
    57:57:int getExposeDuration() -> getExposeDuration
    61:62:void setExposeDuration(int) -> setExposeDuration
    65:65:int getWidth() -> getWidth
    69:70:void setWidth(int) -> setWidth
    73:73:int getHeight() -> getHeight
    77:78:void setHeight(int) -> setHeight
    80:86:void <init>(android.os.Parcel) -> <init>
    102:102:int describeContents() -> describeContents
    107:112:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    88:88:void <clinit>() -> <clinit>
com.kaolafm.ad.api.model.AttachImage$1 -> com.kaolafm.ad.api.model.c:
    88:88:void <init>() -> <init>
    91:91:com.kaolafm.ad.api.model.AttachImage createFromParcel(android.os.Parcel) -> a
    96:96:com.kaolafm.ad.api.model.AttachImage[] newArray(int) -> a
    88:88:java.lang.Object[] newArray(int) -> newArray
    1096:1096:com.kaolafm.ad.api.model.AttachImage[] newArray(int):96:96 -> newArray
    1096:1096:java.lang.Object[] newArray(int):88 -> newArray
    88:88:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
    2091:2091:com.kaolafm.ad.api.model.AttachImage createFromParcel(android.os.Parcel):91:91 -> createFromParcel
    2091:2091:java.lang.Object createFromParcel(android.os.Parcel):88 -> createFromParcel
com.kaolafm.ad.api.model.AudioAdvert -> com.kaolafm.ad.api.model.AudioAdvert:
    android.os.Parcelable$Creator CREATOR -> CREATOR
    12:13:void <init>() -> <init>
    17:17:int describeContents() -> describeContents
    22:23:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    26:27:void <init>(android.os.Parcel) -> <init>
    29:29:void <clinit>() -> <clinit>
com.kaolafm.ad.api.model.AudioAdvert$1 -> com.kaolafm.ad.api.model.d:
    29:29:void <init>() -> <init>
    32:32:com.kaolafm.ad.api.model.AudioAdvert createFromParcel(android.os.Parcel) -> a
    37:37:com.kaolafm.ad.api.model.AudioAdvert[] newArray(int) -> a
    29:29:java.lang.Object[] newArray(int) -> newArray
    1037:1037:com.kaolafm.ad.api.model.AudioAdvert[] newArray(int):37:37 -> newArray
    1037:1037:java.lang.Object[] newArray(int):29 -> newArray
    29:29:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
    2032:2032:com.kaolafm.ad.api.model.AudioAdvert createFromParcel(android.os.Parcel):32:32 -> createFromParcel
    2032:2032:java.lang.Object createFromParcel(android.os.Parcel):29 -> createFromParcel
com.kaolafm.ad.api.model.AudioImageAdvert -> com.kaolafm.ad.api.model.AudioImageAdvert:
    com.kaolafm.ad.api.model.ImageAdvert imageAdvert -> imageAdvert
    android.os.Parcelable$Creator CREATOR -> CREATOR
    14:15:void <init>() -> <init>
    18:18:com.kaolafm.ad.api.model.ImageAdvert getImageAdvert() -> getImageAdvert
    22:27:void setImageAdvert(com.kaolafm.ad.api.model.ImageAdvert) -> setImageAdvert
    31:36:void setSubtype(int) -> setSubtype
    40:44:void setInteractionAdvert(com.kaolafm.ad.api.model.InteractionAdvert) -> setInteractionAdvert
    48:48:int describeContents() -> describeContents
    53:55:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    58:60:void <init>(android.os.Parcel) -> <init>
    62:62:void <clinit>() -> <clinit>
com.kaolafm.ad.api.model.AudioImageAdvert$1 -> com.kaolafm.ad.api.model.e:
    62:62:void <init>() -> <init>
    65:65:com.kaolafm.ad.api.model.AudioImageAdvert createFromParcel(android.os.Parcel) -> a
    70:70:com.kaolafm.ad.api.model.AudioImageAdvert[] newArray(int) -> a
    62:62:java.lang.Object[] newArray(int) -> newArray
    1070:1070:com.kaolafm.ad.api.model.AudioImageAdvert[] newArray(int):70:70 -> newArray
    1070:1070:java.lang.Object[] newArray(int):62 -> newArray
    62:62:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
    2065:2065:com.kaolafm.ad.api.model.AudioImageAdvert createFromParcel(android.os.Parcel):65:65 -> createFromParcel
    2065:2065:java.lang.Object createFromParcel(android.os.Parcel):62 -> createFromParcel
com.kaolafm.ad.api.model.BaseAdvert -> com.kaolafm.ad.api.model.BaseAdvert:
    com.kaolafm.ad.api.model.InteractionAdvert interactionAdvert -> interactionAdvert
    android.os.Parcelable$Creator CREATOR -> CREATOR
    17:18:void <init>() -> <init>
    21:21:com.kaolafm.ad.api.model.InteractionAdvert getInteractionAdvert() -> getInteractionAdvert
    25:26:void setInteractionAdvert(com.kaolafm.ad.api.model.InteractionAdvert) -> setInteractionAdvert
    30:30:int describeContents() -> describeContents
    35:37:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    40:42:void <init>(android.os.Parcel) -> <init>
    44:44:void <clinit>() -> <clinit>
com.kaolafm.ad.api.model.BaseAdvert$1 -> com.kaolafm.ad.api.model.f:
    44:44:void <init>() -> <init>
    47:47:com.kaolafm.ad.api.model.BaseAdvert createFromParcel(android.os.Parcel) -> a
    52:52:com.kaolafm.ad.api.model.BaseAdvert[] newArray(int) -> a
    44:44:java.lang.Object[] newArray(int) -> newArray
    1052:1052:com.kaolafm.ad.api.model.BaseAdvert[] newArray(int):52:52 -> newArray
    1052:1052:java.lang.Object[] newArray(int):44 -> newArray
    44:44:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
    2047:2047:com.kaolafm.ad.api.model.BaseAdvert createFromParcel(android.os.Parcel):47:47 -> createFromParcel
    2047:2047:java.lang.Object createFromParcel(android.os.Parcel):44 -> createFromParcel
com.kaolafm.ad.api.model.ImageAdvert -> com.kaolafm.ad.api.model.ImageAdvert:
    int width -> width
    int height -> height
    com.kaolafm.ad.api.model.AttachImage attachImage -> attachImage
    android.os.Parcelable$Creator CREATOR -> CREATOR
    20:21:void <init>() -> <init>
    24:24:int getWidth() -> getWidth
    28:29:void setWidth(int) -> setWidth
    32:32:int getHeight() -> getHeight
    36:37:void setHeight(int) -> setHeight
    40:40:com.kaolafm.ad.api.model.AttachImage getAttachImage() -> getAttachImage
    44:45:void setAttachImage(com.kaolafm.ad.api.model.AttachImage) -> setAttachImage
    60:64:void <init>(android.os.Parcel) -> <init>
    68:72:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    47:47:void <clinit>() -> <clinit>
com.kaolafm.ad.api.model.ImageAdvert$1 -> com.kaolafm.ad.api.model.g:
    47:47:void <init>() -> <init>
    50:50:com.kaolafm.ad.api.model.ImageAdvert createFromParcel(android.os.Parcel) -> a
    55:55:com.kaolafm.ad.api.model.ImageAdvert[] newArray(int) -> a
    47:47:java.lang.Object[] newArray(int) -> newArray
    1055:1055:com.kaolafm.ad.api.model.ImageAdvert[] newArray(int):55:55 -> newArray
    1055:1055:java.lang.Object[] newArray(int):47 -> newArray
    47:47:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
    2050:2050:com.kaolafm.ad.api.model.ImageAdvert createFromParcel(android.os.Parcel):50:50 -> createFromParcel
    2050:2050:java.lang.Object createFromParcel(android.os.Parcel):47 -> createFromParcel
com.kaolafm.ad.api.model.InteractionAdvert -> com.kaolafm.ad.api.model.InteractionAdvert:
    java.lang.String description -> description
    int opportunity -> opportunity
    int interactionType -> interactionType
    java.lang.String destUrl -> destUrl
    java.lang.String localImagePath -> localImagePath
    int width -> width
    int height -> height
    android.os.Parcelable$Creator CREATOR -> CREATOR
    48:49:void <init>() -> <init>
    52:52:java.lang.String getDescription() -> getDescription
    56:57:void setDescription(java.lang.String) -> setDescription
    60:60:int getOpportunity() -> getOpportunity
    64:65:void setOpportunity(int) -> setOpportunity
    68:68:int getInteractionType() -> getInteractionType
    72:73:void setInteractionType(int) -> setInteractionType
    76:76:java.lang.String getDestUrl() -> getDestUrl
    80:81:void setDestUrl(java.lang.String) -> setDestUrl
    84:84:java.lang.String getLocalImagePath() -> getLocalImagePath
    88:89:void setLocalImagePath(java.lang.String) -> setLocalImagePath
    92:92:int getWidth() -> getWidth
    96:97:void setWidth(int) -> setWidth
    100:100:int getHeight() -> getHeight
    104:105:void setHeight(int) -> setHeight
    109:109:int describeContents() -> describeContents
    114:122:void writeToParcel(android.os.Parcel,int) -> writeToParcel
    125:133:void <init>(android.os.Parcel) -> <init>
    135:135:void <clinit>() -> <clinit>
com.kaolafm.ad.api.model.InteractionAdvert$1 -> com.kaolafm.ad.api.model.h:
    135:135:void <init>() -> <init>
    138:138:com.kaolafm.ad.api.model.InteractionAdvert createFromParcel(android.os.Parcel) -> a
    143:143:com.kaolafm.ad.api.model.InteractionAdvert[] newArray(int) -> a
    135:135:java.lang.Object[] newArray(int) -> newArray
    1143:1143:com.kaolafm.ad.api.model.InteractionAdvert[] newArray(int):143:143 -> newArray
    1143:1143:java.lang.Object[] newArray(int):135 -> newArray
    135:135:java.lang.Object createFromParcel(android.os.Parcel) -> createFromParcel
    2138:2138:com.kaolafm.ad.api.model.InteractionAdvert createFromParcel(android.os.Parcel):138:138 -> createFromParcel
    2138:2138:java.lang.Object createFromParcel(android.os.Parcel):135 -> createFromParcel
com.kaolafm.ad.db.AdvertisingEntity -> com.kaolafm.ad.db.a:
    long id -> a
    java.lang.String adZoneId -> b
    14:16:void <init>(java.lang.String) -> <init>
    18:19:void <init>() -> <init>
    22:22:java.lang.String getAdZoneId() -> a
    26:27:void setAdZoneId(java.lang.String) -> a
com.kaolafm.ad.db.greendao.AdvertisingDetailsDao -> com.kaolafm.ad.db.greendao.AdvertisingDetailsDao:
    java.lang.String TABLENAME -> a
    com.kaolafm.opensdk.db.helper.DaoStringListConverter adPlayTimestampsConverter -> b
    64:68:void <init>(org.greenrobot.greendao.internal.DaoConfig) -> <init>
    64:72:void <init>(org.greenrobot.greendao.internal.DaoConfig,com.kaolafm.ad.db.greendao.DaoSession) -> <init>
    76:110:void createTable$3c87de94(org.greenrobot.greendao.database.Database) -> a
    114:116:void dropTable$3c87de94(org.greenrobot.greendao.database.Database) -> b
    120:201:void bindValues(org.greenrobot.greendao.database.DatabaseStatement,com.kaolafm.ad.api.model.AdvertisingDetails) -> a
    205:286:void bindValues(android.database.sqlite.SQLiteStatement,com.kaolafm.ad.api.model.AdvertisingDetails) -> a
    290:290:java.lang.Long readKey(android.database.Cursor,int) -> a
    295:329:com.kaolafm.ad.api.model.AdvertisingDetails readEntity(android.database.Cursor,int) -> b
    334:366:void readEntity(android.database.Cursor,com.kaolafm.ad.api.model.AdvertisingDetails,int) -> a
    370:371:java.lang.Long updateKeyAfterInsert(com.kaolafm.ad.api.model.AdvertisingDetails,long) -> a
    376:379:java.lang.Long getKey(com.kaolafm.ad.api.model.AdvertisingDetails) -> a
    385:385:boolean hasKey(com.kaolafm.ad.api.model.AdvertisingDetails) -> b
    390:390:boolean isEntityUpdateable() -> a
    21:21:boolean hasKey(java.lang.Object) -> a
    1385:1385:boolean hasKey(com.kaolafm.ad.api.model.AdvertisingDetails):385:385 -> a
    1385:1385:boolean hasKey(java.lang.Object):21 -> a
    21:21:java.lang.Object getKey(java.lang.Object) -> b
    2376:2379:java.lang.Long getKey(com.kaolafm.ad.api.model.AdvertisingDetails):376:379 -> b
    2376:2379:java.lang.Object getKey(java.lang.Object):21 -> b
    21:21:java.lang.Object updateKeyAfterInsert(java.lang.Object,long) -> a
    3370:3371:java.lang.Long updateKeyAfterInsert(com.kaolafm.ad.api.model.AdvertisingDetails,long):370:371 -> a
    3370:3371:java.lang.Object updateKeyAfterInsert(java.lang.Object,long):21 -> a
    21:21:void bindValues(android.database.sqlite.SQLiteStatement,java.lang.Object) -> a
    4205:4286:void bindValues(android.database.sqlite.SQLiteStatement,com.kaolafm.ad.api.model.AdvertisingDetails):205:286 -> a
    4205:4286:void bindValues(android.database.sqlite.SQLiteStatement,java.lang.Object):21 -> a
    21:21:void bindValues(org.greenrobot.greendao.database.DatabaseStatement,java.lang.Object) -> a
    5120:5201:void bindValues(org.greenrobot.greendao.database.DatabaseStatement,com.kaolafm.ad.api.model.AdvertisingDetails):120:201 -> a
    5120:5201:void bindValues(org.greenrobot.greendao.database.DatabaseStatement,java.lang.Object):21 -> a
    21:21:void readEntity(android.database.Cursor,java.lang.Object,int) -> a
    5334:5366:void readEntity(android.database.Cursor,com.kaolafm.ad.api.model.AdvertisingDetails,int):334:366 -> a
    5334:5366:void readEntity(android.database.Cursor,java.lang.Object,int):21 -> a
    21:21:java.lang.Object readKey(android.database.Cursor,int) -> c
    6290:6290:java.lang.Long readKey(android.database.Cursor,int):290:290 -> c
    6290:6290:java.lang.Object readKey(android.database.Cursor,int):21 -> c
    21:21:java.lang.Object readEntity(android.database.Cursor,int) -> d
    6295:6329:com.kaolafm.ad.api.model.AdvertisingDetails readEntity(android.database.Cursor,int):295:329 -> d
    6295:6329:java.lang.Object readEntity(android.database.Cursor,int):21 -> d
com.kaolafm.ad.db.greendao.AdvertisingDetailsDao$Properties -> com.kaolafm.ad.db.greendao.AdvertisingDetailsDao$Properties:
    org.greenrobot.greendao.Property CreativeId -> a
    org.greenrobot.greendao.Property AdZoneId -> b
    org.greenrobot.greendao.Property AdType -> c
    org.greenrobot.greendao.Property Width -> d
    org.greenrobot.greendao.Property Height -> e
    org.greenrobot.greendao.Property ImageUrl -> f
    org.greenrobot.greendao.Property AttachImageUrl -> g
    org.greenrobot.greendao.Property AudioUrl -> h
    org.greenrobot.greendao.Property ClickMonitorUrl -> i
    org.greenrobot.greendao.Property PvMonitorUrl -> j
    org.greenrobot.greendao.Property JumpSeconds -> k
    org.greenrobot.greendao.Property MonitorType -> l
    org.greenrobot.greendao.Property ImageDuration -> m
    org.greenrobot.greendao.Property Duration -> n
    org.greenrobot.greendao.Property AudioDuration -> o
    org.greenrobot.greendao.Property AttachImageDuration -> p
    org.greenrobot.greendao.Property MoreInteraction -> q
    org.greenrobot.greendao.Property MoreInteractionIcon -> r
    org.greenrobot.greendao.Property MoreInteractionType -> s
    org.greenrobot.greendao.Property MoreInteractionImage -> t
    org.greenrobot.greendao.Property MoreInteractionDestUrl -> u
    org.greenrobot.greendao.Property MoreInteractionDisplayDuration -> v
    org.greenrobot.greendao.Property MoreInteractionIconDisplayOption -> w
    org.greenrobot.greendao.Property MoreInteractionText -> x
    org.greenrobot.greendao.Property Jump -> y
    org.greenrobot.greendao.Property AdPlayTimestamps -> z
    org.greenrobot.greendao.Property Subtype -> A
    org.greenrobot.greendao.Property SessionId -> B
    org.greenrobot.greendao.Property AttachWidth -> C
    org.greenrobot.greendao.Property AttachHeight -> D
    org.greenrobot.greendao.Property MoreInteractionWidth -> E
    org.greenrobot.greendao.Property MoreInteractionHeight -> F
    29:29:void <init>() -> <init>
    30:61:void <clinit>() -> <clinit>
com.kaolafm.ad.db.greendao.DaoMaster -> com.kaolafm.ad.db.greendao.a:
    int SCHEMA_VERSION -> a
    24:26:void createAllTables$3c87de94(org.greenrobot.greendao.database.Database) -> a
    30:32:void dropAllTables$3c87de94(org.greenrobot.greendao.database.Database) -> b
    39:41:com.kaolafm.ad.db.greendao.DaoSession newDevSession(android.content.Context,java.lang.String) -> a
    45:46:void <init>(android.database.sqlite.SQLiteDatabase) -> <init>
    49:52:void <init>(org.greenrobot.greendao.database.Database) -> <init>
    55:55:com.kaolafm.ad.db.greendao.DaoSession newSession() -> a
    59:59:com.kaolafm.ad.db.greendao.DaoSession newSession(org.greenrobot.greendao.identityscope.IdentityScopeType) -> a
    19:19:org.greenrobot.greendao.AbstractDaoSession newSession(org.greenrobot.greendao.identityscope.IdentityScopeType) -> b
    1059:1059:com.kaolafm.ad.db.greendao.DaoSession newSession(org.greenrobot.greendao.identityscope.IdentityScopeType):59:59 -> b
    1059:1059:org.greenrobot.greendao.AbstractDaoSession newSession(org.greenrobot.greendao.identityscope.IdentityScopeType):19 -> b
    19:19:org.greenrobot.greendao.AbstractDaoSession newSession() -> b
com.kaolafm.ad.db.greendao.DaoMaster$DevOpenHelper -> com.kaolafm.ad.db.greendao.a$a:
    84:85:void <init>(android.content.Context,java.lang.String) -> <init>
    88:89:void <init>(android.content.Context,java.lang.String,android.database.sqlite.SQLiteDatabase$CursorFactory) -> <init>
    93:96:void onUpgrade(org.greenrobot.greendao.database.Database,int,int) -> a
    1030:1032:void com.kaolafm.ad.db.greendao.DaoMaster.dropAllTables(org.greenrobot.greendao.database.Database,boolean):30:32 -> a
    1030:1032:void onUpgrade(org.greenrobot.greendao.database.Database,int,int):94 -> a
    1076:1078:void com.kaolafm.ad.db.greendao.DaoMaster$OpenHelper.onCreate(org.greenrobot.greendao.database.Database):76:78 -> a
    1076:1078:void onUpgrade(org.greenrobot.greendao.database.Database,int,int):95 -> a
    2024:2026:void com.kaolafm.ad.db.greendao.DaoMaster.createAllTables(org.greenrobot.greendao.database.Database,boolean):24:26 -> a
    2024:2026:void com.kaolafm.ad.db.greendao.DaoMaster$OpenHelper.onCreate(org.greenrobot.greendao.database.Database):77 -> a
    2024:2026:void onUpgrade(org.greenrobot.greendao.database.Database,int,int):95 -> a
com.kaolafm.ad.db.greendao.DaoMaster$OpenHelper -> com.kaolafm.ad.db.greendao.a$b:
    67:68:void <init>(android.content.Context,java.lang.String) -> <init>
    71:72:void <init>(android.content.Context,java.lang.String,android.database.sqlite.SQLiteDatabase$CursorFactory) -> <init>
    76:78:void onCreate(org.greenrobot.greendao.database.Database) -> a
    1024:1026:void com.kaolafm.ad.db.greendao.DaoMaster.createAllTables$3c87de94(org.greenrobot.greendao.database.Database):24:26 -> a
    1024:1026:void onCreate(org.greenrobot.greendao.database.Database):77 -> a
com.kaolafm.ad.db.greendao.DaoSession -> com.kaolafm.ad.db.greendao.b:
    org.greenrobot.greendao.internal.DaoConfig reportParamEntityDaoConfig -> a
    org.greenrobot.greendao.internal.DaoConfig advertisingDetailsDaoConfig -> b
    com.kaolafm.ad.db.greendao.ReportParamEntityDao reportParamEntityDao -> c
    com.kaolafm.ad.db.greendao.AdvertisingDetailsDao advertisingDetailsDao -> d
    34:47:void <init>(org.greenrobot.greendao.database.Database,org.greenrobot.greendao.identityscope.IdentityScopeType,java.util.Map) -> <init>
    50:52:void clear() -> a
    55:55:com.kaolafm.ad.db.greendao.ReportParamEntityDao getReportParamEntityDao() -> b
    59:59:com.kaolafm.ad.db.greendao.AdvertisingDetailsDao getAdvertisingDetailsDao() -> c
com.kaolafm.ad.db.greendao.ReportParamEntityDao -> com.kaolafm.ad.db.greendao.ReportParamEntityDao:
    java.lang.String TABLENAME -> a
    44:45:void <init>(org.greenrobot.greendao.internal.DaoConfig) -> <init>
    48:49:void <init>(org.greenrobot.greendao.internal.DaoConfig,com.kaolafm.ad.db.greendao.DaoSession) -> <init>
    53:68:void createTable$3c87de94(org.greenrobot.greendao.database.Database) -> a
    72:74:void dropTable$3c87de94(org.greenrobot.greendao.database.Database) -> b
    78:108:void bindValues(org.greenrobot.greendao.database.DatabaseStatement,com.kaolafm.ad.api.internal.model.ReportParamEntity) -> a
    112:142:void bindValues(android.database.sqlite.SQLiteStatement,com.kaolafm.ad.api.internal.model.ReportParamEntity) -> a
    146:146:java.lang.Long readKey(android.database.Cursor,int) -> a
    151:166:com.kaolafm.ad.api.internal.model.ReportParamEntity readEntity(android.database.Cursor,int) -> b
    171:184:void readEntity(android.database.Cursor,com.kaolafm.ad.api.internal.model.ReportParamEntity,int) -> a
    188:189:java.lang.Long updateKeyAfterInsert(com.kaolafm.ad.api.internal.model.ReportParamEntity,long) -> a
    194:197:java.lang.Long getKey(com.kaolafm.ad.api.internal.model.ReportParamEntity) -> a
    203:203:boolean hasKey(com.kaolafm.ad.api.internal.model.ReportParamEntity) -> b
    208:208:boolean isEntityUpdateable() -> a
    18:18:boolean hasKey(java.lang.Object) -> a
    1203:1203:boolean hasKey(com.kaolafm.ad.api.internal.model.ReportParamEntity):203:203 -> a
    1203:1203:boolean hasKey(java.lang.Object):18 -> a
    18:18:java.lang.Object getKey(java.lang.Object) -> b
    2194:2197:java.lang.Long getKey(com.kaolafm.ad.api.internal.model.ReportParamEntity):194:197 -> b
    2194:2197:java.lang.Object getKey(java.lang.Object):18 -> b
    18:18:java.lang.Object updateKeyAfterInsert(java.lang.Object,long) -> a
    3188:3189:java.lang.Long updateKeyAfterInsert(com.kaolafm.ad.api.internal.model.ReportParamEntity,long):188:189 -> a
    3188:3189:java.lang.Object updateKeyAfterInsert(java.lang.Object,long):18 -> a
    18:18:void bindValues(android.database.sqlite.SQLiteStatement,java.lang.Object) -> a
    4112:4142:void bindValues(android.database.sqlite.SQLiteStatement,com.kaolafm.ad.api.internal.model.ReportParamEntity):112:142 -> a
    4112:4142:void bindValues(android.database.sqlite.SQLiteStatement,java.lang.Object):18 -> a
    18:18:void bindValues(org.greenrobot.greendao.database.DatabaseStatement,java.lang.Object) -> a
    5078:5108:void bindValues(org.greenrobot.greendao.database.DatabaseStatement,com.kaolafm.ad.api.internal.model.ReportParamEntity):78:108 -> a
    5078:5108:void bindValues(org.greenrobot.greendao.database.DatabaseStatement,java.lang.Object):18 -> a
    18:18:void readEntity(android.database.Cursor,java.lang.Object,int) -> a
    5171:5184:void readEntity(android.database.Cursor,com.kaolafm.ad.api.internal.model.ReportParamEntity,int):171:184 -> a
    5171:5184:void readEntity(android.database.Cursor,java.lang.Object,int):18 -> a
    18:18:java.lang.Object readKey(android.database.Cursor,int) -> c
    6146:6146:java.lang.Long readKey(android.database.Cursor,int):146:146 -> c
    6146:6146:java.lang.Object readKey(android.database.Cursor,int):18 -> c
    18:18:java.lang.Object readEntity(android.database.Cursor,int) -> d
    6151:6166:com.kaolafm.ad.api.internal.model.ReportParamEntity readEntity(android.database.Cursor,int):151:166 -> d
    6151:6166:java.lang.Object readEntity(android.database.Cursor,int):18 -> d
com.kaolafm.ad.db.greendao.ReportParamEntityDao$Properties -> com.kaolafm.ad.db.greendao.ReportParamEntityDao$Properties:
    org.greenrobot.greendao.Property Id -> a
    org.greenrobot.greendao.Property CreativeId -> b
    org.greenrobot.greendao.Property SessionId -> c
    org.greenrobot.greendao.Property MemberId -> d
    org.greenrobot.greendao.Property SecondaryMemberIds -> e
    org.greenrobot.greendao.Property MediaId -> f
    org.greenrobot.greendao.Property AdZoneId -> g
    org.greenrobot.greendao.Property CampaignId -> h
    org.greenrobot.greendao.Property AdGroupId -> i
    org.greenrobot.greendao.Property CustomerId -> j
    org.greenrobot.greendao.Property TransType -> k
    org.greenrobot.greendao.Property Cost -> l
    org.greenrobot.greendao.Property CbCarousel -> m
    26:26:void <init>() -> <init>
    27:39:void <clinit>() -> <clinit>
com.kaolafm.ad.db.manager.AdvertDBManager -> com.kaolafm.ad.db.a.a:
    26:27:void <init>() -> <init>
    30:45:void updateByZoneId(com.kaolafm.ad.api.model.AdvertisingDetails) -> a
    48:52:void queryByZoneId(java.lang.String,com.kaolafm.opensdk.db.OnQueryListener) -> a
    60:69:void deleteBySessionId(java.lang.String) -> a
    72:76:void queryTimedAdvert(com.kaolafm.opensdk.db.OnQueryListener) -> queryTimedAdvert
    79:90:void deleteTimedAdvert() -> a
    80:88:java.lang.Boolean lambda$deleteTimedAdvert$4() -> b
    72:75:java.util.List lambda$queryTimedAdvert$3() -> c
    61:67:java.lang.Boolean lambda$deleteBySessionId$2(java.lang.String) -> b
    48:51:java.util.List lambda$queryByZoneId$1(java.lang.String) -> c
    34:43:java.lang.Boolean lambda$updateByZoneId$0(com.kaolafm.ad.api.model.AdvertisingDetails) -> b
com.kaolafm.ad.db.manager.AdvertDBManager_Factory -> com.kaolafm.ad.db.a.b:
    com.kaolafm.ad.db.manager.AdvertDBManager_Factory INSTANCE -> a
    6:6:void <init>() -> <init>
    11:11:com.kaolafm.ad.db.manager.AdvertDBManager get() -> get
    1015:1015:com.kaolafm.ad.db.manager.AdvertDBManager provideInstance():15:15 -> get
    1015:1015:com.kaolafm.ad.db.manager.AdvertDBManager get():11 -> get
    15:15:com.kaolafm.ad.db.manager.AdvertDBManager provideInstance() -> a
    19:19:com.kaolafm.ad.db.manager.AdvertDBManager_Factory create() -> create
    23:23:com.kaolafm.ad.db.manager.AdvertDBManager newAdvertDBManager() -> b
    6:6:java.lang.Object get() -> get
    7:7:void <clinit>() -> <clinit>
com.kaolafm.ad.db.manager.DaoManager -> com.kaolafm.ad.db.a.c:
    com.kaolafm.ad.db.manager.DaoManager mInstance -> a
    android.app.Application mContext -> b
    com.kaolafm.ad.db.greendao.DaoMaster mDaoMaster -> c
    com.kaolafm.ad.db.greendao.DaoSession mDaoSession -> d
    com.kaolafm.ad.db.greendao.DaoMaster$OpenHelper mHelper -> e
    34:40:void <init>(java.lang.String) -> <init>
    43:50:com.kaolafm.ad.db.manager.DaoManager getInstance(java.lang.String) -> a
    56:59:com.kaolafm.ad.db.greendao.DaoSession getDaoSession() -> a
    64:66:void close() -> b
    1076:1080:void closeHelper():76:80 -> b
    1076:1080:void close():64 -> b
    2069:2073:void closeDaoSession():69:73 -> b
    2069:2073:void close():65 -> b
    69:73:void closeDaoSession() -> c
    76:80:void closeHelper() -> d
    22:22:org.greenrobot.greendao.AbstractDaoSession getDaoSession() -> e
    3056:3059:com.kaolafm.ad.db.greendao.DaoSession getDaoSession():56:59 -> e
    3056:3059:org.greenrobot.greendao.AbstractDaoSession getDaoSession():22 -> e
    22:22:com.kaolafm.ad.db.greendao.DaoSession access$000(com.kaolafm.ad.db.manager.DaoManager) -> a
com.kaolafm.ad.db.manager.DaoManager$SQLiteUpdateOpenHelper -> com.kaolafm.ad.db.a.c$a:
    com.kaolafm.ad.db.manager.DaoManager this$0 -> a
    84:86:void <init>(com.kaolafm.ad.db.manager.DaoManager,android.content.Context,java.lang.String) -> <init>
    90:93:void onUpgrade(org.greenrobot.greendao.database.Database,int,int) -> a
com.kaolafm.ad.db.manager.ReportParamDBManager -> com.kaolafm.ad.db.a.d:
    com.kaolafm.ad.db.manager.ReportParamDBManager mInstance -> a
    24:25:void <init>() -> <init>
    28:35:com.kaolafm.ad.db.manager.ReportParamDBManager getInstance() -> a
    45:54:io.reactivex.Single queryById(java.lang.String) -> a
    46:53:com.kaolafm.ad.api.internal.model.ReportParamEntity lambda$queryById$0(java.lang.String) -> b
com.kaolafm.ad.di.component.AdvertSubcomponent -> com.kaolafm.ad.a.a.a:
    void inject(com.kaolafm.ad.api.internal.AdReportRequest) -> a
    void inject(com.kaolafm.ad.api.internal.AdInternalRequest) -> a
    void inject(com.kaolafm.ad.expose.AdvertisingManager) -> a
    void inject(com.kaolafm.ad.timer.TimedAdvertManager) -> a
com.kaolafm.ad.di.component.AdvertisingComponent -> com.kaolafm.ad.a.a.b:
com.kaolafm.ad.di.component.AdvertisingComponent$Builder -> com.kaolafm.ad.a.a.b$a:
com.kaolafm.ad.di.component.DaggerAdvertisingComponent -> com.kaolafm.ad.a.a.c:
    android.app.Application application -> a
    com.kaolafm.ad.AdvertOptions options -> b
    com.kaolafm.ad.di.module.AdCommonParamModule adCommonParamModule -> c
    javax.inject.Provider provideGsonProvider -> d
    javax.inject.Provider applicationProvider -> e
    javax.inject.Provider optionsProvider -> f
    com.kaolafm.ad.di.module.AdvertConfigModule_ProvideAdvertOptionsFactory provideAdvertOptionsProvider -> g
    com.kaolafm.ad.di.module.AdCommonParamModule_IsUseHttpsFactory isUseHttpsProvider -> h
    com.kaolafm.ad.di.module.AdCommonParamModule_ProvideEngineHostFactory provideEngineHostProvider -> i
    com.kaolafm.ad.di.module.AdCommonParamModule_ProvideReportHostFactory provideReportHostProvider -> j
    javax.inject.Provider domainQualifierMapOfStringAndStringProvider -> k
    com.kaolafm.opensdk.di.module.CommonHttpCfgModule_ProvideDomainsFactory provideDomainsProvider -> l
    javax.inject.Provider adProfileManagerProvider -> m
    com.kaolafm.ad.di.module.AdvertConfigModule_ProvideAdvertisingProfileFactory provideAdvertisingProfileProvider -> n
    javax.inject.Provider urlManagerImplProvider -> o
    javax.inject.Provider kaolaProfileManagerProvider -> p
    com.kaolafm.opensdk.di.module.CommonHttpCfgModule_ProvideProfileFactory provideProfileProvider -> q
    com.kaolafm.opensdk.account.token.KaolaAccessTokenCache_Factory kaolaAccessTokenCacheProvider -> r
    javax.inject.Provider accessTokenQualifierMapOfStringAndTokenCacheProvider -> s
    javax.inject.Provider realAccessTokenManagerProvider -> t
    com.kaolafm.opensdk.di.module.CommonHttpCfgModule_ProvideKaolaAccessTokenFactory provideKaolaAccessTokenProvider -> u
    javax.inject.Provider provideParamsProvider -> v
    com.kaolafm.ad.di.module.AdCommonParamModule_ProvideEngineParamsFactory provideEngineParamsProvider -> w
    javax.inject.Provider paramQualifierMapOfStringAndProviderOfMapOfStringAndStringProvider -> x
    javax.inject.Provider provideCacheFactoryProvider -> y
    javax.inject.Provider providePrintHttpLogLevelProvider -> z
    javax.inject.Provider requestInterceptorProvider -> A
    javax.inject.Provider setOfHttpBeforeHandlerProvider -> B
    javax.inject.Provider httpHandlerProvider -> C
    javax.inject.Provider provideOkHttpClientBuilderProvider -> D
    javax.inject.Provider provideOkHttpClientProvider -> E
    javax.inject.Provider provideApiUrlProvider -> F
    javax.inject.Provider provideRetrofitProvider -> G
    javax.inject.Provider setOfResponseErrorListenerProvider -> H
    javax.inject.Provider provideResponseErrorListenersProvider -> I
    com.kaolafm.opensdk.http.core.SingleRetryFunction_Factory singleRetryFunctionProvider -> J
    com.kaolafm.opensdk.http.core.ObservableRetryFunction_Factory observableRetryFunctionProvider -> K
    javax.inject.Provider repositoryManagerProvider -> L
    com.kaolafm.ad.di.module.AdCommonParamModule_ProvideActiveParamFactory provideActiveParamProvider -> M
    com.kaolafm.ad.di.module.AdCommonParamModule_ProvideBasicCommonParamFactory provideBasicCommonParamProvider -> N
    com.kaolafm.ad.di.module.AdCommonParamModule_ProvideCommonParamFactory provideCommonParamProvider -> O
    javax.inject.Provider adReportRequestProvider -> P
    javax.inject.Provider advertisingImagerAdapterProvider -> Q
    javax.inject.Provider advertisingPlayerAdapterProvider -> R
    javax.inject.Provider compositeExecutorProvider -> S
    javax.inject.Provider compositeAdapterProvider -> T
    com.kaolafm.ad.di.module.AdCommonParamModule_ProvideSocketHostFactory provideSocketHostProvider -> U
    javax.inject.Provider adInternalRequestProvider -> V
    javax.inject.Provider advertDBManagerProvider -> W
    javax.inject.Provider advertRepositoryProvider -> X
    javax.inject.Provider alarmTimerProvider -> Y
    225:227:void <init>(com.kaolafm.ad.di.component.DaggerAdvertisingComponent$Builder) -> <init>
    1259:1460:void initialize(com.kaolafm.ad.di.component.DaggerAdvertisingComponent$Builder):259:460 -> <init>
    1259:1460:void <init>(com.kaolafm.ad.di.component.DaggerAdvertisingComponent$Builder):226 -> <init>
    230:230:com.kaolafm.ad.di.component.AdvertisingComponent$Builder builder() -> a
    234:235:com.kaolafm.opensdk.account.token.KaolaAccessTokenCache getKaolaAccessTokenCache() -> b
    1493:1496:com.kaolafm.opensdk.account.token.KaolaAccessTokenCache injectKaolaAccessTokenCache(com.kaolafm.opensdk.account.token.KaolaAccessTokenCache):493:496 -> b
    1493:1496:com.kaolafm.opensdk.account.token.KaolaAccessTokenCache getKaolaAccessTokenCache():234 -> b
    239:239:java.util.Map getAccessTokenQualifierMapOfStringAndTokenCache() -> c
    2234:2235:com.kaolafm.opensdk.account.token.KaolaAccessTokenCache getKaolaAccessTokenCache():234:235 -> c
    2234:2235:java.util.Map getAccessTokenQualifierMapOfStringAndTokenCache():239 -> c
    2493:2496:com.kaolafm.opensdk.account.token.KaolaAccessTokenCache injectKaolaAccessTokenCache(com.kaolafm.opensdk.account.token.KaolaAccessTokenCache):493:496 -> c
    2493:2496:com.kaolafm.opensdk.account.token.KaolaAccessTokenCache getKaolaAccessTokenCache():234 -> c
    2493:2496:java.util.Map getAccessTokenQualifierMapOfStringAndTokenCache():239 -> c
    244:244:java.util.Map getParamQualifierMapOfStringAndProviderOfMapOfStringAndString() -> d
    249:249:boolean getB() -> e
    3474:3474:com.kaolafm.opensdk.Options options():474:474 -> e
    3474:3474:boolean getB():249 -> e
    4035:4035:boolean com.kaolafm.ad.di.module.AdCommonParamModule_IsUseHttpsFactory.proxyIsUseHttps$9cf4828(com.kaolafm.opensdk.Options):35:35 -> e
    4035:4035:boolean getB():249 -> e
    4152:4152:boolean com.kaolafm.ad.di.module.AdCommonParamModule.isUseHttps(com.kaolafm.opensdk.Options):152:152 -> e
    4152:4152:boolean com.kaolafm.ad.di.module.AdCommonParamModule_IsUseHttpsFactory.proxyIsUseHttps$9cf4828(com.kaolafm.opensdk.Options):35 -> e
    4152:4152:boolean getB():249 -> e
    253:254:java.lang.String getAdvertHostQualifierString() -> f
    4249:4249:boolean getB():249:249 -> f
    4249:4249:java.lang.String getAdvertHostQualifierString():254 -> f
    4474:4474:com.kaolafm.opensdk.Options options():474:474 -> f
    4474:4474:boolean getB():249 -> f
    4474:4474:java.lang.String getAdvertHostQualifierString():254 -> f
    5035:5035:boolean com.kaolafm.ad.di.module.AdCommonParamModule_IsUseHttpsFactory.proxyIsUseHttps$9cf4828(com.kaolafm.opensdk.Options):35:35 -> f
    5035:5035:boolean getB():249 -> f
    5035:5035:java.lang.String getAdvertHostQualifierString():254 -> f
    5152:5152:boolean com.kaolafm.ad.di.module.AdCommonParamModule.isUseHttps(com.kaolafm.opensdk.Options):152:152 -> f
    5152:5152:boolean com.kaolafm.ad.di.module.AdCommonParamModule_IsUseHttpsFactory.proxyIsUseHttps$9cf4828(com.kaolafm.opensdk.Options):35 -> f
    5152:5152:boolean getB():249 -> f
    5152:5152:java.lang.String getAdvertHostQualifierString():254 -> f
    259:460:void initialize(com.kaolafm.ad.di.component.DaggerAdvertisingComponent$Builder) -> a
    464:465:void inject(com.kaolafm.opensdk.account.token.RealAccessTokenManager) -> a
    5500:5502:com.kaolafm.opensdk.account.token.RealAccessTokenManager injectRealAccessTokenManager(com.kaolafm.opensdk.account.token.RealAccessTokenManager):500:502 -> a
    5500:5502:void inject(com.kaolafm.opensdk.account.token.RealAccessTokenManager):464 -> a
    6239:6239:java.util.Map getAccessTokenQualifierMapOfStringAndTokenCache():239:239 -> a
    6239:6239:com.kaolafm.opensdk.account.token.RealAccessTokenManager injectRealAccessTokenManager(com.kaolafm.opensdk.account.token.RealAccessTokenManager):501 -> a
    6239:6239:void inject(com.kaolafm.opensdk.account.token.RealAccessTokenManager):464 -> a
    7234:7235:com.kaolafm.opensdk.account.token.KaolaAccessTokenCache getKaolaAccessTokenCache():234:235 -> a
    7234:7235:java.util.Map getAccessTokenQualifierMapOfStringAndTokenCache():239 -> a
    7234:7235:com.kaolafm.opensdk.account.token.RealAccessTokenManager injectRealAccessTokenManager(com.kaolafm.opensdk.account.token.RealAccessTokenManager):501 -> a
    7234:7235:void inject(com.kaolafm.opensdk.account.token.RealAccessTokenManager):464 -> a
    7493:7496:com.kaolafm.opensdk.account.token.KaolaAccessTokenCache injectKaolaAccessTokenCache(com.kaolafm.opensdk.account.token.KaolaAccessTokenCache):493:496 -> a
    7493:7496:com.kaolafm.opensdk.account.token.KaolaAccessTokenCache getKaolaAccessTokenCache():234 -> a
    7493:7496:java.util.Map getAccessTokenQualifierMapOfStringAndTokenCache():239 -> a
    7493:7496:com.kaolafm.opensdk.account.token.RealAccessTokenManager injectRealAccessTokenManager(com.kaolafm.opensdk.account.token.RealAccessTokenManager):501 -> a
    7493:7496:void inject(com.kaolafm.opensdk.account.token.RealAccessTokenManager):464 -> a
    469:469:android.app.Application application() -> g
    474:474:com.kaolafm.opensdk.Options options() -> h
    479:480:void inject(com.kaolafm.ad.AdvertisingEngine) -> a
    7506:7509:com.kaolafm.ad.AdvertisingEngine injectAdvertisingEngine(com.kaolafm.ad.AdvertisingEngine):506:509 -> a
    7506:7509:void inject(com.kaolafm.ad.AdvertisingEngine):479 -> a
    484:484:com.kaolafm.opensdk.di.component.RequestComponent requestComponent() -> i
    489:489:com.kaolafm.ad.di.component.AdvertSubcomponent subComponent() -> j
    493:496:com.kaolafm.opensdk.account.token.KaolaAccessTokenCache injectKaolaAccessTokenCache(com.kaolafm.opensdk.account.token.KaolaAccessTokenCache) -> a
    500:502:com.kaolafm.opensdk.account.token.RealAccessTokenManager injectRealAccessTokenManager(com.kaolafm.opensdk.account.token.RealAccessTokenManager) -> b
    8239:8239:java.util.Map getAccessTokenQualifierMapOfStringAndTokenCache():239:239 -> b
    8239:8239:com.kaolafm.opensdk.account.token.RealAccessTokenManager injectRealAccessTokenManager(com.kaolafm.opensdk.account.token.RealAccessTokenManager):501 -> b
    9234:9235:com.kaolafm.opensdk.account.token.KaolaAccessTokenCache getKaolaAccessTokenCache():234:235 -> b
    9234:9235:java.util.Map getAccessTokenQualifierMapOfStringAndTokenCache():239 -> b
    9234:9235:com.kaolafm.opensdk.account.token.RealAccessTokenManager injectRealAccessTokenManager(com.kaolafm.opensdk.account.token.RealAccessTokenManager):501 -> b
    9493:9496:com.kaolafm.opensdk.account.token.KaolaAccessTokenCache injectKaolaAccessTokenCache(com.kaolafm.opensdk.account.token.KaolaAccessTokenCache):493:496 -> b
    9493:9496:com.kaolafm.opensdk.account.token.KaolaAccessTokenCache getKaolaAccessTokenCache():234 -> b
    9493:9496:java.util.Map getAccessTokenQualifierMapOfStringAndTokenCache():239 -> b
    9493:9496:com.kaolafm.opensdk.account.token.RealAccessTokenManager injectRealAccessTokenManager(com.kaolafm.opensdk.account.token.RealAccessTokenManager):501 -> b
    506:509:com.kaolafm.ad.AdvertisingEngine injectAdvertisingEngine(com.kaolafm.ad.AdvertisingEngine) -> b
    120:120:com.kaolafm.opensdk.di.component.BaseSubcomponent subComponent() -> k
    10489:10489:com.kaolafm.ad.di.component.AdvertSubcomponent subComponent():489:489 -> k
    10489:10489:com.kaolafm.opensdk.di.component.BaseSubcomponent subComponent():120 -> k
    120:120:void inject(java.lang.Object) -> a
    11479:11480:void inject(com.kaolafm.ad.AdvertisingEngine):479:480 -> a
    11479:11480:void inject(java.lang.Object):120 -> a
    11506:11509:com.kaolafm.ad.AdvertisingEngine injectAdvertisingEngine(com.kaolafm.ad.AdvertisingEngine):506:509 -> a
    11506:11509:void inject(com.kaolafm.ad.AdvertisingEngine):479 -> a
    11506:11509:void inject(java.lang.Object):120 -> a
    120:120:void <init>(com.kaolafm.ad.di.component.DaggerAdvertisingComponent$Builder,byte) -> <init>
    120:120:javax.inject.Provider access$800(com.kaolafm.ad.di.component.DaggerAdvertisingComponent) -> a
    120:120:javax.inject.Provider access$900(com.kaolafm.ad.di.component.DaggerAdvertisingComponent) -> b
    120:120:com.kaolafm.opensdk.di.module.CommonHttpCfgModule_ProvideProfileFactory access$1000(com.kaolafm.ad.di.component.DaggerAdvertisingComponent) -> c
    120:120:javax.inject.Provider access$1100(com.kaolafm.ad.di.component.DaggerAdvertisingComponent) -> d
    120:120:javax.inject.Provider access$1200(com.kaolafm.ad.di.component.DaggerAdvertisingComponent) -> e
    120:120:javax.inject.Provider access$1300(com.kaolafm.ad.di.component.DaggerAdvertisingComponent) -> f
    120:120:javax.inject.Provider access$1400(com.kaolafm.ad.di.component.DaggerAdvertisingComponent) -> g
    120:120:javax.inject.Provider access$1500(com.kaolafm.ad.di.component.DaggerAdvertisingComponent) -> h
    120:120:com.kaolafm.ad.di.module.AdCommonParamModule_ProvideActiveParamFactory access$1600(com.kaolafm.ad.di.component.DaggerAdvertisingComponent) -> i
    120:120:com.kaolafm.ad.di.module.AdvertConfigModule_ProvideAdvertisingProfileFactory access$1700(com.kaolafm.ad.di.component.DaggerAdvertisingComponent) -> j
    120:120:com.kaolafm.ad.di.module.AdCommonParamModule_ProvideCommonParamFactory access$1800(com.kaolafm.ad.di.component.DaggerAdvertisingComponent) -> k
    120:120:com.kaolafm.ad.di.module.AdCommonParamModule_ProvideBasicCommonParamFactory access$1900(com.kaolafm.ad.di.component.DaggerAdvertisingComponent) -> l
    120:120:javax.inject.Provider access$2000(com.kaolafm.ad.di.component.DaggerAdvertisingComponent) -> m
    120:120:java.util.Map access$2100(com.kaolafm.ad.di.component.DaggerAdvertisingComponent) -> n
    12244:12244:java.util.Map getParamQualifierMapOfStringAndProviderOfMapOfStringAndString():244:244 -> n
    12244:12244:java.util.Map access$2100(com.kaolafm.ad.di.component.DaggerAdvertisingComponent):120 -> n
    120:120:java.lang.String access$2200(com.kaolafm.ad.di.component.DaggerAdvertisingComponent) -> o
    12253:12254:java.lang.String getAdvertHostQualifierString():253:254 -> o
    12253:12254:java.lang.String access$2200(com.kaolafm.ad.di.component.DaggerAdvertisingComponent):120 -> o
    13249:13249:boolean getB():249:249 -> o
    13249:13249:java.lang.String getAdvertHostQualifierString():254 -> o
    13249:13249:java.lang.String access$2200(com.kaolafm.ad.di.component.DaggerAdvertisingComponent):120 -> o
    13474:13474:com.kaolafm.opensdk.Options options():474:474 -> o
    13474:13474:boolean getB():249 -> o
    13474:13474:java.lang.String getAdvertHostQualifierString():254 -> o
    13474:13474:java.lang.String access$2200(com.kaolafm.ad.di.component.DaggerAdvertisingComponent):120 -> o
    14035:14035:boolean com.kaolafm.ad.di.module.AdCommonParamModule_IsUseHttpsFactory.proxyIsUseHttps$9cf4828(com.kaolafm.opensdk.Options):35:35 -> o
    14035:14035:boolean getB():249 -> o
    14035:14035:java.lang.String getAdvertHostQualifierString():254 -> o
    14035:14035:java.lang.String access$2200(com.kaolafm.ad.di.component.DaggerAdvertisingComponent):120 -> o
    14152:14152:boolean com.kaolafm.ad.di.module.AdCommonParamModule.isUseHttps(com.kaolafm.opensdk.Options):152:152 -> o
    14152:14152:boolean com.kaolafm.ad.di.module.AdCommonParamModule_IsUseHttpsFactory.proxyIsUseHttps$9cf4828(com.kaolafm.opensdk.Options):35 -> o
    14152:14152:boolean getB():249 -> o
    14152:14152:java.lang.String getAdvertHostQualifierString():254 -> o
    14152:14152:java.lang.String access$2200(com.kaolafm.ad.di.component.DaggerAdvertisingComponent):120 -> o
    120:120:javax.inject.Provider access$2300(com.kaolafm.ad.di.component.DaggerAdvertisingComponent) -> p
    120:120:javax.inject.Provider access$2400(com.kaolafm.ad.di.component.DaggerAdvertisingComponent) -> q
    120:120:javax.inject.Provider access$2500(com.kaolafm.ad.di.component.DaggerAdvertisingComponent) -> r
    120:120:javax.inject.Provider access$2600(com.kaolafm.ad.di.component.DaggerAdvertisingComponent) -> s
com.kaolafm.ad.di.component.DaggerAdvertisingComponent$1 -> com.kaolafm.ad.a.a.d:
com.kaolafm.ad.di.component.DaggerAdvertisingComponent$AdvertSubcomponentImpl -> com.kaolafm.ad.a.a.c$a:
    com.kaolafm.ad.di.component.DaggerAdvertisingComponent this$0 -> a
    602:602:void <init>(com.kaolafm.ad.di.component.DaggerAdvertisingComponent) -> <init>
    605:609:java.util.Map getAdvertAdapterQualifierMapOfStringAndAdapter() -> a
    614:615:void inject(com.kaolafm.ad.api.internal.AdReportRequest) -> a
    1633:1648:com.kaolafm.ad.api.internal.AdReportRequest injectAdReportRequest(com.kaolafm.ad.api.internal.AdReportRequest):633:648 -> a
    1633:1648:void inject(com.kaolafm.ad.api.internal.AdReportRequest):614 -> a
    619:620:void inject(com.kaolafm.ad.api.internal.AdInternalRequest) -> a
    1652:1668:com.kaolafm.ad.api.internal.AdInternalRequest injectAdInternalRequest(com.kaolafm.ad.api.internal.AdInternalRequest):652:668 -> a
    1652:1668:void inject(com.kaolafm.ad.api.internal.AdInternalRequest):619 -> a
    624:625:void inject(com.kaolafm.ad.expose.AdvertisingManager) -> a
    1672:1676:com.kaolafm.ad.expose.AdvertisingManager injectAdvertisingManager(com.kaolafm.ad.expose.AdvertisingManager):672:676 -> a
    1672:1676:void inject(com.kaolafm.ad.expose.AdvertisingManager):624 -> a
    2605:2609:java.util.Map getAdvertAdapterQualifierMapOfStringAndAdapter():605:609 -> a
    2605:2609:com.kaolafm.ad.expose.AdvertisingManager injectAdvertisingManager(com.kaolafm.ad.expose.AdvertisingManager):673 -> a
    2605:2609:void inject(com.kaolafm.ad.expose.AdvertisingManager):624 -> a
    629:630:void inject(com.kaolafm.ad.timer.TimedAdvertManager) -> a
    2680:2686:com.kaolafm.ad.timer.TimedAdvertManager injectTimedAdvertManager(com.kaolafm.ad.timer.TimedAdvertManager):680:686 -> a
    2680:2686:void inject(com.kaolafm.ad.timer.TimedAdvertManager):629 -> a
    633:648:com.kaolafm.ad.api.internal.AdReportRequest injectAdReportRequest(com.kaolafm.ad.api.internal.AdReportRequest) -> b
    652:668:com.kaolafm.ad.api.internal.AdInternalRequest injectAdInternalRequest(com.kaolafm.ad.api.internal.AdInternalRequest) -> b
    672:676:com.kaolafm.ad.expose.AdvertisingManager injectAdvertisingManager(com.kaolafm.ad.expose.AdvertisingManager) -> b
    3605:3609:java.util.Map getAdvertAdapterQualifierMapOfStringAndAdapter():605:609 -> b
    3605:3609:com.kaolafm.ad.expose.AdvertisingManager injectAdvertisingManager(com.kaolafm.ad.expose.AdvertisingManager):673 -> b
    680:686:com.kaolafm.ad.timer.TimedAdvertManager injectTimedAdvertManager(com.kaolafm.ad.timer.TimedAdvertManager) -> b
    601:601:void <init>(com.kaolafm.ad.di.component.DaggerAdvertisingComponent,byte) -> <init>
com.kaolafm.ad.di.component.DaggerAdvertisingComponent$Builder -> com.kaolafm.ad.a.a.c$b:
    com.kaolafm.opensdk.di.module.HttpClientModule httpClientModule -> a
    com.kaolafm.ad.di.module.AdCommonParamModule adCommonParamModule -> b
    android.app.Application application -> c
    com.kaolafm.ad.AdvertOptions options -> d
    512:512:void <init>() -> <init>
    523:535:com.kaolafm.ad.di.component.AdvertisingComponent build() -> a
    540:541:com.kaolafm.ad.di.component.DaggerAdvertisingComponent$Builder application(android.app.Application) -> a
    546:547:com.kaolafm.ad.di.component.DaggerAdvertisingComponent$Builder options(com.kaolafm.ad.AdvertOptions) -> a
    512:512:com.kaolafm.opensdk.di.component.CoreComponent build() -> b
    1523:1535:com.kaolafm.ad.di.component.AdvertisingComponent build():523:535 -> b
    1523:1535:com.kaolafm.opensdk.di.component.CoreComponent build():512 -> b
    512:512:com.kaolafm.opensdk.di.component.CoreComponent$Builder options(com.kaolafm.opensdk.Options) -> a
    1546:1547:com.kaolafm.ad.di.component.DaggerAdvertisingComponent$Builder options(com.kaolafm.ad.AdvertOptions):546:547 -> a
    1546:1547:com.kaolafm.opensdk.di.component.CoreComponent$Builder options(com.kaolafm.opensdk.Options):512 -> a
    512:512:com.kaolafm.opensdk.di.component.CoreComponent$Builder application(android.app.Application) -> b
    2540:2541:com.kaolafm.ad.di.component.DaggerAdvertisingComponent$Builder application(android.app.Application):540:541 -> b
    2540:2541:com.kaolafm.opensdk.di.component.CoreComponent$Builder application(android.app.Application):512 -> b
    512:512:void <init>(byte) -> <init>
    512:512:android.app.Application access$100(com.kaolafm.ad.di.component.DaggerAdvertisingComponent$Builder) -> a
    512:512:com.kaolafm.opensdk.di.module.HttpClientModule access$200(com.kaolafm.ad.di.component.DaggerAdvertisingComponent$Builder) -> b
    512:512:com.kaolafm.ad.AdvertOptions access$300(com.kaolafm.ad.di.component.DaggerAdvertisingComponent$Builder) -> c
    512:512:com.kaolafm.ad.di.module.AdCommonParamModule access$400(com.kaolafm.ad.di.component.DaggerAdvertisingComponent$Builder) -> d
com.kaolafm.ad.di.component.DaggerAdvertisingComponent$RequestComponentImpl -> com.kaolafm.ad.a.a.c$c:
    com.kaolafm.opensdk.http.socket.SocketEngine_Factory socketEngineProvider -> b
    com.kaolafm.opensdk.http.socket.SocketConnection_Factory socketConnectionProvider -> c
    com.kaolafm.opensdk.http.socket.Socket_Factory socketProvider -> d
    com.kaolafm.ad.di.component.DaggerAdvertisingComponent this$0 -> a
    558:560:void <init>(com.kaolafm.ad.di.component.DaggerAdvertisingComponent) -> <init>
    1564:1569:void initialize():564:569 -> <init>
    1564:1569:void <init>(com.kaolafm.ad.di.component.DaggerAdvertisingComponent):559 -> <init>
    564:569:void initialize() -> a
    573:574:void inject(com.kaolafm.opensdk.api.BaseRequest) -> a
    1582:1590:com.kaolafm.opensdk.api.BaseRequest injectBaseRequest(com.kaolafm.opensdk.api.BaseRequest):582:590 -> a
    1582:1590:void inject(com.kaolafm.opensdk.api.BaseRequest):573 -> a
    578:579:void inject(com.kaolafm.opensdk.http.socket.SocketManager) -> a
    1594:1597:com.kaolafm.opensdk.http.socket.SocketManager injectSocketManager(com.kaolafm.opensdk.http.socket.SocketManager):594:597 -> a
    1594:1597:void inject(com.kaolafm.opensdk.http.socket.SocketManager):578 -> a
    582:590:com.kaolafm.opensdk.api.BaseRequest injectBaseRequest(com.kaolafm.opensdk.api.BaseRequest) -> b
    594:597:com.kaolafm.opensdk.http.socket.SocketManager injectSocketManager(com.kaolafm.opensdk.http.socket.SocketManager) -> b
    551:551:void <init>(com.kaolafm.ad.di.component.DaggerAdvertisingComponent,byte) -> <init>
com.kaolafm.ad.di.module.AdCommonParamModule -> com.kaolafm.ad.a.b.a:
    41:41:void <init>() -> <init>
    53:70:java.lang.String provideActiveParam(com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken) -> a
    83:89:java.util.Set provideCommonParam(com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,java.util.Set) -> a
    102:109:java.util.Set provideBasicCommonParam(com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken) -> b
    115:115:java.util.Map provideParams(com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken) -> c
    147:147:java.util.Map provideEngineParams(java.util.Map) -> a
    152:152:boolean isUseHttps(com.kaolafm.opensdk.Options) -> a
    160:160:java.lang.String provideEngineHost(boolean) -> b
    168:168:java.lang.String provideReportHost(boolean) -> c
    174:174:java.lang.String provideSocketHost(boolean) -> a
com.kaolafm.ad.di.module.AdCommonParamModule$1 -> com.kaolafm.ad.a.b.b:
    com.kaolafm.ad.profile.AdvertisingProfile val$profile -> val$profile
    com.kaolafm.opensdk.account.profile.KaolaProfile val$kaolaProfile -> val$kaolaProfile
    com.kaolafm.opensdk.account.token.KaolaAccessToken val$accessToken -> val$accessToken
    com.kaolafm.ad.di.module.AdCommonParamModule this$0 -> this$0
    53:69:void <init>(com.kaolafm.ad.di.module.AdCommonParamModule,com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken) -> <init>
com.kaolafm.ad.di.module.AdCommonParamModule$2 -> com.kaolafm.ad.a.b.c:
    com.kaolafm.opensdk.account.profile.KaolaProfile val$kaolaProfile -> val$kaolaProfile
    com.kaolafm.opensdk.account.token.KaolaAccessToken val$accessToken -> val$accessToken
    com.kaolafm.ad.profile.AdvertisingProfile val$profile -> val$profile
    com.kaolafm.ad.di.module.AdCommonParamModule this$0 -> this$0
    115:131:void <init>(com.kaolafm.ad.di.module.AdCommonParamModule,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken,com.kaolafm.ad.profile.AdvertisingProfile) -> <init>
com.kaolafm.ad.di.module.AdCommonParamModule_IsUseHttpsFactory -> com.kaolafm.ad.a.b.d:
    com.kaolafm.ad.di.module.AdCommonParamModule module -> a
    javax.inject.Provider optionsProvider -> b
    14:17:void <init>(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider) -> <init>
    21:21:java.lang.Boolean get() -> get
    1026:1026:java.lang.Boolean provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider):26:26 -> get
    1026:1026:java.lang.Boolean get():21 -> get
    1035:1035:boolean proxyIsUseHttps(com.kaolafm.ad.di.module.AdCommonParamModule,com.kaolafm.opensdk.Options):35:35 -> get
    1035:1035:java.lang.Boolean provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider):26 -> get
    1035:1035:java.lang.Boolean get():21 -> get
    1152:1152:boolean com.kaolafm.ad.di.module.AdCommonParamModule.isUseHttps(com.kaolafm.opensdk.Options):152:152 -> get
    1152:1152:boolean proxyIsUseHttps(com.kaolafm.ad.di.module.AdCommonParamModule,com.kaolafm.opensdk.Options):35 -> get
    1152:1152:java.lang.Boolean provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider):26 -> get
    1152:1152:java.lang.Boolean get():21 -> get
    26:26:java.lang.Boolean provideInstance$2d516b72(javax.inject.Provider) -> a
    2035:2035:boolean proxyIsUseHttps(com.kaolafm.ad.di.module.AdCommonParamModule,com.kaolafm.opensdk.Options):35:35 -> a
    2035:2035:java.lang.Boolean provideInstance$2d516b72(javax.inject.Provider):26 -> a
    2152:2152:boolean com.kaolafm.ad.di.module.AdCommonParamModule.isUseHttps(com.kaolafm.opensdk.Options):152:152 -> a
    2152:2152:boolean proxyIsUseHttps(com.kaolafm.ad.di.module.AdCommonParamModule,com.kaolafm.opensdk.Options):35 -> a
    2152:2152:java.lang.Boolean provideInstance$2d516b72(javax.inject.Provider):26 -> a
    31:31:com.kaolafm.ad.di.module.AdCommonParamModule_IsUseHttpsFactory create(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider) -> create
    35:35:boolean proxyIsUseHttps$9cf4828(com.kaolafm.opensdk.Options) -> a
    3152:3152:boolean com.kaolafm.ad.di.module.AdCommonParamModule.isUseHttps(com.kaolafm.opensdk.Options):152:152 -> a
    3152:3152:boolean proxyIsUseHttps$9cf4828(com.kaolafm.opensdk.Options):35 -> a
    8:8:java.lang.Object get() -> get
com.kaolafm.ad.di.module.AdCommonParamModule_ProvideActiveParamFactory -> com.kaolafm.ad.a.b.e:
    com.kaolafm.ad.di.module.AdCommonParamModule module -> a
    javax.inject.Provider profileProvider -> b
    javax.inject.Provider kaolaProfileProvider -> c
    javax.inject.Provider accessTokenProvider -> d
    24:29:void <init>(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    33:33:java.lang.String get() -> get
    1041:1042:java.lang.String provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):41:42 -> get
    1041:1042:java.lang.String get():33 -> get
    1059:1060:java.lang.String proxyProvideActiveParam(com.kaolafm.ad.di.module.AdCommonParamModule,com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken):59:60 -> get
    1059:1060:java.lang.String provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):41 -> get
    1059:1060:java.lang.String get():33 -> get
    2053:2070:java.lang.String com.kaolafm.ad.di.module.AdCommonParamModule.provideActiveParam(com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken):53:70 -> get
    2053:2070:java.lang.String proxyProvideActiveParam(com.kaolafm.ad.di.module.AdCommonParamModule,com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken):60 -> get
    2053:2070:java.lang.String provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):41 -> get
    2053:2070:java.lang.String get():33 -> get
    41:42:java.lang.String provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> a
    3059:3060:java.lang.String proxyProvideActiveParam(com.kaolafm.ad.di.module.AdCommonParamModule,com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken):59:60 -> a
    3059:3060:java.lang.String provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):41 -> a
    4053:4070:java.lang.String com.kaolafm.ad.di.module.AdCommonParamModule.provideActiveParam(com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken):53:70 -> a
    4053:4070:java.lang.String proxyProvideActiveParam(com.kaolafm.ad.di.module.AdCommonParamModule,com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken):60 -> a
    4053:4070:java.lang.String provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):41 -> a
    50:50:com.kaolafm.ad.di.module.AdCommonParamModule_ProvideActiveParamFactory create(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> create
    59:60:java.lang.String proxyProvideActiveParam(com.kaolafm.ad.di.module.AdCommonParamModule,com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken) -> a
    5053:5070:java.lang.String com.kaolafm.ad.di.module.AdCommonParamModule.provideActiveParam(com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken):53:70 -> a
    5053:5070:java.lang.String proxyProvideActiveParam(com.kaolafm.ad.di.module.AdCommonParamModule,com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken):60 -> a
    11:11:java.lang.Object get() -> get
com.kaolafm.ad.di.module.AdCommonParamModule_ProvideBasicCommonParamFactory -> com.kaolafm.ad.a.b.f:
    com.kaolafm.ad.di.module.AdCommonParamModule module -> a
    javax.inject.Provider profileProvider -> b
    javax.inject.Provider kaolaProfileProvider -> c
    javax.inject.Provider accessTokenProvider -> d
    27:32:void <init>(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    36:36:java.util.Set get() -> get
    1044:1045:java.util.Set provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):44:45 -> get
    1044:1045:java.util.Set get():36 -> get
    1062:1063:java.util.Set proxyProvideBasicCommonParam(com.kaolafm.ad.di.module.AdCommonParamModule,com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken):62:63 -> get
    1062:1063:java.util.Set provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):44 -> get
    1062:1063:java.util.Set get():36 -> get
    1102:1109:java.util.Set com.kaolafm.ad.di.module.AdCommonParamModule.provideBasicCommonParam(com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken):102:109 -> get
    1102:1109:java.util.Set proxyProvideBasicCommonParam(com.kaolafm.ad.di.module.AdCommonParamModule,com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken):63 -> get
    1102:1109:java.util.Set provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):44 -> get
    1102:1109:java.util.Set get():36 -> get
    44:45:java.util.Set provideInstance$195e344(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> a
    2062:2063:java.util.Set proxyProvideBasicCommonParam$2191f67(com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken):62:63 -> a
    2062:2063:java.util.Set provideInstance$195e344(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):44 -> a
    2102:2109:java.util.Set com.kaolafm.ad.di.module.AdCommonParamModule.provideBasicCommonParam(com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken):102:109 -> a
    2102:2109:java.util.Set proxyProvideBasicCommonParam$2191f67(com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken):63 -> a
    2102:2109:java.util.Set provideInstance$195e344(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):44 -> a
    53:53:com.kaolafm.ad.di.module.AdCommonParamModule_ProvideBasicCommonParamFactory create(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> create
    62:63:java.util.Set proxyProvideBasicCommonParam$2191f67(com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken) -> a
    3102:3109:java.util.Set com.kaolafm.ad.di.module.AdCommonParamModule.provideBasicCommonParam(com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken):102:109 -> a
    3102:3109:java.util.Set proxyProvideBasicCommonParam$2191f67(com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken):63 -> a
    13:13:java.lang.Object get() -> get
com.kaolafm.ad.di.module.AdCommonParamModule_ProvideCommonParamFactory -> com.kaolafm.ad.a.b.g:
    com.kaolafm.ad.di.module.AdCommonParamModule module -> a
    javax.inject.Provider profileProvider -> b
    javax.inject.Provider kaolaProfileProvider -> c
    javax.inject.Provider basicParamsProvider -> d
    26:31:void <init>(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    35:35:java.util.Set get() -> get
    1043:1044:java.util.Set provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):43:44 -> get
    1043:1044:java.util.Set get():35 -> get
    1061:1062:java.util.Set proxyProvideCommonParam(com.kaolafm.ad.di.module.AdCommonParamModule,com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,java.util.Set):61:62 -> get
    1061:1062:java.util.Set provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):43 -> get
    1061:1062:java.util.Set get():35 -> get
    1083:1089:java.util.Set com.kaolafm.ad.di.module.AdCommonParamModule.provideCommonParam(com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,java.util.Set):83:89 -> get
    1083:1089:java.util.Set proxyProvideCommonParam(com.kaolafm.ad.di.module.AdCommonParamModule,com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,java.util.Set):62 -> get
    1083:1089:java.util.Set provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):43 -> get
    1083:1089:java.util.Set get():35 -> get
    43:44:java.util.Set provideInstance$195e344(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> a
    2061:2062:java.util.Set proxyProvideCommonParam$346cbaad(com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,java.util.Set):61:62 -> a
    2061:2062:java.util.Set provideInstance$195e344(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):43 -> a
    2083:2089:java.util.Set com.kaolafm.ad.di.module.AdCommonParamModule.provideCommonParam(com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,java.util.Set):83:89 -> a
    2083:2089:java.util.Set proxyProvideCommonParam$346cbaad(com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,java.util.Set):62 -> a
    2083:2089:java.util.Set provideInstance$195e344(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):43 -> a
    52:52:com.kaolafm.ad.di.module.AdCommonParamModule_ProvideCommonParamFactory create(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> create
    61:62:java.util.Set proxyProvideCommonParam$346cbaad(com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,java.util.Set) -> a
    3083:3089:java.util.Set com.kaolafm.ad.di.module.AdCommonParamModule.provideCommonParam(com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,java.util.Set):83:89 -> a
    3083:3089:java.util.Set proxyProvideCommonParam$346cbaad(com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,java.util.Set):62 -> a
    12:12:java.lang.Object get() -> get
com.kaolafm.ad.di.module.AdCommonParamModule_ProvideEngineHostFactory -> com.kaolafm.ad.a.b.h:
    com.kaolafm.ad.di.module.AdCommonParamModule module -> a
    javax.inject.Provider useHttpsProvider -> b
    14:17:void <init>(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider) -> <init>
    21:21:java.lang.String get() -> get
    1026:1026:java.lang.String provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider):26:26 -> get
    1026:1026:java.lang.String get():21 -> get
    1035:1036:java.lang.String proxyProvideEngineHost(com.kaolafm.ad.di.module.AdCommonParamModule,boolean):35:36 -> get
    1035:1036:java.lang.String provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider):26 -> get
    1035:1036:java.lang.String get():21 -> get
    1160:1160:java.lang.String com.kaolafm.ad.di.module.AdCommonParamModule.provideEngineHost(boolean):160:160 -> get
    1160:1160:java.lang.String proxyProvideEngineHost(com.kaolafm.ad.di.module.AdCommonParamModule,boolean):36 -> get
    1160:1160:java.lang.String provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider):26 -> get
    1160:1160:java.lang.String get():21 -> get
    26:26:java.lang.String provideInstance$283bf7f5(javax.inject.Provider) -> a
    2035:2036:java.lang.String proxyProvideEngineHost$6dd5b3e(boolean):35:36 -> a
    2035:2036:java.lang.String provideInstance$283bf7f5(javax.inject.Provider):26 -> a
    2160:2160:java.lang.String com.kaolafm.ad.di.module.AdCommonParamModule.provideEngineHost(boolean):160:160 -> a
    2160:2160:java.lang.String proxyProvideEngineHost$6dd5b3e(boolean):36 -> a
    2160:2160:java.lang.String provideInstance$283bf7f5(javax.inject.Provider):26 -> a
    31:31:com.kaolafm.ad.di.module.AdCommonParamModule_ProvideEngineHostFactory create(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider) -> create
    35:36:java.lang.String proxyProvideEngineHost$6dd5b3e(boolean) -> a
    3160:3160:java.lang.String com.kaolafm.ad.di.module.AdCommonParamModule.provideEngineHost(boolean):160:160 -> a
    3160:3160:java.lang.String proxyProvideEngineHost$6dd5b3e(boolean):36 -> a
    8:8:java.lang.Object get() -> get
com.kaolafm.ad.di.module.AdCommonParamModule_ProvideEngineParamsFactory -> com.kaolafm.ad.a.b.i:
    com.kaolafm.ad.di.module.AdCommonParamModule module -> a
    javax.inject.Provider paramsProvider -> b
    16:19:void <init>(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider) -> <init>
    23:23:java.util.Map get() -> get
    1028:1028:java.util.Map provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider):28:28 -> get
    1028:1028:java.util.Map get():23 -> get
    1038:1039:java.util.Map proxyProvideEngineParams(com.kaolafm.ad.di.module.AdCommonParamModule,java.util.Map):38:39 -> get
    1038:1039:java.util.Map provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider):28 -> get
    1038:1039:java.util.Map get():23 -> get
    1147:1147:java.util.Map com.kaolafm.ad.di.module.AdCommonParamModule.provideEngineParams(java.util.Map):147:147 -> get
    1147:1147:java.util.Map proxyProvideEngineParams(com.kaolafm.ad.di.module.AdCommonParamModule,java.util.Map):39 -> get
    1147:1147:java.util.Map provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider):28 -> get
    1147:1147:java.util.Map get():23 -> get
    28:28:java.util.Map provideInstance$5e27aeaa(javax.inject.Provider) -> a
    2038:2039:java.util.Map proxyProvideEngineParams$1d82c53c(java.util.Map):38:39 -> a
    2038:2039:java.util.Map provideInstance$5e27aeaa(javax.inject.Provider):28 -> a
    2147:2147:java.util.Map com.kaolafm.ad.di.module.AdCommonParamModule.provideEngineParams(java.util.Map):147:147 -> a
    2147:2147:java.util.Map proxyProvideEngineParams$1d82c53c(java.util.Map):39 -> a
    2147:2147:java.util.Map provideInstance$5e27aeaa(javax.inject.Provider):28 -> a
    33:33:com.kaolafm.ad.di.module.AdCommonParamModule_ProvideEngineParamsFactory create(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider) -> create
    38:39:java.util.Map proxyProvideEngineParams$1d82c53c(java.util.Map) -> a
    3147:3147:java.util.Map com.kaolafm.ad.di.module.AdCommonParamModule.provideEngineParams(java.util.Map):147:147 -> a
    3147:3147:java.util.Map proxyProvideEngineParams$1d82c53c(java.util.Map):39 -> a
    9:9:java.lang.Object get() -> get
com.kaolafm.ad.di.module.AdCommonParamModule_ProvideParamsFactory -> com.kaolafm.ad.a.b.j:
    com.kaolafm.ad.di.module.AdCommonParamModule module -> a
    javax.inject.Provider profileProvider -> b
    javax.inject.Provider kaolaProfileProvider -> c
    javax.inject.Provider accessTokenProvider -> d
    26:31:void <init>(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    35:35:java.util.Map get() -> get
    1043:1044:java.util.Map provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):43:44 -> get
    1043:1044:java.util.Map get():35 -> get
    1061:1062:java.util.Map proxyProvideParams(com.kaolafm.ad.di.module.AdCommonParamModule,com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken):61:62 -> get
    1061:1062:java.util.Map provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):43 -> get
    1061:1062:java.util.Map get():35 -> get
    1115:1115:java.util.Map com.kaolafm.ad.di.module.AdCommonParamModule.provideParams(com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken):115:115 -> get
    1115:1115:java.util.Map proxyProvideParams(com.kaolafm.ad.di.module.AdCommonParamModule,com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken):62 -> get
    1115:1115:java.util.Map provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):43 -> get
    1115:1115:java.util.Map get():35 -> get
    43:44:java.util.Map provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> a
    2061:2062:java.util.Map proxyProvideParams(com.kaolafm.ad.di.module.AdCommonParamModule,com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken):61:62 -> a
    2061:2062:java.util.Map provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):43 -> a
    2115:2115:java.util.Map com.kaolafm.ad.di.module.AdCommonParamModule.provideParams(com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken):115:115 -> a
    2115:2115:java.util.Map proxyProvideParams(com.kaolafm.ad.di.module.AdCommonParamModule,com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken):62 -> a
    2115:2115:java.util.Map provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):43 -> a
    52:52:com.kaolafm.ad.di.module.AdCommonParamModule_ProvideParamsFactory create(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> create
    61:62:java.util.Map proxyProvideParams(com.kaolafm.ad.di.module.AdCommonParamModule,com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken) -> a
    3115:3115:java.util.Map com.kaolafm.ad.di.module.AdCommonParamModule.provideParams(com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken):115:115 -> a
    3115:3115:java.util.Map proxyProvideParams(com.kaolafm.ad.di.module.AdCommonParamModule,com.kaolafm.ad.profile.AdvertisingProfile,com.kaolafm.opensdk.account.profile.KaolaProfile,com.kaolafm.opensdk.account.token.KaolaAccessToken):62 -> a
    12:12:java.lang.Object get() -> get
com.kaolafm.ad.di.module.AdCommonParamModule_ProvideReportHostFactory -> com.kaolafm.ad.a.b.k:
    com.kaolafm.ad.di.module.AdCommonParamModule module -> a
    javax.inject.Provider useHttpsProvider -> b
    14:17:void <init>(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider) -> <init>
    21:21:java.lang.String get() -> get
    1026:1026:java.lang.String provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider):26:26 -> get
    1026:1026:java.lang.String get():21 -> get
    1035:1036:java.lang.String proxyProvideReportHost(com.kaolafm.ad.di.module.AdCommonParamModule,boolean):35:36 -> get
    1035:1036:java.lang.String provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider):26 -> get
    1035:1036:java.lang.String get():21 -> get
    1168:1168:java.lang.String com.kaolafm.ad.di.module.AdCommonParamModule.provideReportHost(boolean):168:168 -> get
    1168:1168:java.lang.String proxyProvideReportHost(com.kaolafm.ad.di.module.AdCommonParamModule,boolean):36 -> get
    1168:1168:java.lang.String provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider):26 -> get
    1168:1168:java.lang.String get():21 -> get
    26:26:java.lang.String provideInstance$283bf7f5(javax.inject.Provider) -> a
    2035:2036:java.lang.String proxyProvideReportHost$6dd5b3e(boolean):35:36 -> a
    2035:2036:java.lang.String provideInstance$283bf7f5(javax.inject.Provider):26 -> a
    2168:2168:java.lang.String com.kaolafm.ad.di.module.AdCommonParamModule.provideReportHost(boolean):168:168 -> a
    2168:2168:java.lang.String proxyProvideReportHost$6dd5b3e(boolean):36 -> a
    2168:2168:java.lang.String provideInstance$283bf7f5(javax.inject.Provider):26 -> a
    31:31:com.kaolafm.ad.di.module.AdCommonParamModule_ProvideReportHostFactory create(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider) -> create
    35:36:java.lang.String proxyProvideReportHost$6dd5b3e(boolean) -> a
    3168:3168:java.lang.String com.kaolafm.ad.di.module.AdCommonParamModule.provideReportHost(boolean):168:168 -> a
    3168:3168:java.lang.String proxyProvideReportHost$6dd5b3e(boolean):36 -> a
    8:8:java.lang.Object get() -> get
com.kaolafm.ad.di.module.AdCommonParamModule_ProvideSocketHostFactory -> com.kaolafm.ad.a.b.l:
    com.kaolafm.ad.di.module.AdCommonParamModule module -> a
    javax.inject.Provider useHttpsProvider -> b
    14:17:void <init>(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider) -> <init>
    21:21:java.lang.String get() -> get
    1026:1026:java.lang.String provideInstance(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider):26:26 -> get
    1026:1026:java.lang.String get():21 -> get
    26:26:java.lang.String provideInstance$283bf7f5(javax.inject.Provider) -> a
    31:31:com.kaolafm.ad.di.module.AdCommonParamModule_ProvideSocketHostFactory create(com.kaolafm.ad.di.module.AdCommonParamModule,javax.inject.Provider) -> create
    35:36:java.lang.String proxyProvideSocketHost$6dd5b3e(boolean) -> a
    8:8:java.lang.Object get() -> get
com.kaolafm.ad.di.module.AdvertConfigModule -> com.kaolafm.ad.a.b.m:
    29:29:void <init>() -> <init>
    34:34:com.kaolafm.ad.profile.AdvertisingProfile provideAdvertisingProfile(com.kaolafm.ad.profile.AdProfileManager) -> a
    com.kaolafm.ad.expose.Adapter provideImagerAdapter$1ccbe74e() -> a
    com.kaolafm.ad.expose.Adapter providePlayerAdapter$7971ff1c() -> b
    com.kaolafm.ad.expose.Adapter provideCompositeAdapter$e93e682() -> c
    com.kaolafm.ad.timer.Timer provideTimer$1edb94fc() -> d
    66:66:com.kaolafm.opensdk.Options provideAdvertOptions(com.kaolafm.ad.AdvertOptions) -> a
com.kaolafm.ad.di.module.AdvertConfigModule_ProvideAdvertOptionsFactory -> com.kaolafm.ad.a.b.n:
    javax.inject.Provider optionsProvider -> a
    13:15:void <init>(javax.inject.Provider) -> <init>
    19:19:com.kaolafm.opensdk.Options get() -> get
    1023:1023:com.kaolafm.opensdk.Options provideInstance(javax.inject.Provider):23:23 -> get
    1023:1023:com.kaolafm.opensdk.Options get():19 -> get
    23:23:com.kaolafm.opensdk.Options provideInstance(javax.inject.Provider) -> a
    28:28:com.kaolafm.ad.di.module.AdvertConfigModule_ProvideAdvertOptionsFactory create(javax.inject.Provider) -> create
    32:33:com.kaolafm.opensdk.Options proxyProvideAdvertOptions(com.kaolafm.ad.AdvertOptions) -> a
    1066:1066:com.kaolafm.opensdk.Options com.kaolafm.ad.di.module.AdvertConfigModule.provideAdvertOptions(com.kaolafm.ad.AdvertOptions):66:66 -> a
    1066:1066:com.kaolafm.opensdk.Options proxyProvideAdvertOptions(com.kaolafm.ad.AdvertOptions):33 -> a
    10:10:java.lang.Object get() -> get
com.kaolafm.ad.di.module.AdvertConfigModule_ProvideAdvertisingProfileFactory -> com.kaolafm.ad.a.b.o:
    javax.inject.Provider managerProvider -> a
    15:17:void <init>(javax.inject.Provider) -> <init>
    21:21:com.kaolafm.ad.profile.AdvertisingProfile get() -> get
    1025:1025:com.kaolafm.ad.profile.AdvertisingProfile provideInstance(javax.inject.Provider):25:25 -> get
    1025:1025:com.kaolafm.ad.profile.AdvertisingProfile get():21 -> get
    1034:1035:com.kaolafm.ad.profile.AdvertisingProfile proxyProvideAdvertisingProfile(com.kaolafm.ad.profile.AdProfileManager):34:35 -> get
    1034:1035:com.kaolafm.ad.profile.AdvertisingProfile provideInstance(javax.inject.Provider):25 -> get
    1034:1035:com.kaolafm.ad.profile.AdvertisingProfile get():21 -> get
    2034:2034:com.kaolafm.ad.profile.AdvertisingProfile com.kaolafm.ad.di.module.AdvertConfigModule.provideAdvertisingProfile(com.kaolafm.ad.profile.AdProfileManager):34:34 -> get
    2034:2034:com.kaolafm.ad.profile.AdvertisingProfile proxyProvideAdvertisingProfile(com.kaolafm.ad.profile.AdProfileManager):35 -> get
    2034:2034:com.kaolafm.ad.profile.AdvertisingProfile provideInstance(javax.inject.Provider):25 -> get
    2034:2034:com.kaolafm.ad.profile.AdvertisingProfile get():21 -> get
    25:25:com.kaolafm.ad.profile.AdvertisingProfile provideInstance(javax.inject.Provider) -> a
    3034:3035:com.kaolafm.ad.profile.AdvertisingProfile proxyProvideAdvertisingProfile(com.kaolafm.ad.profile.AdProfileManager):34:35 -> a
    3034:3035:com.kaolafm.ad.profile.AdvertisingProfile provideInstance(javax.inject.Provider):25 -> a
    4034:4034:com.kaolafm.ad.profile.AdvertisingProfile com.kaolafm.ad.di.module.AdvertConfigModule.provideAdvertisingProfile(com.kaolafm.ad.profile.AdProfileManager):34:34 -> a
    4034:4034:com.kaolafm.ad.profile.AdvertisingProfile proxyProvideAdvertisingProfile(com.kaolafm.ad.profile.AdProfileManager):35 -> a
    4034:4034:com.kaolafm.ad.profile.AdvertisingProfile provideInstance(javax.inject.Provider):25 -> a
    30:30:com.kaolafm.ad.di.module.AdvertConfigModule_ProvideAdvertisingProfileFactory create(javax.inject.Provider) -> create
    34:35:com.kaolafm.ad.profile.AdvertisingProfile proxyProvideAdvertisingProfile(com.kaolafm.ad.profile.AdProfileManager) -> a
    5034:5034:com.kaolafm.ad.profile.AdvertisingProfile com.kaolafm.ad.di.module.AdvertConfigModule.provideAdvertisingProfile(com.kaolafm.ad.profile.AdProfileManager):34:34 -> a
    5034:5034:com.kaolafm.ad.profile.AdvertisingProfile proxyProvideAdvertisingProfile(com.kaolafm.ad.profile.AdProfileManager):35 -> a
    10:10:java.lang.Object get() -> get
com.kaolafm.ad.di.module.EmptyModule -> com.kaolafm.ad.a.b.p:
    14:14:void <init>() -> <init>
    18:18:com.kaolafm.opensdk.http.core.TokenRefresh provideEmptyTokenRefresh() -> a
com.kaolafm.ad.di.module.EmptyModule$1 -> com.kaolafm.ad.a.b.q:
    18:18:void <init>() -> <init>
    21:21:io.reactivex.Single refresh() -> a
    27:27:void logout() -> b
com.kaolafm.ad.di.module.EmptyModule_ProvideEmptyTokenRefreshFactory -> com.kaolafm.ad.a.b.r:
    com.kaolafm.ad.di.module.EmptyModule_ProvideEmptyTokenRefreshFactory INSTANCE -> a
    8:8:void <init>() -> <init>
    14:14:com.kaolafm.opensdk.http.core.TokenRefresh get() -> get
    1018:1018:com.kaolafm.opensdk.http.core.TokenRefresh provideInstance():18:18 -> get
    1018:1018:com.kaolafm.opensdk.http.core.TokenRefresh get():14 -> get
    1027:1028:com.kaolafm.opensdk.http.core.TokenRefresh proxyProvideEmptyTokenRefresh():26:27 -> get
    1027:1028:com.kaolafm.opensdk.http.core.TokenRefresh provideInstance():18 -> get
    1027:1028:com.kaolafm.opensdk.http.core.TokenRefresh get():14 -> get
    2018:2018:com.kaolafm.opensdk.http.core.TokenRefresh com.kaolafm.ad.di.module.EmptyModule.provideEmptyTokenRefresh():18:18 -> get
    2018:2018:com.kaolafm.opensdk.http.core.TokenRefresh proxyProvideEmptyTokenRefresh():27 -> get
    2018:2018:com.kaolafm.opensdk.http.core.TokenRefresh provideInstance():18 -> get
    2018:2018:com.kaolafm.opensdk.http.core.TokenRefresh get():14 -> get
    18:18:com.kaolafm.opensdk.http.core.TokenRefresh provideInstance() -> a
    2027:2028:com.kaolafm.opensdk.http.core.TokenRefresh proxyProvideEmptyTokenRefresh():26:27 -> a
    2027:2028:com.kaolafm.opensdk.http.core.TokenRefresh provideInstance():18 -> a
    3018:3018:com.kaolafm.opensdk.http.core.TokenRefresh com.kaolafm.ad.di.module.EmptyModule.provideEmptyTokenRefresh():18:18 -> a
    3018:3018:com.kaolafm.opensdk.http.core.TokenRefresh proxyProvideEmptyTokenRefresh():27 -> a
    3018:3018:com.kaolafm.opensdk.http.core.TokenRefresh provideInstance():18 -> a
    22:22:com.kaolafm.ad.di.module.EmptyModule_ProvideEmptyTokenRefreshFactory create() -> create
    26:27:com.kaolafm.opensdk.http.core.TokenRefresh proxyProvideEmptyTokenRefresh() -> b
    4018:4018:com.kaolafm.opensdk.http.core.TokenRefresh com.kaolafm.ad.di.module.EmptyModule.provideEmptyTokenRefresh():18:18 -> b
    4018:4018:com.kaolafm.opensdk.http.core.TokenRefresh proxyProvideEmptyTokenRefresh():27 -> b
    8:8:java.lang.Object get() -> get
    9:9:void <clinit>() -> <clinit>
com.kaolafm.ad.di.qualifier.AdEngineParam -> com.kaolafm.ad.a.c.a:
com.kaolafm.ad.di.qualifier.AdInitParam -> com.kaolafm.ad.a.c.b:
com.kaolafm.ad.di.qualifier.AdReportParam -> com.kaolafm.ad.a.c.c:
com.kaolafm.ad.di.qualifier.AdvertAdapterQualifier -> com.kaolafm.ad.a.c.d:
com.kaolafm.ad.di.qualifier.AdvertHostQualifier -> com.kaolafm.ad.a.c.e:
com.kaolafm.ad.di.qualifier.BackParam -> com.kaolafm.ad.a.c.f:
com.kaolafm.ad.di.qualifier.BasicParam -> com.kaolafm.ad.a.c.g:
com.kaolafm.ad.di.qualifier.CommonParam -> com.kaolafm.ad.a.c.h:
com.kaolafm.ad.di.scope.AdRequestScope -> com.kaolafm.ad.a.d.a:
com.kaolafm.ad.di.scope.AdScope -> com.kaolafm.ad.a.d.b:
com.kaolafm.ad.expose.Adapter -> com.kaolafm.ad.expose.a:
    boolean accept(com.kaolafm.ad.api.model.Advert) -> a
    void expose(com.kaolafm.ad.api.model.Advert) -> b
    void close(com.kaolafm.ad.api.model.Advert) -> c
    java.lang.Object getExecutor() -> a
    void setExecutor(java.lang.Object) -> a
com.kaolafm.ad.expose.AdvertExposeChain -> com.kaolafm.ad.expose.b:
    java.util.List mInterceptors -> a
    int mIndex -> b
    com.kaolafm.ad.api.model.Advert mAdvert -> c
    com.kaolafm.ad.expose.AdvertisingManager mManager -> d
    com.kaolafm.ad.expose.AdvertInterceptor mInterceptor -> e
    22:26:void <init>(com.kaolafm.ad.expose.AdvertisingManager) -> <init>
    29:30:void addInterceptor(com.kaolafm.ad.expose.AdvertInterceptor) -> a
    33:34:void addInterceptors(java.util.List) -> a
    38:38:com.kaolafm.ad.api.model.Advert advert() -> advert
    43:60:void process(com.kaolafm.ad.api.model.Advert) -> process
    64:65:void error(java.lang.Exception) -> error
com.kaolafm.ad.expose.AdvertInterceptor -> com.kaolafm.ad.expose.AdvertInterceptor:
    void intercept(com.kaolafm.ad.expose.AdvertInterceptor$Chain) -> intercept
com.kaolafm.ad.expose.AdvertInterceptor$Chain -> com.kaolafm.ad.expose.AdvertInterceptor$Chain:
    com.kaolafm.ad.api.model.Advert advert() -> advert
    void process(com.kaolafm.ad.api.model.Advert) -> process
    void error(java.lang.Exception) -> error
com.kaolafm.ad.expose.AdvertRepository -> com.kaolafm.ad.expose.c:
    com.kaolafm.ad.api.internal.AdInternalRequest mRequest -> a
    com.kaolafm.ad.db.manager.AdvertDBManager mAdvertDBManager -> b
    39:40:void <init>() -> <init>
    60:63:void getPreloadingAdvert(java.lang.String,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> a
    1100:1112:void loadAdvert(java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback):100:112 -> a
    1100:1112:void getPreloadingAdvert(java.lang.String,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback):60 -> a
    2075:2091:void preloadAdvert(java.lang.String,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String):75:91 -> a
    2075:2091:void getPreloadingAdvert(java.lang.String,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback):62 -> a
    75:91:void preloadAdvert(java.lang.String,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String) -> a
    100:112:void loadAdvert(java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> a
    140:141:void getAdvertisingList(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> a
    149:163:void delete(com.kaolafm.ad.api.model.Advert) -> a
    2172:2173:void delete(java.lang.String,java.lang.String,java.lang.String):172:173 -> a
    2172:2173:void delete(com.kaolafm.ad.api.model.Advert):152 -> a
    3172:3173:void delete(java.lang.String,java.lang.String,java.lang.String):172:173 -> a
    3172:3173:void delete(com.kaolafm.ad.api.model.Advert):156 -> a
    4172:4173:void delete(java.lang.String,java.lang.String,java.lang.String):172:173 -> a
    4172:4173:void delete(com.kaolafm.ad.api.model.Advert):160 -> a
    172:173:void delete(java.lang.String,java.lang.String,java.lang.String) -> a
    101:111:void lambda$loadAdvert$0(com.kaolafm.opensdk.http.core.HttpCallback,java.util.List) -> a
com.kaolafm.ad.expose.AdvertRepository$1 -> com.kaolafm.ad.expose.d:
    int val$subtype -> a
    com.kaolafm.ad.expose.AdvertRepository this$0 -> b
    75:75:void <init>(com.kaolafm.ad.expose.AdvertRepository,int) -> <init>
    78:84:void onSuccess(java.util.List) -> a
    89:89:void onError$626d7010() -> a
    75:75:void onSuccess(java.lang.Object) -> a
    1078:1084:void onSuccess(java.util.List):78:84 -> a
    1078:1084:void onSuccess(java.lang.Object):75 -> a
com.kaolafm.ad.expose.AdvertRepository_Factory -> com.kaolafm.ad.expose.e:
    javax.inject.Provider mRequestProvider -> a
    javax.inject.Provider mAdvertDBManagerProvider -> b
    16:19:void <init>(javax.inject.Provider,javax.inject.Provider) -> <init>
    23:23:com.kaolafm.ad.expose.AdvertRepository get() -> get
    1029:1033:com.kaolafm.ad.expose.AdvertRepository provideInstance(javax.inject.Provider,javax.inject.Provider):29:33 -> get
    1029:1033:com.kaolafm.ad.expose.AdvertRepository get():23 -> get
    1034:1035:void com.kaolafm.ad.expose.AdvertRepository_MembersInjector.injectMRequest(com.kaolafm.ad.expose.AdvertRepository,com.kaolafm.ad.api.internal.AdInternalRequest):34:35 -> get
    1034:1035:com.kaolafm.ad.expose.AdvertRepository provideInstance(javax.inject.Provider,javax.inject.Provider):30 -> get
    1034:1035:com.kaolafm.ad.expose.AdvertRepository get():23 -> get
    1039:1040:void com.kaolafm.ad.expose.AdvertRepository_MembersInjector.injectMAdvertDBManager(com.kaolafm.ad.expose.AdvertRepository,com.kaolafm.ad.db.manager.AdvertDBManager):39:40 -> get
    1039:1040:com.kaolafm.ad.expose.AdvertRepository provideInstance(javax.inject.Provider,javax.inject.Provider):31 -> get
    1039:1040:com.kaolafm.ad.expose.AdvertRepository get():23 -> get
    29:33:com.kaolafm.ad.expose.AdvertRepository provideInstance(javax.inject.Provider,javax.inject.Provider) -> a
    2034:2035:void com.kaolafm.ad.expose.AdvertRepository_MembersInjector.injectMRequest(com.kaolafm.ad.expose.AdvertRepository,com.kaolafm.ad.api.internal.AdInternalRequest):34:35 -> a
    2034:2035:com.kaolafm.ad.expose.AdvertRepository provideInstance(javax.inject.Provider,javax.inject.Provider):30 -> a
    2039:2040:void com.kaolafm.ad.expose.AdvertRepository_MembersInjector.injectMAdvertDBManager(com.kaolafm.ad.expose.AdvertRepository,com.kaolafm.ad.db.manager.AdvertDBManager):39:40 -> a
    2039:2040:com.kaolafm.ad.expose.AdvertRepository provideInstance(javax.inject.Provider,javax.inject.Provider):31 -> a
    39:39:com.kaolafm.ad.expose.AdvertRepository_Factory create(javax.inject.Provider,javax.inject.Provider) -> create
    43:43:com.kaolafm.ad.expose.AdvertRepository newAdvertRepository() -> a
    9:9:java.lang.Object get() -> get
com.kaolafm.ad.expose.AdvertRepository_MembersInjector -> com.kaolafm.ad.expose.f:
    javax.inject.Provider mRequestProvider -> a
    javax.inject.Provider mAdvertDBManagerProvider -> b
    16:19:void <init>(javax.inject.Provider,javax.inject.Provider) -> <init>
    24:24:dagger.MembersInjector create(javax.inject.Provider,javax.inject.Provider) -> a
    29:31:void injectMembers(com.kaolafm.ad.expose.AdvertRepository) -> a
    1034:1035:void injectMRequest(com.kaolafm.ad.expose.AdvertRepository,com.kaolafm.ad.api.internal.AdInternalRequest):34:35 -> a
    1034:1035:void injectMembers(com.kaolafm.ad.expose.AdvertRepository):29 -> a
    1039:1040:void injectMAdvertDBManager(com.kaolafm.ad.expose.AdvertRepository,com.kaolafm.ad.db.manager.AdvertDBManager):39:40 -> a
    1039:1040:void injectMembers(com.kaolafm.ad.expose.AdvertRepository):30 -> a
    34:35:void injectMRequest(com.kaolafm.ad.expose.AdvertRepository,com.kaolafm.ad.api.internal.AdInternalRequest) -> a
    39:40:void injectMAdvertDBManager(com.kaolafm.ad.expose.AdvertRepository,com.kaolafm.ad.db.manager.AdvertDBManager) -> a
    9:9:void injectMembers(java.lang.Object) -> a
    2029:2031:void injectMembers(com.kaolafm.ad.expose.AdvertRepository):29:31 -> a
    2029:2031:void injectMembers(java.lang.Object):9 -> a
    2034:2035:void injectMRequest(com.kaolafm.ad.expose.AdvertRepository,com.kaolafm.ad.api.internal.AdInternalRequest):34:35 -> a
    2034:2035:void injectMembers(com.kaolafm.ad.expose.AdvertRepository):29 -> a
    2034:2035:void injectMembers(java.lang.Object):9 -> a
    2039:2040:void injectMAdvertDBManager(com.kaolafm.ad.expose.AdvertRepository,com.kaolafm.ad.db.manager.AdvertDBManager):39:40 -> a
    2039:2040:void injectMembers(com.kaolafm.ad.expose.AdvertRepository):30 -> a
    2039:2040:void injectMembers(java.lang.Object):9 -> a
com.kaolafm.ad.expose.AdvertisingImager -> com.kaolafm.ad.expose.AdvertisingImager:
    void display(com.kaolafm.ad.api.model.ImageAdvert) -> display
    void hide(com.kaolafm.ad.api.model.ImageAdvert) -> hide
    void skip(com.kaolafm.ad.api.model.ImageAdvert) -> skip
    void displayInteraction(com.kaolafm.ad.api.model.InteractionAdvert) -> displayInteraction
    void hideInteraction(com.kaolafm.ad.api.model.InteractionAdvert) -> hideInteraction
    void click(com.kaolafm.ad.api.model.InteractionAdvert) -> click
com.kaolafm.ad.expose.AdvertisingImagerAdapter -> com.kaolafm.ad.expose.g:
    com.kaolafm.ad.expose.AdvertisingImager mImager -> a
    23:24:void <init>() -> <init>
    28:28:boolean accept(com.kaolafm.ad.api.model.Advert) -> a
    33:40:void expose(com.kaolafm.ad.api.model.Advert) -> b
    44:55:void close(com.kaolafm.ad.api.model.Advert) -> c
    59:59:com.kaolafm.ad.expose.AdvertisingImager getExecutor() -> b
    64:65:void setExecutor(com.kaolafm.ad.expose.AdvertisingImager) -> a
    69:72:void error(java.lang.String,int,com.kaolafm.opensdk.http.error.ApiException) -> error
    17:17:void setExecutor(java.lang.Object) -> a
    1064:1065:void setExecutor(com.kaolafm.ad.expose.AdvertisingImager):64:65 -> a
    1064:1065:void setExecutor(java.lang.Object):17 -> a
    17:17:java.lang.Object getExecutor() -> a
    2059:2059:com.kaolafm.ad.expose.AdvertisingImager getExecutor():59:59 -> a
    2059:2059:java.lang.Object getExecutor():17 -> a
com.kaolafm.ad.expose.AdvertisingImagerAdapter_Factory -> com.kaolafm.ad.expose.h:
    com.kaolafm.ad.expose.AdvertisingImagerAdapter_Factory INSTANCE -> a
    6:6:void <init>() -> <init>
    12:12:com.kaolafm.ad.expose.AdvertisingImagerAdapter get() -> get
    1016:1016:com.kaolafm.ad.expose.AdvertisingImagerAdapter provideInstance():16:16 -> get
    1016:1016:com.kaolafm.ad.expose.AdvertisingImagerAdapter get():12 -> get
    16:16:com.kaolafm.ad.expose.AdvertisingImagerAdapter provideInstance() -> a
    20:20:com.kaolafm.ad.expose.AdvertisingImagerAdapter_Factory create() -> create
    24:24:com.kaolafm.ad.expose.AdvertisingImagerAdapter newAdvertisingImagerAdapter() -> b
    6:6:java.lang.Object get() -> get
    7:7:void <clinit>() -> <clinit>
com.kaolafm.ad.expose.AdvertisingLifecycleCallback -> com.kaolafm.ad.expose.AdvertisingLifecycleCallback:
    void onCreate(java.lang.String,int) -> onCreate
    void onStart(com.kaolafm.ad.api.model.Advert) -> onStart
    void onClose(com.kaolafm.ad.api.model.Advert) -> onClose
    void onError(java.lang.String,int,java.lang.Exception) -> onError
com.kaolafm.ad.expose.AdvertisingManager -> com.kaolafm.ad.expose.AdvertisingManager:
    java.lang.String IMAGER -> c
    java.lang.String PLAYER -> d
    java.lang.String COMPOSITE -> e
    com.kaolafm.ad.expose.AdvertisingManager mInstance -> f
    com.kaolafm.ad.expose.AdvertisingReporter mReporter -> g
    java.util.List mLifecycleCallbacks -> h
    java.util.List mInterceptors -> i
    java.util.Map mAdapters -> a
    com.kaolafm.ad.expose.AdvertRepository mAdvertRepository -> b
    41:54:void <init>() -> <init>
    57:64:com.kaolafm.ad.expose.AdvertisingManager getInstance() -> getInstance
    68:71:void addInterceptor(com.kaolafm.ad.expose.AdvertInterceptor) -> addInterceptor
    74:77:void removeInterceptor(com.kaolafm.ad.expose.AdvertInterceptor) -> removeInterceptor
    85:89:void expose(com.kaolafm.ad.api.model.Advert) -> expose
    99:101:void expose(com.kaolafm.ad.api.model.AdvertisingDetails) -> expose
    129:146:void expose(java.lang.String,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String) -> expose
    1140:1141:void com.kaolafm.ad.expose.AdvertRepository.getAdvertisingList(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback):140:141 -> expose
    1140:1141:void expose(java.lang.String,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String):130 -> expose
    172:173:void expose(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String) -> expose
    201:213:void exposePreloading(java.lang.String,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String) -> exposePreloading
    239:240:void exposePreloading(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String) -> exposePreloading
    248:250:void close(com.kaolafm.ad.api.model.Advert) -> close
    257:269:void onlyClose(com.kaolafm.ad.api.model.Advert) -> onlyClose
    1359:1368:void dispatchAdvertClose(com.kaolafm.ad.api.model.Advert):359:368 -> onlyClose
    1359:1368:void onlyClose(com.kaolafm.ad.api.model.Advert):268 -> onlyClose
    276:277:void clearCache(com.kaolafm.ad.api.model.Advert) -> clearCache
    2149:2163:void com.kaolafm.ad.expose.AdvertRepository.delete(com.kaolafm.ad.api.model.Advert):149:163 -> clearCache
    2149:2163:void clearCache(com.kaolafm.ad.api.model.Advert):276 -> clearCache
    2172:2173:void com.kaolafm.ad.expose.AdvertRepository.delete(java.lang.String,java.lang.String,java.lang.String):172:173 -> clearCache
    2172:2173:void com.kaolafm.ad.expose.AdvertRepository.delete(com.kaolafm.ad.api.model.Advert):152 -> clearCache
    2172:2173:void clearCache(com.kaolafm.ad.api.model.Advert):276 -> clearCache
    3172:3173:void com.kaolafm.ad.expose.AdvertRepository.delete(java.lang.String,java.lang.String,java.lang.String):172:173 -> clearCache
    3172:3173:void com.kaolafm.ad.expose.AdvertRepository.delete(com.kaolafm.ad.api.model.Advert):156 -> clearCache
    3172:3173:void clearCache(com.kaolafm.ad.api.model.Advert):276 -> clearCache
    4172:4173:void com.kaolafm.ad.expose.AdvertRepository.delete(java.lang.String,java.lang.String,java.lang.String):172:173 -> clearCache
    4172:4173:void com.kaolafm.ad.expose.AdvertRepository.delete(com.kaolafm.ad.api.model.Advert):160 -> clearCache
    4172:4173:void clearCache(com.kaolafm.ad.api.model.Advert):276 -> clearCache
    285:286:void close(com.kaolafm.ad.api.model.AdvertisingDetails) -> close
    292:293:void close() -> close
    299:303:void error(java.lang.String,int,com.kaolafm.opensdk.http.error.ApiException) -> error
    306:306:com.kaolafm.ad.expose.AdvertisingImager getImager() -> getImager
    310:311:void setImager(com.kaolafm.ad.expose.AdvertisingImager) -> setImager
    314:314:com.kaolafm.ad.expose.AdvertisingPlayer getPlayer() -> getPlayer
    318:319:void setPlayer(com.kaolafm.ad.expose.AdvertisingPlayer) -> setPlayer
    322:322:com.kaolafm.ad.expose.AdvertisingReporter getReporter() -> getReporter
    326:327:void setReporter(com.kaolafm.ad.expose.AdvertisingReporter) -> setReporter
    330:334:void registerAdvertLifecycleCallback(com.kaolafm.ad.expose.AdvertisingLifecycleCallback) -> registerAdvertLifecycleCallback
    337:340:void unregisterAdvertLifecycleCallback(com.kaolafm.ad.expose.AdvertisingLifecycleCallback) -> unregisterAdvertLifecycleCallback
    343:348:void dispatchAdvertCreate(java.lang.String,int) -> a
    351:356:void dispatchAdvertStart(com.kaolafm.ad.api.model.Advert) -> a
    359:368:void dispatchAdvertClose(com.kaolafm.ad.api.model.Advert) -> b
    371:376:void dispatchAdvertError(java.lang.String,int,java.lang.Exception) -> a
    27:27:void access$100(com.kaolafm.ad.expose.AdvertisingManager,com.kaolafm.ad.api.model.Advert) -> a
    4351:4356:void dispatchAdvertStart(com.kaolafm.ad.api.model.Advert):351:356 -> a
    4351:4356:void access$100(com.kaolafm.ad.expose.AdvertisingManager,com.kaolafm.ad.api.model.Advert):27 -> a
com.kaolafm.ad.expose.AdvertisingManager$1 -> com.kaolafm.ad.expose.i:
    int val$subtype -> a
    java.lang.String val$adZoneId -> b
    com.kaolafm.ad.expose.AdvertisingManager this$0 -> c
    130:130:void <init>(com.kaolafm.ad.expose.AdvertisingManager,int,java.lang.String) -> <init>
    133:139:void onSuccess(java.util.List) -> a
    143:144:void onError(com.kaolafm.opensdk.http.error.ApiException) -> a
    130:130:void onSuccess(java.lang.Object) -> a
    1133:1139:void onSuccess(java.util.List):133:139 -> a
    1133:1139:void onSuccess(java.lang.Object):130 -> a
com.kaolafm.ad.expose.AdvertisingManager$2 -> com.kaolafm.ad.expose.j:
    java.lang.String val$adZoneId -> a
    int val$subtype -> b
    com.kaolafm.ad.expose.AdvertisingManager this$0 -> c
    202:202:void <init>(com.kaolafm.ad.expose.AdvertisingManager,java.lang.String,int) -> <init>
    205:206:void onSuccess(com.kaolafm.ad.api.model.Advert) -> a
    210:211:void onError(com.kaolafm.opensdk.http.error.ApiException) -> a
    202:202:void onSuccess(java.lang.Object) -> a
    1205:1206:void onSuccess(com.kaolafm.ad.api.model.Advert):205:206 -> a
    1205:1206:void onSuccess(java.lang.Object):202 -> a
com.kaolafm.ad.expose.AdvertisingManager$RealExposeInterceptor -> com.kaolafm.ad.expose.AdvertisingManager$a:
    com.kaolafm.ad.expose.AdvertisingManager this$0 -> a
    377:377:void <init>(com.kaolafm.ad.expose.AdvertisingManager) -> <init>
    381:388:void intercept(com.kaolafm.ad.expose.AdvertInterceptor$Chain) -> intercept
    377:377:void <init>(com.kaolafm.ad.expose.AdvertisingManager,byte) -> <init>
com.kaolafm.ad.expose.AdvertisingManager_MembersInjector -> com.kaolafm.ad.expose.k:
    javax.inject.Provider mAdaptersProvider -> a
    javax.inject.Provider mAdvertRepositoryProvider -> b
    16:19:void <init>(javax.inject.Provider,javax.inject.Provider) -> <init>
    24:24:dagger.MembersInjector create(javax.inject.Provider,javax.inject.Provider) -> a
    29:31:void injectMembers(com.kaolafm.ad.expose.AdvertisingManager) -> a
    1034:1035:void injectMAdapters(com.kaolafm.ad.expose.AdvertisingManager,java.util.Map):34:35 -> a
    1034:1035:void injectMembers(com.kaolafm.ad.expose.AdvertisingManager):29 -> a
    1039:1040:void injectMAdvertRepository(com.kaolafm.ad.expose.AdvertisingManager,com.kaolafm.ad.expose.AdvertRepository):39:40 -> a
    1039:1040:void injectMembers(com.kaolafm.ad.expose.AdvertisingManager):30 -> a
    34:35:void injectMAdapters(com.kaolafm.ad.expose.AdvertisingManager,java.util.Map) -> a
    39:40:void injectMAdvertRepository(com.kaolafm.ad.expose.AdvertisingManager,com.kaolafm.ad.expose.AdvertRepository) -> a
    8:8:void injectMembers(java.lang.Object) -> a
    2029:2031:void injectMembers(com.kaolafm.ad.expose.AdvertisingManager):29:31 -> a
    2029:2031:void injectMembers(java.lang.Object):8 -> a
    2034:2035:void injectMAdapters(com.kaolafm.ad.expose.AdvertisingManager,java.util.Map):34:35 -> a
    2034:2035:void injectMembers(com.kaolafm.ad.expose.AdvertisingManager):29 -> a
    2034:2035:void injectMembers(java.lang.Object):8 -> a
    2039:2040:void injectMAdvertRepository(com.kaolafm.ad.expose.AdvertisingManager,com.kaolafm.ad.expose.AdvertRepository):39:40 -> a
    2039:2040:void injectMembers(com.kaolafm.ad.expose.AdvertisingManager):30 -> a
    2039:2040:void injectMembers(java.lang.Object):8 -> a
com.kaolafm.ad.expose.AdvertisingPlayer -> com.kaolafm.ad.expose.AdvertisingPlayer:
    void play(com.kaolafm.ad.api.model.AudioAdvert) -> play
    void stop(com.kaolafm.ad.api.model.AudioAdvert) -> stop
    void pause(com.kaolafm.ad.api.model.AudioAdvert) -> pause
com.kaolafm.ad.expose.AdvertisingPlayerAdapter -> com.kaolafm.ad.expose.l:
    com.kaolafm.ad.expose.AdvertisingPlayer mPlayer -> a
    23:24:void <init>() -> <init>
    28:28:boolean accept(com.kaolafm.ad.api.model.Advert) -> a
    33:36:void expose(com.kaolafm.ad.api.model.Advert) -> b
    40:43:void close(com.kaolafm.ad.api.model.Advert) -> c
    47:47:com.kaolafm.ad.expose.AdvertisingPlayer getExecutor() -> b
    52:53:void setExecutor(com.kaolafm.ad.expose.AdvertisingPlayer) -> a
    57:60:void error(java.lang.String,int,com.kaolafm.opensdk.http.error.ApiException) -> error
    16:16:void setExecutor(java.lang.Object) -> a
    1052:1053:void setExecutor(com.kaolafm.ad.expose.AdvertisingPlayer):52:53 -> a
    1052:1053:void setExecutor(java.lang.Object):16 -> a
    16:16:java.lang.Object getExecutor() -> a
    2047:2047:com.kaolafm.ad.expose.AdvertisingPlayer getExecutor():47:47 -> a
    2047:2047:java.lang.Object getExecutor():16 -> a
com.kaolafm.ad.expose.AdvertisingPlayerAdapter_Factory -> com.kaolafm.ad.expose.m:
    com.kaolafm.ad.expose.AdvertisingPlayerAdapter_Factory INSTANCE -> a
    6:6:void <init>() -> <init>
    12:12:com.kaolafm.ad.expose.AdvertisingPlayerAdapter get() -> get
    1016:1016:com.kaolafm.ad.expose.AdvertisingPlayerAdapter provideInstance():16:16 -> get
    1016:1016:com.kaolafm.ad.expose.AdvertisingPlayerAdapter get():12 -> get
    16:16:com.kaolafm.ad.expose.AdvertisingPlayerAdapter provideInstance() -> a
    20:20:com.kaolafm.ad.expose.AdvertisingPlayerAdapter_Factory create() -> create
    24:24:com.kaolafm.ad.expose.AdvertisingPlayerAdapter newAdvertisingPlayerAdapter() -> b
    6:6:java.lang.Object get() -> get
    7:7:void <clinit>() -> <clinit>
com.kaolafm.ad.expose.AdvertisingReporter -> com.kaolafm.ad.expose.n:
    void display$3df34b87() -> a
    void endDisplay$3df34b87() -> b
    void interruptDisplay$3df34b87() -> c
    void play$3df34b87() -> d
    void endPlay$7f8a516b() -> e
    void click$3df34b87() -> f
    void skip$3df34b87() -> g
    void displayInteraction$3df34b87() -> h
    void endInteraction$3df34b87() -> i
com.kaolafm.ad.expose.CompositeAdapter -> com.kaolafm.ad.expose.o:
    com.kaolafm.ad.expose.CompositeExecutor mExecutor -> a
    22:24:void <init>(com.kaolafm.ad.expose.CompositeExecutor) -> <init>
    28:28:boolean accept(com.kaolafm.ad.api.model.Advert) -> a
    33:36:void expose(com.kaolafm.ad.api.model.Advert) -> b
    40:44:void close(com.kaolafm.ad.api.model.Advert) -> c
    48:48:com.kaolafm.ad.expose.CompositeExecutor getExecutor() -> b
    53:54:void setExecutor(com.kaolafm.ad.expose.CompositeExecutor) -> a
    59:59:void error(java.lang.String,int,com.kaolafm.opensdk.http.error.ApiException) -> error
    16:16:void setExecutor(java.lang.Object) -> a
    1053:1054:void setExecutor(com.kaolafm.ad.expose.CompositeExecutor):53:54 -> a
    1053:1054:void setExecutor(java.lang.Object):16 -> a
    16:16:java.lang.Object getExecutor() -> a
    2048:2048:com.kaolafm.ad.expose.CompositeExecutor getExecutor():48:48 -> a
    2048:2048:java.lang.Object getExecutor():16 -> a
com.kaolafm.ad.expose.CompositeAdapter_Factory -> com.kaolafm.ad.expose.p:
    javax.inject.Provider executorProvider -> a
    10:12:void <init>(javax.inject.Provider) -> <init>
    16:16:com.kaolafm.ad.expose.CompositeAdapter get() -> get
    1020:1020:com.kaolafm.ad.expose.CompositeAdapter provideInstance(javax.inject.Provider):20:20 -> get
    1020:1020:com.kaolafm.ad.expose.CompositeAdapter get():16 -> get
    20:20:com.kaolafm.ad.expose.CompositeAdapter provideInstance(javax.inject.Provider) -> a
    24:24:com.kaolafm.ad.expose.CompositeAdapter_Factory create(javax.inject.Provider) -> create
    28:28:com.kaolafm.ad.expose.CompositeAdapter newCompositeAdapter(java.lang.Object) -> a
    7:7:java.lang.Object get() -> get
com.kaolafm.ad.expose.CompositeExecutor -> com.kaolafm.ad.expose.q:
    com.kaolafm.ad.expose.AdvertisingImagerAdapter mImager -> a
    com.kaolafm.ad.expose.AdvertisingPlayerAdapter mPlayer -> b
    21:24:void <init>(com.kaolafm.ad.expose.AdvertisingImagerAdapter,com.kaolafm.ad.expose.AdvertisingPlayerAdapter) -> <init>
    27:27:com.kaolafm.ad.expose.AdvertisingImagerAdapter getImager() -> a
    31:32:void setImager(com.kaolafm.ad.expose.AdvertisingImagerAdapter) -> a
    35:35:com.kaolafm.ad.expose.AdvertisingPlayerAdapter getPlayer() -> b
    39:40:void setPlayer(com.kaolafm.ad.expose.AdvertisingPlayerAdapter) -> a
com.kaolafm.ad.expose.CompositeExecutor_Factory -> com.kaolafm.ad.expose.r:
    javax.inject.Provider imagerProvider -> a
    javax.inject.Provider playerProvider -> b
    14:17:void <init>(javax.inject.Provider,javax.inject.Provider) -> <init>
    21:21:com.kaolafm.ad.expose.CompositeExecutor get() -> get
    1027:1027:com.kaolafm.ad.expose.CompositeExecutor provideInstance(javax.inject.Provider,javax.inject.Provider):27:27 -> get
    1027:1027:com.kaolafm.ad.expose.CompositeExecutor get():21 -> get
    27:27:com.kaolafm.ad.expose.CompositeExecutor provideInstance(javax.inject.Provider,javax.inject.Provider) -> a
    33:33:com.kaolafm.ad.expose.CompositeExecutor_Factory create(javax.inject.Provider,javax.inject.Provider) -> create
    38:38:com.kaolafm.ad.expose.CompositeExecutor newCompositeExecutor(com.kaolafm.ad.expose.AdvertisingImagerAdapter,com.kaolafm.ad.expose.AdvertisingPlayerAdapter) -> a
    7:7:java.lang.Object get() -> get
com.kaolafm.ad.expose.Executor -> com.kaolafm.ad.expose.Executor:
    void error(java.lang.String,int,com.kaolafm.opensdk.http.error.ApiException) -> error
com.kaolafm.ad.profile.AdProfileManager -> com.kaolafm.ad.b.a:
    java.lang.String APP_ID_PROPERTY -> a
    25:26:void <init>() -> <init>
    30:32:void loadProfile() -> a
    1036:1040:void loadProfileFromManifest():36:40 -> a
    1036:1040:void loadProfile():30 -> a
    1054:1060:void setProfile():54:60 -> a
    1054:1060:void loadProfile():31 -> a
    36:40:void loadProfileFromManifest() -> b
    44:50:void setupFromManifest(android.os.Bundle) -> a
    54:60:void setProfile() -> c
    63:64:void setBrand(java.lang.String) -> a
com.kaolafm.ad.profile.AdProfileManager_Factory -> com.kaolafm.ad.b.b:
    javax.inject.Provider mApplicationProvider -> a
    javax.inject.Provider mUrlManagerProvider -> b
    javax.inject.Provider mProfileProvider -> c
    javax.inject.Provider optionsProvider -> d
    24:29:void <init>(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    33:33:com.kaolafm.ad.profile.AdProfileManager get() -> get
    1042:1047:com.kaolafm.ad.profile.AdProfileManager provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider):42:47 -> get
    1042:1047:com.kaolafm.ad.profile.AdProfileManager get():33 -> get
    42:47:com.kaolafm.ad.profile.AdProfileManager provideInstance(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> a
    55:55:com.kaolafm.ad.profile.AdProfileManager_Factory create(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> create
    60:60:com.kaolafm.ad.profile.AdProfileManager newAdProfileManager() -> a
    11:11:java.lang.Object get() -> get
com.kaolafm.ad.profile.AdProfileManager_MembersInjector -> com.kaolafm.ad.b.c:
    javax.inject.Provider mApplicationProvider -> a
    javax.inject.Provider mUrlManagerProvider -> b
    javax.inject.Provider mProfileProvider -> c
    javax.inject.Provider optionsProvider -> d
    24:29:void <init>(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    36:36:dagger.MembersInjector create(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> a
    42:46:void injectMembers(com.kaolafm.ad.profile.AdProfileManager) -> a
    11:11:void injectMembers(java.lang.Object) -> a
    1042:1046:void injectMembers(com.kaolafm.ad.profile.AdProfileManager):42:46 -> a
    1042:1046:void injectMembers(java.lang.Object):11 -> a
com.kaolafm.ad.profile.AdvertisingProfile -> com.kaolafm.ad.b.d:
    java.lang.String brand -> a
    16:17:void <init>() -> <init>
    20:20:java.lang.String getBrand() -> a
    24:25:void setBrand(java.lang.String) -> a
com.kaolafm.ad.profile.AdvertisingProfile_Factory -> com.kaolafm.ad.b.e:
    com.kaolafm.ad.profile.AdvertisingProfile_Factory INSTANCE -> a
    6:6:void <init>() -> <init>
    11:11:com.kaolafm.ad.profile.AdvertisingProfile get() -> get
    1015:1015:com.kaolafm.ad.profile.AdvertisingProfile provideInstance():15:15 -> get
    1015:1015:com.kaolafm.ad.profile.AdvertisingProfile get():11 -> get
    15:15:com.kaolafm.ad.profile.AdvertisingProfile provideInstance() -> a
    19:19:com.kaolafm.ad.profile.AdvertisingProfile_Factory create() -> create
    23:23:com.kaolafm.ad.profile.AdvertisingProfile newAdvertisingProfile() -> b
    6:6:java.lang.Object get() -> get
    7:7:void <clinit>() -> <clinit>
com.kaolafm.ad.profile.OSProfile -> com.kaolafm.ad.b.f:
    java.lang.String osType -> a
    int deviceType -> b
    java.lang.String deviceModel -> c
    java.lang.String osVersion -> d
    java.lang.String deviceManufacturer -> e
    java.lang.String resolution -> f
    java.lang.String ip -> g
    12:27:void <init>() -> <init>
    35:35:java.lang.String getOsType() -> b
    39:40:void setOsType(java.lang.String) -> a
    43:43:java.lang.String getOsVersion() -> c
    47:48:void setOsVersion(java.lang.String) -> d
    51:51:int getDeviceType() -> d
    55:56:void setDeviceType(int) -> a
    59:59:java.lang.String getDeviceManufacturer() -> e
    63:64:void setDeviceManufacturer(java.lang.String) -> e
    67:67:java.lang.String getResolution() -> f
    71:72:void setResolution(java.lang.String) -> b
    75:75:java.lang.String getDeviceModel() -> a
    79:80:void setDeviceModel(java.lang.String) -> f
    83:83:java.lang.String getIp() -> g
    87:88:void setIp(java.lang.String) -> c
com.kaolafm.ad.report.AdConstants -> com.kaolafm.ad.report.a:
    int REPORTED -> a
    int REPORTING -> b
    int NOT_REPORTED -> c
    int NOT_MONITOR -> d
    int MIAOZHEN_MONITOR -> e
    int TALKING_DATA_MONITOR -> f
    java.lang.String AD_REPORT_DOMAIN_NAME -> g
    3:3:void <init>() -> <init>
com.kaolafm.ad.report.AdReportAgent -> com.kaolafm.ad.report.b:
    6:6:void <init>() -> <init>
    14:18:void onEvent(java.lang.String,int) -> a
    1021:1023:void onEvent(com.kaolafm.ad.report.bean.BaseAdEvent):21:23 -> a
    1021:1023:void onEvent(java.lang.String,int):17 -> a
    21:23:void onEvent(com.kaolafm.ad.report.bean.BaseAdEvent) -> a
com.kaolafm.ad.report.AdReportAgent$EventType -> com.kaolafm.ad.report.b$a:
    int PV -> a
    int PLAY_START -> b
    int PLAY_END -> c
    int CLICK -> d
    int SKIP -> e
    int DISPLAY_INTERRUPT -> f
    int DISPLAY_END -> g
    int DISPLAY_MORE_INTERACTION -> h
    int MODE_INTERACTION_END -> i
    int MIAOZHEN_MONITOR -> j
    int TALKING_DATA_MONITOR -> k
    25:25:void <init>() -> <init>
com.kaolafm.ad.report.AdReportDBManager -> com.kaolafm.ad.report.c:
    java.lang.String DB_NAME -> a
    com.kaolafm.ad.report.db.greendao.DaoSession mDaoSession -> b
    20:22:void <init>() -> <init>
    29:29:com.kaolafm.ad.report.AdReportDBManager getInstance() -> c
    33:37:void init() -> a
    40:44:java.util.List queryNotReported() -> b
    48:52:void insert(com.kaolafm.ad.report.db.bean.EventData) -> a
    55:59:void insertOrReplace(com.kaolafm.ad.report.db.bean.EventData) -> b
    62:66:void update(java.util.List) -> a
    69:73:void delete(java.util.List) -> b
    76:80:void delete(long) -> a
    83:83:boolean checkDaoSessionNull() -> d
    14:14:void <init>(byte) -> <init>
com.kaolafm.ad.report.AdReportDBManager$1 -> com.kaolafm.ad.report.d:
com.kaolafm.ad.report.AdReportDBManager$KRADIO_AD_REPORT_DB_MANAGER -> com.kaolafm.ad.report.c$a:
    com.kaolafm.ad.report.AdReportDBManager INSTANCE -> a
    24:24:void <init>() -> <init>
    24:24:com.kaolafm.ad.report.AdReportDBManager access$100() -> a
    25:25:void <clinit>() -> <clinit>
com.kaolafm.ad.report.AdReportManager -> com.kaolafm.ad.report.e:
    java.util.concurrent.ExecutorService executors -> b
    android.content.Context mContext -> c
    java.lang.String TAG -> d
    boolean hasInit -> a
    19:29:void <init>() -> <init>
    36:36:com.kaolafm.ad.report.AdReportManager getInstance() -> a
    40:40:java.util.concurrent.ExecutorService getExecutorService() -> b
    44:48:void init(android.content.Context) -> a
    1029:1029:com.kaolafm.ad.report.AdReportDBManager com.kaolafm.ad.report.AdReportDBManager.getInstance():29:29 -> a
    1029:1029:void init(android.content.Context):45 -> a
    1033:1033:com.kaolafm.ad.report.MonitorParameterManager com.kaolafm.ad.report.MonitorParameterManager.getInstance():33:33 -> a
    1033:1033:void init(android.content.Context):46 -> a
    51:51:android.content.Context getContext() -> c
    55:58:void addEvent(com.kaolafm.ad.report.bean.BaseAdEvent) -> a
    1064:1076:void checkNotReportedAndExecute():64:76 -> a
    1064:1076:void addEvent(com.kaolafm.ad.report.bean.BaseAdEvent):57 -> a
    2029:2029:com.kaolafm.ad.report.AdReportDBManager com.kaolafm.ad.report.AdReportDBManager.getInstance():29:29 -> a
    2029:2029:void checkNotReportedAndExecute():64 -> a
    2029:2029:void addEvent(com.kaolafm.ad.report.bean.BaseAdEvent):57 -> a
    2080:2097:com.kaolafm.ad.report.bean.BaseAdEvent createAdEvent(int,java.lang.String):80:97 -> a
    2080:2097:void checkNotReportedAndExecute():70 -> a
    2080:2097:void addEvent(com.kaolafm.ad.report.bean.BaseAdEvent):57 -> a
    3029:3029:com.kaolafm.ad.report.AdReportDBManager com.kaolafm.ad.report.AdReportDBManager.getInstance():29:29 -> a
    3029:3029:void checkNotReportedAndExecute():75 -> a
    3029:3029:void addEvent(com.kaolafm.ad.report.bean.BaseAdEvent):57 -> a
    64:76:void checkNotReportedAndExecute() -> d
    4029:4029:com.kaolafm.ad.report.AdReportDBManager com.kaolafm.ad.report.AdReportDBManager.getInstance():29:29 -> d
    4029:4029:void checkNotReportedAndExecute():64 -> d
    4080:4097:com.kaolafm.ad.report.bean.BaseAdEvent createAdEvent(int,java.lang.String):80:97 -> d
    4080:4097:void checkNotReportedAndExecute():70 -> d
    5029:5029:com.kaolafm.ad.report.AdReportDBManager com.kaolafm.ad.report.AdReportDBManager.getInstance():29:29 -> d
    5029:5029:void checkNotReportedAndExecute():75 -> d
    80:97:com.kaolafm.ad.report.bean.BaseAdEvent createAdEvent(int,java.lang.String) -> a
    17:17:void <init>(byte) -> <init>
    23:23:void <clinit>() -> <clinit>
com.kaolafm.ad.report.AdReportManager$1 -> com.kaolafm.ad.report.f:
com.kaolafm.ad.report.AdReportManager$KRADIO_AD_REPORT_MANAGER -> com.kaolafm.ad.report.e$a:
    com.kaolafm.ad.report.AdReportManager INSTANCE -> a
    31:31:void <init>() -> <init>
    31:31:com.kaolafm.ad.report.AdReportManager access$100() -> a
    32:32:void <clinit>() -> <clinit>
com.kaolafm.ad.report.EventPushTask -> com.kaolafm.ad.report.g:
    com.kaolafm.ad.report.bean.BaseAdEvent baseAdEvent -> a
    long id -> b
    java.lang.String TAG -> c
    14:20:void <init>(com.kaolafm.ad.report.bean.BaseAdEvent) -> <init>
    14:25:void <init>(long,com.kaolafm.ad.report.bean.BaseAdEvent) -> <init>
    28:46:void report() -> a
    36:45:void lambda$report$0(java.lang.Boolean) -> a
    1011:1015:com.kaolafm.ad.report.db.bean.EventData com.kaolafm.ad.report.util.EventUtil.getEventBean(com.kaolafm.ad.report.bean.BaseAdEvent):11:15 -> a
    1011:1015:void lambda$report$0(java.lang.Boolean):37 -> a
    1029:1029:com.kaolafm.ad.report.AdReportDBManager com.kaolafm.ad.report.AdReportDBManager.getInstance():29:29 -> a
    1029:1029:void lambda$report$0(java.lang.Boolean):40 -> a
    2029:2029:com.kaolafm.ad.report.AdReportDBManager com.kaolafm.ad.report.AdReportDBManager.getInstance():29:29 -> a
    2029:2029:void lambda$report$0(java.lang.Boolean):43 -> a
    16:16:void <clinit>() -> <clinit>
com.kaolafm.ad.report.EventTask -> com.kaolafm.ad.report.h:
    com.kaolafm.ad.report.bean.BaseAdEvent baseAdEvent -> a
    java.lang.String TAG -> b
    18:20:void <init>(com.kaolafm.ad.report.bean.BaseAdEvent) -> <init>
    25:35:void run() -> run
    16:16:void <clinit>() -> <clinit>
com.kaolafm.ad.report.MonitorParameterManager -> com.kaolafm.ad.report.i:
    com.kaolafm.ad.report.parameter.MiaoZhenParameter miaoZhenParameter -> a
    com.kaolafm.ad.report.parameter.TalkingDataParameter talkingDataParameter -> b
    java.lang.String TAG -> c
    21:26:void <init>() -> <init>
    33:33:com.kaolafm.ad.report.MonitorParameterManager getInstance() -> a
    37:39:void loadMonitorParameter() -> b
    1110:1128:void loadMiaozhenParameter():110:128 -> b
    1110:1128:void loadMonitorParameter():37 -> b
    1132:1158:void loadTalkingDataParameter():132:158 -> b
    1132:1158:void loadMonitorParameter():38 -> b
    2135:2135:java.lang.String com.kaolafm.ad.report.util.ADReportParameterUtil.getModel():135:135 -> b
    2135:2135:void loadTalkingDataParameter():133 -> b
    2135:2135:void loadMonitorParameter():38 -> b
    42:49:void loadMonitorParameterOptions(com.kaolafm.ad.report.parameter.ParameterOptions,int) -> a
    2161:2169:void setTalkingDataParameterOptions(com.kaolafm.ad.report.parameter.TalkingDataParameterOptions):161:169 -> a
    2161:2169:void loadMonitorParameterOptions(com.kaolafm.ad.report.parameter.ParameterOptions,int):44 -> a
    52:58:java.lang.String getMonitorParameter(java.lang.String,int) -> a
    3078:3098:java.lang.String macroTalkingDataParameter(java.lang.String):78:98 -> a
    3078:3098:java.lang.String getMonitorParameter(java.lang.String,int):54 -> a
    4063:4073:java.lang.String macroMiaozhenParameter(java.lang.String):63:73 -> a
    4063:4073:java.lang.String getMonitorParameter(java.lang.String,int):56 -> a
    63:73:java.lang.String macroMiaozhenParameter(java.lang.String) -> a
    78:98:java.lang.String macroTalkingDataParameter(java.lang.String) -> b
    102:106:void putMap(java.util.Map,java.lang.String,java.lang.Object) -> a
    110:128:void loadMiaozhenParameter() -> c
    132:158:void loadTalkingDataParameter() -> d
    4135:4135:java.lang.String com.kaolafm.ad.report.util.ADReportParameterUtil.getModel():135:135 -> d
    4135:4135:void loadTalkingDataParameter():133 -> d
    161:169:void setTalkingDataParameterOptions(com.kaolafm.ad.report.parameter.TalkingDataParameterOptions) -> a
    172:173:void setLocation(java.lang.String,java.lang.String) -> a
    19:19:void <init>(byte) -> <init>
com.kaolafm.ad.report.MonitorParameterManager$1 -> com.kaolafm.ad.report.j:
com.kaolafm.ad.report.MonitorParameterManager$KRADIO_MONITOR_PARAMETER_MANAGER -> com.kaolafm.ad.report.i$a:
    com.kaolafm.ad.report.MonitorParameterManager INSTANCE -> a
    28:28:void <init>() -> <init>
    28:28:com.kaolafm.ad.report.MonitorParameterManager access$100() -> a
    29:29:void <clinit>() -> <clinit>
com.kaolafm.ad.report.api.ReportRequest -> com.kaolafm.ad.report.a.a:
    void display(java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> a
    void endDisplay(java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> b
    void interruptDisplay(java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> c
    void play(java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> d
    void endPlay(java.lang.String,java.lang.String,long,com.kaolafm.opensdk.http.core.HttpCallback) -> a
    void skip(java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> e
    void displayMoreInteraction(java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> f
    void displayMoreInteractionEnd(java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> g
    void click(java.lang.String,java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> h
com.kaolafm.ad.report.api.monitor.MonitorApiService -> com.kaolafm.ad.report.a.a.a:
    io.reactivex.Single report(java.lang.String) -> a
com.kaolafm.ad.report.api.monitor.MonitorRequest -> com.kaolafm.ad.report.a.a.b:
    com.kaolafm.ad.report.api.monitor.MonitorApiService monitorApiService -> a
    13:15:void <init>() -> <init>
    18:18:io.reactivex.functions.Function map() -> a
    22:23:void report(java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback) -> a
    1018:1018:io.reactivex.functions.Function map():18:18 -> a
    1018:1018:void report(java.lang.String,com.kaolafm.opensdk.http.core.HttpCallback):22 -> a
com.kaolafm.ad.report.bean.AdReportMonitorEvent -> com.kaolafm.ad.report.b.a:
    java.lang.String monitorUrl -> a
    7:9:void <init>(int) -> <init>
    12:12:java.lang.String getMonitorUrl() -> a
    16:17:void setMonitorUrl(java.lang.String) -> c
com.kaolafm.ad.report.bean.AdReportPlayEndEvent -> com.kaolafm.ad.report.b.b:
    long playTime -> a
    7:9:void <init>() -> <init>
    14:15:void setPlayTime(long) -> a
    18:18:long getPlayTime() -> a
com.kaolafm.ad.report.bean.BaseAdEvent -> com.kaolafm.ad.report.b.c:
    java.lang.String date -> a
    long creativeId -> b
    java.lang.String sessionId -> c
    int eventType -> d
    3:3:void <init>() -> <init>
    14:14:long getCreativeId() -> b
    18:18:java.lang.String getDate() -> c
    22:22:int getEventType() -> d
    26:27:void setCreativeId(long) -> a
    30:31:void setDate(java.lang.String) -> a
    34:35:void setEventType(int) -> a
    38:39:void setSessionId(java.lang.String) -> b
    42:42:java.lang.String getSessionId() -> e
    47:47:java.lang.String toString() -> toString
com.kaolafm.ad.report.db.bean.EventData -> com.kaolafm.ad.report.db.a.a:
    java.lang.Long id -> a
    long creativeId -> b
    int type -> c
    int status -> d
    java.lang.String reportData -> e
    37:38:void <init>() -> <init>
    42:48:void <init>(java.lang.Long,long,int,int,java.lang.String) -> <init>
    69:69:java.lang.Long getId() -> a
    75:76:void setId(java.lang.Long) -> a
    81:81:long getCreativeId() -> b
    87:88:void setCreativeId(long) -> a
    93:93:int getType() -> c
    99:100:void setType(int) -> a
    105:105:int getStatus() -> d
    111:112:void setStatus(int) -> b
    117:117:java.lang.String getReportData() -> e
    123:124:void setReportData(java.lang.String) -> a
    129:129:java.lang.String toString() -> toString
com.kaolafm.ad.report.db.greendao.DaoMaster -> com.kaolafm.ad.report.db.greendao.DaoMaster:
    int SCHEMA_VERSION -> SCHEMA_VERSION
    24:25:void createAllTables(org.greenrobot.greendao.database.Database,boolean) -> createAllTables
    29:30:void dropAllTables(org.greenrobot.greendao.database.Database,boolean) -> dropAllTables
    37:39:com.kaolafm.ad.report.db.greendao.DaoSession newDevSession(android.content.Context,java.lang.String) -> newDevSession
    43:44:void <init>(android.database.sqlite.SQLiteDatabase) -> <init>
    47:49:void <init>(org.greenrobot.greendao.database.Database) -> <init>
    52:52:com.kaolafm.ad.report.db.greendao.DaoSession newSession() -> newSession
    56:56:com.kaolafm.ad.report.db.greendao.DaoSession newSession(org.greenrobot.greendao.identityscope.IdentityScopeType) -> newSession
    19:19:org.greenrobot.greendao.AbstractDaoSession newSession(org.greenrobot.greendao.identityscope.IdentityScopeType) -> newSession
    19:19:org.greenrobot.greendao.AbstractDaoSession newSession() -> newSession
com.kaolafm.ad.report.db.greendao.DaoMaster$DevOpenHelper -> com.kaolafm.ad.report.db.greendao.DaoMaster$DevOpenHelper:
    81:82:void <init>(android.content.Context,java.lang.String) -> <init>
    85:86:void <init>(android.content.Context,java.lang.String,android.database.sqlite.SQLiteDatabase$CursorFactory) -> <init>
    90:93:void onUpgrade(org.greenrobot.greendao.database.Database,int,int) -> onUpgrade
com.kaolafm.ad.report.db.greendao.DaoMaster$OpenHelper -> com.kaolafm.ad.report.db.greendao.DaoMaster$OpenHelper:
    64:65:void <init>(android.content.Context,java.lang.String) -> <init>
    68:69:void <init>(android.content.Context,java.lang.String,android.database.sqlite.SQLiteDatabase$CursorFactory) -> <init>
    73:75:void onCreate(org.greenrobot.greendao.database.Database) -> onCreate
com.kaolafm.ad.report.db.greendao.DaoSession -> com.kaolafm.ad.report.db.greendao.DaoSession:
    org.greenrobot.greendao.internal.DaoConfig eventDataDaoConfig -> eventDataDaoConfig
    com.kaolafm.ad.report.db.greendao.EventDataDao eventDataDao -> eventDataDao
    30:38:void <init>(org.greenrobot.greendao.database.Database,org.greenrobot.greendao.identityscope.IdentityScopeType,java.util.Map) -> <init>
    41:42:void clear() -> clear
    45:45:com.kaolafm.ad.report.db.greendao.EventDataDao getEventDataDao() -> getEventDataDao
com.kaolafm.ad.report.db.greendao.EventDataDao -> com.kaolafm.ad.report.db.greendao.EventDataDao:
    java.lang.String TABLENAME -> TABLENAME
    36:37:void <init>(org.greenrobot.greendao.internal.DaoConfig) -> <init>
    40:41:void <init>(org.greenrobot.greendao.internal.DaoConfig,com.kaolafm.ad.report.db.greendao.DaoSession) -> <init>
    45:52:void createTable(org.greenrobot.greendao.database.Database,boolean) -> createTable
    56:58:void dropTable(org.greenrobot.greendao.database.Database,boolean) -> dropTable
    62:76:void bindValues(org.greenrobot.greendao.database.DatabaseStatement,com.kaolafm.ad.report.db.bean.EventData) -> bindValues
    80:94:void bindValues(android.database.sqlite.SQLiteStatement,com.kaolafm.ad.report.db.bean.EventData) -> bindValues
    98:98:java.lang.Long readKey(android.database.Cursor,int) -> readKey
    103:110:com.kaolafm.ad.report.db.bean.EventData readEntity(android.database.Cursor,int) -> readEntity
    115:120:void readEntity(android.database.Cursor,com.kaolafm.ad.report.db.bean.EventData,int) -> readEntity
    124:125:java.lang.Long updateKeyAfterInsert(com.kaolafm.ad.report.db.bean.EventData,long) -> updateKeyAfterInsert
    130:133:java.lang.Long getKey(com.kaolafm.ad.report.db.bean.EventData) -> getKey
    139:139:boolean hasKey(com.kaolafm.ad.report.db.bean.EventData) -> hasKey
    144:144:boolean isEntityUpdateable() -> isEntityUpdateable
    18:18:boolean hasKey(java.lang.Object) -> hasKey
    18:18:java.lang.Object getKey(java.lang.Object) -> getKey
    18:18:java.lang.Object updateKeyAfterInsert(java.lang.Object,long) -> updateKeyAfterInsert
    18:18:void bindValues(android.database.sqlite.SQLiteStatement,java.lang.Object) -> bindValues
    18:18:void bindValues(org.greenrobot.greendao.database.DatabaseStatement,java.lang.Object) -> bindValues
    18:18:void readEntity(android.database.Cursor,java.lang.Object,int) -> readEntity
    18:18:java.lang.Object readKey(android.database.Cursor,int) -> readKey
    18:18:java.lang.Object readEntity(android.database.Cursor,int) -> readEntity
com.kaolafm.ad.report.db.greendao.EventDataDao$Properties -> com.kaolafm.ad.report.db.greendao.EventDataDao$Properties:
    org.greenrobot.greendao.Property Id -> Id
    org.greenrobot.greendao.Property CreativeId -> CreativeId
    org.greenrobot.greendao.Property Type -> Type
    org.greenrobot.greendao.Property Status -> Status
    org.greenrobot.greendao.Property ReportData -> ReportData
    26:26:void <init>() -> <init>
    27:31:void <clinit>() -> <clinit>
com.kaolafm.ad.report.net.AdReportNetHelper -> com.kaolafm.ad.report.c.a:
    java.lang.String TAG -> a
    com.kaolafm.ad.report.api.ReportRequest mReportRequest -> b
    com.kaolafm.ad.report.api.monitor.MonitorRequest mMonitorRequest -> c
    21:22:void <init>() -> <init>
    29:29:com.kaolafm.ad.report.net.AdReportNetHelper getInstance() -> a
    33:34:void setReportRequest(com.kaolafm.ad.report.api.ReportRequest) -> a
    37:38:void sendEvent(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback) -> a
    1041:1077:void sendEventInternal(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):41:77 -> a
    1041:1077:void sendEvent(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):37 -> a
    1100:1111:void sendPV(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):100:111 -> a
    1100:1111:void sendEventInternal(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):44 -> a
    1100:1111:void sendEvent(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):37 -> a
    1143:1154:void sendClick(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):143:154 -> a
    1143:1154:void sendEventInternal(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):47 -> a
    1143:1154:void sendEvent(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):37 -> a
    1157:1168:void sendDisPlayEnd(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):157:168 -> a
    1157:1168:void sendEventInternal(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):50 -> a
    1157:1168:void sendEvent(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):37 -> a
    1185:1196:void sendInterruptDisplay(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):185:196 -> a
    1185:1196:void sendEventInternal(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):53 -> a
    1185:1196:void sendEvent(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):37 -> a
    1199:1210:void sendDisplayMoreInteraction(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):199:210 -> a
    1199:1210:void sendEventInternal(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):56 -> a
    1199:1210:void sendEvent(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):37 -> a
    1213:1224:void sendDisplayMoreInteractionEnd(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):213:224 -> a
    1213:1224:void sendEventInternal(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):59 -> a
    1213:1224:void sendEvent(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):37 -> a
    2128:2140:void sendEndPlay(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):128:140 -> a
    2128:2140:void sendEventInternal(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):62 -> a
    2128:2140:void sendEvent(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):37 -> a
    3114:3125:void sendStartPlay(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):114:125 -> a
    3114:3125:void sendEventInternal(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):65 -> a
    3114:3125:void sendEvent(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):37 -> a
    3171:3182:void sendSkid(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):171:182 -> a
    3171:3182:void sendEventInternal(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):68 -> a
    3171:3182:void sendEvent(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):37 -> a
    41:77:void sendEventInternal(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback) -> b
    4100:4111:void sendPV(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):100:111 -> b
    4100:4111:void sendEventInternal(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):44 -> b
    4143:4154:void sendClick(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):143:154 -> b
    4143:4154:void sendEventInternal(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):47 -> b
    4157:4168:void sendDisPlayEnd(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):157:168 -> b
    4157:4168:void sendEventInternal(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):50 -> b
    4185:4196:void sendInterruptDisplay(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):185:196 -> b
    4185:4196:void sendEventInternal(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):53 -> b
    4199:4210:void sendDisplayMoreInteraction(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):199:210 -> b
    4199:4210:void sendEventInternal(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):56 -> b
    4213:4224:void sendDisplayMoreInteractionEnd(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):213:224 -> b
    4213:4224:void sendEventInternal(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):59 -> b
    5128:5140:void sendEndPlay(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):128:140 -> b
    5128:5140:void sendEventInternal(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):62 -> b
    6114:6125:void sendStartPlay(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):114:125 -> b
    6114:6125:void sendEventInternal(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):65 -> b
    6171:6182:void sendSkid(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):171:182 -> b
    6171:6182:void sendEventInternal(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback):68 -> b
    80:97:void sendMonitor(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback) -> c
    100:111:void sendPV(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback) -> d
    114:125:void sendStartPlay(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback) -> e
    128:140:void sendEndPlay(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback) -> f
    143:154:void sendClick(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback) -> g
    157:168:void sendDisPlayEnd(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback) -> h
    171:182:void sendSkid(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback) -> i
    185:196:void sendInterruptDisplay(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback) -> j
    199:210:void sendDisplayMoreInteraction(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback) -> k
    213:224:void sendDisplayMoreInteractionEnd(com.kaolafm.ad.report.bean.BaseAdEvent,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback) -> l
    233:236:void notifyCallback(com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback,boolean) -> b
    15:15:void <init>(byte) -> <init>
    15:15:void access$200$571810d9(com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback,boolean) -> a
    6233:6236:void notifyCallback(com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback,boolean):233:236 -> a
    6233:6236:void access$200$571810d9(com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback,boolean):15 -> a
    17:17:void <clinit>() -> <clinit>
com.kaolafm.ad.report.net.AdReportNetHelper$1 -> com.kaolafm.ad.report.c.b:
    com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback val$adReportCallback -> a
    com.kaolafm.ad.report.net.AdReportNetHelper this$0 -> b
    86:86:void <init>(com.kaolafm.ad.report.net.AdReportNetHelper,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback) -> <init>
    89:90:void onSuccess(java.lang.Boolean) -> a
    94:95:void onError$626d7010() -> a
    86:86:void onSuccess(java.lang.Object) -> a
    1089:1090:void onSuccess(java.lang.Boolean):89:90 -> a
    1089:1090:void onSuccess(java.lang.Object):86 -> a
com.kaolafm.ad.report.net.AdReportNetHelper$10 -> com.kaolafm.ad.report.c.c:
    com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback val$adReportCallback -> a
    com.kaolafm.ad.report.net.AdReportNetHelper this$0 -> b
    213:213:void <init>(com.kaolafm.ad.report.net.AdReportNetHelper,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback) -> <init>
    216:217:void onSuccess(java.lang.Boolean) -> a
    221:222:void onError$626d7010() -> a
    213:213:void onSuccess(java.lang.Object) -> a
    1216:1217:void onSuccess(java.lang.Boolean):216:217 -> a
    1216:1217:void onSuccess(java.lang.Object):213 -> a
com.kaolafm.ad.report.net.AdReportNetHelper$2 -> com.kaolafm.ad.report.c.d:
    com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback val$adReportCallback -> a
    com.kaolafm.ad.report.net.AdReportNetHelper this$0 -> b
    100:100:void <init>(com.kaolafm.ad.report.net.AdReportNetHelper,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback) -> <init>
    103:104:void onSuccess(java.lang.Boolean) -> a
    108:109:void onError$626d7010() -> a
    100:100:void onSuccess(java.lang.Object) -> a
    1103:1104:void onSuccess(java.lang.Boolean):103:104 -> a
    1103:1104:void onSuccess(java.lang.Object):100 -> a
com.kaolafm.ad.report.net.AdReportNetHelper$3 -> com.kaolafm.ad.report.c.e:
    com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback val$adReportCallback -> a
    com.kaolafm.ad.report.net.AdReportNetHelper this$0 -> b
    114:114:void <init>(com.kaolafm.ad.report.net.AdReportNetHelper,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback) -> <init>
    117:118:void onSuccess(java.lang.Boolean) -> a
    122:123:void onError$626d7010() -> a
    114:114:void onSuccess(java.lang.Object) -> a
    1117:1118:void onSuccess(java.lang.Boolean):117:118 -> a
    1117:1118:void onSuccess(java.lang.Object):114 -> a
com.kaolafm.ad.report.net.AdReportNetHelper$4 -> com.kaolafm.ad.report.c.f:
    com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback val$adReportCallback -> a
    com.kaolafm.ad.report.net.AdReportNetHelper this$0 -> b
    129:129:void <init>(com.kaolafm.ad.report.net.AdReportNetHelper,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback) -> <init>
    132:133:void onSuccess(java.lang.Boolean) -> a
    137:138:void onError$626d7010() -> a
    129:129:void onSuccess(java.lang.Object) -> a
    1132:1133:void onSuccess(java.lang.Boolean):132:133 -> a
    1132:1133:void onSuccess(java.lang.Object):129 -> a
com.kaolafm.ad.report.net.AdReportNetHelper$5 -> com.kaolafm.ad.report.c.g:
    com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback val$adReportCallback -> a
    com.kaolafm.ad.report.net.AdReportNetHelper this$0 -> b
    143:143:void <init>(com.kaolafm.ad.report.net.AdReportNetHelper,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback) -> <init>
    146:147:void onSuccess(java.lang.Boolean) -> a
    151:152:void onError$626d7010() -> a
    143:143:void onSuccess(java.lang.Object) -> a
    1146:1147:void onSuccess(java.lang.Boolean):146:147 -> a
    1146:1147:void onSuccess(java.lang.Object):143 -> a
com.kaolafm.ad.report.net.AdReportNetHelper$6 -> com.kaolafm.ad.report.c.h:
    com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback val$adReportCallback -> a
    com.kaolafm.ad.report.net.AdReportNetHelper this$0 -> b
    157:157:void <init>(com.kaolafm.ad.report.net.AdReportNetHelper,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback) -> <init>
    160:161:void onSuccess(java.lang.Boolean) -> a
    165:166:void onError$626d7010() -> a
    157:157:void onSuccess(java.lang.Object) -> a
    1160:1161:void onSuccess(java.lang.Boolean):160:161 -> a
    1160:1161:void onSuccess(java.lang.Object):157 -> a
com.kaolafm.ad.report.net.AdReportNetHelper$7 -> com.kaolafm.ad.report.c.i:
    com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback val$adReportCallback -> a
    com.kaolafm.ad.report.net.AdReportNetHelper this$0 -> b
    171:171:void <init>(com.kaolafm.ad.report.net.AdReportNetHelper,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback) -> <init>
    174:175:void onSuccess(java.lang.Boolean) -> a
    179:180:void onError$626d7010() -> a
    171:171:void onSuccess(java.lang.Object) -> a
    1174:1175:void onSuccess(java.lang.Boolean):174:175 -> a
    1174:1175:void onSuccess(java.lang.Object):171 -> a
com.kaolafm.ad.report.net.AdReportNetHelper$8 -> com.kaolafm.ad.report.c.j:
    com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback val$adReportCallback -> a
    com.kaolafm.ad.report.net.AdReportNetHelper this$0 -> b
    185:185:void <init>(com.kaolafm.ad.report.net.AdReportNetHelper,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback) -> <init>
    188:189:void onSuccess(java.lang.Boolean) -> a
    193:194:void onError$626d7010() -> a
    185:185:void onSuccess(java.lang.Object) -> a
    1188:1189:void onSuccess(java.lang.Boolean):188:189 -> a
    1188:1189:void onSuccess(java.lang.Object):185 -> a
com.kaolafm.ad.report.net.AdReportNetHelper$9 -> com.kaolafm.ad.report.c.k:
    com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback val$adReportCallback -> a
    com.kaolafm.ad.report.net.AdReportNetHelper this$0 -> b
    199:199:void <init>(com.kaolafm.ad.report.net.AdReportNetHelper,com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback) -> <init>
    202:203:void onSuccess(java.lang.Boolean) -> a
    207:208:void onError$626d7010() -> a
    199:199:void onSuccess(java.lang.Object) -> a
    1202:1203:void onSuccess(java.lang.Boolean):202:203 -> a
    1202:1203:void onSuccess(java.lang.Object):199 -> a
com.kaolafm.ad.report.net.AdReportNetHelper$AdReportCallback -> com.kaolafm.ad.report.c.a$a:
    void onResult(java.lang.Boolean) -> onResult
com.kaolafm.ad.report.net.AdReportNetHelper$KRADIO_AD_REPORT_NET_HELPER -> com.kaolafm.ad.report.c.a$b:
    com.kaolafm.ad.report.net.AdReportNetHelper INSTANCE -> a
    24:24:void <init>() -> <init>
    24:24:com.kaolafm.ad.report.net.AdReportNetHelper access$100() -> a
    25:25:void <clinit>() -> <clinit>
com.kaolafm.ad.report.parameter.MiaoZhenParameter -> com.kaolafm.ad.report.d.a:
    java.lang.String mo -> a
    java.lang.String ns -> b
    java.lang.String m2 -> c
    java.lang.String m1a -> d
    java.lang.String m1 -> e
    java.lang.String m6 -> f
    java.lang.String m6a -> g
    java.lang.String nn -> h
    3:3:void <init>() -> <init>
    40:40:java.lang.String getMo() -> a
    44:44:java.lang.String getNs() -> b
    48:48:java.lang.String getM2() -> c
    52:52:java.lang.String getM1a() -> d
    56:56:java.lang.String getM1() -> e
    60:60:java.lang.String getM6() -> f
    64:64:java.lang.String getM6a() -> g
    68:68:java.lang.String getNn() -> h
    72:73:void setMo(java.lang.String) -> a
    76:77:void setNs(java.lang.String) -> b
    80:81:void setM2(java.lang.String) -> c
    84:85:void setM1a(java.lang.String) -> d
    88:89:void setM1(java.lang.String) -> e
    92:93:void setM6(java.lang.String) -> f
    96:97:void setM6a(java.lang.String) -> g
    100:101:void setNn(java.lang.String) -> h
    105:105:java.lang.String toString() -> toString
com.kaolafm.ad.report.parameter.ParameterOptions -> com.kaolafm.ad.report.d.b:
    3:3:void <init>() -> <init>
com.kaolafm.ad.report.parameter.TalkingDataParameter -> com.kaolafm.ad.report.d.c:
    java.lang.String devicetype -> a
    java.lang.String ip -> b
    long time -> c
    java.lang.String mac -> d
    java.lang.String mac_md5 -> e
    java.lang.String androidid -> f
    java.lang.String androidid_md5 -> g
    java.lang.String imei -> h
    java.lang.String imei_md5 -> i
    java.lang.String advertisingid -> j
    java.lang.String advertisingid_md5 -> k
    java.lang.String pname -> l
    java.lang.String osversion -> m
    java.lang.String vin -> n
    java.lang.String motocity -> o
    java.lang.String location -> p
    java.lang.String mototype -> q
    java.lang.String motostate -> r
    java.lang.String screentype -> s
    4:4:void <init>() -> <init>
    87:87:java.lang.String getDevicetype() -> l
    91:91:java.lang.String getIp() -> a
    95:95:long getTime() -> m
    99:99:java.lang.String getMac() -> b
    103:103:java.lang.String getMac_md5() -> n
    107:107:java.lang.String getAndroidid() -> c
    111:111:java.lang.String getAndroidid_md5() -> o
    115:115:java.lang.String getImei() -> d
    119:119:java.lang.String getImei_md5() -> p
    123:123:java.lang.String getAdvertisingid() -> q
    127:127:java.lang.String getAdvertisingid_md5() -> r
    131:131:java.lang.String getPname() -> s
    135:135:java.lang.String getOsversion() -> e
    139:140:void setDevicetype(java.lang.String) -> a
    143:144:void setIp(java.lang.String) -> b
    147:148:void setTime(long) -> a
    151:152:void setMac(java.lang.String) -> c
    155:156:void setMac_md5(java.lang.String) -> d
    159:160:void setAndroidid(java.lang.String) -> e
    163:164:void setAndroidid_md5(java.lang.String) -> f
    167:168:void setImei(java.lang.String) -> g
    171:172:void setImei_md5(java.lang.String) -> h
    175:176:void setAdvertisingid(java.lang.String) -> q
    179:180:void setAdvertisingid_md5(java.lang.String) -> r
    183:184:void setPname(java.lang.String) -> i
    187:188:void setOsversion(java.lang.String) -> j
    191:191:java.lang.String getVin() -> f
    195:196:void setVin(java.lang.String) -> k
    199:199:java.lang.String getMotocity() -> g
    203:204:void setMotocity(java.lang.String) -> l
    207:207:java.lang.String getLocation() -> h
    211:212:void setLocation(java.lang.String) -> m
    215:215:java.lang.String getMototype() -> i
    219:220:void setMototype(java.lang.String) -> n
    223:223:java.lang.String getMotostate() -> j
    227:228:void setMotostate(java.lang.String) -> o
    231:231:java.lang.String getScreentype() -> k
    235:236:void setScreentype(java.lang.String) -> p
    240:240:java.lang.String toString() -> toString
com.kaolafm.ad.report.parameter.TalkingDataParameterOptions -> com.kaolafm.ad.report.d.d:
    java.lang.String motocity -> a
    java.lang.String location -> b
    java.lang.String mototype -> c
    java.lang.String motostate -> d
    java.lang.String screentype -> e
    java.lang.String vin -> f
    13:14:void <init>() -> <init>
    16:23:void <init>(com.kaolafm.ad.report.parameter.TalkingDataParameterOptions$Builder) -> <init>
    26:26:java.lang.String getMotocity() -> a
    30:30:java.lang.String getLocation() -> b
    34:34:java.lang.String getMototype() -> c
    38:38:java.lang.String getMotostate() -> d
    42:42:java.lang.String getScreentype() -> e
    46:46:java.lang.String getVin() -> f
com.kaolafm.ad.report.parameter.TalkingDataParameterOptions$Builder -> com.kaolafm.ad.report.d.d$a:
    java.lang.String motocity -> a
    java.lang.String location -> b
    java.lang.String mototype -> c
    java.lang.String motostate -> d
    java.lang.String screentype -> e
    java.lang.String vin -> f
    57:64:void <init>() -> <init>
    67:68:com.kaolafm.ad.report.parameter.TalkingDataParameterOptions$Builder motocity(java.lang.String) -> a
    72:73:com.kaolafm.ad.report.parameter.TalkingDataParameterOptions$Builder location(java.lang.String) -> b
    77:78:com.kaolafm.ad.report.parameter.TalkingDataParameterOptions$Builder mototype(java.lang.String) -> c
    82:83:com.kaolafm.ad.report.parameter.TalkingDataParameterOptions$Builder motostate(java.lang.String) -> d
    87:88:com.kaolafm.ad.report.parameter.TalkingDataParameterOptions$Builder screentype(java.lang.String) -> e
    92:93:com.kaolafm.ad.report.parameter.TalkingDataParameterOptions$Builder vin(java.lang.String) -> f
    97:97:com.kaolafm.ad.report.parameter.TalkingDataParameterOptions build() -> a
com.kaolafm.ad.report.util.ADReportParameterUtil -> com.kaolafm.ad.report.e.a:
    32:32:void <init>() -> <init>
    37:43:java.lang.String encode(java.lang.String) -> a
    49:79:java.lang.String getIpAddress(android.content.Context) -> a
    1083:1083:java.lang.String intIP2StringIP(int):83:83 -> a
    1083:1083:java.lang.String getIpAddress(android.content.Context):72 -> a
    83:83:java.lang.String intIP2StringIP(int) -> a
    94:108:java.lang.String getLocalIp() -> a
    117:125:java.lang.String getAppName(android.content.Context) -> b
    135:135:java.lang.String getModel() -> b
    145:157:java.util.Map getUrlParams(java.lang.String) -> b
    167:191:java.lang.String getUrlParamsByMap(java.util.Map,java.lang.String) -> a
    203:225:java.lang.String getMacDefault(android.content.Context) -> d
    233:239:java.lang.String getMacFromFile() -> c
    249:271:java.lang.String getMacFromHardware() -> d
    281:289:java.lang.String getMacAddress(android.content.Context) -> c
com.kaolafm.ad.report.util.EventUtil -> com.kaolafm.ad.report.e.b:
    8:8:void <init>() -> <init>
    11:15:com.kaolafm.ad.report.db.bean.EventData getEventBean(com.kaolafm.ad.report.bean.BaseAdEvent) -> a
com.kaolafm.ad.timer.AbstractTimer -> com.kaolafm.ad.timer.a:
    java.lang.String TIME -> a
    java.lang.String ID -> b
    int mId -> d
    int mTime -> e
    com.kaolafm.ad.db.manager.AdvertDBManager mAdvertDBManager -> c
    17:24:void <init>() -> <init>
    36:47:void expose(int,int) -> a
    43:45:void lambda$expose$0(int,int,com.kaolafm.ad.api.model.AdvertisingDetails) -> a
com.kaolafm.ad.timer.AbstractTimer_MembersInjector -> com.kaolafm.ad.timer.b:
    javax.inject.Provider mAdvertDBManagerProvider -> a
    11:13:void <init>(javax.inject.Provider) -> <init>
    17:17:dagger.MembersInjector create(javax.inject.Provider) -> a
    22:23:void injectMembers(com.kaolafm.ad.timer.AbstractTimer) -> a
    1027:1028:void injectMAdvertDBManager(com.kaolafm.ad.timer.AbstractTimer,com.kaolafm.ad.db.manager.AdvertDBManager):27:28 -> a
    1027:1028:void injectMembers(com.kaolafm.ad.timer.AbstractTimer):22 -> a
    27:28:void injectMAdvertDBManager(com.kaolafm.ad.timer.AbstractTimer,com.kaolafm.ad.db.manager.AdvertDBManager) -> a
    8:8:void injectMembers(java.lang.Object) -> a
    2022:2023:void injectMembers(com.kaolafm.ad.timer.AbstractTimer):22:23 -> a
    2022:2023:void injectMembers(java.lang.Object):8 -> a
    2027:2028:void injectMAdvertDBManager(com.kaolafm.ad.timer.AbstractTimer,com.kaolafm.ad.db.manager.AdvertDBManager):27:28 -> a
    2027:2028:void injectMembers(com.kaolafm.ad.timer.AbstractTimer):22 -> a
    2027:2028:void injectMembers(java.lang.Object):8 -> a
com.kaolafm.ad.timer.AdvertTask -> com.kaolafm.ad.timer.c:
    int id -> a
    long timeDuration -> b
    long timestamp -> c
    20:23:void <init>(int,long) -> <init>
    25:29:void <init>(int,long,long) -> <init>
    32:33:void setId(int) -> a
    36:37:void setTimeDuration(long) -> a
    40:40:long getTimeDuration() -> a
    44:44:int getId() -> b
    48:48:long getTimestamp() -> c
    52:53:void setTimestamp(long) -> b
    57:64:boolean equals(java.lang.Object) -> equals
    69:69:int hashCode() -> hashCode
    74:74:int compareTo(java.lang.Object) -> compareTo
com.kaolafm.ad.timer.AlarmTimer -> com.kaolafm.ad.timer.AlarmTimer:
    android.content.Context mContext -> d
    boolean started -> e
    android.app.AlarmManager mManager -> f
    java.lang.String ACTION -> g
    34:43:void <init>(android.app.Application) -> <init>
    47:55:void start() -> a
    59:73:void addTask(com.kaolafm.ad.timer.AdvertTask) -> a
    78:82:void removeTask(com.kaolafm.ad.timer.AdvertTask) -> b
    87:101:void stop() -> b
    104:114:void removeTask(long,int) -> a
    124:128:android.app.PendingIntent getIntent(long,int) -> b
    88:100:void lambda$stop$0(java.util.List) -> a
com.kaolafm.ad.timer.AlarmTimer$TimerReceiver -> com.kaolafm.ad.timer.AlarmTimer$TimerReceiver:
    com.kaolafm.ad.timer.AlarmTimer this$0 -> a
    132:132:void <init>(com.kaolafm.ad.timer.AlarmTimer) -> <init>
    136:141:void onReceive(android.content.Context,android.content.Intent) -> onReceive
com.kaolafm.ad.timer.AlarmTimer_Factory -> com.kaolafm.ad.timer.d:
    javax.inject.Provider contextProvider -> a
    javax.inject.Provider mAdvertDBManagerProvider -> b
    15:18:void <init>(javax.inject.Provider,javax.inject.Provider) -> <init>
    22:22:com.kaolafm.ad.timer.AlarmTimer get() -> get
    1027:1029:com.kaolafm.ad.timer.AlarmTimer provideInstance(javax.inject.Provider,javax.inject.Provider):27:29 -> get
    1027:1029:com.kaolafm.ad.timer.AlarmTimer get():22 -> get
    2027:2028:void com.kaolafm.ad.timer.AbstractTimer_MembersInjector.injectMAdvertDBManager(com.kaolafm.ad.timer.AbstractTimer,com.kaolafm.ad.db.manager.AdvertDBManager):27:28 -> get
    2027:2028:com.kaolafm.ad.timer.AlarmTimer provideInstance(javax.inject.Provider,javax.inject.Provider):28 -> get
    2027:2028:com.kaolafm.ad.timer.AlarmTimer get():22 -> get
    27:29:com.kaolafm.ad.timer.AlarmTimer provideInstance(javax.inject.Provider,javax.inject.Provider) -> a
    3027:3028:void com.kaolafm.ad.timer.AbstractTimer_MembersInjector.injectMAdvertDBManager(com.kaolafm.ad.timer.AbstractTimer,com.kaolafm.ad.db.manager.AdvertDBManager):27:28 -> a
    3027:3028:com.kaolafm.ad.timer.AlarmTimer provideInstance(javax.inject.Provider,javax.inject.Provider):28 -> a
    34:34:com.kaolafm.ad.timer.AlarmTimer_Factory create(javax.inject.Provider,javax.inject.Provider) -> create
    38:38:com.kaolafm.ad.timer.AlarmTimer newAlarmTimer(android.app.Application) -> a
    9:9:java.lang.Object get() -> get
com.kaolafm.ad.timer.AlarmTimer_MembersInjector -> com.kaolafm.ad.timer.e:
    javax.inject.Provider mAdvertDBManagerProvider -> a
    11:13:void <init>(javax.inject.Provider) -> <init>
    17:17:dagger.MembersInjector create(javax.inject.Provider) -> a
    22:23:void injectMembers(com.kaolafm.ad.timer.AlarmTimer) -> a
    1027:1028:void com.kaolafm.ad.timer.AbstractTimer_MembersInjector.injectMAdvertDBManager(com.kaolafm.ad.timer.AbstractTimer,com.kaolafm.ad.db.manager.AdvertDBManager):27:28 -> a
    1027:1028:void injectMembers(com.kaolafm.ad.timer.AlarmTimer):22 -> a
    8:8:void injectMembers(java.lang.Object) -> a
    2022:2023:void injectMembers(com.kaolafm.ad.timer.AlarmTimer):22:23 -> a
    2022:2023:void injectMembers(java.lang.Object):8 -> a
    2027:2028:void com.kaolafm.ad.timer.AbstractTimer_MembersInjector.injectMAdvertDBManager(com.kaolafm.ad.timer.AbstractTimer,com.kaolafm.ad.db.manager.AdvertDBManager):27:28 -> a
    2027:2028:void injectMembers(com.kaolafm.ad.timer.AlarmTimer):22 -> a
    2027:2028:void injectMembers(java.lang.Object):8 -> a
com.kaolafm.ad.timer.JobTimer -> com.kaolafm.ad.timer.f:
    android.content.Context mContext -> d
    android.app.job.JobScheduler mScheduler -> e
    android.content.ComponentName mComponentName -> f
    27:29:void <init>(android.content.Context) -> <init>
    33:35:void start() -> a
    39:61:void addTask(com.kaolafm.ad.timer.AdvertTask) -> a
    65:66:void removeTask(com.kaolafm.ad.timer.AdvertTask) -> b
    71:72:void stop() -> b
com.kaolafm.ad.timer.Task -> com.kaolafm.ad.timer.g:
com.kaolafm.ad.timer.TimedAdvertManager -> com.kaolafm.ad.timer.TimedAdvertManager:
    com.kaolafm.ad.timer.TimedAdvertManager mInstance -> mInstance
    java.util.Set mTasks -> mTasks
    com.kaolafm.ad.db.manager.AdvertDBManager mAdvertDBManager -> mAdvertDBManager
    com.kaolafm.ad.api.internal.AdInternalRequest mInternalRequest -> mInternalRequest
    com.kaolafm.ad.timer.Timer mTimer -> mTimer
    33:50:void <init>() -> <init>
    53:60:com.kaolafm.ad.timer.TimedAdvertManager getInstance() -> getInstance
    65:77:void start(int,java.lang.String,java.lang.String) -> start
    80:107:void updateTimer(java.util.List) -> updateTimer
    114:118:void cancelOldTask(java.util.Set) -> cancelOldTask
    126:128:void addTask(com.kaolafm.ad.timer.AdvertTask) -> addTask
    136:140:void addTasks(java.util.List) -> addTasks
    148:156:void removeTask(long) -> removeTask
    159:161:void removeTask(com.kaolafm.ad.timer.AdvertTask) -> removeTask
    164:164:com.kaolafm.ad.timer.Timer getTimer() -> getTimer
    173:175:void clear() -> clear
    182:185:void stop() -> stop
    29:29:void access$000(com.kaolafm.ad.timer.TimedAdvertManager,java.util.List) -> access$000
com.kaolafm.ad.timer.TimedAdvertManager$1 -> com.kaolafm.ad.timer.h:
    com.kaolafm.ad.timer.TimedAdvertManager this$0 -> a
    66:66:void <init>(com.kaolafm.ad.timer.TimedAdvertManager) -> <init>
    69:70:void onSuccess(java.util.List) -> a
    74:74:void onError$626d7010() -> a
    66:66:void onSuccess(java.lang.Object) -> a
    1069:1070:void onSuccess(java.util.List):69:70 -> a
    1069:1070:void onSuccess(java.lang.Object):66 -> a
com.kaolafm.ad.timer.TimedAdvertManager_MembersInjector -> com.kaolafm.ad.timer.i:
    javax.inject.Provider mAdvertDBManagerProvider -> a
    javax.inject.Provider mInternalRequestProvider -> b
    javax.inject.Provider mTimerProvider -> c
    20:24:void <init>(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> <init>
    30:30:dagger.MembersInjector create(javax.inject.Provider,javax.inject.Provider,javax.inject.Provider) -> a
    36:39:void injectMembers(com.kaolafm.ad.timer.TimedAdvertManager) -> a
    1043:1044:void injectMAdvertDBManager(com.kaolafm.ad.timer.TimedAdvertManager,com.kaolafm.ad.db.manager.AdvertDBManager):43:44 -> a
    1043:1044:void injectMembers(com.kaolafm.ad.timer.TimedAdvertManager):36 -> a
    1048:1049:void injectMInternalRequest(com.kaolafm.ad.timer.TimedAdvertManager,com.kaolafm.ad.api.internal.AdInternalRequest):48:49 -> a
    1048:1049:void injectMembers(com.kaolafm.ad.timer.TimedAdvertManager):37 -> a
    1052:1053:void injectMTimer(com.kaolafm.ad.timer.TimedAdvertManager,com.kaolafm.ad.timer.Timer):52:53 -> a
    1052:1053:void injectMembers(com.kaolafm.ad.timer.TimedAdvertManager):38 -> a
    43:44:void injectMAdvertDBManager(com.kaolafm.ad.timer.TimedAdvertManager,com.kaolafm.ad.db.manager.AdvertDBManager) -> a
    48:49:void injectMInternalRequest(com.kaolafm.ad.timer.TimedAdvertManager,com.kaolafm.ad.api.internal.AdInternalRequest) -> a
    52:53:void injectMTimer(com.kaolafm.ad.timer.TimedAdvertManager,com.kaolafm.ad.timer.Timer) -> a
    9:9:void injectMembers(java.lang.Object) -> a
    2036:2039:void injectMembers(com.kaolafm.ad.timer.TimedAdvertManager):36:39 -> a
    2036:2039:void injectMembers(java.lang.Object):9 -> a
    2043:2044:void injectMAdvertDBManager(com.kaolafm.ad.timer.TimedAdvertManager,com.kaolafm.ad.db.manager.AdvertDBManager):43:44 -> a
    2043:2044:void injectMembers(com.kaolafm.ad.timer.TimedAdvertManager):36 -> a
    2043:2044:void injectMembers(java.lang.Object):9 -> a
    2048:2049:void injectMInternalRequest(com.kaolafm.ad.timer.TimedAdvertManager,com.kaolafm.ad.api.internal.AdInternalRequest):48:49 -> a
    2048:2049:void injectMembers(com.kaolafm.ad.timer.TimedAdvertManager):37 -> a
    2048:2049:void injectMembers(java.lang.Object):9 -> a
    2052:2053:void injectMTimer(com.kaolafm.ad.timer.TimedAdvertManager,com.kaolafm.ad.timer.Timer):52:53 -> a
    2052:2053:void injectMembers(com.kaolafm.ad.timer.TimedAdvertManager):38 -> a
    2052:2053:void injectMembers(java.lang.Object):9 -> a
com.kaolafm.ad.timer.Timer -> com.kaolafm.ad.timer.j:
    void start() -> a
    void addTask(com.kaolafm.ad.timer.AdvertTask) -> a
    void removeTask(com.kaolafm.ad.timer.AdvertTask) -> b
    void stop() -> b
com.kaolafm.ad.timer.TimerJobService -> com.kaolafm.ad.timer.TimerJobService:
    16:17:void <init>() -> <init>
    21:28:boolean onStartJob(android.app.job.JobParameters) -> onStartJob
    33:33:boolean onStopJob(android.app.job.JobParameters) -> onStopJob
com.kaolafm.ad.util.AdBeanUtil -> com.kaolafm.ad.util.AdBeanUtil:
    20:21:void <init>() -> <init>
    30:53:com.kaolafm.ad.api.model.Advert transform(com.kaolafm.ad.api.model.AdvertisingDetails) -> transform
    63:77:void setupAdvert(com.kaolafm.ad.api.model.AdvertisingDetails,com.kaolafm.ad.api.model.Advert) -> setupAdvert
    86:91:com.kaolafm.ad.api.model.AudioImageAdvert createAudioImageAdvert(com.kaolafm.ad.api.model.AdvertisingDetails) -> createAudioImageAdvert
    101:109:com.kaolafm.ad.api.model.ImageAdvert createImageAdvert(com.kaolafm.ad.api.model.AdvertisingDetails) -> createImageAdvert
    113:120:com.kaolafm.ad.api.model.AttachImage createAttachImage(com.kaolafm.ad.api.model.AdvertisingDetails) -> createAttachImage
    130:132:com.kaolafm.ad.api.model.AudioAdvert createAudioAdvert(com.kaolafm.ad.api.model.AdvertisingDetails) -> createAudioAdvert
    142:146:void setupAudioAdvert(com.kaolafm.ad.api.model.AdvertisingDetails,com.kaolafm.ad.api.model.AudioAdvert) -> setupAudioAdvert
    155:177:com.kaolafm.ad.api.model.InteractionAdvert createInteractionAdvert(com.kaolafm.ad.api.model.AdvertisingDetails) -> createInteractionAdvert
    181:181:java.lang.String getLocalPath(java.lang.String,java.lang.String) -> getLocalPath
com.kaolafm.ad.util.AdParamsUtil -> com.kaolafm.ad.util.AdParamsUtil:
    18:18:void <init>() -> <init>
    21:25:java.lang.String createParamData(java.util.List) -> createParamData
    29:40:java.lang.String createParamData(java.util.Set,java.lang.String[]) -> createParamData
    44:52:boolean exclude(java.lang.String[],java.lang.String) -> exclude
    61:61:java.lang.String getTime() -> getTime
    75:96:java.lang.String getAdvanceAttrs(long,long,long,long,int) -> getAdvanceAttrs
    108:108:java.lang.String getAlbumAttrs(long,long,int) -> getAlbumAttrs
    120:120:java.lang.String getRadioAttrs(long,long,int) -> getRadioAttrs
com.kaolafm.ad.util.DownloadUtil -> com.kaolafm.ad.util.DownloadUtil:
    java.lang.String RESOURCES_PATH -> RESOURCES_PATH
    java.lang.String IMAGE_PATH -> IMAGE_PATH
    java.lang.String AUDIO_PATH -> AUDIO_PATH
    31:32:void <init>() -> <init>
    35:53:void download(com.kaolafm.ad.api.model.AdvertisingDetails) -> download
    56:59:java.lang.String uniqueKey(java.lang.String,java.lang.String) -> uniqueKey
    63:65:void delete(com.kaolafm.ad.api.model.AdvertisingDetails) -> delete
    67:72:void delete(java.lang.String,java.lang.String[]) -> delete
    75:79:void deleteSPAndFile(java.lang.String,java.lang.String,java.lang.String) -> deleteSPAndFile
    82:82:java.lang.String getLocalPath(java.lang.String,java.lang.String) -> getLocalPath
    25:25:void <clinit>() -> <clinit>
com.kaolafm.ad.util.DownloadUtil$DownloadListenerWrapper -> com.kaolafm.ad.util.DownloadUtil$DownloadListenerWrapper:
    java.lang.String key -> key
    89:91:void <init>(java.lang.String,java.lang.String) -> <init>
    96:96:void onStart() -> onStart
    101:101:void onProgress(com.kaolafm.opensdk.http.download.DownloadProgress) -> onProgress
    105:107:void onSuccess(java.io.File) -> onSuccess
    111:112:void onError(com.kaolafm.opensdk.http.error.ApiException) -> onError
    85:85:void onProgress(java.lang.Object) -> onProgress
    85:85:void onSuccess(java.lang.Object) -> onSuccess
