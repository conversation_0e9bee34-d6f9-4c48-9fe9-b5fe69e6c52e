<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="10dp">

    <EditText
        android:id="@+id/et_login_phone_num"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="输入手机号"
        android:inputType="phone"
        app:layout_constraintStart_toStartOf="parent"
        tools:layout_editor_absoluteY="10dp" />

    <Button
        android:id="@+id/btn_login_check"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="检测手机号是否注册"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <EditText
        android:id="@+id/et_login_code"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:hint="输入验证码"
        android:inputType="phone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/et_login_phone_num" />

    <Button
        android:id="@+id/btn_login_get_code"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="获取验证码"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/et_login_phone_num" />

    <RadioGroup
        android:id="@+id/rg_login_sex"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@id/et_login_code"

        >

        <RadioButton
            android:id="@+id/rb_login_man"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="男" />

        <RadioButton
            android:id="@+id/rb_login_woman"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="女" />
    </RadioGroup>

    <EditText
        android:id="@+id/et_login_age"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="输入年龄(无限制)"
        app:layout_constraintTop_toBottomOf="@id/rg_login_sex" />

    <Button
        android:id="@+id/btn_login_register_commit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="登录"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/et_login_age" />

    <TextView
        android:id="@+id/tv_login_or_register"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:autoLink="all"
        android:text="去登陆"
        android:textColor="@color/colorAccent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn_login_register_commit" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="et_login_age,rg_login_sex"
        tools:layout_editor_absoluteX="10dp"
        tools:layout_editor_absoluteY="10dp" />

    <TextView
        android:id="@+id/tv_login_user_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toBottomOf="@id/tv_login_or_register"
        />
    <Button
        android:id="@+id/btn_kaola_logout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="退出登录"
        app:layout_constraintTop_toBottomOf="@id/tv_login_or_register"
        />


</androidx.constraintlayout.widget.ConstraintLayout>
