package com.kaolafm.opensdk.api.operation.model.column;

/**
 * 栏目成员：听电视
 */
public class TVDetailColumnMember extends ColumnContent {

    /** 听电视的id*/
    private long listenTVid;

    /** 听电视的收听数*/
    private int playTimes;

    /** 用来区分听电视类型（音乐，交通，新闻等）*/
    private int tvSort;

    public long getListenTVid() {
        return listenTVid;
    }

    public void setListenTVid(long listenTVid) {
        this.listenTVid = listenTVid;
    }

    public int getTvSort() {
        return tvSort;
    }

    public void setTvSort(int tvSort) {
        this.tvSort = tvSort;
    }

    public int getPlayTimes() {
        return playTimes;
    }

    public void setPlayTimes(int playTimes) {
        this.playTimes = playTimes;
    }

    @Override
    public String toString() {
        return "TVDetailColumnMember{" +
                "listenTVid=" + listenTVid +
                ", playTimes=" + playTimes +
                ", tvSort=" + tvSort +
                '}';
    }
}
