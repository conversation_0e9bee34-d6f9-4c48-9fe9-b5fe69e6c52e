package com.kaolafm.opensdk.player.core;

import static android.media.AudioManager.AUDIOFOCUS_LOSS;
import static android.media.AudioManager.AUDIOFOCUS_LOSS_TRANSIENT;
import static android.media.AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK;
import static android.media.AudioManager.AUDIOFOCUS_NONE;

import android.app.Service;
import android.content.Intent;
import android.media.AudioAttributes;
import android.media.AudioManager;
import android.os.Binder;
import android.os.Build;
import android.os.IBinder;
import android.text.TextUtils;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.player.core.ijk.VideoView;
import com.kaolafm.opensdk.player.core.listener.IPlayerBufferProgressListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerStateCoreListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerStateVideoCoreListener;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInner;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter;
import com.kaolafm.opensdk.player.core.model.AAudioFocus;
import com.kaolafm.opensdk.player.core.model.AMediaPlayer;
import com.kaolafm.opensdk.player.core.model.AudioFadeConfig;
import com.kaolafm.opensdk.player.core.utils.AudioFocusManager;
import com.kaolafm.opensdk.player.core.utils.ContextMediaPlayer;
import com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;

/**
 * <AUTHOR> on 2019-05-24.
 */

public class PlayerService extends Service {
    private final String TAG = "PlayerService";

    private ContextMediaPlayer mContextMediaPlayer;
    private String mPlayingUri;
    private boolean mLastIsVideo = false;
    private String mPlayUrlId;
    private boolean mIsLiving;
    private AAudioFocus mAudioFocusManager;
    private OnAudioFocusChangeInter mIAudioFocusListener;
    /**
     * 音频焦点状态同步锁
     * 主要防止在异步场景下通知拿到了音频焦点但依旧判断音频焦点丢失的情况
     */
    private final Object mAudioFocusLock = new Object();

    /**
     * 是否失去了音频焦点
     */
    private boolean isLoseAudioFocus = true;

    /**
     * 焦点变化时, 播放状态
     */
    private int mPrePlayStatus;

    /**
     * 上次焦点状态
     */
    private int mPreFocusChange = AUDIOFOCUS_NONE;
    /**
     * 当前的音频焦点
     */
    private int mCurrentAudioFocusState = AUDIOFOCUS_NONE;

    private IPlayerStateCoreListener mIPlayerStateCoreListener;

    /**
     * 是否启用淡入淡出
     */
    private boolean audioFadeEnabled = true;

    private String httpProxy = null;

    // 是否清除dns缓存
    private boolean clearDnsCache = false;

    /**
     * 淡入淡出效果配置信息
     */
    private AudioFadeConfig audioFadeConfig;

    @Override
    public IBinder onBind(Intent intent) {
        return new PlayerServiceBinder();
    }

    @Override
    public void onCreate() {
        super.onCreate();
        initContextMediaPlayer();
    }

    private void initContextMediaPlayer() {
        mContextMediaPlayer = new ContextMediaPlayer();
    }


    public class PlayerServiceBinder extends Binder {
        public void initPlayer() {
            initPlayerInner();
        }

        public AMediaPlayer getMediaPlayer() {
            return mContextMediaPlayer.getMediaPlayer();
        }

        public void start(String uri, long duration) {
            start(uri, duration, 0, false, null, null);
        }

        public void start(String uri, long duration, boolean isLiving) {
            start(uri, duration, 0, isLiving, null, null);
        }

        public void start(String uri, long duration, boolean isLiving, VideoView videoView) {
            start(uri, duration, 0, isLiving, videoView, null);
        }

        public void start(String uri, long duration, long position, boolean isLiving) {
            start(uri, duration, position, isLiving, null, null);
        }

        public void start(String uri, long duration, long position, boolean isLiving, VideoView videoView) {
            start(uri, duration, position, isLiving, videoView, null);
        }

        public void start(String uri, long duration, long position, boolean isLiving, VideoView videoView, String playUrlId) {
            startInner(uri, duration, position, isLiving, videoView, playUrlId);
        }

        public void setPosition(long sec) {
            setPositionInner(sec);
        }

        public void pause() {
            pauseInner();
        }

        public void play() {
            playInner();
        }

        public void stop() {
            stopInner();
        }

        public void reset(boolean needResetLastPlaybackRateFlag) {
            resetInner(needResetLastPlaybackRateFlag);
        }

        public void rePlay() {
            rePlayInner();
        }

        public void release() {
            releaseInner();
        }

        public void seek(long mSec) {
            seekInner(mSec);
        }

        public void setPlayerStateListener(IPlayerStateCoreListener iPlayerState) {
            setPlayerStateListenerInner(iPlayerState);
        }

        public void setPlayerStateVideoListener(IPlayerStateVideoCoreListener iPlayerStateVideoCoreListener) {
            setPlayerStateVideoListenerInner(iPlayerStateVideoCoreListener);
        }

        public void setPlayerBufferProgressListener(IPlayerBufferProgressListener listener) {
            setPlayerBufferProgressListenerInner(listener);
        }

        public void setInitCompleteListener(IPlayerInitCompleteListener iPlayerInitCompleteListener) {
            setInitCompleteListenerInner(iPlayerInitCompleteListener);
        }

        public void setLoudnessNormalization(int active) {
            setLoudnessNormalizationInner(active);
        }

        public boolean isPlaying() {
            return isPlayingInner();
        }

        public int getPlayStatus() {
            return getPlayStatusInner();
        }

        public void setPlayStatus(int status) {
            setPlayStatusInner(status);
        }

        public boolean requestAudioFocus() {
            return requestAudioFocusInner();
        }

        public void setCustomAudioAttributes(AudioAttributes customAudioAttributes) {
            mAudioFocusManager.setCustomAudioAttributes(customAudioAttributes);
        }

        public boolean abandonAudioFocus() {
            return abandonAudioFocusInner();
        }

        public void setCustomAudioFocus(AAudioFocus audioFocus) {
            setCustomAudioFocusInner(audioFocus);
        }

        public void setAudioFocusListener(OnAudioFocusChangeInter iAudioFocusListener) {
            setAudioFocusListenerInner(iAudioFocusListener);
        }

        public long getCurrentPosition() {
            return getCurrentPositionInner();
        }

        public void initPlayerForce(){
            initPlayerForceInner();
        }

        public void setMediaVolume(float leftVolume, float rightVolume) {
            setMediaVolumeInner(leftVolume, rightVolume);
        }

        public void setPlaybackRate(float rate) {
            setPlaybackRateInner(rate);
        }

        public float getPlaybackRate() {
            return getPlaybackRateInner();
        }

        public void setUsageAndContentType(int usage, int contentType) {
            setUsageAndContentTypeInner(usage, contentType);
        }

        public void setLogInValid() {
            setLogInValidInner();
        }

        public void setPlayUrl(String uri, long position, long duration, VideoView videoView) {
            setPlayUrlInner(uri, position, duration, videoView);
        }

        public void disableAudioFade() {
            setAudioFadeEnabledInner(false);
        }

        public void setHttpProxy(String httpProxy) {
            setHttpProxyInner(httpProxy);
        }

        public void clearHttpProxy() {
            setHttpProxyInner(null);
        }

        public void clearDnsCache(boolean clearDnsCache) {
            setClearDnsCacheInner(clearDnsCache);
        }

        public void setAudioFadeConfig(AudioFadeConfig audioFadeConfig) {
            PlayerLogUtil.log(TAG, "setAudioFadeConfig, audioFadeConfig = " + audioFadeConfig.toString());
            setAudioFadeConfigInner(audioFadeConfig);
        }

        public void setPlayNowOnPrepared(boolean isPlayNow) {
            PlayerLogUtil.log(TAG, "setPlayNowOnPrepared, isPlayNow = " + isPlayNow);
            setPlayNowOnPreparedInner(isPlayNow);
        }
    }

    private void initPlayerInner() {
        mContextMediaPlayer.initPlayer(ContextMediaPlayer.TYPE_IJK_MEDIA_PLAYER, this);
        mAudioFocusManager = new AudioFocusManager(PlayerService.this);
        mAudioFocusManager.setAudioFocusListener(onAudioFocusChangeInner);
    }

    private void startInner(String uri, long duration, long position) {
        // done 此方法调用已检查处理完成
        startInner(uri, duration, position, false, null, null);
    }

    private void startInner(String uri, long duration, long position, boolean isLiving, VideoView videoView) {
        // done 此方法调用已检查处理完成
        startInner(uri, duration, position, isLiving, videoView, null);
    }

    /**
     * 开始第一次播放的核心方法
     */
    private void startInner(String uri, long duration, long position, boolean isLiving, VideoView videoView, String playUrlId) {
        // done 此方法调用已检查处理完成
        PlayerLogUtil.log(TAG, "startInner, uri=" + uri + ", duration=" + duration + ", position=" + position + ", isLiving=" + isLiving + ", playUrlId=" + playUrlId);
        if (StringUtil.isEmpty(uri)) {
            iPlayerStateCoreListener.onPlayerFailed("", PlayerConstants.ERROR_CODE_PLAY_LIST_URL_NULL, -1, "");
            return;
        }
        // 日志检索：tag:player_log_tag message:PlaybackRate
        String newPlayUrlId = generateIdentByUrlAndId(uri, playUrlId);
        boolean isVideo = videoView != null;
        boolean needUseLastPlaybackRate =
                !isLiving
                        && isVideo
                        && !TextUtils.isEmpty(mPlayUrlId)
                        && !TextUtils.isEmpty(newPlayUrlId)
                        && TextUtils.equals(mPlayUrlId, newPlayUrlId);

        mIsLiving = isLiving;
        mPlayingUri = uri;

        boolean shouldResetPlaybackRate = isVideo || !mLastIsVideo;

        if (shouldResetPlaybackRate) {
            mPlayUrlId = newPlayUrlId;
        }
        mLastIsVideo = isVideo;

        PlayerCustomizeManager playerCustomizeManager = PlayerCustomizeManager.getInstance();
        if (playerCustomizeManager.disposePlay()) {
            // 阻断之后，获取音频焦点还能从此继续播放
            setPlayUrlInner(uri, position, duration, videoView);
            return;
        }

        if (!requestAudioFocus()) {
            setPlayUrlInner(uri, position, duration, videoView);
            return;
        }

        int streamTypeChannel = playerCustomizeManager.getStreamChannel();
        synchronized (PlayerService.class) {
            mContextMediaPlayer.getMediaPlayer().start(uri, duration, position, streamTypeChannel, audioFadeEnabled, audioFadeConfig, httpProxy, clearDnsCache, videoView, needUseLastPlaybackRate, shouldResetPlaybackRate);
        }
    }

    private String generateIdentByUrlAndId(String url, String playUrlId) {
        if (!TextUtils.isEmpty(playUrlId)) {
            return playUrlId;
        }
        if (!TextUtils.isEmpty(url)) {
            return url;
        }
        return "";
    }

    private void setPositionInner(long sec) {
        mContextMediaPlayer.getMediaPlayer().seek(sec);
    }

    private void pauseInner() {
        mContextMediaPlayer.getMediaPlayer().pause();
    }

    private void playInner() {
        if (PlayerCustomizeManager.getInstance().disposePlay()) {
            return;
        }
        if (!requestAudioFocus()) {
            return;
        }
        mContextMediaPlayer.getMediaPlayer().play();
    }

    private void stopInner() {
        mContextMediaPlayer.getMediaPlayer().stop();
    }

    private void resetInner() {
        resetInner(true);
    }

    private void resetInner(boolean needResetLastPlaybackRateFlag) {
        mContextMediaPlayer.getMediaPlayer().reset(needResetLastPlaybackRateFlag);
    }

    private void rePlayInner() {
        if (!requestAudioFocus()) {
            return;
        }
        mContextMediaPlayer.getMediaPlayer().rePlay();
    }

    private void releaseInner() {
        mContextMediaPlayer.getMediaPlayer().release();
    }

    private void seekInner(long mSec) {
        mContextMediaPlayer.getMediaPlayer().seek(mSec);
    }

    private void setPlayerStateListenerInner(IPlayerStateCoreListener iPlayerState) {
        mIPlayerStateCoreListener = iPlayerState;
        mContextMediaPlayer.getMediaPlayer().setPlayerStateListener(iPlayerStateCoreListener);
    }

    private void setPlayerStateVideoListenerInner(IPlayerStateVideoCoreListener iPlayerStateVideoCoreListener) {
        mContextMediaPlayer.getMediaPlayer().setPlayerStateVideoListener(iPlayerStateVideoCoreListener);
    }

    private void setPlayerBufferProgressListenerInner(IPlayerBufferProgressListener listener) {
        mContextMediaPlayer.getMediaPlayer().setBufferProgressListener(listener);
    }

    private void setInitCompleteListenerInner(IPlayerInitCompleteListener iPlayerInitCompleteListener) {
        mContextMediaPlayer.getMediaPlayer().setInitPlayerInitCompleteListener(iPlayerInitCompleteListener);
    }

    private void setLoudnessNormalizationInner(int active) {
        mContextMediaPlayer.getMediaPlayer().setLoudnessNormalization(active);
    }

    private boolean isPlayingInner() {
        return mContextMediaPlayer.getMediaPlayer().isPlaying();
    }

    private void setCustomAudioFocusInner(AAudioFocus audioFocus) {
        if (audioFocus == null) {
            return;
        }
        mAudioFocusManager = audioFocus;
        audioFocus.setAudioFocusListener(onAudioFocusChangeInner);
    }

    private void setAudioFocusListenerInner(OnAudioFocusChangeInter iAudioFocusListener) {
        mIAudioFocusListener = iAudioFocusListener;
    }

    private boolean requestAudioFocusInner() {
        return mAudioFocusManager.requestAudioFocus();
    }


    private boolean abandonAudioFocusInner() {
        return mAudioFocusManager.abandonAudioFocus();
    }

    private void managerAudioFocusChange(int focusChange) {
        PlayerLogUtil.log(TAG, "managerAudioFocusChange", "focusChange = " + focusChange);
        if (focusChange == AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK) {
            if (isPlayingInner()) {
                mPrePlayStatus = PlayerConstants.TYPE_PLAYER_PLAYING;
                if (!PlayerCustomizeManager.getInstance().disposeAudioFocusChangeDuck()) {
                    setMediaVolumeInner(PlayerConstants.LEFT_VOLUME_MIN, PlayerConstants.RIGHT_VOLUME_MIN);
                }
            } else {
                setMediaVolumeInner(PlayerConstants.LEFT_VOLUME_MAX, PlayerConstants.RIGHT_VOLUME_MAX);
                if (mPreFocusChange != focusChange) {
                    mPrePlayStatus = getPlayStatusInner();
                }
            }
        } else if (focusChange == AudioManager.AUDIOFOCUS_GAIN) {
            manageGainFocus();
        } else if (focusChange == AUDIOFOCUS_LOSS || focusChange == AUDIOFOCUS_LOSS_TRANSIENT) {
            manageLossFocus();
        }

        mPreFocusChange = focusChange;
    }

    private void manageGainFocus() {
        boolean isPauseFromUser = PlayerManager.getInstance().isPauseFromUser();
        PlayerLogUtil.log(TAG, "manageGainFocus", "mPreFocusChange = " + mPreFocusChange + ", mPrePlayStatus = " + mPrePlayStatus + ", isPauseFromUser = " + isPauseFromUser);
        //当重新获取焦点后, 检测是否是用户暂停
        if (isPauseFromUser) {
            return;
        }
        if (mPreFocusChange == AUDIOFOCUS_LOSS_TRANSIENT || mPreFocusChange == AUDIOFOCUS_LOSS || mPreFocusChange == AUDIOFOCUS_NONE) {
            setMediaVolumeInner(PlayerConstants.LEFT_VOLUME_MAX, PlayerConstants.RIGHT_VOLUME_MAX);
            if(mPrePlayStatus==PlayerConstants.TYPE_PLAYER_PLAYING){
                if (mIsLiving) {
                    startInner(mPlayingUri, 0, 0);
                } else {
                    playInner();
                }
            }
            PlayerCustomizeManager.getInstance().notifyPlayerStateChangedByAudioFocus(true);
        } else if (mPreFocusChange == AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK) {
            setMediaVolumeInner(PlayerConstants.LEFT_VOLUME_MAX, PlayerConstants.RIGHT_VOLUME_MAX);
        }
    }

    private void manageLossFocus() {
        if (isPlayingInner()) {
            pauseInner();
            PlayerCustomizeManager.getInstance().notifyPlayerStateChangedByAudioFocus(false);
        }
    }

    /**
     * 检查音频焦点
     */
    private boolean requestAudioFocus() {
        if (!isAudioFocusLost()){
            PlayerLogUtil.log(TAG,"requestAudioFocus return true");
            return true;
        }
        if (mContextMediaPlayer.getMediaPlayer().getPlayNow() || PlayerManager.getInstance().isPlayIsNow()){
            PlayerLogUtil.log(TAG,"mContextMediaPlayer.getMediaPlayer().getPlayNow() is " + mContextMediaPlayer.getMediaPlayer().getPlayNow());
            PlayerLogUtil.log(TAG,"PlayerManager.getInstance().isPlayIsNow() is " + PlayerManager.getInstance().isPlayIsNow());
            PlayerManager.getInstance().setPlayIsNow(false);
            PlayerLogUtil.log(TAG,"PlayerManager.getInstance().setPlayIsNow(false)");
            return requestAudioFocusInner();
        } else {
            PlayerLogUtil.log(TAG,"requestAudioFocus return false");
            return false;
        }
    }

    // 用来表明是否失去了音频焦点
    private boolean isAudioFocusLost() {
        synchronized (mAudioFocusLock) {
            boolean need = PlayerCustomizeManager.getInstance().isNeedRequestAudioFocus();
            PlayerLogUtil.log(TAG, "lossFocus", "need = " + need + ", isLoseAudioFocus = " + isLoseAudioFocus);
            if (!need) {
                return false;
            }
            if (!isLoseAudioFocus){
                return false;
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                return mCurrentAudioFocusState == AUDIOFOCUS_LOSS || mCurrentAudioFocusState == AUDIOFOCUS_LOSS_TRANSIENT || mCurrentAudioFocusState == AUDIOFOCUS_NONE;
            } else {
                return mCurrentAudioFocusState == AUDIOFOCUS_LOSS || mCurrentAudioFocusState == AUDIOFOCUS_LOSS_TRANSIENT;
            }
        }
    }

    private void saveLostAudioFocusBeforePlayState(int focusChange) {
        if (focusChange != AUDIOFOCUS_LOSS && focusChange != AUDIOFOCUS_LOSS_TRANSIENT) {
            return;
        }
        if (isPlayingInner()) {
            mPrePlayStatus = PlayerConstants.TYPE_PLAYER_PLAYING;
        } else {
            mPrePlayStatus = PlayerConstants.TYPE_PLAYER_IDLE;
        }
        PlayerLogUtil.log(TAG, "saveLostAudioFocusBeforePlayState", "mPrePlayStatus=" + mPrePlayStatus);
    }

    private final OnAudioFocusChangeInner onAudioFocusChangeInner = new OnAudioFocusChangeInner() {
        @Override
        public void onAudioFocusChange(boolean isUseBySystem, int focusChange) {
            PlayerLogUtil.log(TAG, "onAudioFocusChangeInner", "focusChange =" + focusChange);
            synchronized (mAudioFocusLock) {
                isLoseAudioFocus = focusChange != AudioManager.AUDIOFOCUS_REQUEST_GRANTED;
                mCurrentAudioFocusState = focusChange;
            }
            saveLostAudioFocusBeforePlayState(focusChange);
            if (mIAudioFocusListener != null) {
                mIAudioFocusListener.onAudioFocusChange(focusChange);
            }

            if (isUseBySystem) {
                managerAudioFocusChange(focusChange);
            }
        }
    };

    /**
     * 获取播放状态
     *
     * @return
     */
    private int getPlayStatusInner() {
        return mContextMediaPlayer.getMediaPlayer().getPlayStatus();
    }

    private void setPlayStatusInner(int status) {
        mContextMediaPlayer.getMediaPlayer().setPlayStatus(status);
    }

    /**
     * 获取当前进度
     *
     * @return
     */
    private long getCurrentPositionInner() {
        return mContextMediaPlayer.getMediaPlayer().getCurrentPosition();
    }

    private void initPlayerForceInner(){
        mContextMediaPlayer.getMediaPlayer().initPlayerForce();
    }

    /**
     * 设置音量
     *
     * @param leftVolume
     * @param rightVolume
     */
    private void setMediaVolumeInner(float leftVolume, float rightVolume) {
        mContextMediaPlayer.getMediaPlayer().setMediaVolume(leftVolume, rightVolume);
    }

    private void setPlaybackRateInner(float rate) {
        mContextMediaPlayer.getMediaPlayer().setPlaybackRate(rate);
    }

    private float getPlaybackRateInner() {
        return mContextMediaPlayer.getMediaPlayer().getPlaybackRate();
    }

    /**
     * 设置AudioTrack的 Usage和ContentType
     *
     * @param usage
     * @param contentType
     */
    private void setUsageAndContentTypeInner(int usage, int contentType) {
        mContextMediaPlayer.getMediaPlayer().setUsageAndContentType(usage, contentType);
    }

    private void setLogInValidInner() {
        mContextMediaPlayer.getMediaPlayer().setLogInValid();
    }

    private void setPlayUrlInner(String uri, long position, long duration, VideoView videoView) {
        PlayerLogUtil.log(TAG, "setPlayUrlInner", "url= " + uri + "; position = " + position + "; duration = " + duration);

        AMediaPlayer player = mContextMediaPlayer.getMediaPlayer();
        if (player == null) {
            return;
        }
//        player.reset(true);
        if (videoView != null) {
            player.setDisplay(videoView.getSurfaceHolder());
        }
        player.setDataSource(uri);
        long realDuration = duration > 0 ? duration : 0;
        player.setDuration(realDuration, realDuration);
        player.seekAtStart(position > 0 ? position : 0);
        player.prepare(1);
    }

    private void doPlayAfterPrepared() {
        //TODO:getPlayNow;
        boolean lf = isAudioFocusLost();
        PlayerLogUtil.log(TAG, "doPlayAfterPrepared", "lf = " + lf);
        if (lf) {
            if (mIsLiving) {
                resetInner();
            } else {
                pauseInner();
            }
            PlayerManager.getInstance().setPauseFromUser(false);
            mPrePlayStatus = PlayerConstants.TYPE_PLAYER_PLAYING;
        } else {
            mContextMediaPlayer.getMediaPlayer().play();
        }
    }

    private final IPlayerStateCoreListener iPlayerStateCoreListener = new IPlayerStateCoreListener() {
        @Override
        public void onIdle(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onIdle(url);
            }
        }

        @Override
        public void onPlayerPreparingComplete(String url) {
            PlayerLogUtil.log(TAG, "onPlayerPreparingComplete", "mCurrentAudioFocusState = " + mCurrentAudioFocusState);
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onPlayerPreparingComplete(url);
            }
            doPlayAfterPrepared();
        }


        @Override
        public void onPlayerPreparing(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onPlayerPreparing(url);
            }
        }

        @Override
        public void onPlayerPlaying(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onPlayerPlaying(url);
            }
        }

        @Override
        public void onPlayerPaused(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onPlayerPaused(url);
            }
        }

        @Override
        public void onProgress(String url, long progress, long total) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onProgress(url, progress, total);
            }
        }

        @Override
        public void onPlayerFailed(String url, int what, int extra, String dnsAddress) {
            stopInner();
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onPlayerFailed(url, what, extra, dnsAddress);
            }
        }

        @Override
        public void onPlayerEnd(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onPlayerEnd(url);
            }
        }

        @Override
        public void onSeekStart(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onSeekStart(url);
            }
        }

        @Override
        public void onSeekComplete(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onSeekComplete(url);
            }
        }

        @Override
        public void onBufferingStart(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onBufferingStart(url);
            }
        }

        @Override
        public void onBufferingEnd(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onBufferingEnd(url);
            }
        }

        @Override
        public void onInteractionFired(String url, int position, int id) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onInteractionFired(url, position, id);
            }
        }
    };

    private void setAudioFadeEnabledInner(boolean audioFadeEnabled) {
        this.audioFadeEnabled = audioFadeEnabled;
    }

    private void setHttpProxyInner(String httpProxy) {
        this.httpProxy = httpProxy;
    }

    private void setClearDnsCacheInner(boolean clearDnsCache) {
        this.clearDnsCache = clearDnsCache;
    }

    private void setAudioFadeConfigInner(AudioFadeConfig audioFadeConfig) {
        this.audioFadeConfig = audioFadeConfig;
    }

    private void setPlayNowOnPreparedInner(boolean isPlayNow) {
        mContextMediaPlayer.getMediaPlayer().setPlayNowOnPrepared(isPlayNow);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
