package com.kaolafm.base.utils;

import android.net.Uri;
import android.text.TextUtils;

import java.util.Arrays;

/******************************************
 * 类描述： 处理url工具类 类名称：UrlUtil
 *
 * @version: 1.0
 * @author: shaoning<PERSON>ang
 * @time: 2016-7-19 10:24
 ******************************************/

public final class UrlUtil {
    private UrlUtil() {
    }

    public final static String PIC_100_100 = "/100_100";
    public final static String PIC_250_250 = "/250_250";
    public final static String PIC_340_340 = "/340_340";
    public final static String PIC_720_254 = "/720_254";
    public final static String PIC_550_550 = "/550_550";

    private static final String JPG_STR = ".JPG";
    private static final String JPEG_STR = ".JPEG";
    private static final String PNG_STR = ".PNG";

    public static String getCustomPicUrl(String type, String url) {
        if (url == null) {
            return "";
        }
        if (TextUtils.isEmpty(type)) {
            return url;
        }
        int index_suffix = url.lastIndexOf(".");
        int index_resolution = url.lastIndexOf("/");
        if (index_suffix < 0 || index_resolution < 0 || TextUtils.isEmpty(url)) {
            return url;
        }
        String suffixStr = url.substring(index_suffix);
        String upperCaseSuffixStr = suffixStr.toUpperCase();
        if (JPG_STR.equals(upperCaseSuffixStr) ||
                PNG_STR.equals(upperCaseSuffixStr) ||
                JPEG_STR.equals(upperCaseSuffixStr)) {
            return StringUtil.join(url.substring(0, index_resolution), type, suffixStr);
        } else {
            return url;
        }
    }

    /**
     * 拼接URL参数，用于GET请求
     *
     * @param builder 参数对象
     * @param key     参数名
     * @param value   参数值
     */
    public static void appendValue(StringBuilder builder, String key, String value) {
        if (builder == null) {
            return;
        }
        if (TextUtils.isEmpty(key) || TextUtils.isEmpty(value)) {
            return;
        }
        key = Uri.encode(key, "UTF-8");
        value = Uri.encode(value, "UTF-8");
        builder.append(key).append("=").append(value).append("&");
    }

    /**
     * 生成url加密sign数组
     *
     * @param params
     * @return
     */
    public static String madeUrlSign(String[] params, String accessKey) {
        Arrays.sort(params);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(accessKey);
        int index = 0;
        String preValue = null;
        for (String value : params) {
            if (TextUtils.isEmpty(value) || value.equals(preValue)) {
                continue;
            }
            preValue = value;
            stringBuilder.append(index++);
            stringBuilder.append(value);
        }
        stringBuilder.append(accessKey);
        return stringBuilder.toString();
    }
}
