package com.kaolafm.ad.report;

import android.database.sqlite.SQLiteDatabase;

import com.kaolafm.ad.report.db.bean.EventData;
import com.kaolafm.ad.report.db.greendao.DaoMaster;
import com.kaolafm.ad.report.db.greendao.DaoSession;
import com.kaolafm.ad.report.db.greendao.EventDataDao;

import org.greenrobot.greendao.query.QueryBuilder;

import java.util.List;

public class AdReportDBManager {

    private static final String DB_NAME = "kradioADReport.db";

    private DaoSession mDaoSession;

    private AdReportDBManager(){

    }

    public static class KRADIO_AD_REPORT_DB_MANAGER{
        private static final AdReportDBManager INSTANCE = new AdReportDBManager();
    }

    public static AdReportDBManager getInstance(){
        return KRADIO_AD_REPORT_DB_MANAGER.INSTANCE;
    }

    public void init(){
        DaoMaster.DevOpenHelper helper = new DaoMaster.DevOpenHelper(AdReportManager.getInstance().getContext(),DB_NAME);
        SQLiteDatabase db = helper.getWritableDatabase();
        DaoMaster daoMaster = new DaoMaster(db);
        mDaoSession = daoMaster.newSession();
    }

    public List<EventData> queryNotReported(){
        if (checkDaoSessionNull()) {
            return null;
        }
        QueryBuilder qb = mDaoSession.getEventDataDao().queryBuilder();
        return qb.where(EventDataDao.Properties.Status.eq(AdConstants.NOT_REPORTED)).list();
    }

    public void insert(EventData eventData){
        if (checkDaoSessionNull()) {
            return ;
        }
        mDaoSession.getEventDataDao().insert(eventData);
    }

    public void insertOrReplace(EventData eventData){
        if (checkDaoSessionNull()) {
            return ;
        }
        mDaoSession.getEventDataDao().insertOrReplace(eventData);
    }

    public void update(List<EventData> eventDatas){
        if (checkDaoSessionNull()) {
            return ;
        }
        mDaoSession.getEventDataDao().updateInTx(eventDatas);
    }

    public void delete(List<Long> ids){
        if (checkDaoSessionNull()) {
            return ;
        }
        mDaoSession.getEventDataDao().deleteByKeyInTx(ids);
    }

    public void delete(long id){
        if (checkDaoSessionNull()) {
            return ;
        }
        mDaoSession.getEventDataDao().deleteByKey(id);
    }

    private boolean checkDaoSessionNull(){
        return mDaoSession == null;
    }
}
