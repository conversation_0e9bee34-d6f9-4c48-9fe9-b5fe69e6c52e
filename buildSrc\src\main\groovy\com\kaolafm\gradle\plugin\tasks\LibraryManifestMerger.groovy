package com.kaolafm.gradle.plugin.tasks

import com.android.build.gradle.internal.LoggerWrapper
import com.android.build.gradle.tasks.InvokeManifestMerger
import com.android.manifmerger.ManifestMerger2
import com.android.manifmerger.ManifestProvider
import com.android.manifmerger.MergingReport
import com.android.utils.ILogger
import org.apache.tools.ant.BuildException
import org.gradle.api.tasks.TaskAction
import org.gradle.workers.WorkerExecutor

/**
 * ManifestMerger for Library
 * <AUTHOR> on 2019/7/8.
 */
 class LibraryManifestMerger extends InvokeManifestMerger {

    private String mGradlePluginVersion

    private String mGradleVersion

     void setGradlePluginVersion(String gradlePluginVersion) {
        mGradlePluginVersion = gradlePluginVersion
    }

     void setGradleVersion(String gradleVersion) {
        mGradleVersion = gradleVersion
    }

    protected void doTaskAction() {
        try {
            doFullTaskAction()
        } catch (Exception e) {
            e.printStackTrace()
            System.out.println("Gradle Plugin Version:" + mGradlePluginVersion)
            System.out.println("Gradle Version:" + mGradleVersion)
            System.out.println("If you see this error message, please submit issue to " +
                    "https://github.com/kezong/fat-aar-android/issues with Gradle version. Thank you.")
        }
    }

    @TaskAction
    protected void doFullTaskAction() throws ManifestMerger2.MergeFailureException, IOException {
        ILogger iLogger = new LoggerWrapper(getLogger())
        ManifestMerger2.Invoker<?> mergerInvoker = ManifestMerger2.
                newMerger(getMainManifestFile(), iLogger, ManifestMerger2.MergeType.LIBRARY)
        List<File> secondaryManifestFiles = getSecondaryManifestFiles()
        List<ManifestProvider> manifestProviders = new ArrayList<>()
        if (secondaryManifestFiles != null) {
            for (final File file : secondaryManifestFiles) {
                manifestProviders.add(new ManifestProvider() {
                    @Override
                     File getManifest() {
                        return file.getAbsoluteFile()
                    }

                    @Override
                     String getName() {
                        return file.getName()
                    }
                })
            }
        }
        mergerInvoker.addManifestProviders(manifestProviders)
        MergingReport mergingReport = mergerInvoker.merge()
        if (mergingReport.getResult().isError()) {
            getLogger().error(mergingReport.getReportString())
            mergingReport.log(iLogger)
            throw new BuildException(mergingReport.getReportString())
        }

        // fix utf-8 problem in windows
        BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(
                new FileOutputStream(getOutputFile()), "UTF-8")
        )
        writer.append(mergingReport
                .getMergedDocument(MergingReport.MergedManifestKind.MERGED))
        writer.flush()
        writer.close()
    }

     WorkerExecutor getWorkerExecutor() {
        return null
    }
}
