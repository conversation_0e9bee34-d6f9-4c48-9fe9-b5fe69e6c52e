package com.kaolafm.opensdk.api.yunxin.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 云信自定义消息-直播状态
 * <AUTHOR>
 * @date 2023-02-16
 */
public class LiveStatusMsg implements Parcelable {
    private Integer liveStatus; // 1：开播；2：下播

    public LiveStatusMsg() {
    }

    public LiveStatusMsg(Integer liveStatus) {
        this.liveStatus = liveStatus;
    }

    protected LiveStatusMsg(Parcel in) {
        if (in.readByte() == 0) {
            liveStatus = null;
        } else {
            liveStatus = in.readInt();
        }
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        if (liveStatus == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(liveStatus);
        }
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<LiveStatusMsg> CREATOR = new Creator<LiveStatusMsg>() {
        @Override
        public LiveStatusMsg createFromParcel(Parcel in) {
            return new LiveStatusMsg(in);
        }

        @Override
        public LiveStatusMsg[] newArray(int size) {
            return new LiveStatusMsg[size];
        }
    };

    public Integer getLiveStatus() {
        return liveStatus;
    }

    public void setLiveStatus(Integer liveStatus) {
        this.liveStatus = liveStatus;
    }
}
