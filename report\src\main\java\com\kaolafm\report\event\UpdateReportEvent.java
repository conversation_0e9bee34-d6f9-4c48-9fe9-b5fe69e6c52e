package com.kaolafm.report.event;

import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> on 2019/1/21.
 * 升级事件
 */

public class UpdateReportEvent extends BaseReportEventBean {
    /**
     * 普通升级
     */
    public static final String TYPE_UPDATE_BY_SELF = "1";
    /**
     * 推荐升级
     */
    public static final String TYPE_UPDATE_RECOMMEND  = "3";
    /**
     * 静默升级
     */
    public static final String TYPE_UPDATE_BACKGROUND = "4";
    /**
     * 强制升级
     */
    public static final String TYPE_UPDATE_FORCE = "5";
    /**
     * 升级前版本号
     */
    private String remarks1;
    /**
     * 升级后版本号
     */
    private String remarks2;
    /**
     * 升级类型
     * 1.升级：应用商店升级；车厂OTA升级；用户自主安装APK导致的升级（当前可能没有）；
     * 3.推荐升级：用户前端点击升级按钮 or 听伴推送指令推荐升级；
     * 4.静默升级：听伴后台发生指令，客户端进行升级（用户无感知）；
     * 5.强制升级：听伴后台发生指令，客户端进行升级（不完成升级不能使用）
     */
    private String type = TYPE_UPDATE_BY_SELF;

    public UpdateReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_UPDATE);
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRemarks1() {
        return remarks1;
    }

    public void setRemarks1(String remarks1) {
        this.remarks1 = remarks1;
    }

    public String getRemarks2() {
        return remarks2;
    }

    public void setRemarks2(String remarks2) {
        this.remarks2 = remarks2;
    }
}
