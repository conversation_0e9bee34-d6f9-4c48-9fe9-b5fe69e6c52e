package com.kaolafm.opensdk.api.topic.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 话题详情
 *
 * <AUTHOR> @date 2019-03-18
 */
public class TopicDetail implements Parcelable {

    /**
     * 话题封面头像
     */
    @SerializedName("cover")
    private String cover;
    /**
     * 参与用户数量
     */
    @SerializedName("userCount")
    private int userCount;
    /**
     * 阅读量
     */
    @SerializedName("readCount")
    private int readCount;
    /**
     * 话题logo
     */
    @SerializedName("logo")
    private String logo;
    /**
     * 话题标题
     */
    @SerializedName("title")
    private String title;
    /**
     * 话题标题
     */
    @SerializedName("des")
    private String des;
    /**
     * 相关内容
     */
    @SerializedName("content")
    private List<TopicRelatedContent> relatedContent;
    /**
     * 帖子数量
     */
    @SerializedName("postsCount")
    private int postsCount;
    /**
     * 最新帖子发布时间
     */
    @SerializedName("updateTime")
    private long updateTime;
    /**
     * 语音播报地址
     */
    @SerializedName("videoUrl")
    private String voiceUrl;

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public int getUserCount() {
        return userCount;
    }

    public void setUserCount(int userCount) {
        this.userCount = userCount;
    }

    public int getReadCount() {
        return readCount;
    }

    public void setReadCount(int readCount) {
        this.readCount = readCount;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public List<TopicRelatedContent> getRelatedContent() {
        return relatedContent;
    }

    public void setRelatedContent(List<TopicRelatedContent> relatedContent) {
        this.relatedContent = relatedContent;
    }

    public int getPostsCount() {
        return postsCount;
    }

    public void setPostsCount(int postsCount) {
        this.postsCount = postsCount;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }

    public String getVoiceUrl() {
        return voiceUrl;
    }

    public void setVoiceUrl(String voiceUrl) {
        this.voiceUrl = voiceUrl;
    }

    protected TopicDetail(Parcel in) {
        cover = in.readString();
        userCount = in.readInt();
        readCount = in.readInt();
        logo = in.readString();
        title = in.readString();
        des = in.readString();
        in.readTypedList(relatedContent,TopicRelatedContent.CREATOR);
        postsCount = in.readInt();
        updateTime = in.readLong();
        voiceUrl = in.readString();
    }

    public static final Creator<TopicDetail> CREATOR = new Creator<TopicDetail>() {
        @Override
        public TopicDetail createFromParcel(Parcel in) {
            return new TopicDetail(in);
        }

        @Override
        public TopicDetail[] newArray(int size) {
            return new TopicDetail[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(cover);
        dest.writeInt(userCount);
        dest.writeInt(readCount);
        dest.writeString(logo);
        dest.writeString(title);
        dest.writeString(des);
        dest.writeTypedList(relatedContent);
        dest.writeInt(postsCount);
        dest.writeLong(updateTime);
        dest.writeString(voiceUrl);
    }

}
