package com.kaolafm.opensdk.player.logic.model.item;

import android.os.Parcel;

import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

/**
 * 无效的PlayItem
 */
public class InvalidPlayItem extends PlayItem {

    private int type          = PlayerConstants.RESOURCES_TYPE_INVALID;
    private String radioId    = "0";
    private String albumId    = "0";
    private String title      = "";
    private String picUrl     = "";
    private String host       = "";
    private String albumTitle = "";

    public InvalidPlayItem() {

    }

    @Override
    public String getRadioId() {
        return radioId;
    }

    @Override
    public String getAlbumId() {
        return albumId;
    }

    @Override
    public String getTitle() {
        return title;
    }

    @Override
    public String getPicUrl() {
        return picUrl;
    }

    @Override
    public String getHost() {
        return host;
    }

    @Override
    public String getAlbumTitle() {
        return albumTitle;
    }

    @Override
    public int getType() {
        return type;
    }

    private InvalidPlayItem(Parcel parcel) {

    }

    public static final Creator<InvalidPlayItem> CREATOR = new Creator<InvalidPlayItem>() {

        @Override
        public InvalidPlayItem createFromParcel(Parcel source) {
            return new InvalidPlayItem(source);
        }

        @Override
        public InvalidPlayItem[] newArray(int size) {
            return new InvalidPlayItem[size];
        }
    };
}
