package com.kaolafm.opensdk.api.music.qq.model;

import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * 音乐分类子标签
 * <AUTHOR>
 * @date 2018/4/23
 */

public class SubcategoryLabel {

    /**
     * category_desc : 念念不忘的经典，总有回响
     * category_id : 59
     * category_name : 经典
     * category_pic : http://y.gtimg.cn/music/common/upload/t_tag_group_set/35365.jpg
     * category_sharepic : http://y.gtimg.cn/music/common/upload/t_tag_group_set/20140812185411.jpg
     * category_show_type : 1
     * sub_category_list : [{"category_desc":"","category_id":3230,"category_imgurl":"","category_is_hot":0,"category_is_new":0,"category_is_parent":0,"category_name":"经典国语","category_share_pic":"http://y.gtimg.cn/music/common/upload/t_tag_group_set/80361.jpg","category_show_detail":0,"category_show_type":1,"group_id":2},{"category_desc":"","category_id":3231,"category_imgurl":"","category_is_hot":0,"category_is_new":0,"category_is_parent":0,"category_name":"经典粤语","category_share_pic":"http://y.gtimg.cn/music/common/upload/t_tag_group_set/80400.jpg","category_show_detail":0,"category_show_type":1,"group_id":2},{"category_desc":"","category_id":3232,"category_imgurl":"","category_is_hot":0,"category_is_new":0,"category_is_parent":0,"category_name":"经典英语","category_share_pic":"http://y.gtimg.cn/music/common/upload/t_tag_group_set/80366.jpg","category_show_detail":0,"category_show_type":1,"group_id":2}]
     */

    @SerializedName("category_desc")
    private String categoryDesc;

    @SerializedName("category_id")
    private int categoryId;

    @SerializedName("category_name")
    private String categoryName;

    @SerializedName("category_pic")
    private String categoryPic;

    @SerializedName("category_sharepic")
    private String categorySharePic;

    @SerializedName("category_show_type")
    private int categoryShowType;

    @SerializedName("sub_category_list")
    private List<CategoryDetail> subCategoryList;

    public String getCategoryDesc() {
        return categoryDesc;
    }

    public int getCategoryId() {
        return categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public String getCategoryPic() {
        return categoryPic;
    }

    public String getCategorySharePic() {
        return categorySharePic;
    }

    public int getCategoryShowType() {
        return categoryShowType;
    }

    public List<CategoryDetail> getSubCategoryList() {
        return subCategoryList;
    }

    public void setCategoryDesc(String categoryDesc) {
        this.categoryDesc = categoryDesc;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public void setCategoryPic(String categoryPic) {
        this.categoryPic = categoryPic;
    }

    public void setCategorySharePic(String categorySharePic) {
        this.categorySharePic = categorySharePic;
    }

    public void setCategoryShowType(int categoryShowType) {
        this.categoryShowType = categoryShowType;
    }

    public void setSubCategoryList(List<CategoryDetail> subCategoryList) {
        this.subCategoryList = subCategoryList;
    }
}
