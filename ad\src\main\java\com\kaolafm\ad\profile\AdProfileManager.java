package com.kaolafm.ad.profile;

import android.os.Bundle;
import android.text.TextUtils;

import com.kaolafm.ad.AdvertOptions;
import com.kaolafm.ad.BuildConfig;
import com.kaolafm.base.utils.DeviceUtil;
import com.kaolafm.opensdk.account.profile.AbstractProfileManager;
import com.kaolafm.opensdk.di.scope.AppScope;

import javax.inject.Inject;

/**
 * 广告SDK的属性管理类。
 * <AUTHOR>
 * @date 2020-01-14
 */
@AppScope
public class AdProfileManager extends AbstractProfileManager<AdvertisingProfile, AdvertOptions> {

    private static final String APP_ID_PROPERTY = "com.kaolafm.ad.AppId";

    @Inject
    public AdProfileManager() {
    }

    @Override
    public void loadProfile() {
        loadProfileFromManifest();
        setProfile();
    }

    @Override
    protected void loadProfileFromManifest() {
        if (!TextUtils.isEmpty(mProfile.getAppId())) {
            return;
        }
        super.loadProfileFromManifest();
    }

    @Override
    protected void setupFromManifest(Bundle metaData) {
        String appId = metaData.getString(APP_ID_PROPERTY);
        if (TextUtils.isEmpty(appId)) {
            mProfile.setAppId("7aaedb73ec854a1b907d3cce6b0f109d");
        } else {
            mProfile.setAppId(appId);
        }
    }

    @Override
    protected void setProfile() {
        super.setProfile();
        mProfile.setIp(DeviceUtil.getClientIP());
        mProfile.setResolution(DeviceUtil.getScreenResolution(mApplication));
        setBrand(options.brand());
        mProfile.setDeviceType(options.deviceType());
        mProfile.setVersionName(BuildConfig.VERSION_NAME);
    }

    public void setBrand(String brand) {
        mProfile.setBrand(brand);
    }
}
