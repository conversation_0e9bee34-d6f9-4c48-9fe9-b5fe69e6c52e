package com.kaolafm.opensdk.api.operation;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.operation.model.category.Category;
import com.kaolafm.opensdk.api.operation.model.category.CategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.LeafCategory;
import com.kaolafm.opensdk.api.operation.model.category.MemberNum;
import com.kaolafm.opensdk.api.operation.model.column.Column;
import com.kaolafm.opensdk.api.operation.model.column.ColumnGrp;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;
import io.reactivex.Single;
import java.util.List;
import java.util.Map;
import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.POST;
import retrofit2.http.Path;
import retrofit2.http.Query;

/**
 * 运营网络请求接口
 *
 * <AUTHOR> Yan
 * @date 2018/7/30
 */
interface OperationService {


    /**
     * 递归获取某一内容类型的整颗分类树
     *
     * @param contentType 内容类型（0:综合, 1:专辑, 2:广播,4:AI电台）
     * @param zone        区域信息
     * @param extInfo     拓展信息
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_CATEGORY_TREE)
    Single<BaseResult<List<Category>>> getCategoryTree(@Query("contentType") int contentType,
            @Query("zone") String zone, @Query("extInfo") Map<String, String> extInfo);

    /**
     * 获取某一内容类型的根分类列表
     *
     * @param contentType 类容类型（0:综合, 1:专辑, 2:广播,4:AI电台）
     * @param zone        区域信息
     * @param extInfo     拓展信息
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_CATEGORY_ROOT)
    Single<BaseResult<List<Category>>> getCategoryRoot(@Query("contentType") int contentType,
            @Query("zone") String zone, @Query("extInfo") Map<String, String> extInfo);

    /**
     * 获取某一内容类型的根分类列表
     *
     * @param parentCode 分类父编码
     * @param extInfo    拓展信息
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_SUBCATEGORY_LIST)
    Single<BaseResult<List<LeafCategory>>> getSubcategoryList(@Query("parentCode") String parentCode,
            @Query("extInfo") Map<String, String> extInfo);

    /**
     * 获取某一内容类型的根分类列表
     *
     * @param parentCode 分类父编码
     * @param extInfo    拓展信息
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_SUBCATEGORY_LIST)
    Single<BaseResult<List<Category>>> getSubcategoryListForMoreLevels(@Query("parentCode") String parentCode,
            @Query("extInfo") Map<String, String> extInfo);

    /**
     * 根据分类编码获取分类成员数量
     *
     * @param code 分类编码
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_CATEGORY_MEMBER_NUM)
    Single<BaseResult<MemberNum>> getCategoryMemberNum(@Query("code") String code,
            @Query("extInfo") Map<String, String> extInfo);

    /**
     * 根据分类编码分页获取分类成员列表
     *
     * @param code     分类编码
     * @param pageNum  页码
     * @param pageSize 每页大小
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_CATEGORY_MEMBER_LIST_NEW)
    Single<BaseResult<BasePageResult<List<CategoryMember>>>> getCategoryMemberList(@Query("code") String code,
            @Query("pageNum") int pageNum, @Query("pageSize") int pageSize);

    /**
     * 递归获取整个栏目树
     *
     * @param withMembers 是否要成员(0表示不要成员)
     * @param extInfo     拓展信息
     * @param zone        区域信息
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_COLUMN_TREE)
    Single<BaseResult<List<ColumnGrp>>> getColumnTree(@Query("withMembers") int withMembers,@Query("zone") String zone,
            @Query("extInfo") Map<String, String> extInfo);

    /**
     * 根据父栏目编码获取下级栏目列表
     *
     * @param parentCode 栏目父编码
     * @param extInfo    拓展信息
     * @param zone       区域信息
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_SUBCOLUMN_LIST)
    Single<BaseResult<List<Column>>> getSubcolumnList(@Query("parentCode") String parentCode, @Query("zone") String zone,
            @Query("extInfo") Map<String, String> extInfo);

    /**
     * 根据父栏目编码获取下级栏目列表
     *
     * @param code    栏目编码
     * @param extInfo 拓展信息
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_COLUMN_MEMBER_LIST)
    Single<BaseResult<List<ColumnMember>>> getColumnMemberList(@Query("code") String code,
            @Query("extInfo") Map<String, String> extInfo);

    //---------------------------------------以下接口已过时-----------------------------------

    /**
     * 获取栏目列表
     * @deprecated
     */
    @Deprecated
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @POST(KaolaApiConstant.GET_COLUMN_LIST)
    Single<BaseResult<List<ColumnGrp>>> getColumnList(@Body RequestBody requestBody);

    /**
     * 获取栏目列表 (同步)
     * @deprecated
     */
    @Deprecated
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @POST(KaolaApiConstant.GET_COLUMN_LIST)
    Call<BaseResult<List<ColumnGrp>>> getColumnListSync(@Body RequestBody requestBody);

    /**
     * 获取分类列表
     * @deprecated
     */
    @Deprecated
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @POST(KaolaApiConstant.GET_CATEGORY_LIST)
    Single<BaseResult<List<Category>>> getCategoryList(@Body RequestBody requestBody);

    /**
     * 根据分类id获取分类信息
     * @deprecated
     */
    @Deprecated
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_CATEGORY_INFO)
    Single<BaseResult<Category>> getCategoryInfo(@Path("id") String id);

    /**
     * 获取分类成员列表
     * @deprecated
     */
    @Deprecated
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_CATEGORY_MEMBER_LIST)
    Single<BaseResult<BasePageResult<List<CategoryMember>>>> getCategoryMemberList(@Query("code") String code);

    /**
     *  分页 获取分类成员列表
     */
    /*@Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_CATEGORY_MEMBER_LIST)
    Single<BaseResult<BasePageResult<List<CategoryMember>>>> getCategoryMemberList(@Query("code") String code,
            @Query("pageNum") int pageNum, @Query("pageSize") int pageSize);
*/
}
