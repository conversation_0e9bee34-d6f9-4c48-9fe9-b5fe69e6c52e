package com.kaolafm.opensdk.api.yunxin.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * <AUTHOR>
 * @date 2023-02-21
 */
public class CustomMsg implements Parcelable {

    private Integer type;
    private Object msg;

    public CustomMsg() {
    }

    protected CustomMsg(Parcel in) {
        if (in.readByte() == 0) {
            type = null;
        } else {
            type = in.readInt();
        }
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        if (type == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(type);
        }
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<CustomMsg> CREATOR = new Creator<CustomMsg>() {
        @Override
        public CustomMsg createFromParcel(Parcel in) {
            return new CustomMsg(in);
        }

        @Override
        public CustomMsg[] newArray(int size) {
            return new CustomMsg[size];
        }
    };

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Object getMsg() {
        return msg;
    }

    public void setMsg(Object msg) {
        this.msg = msg;
    }
}
