package com.kaolafm.opensdk.player.logic.model.item;

import android.os.Parcel;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.media.model.AudioFileInfo;
import com.kaolafm.opensdk.player.logic.model.item.model.BroadcastInfoData;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.TimeInfoData;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import java.util.ArrayList;
import java.util.List;

/**
 * 在线广播-播放对象
 */
public class BroadcastPlayItem extends PlayItem {

    /**
     * 信息相关数据
     */
    private BroadcastInfoData mInfoData;

    /**
     * 播放时间相关数据
     */
    private TimeInfoData mTimeInfoData;

    /**
     * 广播频段
     */
    private String frequencyChannel;

    /**
     * 用来区分广播类型（音乐，交通，新闻等）
     */
    private int broadcastSort;

    /**
     * 在线广播定制状态
     */
    private int status;

    /**
     * 电台归属：
     * 国家台：1，地方台：2
     */
    private int classifyId;

    /**
     * 直播分音质播放地址
     */
    private List<AudioFileInfo> playInfoList;

    /**
     * 回放分音质播放地址
     */
    private List<AudioFileInfo> backPlayInfoList;

    /**
     * 广播回放的状态
     * 0-节目单隐藏
     * 1-节目单显示，回放能播
     * 2-节目单显示，回放不能播
     * 如果为1，节目开播之后没有回放地址，为转码中状态
     */
    private int programEnable;

    public BroadcastPlayItem() {
        mInfoData = new BroadcastInfoData();
        mTimeInfoData = new TimeInfoData();
        playInfoList = new ArrayList<>();
        backPlayInfoList = new ArrayList<>();
    }

    @Override
    public String getRadioId() {
        return String.valueOf(mInfoData.getAlbumId());
    }


    @Override
    public String getAlbumId() {
        return String.valueOf(mInfoData.getAlbumId());
    }

    @Override
    public String getTitle() {
        String title = mInfoData.getTitle();
        if (StringUtil.isEmpty(title)) {
            title = mInfoData.getAlbumName();
        }
        return title;
    }

    @Override
    public String getPicUrl() {
        String picUrl = mInfoData.getAudioPic();
        if (StringUtil.isEmpty(picUrl)) {
            picUrl = mInfoData.getAlbumPic();
        }
        return picUrl;
    }

    @Override
    public String getHost() {
        return mInfoData.getHosts();
    }

    @Override
    public String getUpdateTime() {
        return mInfoData.getUpdateTime();
    }

    @Override
    public String getAlbumTitle() {
        return mInfoData.getAlbumName();
    }

    @Override
    public String getBeginTime() {
        return mTimeInfoData.getBeginTime();
    }

    @Override
    public String getEndTime() {
        return mTimeInfoData.getEndTime();
    }

    @Override
    public long getStartTime() {
        return mTimeInfoData.getStartTime();
    }

    @Override
    public int getType() {
        return PlayerConstants.RESOURCES_TYPE_BROADCAST;
    }

    public String getFrequencyChannel() {
        return frequencyChannel;
    }

    public void setFrequencyChannel(String frequencyChannel) {
        this.frequencyChannel = frequencyChannel;
    }

    public int getBroadcastSort() {
        return broadcastSort;
    }

    public void setBroadcastSort(int broadcastSort) {
        this.broadcastSort = broadcastSort;
    }

    @Override
    public int getStatus() {
        return status;
    }

    public int getProgramEnable() {
        return programEnable;
    }

    public void setProgramEnable(int programEnable) {
        this.programEnable = programEnable;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public boolean isLivingUrl() {
        return status == PlayerConstants.BROADCAST_STATUS_LIVING;
    }

    @Override
    public boolean isLiving() {
        return isLivingUrl();
    }

    public BroadcastInfoData getInfoData() {
        return mInfoData;
    }

    public void setInfoData(BroadcastInfoData infoData) {
        this.mInfoData = infoData;
    }

    public TimeInfoData getTimeInfoData() {
        return mTimeInfoData;
    }

    public void setTimeInfoData(TimeInfoData timeInfoData) {
        this.mTimeInfoData = timeInfoData;
    }

    public List<AudioFileInfo> getPlayInfoList() {
        return playInfoList;
    }

    public void setPlayInfoList(List<AudioFileInfo> playInfoList) {
        this.playInfoList = playInfoList;
    }

    public List<AudioFileInfo> getBackPlayInfoList() {
        return backPlayInfoList;
    }

    public void setBackPlayInfoList(List<AudioFileInfo> backPlayInfoList) {
        this.backPlayInfoList = backPlayInfoList;
    }

    public int getClassifyId() {
        return classifyId;
    }

    public void setClassifyId(int classifyId) {
        this.classifyId = classifyId;
    }

    private BroadcastPlayItem(Parcel parcel) {

    }

    public static final Creator<BroadcastPlayItem> CREATOR = new Creator<BroadcastPlayItem>() {

        @Override
        public BroadcastPlayItem createFromParcel(Parcel source) {
            return new BroadcastPlayItem(source);
        }

        @Override
        public BroadcastPlayItem[] newArray(int size) {
            return new BroadcastPlayItem[size];
        }
    };

    @Override
    public long getFinishTime() {
        return mTimeInfoData.getFinishTime();
    }
}
