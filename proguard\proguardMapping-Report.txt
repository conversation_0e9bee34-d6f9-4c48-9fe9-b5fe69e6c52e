# compiler: R8
# compiler_version: 1.5.69
# pg_map_id: 9a7574c
com.kaolafm.report.BuildConfig -> com.kaolafm.report.a:
    java.lang.String VERSION_NAME -> g
    int VERSION_CODE -> f
    boolean DEBUG -> a
    java.lang.String APPLICATION_ID -> c
    java.lang.String LIBRARY_PACKAGE_NAME -> b
    java.lang.String FLAVOR -> e
    java.lang.String BUILD_TYPE -> d
    1:1:void <init>():6:6 -> <init>
com.kaolafm.report.ReportHelper -> com.kaolafm.report.b:
    com.kaolafm.report.model.KaolaActivateData mKaolaActivateData -> g
    com.kaolafm.report.ReportHelper reportHelper -> h
    boolean isUseBySDK -> a
    android.content.Context mContext -> e
    com.kaolafm.report.listener.IReportInitListener mIReportInitListener -> b
    boolean isInitSuccess -> c
    java.lang.String mCarType -> d
    com.kaolafm.report.util.PlayReportManager playReportManager -> f
    1:1:void <init>():57:57 -> <init>
    2:11:void <init>():37:46 -> <init>
    1:17:void init(android.content.Context,com.kaolafm.report.model.ReportParameter):79:95 -> a
    18:21:void setCarParameter(com.kaolafm.report.model.ReportCarParameter):117:120 -> a
    22:45:void setCarParameter(com.kaolafm.report.model.KaolaActivateData):124:147 -> a
    46:49:void setPlayPosition(long,long):156:159 -> a
    50:50:android.content.Context getContext():192:192 -> a
    51:51:void addEvent(com.kaolafm.report.event.BaseReportEventBean):201:201 -> a
    52:79:void addEvent(com.kaolafm.report.event.BaseReportEventBean,boolean):211:238 -> a
    80:83:void addStartListenReport(com.kaolafm.report.model.PlayReportParameter):248:251 -> a
    84:87:void addEndListenReport(java.lang.String,boolean):261:264 -> a
    88:91:void addAppStart(java.lang.String):269:272 -> a
    92:92:void setRealVersion(java.lang.String,java.lang.String):315:315 -> a
    93:93:void setIReportInitListener(com.kaolafm.report.listener.IReportInitListener):320:320 -> a
    94:97:void setReportEventIntercept(com.kaolafm.report.listener.IReportEventIntercept):324:327 -> a
    1:1:void initBySdk():75:75 -> b
    2:5:void initUid(java.lang.String):109:112 -> b
    6:9:void setSearchAudioPlayCallBack(java.lang.String,java.lang.String):163:166 -> b
    1:4:void initUpdateEvent(java.lang.String):303:306 -> c
    5:13:void release():331:339 -> c
    1:8:com.kaolafm.report.ReportHelper getInstance():61:68 -> d
    9:13:void setCarType(java.lang.String):276:280 -> d
    1:5:void setLandOrPortrait(java.lang.String):290:294 -> e
    1:4:void setLat(java.lang.String):171:174 -> f
    1:4:void setLon(java.lang.String):178:181 -> g
    1:4:void setPage(java.lang.String):185:188 -> h
com.kaolafm.report.api.carinfo.CarInfoRequest -> com.kaolafm.report.c.a.a:
    com.kaolafm.report.api.carinfo.CarInfoService reportInfoService -> a
    1:2:void <init>():15:16 -> <init>
    1:1:void getReportInfo(com.kaolafm.opensdk.http.core.HttpCallback):21:21 -> a
com.kaolafm.report.api.carinfo.CarInfoService -> com.kaolafm.report.c.a.b:
    io.reactivex.Single getReportInfo() -> a
com.kaolafm.report.api.carinfo.model.CarInfoData -> com.kaolafm.report.api.carinfo.model.CarInfoData:
    1:1:void <init>():8:8 -> <init>
    1:1:com.kaolafm.report.api.carinfo.model.CarInfoData$CarConfigBean getCarConfig():19:19 -> getCarConfig
    1:1:com.kaolafm.report.api.carinfo.model.CarInfoData$CarInfoBean getCarInfo():27:27 -> getCarInfo
    1:1:void setCarConfig(com.kaolafm.report.api.carinfo.model.CarInfoData$CarConfigBean):23:23 -> setCarConfig
    1:1:void setCarInfo(com.kaolafm.report.api.carinfo.model.CarInfoData$CarInfoBean):31:31 -> setCarInfo
com.kaolafm.report.api.carinfo.model.CarInfoData$CarConfigBean -> com.kaolafm.report.api.carinfo.model.CarInfoData$CarConfigBean:
    1:1:void <init>():34:34 -> <init>
    1:1:int getReportInterval():42:42 -> getReportInterval
    1:1:void setReportInterval(int):46:46 -> setReportInterval
com.kaolafm.report.api.carinfo.model.CarInfoData$CarInfoBean -> com.kaolafm.report.api.carinfo.model.CarInfoData$CarInfoBean:
    1:1:void <init>():50:50 -> <init>
    1:1:java.lang.String getAppIdType():116:116 -> getAppIdType
    1:1:java.lang.String getCarBrand():132:132 -> getCarBrand
    1:1:java.lang.String getCarType():140:140 -> getCarType
    1:1:java.lang.String getDeveloper():148:148 -> getDeveloper
    1:1:java.lang.String getFirstAppId():92:92 -> getFirstAppId
    1:1:java.lang.String getFirstAppIdName():108:108 -> getFirstAppIdName
    1:1:java.lang.String getMarketType():100:100 -> getMarketType
    1:1:java.lang.String getOem():124:124 -> getOem
    1:1:void setAppIdType(java.lang.String):120:120 -> setAppIdType
    1:1:void setCarBrand(java.lang.String):136:136 -> setCarBrand
    1:1:void setCarType(java.lang.String):144:144 -> setCarType
    1:1:void setDeveloper(java.lang.String):152:152 -> setDeveloper
    1:1:void setFirstAppId(java.lang.String):96:96 -> setFirstAppId
    1:1:void setFirstAppIdName(java.lang.String):112:112 -> setFirstAppIdName
    1:1:void setMarketType(java.lang.String):104:104 -> setMarketType
    1:1:void setOem(java.lang.String):128:128 -> setOem
com.kaolafm.report.api.report.ReportShenCeService -> com.kaolafm.report.c.b.a:
    retrofit2.Call report(okhttp3.RequestBody) -> a
com.kaolafm.report.api.report.ReportShenCeRequest -> com.kaolafm.report.c.b.b:
    com.kaolafm.report.api.report.ReportShenCeService reportApiService -> a
    1:3:void <init>():19:21 -> <init>
    1:12:boolean postReport(java.lang.String):26:37 -> a
com.kaolafm.report.database.ConfigData -> com.kaolafm.report.d.a:
    java.lang.Long id -> a
    int type -> b
    java.lang.String json -> c
    1:4:void <init>(java.lang.Long,int,java.lang.String):21:24 -> <init>
    5:5:void <init>():28:28 -> <init>
    1:1:java.lang.Long getId():32:32 -> a
    2:2:void setId(java.lang.Long):36:36 -> a
    3:3:void setType(int):44:44 -> a
    4:4:void setJson(java.lang.String):52:52 -> a
    1:1:java.lang.String getJson():48:48 -> b
    1:1:int getType():40:40 -> c
com.kaolafm.report.database.ReportData -> com.kaolafm.report.d.b:
    java.lang.Long id -> a
    int type -> b
    java.lang.String sendStr -> c
    1:4:void <init>(java.lang.Long,int,java.lang.String):19:22 -> <init>
    5:5:void <init>():25:25 -> <init>
    1:1:java.lang.Long getId():28:28 -> a
    2:2:void setId(java.lang.Long):31:31 -> a
    3:3:void setType(int):37:37 -> a
    4:4:void setSendStr(java.lang.String):43:43 -> a
    1:1:java.lang.String getSendStr():40:40 -> b
    1:1:int getType():34:34 -> c
com.kaolafm.report.database.greendao.ConfigDataDao -> com.kaolafm.report.database.greendao.ConfigDataDao:
    1:1:void <init>(org.greenrobot.greendao.internal.DaoConfig):34:34 -> <init>
    2:2:void <init>(org.greenrobot.greendao.internal.DaoConfig,com.kaolafm.report.database.greendao.DaoSession):38:38 -> <init>
    1:1:void bindValues(android.database.sqlite.SQLiteStatement,java.lang.Object):18:18 -> bindValues
    2:2:void bindValues(org.greenrobot.greendao.database.DatabaseStatement,java.lang.Object):18:18 -> bindValues
    3:13:void bindValues(org.greenrobot.greendao.database.DatabaseStatement,com.kaolafm.report.database.ConfigData):58:68 -> bindValues
    14:24:void bindValues(android.database.sqlite.SQLiteStatement,com.kaolafm.report.database.ConfigData):74:84 -> bindValues
    1:1:void createTable(org.greenrobot.greendao.database.Database,boolean):44:44 -> createTable
    1:2:void dropTable(org.greenrobot.greendao.database.Database,boolean):52:53 -> dropTable
    1:1:java.lang.Object getKey(java.lang.Object):18:18 -> getKey
    2:2:java.lang.Long getKey(com.kaolafm.report.database.ConfigData):119:119 -> getKey
    1:1:boolean hasKey(java.lang.Object):18:18 -> hasKey
    2:2:boolean hasKey(com.kaolafm.report.database.ConfigData):127:127 -> hasKey
    1:1:java.lang.Object readEntity(android.database.Cursor,int):18:18 -> readEntity
    2:2:void readEntity(android.database.Cursor,java.lang.Object,int):18:18 -> readEntity
    3:6:com.kaolafm.report.database.ConfigData readEntity(android.database.Cursor,int):95:98 -> readEntity
    7:9:void readEntity(android.database.Cursor,com.kaolafm.report.database.ConfigData,int):105:107 -> readEntity
    1:1:java.lang.Object readKey(android.database.Cursor,int):18:18 -> readKey
    2:2:java.lang.Long readKey(android.database.Cursor,int):90:90 -> readKey
    1:1:java.lang.Object updateKeyAfterInsert(java.lang.Object,long):18:18 -> updateKeyAfterInsert
    2:3:java.lang.Long updateKeyAfterInsert(com.kaolafm.report.database.ConfigData,long):112:113 -> updateKeyAfterInsert
com.kaolafm.report.database.greendao.ConfigDataDao$Properties -> com.kaolafm.report.database.greendao.ConfigDataDao$Properties:
    1:3:void <clinit>():27:29 -> <clinit>
    1:1:void <init>():26:26 -> <init>
com.kaolafm.report.database.greendao.DaoMaster -> com.kaolafm.report.database.greendao.DaoMaster:
    1:1:void <init>(android.database.sqlite.SQLiteDatabase):45:45 -> <init>
    2:4:void <init>(org.greenrobot.greendao.database.Database):49:51 -> <init>
    1:2:void createAllTables(org.greenrobot.greendao.database.Database,boolean):24:25 -> createAllTables
    1:2:void dropAllTables(org.greenrobot.greendao.database.Database,boolean):30:31 -> dropAllTables
    1:3:com.kaolafm.report.database.greendao.DaoSession newDevSession(android.content.Context,java.lang.String):39:41 -> newDevSession
    1:1:org.greenrobot.greendao.AbstractDaoSession newSession():19:19 -> newSession
    2:2:org.greenrobot.greendao.AbstractDaoSession newSession(org.greenrobot.greendao.identityscope.IdentityScopeType):19:19 -> newSession
    3:3:com.kaolafm.report.database.greendao.DaoSession newSession():55:55 -> newSession
    4:4:com.kaolafm.report.database.greendao.DaoSession newSession(org.greenrobot.greendao.identityscope.IdentityScopeType):59:59 -> newSession
com.kaolafm.report.database.greendao.DaoMaster$DevOpenHelper -> com.kaolafm.report.database.greendao.DaoMaster$DevOpenHelper:
    1:1:void <init>(android.content.Context,java.lang.String):84:84 -> <init>
    2:2:void <init>(android.content.Context,java.lang.String,android.database.sqlite.SQLiteDatabase$CursorFactory):88:88 -> <init>
    1:3:void onUpgrade(org.greenrobot.greendao.database.Database,int,int):93:95 -> onUpgrade
com.kaolafm.report.database.greendao.DaoMaster$OpenHelper -> com.kaolafm.report.database.greendao.DaoMaster$OpenHelper:
    1:1:void <init>(android.content.Context,java.lang.String):67:67 -> <init>
    2:2:void <init>(android.content.Context,java.lang.String,android.database.sqlite.SQLiteDatabase$CursorFactory):71:71 -> <init>
    1:2:void onCreate(org.greenrobot.greendao.database.Database):76:77 -> onCreate
com.kaolafm.report.database.greendao.DaoSession -> com.kaolafm.report.database.greendao.DaoSession:
    1:13:void <init>(org.greenrobot.greendao.database.Database,org.greenrobot.greendao.identityscope.IdentityScopeType,java.util.Map):34:46 -> <init>
    1:2:void clear():50:51 -> clear
    1:1:com.kaolafm.report.database.greendao.ConfigDataDao getConfigDataDao():55:55 -> getConfigDataDao
    1:1:com.kaolafm.report.database.greendao.ReportDataDao getReportDataDao():59:59 -> getReportDataDao
com.kaolafm.report.database.greendao.ReportDataDao -> com.kaolafm.report.database.greendao.ReportDataDao:
    1:1:void <init>(org.greenrobot.greendao.internal.DaoConfig):34:34 -> <init>
    2:2:void <init>(org.greenrobot.greendao.internal.DaoConfig,com.kaolafm.report.database.greendao.DaoSession):38:38 -> <init>
    1:1:void bindValues(android.database.sqlite.SQLiteStatement,java.lang.Object):18:18 -> bindValues
    2:2:void bindValues(org.greenrobot.greendao.database.DatabaseStatement,java.lang.Object):18:18 -> bindValues
    3:13:void bindValues(org.greenrobot.greendao.database.DatabaseStatement,com.kaolafm.report.database.ReportData):58:68 -> bindValues
    14:24:void bindValues(android.database.sqlite.SQLiteStatement,com.kaolafm.report.database.ReportData):74:84 -> bindValues
    1:1:void createTable(org.greenrobot.greendao.database.Database,boolean):44:44 -> createTable
    1:2:void dropTable(org.greenrobot.greendao.database.Database,boolean):52:53 -> dropTable
    1:1:java.lang.Object getKey(java.lang.Object):18:18 -> getKey
    2:2:java.lang.Long getKey(com.kaolafm.report.database.ReportData):119:119 -> getKey
    1:1:boolean hasKey(java.lang.Object):18:18 -> hasKey
    2:2:boolean hasKey(com.kaolafm.report.database.ReportData):127:127 -> hasKey
    1:1:java.lang.Object readEntity(android.database.Cursor,int):18:18 -> readEntity
    2:2:void readEntity(android.database.Cursor,java.lang.Object,int):18:18 -> readEntity
    3:6:com.kaolafm.report.database.ReportData readEntity(android.database.Cursor,int):95:98 -> readEntity
    7:9:void readEntity(android.database.Cursor,com.kaolafm.report.database.ReportData,int):105:107 -> readEntity
    1:1:java.lang.Object readKey(android.database.Cursor,int):18:18 -> readKey
    2:2:java.lang.Long readKey(android.database.Cursor,int):90:90 -> readKey
    1:1:java.lang.Object updateKeyAfterInsert(java.lang.Object,long):18:18 -> updateKeyAfterInsert
    2:3:java.lang.Long updateKeyAfterInsert(com.kaolafm.report.database.ReportData,long):112:113 -> updateKeyAfterInsert
com.kaolafm.report.database.greendao.ReportDataDao$Properties -> com.kaolafm.report.database.greendao.ReportDataDao$Properties:
    1:3:void <clinit>():27:29 -> <clinit>
    1:1:void <init>():26:26 -> <init>
com.kaolafm.report.event.AudioSearchReportEvent -> com.kaolafm.report.event.AudioSearchReportEvent:
    1:2:void <init>():39:40 -> <init>
    1:1:java.lang.String getPlaytype():60:60 -> getPlaytype
    1:1:java.lang.String getRemarks1():68:68 -> getRemarks1
    1:1:java.lang.String getRemarks9():76:76 -> getRemarks9
    1:1:java.lang.String getResult():52:52 -> getResult
    1:1:java.lang.String getText():44:44 -> getText
    1:1:void setPlaytype(java.lang.String):64:64 -> setPlaytype
    1:1:void setRemarks1(java.lang.String):72:72 -> setRemarks1
    1:1:void setRemarks9(java.lang.String):80:80 -> setRemarks9
    1:1:void setResult(java.lang.String):56:56 -> setResult
    1:1:void setText(java.lang.String):48:48 -> setText
com.kaolafm.report.event.BaseReportEventBean -> com.kaolafm.report.event.BaseReportEventBean:
    1:1:void <init>():113:113 -> <init>
    2:53:void <init>():101:152 -> <init>
    1:1:java.lang.String getAction_id():285:285 -> getAction_id
    1:1:java.lang.String getApp_version():253:253 -> getApp_version
    1:1:java.lang.String getApp_version2():261:261 -> getApp_version2
    1:1:java.lang.String getAppid():156:156 -> getAppid
    1:1:java.lang.String getAppid_type():333:333 -> getAppid_type
    1:1:java.lang.String getCar_brand():349:349 -> getCar_brand
    1:1:java.lang.String getCar_id():317:317 -> getCar_id
    1:1:java.lang.String getCar_type():357:357 -> getCar_type
    1:1:java.lang.String getCarrier():309:309 -> getCarrier
    1:1:java.lang.String getChannel():421:421 -> getChannel
    1:1:java.lang.String getDeveloper():429:429 -> getDeveloper
    1:1:java.lang.String getEventcode():180:180 -> getEventcode
    1:1:java.lang.String getImsi():164:164 -> getImsi
    1:1:java.lang.String getLat():204:204 -> getLat
    1:1:java.lang.String getLib_version():269:269 -> getLib_version
    1:1:java.lang.String getLon():196:196 -> getLon
    1:1:java.lang.String getManufacturer():373:373 -> getManufacturer
    1:1:java.lang.String getMarket_type():325:325 -> getMarket_type
    1:1:java.lang.String getModel():381:381 -> getModel
    1:1:java.lang.String getNetwork():301:301 -> getNetwork
    1:1:java.lang.String getOem():341:341 -> getOem
    1:1:java.lang.String getOpenid():212:212 -> getOpenid
    1:1:java.lang.String getOs():228:228 -> getOs
    1:1:java.lang.String getOsversion():413:413 -> getOsversion
    1:1:java.lang.String getPage():220:220 -> getPage
    1:1:java.lang.String getPlayid():389:389 -> getPlayid
    1:1:java.lang.String getReport_timely():397:397 -> getReport_timely
    1:1:java.lang.String getScreen_direction():365:365 -> getScreen_direction
    1:1:java.lang.String getScreen_height():236:236 -> getScreen_height
    1:1:java.lang.String getScreen_width():244:244 -> getScreen_width
    1:1:java.lang.String getSessionid():277:277 -> getSessionid
    1:1:java.lang.String getTimestamp():188:188 -> getTimestamp
    1:1:java.lang.String getUdid():405:405 -> getUdid
    1:1:java.lang.String getUid():172:172 -> getUid
    1:1:java.lang.String getWifi():293:293 -> getWifi
    1:1:void setAction_id(java.lang.String):289:289 -> setAction_id
    1:1:void setApp_version(java.lang.String):257:257 -> setApp_version
    1:1:void setApp_version2(java.lang.String):265:265 -> setApp_version2
    1:1:void setAppid(java.lang.String):160:160 -> setAppid
    1:1:void setAppid_type(java.lang.String):337:337 -> setAppid_type
    1:1:void setCar_brand(java.lang.String):353:353 -> setCar_brand
    1:1:void setCar_id(java.lang.String):321:321 -> setCar_id
    1:1:void setCar_type(java.lang.String):361:361 -> setCar_type
    1:1:void setCarrier(java.lang.String):313:313 -> setCarrier
    1:1:void setChannel(java.lang.String):425:425 -> setChannel
    1:1:void setDeveloper(java.lang.String):433:433 -> setDeveloper
    1:1:void setEventcode(java.lang.String):184:184 -> setEventcode
    1:1:void setImsi(java.lang.String):168:168 -> setImsi
    1:1:void setLat(java.lang.String):208:208 -> setLat
    1:1:void setLib_version(java.lang.String):273:273 -> setLib_version
    1:1:void setLon(java.lang.String):200:200 -> setLon
    1:1:void setManufacturer(java.lang.String):377:377 -> setManufacturer
    1:1:void setMarket_type(java.lang.String):329:329 -> setMarket_type
    1:1:void setModel(java.lang.String):385:385 -> setModel
    1:1:void setNetwork(java.lang.String):305:305 -> setNetwork
    1:1:void setOem(java.lang.String):345:345 -> setOem
    1:1:void setOpenid(java.lang.String):216:216 -> setOpenid
    1:1:void setOs(java.lang.String):232:232 -> setOs
    1:1:void setOsversion(java.lang.String):417:417 -> setOsversion
    1:1:void setPage(java.lang.String):224:224 -> setPage
    1:1:void setPlayid(java.lang.String):393:393 -> setPlayid
    1:1:void setReport_timely(java.lang.String):401:401 -> setReport_timely
    1:1:void setScreen_direction(java.lang.String):369:369 -> setScreen_direction
    1:1:void setScreen_height(java.lang.String):240:240 -> setScreen_height
    1:1:void setScreen_width(java.lang.String):248:248 -> setScreen_width
    1:1:void setSessionid(java.lang.String):281:281 -> setSessionid
    1:1:void setTimestamp(java.lang.String):192:192 -> setTimestamp
    1:1:void setUdid(java.lang.String):409:409 -> setUdid
    1:1:void setUid(java.lang.String):176:176 -> setUid
    1:1:void setWifi(java.lang.String):297:297 -> setWifi
com.kaolafm.report.event.BroadcastEndListenReportEvent -> com.kaolafm.report.event.BroadcastEndListenReportEvent:
    1:2:void <init>():29:30 -> <init>
    1:1:java.lang.String getAudioid():46:46 -> getAudioid
    1:1:java.lang.String getPlaytime():70:70 -> getPlaytime
    1:1:java.lang.String getRadioid():54:54 -> getRadioid
    1:1:java.lang.String getStatus():62:62 -> getStatus
    1:9:void playParameterToEvent(com.kaolafm.report.model.PlayReportParameter):34:42 -> playParameterToEvent
    1:1:void setAudioid(java.lang.String):50:50 -> setAudioid
    1:1:void setPlaytime(java.lang.String):74:74 -> setPlaytime
    1:1:void setRadioid(java.lang.String):58:58 -> setRadioid
    1:1:void setStatus(java.lang.String):66:66 -> setStatus
com.kaolafm.report.event.BroadcastStartListenReportEvent -> com.kaolafm.report.event.BroadcastStartListenReportEvent:
    1:2:void <init>():26:27 -> <init>
    1:1:java.lang.String getAudioid():31:31 -> getAudioid
    1:1:java.lang.String getRadioid():39:39 -> getRadioid
    1:1:java.lang.String getStatus():47:47 -> getStatus
    1:1:void setAudioid(java.lang.String):35:35 -> setAudioid
    1:1:void setRadioid(java.lang.String):43:43 -> setRadioid
    1:1:void setStatus(java.lang.String):51:51 -> setStatus
com.kaolafm.report.event.BufferEndReportEvent -> com.kaolafm.report.event.BufferEndReportEvent:
    1:1:void <init>():38:38 -> <init>
    2:18:void <init>():24:40 -> <init>
    1:1:java.lang.String getAlbumid():60:60 -> getAlbumid
    1:1:java.lang.String getAudioid():44:44 -> getAudioid
    1:1:java.lang.String getPlaytime():92:92 -> getPlaytime
    1:1:java.lang.String getRadioid():52:52 -> getRadioid
    1:1:java.lang.String getRemarks1():76:76 -> getRemarks1
    1:1:java.lang.String getRemarks2():84:84 -> getRemarks2
    1:1:java.lang.String getType():68:68 -> getType
    1:1:void setAlbumid(java.lang.String):64:64 -> setAlbumid
    1:1:void setAudioid(java.lang.String):48:48 -> setAudioid
    1:1:void setPlaytime(java.lang.String):96:96 -> setPlaytime
    1:1:void setRadioid(java.lang.String):56:56 -> setRadioid
    1:1:void setRemarks1(java.lang.String):80:80 -> setRemarks1
    1:1:void setRemarks2(java.lang.String):88:88 -> setRemarks2
    1:1:void setType(java.lang.String):72:72 -> setType
com.kaolafm.report.event.BufferStartReportEvent -> com.kaolafm.report.event.BufferStartReportEvent:
    1:1:void <init>():34:34 -> <init>
    2:12:void <init>():25:35 -> <init>
    1:1:java.lang.String getAlbumid():56:56 -> getAlbumid
    1:1:java.lang.String getAudioid():40:40 -> getAudioid
    1:1:java.lang.String getRadioid():48:48 -> getRadioid
    1:1:java.lang.String getRemarks1():72:72 -> getRemarks1
    1:1:java.lang.String getRemarks2():80:80 -> getRemarks2
    1:1:java.lang.String getType():64:64 -> getType
    1:1:void setAlbumid(java.lang.String):60:60 -> setAlbumid
    1:1:void setAudioid(java.lang.String):44:44 -> setAudioid
    1:1:void setRadioid(java.lang.String):52:52 -> setRadioid
    1:1:void setRemarks1(java.lang.String):76:76 -> setRemarks1
    1:1:void setRemarks2(java.lang.String):84:84 -> setRemarks2
    1:1:void setType(java.lang.String):68:68 -> setType
com.kaolafm.report.event.CrashReportEvent -> com.kaolafm.report.event.CrashReportEvent:
    1:2:void <init>():22:23 -> <init>
    1:1:java.lang.String getMessage():35:35 -> getMessage
    1:1:java.lang.String getResult():27:27 -> getResult
    1:1:void setMessage(java.lang.String):39:39 -> setMessage
    1:1:void setResult(java.lang.String):31:31 -> setResult
com.kaolafm.report.event.EndListenReportEvent -> com.kaolafm.report.event.EndListenReportEvent:
    1:1:void <init>():100:100 -> <init>
    2:68:void <init>():35:101 -> <init>
    1:1:java.lang.String getAi_mz_location():261:261 -> getAi_mz_location
    1:1:java.lang.String getAlbumid():163:163 -> getAlbumid
    1:1:java.lang.String getAudioid():147:147 -> getAudioid
    1:1:java.lang.String getLength():203:203 -> getLength
    1:1:java.lang.String getPlayrate():195:195 -> getPlayrate
    1:1:java.lang.String getPlaytime():187:187 -> getPlaytime
    1:1:java.lang.String getPosition():179:179 -> getPosition
    1:1:java.lang.String getRadioid():155:155 -> getRadioid
    1:1:java.lang.String getRemarks10():269:269 -> getRemarks10
    1:1:java.lang.String getRemarks11():285:285 -> getRemarks11
    1:1:java.lang.String getRemarks2():211:211 -> getRemarks2
    1:1:java.lang.String getRemarks4():220:220 -> getRemarks4
    1:1:java.lang.String getRemarks6():229:229 -> getRemarks6
    1:1:java.lang.String getRemarks7():237:237 -> getRemarks7
    1:1:java.lang.String getRemarks8():245:245 -> getRemarks8
    1:1:java.lang.String getRemarks9():253:253 -> getRemarks9
    1:1:java.lang.String getSource():277:277 -> getSource
    1:1:java.lang.String getType():171:171 -> getType
    1:38:void playParameterToEvent(com.kaolafm.report.model.PlayReportParameter):105:142 -> playParameterToEvent
    1:1:void setAi_mz_location(java.lang.String):265:265 -> setAi_mz_location
    1:1:void setAlbumid(java.lang.String):167:167 -> setAlbumid
    1:1:void setAudioid(java.lang.String):151:151 -> setAudioid
    1:1:void setLength(java.lang.String):207:207 -> setLength
    1:1:void setPlayrate(java.lang.String):199:199 -> setPlayrate
    1:1:void setPlaytime(java.lang.String):191:191 -> setPlaytime
    1:1:void setPosition(java.lang.String):183:183 -> setPosition
    1:1:void setRadioid(java.lang.String):159:159 -> setRadioid
    1:1:void setRemarks10(java.lang.String):273:273 -> setRemarks10
    1:1:void setRemarks11(java.lang.String):289:289 -> setRemarks11
    1:1:void setRemarks2(java.lang.String):215:215 -> setRemarks2
    1:1:void setRemarks4(java.lang.String):224:224 -> setRemarks4
    1:1:void setRemarks6(java.lang.String):233:233 -> setRemarks6
    1:1:void setRemarks7(java.lang.String):241:241 -> setRemarks7
    1:1:void setRemarks8(java.lang.String):249:249 -> setRemarks8
    1:1:void setRemarks9(java.lang.String):257:257 -> setRemarks9
    1:1:void setSource(java.lang.String):281:281 -> setSource
    1:1:void setType(java.lang.String):175:175 -> setType
com.kaolafm.report.event.KaoLaFmToKRadioEvent -> com.kaolafm.report.event.KaoLaFmToKRadioEvent:
    1:2:void <init>():11:12 -> <init>
com.kaolafm.report.event.LivingEndListenReportEvent -> com.kaolafm.report.event.LivingEndListenReportEvent:
    1:2:void <init>():38:39 -> <init>
    1:1:java.lang.String getLive_id():54:54 -> getLive_id
    1:1:java.lang.String getLive_manager_uid():95:95 -> getLive_manager_uid
    1:1:java.lang.String getPlan_id():62:62 -> getPlan_id
    1:1:java.lang.String getPlaytime():79:79 -> getPlaytime
    1:1:java.lang.String getPosition():87:87 -> getPosition
    1:1:java.lang.String getStatus():71:71 -> getStatus
    1:7:void playParameterToEvent(com.kaolafm.report.model.PlayReportParameter):44:50 -> playParameterToEvent
    1:1:void setLive_id(java.lang.String):58:58 -> setLive_id
    1:1:void setLive_manager_uid(java.lang.String):99:99 -> setLive_manager_uid
    1:1:void setPlan_id(java.lang.String):66:66 -> setPlan_id
    1:1:void setPlaytime(java.lang.String):83:83 -> setPlaytime
    1:1:void setPosition(java.lang.String):91:91 -> setPosition
    1:1:void setStatus(java.lang.String):75:75 -> setStatus
com.kaolafm.report.event.LivingLeaveMessageReportEvent -> com.kaolafm.report.event.LivingLeaveMessageReportEvent:
    1:2:void <init>():25:26 -> <init>
    1:1:java.lang.String getLive_id():30:30 -> getLive_id
    1:1:java.lang.String getLive_manager_uid():46:46 -> getLive_manager_uid
    1:1:java.lang.String getPlan_id():38:38 -> getPlan_id
    1:1:java.lang.String getRadioid():54:54 -> getRadioid
    1:1:void setLive_id(java.lang.String):34:34 -> setLive_id
    1:1:void setLive_manager_uid(java.lang.String):50:50 -> setLive_manager_uid
    1:1:void setPlan_id(java.lang.String):42:42 -> setPlan_id
    1:1:void setRadioid(java.lang.String):58:58 -> setRadioid
com.kaolafm.report.event.LivingStartListenReportEvent -> com.kaolafm.report.event.LivingStartListenReportEvent:
    1:2:void <init>():37:38 -> <init>
    1:1:java.lang.String getLive_id():42:42 -> getLive_id
    1:1:java.lang.String getLive_manager_uid():74:74 -> getLive_manager_uid
    1:1:java.lang.String getPlan_id():50:50 -> getPlan_id
    1:1:java.lang.String getPosition():58:58 -> getPosition
    1:1:java.lang.String getStatus():66:66 -> getStatus
    1:1:void setLive_id(java.lang.String):46:46 -> setLive_id
    1:1:void setLive_manager_uid(java.lang.String):78:78 -> setLive_manager_uid
    1:1:void setPlan_id(java.lang.String):54:54 -> setPlan_id
    1:1:void setPosition(java.lang.String):62:62 -> setPosition
    1:1:void setStatus(java.lang.String):70:70 -> setStatus
com.kaolafm.report.event.LoginReportEvent -> com.kaolafm.report.event.LoginReportEvent:
    1:1:void <init>():15:15 -> <init>
    2:5:void <init>():13:16 -> <init>
    1:1:java.lang.String getType():20:20 -> getType
    1:1:void setType(java.lang.String):24:24 -> setType
com.kaolafm.report.event.MinusFeedbackReportEvent -> com.kaolafm.report.event.MinusFeedbackReportEvent:
    1:2:void <init>():25:26 -> <init>
    1:1:java.lang.String getAlbumid():46:46 -> getAlbumid
    1:1:java.lang.String getAudioid():30:30 -> getAudioid
    1:1:java.lang.String getPosition():62:62 -> getPosition
    1:1:java.lang.String getRadioid():38:38 -> getRadioid
    1:1:java.lang.String getRemarks11():54:54 -> getRemarks11
    1:1:void setAlbumid(java.lang.String):50:50 -> setAlbumid
    1:1:void setAudioid(java.lang.String):34:34 -> setAudioid
    1:1:void setPosition(java.lang.String):66:66 -> setPosition
    1:1:void setRadioid(java.lang.String):42:42 -> setRadioid
    1:1:void setRemarks11(java.lang.String):58:58 -> setRemarks11
com.kaolafm.report.event.PlayerUiControlReportEvent -> com.kaolafm.report.event.PlayerUiControlReportEvent:
    1:2:void <init>():38:39 -> <init>
    1:1:java.lang.String getControltype():51:51 -> getControltype
    1:1:java.lang.String getPosition():59:59 -> getPosition
    1:1:java.lang.String getType():43:43 -> getType
    1:1:void setControltype(java.lang.String):55:55 -> setControltype
    1:1:void setPosition(java.lang.String):63:63 -> setPosition
    1:1:void setType(java.lang.String):47:47 -> setType
com.kaolafm.report.event.PlusFeedbackReportEvent -> com.kaolafm.report.event.PlusFeedbackReportEvent:
    1:2:void <init>():18:19 -> <init>
    1:1:java.lang.String getAlbumid():39:39 -> getAlbumid
    1:1:java.lang.String getAudioid():23:23 -> getAudioid
    1:1:java.lang.String getRadioid():31:31 -> getRadioid
    1:1:java.lang.String getRemarks11():47:47 -> getRemarks11
    1:1:void setAlbumid(java.lang.String):43:43 -> setAlbumid
    1:1:void setAudioid(java.lang.String):27:27 -> setAudioid
    1:1:void setRadioid(java.lang.String):35:35 -> setRadioid
    1:1:void setRemarks11(java.lang.String):51:51 -> setRemarks11
com.kaolafm.report.event.RecommendSelectReportEvent -> com.kaolafm.report.event.RecommendSelectReportEvent:
    1:2:void <init>():13:14 -> <init>
    1:1:java.lang.String getRemarks11():26:26 -> getRemarks11
    1:1:java.lang.String getType():18:18 -> getType
    1:1:void setRemarks11(java.lang.String):30:30 -> setRemarks11
    1:1:void setType(java.lang.String):22:22 -> setType
com.kaolafm.report.event.RecommendShowReportEvent -> com.kaolafm.report.event.RecommendShowReportEvent:
    1:2:void <init>():12:13 -> <init>
    1:1:java.lang.String getRemarks11():25:25 -> getRemarks11
    1:1:java.lang.String getType():17:17 -> getType
    1:1:void setRemarks11(java.lang.String):29:29 -> setRemarks11
    1:1:void setType(java.lang.String):21:21 -> setType
com.kaolafm.report.event.RequetErrorReportEvent -> com.kaolafm.report.event.RequetErrorReportEvent:
    1:1:void <init>():43:43 -> <init>
    2:27:void <init>():19:44 -> <init>
    1:1:java.lang.String getAudioid():80:80 -> getAudioid
    1:1:java.lang.String getMessage():72:72 -> getMessage
    1:1:java.lang.String getRadioid():88:88 -> getRadioid
    1:1:java.lang.String getRemarks1():96:96 -> getRemarks1
    1:1:java.lang.String getRemarks2():104:104 -> getRemarks2
    1:1:java.lang.String getRemarks3():112:112 -> getRemarks3
    1:1:java.lang.String getResult():48:48 -> getResult
    1:1:java.lang.String getSpeed():56:56 -> getSpeed
    1:1:java.lang.String getUrl():64:64 -> getUrl
    1:1:void setAudioid(java.lang.String):84:84 -> setAudioid
    1:1:void setMessage(java.lang.String):76:76 -> setMessage
    1:1:void setRadioid(java.lang.String):92:92 -> setRadioid
    1:1:void setRemarks1(java.lang.String):100:100 -> setRemarks1
    1:1:void setRemarks2(java.lang.String):108:108 -> setRemarks2
    1:1:void setRemarks3(java.lang.String):116:116 -> setRemarks3
    1:1:void setResult(java.lang.String):52:52 -> setResult
    1:1:void setSpeed(java.lang.String):60:60 -> setSpeed
    1:1:void setUrl(java.lang.String):68:68 -> setUrl
com.kaolafm.report.event.SearchResultReportEvent -> com.kaolafm.report.event.SearchResultReportEvent:
    1:1:void <init>():48:48 -> <init>
    2:22:void <init>():29:49 -> <init>
    1:1:java.lang.String getPlaytype():78:78 -> getPlaytype
    1:1:java.lang.String getRemarks1():86:86 -> getRemarks1
    1:1:java.lang.String getRemarks2():94:94 -> getRemarks2
    1:1:java.lang.String getRequest_agent():54:54 -> getRequest_agent
    1:1:java.lang.String getResult():70:70 -> getResult
    1:1:java.lang.String getType():62:62 -> getType
    1:1:void setPlaytype(java.lang.String):82:82 -> setPlaytype
    1:1:void setRemarks1(java.lang.String):90:90 -> setRemarks1
    1:1:void setRemarks2(java.lang.String):98:98 -> setRemarks2
    1:1:void setRequest_agent(java.lang.String):58:58 -> setRequest_agent
    1:1:void setResult(java.lang.String):74:74 -> setResult
    1:1:void setType(java.lang.String):66:66 -> setType
com.kaolafm.report.event.SearchResultSelectReportEvent -> com.kaolafm.report.event.SearchResultSelectReportEvent:
    1:1:void <init>():37:37 -> <init>
    2:21:void <init>():19:38 -> <init>
    1:1:java.lang.String getRadioid():50:50 -> getRadioid
    1:1:java.lang.String getRemarks2():58:58 -> getRemarks2
    1:1:java.lang.String getRemarks3():66:66 -> getRemarks3
    1:1:java.lang.String getRemarks9():74:74 -> getRemarks9
    1:1:java.lang.String getWay():42:42 -> getWay
    1:1:void setRadioid(java.lang.String):54:54 -> setRadioid
    1:1:void setRemarks2(java.lang.String):62:62 -> setRemarks2
    1:1:void setRemarks3(java.lang.String):70:70 -> setRemarks3
    1:1:void setRemarks9(java.lang.String):78:78 -> setRemarks9
    1:1:void setWay(java.lang.String):46:46 -> setWay
com.kaolafm.report.event.StartListenReportEvent -> com.kaolafm.report.event.StartListenReportEvent:
    1:1:void <init>():74:74 -> <init>
    2:45:void <init>():32:75 -> <init>
    1:1:java.lang.String getAi_mz_location():151:151 -> getAi_mz_location
    1:1:java.lang.String getAlbumid():95:95 -> getAlbumid
    1:1:java.lang.String getAudioid():79:79 -> getAudioid
    1:1:java.lang.String getPosition():111:111 -> getPosition
    1:1:java.lang.String getRadioid():87:87 -> getRadioid
    1:1:java.lang.String getRemarks1():119:119 -> getRemarks1
    1:1:java.lang.String getRemarks11():176:176 -> getRemarks11
    1:1:java.lang.String getRemarks2():127:127 -> getRemarks2
    1:1:java.lang.String getRemarks4():135:135 -> getRemarks4
    1:1:java.lang.String getRemarks6():159:159 -> getRemarks6
    1:1:java.lang.String getRemarks9():143:143 -> getRemarks9
    1:1:java.lang.String getSource():168:168 -> getSource
    1:1:java.lang.String getType():103:103 -> getType
    1:1:void setAi_mz_location(java.lang.String):155:155 -> setAi_mz_location
    1:1:void setAlbumid(java.lang.String):99:99 -> setAlbumid
    1:1:void setAudioid(java.lang.String):83:83 -> setAudioid
    1:1:void setPosition(java.lang.String):115:115 -> setPosition
    1:1:void setRadioid(java.lang.String):91:91 -> setRadioid
    1:1:void setRemarks1(java.lang.String):123:123 -> setRemarks1
    1:1:void setRemarks11(java.lang.String):180:180 -> setRemarks11
    1:1:void setRemarks2(java.lang.String):131:131 -> setRemarks2
    1:1:void setRemarks4(java.lang.String):139:139 -> setRemarks4
    1:1:void setRemarks6(java.lang.String):163:163 -> setRemarks6
    1:1:void setRemarks9(java.lang.String):147:147 -> setRemarks9
    1:1:void setSource(java.lang.String):172:172 -> setSource
    1:1:void setType(java.lang.String):107:107 -> setType
com.kaolafm.report.event.StartReportEvent -> com.kaolafm.report.event.StartReportEvent:
    1:1:void <init>():26:26 -> <init>
    2:9:void <init>():24:31 -> <init>
    1:1:java.lang.String getFlow():44:44 -> getFlow
    1:1:java.lang.String getType():36:36 -> getType
    1:1:void setFlow(java.lang.String):48:48 -> setFlow
    1:1:void setType(java.lang.String):40:40 -> setType
com.kaolafm.report.event.SubscibeReportEvent -> com.kaolafm.report.event.SubscibeReportEvent:
    1:2:void <init>():39:40 -> <init>
    1:1:java.lang.String getPosition():60:60 -> getPosition
    1:1:java.lang.String getRadioid():68:68 -> getRadioid
    1:1:java.lang.String getSubscribetype():52:52 -> getSubscribetype
    1:1:java.lang.String getType():44:44 -> getType
    1:1:void setPosition(java.lang.String):64:64 -> setPosition
    1:1:void setRadioid(java.lang.String):72:72 -> setRadioid
    1:1:void setSubscribetype(java.lang.String):56:56 -> setSubscribetype
    1:1:void setType(java.lang.String):48:48 -> setType
com.kaolafm.report.event.ToneSelectReportEvent -> com.kaolafm.report.event.ToneSelectReportEvent:
    1:2:void <init>():20:21 -> <init>
    1:1:java.lang.String getType():25:25 -> getType
    1:1:void setType(java.lang.String):29:29 -> setType
com.kaolafm.report.event.UpdateReportEvent -> com.kaolafm.report.event.UpdateReportEvent:
    1:1:void <init>():44:44 -> <init>
    2:5:void <init>():42:45 -> <init>
    1:1:java.lang.String getRemarks1():57:57 -> getRemarks1
    1:1:java.lang.String getRemarks2():65:65 -> getRemarks2
    1:1:java.lang.String getType():49:49 -> getType
    1:1:void setRemarks1(java.lang.String):61:61 -> setRemarks1
    1:1:void setRemarks2(java.lang.String):69:69 -> setRemarks2
    1:1:void setType(java.lang.String):53:53 -> setType
com.kaolafm.report.event.VersionChangeReportEvent -> com.kaolafm.report.event.VersionChangeReportEvent:
    1:1:void <init>():41:41 -> <init>
    2:5:void <init>():39:42 -> <init>
    1:1:java.lang.String getMessage():62:62 -> getMessage
    1:1:java.lang.String getRemarks1():70:70 -> getRemarks1
    1:1:java.lang.String getRemarks2():78:78 -> getRemarks2
    1:1:java.lang.String getRemarks3():86:86 -> getRemarks3
    1:1:java.lang.String getResult():46:46 -> getResult
    1:1:java.lang.String getType():54:54 -> getType
    1:1:void setMessage(java.lang.String):66:66 -> setMessage
    1:1:void setRemarks1(java.lang.String):74:74 -> setRemarks1
    1:1:void setRemarks2(java.lang.String):82:82 -> setRemarks2
    1:1:void setRemarks3(java.lang.String):90:90 -> setRemarks3
    1:1:void setResult(java.lang.String):50:50 -> setResult
    1:1:void setType(java.lang.String):58:58 -> setType
com.kaolafm.report.inner.InnerPlayReportParameter -> com.kaolafm.report.e.a:
    1:1:void <init>():9:9 -> <init>
    1:1:void setInnerPlayer():12:12 -> B
com.kaolafm.report.listener.IReportEventIntercept -> com.kaolafm.report.f.a:
    void report(java.lang.String,com.kaolafm.report.event.BaseReportEventBean) -> a
com.kaolafm.report.listener.IReportInitListener -> com.kaolafm.report.f.b:
    void initComplete() -> a
com.kaolafm.report.model.KaolaActivateData -> com.kaolafm.report.g.a:
    com.kaolafm.report.model.KaolaActivateData$CarInfoBean carInfo -> c
    com.kaolafm.report.model.KaolaActivateData$CarConfigBean carConfig -> a
    java.lang.String openid -> b
    1:1:void <init>():7:7 -> <init>
    1:1:com.kaolafm.report.model.KaolaActivateData$CarConfigBean getCarConfig():20:20 -> a
    2:2:void setCarConfig(com.kaolafm.report.model.KaolaActivateData$CarConfigBean):24:24 -> a
    3:3:void setOpenid(java.lang.String):32:32 -> a
    4:4:void setCarInfo(com.kaolafm.report.model.KaolaActivateData$CarInfoBean):40:40 -> a
    1:1:com.kaolafm.report.model.KaolaActivateData$CarInfoBean getCarInfo():36:36 -> b
    1:1:java.lang.String getOpenid():28:28 -> c
com.kaolafm.report.model.KaolaActivateData$CarConfigBean -> com.kaolafm.report.g.a$a:
    int reportInterval -> a
    1:1:void <init>():43:43 -> <init>
    1:1:int getReportInterval():51:51 -> a
    2:2:void setReportInterval(int):55:55 -> a
com.kaolafm.report.model.KaolaActivateData$CarInfoBean -> com.kaolafm.report.g.a$b:
    java.lang.String carType -> g
    java.lang.String carBrand -> f
    java.lang.String developer -> h
    java.lang.String firstAppId -> a
    java.lang.String firstAppIdName -> c
    java.lang.String marketType -> b
    java.lang.String oem -> e
    java.lang.String appIdType -> d
    1:1:void <init>():59:59 -> <init>
    1:1:java.lang.String getAppIdType():107:107 -> a
    2:2:void setAppIdType(java.lang.String):111:111 -> a
    1:1:java.lang.String getCarBrand():123:123 -> b
    2:2:void setCarBrand(java.lang.String):127:127 -> b
    1:1:java.lang.String getCarType():131:131 -> c
    2:2:void setCarType(java.lang.String):135:135 -> c
    1:1:java.lang.String getDeveloper():139:139 -> d
    2:2:void setDeveloper(java.lang.String):143:143 -> d
    1:1:java.lang.String getFirstAppId():83:83 -> e
    2:2:void setFirstAppId(java.lang.String):87:87 -> e
    1:1:java.lang.String getFirstAppIdName():99:99 -> f
    2:2:void setFirstAppIdName(java.lang.String):103:103 -> f
    1:1:java.lang.String getMarketType():91:91 -> g
    2:2:void setMarketType(java.lang.String):95:95 -> g
    1:1:java.lang.String getOem():115:115 -> h
    2:2:void setOem(java.lang.String):119:119 -> h
com.kaolafm.report.model.PlayReportParameter -> com.kaolafm.report.g.b:
    java.lang.String isStartFirstPlay -> f
    java.lang.String searchResultContent -> h
    long totalLength -> k
    java.lang.String startTime -> l
    java.lang.String playId -> n
    java.lang.String radioType -> p
    java.lang.String audioSource -> r
    java.lang.String liveType_live_id -> t
    java.lang.String liveType_compereid -> v
    java.lang.String liveType_status -> x
    java.lang.String audioid -> a
    java.lang.String albumid -> c
    java.lang.String position -> e
    int isThirdParty -> q
    java.lang.String contentObtainType -> g
    long playPosition -> j
    java.lang.String isFirst -> i
    java.lang.String changeType -> m
    java.lang.String innerPlayer -> o
    java.lang.String recommendResultCallback -> s
    java.lang.String liveType_plan_id -> u
    java.lang.String liveType_position -> w
    java.lang.String broadcast_status -> y
    int sourceType -> z
    java.lang.String radioid -> b
    java.lang.String type -> d
    1:121:void <init>():10:130 -> <init>
    1:1:boolean isSendEventNow():254:254 -> A
    1:1:java.lang.String getAlbumid():149:149 -> a
    2:2:void setAlbumid(java.lang.String):153:153 -> a
    3:3:void setPlayPosition(long):178:178 -> a
    4:4:void setIsThirdParty(int):250:250 -> a
    1:1:void setTotalLength(long):186:186 -> b
    2:2:java.lang.String getAudioSource():270:270 -> b
    3:3:void setAudioSource(java.lang.String):274:274 -> b
    4:4:void setSourceType(int):338:338 -> b
    1:1:java.lang.String getAudioid():133:133 -> c
    2:2:void setAudioid(java.lang.String):137:137 -> c
    1:1:java.lang.String getBroadcast_status():326:326 -> d
    2:2:void setBroadcast_status(java.lang.String):330:330 -> d
    1:1:java.lang.String getChangeType():230:230 -> e
    2:2:void setChangeType(java.lang.String):234:234 -> e
    1:1:java.lang.String getContentObtainType():198:198 -> f
    2:2:void setContentObtainType(java.lang.String):202:202 -> f
    1:1:void setIsFirst(java.lang.String):218:218 -> g
    2:2:java.lang.String getInnerPlayer():258:258 -> g
    1:1:void setIsStartFirstPlay(java.lang.String):194:194 -> h
    2:2:java.lang.String getIsFirst():214:214 -> h
    1:1:java.lang.String getIsStartFirstPlay():190:190 -> i
    2:2:void setLiveType_compereid(java.lang.String):306:306 -> i
    1:1:int getIsThirdParty():246:246 -> j
    2:2:void setLiveType_live_id(java.lang.String):290:290 -> j
    1:1:void setLiveType_plan_id(java.lang.String):298:298 -> k
    2:2:java.lang.String getLiveType_compereid():302:302 -> k
    1:1:java.lang.String getLiveType_live_id():286:286 -> l
    2:2:void setLiveType_position(java.lang.String):314:314 -> l
    1:1:java.lang.String getLiveType_plan_id():294:294 -> m
    2:2:void setLiveType_status(java.lang.String):322:322 -> m
    1:1:void setPlayId(java.lang.String):242:242 -> n
    2:2:java.lang.String getLiveType_position():310:310 -> n
    1:1:void setPosition(java.lang.String):169:169 -> o
    2:2:java.lang.String getLiveType_status():318:318 -> o
    1:1:java.lang.String getPlayId():238:238 -> p
    2:2:void setRadioType(java.lang.String):266:266 -> p
    1:1:void setRadioid(java.lang.String):145:145 -> q
    2:2:long getPlayPosition():174:174 -> q
    1:1:java.lang.String getPosition():165:165 -> r
    2:2:void setRecommendResultCallback(java.lang.String):282:282 -> r
    1:1:void setSearchResultContent(java.lang.String):210:210 -> s
    2:2:java.lang.String getRadioType():262:262 -> s
    1:1:java.lang.String getRadioid():141:141 -> t
    2:2:void setStartTime(java.lang.String):226:226 -> t
    1:1:void setType(java.lang.String):161:161 -> u
    2:2:java.lang.String getRecommendResultCallback():278:278 -> u
    1:1:java.lang.String getSearchResultContent():206:206 -> v
    1:1:int getSourceType():334:334 -> w
    1:1:java.lang.String getStartTime():222:222 -> x
    1:1:long getTotalLength():182:182 -> y
    1:1:java.lang.String getType():157:157 -> z
com.kaolafm.report.model.ReportBean -> com.kaolafm.report.g.c:
    java.util.List mIdList -> b
    org.json.JSONArray jsonArray -> c
    int mType -> a
    1:3:void <init>():17:19 -> <init>
    1:2:void addData(java.lang.Long,java.lang.String):23:24 -> a
    3:3:void addData(java.lang.String):28:28 -> a
    4:4:java.util.List getIdList():32:32 -> a
    5:5:void setType(int):44:44 -> a
    1:1:java.lang.String getReportValue():36:36 -> b
    1:1:int getType():40:40 -> c
com.kaolafm.report.model.ReportCarParameter -> com.kaolafm.report.g.d:
    java.lang.String carType -> g
    java.lang.String carBrand -> f
    long systemTime -> i
    java.lang.String developer -> j
    int timer -> h
    java.lang.String firstAppId -> a
    java.lang.String firstAppIdName -> c
    java.lang.String marketType -> b
    java.lang.String oem -> e
    java.lang.String appIdType -> d
    1:40:void <init>():10:49 -> <init>
    1:1:void setTimer(int):67:67 -> a
    2:2:void setSystemTime(long):75:75 -> a
    3:3:java.lang.String getAppIdType():103:103 -> a
    4:4:void setAppIdType(java.lang.String):107:107 -> a
    1:1:java.lang.String getCarBrand():119:119 -> b
    2:2:void setCarBrand(java.lang.String):123:123 -> b
    1:1:java.lang.String getCarType():127:127 -> c
    2:2:void setCarType(java.lang.String):131:131 -> c
    1:1:java.lang.String getDeveloper():135:135 -> d
    2:2:void setDeveloper(java.lang.String):139:139 -> d
    1:1:java.lang.String getFirstAppId():79:79 -> e
    2:2:void setFirstAppId(java.lang.String):83:83 -> e
    1:1:java.lang.String getFirstAppIdName():95:95 -> f
    2:2:void setFirstAppIdName(java.lang.String):99:99 -> f
    1:1:java.lang.String getMarketType():87:87 -> g
    2:2:void setMarketType(java.lang.String):91:91 -> g
    1:1:java.lang.String getOem():111:111 -> h
    2:2:void setOem(java.lang.String):115:115 -> h
    1:1:long getSystemTime():71:71 -> i
    1:1:int getTimer():63:63 -> j
com.kaolafm.report.model.ReportParameter -> com.kaolafm.report.g.e:
    java.lang.String channel -> f
    java.lang.String deviceId -> a
    java.lang.String uid -> c
    java.lang.String appid -> b
    java.lang.String lib_version -> e
    java.lang.String openid -> d
    1:1:void <init>():8:8 -> <init>
    1:1:java.lang.String getAppid():20:20 -> a
    2:2:void setAppid(java.lang.String):24:24 -> a
    1:1:java.lang.String getChannel():60:60 -> b
    2:2:void setChannel(java.lang.String):64:64 -> b
    1:1:java.lang.String getDeviceId():52:52 -> c
    2:2:void setDeviceId(java.lang.String):56:56 -> c
    1:1:java.lang.String getLib_version():44:44 -> d
    2:2:void setLib_version(java.lang.String):48:48 -> d
    1:1:java.lang.String getOpenid():36:36 -> e
    2:2:void setOpenid(java.lang.String):40:40 -> e
    1:1:java.lang.String getUid():28:28 -> f
    2:2:void setUid(java.lang.String):32:32 -> f
com.kaolafm.report.model.ReportPrivateParameter -> com.kaolafm.report.g.f:
    java.lang.String appVersionName -> f
    long action_id -> a
    int mSessionId -> b
    java.lang.String app_version -> c
    java.lang.String carType -> e
    boolean isFirstListen -> d
    1:5:void <init>():7:11 -> <init>
    1:2:long getAction_id():16:17 -> a
    3:3:void setAction_id(long):21:21 -> a
    4:4:void setmSessionId(int):29:29 -> a
    5:5:void setFirstListen(boolean):45:45 -> a
    6:6:void setAppVersionName(java.lang.String):61:61 -> a
    1:1:void setApp_version(java.lang.String):37:37 -> b
    2:2:java.lang.String getAppVersionName():57:57 -> b
    1:1:java.lang.String getApp_version():33:33 -> c
    2:2:void setCarType(java.lang.String):53:53 -> c
    1:1:java.lang.String getCarType():49:49 -> d
    1:1:int getmSessionId():25:25 -> e
    1:1:boolean isFirstListen():41:41 -> f
com.kaolafm.report.model.ReportTask -> com.kaolafm.report.g.g:
    io.reactivex.Single singleTask -> b
    int type -> a
    1:1:void <init>():9:9 -> <init>
    1:1:io.reactivex.Single getSingleTask():14:14 -> a
    2:2:void setSingleTask(io.reactivex.Single):18:18 -> a
    3:3:void setType(int):26:26 -> a
    1:1:int getType():22:22 -> b
com.kaolafm.report.util.ConfigDBHelper -> com.kaolafm.report.util.ConfigDBHelper:
    1:1:void <init>():28:28 -> <init>
    1:2:java.lang.Long lambda$insertData$0(com.kaolafm.report.database.ConfigData):62:63 -> a
    3:4:java.lang.Long lambda$deleteData$1(java.lang.Long):73:74 -> a
    5:9:com.kaolafm.report.database.ConfigData lambda$read$2(java.lang.String):83:87 -> a
    10:10:java.util.List lambda$readAll$3():96:96 -> a
    1:7:io.reactivex.Single deleteData(java.lang.Long):69:75 -> deleteData
    1:8:com.kaolafm.report.util.ConfigDBHelper getInstance():33:40 -> getInstance
    1:10:void init():44:53 -> init
    11:11:void init():49:49 -> init
    1:8:io.reactivex.Single insertData(com.kaolafm.report.database.ConfigData):58:65 -> insertData
    1:1:boolean isDaoSessionUnavailable():100:100 -> isDaoSessionUnavailable
    1:10:io.reactivex.Single read(java.lang.String):79:88 -> read
    1:4:io.reactivex.Single readAll():93:96 -> readAll
com.kaolafm.report.util.CrashHandler -> com.kaolafm.report.util.a:
    android.content.Context mContext -> b
    java.lang.Thread$UncaughtExceptionHandler mDefaultHandler -> a
    com.kaolafm.report.util.CrashHandler sInstance -> d
    com.kaolafm.report.util.CrashHandler$OnCrashListener mCrashListener -> c
    1:1:void <init>():19:19 -> <init>
    1:8:com.kaolafm.report.util.CrashHandler getInstance():23:30 -> a
    9:11:void programStart(android.content.Context):34:36 -> a
    1:1:void setOnCrashListener(com.kaolafm.report.util.CrashHandler$OnCrashListener):40:40 -> setOnCrashListener
    1:14:void uncaughtException(java.lang.Thread,java.lang.Throwable):45:58 -> uncaughtException
com.kaolafm.report.util.CrashHandler$OnCrashListener -> com.kaolafm.report.util.a$a:
    void onCrash(java.lang.Throwable) -> a
com.kaolafm.report.util.InitUtil -> com.kaolafm.report.util.b:
    1:1:void <init>():16:16 -> <init>
    1:2:void initCarInfo():19:20 -> a
    3:9:com.kaolafm.report.model.ReportParameter init(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String):52:58 -> a
    10:18:boolean isNeedUpdateReportInfo(long):68:76 -> a
com.kaolafm.report.util.InitUtil$1 -> com.kaolafm.report.util.b$a:
    1:1:void <init>():20:20 -> <init>
    1:17:void onSuccess(com.kaolafm.report.api.carinfo.model.CarInfoData):24:40 -> a
    1:1:void onSuccess(java.lang.Object):20:20 -> onSuccess
com.kaolafm.report.util.PlayReportManager -> com.kaolafm.report.util.c:
    int SAVE_POSITION_TIMER -> f
    com.kaolafm.report.model.PlayReportParameter mPlayReportParameter -> a
    java.lang.String mPlayCallBack -> c
    com.kaolafm.report.listener.IReportEventIntercept mReportEventIntercept -> d
    java.lang.String mPlayType -> b
    io.reactivex.disposables.Disposable mDisposable -> e
    1:2:void <init>():44:45 -> <init>
    void lambda$clearPlayReportParameter$0(java.lang.Long) -> a
    1:7:void addEndEvent(java.lang.String,boolean):162:168 -> a
    8:10:void clearPlayReportParameter():173:175 -> a
    11:15:void setPosition(long,long):185:189 -> a
    16:17:void setSearchPlayCallBack(java.lang.String,java.lang.String):231:232 -> a
    18:26:void makePlayId(java.lang.String):236:244 -> a
    27:35:void initPlayReportParameter(com.kaolafm.report.model.PlayReportParameter):249:257 -> a
    36:36:void setReportEventIntercept(com.kaolafm.report.listener.IReportEventIntercept):261:261 -> a
    37:40:void reportIntercept(java.lang.String,com.kaolafm.report.event.BaseReportEventBean):270:273 -> a
    void lambda$saveDB$2(java.lang.Long) -> b
    1:22:void setPlayReportParameter(com.kaolafm.report.model.PlayReportParameter):52:73 -> b
    23:24:void release():265:266 -> b
    1:21:void addEndEvent():133:153 -> c
    22:22:void lambda$savePlayPosition$1(java.lang.Long):200:200 -> c
    1:9:void addStartEvent():77:85 -> d
    1:20:void addStartListenAlbum():110:129 -> e
    1:6:void addStartListenBroadcast():90:95 -> f
    1:8:void addStartListenLiving():99:106 -> g
    1:15:void saveDB():204:218 -> h
    1:6:void savePlayPosition():195:200 -> i
    1:3:void stopSave():224:226 -> j
com.kaolafm.report.util.ReportConstants -> com.kaolafm.report.util.d:
    int UPLOAD_TASK_TYPE_NORMAL -> l
    java.lang.String EVENT_ID_LIVING_LEAVE_MESSAGE -> I
    int READ_DATA_BASE_MAX_COUNT -> h
    java.lang.String EVENT_ID_RECOMMEND_SELECT -> M
    java.lang.String EVENT_ID_START -> n
    java.lang.String PLAY_CHANGE_BY_AUTO -> Q
    java.lang.String EVENT_ID_VERSION_CHANGE -> r
    java.lang.String POSITION_INNER_APP -> U
    java.lang.String EVENT_ID_CRASH -> v
    java.lang.String COTENT_BY_OTHER -> Y
    java.lang.String EVENT_ID_KAOLA_FM_TO_K_RADIO -> z
    java.lang.String REPORT_TAG -> a
    java.lang.String ORIENTATION_LANDSCAPE -> n0
    java.lang.String GET_CAR_INFO -> e
    java.lang.String EVENT_ID_LIVING_END_LISTEN -> D
    int UPLOAD_TASK_TYPE_BY_DATA_BASE -> m
    java.lang.String EVENT_ID_MINUS_FEEDBACK -> H
    java.lang.String QQ_RADIO_AUDIO_ID -> f0
    int REPORT_EVENT_NORMAL -> h0
    java.lang.String FIRST_LISTEN -> d0
    int TASK_TYPE_INSERT -> i
    java.lang.String EVENT_ID_RECOMMEND_SHOW -> L
    java.lang.String CARRIER_LIANTONG -> b0
    int SOURCE_TYPE_LIVING -> l0
    java.lang.String EVENT_ID_UPDATE -> q
    int SOURCE_TYPE_ALBUM -> j0
    java.lang.String PLAY_TYPE_ON_LINE -> P
    java.lang.String EVENT_ID_REQUEST_ERROR -> u
    java.lang.String PLAY_CHANGE_BY_HANDWARE_CONTROL -> T
    java.lang.String EVENT_ID_SEARCH_RESULT -> y
    java.lang.String COTENT_BY_SEARCH -> X
    java.lang.String EVENT_ID_LIVING_START_LISTEN -> C
    java.lang.String DOMAIN_HEADER_REPORT -> d
    java.lang.String EVENT_ID_PLUS_FEEDBACK -> G
    int TASK_TYPE_SEND_DATA -> j
    java.lang.String EVENT_ID_LOGIN -> K
    int READ_DATE_BASE_TIMER -> f
    java.lang.String PLAY_TYPE_OFF_LINE -> O
    java.lang.String EVENT_ID_BUFFER_END -> p
    java.lang.String PLAY_CHANGE_BY_CLICK -> S
    java.lang.String EVENT_ID_LISTEN_END -> t
    java.lang.String COTENT_BY_AUDIO -> W
    java.lang.String EVENT_ID_SEARCH_RESULT_SELECT -> x
    java.lang.String REPORT_DOMAIN_NAME -> c
    java.lang.String EVENT_ID_BROADCAST_END_LISTEN -> B
    java.lang.String ORIENTATION_PORTRAIT -> m0
    java.lang.String EVENT_ID_SUBSCIBE -> F
    int TASK_TYPE_DELETE -> k
    java.lang.String EVENT_ID_TONE_SELECT -> J
    java.lang.String QQ_API_AUDIO_ID -> e0
    int UPDATE_REPORT_INFO_MAX_DAY -> g0
    java.lang.String CARRIER_DIANXIN -> c0
    java.lang.String EVENT_ID_BUFFER_START -> o
    int READ_DATE_BASE_MAX_TIMER -> g
    java.lang.String VALUE_OS_ANDROID -> N
    java.lang.String CARRIER_YIDONG -> a0
    int SOURCE_TYPE_BROADCAST -> k0
    int REPORT_EVENT_RIGHT_NOW -> i0
    java.lang.String EVENT_ID_LISTEN_START -> s
    java.lang.String PLAY_CHANGE_BY_OTHER -> R
    java.lang.String EVENT_ID_AUDIO_SEARCH -> w
    java.lang.String POSITION_OUT_APP -> V
    java.lang.String CARRIER_UN_KNOW -> Z
    java.lang.String EVENT_ID_BROADCAST_START_LISTEN -> A
    java.lang.String REPORT_BASE_URL -> b
    java.lang.String EVENT_ID_PLAYER_UI_CONTROL -> E
    1:1:void <init>():9:9 -> <init>
com.kaolafm.report.util.ReportShenCeDBHelper -> com.kaolafm.report.util.ReportShenCeDBHelper:
    1:1:void <init>():28:28 -> <init>
    1:2:java.lang.Long lambda$insertData$0(com.kaolafm.report.database.ReportData):63:64 -> a
    3:4:java.lang.Long lambda$deleteDataList$1(java.util.List):74:75 -> a
    5:6:java.util.List lambda$read$2():84:85 -> a
    1:4:com.kaolafm.report.model.ReportBean lambda$read$3(java.util.List):87:90 -> b
    1:7:io.reactivex.Single deleteDataList(java.util.List):70:76 -> deleteDataList
    1:8:com.kaolafm.report.util.ReportShenCeDBHelper getInstance():33:40 -> getInstance
    1:10:void init():44:53 -> init
    11:11:void init():49:49 -> init
    1:10:io.reactivex.Single insertData(java.lang.String):57:66 -> insertData
    1:1:boolean isDaoSessionUnavailable():97:97 -> isDaoSessionUnavailable
    1:14:io.reactivex.Single read():80:93 -> read
com.kaolafm.report.util.ReportNetworkHelper -> com.kaolafm.report.util.e:
    com.kaolafm.report.util.ReportNetworkHelper reportNetworkHelper -> a
    1:1:void <init>():17:17 -> <init>
    1:8:com.kaolafm.report.util.ReportNetworkHelper getInstance():22:29 -> a
    9:9:java.lang.Boolean lambda$request$0(java.lang.String):41:41 -> a
    10:12:void request(java.lang.String,com.kaolafm.report.util.ReportNetworkHelper$IRequestCallBack):41:43 -> a
    13:13:void lambda$request$1(com.kaolafm.report.util.ReportNetworkHelper$IRequestCallBack,java.lang.Boolean):44:44 -> a
    14:14:void lambda$request$2(com.kaolafm.report.util.ReportNetworkHelper$IRequestCallBack,java.lang.Throwable):46:46 -> a
com.kaolafm.report.util.ReportNetworkHelper$IRequestCallBack -> com.kaolafm.report.util.e$a:
    void result(boolean) -> a
com.kaolafm.report.util.ReportParameterManager -> com.kaolafm.report.util.f:
    java.lang.String os_version -> f
    java.lang.String network_type -> h
    java.lang.String screen_direction -> j
    com.kaolafm.report.model.ReportPrivateParameter mPrivateParameter -> v
    java.lang.String model -> l
    java.lang.String lon -> n
    boolean isInit -> w
    java.lang.String playId -> p
    java.lang.String appStartType -> r
    com.kaolafm.report.model.ReportCarParameter mReportCarParameter -> t
    java.lang.String mRealVersionCode -> x
    java.lang.String mUpdateType -> z
    java.lang.String mStartFirst -> a
    java.lang.String os -> c
    java.lang.String screen_width -> e
    java.lang.String wifi -> g
    java.lang.String carrier -> i
    java.lang.String page -> k
    java.lang.String manufacturer -> m
    java.lang.String lat -> o
    java.lang.String versionName -> q
    java.lang.String mOldVersion -> s
    com.kaolafm.report.model.ReportParameter mReportParameter -> u
    java.lang.String mRealVersionName -> y
    com.kaolafm.report.util.ReportParameterManager reportParameterManager -> A
    java.lang.String imsi -> b
    java.lang.String screen_height -> d
    1:1:void <init>():83:83 -> <init>
    2:59:void <init>():29:86 -> <init>
    1:6:void initNetwork():175:180 -> A
    1:13:void initOther():159:171 -> B
    1:9:void initUpdate():371:379 -> C
    1:5:boolean isFirstListen():480:484 -> D
    1:2:boolean isFirstStart():344:345 -> E
    1:8:com.kaolafm.report.util.ReportParameterManager getInstance():90:97 -> F
    1:1:void initModel():352:352 -> G
    1:5:void saveCarParameter():447:451 -> H
    1:6:void savePrivateParameter():436:441 -> I
    void lambda$saveCarParameter$4(java.lang.Long) -> a
    void lambda$saveCarParameter$5(java.lang.Throwable) -> a
    1:3:void init(com.kaolafm.report.model.ReportParameter,com.kaolafm.report.util.PlayReportManager):101:103 -> a
    4:4:void initDBRecord(com.kaolafm.report.util.PlayReportManager):107:107 -> a
    5:5:void lambda$initDBRecord$0(com.kaolafm.report.util.PlayReportManager,java.util.List):108:108 -> a
    6:6:void lambda$initDBRecord$1(com.kaolafm.report.util.PlayReportManager,java.lang.Throwable):110:110 -> a
    7:45:void initDBRecord(java.util.List,com.kaolafm.report.util.PlayReportManager):117:155 -> a
    46:46:void setReportParameter(com.kaolafm.report.model.ReportParameter):289:289 -> a
    47:48:void setReportCarParameter(com.kaolafm.report.model.ReportCarParameter):314:315 -> a
    49:51:long getActionId():335:337 -> a
    52:68:void initUpdateEvent(java.lang.String):387:403 -> a
    69:71:void setRealVersion(java.lang.String,java.lang.String):523:525 -> a
    void lambda$savePrivateParameter$2(java.lang.Long) -> b
    void lambda$savePrivateParameter$3(java.lang.Throwable) -> b
    1:1:java.lang.String getAppStartType():457:457 -> b
    2:4:void setAppStartType(java.lang.String):461:463 -> b
    1:7:java.lang.String getApp_version():241:247 -> c
    8:10:void setCarType(java.lang.String):509:511 -> c
    1:1:void setCarrier(java.lang.String):264:264 -> d
    2:3:java.lang.String getCarType():516:517 -> d
    1:1:void setImsi(java.lang.String):205:205 -> e
    2:2:java.lang.String getCarrier():260:260 -> e
    1:1:java.lang.String getImsi():201:201 -> f
    2:2:void setLat(java.lang.String):331:331 -> f
    1:1:void setLon(java.lang.String):323:323 -> g
    2:2:java.lang.String getLat():327:327 -> g
    1:1:void setManufacturer(java.lang.String):305:305 -> h
    2:2:java.lang.String getLon():319:319 -> h
    1:1:void setNetwork_type(java.lang.String):297:297 -> i
    2:2:java.lang.String getManufacturer():301:301 -> i
    1:1:void setOs(java.lang.String):213:213 -> j
    2:2:java.lang.String getModel():356:356 -> j
    1:1:void setOs_version(java.lang.String):237:237 -> k
    2:2:java.lang.String getNetwork_type():256:256 -> k
    1:1:java.lang.String getOs():209:209 -> l
    2:2:void setPage(java.lang.String):281:281 -> l
    1:1:java.lang.String getOs_version():233:233 -> m
    2:2:void setPlayId(java.lang.String):426:426 -> m
    1:1:void setScreen_direction(java.lang.String):273:273 -> n
    2:2:java.lang.String getPage():277:277 -> n
    1:1:void setScreen_height(java.lang.String):221:221 -> o
    2:2:java.lang.String getPlayId():422:422 -> o
    1:1:void setScreen_width(java.lang.String):229:229 -> p
    2:2:com.kaolafm.report.model.ReportCarParameter getReportCarParameter():309:309 -> p
    1:1:com.kaolafm.report.model.ReportParameter getReportParameter():285:285 -> q
    2:3:void setUid(java.lang.String):430:431 -> q
    1:1:java.lang.String getScreen_direction():269:269 -> r
    2:2:void setVersionName(java.lang.String):505:505 -> r
    1:1:java.lang.String getScreen_height():217:217 -> s
    2:2:void setWifi(java.lang.String):293:293 -> s
    1:1:java.lang.String getScreen_width():225:225 -> t
    2:9:boolean isNeedReportUpdate(java.lang.String):407:414 -> t
    1:2:java.lang.String getStartFirst():361:362 -> u
    1:2:int getTimer():193:194 -> v
    1:8:java.lang.String getVersionName():492:499 -> w
    1:1:java.lang.String getWifi():252:252 -> x
    1:2:int getmSessionId():185:186 -> y
    1:9:void initAppStart():468:476 -> z
com.kaolafm.report.util.ReportParameterUtil -> com.kaolafm.report.util.g:
    1:1:void <init>():19:19 -> <init>
    1:1:java.lang.String getIMSI(android.content.Context):25:25 -> a
    2:12:java.lang.String getConfiguration():152:162 -> a
    1:9:java.lang.String getOperator(android.content.Context):128:136 -> b
    10:10:android.content.Context getContext():176:176 -> b
    1:5:android.util.DisplayMetrics getDisplayMetrics():198:202 -> c
    1:1:java.lang.String getOsVersion():113:113 -> d
    1:13:android.content.pm.PackageInfo getPackageInfo():225:237 -> e
    1:5:java.lang.String getPackageName():211:215 -> f
    1:5:android.content.res.Resources getResources():185:189 -> g
    1:5:java.lang.String getScreenHeight():79:83 -> h
    1:5:java.lang.String getScreenWidth():95:99 -> i
    1:7:java.lang.String getVersionCode():62:68 -> j
    1:11:java.lang.String getVersionName():39:49 -> k
com.kaolafm.report.util.ReportShenCeTaskHelper -> com.kaolafm.report.util.h:
    com.kaolafm.report.util.ReportShenCeTaskHelper reportTaskHelper -> d
    boolean isRunning -> a
    java.util.LinkedList reportTaskLinkedList -> b
    boolean isClose -> c
    1:1:void <init>():23:23 -> <init>
    2:8:void <init>():19:25 -> <init>
    1:7:void insertTask(com.kaolafm.report.model.ReportTask):40:46 -> a
    8:12:com.kaolafm.report.model.ReportTask getTask():55:59 -> a
    13:17:void lambda$run$0(java.lang.Long):75:79 -> a
    18:19:void lambda$run$1(java.lang.Throwable):81:82 -> a
    20:25:void lambda$run$2(com.kaolafm.report.model.ReportBean):91:96 -> a
    1:2:void lambda$run$3(java.lang.Throwable):99:100 -> b
    3:7:void lambda$run$4(java.lang.Long):109:113 -> b
    8:18:boolean isHasSendTask():131:141 -> b
    1:1:void lambda$run$5(java.lang.Throwable):115:115 -> c
    2:3:void release():150:151 -> c
    1:46:void run():63:108 -> d
    47:49:void run():88:90 -> d
    50:52:void run():72:74 -> d
    1:1:void taskDone():51:51 -> e
    1:8:com.kaolafm.report.util.ReportShenCeTaskHelper getInstance():29:36 -> f
com.kaolafm.report.util.ReportShenCeTimerManager -> com.kaolafm.report.util.i:
    io.reactivex.disposables.Disposable mDisposable -> a
    com.kaolafm.base.utils.NetworkMonitor$OnNetworkStatusChangedListener onNetworkStatusChangedListener -> c
    com.kaolafm.report.util.ReportShenCeTimerManager reportTimerManager -> b
    1:1:void <clinit>():73:73 -> <clinit>
    1:1:void <init>():29:29 -> <init>
    2:2:void <init>():25:25 -> <init>
    1:1:void lambda$runTimer$0(java.lang.Long):56:56 -> a
    2:12:void addSendTask():60:70 -> a
    13:13:void lambda$static$1(int,int):74:74 -> a
    14:16:void lambda$initCrashHandler$2(java.lang.Throwable):80:82 -> a
    1:5:int getTimer():87:91 -> b
    1:4:void init():45:48 -> c
    1:3:void initCrashHandler():77:79 -> d
    1:8:void release():96:103 -> e
    1:8:com.kaolafm.report.util.ReportShenCeTimerManager getInstance():33:40 -> f
    1:4:void runTimer():53:56 -> g
com.kaolafm.report.util.ReportUploadShenCeTask -> com.kaolafm.report.util.j:
    com.kaolafm.report.model.ReportBean mReportBean -> a
    1:2:void <init>(com.kaolafm.report.model.ReportBean):15:16 -> <init>
    1:3:void lambda$report$0(boolean):22:24 -> a
    4:11:void disposeErrorResult():30:37 -> a
    1:10:void disposeSuccessResult():42:51 -> b
    1:1:void report():20:20 -> c
