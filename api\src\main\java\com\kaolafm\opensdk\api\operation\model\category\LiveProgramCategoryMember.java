package com.kaolafm.opensdk.api.operation.model.category;

/**
 * 人工运营分类成员:直播节目
 */
public class LiveProgramCategoryMember extends CategoryMember {

    /** 直播间id*/
    private long liveProgramId;

    @Override
    public String toString() {
        return "LiveProgramCategoryMember{" +
                "liveProgramId=" + liveProgramId +
                "} " + super.toString();
    }

    public long getLiveProgramId() {
        return liveProgramId;
    }

    public void setLiveProgramId(long liveProgramId) {
        this.liveProgramId = liveProgramId;
    }
}
