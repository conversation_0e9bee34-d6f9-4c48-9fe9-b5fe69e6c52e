package com.kaolafm.ad;

import android.app.Application;

import com.kaolafm.ad.di.component.DaggerAdvertisingComponent;
import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.http.core.HttpCallback;

/**
 * 单独打包时，广告SDK的入口。
 * <AUTHOR>
 * @date 2020-01-14
 */
public class AdvertisingEngine extends AdvertisingInternalEngine {

    @Override
    public void internalInit(Application application, AdvertOptions options, HttpCallback<Boolean> callback) {
        ComponentKit.getInstance().inject(DaggerAdvertisingComponent.builder(), application, options, this);
        super.internalInit(application, options, callback);
    }
}
