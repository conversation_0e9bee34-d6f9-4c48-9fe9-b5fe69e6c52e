package com.kaolafm.opensdk.api.poll;

import com.google.gson.Gson;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.history.model.SyncHistoryStatus;
import com.kaolafm.opensdk.api.live.model.SocketLiveBean;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.List;
import java.util.Map;

import okhttp3.MediaType;
import okhttp3.RequestBody;

/**
 * 轮询相关的接口
 *
 * <AUTHOR>
 * @date 2020/6/12
 */
public class PollingRequest extends BaseRequest {

    private PollingService mPollingService;

    public PollingRequest() {
        mPollingService = obtainRetrofitService(PollingService.class);
    }

    /**
     * 保存历史集合，并加积分。
     *
     * @param params
     * @param callback
     */
    public void saveHistoryAccumulate(Map<String, Object> params, HttpCallback<SyncHistoryStatus> callback) {
        String body = new Gson().toJson(params);
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);
        doHttpDeal(mPollingService.saveHistoryAccumulate(requestBody), BaseResult::getResult, callback);
    }

    /**
     * 获取积分
     * @param params
     * @param callback
     */
//    public void getIntegrals(Map<String, Object> params, HttpCallback<CoinBean> callback) {
//        doHttpDeal(mPollingService.getIntegrals(params), BaseResult::getResult, callback);
//    }

    /**
     * 获取直播入流
     *
     * @param params
     * @param callback
     */
    public void getRadioLiveStream(Map<String, Object> params, HttpCallback<List<SocketLiveBean>> callback) {
        doHttpDeal(mPollingService.getRadioLiveStream(params), BaseResult::getResult, callback);
    }

}
