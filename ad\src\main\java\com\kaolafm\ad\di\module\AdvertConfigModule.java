package com.kaolafm.ad.di.module;

import com.kaolafm.ad.AdvertOptions;
import com.kaolafm.ad.di.qualifier.AdvertAdapterQualifier;
import com.kaolafm.ad.expose.Adapter;
import com.kaolafm.ad.expose.AdvertisingImagerAdapter;
import com.kaolafm.ad.expose.AdvertisingPlayerAdapter;
import com.kaolafm.ad.expose.CompositeAdapter;
import com.kaolafm.ad.profile.AdProfileManager;
import com.kaolafm.ad.profile.AdvertisingProfile;
import com.kaolafm.ad.timer.AlarmTimer;
import com.kaolafm.ad.timer.Timer;
import com.kaolafm.opensdk.Options;
import com.kaolafm.opensdk.di.qualifier.ProfileQualifier;
import com.kaolafm.opensdk.di.scope.AppScope;

import dagger.Binds;
import dagger.Module;
import dagger.Provides;
import dagger.multibindings.IntoMap;
import dagger.multibindings.StringKey;

/**
 * 广告相关配置提供module
 * <AUTHOR>
 * @date 2020-01-14
 */
@Module
public abstract class AdvertConfigModule {

    @Provides
    @ProfileQualifier
    static AdvertisingProfile provideAdvertisingProfile(@AppScope AdProfileManager manager) {
        return manager.getProfile();
    }

    @Binds
    @AdvertAdapterQualifier
    @IntoMap
    @StringKey("Imager")
    abstract Adapter provideImagerAdapter(AdvertisingImagerAdapter imagerAdapter);

    @Binds
    @AdvertAdapterQualifier
    @IntoMap
    @StringKey("Player")
    abstract Adapter providePlayerAdapter(AdvertisingPlayerAdapter playerAdapter);

    @Binds
    @AdvertAdapterQualifier
    @IntoMap
    @StringKey("Composite")
    abstract Adapter provideCompositeAdapter(CompositeAdapter compositeAdapter);

    @Binds
    @AppScope
    abstract Timer provideTimer(AlarmTimer timer);

    /**
     * 因为Options类型与AdvertOptions不匹配，所以需要这里提供一下给{@link com.kaolafm.opensdk.di.module.HttpClientModule}。
     * @param options
     * @return
     */
    @Provides
    static Options provideAdvertOptions(AdvertOptions options) {
        return options;
    }
}
