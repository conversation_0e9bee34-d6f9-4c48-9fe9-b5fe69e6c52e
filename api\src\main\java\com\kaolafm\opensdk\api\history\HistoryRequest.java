package com.kaolafm.opensdk.api.history;

import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.history.model.ListeningHistory;
import com.kaolafm.opensdk.api.history.model.SyncHistoryStatus;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 历史记录
 *
 * <AUTHOR>
 * @date 2019/3/8
 */
public class HistoryRequest extends BaseRequest {
    private final HistoryService mHistoryService;

    private static final int STATUS_FAIL = 0;

    private static final int STATUS_SUCCESS = 1;

    public HistoryRequest() {
        mHistoryService = obtainRetrofitService(HistoryService.class);
    }

    /**
     * 获取收听历史列表，一次获取所有的，最多99条。
     *
     * @param callback 回调
     */
    public void getHistoryList(HttpCallback<List<ListeningHistory>> callback) {
        doHttpDeal(mHistoryService.getHistoryList(),
                baseResult -> {
                    //服务端返回的数据格式是分页的，但是一次返回所有的数据，所以这里去除外面没用的数据。
                    BasePageResult<List<ListeningHistory>> basePageResult = baseResult.getResult();
                    if (basePageResult != null) {
                        return basePageResult.getDataList();
                    }
                    return null;
                }, callback);
    }

    /**
     * 保存收听历史到服务器
     *
     * @param type       必填 单曲所在集合类型，专辑{@link com.kaolafm.opensdk.ResType#TYPE_ALBUM}、PGC{@link
     *                   com.kaolafm.opensdk.ResType#TYPE_RADIO}或不确定{@link com.kaolafm.opensdk.ResType#TYPE_INVALID}
     * @param id         必填 单曲所在集合id
     * @param audioId    必填 单曲id
     * @param playedTime 必填 已播放时间
     * @param duration   必填 单曲时长
     * @param timestamp  必填 时间戳
     * @param callback   选填 回调
     */
    public void saveListeningHistory(String type, long id, long audioId, long playedTime, long duration,
                                     long timestamp, HttpCallback<Boolean> callback) {
        HashMap<String, Object> allParam = new HashMap<>();
        List<Map<String, Object>> userHistoryList = new ArrayList<>();
        allParam.put("userHistoryList", userHistoryList);
        HashMap<String, Object> params = new HashMap<>();
        userHistoryList.add(params);

        params.put("type", type);
        params.put("pareContentId", id);
        params.put("contentId", audioId);
        params.put("playedTime", playedTime);
        params.put("duration", duration);
        params.put("timeStamp", timestamp);
        doHttpDeal(mHistoryService.saveListeningHistory(allParam),
                baseResult -> {
                    SyncHistoryStatus status = baseResult.getResult();
                    return status != null && STATUS_SUCCESS == status.getStatus();
                }, callback);
    }

    /**
     * 清除收听历史
     *
     * @param callback 回调
     */
    public void clearListeningHistory(HttpCallback<Boolean> callback) {
        doHttpDeal(mHistoryService.clearListeningHistory(),
                baseResult -> {
                    SyncHistoryStatus status = baseResult.getResult();
                    return status != null && STATUS_SUCCESS == status.getStatus();
                }, callback);
    }
}
