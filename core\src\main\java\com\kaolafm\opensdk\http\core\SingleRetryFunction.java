package com.kaolafm.opensdk.http.core;

import androidx.annotation.Keep;

import org.reactivestreams.Publisher;

import javax.inject.Inject;

import io.reactivex.Flowable;
import io.reactivex.functions.Function;

/**
 * single使用的重试function
 * <AUTHOR>
 * @date 2019/4/10
 */
public class SingleRetryFunction extends RetryFunction implements Function<Flowable<Throwable>, Publisher<?>> {

    @Inject
    public SingleRetryFunction() {
    }

    @Keep
    @Override
    public Flowable<?> apply(Flowable<Throwable> throwableFlowable) {
        return super.apply(throwableFlowable);
    }
}
