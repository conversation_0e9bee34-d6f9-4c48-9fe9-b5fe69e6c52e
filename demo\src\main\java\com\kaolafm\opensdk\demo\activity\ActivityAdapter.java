package com.kaolafm.opensdk.demo.activity;

import android.view.View;

import com.kaolafm.opensdk.api.activity.model.Activity;
import com.kaolafm.opensdk.demo.BaseAdapter;
import com.kaolafm.opensdk.demo.BaseHolder;
import com.kaolafm.opensdk.demo.R;

public class ActivityAdapter extends BaseAdapter<Activity> {

    @Override
    protected BaseHolder<Activity> getViewHolder(View view, int viewType) {
        return new ActivityViewHolder(view);
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_activity;
    }
}