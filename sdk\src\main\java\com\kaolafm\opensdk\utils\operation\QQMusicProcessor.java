package com.kaolafm.opensdk.utils.operation;

import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.operation.model.category.CategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.RadioQQMusicCategoryMember;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.RadioQQMusicDetailColumnMember;
import javax.inject.Inject;

/**
 * QQ音乐 分类/栏目成员处理类
 *
 * <AUTHOR>
 * @date 2018/9/25
 */

public class QQMusicProcessor implements IOperationProcessor {

    @Inject
    public QQMusicProcessor() {
    }

    @Override
    public boolean accept(CategoryMember member) {
        return member instanceof RadioQQMusicCategoryMember;
    }

    @Override
    public boolean accept(ColumnMember member) {
        return member instanceof RadioQQMusicDetailColumnMember;
    }

    @Override
    public long getId(CategoryMember member) {
        return ((RadioQQMusicCategoryMember) member).getOldId();
    }

    @Override
    public long getId(ColumnMember member) {
        return ((RadioQQMusicDetailColumnMember) member).getOldId();
    }

    @Override
    public long getListenNum(CategoryMember member) {
        return 0;
    }

    @Override
    public int getType(CategoryMember member) {
        int radioQQMusicType = ((RadioQQMusicCategoryMember) member)
                .getRadioQQMusicType();
        //场景电台
        if (radioQQMusicType == 0) {
            return ResType.TYPE_MUSIC_RADIO_SCENE;
            //标签电台
        } else if (radioQQMusicType == 1) {
            return ResType.TYPE_MUSIC_RADIO_LABEL;
        }
        return ResType.TYPE_QQ_MUSIC;
    }

    @Override
    public int getType(ColumnMember member) {
        int radioQQMusicType = ((RadioQQMusicDetailColumnMember) member)
                .getRadioQQMusicType();
        //场景电台
        if (radioQQMusicType == 0) {
            return ResType.TYPE_MUSIC_RADIO_SCENE;
            //标签电台
        } else if (radioQQMusicType == 1) {
            return ResType.TYPE_MUSIC_RADIO_LABEL;
        }
        return ResType.TYPE_QQ_MUSIC;
    }

    @Override
    public void play(CategoryMember member) {

    }

    @Override
    public void play(ColumnMember member) {

    }
}
