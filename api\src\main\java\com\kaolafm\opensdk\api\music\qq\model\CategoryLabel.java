package com.kaolafm.opensdk.api.music.qq.model;

import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * 音乐分类标签
 * <AUTHOR>
 * @date 2018/4/23
 */

public class CategoryLabel {

    @SerializedName("category_list")
    private List<CategoryDetail> categoryList;

    /**
     * category_list : [{"category_desc":"民谣原本是指各个民族的传统民歌，拥有每个民族文化的独特色彩。民谣的历史悠远，故其作者多不知名，以口头传播为主，所以它具有一定的传播性。我们在今日所说的民谣是在20世界民谣复兴过程中所演变出来的流派，指以木吉他为伴奏乐器，伴以自然的唱法，抒发大家对淳朴生活、人生理念的追求。在创作上它极具艺术家的个人情感色彩，词曲创作通俗易懂。","category_id":48,"category_imgurl":"","category_is_hot":0,"category_is_new":0,"category_is_parent":0,"category_name":"民谣","category_share_pic":"","category_show_detail":0,"category_show_type":1},{"category_desc":"致，回不去的时光和记忆；感谢所有的相遇和别离，就像时光刻在心底的纹路。","category_id":12,"category_imgurl":"","category_is_hot":0,"category_is_new":0,"category_is_parent":0,"category_name":"伤感","category_share_pic":"","category_show_detail":0,"category_show_type":1}]
     * group_id : 0
     * group_name : 最近收听
     */

    @SerializedName("group_id")
    private int groupId;

    @SerializedName("group_name")
    private String groupName;

    public List<CategoryDetail> getCategoryList() {
        return categoryList;
    }

    public int getGroupId() {
        return groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setCategoryList(List<CategoryDetail> categoryList) {
        this.categoryList = categoryList;
    }

    public void setGroupId(int groupId) {
        this.groupId = groupId;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }
}
