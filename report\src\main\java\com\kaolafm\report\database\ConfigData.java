package com.kaolafm.report.database;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Keep;

/**
 * <AUTHOR> on 2019/2/18.
 */

@Entity
public class ConfigData {
    @Id(autoincrement = false)
    private Long id;

    private int type;

    private String json;

    @Keep
    public ConfigData(Long id, int type, String json) {
        this.id = id;
        this.type = type;
        this.json = json;
    }

    @Keep
    public ConfigData() {
    }







    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public int getType() {
        return this.type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getJson() {
        return this.json;
    }

    public void setJson(String json) {
        this.json = json;
    }

}
