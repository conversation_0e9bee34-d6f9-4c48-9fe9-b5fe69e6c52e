package com.kaolafm.report.util;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;
import android.telephony.TelephonyManager;
import android.util.DisplayMetrics;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.report.ReportHelper;

/**
 * <AUTHOR> on 2019/2/18.
 */

public class ReportParameterUtil {

    public static String getIMSI(Context context) {
        String imsi = null;
        try {
            imsi = ((TelephonyManager) context
                    .getSystemService(Context.TELEPHONY_SERVICE)).getSubscriberId();
        } catch (Exception e) {
        }
        return imsi;
    }

    /**
     * 获得当前应用版本名称
     *
     * @return
     */
    public static String getVersionName() {
        String versionName = "";
        try {
            PackageInfo packageInfo = getPackageInfo();
            if (packageInfo == null) {
                return versionName;
            }
            String appAllVersionName = packageInfo.versionName;
            if (StringUtil.isEmpty(appAllVersionName)) {
                return versionName;
            }
            // 修复：检查是否包含点号，避免StringIndexOutOfBoundsException
            int lastDotIndex = appAllVersionName.lastIndexOf(".");
            if (lastDotIndex > 0) {
                versionName = appAllVersionName.substring(0, lastDotIndex);
            } else {
                // 如果没有点号，直接使用完整版本名称
                versionName = appAllVersionName;
            }
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        } catch (Exception e) {
            // 添加通用异常捕获，避免其他潜在的字符串操作异常
            e.printStackTrace();
        }
        return versionName;
    }

    /**
     * 获得当前应用版本号
     *
     * @return
     */
    public static String getVersionCode() {
        String app_version = "";
        try {
            PackageInfo packageInfo = getPackageInfo();
            if (packageInfo == null) {
                return app_version;
            }
            app_version = String.valueOf(packageInfo.versionCode);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return app_version;
    }

    /**
     * 获得设备屏幕高
     */
    public static String getScreenHeight() {
        String screenHeight = "";
        try {
            DisplayMetrics dm = getDisplayMetrics();
            if (dm == null) {
                return screenHeight;
            }
            screenHeight = String.valueOf(dm.heightPixels);
        } catch (Exception e) {
        }
        return screenHeight;
    }

    /**
     * 获得设备屏幕宽
     */
    public static String getScreenWidth() {
        String screenWidth = "";
        try {
            DisplayMetrics dm = getDisplayMetrics();
            if (dm == null) {
                return screenWidth;
            }
            screenWidth = String.valueOf(dm.widthPixels);
        } catch (Exception e) {
        }
        return screenWidth;
    }

    /**
     * 获取 OsVersion
     *
     * @return
     */
    public static String getOsVersion() {
        String os_version = null;
        try {
            os_version = String.valueOf(Build.VERSION.RELEASE);
        } catch (Exception e) {
        }
        return os_version;
    }

    /**
     * 获取当前的运营商
     *
     * @param context
     * @return 运营商名字
     */
    public static String getOperator(Context context) {
        String carrier = ReportConstants.CARRIER_UN_KNOW;
        try {
            TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
            String IMSI = telephonyManager.getSubscriberId();

            if (!StringUtil.isEmpty(IMSI)) {
                if (IMSI.startsWith("46000") || IMSI.startsWith("46002") || IMSI.startsWith("46007")) {
                    carrier = ReportConstants.CARRIER_YIDONG;
                } else if (IMSI.startsWith("46001") || IMSI.startsWith("46006")) {
                    carrier = ReportConstants.CARRIER_LIANTONG;
                } else if (IMSI.startsWith("46003")) {
                    carrier = ReportConstants.CARRIER_DIANXIN;
                }
            }
        } catch (Exception e) {
        }
        return carrier;
    }

    /**
     * 获取横屏和竖屏
     *
     * @param context
     * @return
     */
    public static String getConfiguration() {
        Resources resources = getResources();
        if (resources == null) {
            return ReportConstants.ORIENTATION_LANDSCAPE;
        }

        Configuration configuration = resources.getConfiguration();
        if (configuration == null) {
            return ReportConstants.ORIENTATION_LANDSCAPE;
        }

        int mCurrentOrientation = configuration.orientation;
        if (mCurrentOrientation == Configuration.ORIENTATION_PORTRAIT) {
            return ReportConstants.ORIENTATION_PORTRAIT;
        } else {
            return ReportConstants.ORIENTATION_LANDSCAPE;
        }
    }

    /**
     * 获取 Context
     *
     * @return
     */
    private static Context getContext() {
        return ReportHelper.getInstance().getContext();
    }

    /**
     * 获取 Resources
     *
     * @return
     */
    private static Resources getResources() {
        Context context = getContext();
        if (context == null) {
            return null;
        }
        return context.getResources();
    }

    /**
     * 获取 DisplayMetrics
     *
     * @return
     */
    private static DisplayMetrics getDisplayMetrics() {
        Resources resources = getResources();
        if (resources == null) {
            return null;
        }
        return resources.getDisplayMetrics();
    }

    /**
     * 获取 PackageName
     *
     * @return
     */
    private static String getPackageName() {
        Context context = getContext();
        if (context == null) {
            return null;
        }
        return context.getPackageName();
    }

    /**
     * 获取 PackageInfo
     *
     * @return
     * @throws PackageManager.NameNotFoundException
     */
    private static PackageInfo getPackageInfo() throws PackageManager.NameNotFoundException {
        Context context = getContext();
        if (context == null) {
            return null;
        }
        PackageManager packageManager = context.getPackageManager();
        if (packageManager == null) {
            return null;
        }
        String packageName = getPackageName();
        if (packageName == null) {
            return null;
        }
        return packageManager.getPackageInfo(packageName, 0);
    }
}
