# 广播上一首/下一首权限检查修复

## 问题描述

根据日志分析发现，上一首/下一首按钮的逻辑和用户点击列表的逻辑不一致：

- **上一首/下一首按钮**：只是简单地根据索引切换节目，没有验证目标节目是否允许播放
- **列表点击**：有完整的权限检查机制，会验证节目的播放权限

这导致了矛盾的用户体验：用户可以通过上一首按钮播放一个本来不允许播放的回听节目，但却无法通过点击列表来播放任何节目。

## 修复方案

在 `BroadcastPlayListControl.java` 的 `getPrePlayItem()` 和 `getNextPlayItem()` 方法中添加权限检查逻辑。

### 修改内容

#### 1. getPrePlayItem() 方法修改

**位置**: `player/src/main/java/com/kaolafm/opensdk/player/logic/playlist/BroadcastPlayListControl.java:289-305`

**修改前**:
```java
if (playItem.getStatus() == PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
    PlayerLogUtil.log(TAG, "getPrePlayItem", "is play back");
    // 检查回放节目是否有有效的播放URL，如果没有则需要重新获取
    if (StringUtil.isEmpty(playItem.getPlayUrl())) {
        PlayerLogUtil.log(TAG, "getPrePlayItem", "播放项需要重新获取 url");
        setAutoPlay(mPlayItemArrayList.get(mPosition), playItem);
        initLiving(LOAD_PRE_PAGE, playItem, iPlayListGetListener);
    } else {
        notifyPlayListGet(iPlayListGetListener, playItem, null);
    }
}
```

**修改后**:
```java
if (playItem.getStatus() == PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
    PlayerLogUtil.log(TAG, "getPrePlayItem", "is play back");
    // 检查回听权限：programEnable=2表示回听不能播
    if (playItem.getProgramEnable() == PlayerConstants.BROADCAST_PROGRAM_CANNOT_PLAY) {
        PlayerLogUtil.log(TAG, "getPrePlayItem", "回听不允许播放, programEnable=" + playItem.getProgramEnable());
        notifyPlayListGetError(iPlayListGetListener, playItem, PlayerConstants.ERROR_CODE_RADIO_COPYRIGHT_LITE, -1);
        return;
    }
    // 检查回放节目是否有有效的播放URL，如果没有则需要重新获取
    if (StringUtil.isEmpty(playItem.getPlayUrl())) {
        PlayerLogUtil.log(TAG, "getPrePlayItem", "播放项需要重新获取 url");
        setAutoPlay(mPlayItemArrayList.get(mPosition), playItem);
        initLiving(LOAD_PRE_PAGE, playItem, iPlayListGetListener);
    } else {
        notifyPlayListGet(iPlayListGetListener, playItem, null);
    }
}
```

#### 2. getNextPlayItem() 方法修改

**位置**: `player/src/main/java/com/kaolafm/opensdk/player/logic/playlist/BroadcastPlayListControl.java:353-369`

**修改前**:
```java
if (playItem.getStatus() == PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
    PlayerLogUtil.log(TAG, "getNextPlayItem", "is play back");
    // 检查回放节目是否有有效的播放URL，如果没有则需要重新获取
    if (StringUtil.isEmpty(playItem.getPlayUrl())) {
        PlayerLogUtil.log(TAG, "getNextPlayItem", "playback item has no url, need to fetch");
        setAutoPlay(mPlayItemArrayList.get(mPosition), playItem);
        initLiving(LOAD_NEXT_PAGE, playItem, iPlayListGetListener);
    } else {
        notifyPlayListGet(iPlayListGetListener, playItem, null);
    }
}
```

**修改后**:
```java
if (playItem.getStatus() == PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
    PlayerLogUtil.log(TAG, "getNextPlayItem", "is play back");
    // 检查回听权限：programEnable=2表示回听不能播
    if (playItem.getProgramEnable() == PlayerConstants.BROADCAST_PROGRAM_CANNOT_PLAY) {
        PlayerLogUtil.log(TAG, "getNextPlayItem", "回听不允许播放, programEnable=" + playItem.getProgramEnable());
        notifyPlayListGetError(iPlayListGetListener, playItem, PlayerConstants.ERROR_CODE_RADIO_COPYRIGHT_LITE, -1);
        return;
    }
    // 检查回放节目是否有有效的播放URL，如果没有则需要重新获取
    if (StringUtil.isEmpty(playItem.getPlayUrl())) {
        PlayerLogUtil.log(TAG, "getNextPlayItem", "playback item has no url, need to fetch");
        setAutoPlay(mPlayItemArrayList.get(mPosition), playItem);
        initLiving(LOAD_NEXT_PAGE, playItem, iPlayListGetListener);
    } else {
        notifyPlayListGet(iPlayListGetListener, playItem, null);
    }
}
```

## 权限检查逻辑说明

### programEnable 字段含义

根据 `BroadcastPlayItem.java` 中的注释：

```java
/**
 * 广播回放的状态
 * 0-节目单隐藏
 * 1-节目单显示，回放能播
 * 2-节目单显示，回放不能播
 * 如果为1，节目开播之后没有回放地址，为转码中状态
 */
private int programEnable;
```

### 权限检查逻辑

- `programEnable = 0`: 节目单隐藏（通常不会出现在播放列表中）
- `programEnable = 1`: 节目单显示，回放能播（允许播放回听）
- `programEnable = 2`: 节目单显示，回放不能播（**不允许播放回听**）

当 `programEnable = 2` 时，表示该节目的回听不允许播放，此时应该返回错误，阻止播放。

## 修复效果

修复后，上一首/下一首按钮的行为将与列表点击保持一致：

1. **一致的权限检查**：两种操作都会检查 `programEnable` 字段
2. **一致的错误处理**：当回听不允许播放时，都会返回相同的错误码
3. **一致的用户体验**：用户无论通过哪种方式都会得到相同的反馈

## 测试建议

1. **测试场景1**：当前播放的节目的上一个节目是不允许回听的回听节目
   - 预期：点击上一首按钮应该提示无法播放，而不是播放该节目

2. **测试场景2**：当前播放的节目的下一个节目是不允许回听的回听节目
   - 预期：点击下一首按钮应该提示无法播放，而不是播放该节目

3. **测试场景3**：验证允许回听的节目仍然可以正常播放
   - 预期：上一首/下一首按钮对于允许回听的节目应该正常工作

## 风险评估

- **风险等级**：低
- **影响范围**：仅影响上一首/下一首按钮的权限检查逻辑
- **向后兼容性**：完全兼容，只是增加了权限检查
- **性能影响**：无，只是增加了一个简单的字段检查

## 总结

这个修复通过在上一首/下一首逻辑中添加与列表点击相同的权限检查，解决了权限检查不一致的问题，确保用户体验的一致性。
