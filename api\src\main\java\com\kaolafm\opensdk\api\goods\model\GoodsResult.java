package com.kaolafm.opensdk.api.goods.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-02-16
 */
public class GoodsResult implements Parcelable {
    private Integer total; //商品类个数

    private List<Goods> goodsList; //

    public GoodsResult() {
    }

    public GoodsResult(Integer total, List<Goods> goodsList) {
        this.total = total;
        this.goodsList = goodsList;
    }

    protected GoodsResult(Parcel in) {
        if (in.readByte() == 0) {
            total = null;
        } else {
            total = in.readInt();
        }
        goodsList = in.createTypedArrayList(Goods.CREATOR);
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        if (total == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(total);
        }
        dest.writeTypedList(goodsList);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<GoodsResult> CREATOR = new Creator<GoodsResult>() {
        @Override
        public GoodsResult createFromParcel(Parcel in) {
            return new GoodsResult(in);
        }

        @Override
        public GoodsResult[] newArray(int size) {
            return new GoodsResult[size];
        }
    };

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public List<Goods> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<Goods> goodsList) {
        this.goodsList = goodsList;
    }
}
