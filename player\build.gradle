import com.android.build.gradle.AppPlugin
import com.android.build.gradle.LibraryPlugin
import proguard.gradle.ProGuardTask

apply plugin: 'com.android.library'
apply plugin: 'greendao-plugin'
apply plugin: 'maven'

def and = rootProject.ext.android
def dependent = rootProject.ext.dependencies

def maven = rootProject.ext.maven

maven.versionCode = 10001
maven.versionName = "1.0.1"
def VERSION_CODE = maven.versionCode
def VERSION_NAME = maven.versionName

def jarPath = 'build/releaseLibs/OpenSdk_Player' + VERSION_NAME + '.jar'

android {
    compileSdkVersion and.compileSdkVersion
    buildToolsVersion and.buildToolsVersion
    defaultConfig {
        minSdkVersion and.minSdkVersion
        targetSdkVersion and.targetSdkVersion
        versionCode VERSION_CODE
        versionName VERSION_NAME
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

        ndk {
            abiFilter("arm64-v8a")
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    //配置数据库相关信息
    greendao {
        schemaVersion 1 //数据库版本号
        daoPackage 'com.kaolafm.opensdk.player.logic.db.greendao'
        targetGenDir 'src/main/java'//设置DaoMaster、DaoSession、Dao目录
    }
}


dependencies {
    // if (isMakePlayListMoudle.toBoolean()) {
    api fileTree(dir: 'libs', include: ['*.jar'])
    compileOnly(dependent["retrofit2"]) {
        exclude module: 'okhttp'
        exclude module: 'okio'
    }
    compileOnly(dependent["retrofit2-gson"]) {
        exclude module: 'gson'
        exclude module: 'okhttp'
        exclude module: 'okio'
        exclude module: 'retrofit'
    }
    compileOnly(dependent["retrofit2-rxjava2"]) {
        exclude module: 'rxjava'
        exclude module: 'okhttp'
        exclude module: 'retrofit'
        exclude module: 'okio'
    }
    compileOnly dependent["okhttp3"]
    compileOnly(dependent["rxandroid2"]) {
        exclude module: 'rxjava'
    }
    compileOnly dependent["rxjava2"]

    compileOnly dependent["gson"]
    compileOnly project(':report')
    compileOnly project(':utils')
    implementation project(':api')
    implementation project(":core")
}

task buildJar(dependsOn: ['compileReleaseJavaWithJavac'], type: Jar) {
    //if (isMakePlayListMoudle.toBoolean()) {
    //     archiveName = "OpenSdk_Playlist" + VERSION_NAME + ".jar"
    // } else {
    archiveName = "OpenSdk_Player" + VERSION_NAME + ".jar"
    // }
    classifier = 'release'
    //需打包的资源所在的路径集
    def srcClassDir = [project.buildDir.absolutePath + "/intermediates/classes/release"]
    //初始化资源路径集
    from srcClassDir
    from android.sourceSets.main.java.srcDirs
    destinationDir = file('build/lib')
}
task proguardJar(dependsOn: ['buildJar'], type: ProGuardTask) {
    delete(project.buildDir.absolutePath)
    delete(jarPath)
    def mappingOutputDir = new File(project.buildDir.absolutePath + "/outputs/mapping/release/")
    if (!mappingOutputDir.exists()) {
        mappingOutputDir.mkdirs()
    }
    def mappingOutputFile = new File(mappingOutputDir, "mapping.txt")
    mappingOutputFile.createNewFile()

    configuration 'proguard-rules.pro'
    String inJar = buildJar.archivePath.getAbsolutePath()
    //输入 jar
    injars inJar
    //输出 jar
    outjars jarPath
    //inJar.substring(0, inJar.lastIndexOf('/')) + "/proguard-${buildJar.archiveName}"
    Plugin plugin = getPlugins().hasPlugin(AppPlugin) ? getPlugins().findPlugin(AppPlugin) : getPlugins().findPlugin(LibraryPlugin)
    if (plugin != null) {
        List<String> runtimeJarList
        if (plugin.getMetaClass().getMetaMethod("getRuntimeJarList")) {
            runtimeJarList = plugin.getRuntimeJarList()
        } else if (android.getMetaClass().getMetaMethod("getBootClasspath")) {
            runtimeJarList = android.getBootClasspath()
        } else {
//            runtimeJarList = plugin.getBootClasspath()
        }
        for (String runtimeJar : runtimeJarList) {
            //给 proguard 添加 runtime
            libraryjars(runtimeJar)
        }
    }
}


artifacts {
    archives file(jarPath)
}

uploadArchives {
    repositories {
        mavenDeployer {
            repository(url: maven.LocalRepoUrl) {
                authentication(userName: maven.UserName, password: maven.Password)
            }
            pom.project {
                name project.name
                packaging maven.libType
                version VERSION_NAME
                artifactId maven.artifactId
                groupId maven.groupId
                description maven.libDescription
            }
        }
    }
}
uploadArchives {}.dependsOn(proguardJar)
