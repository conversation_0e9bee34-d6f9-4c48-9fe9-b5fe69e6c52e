apply plugin: 'com.android.library'

apply from:'maven_push.gradle'

def and = rootProject.ext.android
def dependent = rootProject.ext.dependencies
def maven = rootProject.ext.maven

def VERSION_CODE = and.versionCode
def VERSION_NAME = and.versionName

android {
    compileSdkVersion and.compileSdkVersion
    buildToolsVersion and.buildToolsVersion
    defaultConfig {
        minSdkVersion and.minSdkVersion
        targetSdkVersion and.targetSdkVersion
        versionCode VERSION_CODE
        versionName VERSION_NAME
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
        ndk {
            abiFilter("arm64-v8a")
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            //Zipalign优化
            zipAlignEnabled false
            buildConfigField("boolean", "INCLUDE_OLD_SDK", "true")
        }
        debug {
            buildConfigField("boolean", "INCLUDE_OLD_SDK", "true")
        }
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }

    compileOptions {
        sourceCompatibility and.javaSourceVersion
        targetCompatibility and.javaTargetVersion
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }

    packagingOptions {
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/LICENSE'
    }

}

dependencies {
    api fileTree(include: ['*.jar'], dir: 'libs')
    api dependent["lifecycle-runtime"]
    api dependent["lifecycle-extensions"]
    api dependent["room-runtime"]
    annotationProcessor dependent["room-compiler"]
    api dependent["room-rxjava2"]
//    api dependent["ha-adapter"]
//    api dependent["ha-crashreporter"]
   // implementation 'androidx.core:core:1.6.0'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    annotationProcessor dependent["dagger2-compiler"]
//    embed files('libs/kl_sdk_vehicle_DEV_V2.3.0.jar')
//    embed project(path:":core", configuration:"default")
//    embed project(path:":report", configuration:"default")
    embed project(path:":ad", configuration:"default")
    embed project(path:":api", configuration:"default")
    embed project(path:":player", configuration:"default")
    embed project(path:":socket", configuration:"default")
    implementation 'com.github.tbruyelle:rxpermissions:v0.11'
    compileOnly(dependent["rxlifecycle3-components"]) {
        exclude module: 'rxjava'
        exclude module: 'appcompat-v7'
        exclude module: 'rxandroid'
        exclude module: 'support-annotations'
    }
}
