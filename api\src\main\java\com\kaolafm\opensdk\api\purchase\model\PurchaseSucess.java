package com.kaolafm.opensdk.api.purchase.model;

public class PurchaseSucess {

    //过期
    public static final int STATUS_EXPIRE = 2;

    //成功
    public static final int STATUS_SUCCESS = 1;
    //失败
    public static final int STATUS_FAILURE = 0;

    private Integer status;

    /**
     * 支付方式 1 微信 ；2 支付宝 ；-1 无类型； 4 云闪付
     */
    private int payType;

    public int getPayType() {
        return payType;
    }

    public void setPayType(int payType) {
        this.payType = payType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "PurchaseSucess{" +
                "status=" + status +
                "payType=" + payType +
                '}';
    }
}
