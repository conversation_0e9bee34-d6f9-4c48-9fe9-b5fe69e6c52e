package com.kaolafm.opensdk.api.music.qq.model;

import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * 获取榜单歌曲列表返回的结果
 *
 * <AUTHOR>
 * @date 2018/5/4
 */

public class SongChartsResult extends BaseMusicResult<List<Song>> {
    /**
     * listen_num : 19500000
     * top_banner_pic : http://y.gtimg.cn/music/common//upload/t_order_channel_hitlist_conf/67818.png
     * top_desc : 巅峰榜·流行指数根据歌曲播放次数在7天内的涨幅自动生成，集结了当下正在蹿红的单曲。
     * top_header_pic : http://y.gtimg.cn/music/common/upload/iphone_order_channel/toplist_4_300_201932121.jpg
     * top_id : 4
     * top_name : 巅峰榜·流行指数
     * top_type : 0
     */

    @SerializedName("listen_num")
    private int listenNum;

    @SerializedName("top_banner_pic")
    private String topBannerPic;

    @SerializedName("top_desc")
    private String topDesc;

    @SerializedName("top_header_pic")
    private String topHeaderPic;

    @SerializedName("top_id")
    private int topId;

    @SerializedName("top_name")
    private String topName;

    @SerializedName("top_type")
    private int topType;

    public int getListenNum() {
        return listenNum;
    }

    public String getTopBannerPic() {
        return topBannerPic;
    }

    public String getTopDesc() {
        return topDesc;
    }

    public String getTopHeaderPic() {
        return topHeaderPic;
    }

    public int getTopId() {
        return topId;
    }

    public String getTopName() {
        return topName;
    }

    public int getTopType() {
        return topType;
    }

    public void setListenNum(int listenNum) {
        this.listenNum = listenNum;
    }

    public void setTopBannerPic(String topBannerPic) {
        this.topBannerPic = topBannerPic;
    }

    public void setTopDesc(String topDesc) {
        this.topDesc = topDesc;
    }

    public void setTopHeaderPic(String topHeaderPic) {
        this.topHeaderPic = topHeaderPic;
    }

    public void setTopId(int topId) {
        this.topId = topId;
    }

    public void setTopName(String topName) {
        this.topName = topName;
    }

    public void setTopType(int topType) {
        this.topType = topType;
    }
}
