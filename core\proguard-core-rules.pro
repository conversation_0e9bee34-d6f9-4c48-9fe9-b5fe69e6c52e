# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
-include ../proguard/proguard-common-rules.pro

-keepclassmembers class com.kaolafm.opensdk.account.token.AccessTokenObserver { *;}
-keep class * extends com.kaolafm.opensdk.account.token.AccessTokenObserver
-keep class * implements com.kaolafm.opensdk.account.token.AccessToken {
    public *;
}

-keep class com.kaolafm.opensdk.Options{*;}
-keep class com.kaolafm.opensdk.Options$Builder{*;}
-keep class * extends com.kaolafm.opensdk.Options{*;}
-keep class * extends com.kaolafm.opensdk.Options$Builder{*;}

-keep class com.kaolafm.opensdk.http.download.DownloadManager {
    public *;
}
-keep class com.kaolafm.opensdk.http.download.DownloadProgress {
    public *;
}
-keep class com.kaolafm.opensdk.http.download.DownloadListener {
    public *;
}


-keep class com.kaolafm.opensdk.http.core.RuntimeTypeAdapterFactory {*;}

-keepclasseswithmembernames class * implements com.kaolafm.opensdk.account.token.IAccessToken { *;}

-keep class com.kaolafm.opensdk.http.core.HttpCallback {*;}
-keep class com.kaolafm.opensdk.http.error.ApiException {*;}

-keepclassmembers class com.kaolafm.opensdk.http.core.RequestInterceptor {
    okhttp3.Response intercept(okhttp3.Interceptor$Chain);
}
-keepclassmembers class com.kaolafm.opensdk.http.core.HttpHandler {
    okhttp3.Response intercept(okhttp3.Interceptor$Chain);
}

-keep class com.kaolafm.opensdk.http.core.AutoDisposeObserver {
    public <methods>;
}