package com.kaolafm.report.api.report;


import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.report.BuildConfig;
import com.kaolafm.report.api.ReportHostConstant;
import com.kaolafm.report.util.ReportConstants;

import io.reactivex.functions.Function;
import retrofit2.Response;

/**
 * <AUTHOR> on 2019/3/1.
 */

public class ReportBigDataRequest extends BaseRequest {
    private final String ReportBigDataApiUrl = "https://reportv2.radio.cn/"+
            (BuildConfig.BUILD_TYPE.equals("release")?"collection":"test")+
            "?";
    private final ReportBigDataApiService reportApiService;

    public ReportBigDataRequest() {
        mUrlManager.putDomain(ReportHostConstant.REPORT_DOMAIN_NAME, ReportHostConstant.REPORT_BASE_URL);
        reportApiService = mRepositoryManager.obtainRetrofitService(ReportBigDataApiService.class);
    }

    private Function<Response<Void>, Boolean> map() {
        return retrofit2.Response::isSuccessful;
    }

    public void getReport(String json, HttpCallback<Boolean> callback) {
        Logging.i(ReportConstants.REPORT_BIGDATA_TAG, "发送数据 = " + json);

        doHttpDeal(reportApiService.report(ReportBigDataApiUrl+json),map(),callback);
    }

}
