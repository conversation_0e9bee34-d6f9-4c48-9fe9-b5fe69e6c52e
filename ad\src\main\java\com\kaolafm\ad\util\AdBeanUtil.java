package com.kaolafm.ad.util;

import com.kaolafm.ad.AdConstant;
import com.kaolafm.ad.api.internal.model.AdvertReportEntity;
import com.kaolafm.ad.api.model.Advert;
import com.kaolafm.ad.api.model.AdvertisingDetails;
import com.kaolafm.ad.api.model.AttachImage;
import com.kaolafm.ad.api.model.AudioAdvert;
import com.kaolafm.ad.api.model.AudioImageAdvert;
import com.kaolafm.ad.api.model.BaseAdvert;
import com.kaolafm.ad.api.model.ImageAdvert;
import com.kaolafm.ad.api.model.InteractionAdvert;

/**
 * <AUTHOR>
 * @date 2020-02-05
 */
public class AdBeanUtil {

    private AdBeanUtil() {
    }

    /**
     * 数据转换 - AD
     *
     * @param details
     * @return
     */
    public static Advert transform(AdvertisingDetails details) {
        if (details == null) {
            return null;
        }
        Advert advert;
        switch (details.getAdType()) {
            case 3:
                advert = createAudioAdvert(details);
                break;
            case 4:
                advert = createImageAdvert(details);
                break;
            case 5:
                advert = createAudioImageAdvert(details);
                break;
            default:
                advert = new BaseAdvert();
                break;
        }
        setupAdvert(details, advert);
        //支持二次互动
        if (details.getMoreInteraction() == 1) {
            ((BaseAdvert) advert).setInteractionAdvert(createInteractionAdvert(details));
        }
        return advert;
    }

    /**
     * 配置广告公共信息。
     *
     * @param details
     * @param advert
     */
    private static void setupAdvert(AdvertisingDetails details, Advert advert) {
        advert.setId(details.getCreativeId());
        advert.setSessionId(details.getSessionId());
        advert.setType(details.getAdType());
        advert.setSubtype(details.getSubtype());
        advert.setJump(details.isJump() == 1);
        advert.setJumpSeconds(details.getJumpSeconds());
        advert.setDuration(advert.getDuration());
        //添加上报额外信息
        AdvertReportEntity advertReportEntity = new AdvertReportEntity();
        advertReportEntity.setSessionId(details.getSessionId());
        advertReportEntity.setMonitorType(details.getMonitorType());
        advertReportEntity.setPvMonitorUrl(details.getPvMonitorUrl());
        advertReportEntity.setClickMonitorUrl(details.getClickMonitorUrl());
        advert.putExtra(AdConstant.KEY_EXTRA_REPORT, advertReportEntity);
    }

    /**
     * 创建音图广告
     *
     * @param details
     * @return
     */
    private static AudioImageAdvert createAudioImageAdvert(AdvertisingDetails details) {
        AudioImageAdvert advert = new AudioImageAdvert();
        setupAudioAdvert(details, advert);
        ImageAdvert imageAdvert = createImageAdvert(details);
        setupAdvert(details, imageAdvert);
        advert.setImageAdvert(imageAdvert);
        return advert;
    }

    /**
     * 创建图片广告
     *
     * @param details
     * @return
     */
    private static ImageAdvert createImageAdvert(AdvertisingDetails details) {
        ImageAdvert advert = new ImageAdvert();
        String imageUrl = details.getImageUrl();
        advert.setUrl(imageUrl);
        advert.setLocalPath(getLocalPath(details.getSessionId(), imageUrl));
        advert.setWidth(details.getWidth());
        advert.setHeight(details.getHeight());
        advert.setExposeDuration(details.getImageDuration());
        advert.setAttachImage(createAttachImage(details));
        return advert;
    }

    private static AttachImage createAttachImage(AdvertisingDetails details) {
        String attachImageUrl = details.getAttachImageUrl();
        AttachImage attachImage = new AttachImage();
        attachImage.setUrl(attachImageUrl);
        attachImage.setLocalPath(getLocalPath(details.getSessionId(), attachImageUrl));
        attachImage.setExposeDuration(details.getAttachImageDuration());
        attachImage.setWidth(details.getAttachWidth());
        attachImage.setHeight(details.getAttachHeight());
        return attachImage;
    }

    /**
     * 创建音频广告
     *
     * @param details
     * @return
     */
    private static AudioAdvert createAudioAdvert(AdvertisingDetails details) {
        AudioAdvert advert = new AudioAdvert();
        setupAudioAdvert(details, advert);
        return advert;
    }

    /**
     * 配置音频广告数据
     *
     * @param details
     * @param advert
     */
    private static void setupAudioAdvert(AdvertisingDetails details, AudioAdvert advert) {
        String audioUrl = details.getAudioUrl();
        advert.setUrl(audioUrl);
        advert.setLocalPath(getLocalPath(details.getSessionId(), audioUrl));
        advert.setExposeDuration(details.getAudioDuration());
    }

    /**
     * 创建二次互动广告 do
     *
     * @param details
     * @return
     */
    private static InteractionAdvert createInteractionAdvert(AdvertisingDetails details) {
        InteractionAdvert interactionAdvert = new InteractionAdvert();
        setupAdvert(details, interactionAdvert);
        String sessionId = details.getSessionId();
        String moreInteractionIcon = details.getMoreInteractionIcon();
        interactionAdvert.setUrl(moreInteractionIcon);
        interactionAdvert.setLocalPath(getLocalPath(sessionId, moreInteractionIcon));
        interactionAdvert.setDescription(details.getMoreInteractionText());
        interactionAdvert.setOpportunity(details.getMoreInteractionIconDisplayOption());
        interactionAdvert.setExposeDuration(details.getMoreInteractionDisplayDuration());
        int interactionType = details.getMoreInteractionType();
        interactionAdvert.setInteractionType(interactionType);
        //点击显示图片
        if (interactionType == 1) {
            String moreInteractionImage = details.getMoreInteractionImage();
            interactionAdvert.setDestUrl(moreInteractionImage);
            interactionAdvert.setLocalImagePath(getLocalPath(sessionId, moreInteractionImage));
            interactionAdvert.setWidth(details.getMoreInteractionWidth());
            interactionAdvert.setHeight(details.getMoreInteractionHeight());
            //点击跳转到网页
        } else if (interactionType == 2) {
            interactionAdvert.setDestUrl(details.getMoreInteractionDestUrl());
        }
        return interactionAdvert;
    }

    private static String getLocalPath(String sessionId, String path) {
        return DownloadUtil.getLocalPath(sessionId, path);
    }
}
