package com.kaolafm.opensdk.demo.player;

import android.content.Intent;
import android.os.Bundle;
import android.text.method.ScrollingMovementMethod;
import android.widget.TextView;
import butterknife.BindView;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;

/**
 * 播放单曲json详情页
 *
 * <AUTHOR>
 * @date 2019-06-04
 */
public class AudioDetailActivity extends BaseActivity {

    public static final String KEY_DETAIL = "detail";

    @BindView(R.id.tv_audio_detail_info)
    TextView mTvAudioDetailInfo;

    @Override
    public int getLayoutId() {
        return R.layout.activity_audio_detail;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        setTitle("单曲详情页面");
        mTvAudioDetailInfo.setMovementMethod(ScrollingMovementMethod.getInstance());
    }

    @Override
    public void initArgs() {
        super.initArgs();
        Intent intent = getIntent();
        if (intent != null) {
            String detail = intent.getStringExtra(KEY_DETAIL);
            mTvAudioDetailInfo.setText(detail);
        }
    }

    @Override
    public void initData() {

    }
}
