package com.kaolafm.opensdk.api.ad;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.ad.model.AdZoneMapping;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.List;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;


/**
 * <AUTHOR>
 * @date 2020-01-17
 */

//http://wiki.kaolafm.com/pages/viewpage.action?pageId=11405204

public class KradioAdRequest extends BaseRequest {

    private KradioAdService mKradioAdService;

    public KradioAdRequest() {
        mKradioAdService = obtainRetrofitService(KradioAdService.class);
    }

    public void getZoneMappings(HttpCallback<List<AdZoneMapping>> callback) {
        doHttpDeal(mKradioAdService.getZoneMappings(), BaseResult::getResult, callback);
    }

    interface KradioAdService {

        @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
        @GET(KaolaApiConstant.GET_AD_ZONE_MAPPING)
        Single<BaseResult<List<AdZoneMapping>>> getZoneMappings();
    }
}
