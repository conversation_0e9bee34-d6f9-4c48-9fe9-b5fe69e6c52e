package com.kaolafm.opensdk.player.logic.model.item;

import android.os.Parcel;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.media.model.AudioFileInfo;
import com.kaolafm.opensdk.player.logic.model.item.model.AlbumInfoData;
import com.kaolafm.opensdk.player.logic.model.item.model.InfoData;
import com.kaolafm.opensdk.player.logic.model.item.model.OfflineInfoData;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import java.util.ArrayList;
import java.util.List;

/**
 * 短视频专辑- 播放对象
 */
public class VideoAlbumPlayItem extends PlayItem {


    /**
     * 离线相关数据
     */
    private OfflineInfoData mOfflineInfoData;

    /**
     * 信息相关数据
     */
    private InfoData mInfoData;

    /**
     * 所属专辑 信息
     */
    private AlbumInfoData mAlbumInfoData;


    /**
     * 分音质播放地址
     */
    private List<AudioFileInfo> playUrlData;

    /**
     * 调用播放源头的id是否是单曲
     */
    private boolean isFromAudio = false;

    public VideoAlbumPlayItem() {
        mInfoData = new InfoData();
        mOfflineInfoData = new OfflineInfoData();
        mAlbumInfoData = new AlbumInfoData();
        playUrlData = new ArrayList<>();
    }

    @Override
    public String getRadioId() {
        if (isFromAudio) return String.valueOf(getAudioId());
        return String.valueOf(mInfoData.getAlbumId());
    }

    @Override
    public String getAlbumId() {
        return String.valueOf(mInfoData.getAlbumId());
    }

    @Override
    public String getTitle() {
        String title = mInfoData.getTitle();
        if(StringUtil.isEmpty(title)){
            title = mInfoData.getAlbumName();
        }
        return title;
    }

    @Override
    public String getPicUrl() {
        String picUrl = mInfoData.getAudioPic();
        if (StringUtil.isEmpty(picUrl)) {
            picUrl = mInfoData.getAlbumPic();
        }
        return picUrl;
    }

    @Override
    public String getHost() {
        return mInfoData.getHosts();
    }

    @Override
    public String getAlbumTitle() {
        return mInfoData.getAlbumName();
    }

    @Override
    public int getType() {
        return PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM;
    }

    @Override
    public String getUpdateTime() {
        return mInfoData.getUpdateTime();
    }

    public InfoData getInfoData() {
        return mInfoData;
    }

    public void setInfodata(InfoData infoData) {
        this.mInfoData = infoData;
    }

    @Override
    public String getSourceName() {
        return mInfoData.getSourceName();
    }

    @Override
    public String getSourceLogo() {
        return mInfoData.getSourceLogo();
    }

    public OfflineInfoData getOfflineInfoData() {
        return mOfflineInfoData;
    }

    public void setOfflineInfoData(OfflineInfoData offlineInfoData) {
        this.mOfflineInfoData = offlineInfoData;
    }

    public AlbumInfoData getAlbumInfoData() {
        return mAlbumInfoData;
    }

    public void setAlbumInfoData(AlbumInfoData albumInfoData) {
        this.mAlbumInfoData = albumInfoData;
    }

    public void setPlayUrlData(List<AudioFileInfo> playUrlData) {
        this.playUrlData = playUrlData;
    }

    public List<AudioFileInfo> getPlayUrlDataList() {
        return playUrlData;
    }

    public boolean isFromAudio() {
        return isFromAudio;
    }

    public void setFromAudio(boolean fromAudio) {
        isFromAudio = fromAudio;
    }

    protected VideoAlbumPlayItem(Parcel in) {
        isFromAudio = in.readByte() != 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        super.writeToParcel(dest, flags);
        dest.writeByte((byte) (isFromAudio ? 1 : 0));
    }

    public static final Creator<VideoAlbumPlayItem> CREATOR = new Creator<VideoAlbumPlayItem>() {

        @Override
        public VideoAlbumPlayItem createFromParcel(Parcel source) {
            return new VideoAlbumPlayItem(source);
        }

        @Override
        public VideoAlbumPlayItem[] newArray(int size) {
            return new VideoAlbumPlayItem[size];
        }
    };
}
