package com.kaolafm.opensdk.api.media.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

public class PayMethod implements Parcelable {

    /** 购买方式说明 */
    @SerializedName("payTypeName")
    private String payTypeName;

    /** 购买说明，优惠说明 */
    @SerializedName("buyNotice")
    private String buyNotice;

    /** 支付类型 0 云币, 1 人民币 */
    @SerializedName("payType")
    private int payType;

    /** 原价 单位分 */
    @SerializedName("originPrice")
    private Long originPrice;

    /** 当前价格 单位分 */
    @SerializedName("currentPrice")
    private Long currentPrice;
    /** 当前价格 单位分 */
    @SerializedName("resourceId")
    private Long resourceId;

    public PayMethod() {
    }

    public PayMethod(String payTypeName, String buyNotice, int payType, Long originPrice, Long currentPrice, Long resourceId) {
        this.payTypeName = payTypeName;
        this.buyNotice = buyNotice;
        this.payType = payType;
        this.originPrice = originPrice;
        this.currentPrice = currentPrice;
        this.resourceId = resourceId;
    }

    protected PayMethod(Parcel in) {
        payTypeName = in.readString();
        buyNotice = in.readString();
        payType = in.readInt();
        if (in.readByte() == 0) {
            originPrice = null;
        } else {
            originPrice = in.readLong();
        }
        if (in.readByte() == 0) {
            currentPrice = null;
        } else {
            currentPrice = in.readLong();
        }
        if (in.readByte() == 0) {
            resourceId = null;
        } else {
            resourceId = in.readLong();
        }
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(payTypeName);
        dest.writeString(buyNotice);
        dest.writeInt(payType);
        if (originPrice == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeLong(originPrice);
        }
        if (currentPrice == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeLong(currentPrice);
        }
        if (resourceId == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeLong(resourceId);
        }
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<PayMethod> CREATOR = new Creator<PayMethod>() {
        @Override
        public PayMethod createFromParcel(Parcel in) {
            return new PayMethod(in);
        }

        @Override
        public PayMethod[] newArray(int size) {
            return new PayMethod[size];
        }
    };

    public String getPayTypeName() {
        return payTypeName;
    }

    public void setPayTypeName(String payTypeName) {
        this.payTypeName = payTypeName;
    }

    public String getBuyNotice() {
        return buyNotice;
    }

    public void setBuyNotice(String buyNotice) {
        this.buyNotice = buyNotice;
    }

    public int getPayType() {
        return payType;
    }

    public void setPayType(int payType) {
        this.payType = payType;
    }

    public Long getOriginPrice() {
        return originPrice;
    }

    public void setOriginPrice(Long originPrice) {
        this.originPrice = originPrice;
    }

    public Long getCurrentPrice() {
        return currentPrice;
    }

    public void setCurrentPrice(Long currentPrice) {
        this.currentPrice = currentPrice;
    }

    public Long getResourceId() {
        return resourceId;
    }

    public void setResourceId(Long resourceId) {
        this.resourceId = resourceId;
    }
}
