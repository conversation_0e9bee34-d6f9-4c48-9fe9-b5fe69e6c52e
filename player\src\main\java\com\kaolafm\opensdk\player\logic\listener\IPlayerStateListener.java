package com.kaolafm.opensdk.player.logic.listener;

import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

/******************************************
 * 类描述： 播放状态回调 类名称：IPlayerStateListener
 *
 * @version: 1.0
 * @author: shaoning<PERSON>ang
 * @time: 2016-7-21 16:40
 ******************************************/
public interface IPlayerStateListener {
    /**
     * Callback of Player state.
     */
    void onIdle(PlayItem playItem);

    void onPlayerPreparing(PlayItem playItem);

    void onPlayerPreparingComplete(PlayItem playItem);

    void onPlayerPlaying(PlayItem playItem);

    void onPlayerPaused(PlayItem playItem);

    void onProgress(PlayItem playItem, long progress, long total);

    void onPlayerFailed(PlayItem playItem, int what, int extra);

    void onPlayerEnd(PlayItem playItem);

    void onSeekStart(PlayItem playItem);

    void onSeekComplete(PlayItem playItem);

    void onBufferingStart(PlayItem playItem);

    void onBufferingEnd(PlayItem playItem);

    void onDownloadProgress(PlayItem playItem, long progress, long total);
}
