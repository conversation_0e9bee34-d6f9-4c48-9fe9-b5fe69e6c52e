package com.kaolafm.opensdk.demo.live.ui;


import com.kaolafm.opensdk.api.live.model.LiveInfoDetail;
import com.kaolafm.opensdk.demo.live.chat.ChatUserInfo;
import com.kaolafm.opensdk.demo.live.chat.MessageBean;
import com.kaolafm.opensdk.demo.live.play.ErrorStatus;

import java.util.ArrayList;

public interface LiveIView {

    void onSubscribeSuccess();

    void onSubscribeError();

    void onCheckSubscribeSuccess();

    void onCheckSubscribeError();

//    void onGetMediaInfoSuccess(CommonRadioBase commonRadioBase);

    void onGetMediaInfoError();

    void showLiveInfo(LiveInfoDetail info);

    void showErrorInfo(ErrorStatus status);

    void showListenerNumber(int number);

    void showFileNotExist();

    void showRecordUploadProgress(int progress);

    void showRecordUploadSuccess();

    void showRecordUploadFailure();

    void showRoomMemberEnter(ChatUserInfo member);

    void showChatMessageReceived(ArrayList<MessageBean> messageData);

}
