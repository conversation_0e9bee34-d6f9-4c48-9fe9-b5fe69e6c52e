package com.kaolafm.report.event;

import com.kaolafm.report.model.PlayReportParameter;
import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> on 2019-07-23.
 */

public class BroadcastPlayingReportEvent extends BaseReportEventBean {
    /**
     * 广播正在播放时，上报播放中的延迟
     */
    public static final int BROADCAST_PLAYING_EVENT_REPORT_DELAY = 10_000;
    /**
     * 单曲id（回放id）
     */
    private String audioid;
    /**
     * 节目id（在线广播id）
     */
    private String radioid;
    /**
     * 播放状态 1：直播中；2：回放中
     */
    private String playtime;

    public BroadcastPlayingReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_BROADCAST_PLAYING);
        setPlaytime(String.valueOf(BROADCAST_PLAYING_EVENT_REPORT_DELAY / 1000));
    }

    public String getAudioid() {
        return audioid;
    }

    public void setAudioid(String audioid) {
        this.audioid = audioid;
    }

    public String getRadioid() {
        return radioid;
    }

    public void setRadioid(String radioid) {
        this.radioid = radioid;
    }

    public String getPlaytime() {
        return playtime;
    }

    public void setPlaytime(String playtime) {
        this.playtime = playtime;
    }

    public void playParameterToEvent(PlayReportParameter parameter) {
        setAudioid(parameter.getAudioid());
        setRadioid(parameter.getRadioid());

        setPlayid(parameter.getPlayId());
        if (parameter.getSourceType() == ReportConstants.SOURCE_TYPE_BROADCAST ||
                parameter.getSourceType() == ReportConstants.SOURCE_TYPE_TV) {
            setPlaytime("10");
        }
    }
}
