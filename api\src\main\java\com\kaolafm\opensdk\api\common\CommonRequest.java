package com.kaolafm.opensdk.api.common;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.common.model.AboutInfoBean;
import com.kaolafm.opensdk.http.core.HttpCallback;

public class CommonRequest extends BaseRequest {
    private CommonService mService;

    public CommonRequest() {
        mService = obtainRetrofitService(CommonService.class);
    }


    /**
     * 获取关于我们配置
     *
     * @param callback 回调
     */
    public void getAboutInfo(HttpCallback<AboutInfoBean> callback) {
        doHttpDeal(mService.getAboutInfo(), BaseResult::getResult, callback);
    }
}
