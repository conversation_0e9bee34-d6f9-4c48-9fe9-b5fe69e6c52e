package com.kaolafm.ad.api.internal;

import androidx.annotation.NonNull;

import org.greenrobot.greendao.annotation.NotNull;

import java.util.Arrays;

/**
 * 有序的键值对对象，主要用于上报参数的排序。
 *
 * <AUTHOR>
 * @date 2020-02-13
 */
public class OrderedPair implements Comparable<OrderedPair> {

    /**
     * 索引，用于排序，越小排的越靠前
     */
    private int index;

    /**
     * 参数的键，请求是不会带上，用于描述代表什么参数的
     */
    private String key;

    /**
     * 参数的值
     */
    private Object value = null;

    public OrderedPair(@NonNull int index, @NonNull String key, Object value) {
        this.index = index;
        this.key = key;
        this.value = value;
    }

    public OrderedPair(@NonNull String key) {
        this.key = key;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(@NotNull int index) {
        this.index = index;
    }

    public String getKey() {
        return key;
    }

    public void setKey(@NotNull String key) {
        this.key = key;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return "OrderedPair{" +
                "index=" + index +
                ", key='" + key + '\'' +
                ", value=" + value +
                '}';
    }

    @Override
    public int compareTo(@NonNull OrderedPair that) {
        return this.index - that.index;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        OrderedPair that = (OrderedPair) o;
        boolean equals = this.key.equals(that.key);
        if (equals) {
            that.index = this.index;
            that.value = this.value;
        }
        return equals;
    }

    @Override
    public int hashCode() {
        return hash(key);
    }

    private int hash(Object... objects) {
        return Arrays.hashCode(objects);
    }
}
