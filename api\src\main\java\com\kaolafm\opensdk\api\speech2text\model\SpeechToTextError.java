package com.kaolafm.opensdk.api.speech2text.model;

public class SpeechToTextError extends BaseSpeechSocketMessage {
    /**
     * 错误码
     */
    private String errorCode;
    /**
     * 错误信息
     */
    private String errMsg;

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    @Override
    public String toString() {
        return "SpeechVoiceError{" +
                "taskId='" + getTaskId() + '\'' +
                ", errorCode='" + errorCode + '\'' +
                ", errMsg='" + errMsg + '\'' +
                '}';
    }
}