package com.kaolafm.opensdk.api.live.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.kaolafm.opensdk.api.yunxin.model.GiftRankUser;

import java.util.ArrayList;
import java.util.List;

public class LiveChatRoomMemberInfoResult implements Parcelable {

    // 直播间id
    private Long liveId;

    // 聊天室id
    private Long roomId;

    // 聊天室名称
    private String name;

    // 聊天室公告
    private String announcement;

    // 扩展信息
    private String ext;

    // 聊天室状态
    private Integer status;

    //当前在线用户数量
    private String onlineUserCount;

    // 在线用户信息
    private List<LiveChatRoomMemberInfo> userList;

    // 排行榜信息
    private List<GiftRankUser> rewardRanking;

    public Long getLiveId() {
        return liveId;
    }

    public void setLiveId(Long liveId) {
        this.liveId = liveId;
    }

    public Long getRoomId() {
        return roomId;
    }

    public void setRoomId(Long roomId) {
        this.roomId = roomId;
    }

    public String getName() {
        return name == null ? "" : name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAnnouncement() {
        return announcement == null ? "" : announcement;
    }

    public void setAnnouncement(String announcement) {
        this.announcement = announcement;
    }

    public String getExt() {
        return ext == null ? "" : ext;
    }

    public void setExt(String ext) {
        this.ext = ext;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getOnlineUserCount() {
        return onlineUserCount == null ? "" : onlineUserCount;
    }

    public void setOnlineUserCount(String onlineUserCount) {
        this.onlineUserCount = onlineUserCount;
    }

    public List<LiveChatRoomMemberInfo> getUserList() {
        if (userList == null) {
            return new ArrayList<>();
        }
        return userList;
    }

    public void setUserList(List<LiveChatRoomMemberInfo> userList) {
        this.userList = userList;
    }

    public List<GiftRankUser> getRewardRanking() {
        if (rewardRanking == null) {
            return new ArrayList<>();
        }
        return rewardRanking;
    }

    public void setRewardRanking(List<GiftRankUser> rewardRanking) {
        this.rewardRanking = rewardRanking;
    }

    @Override
    public String toString() {
        return "LiveChatRoomMemberInfoResult{" +
                "liveId=" + liveId +
                ", roomId=" + roomId +
                ", name='" + name + '\'' +
                ", announcement='" + announcement + '\'' +
                ", ext='" + ext + '\'' +
                ", status=" + status +
                ", onlineUserCount='" + onlineUserCount + '\'' +
                ", userList=" + userList +
                ", rewardRanking=" + rewardRanking +
                '}';
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeValue(this.liveId);
        dest.writeValue(this.roomId);
        dest.writeString(this.name);
        dest.writeString(this.announcement);
        dest.writeString(this.ext);
        dest.writeValue(this.status);
        dest.writeString(this.onlineUserCount);
        dest.writeTypedList(this.userList);
        dest.writeTypedList(this.rewardRanking);
    }

    public LiveChatRoomMemberInfoResult() {
    }

    protected LiveChatRoomMemberInfoResult(Parcel in) {
        this.liveId = (Long) in.readValue(Long.class.getClassLoader());
        this.roomId = (Long) in.readValue(Long.class.getClassLoader());
        this.name = in.readString();
        this.announcement = in.readString();
        this.ext = in.readString();
        this.status = (Integer) in.readValue(Integer.class.getClassLoader());
        this.onlineUserCount = in.readString();
        this.userList = in.createTypedArrayList(LiveChatRoomMemberInfo.CREATOR);
        this.rewardRanking = in.createTypedArrayList(GiftRankUser.CREATOR);
    }

    public static final Creator<LiveChatRoomMemberInfoResult> CREATOR = new Creator<LiveChatRoomMemberInfoResult>() {
        @Override
        public LiveChatRoomMemberInfoResult createFromParcel(Parcel source) {
            return new LiveChatRoomMemberInfoResult(source);
        }

        @Override
        public LiveChatRoomMemberInfoResult[] newArray(int size) {
            return new LiveChatRoomMemberInfoResult[size];
        }
    };
}
