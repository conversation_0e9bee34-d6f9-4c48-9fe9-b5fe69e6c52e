package com.kaolafm.report.api.report;

import com.kaolafm.report.api.ReportHostConstant;

import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;

/**
 * <AUTHOR> on 2019/1/10.
 */

public interface ReportShenCeService {
    @Headers(ReportHostConstant.REPORT_DOMAIN_HEADER)
    @POST("k")
    Call<String> report(@Body RequestBody body);
}
