package com.kaolafm.opensdk.utils.operation;

import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.operation.model.category.VideoAlbumCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.CategoryMember;
import com.kaolafm.opensdk.api.operation.model.column.VideoAlbumDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;
import javax.inject.Inject;

/**
 * 视频专辑 分类/栏目成员处理类
 *
 * <AUTHOR>
 * @date 2018/9/21
 */

public class VideoAlbumProcessor implements IOperationProcessor {

    @Inject
    public VideoAlbumProcessor() {
    }

    @Override
    public boolean accept(CategoryMember member) {
        return member instanceof VideoAlbumCategoryMember;
    }

    @Override
    public boolean accept(ColumnMember member) {
        return member instanceof VideoAlbumDetailColumnMember;
    }

    @Override
    public long getId(CategoryMember member) {
        return ((VideoAlbumCategoryMember) member).getAlbumId();
    }

    @Override
    public long getId(ColumnMember member) {
        return ((VideoAlbumDetailColumnMember) member).getAlbumId();
    }

    @Override
    public long getListenNum(CategoryMember member) {
        return ((VideoAlbumCategoryMember) member).getPlayTimes();
    }

    @Override
    public int getType(CategoryMember member) {
        return ResType.TYPE_VIDEO_ALBUM;
    }

    @Override
    public int getType(ColumnMember member) {
        return ResType.TYPE_VIDEO_ALBUM;
    }

    @Override
    public void play(CategoryMember member) {
//        PlayerManager.getInstance(OpenSDK.getInstance().getContext()).playAlbum(getId(member));
    }

    @Override
    public void play(ColumnMember member) {
//        PlayerManager.getInstance(OpenSDK.getInstance().getContext()).playAlbum(getId(member));
    }
}
