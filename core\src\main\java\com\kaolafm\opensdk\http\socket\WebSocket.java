package com.kaolafm.opensdk.http.socket;

import com.kaolafm.base.utils.Yeast;
import com.kaolafm.opensdk.http.error.UTF8Exception;
import com.kaolafm.opensdk.http.socket.parser.ParseQS;
import com.kaolafm.opensdk.http.socket.parser.Parser;
import com.kaolafm.opensdk.log.Logging;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.WebSocketListener;
import okio.ByteString;

/**
 * WebSocket通信协议。使用OkHttp 3中的WebSocket进行通信
 *
 * <AUTHOR>
 * @date 2020-01-07
 */
public class WebSocket extends Transport {

    public static final String NAME = "websocket";

    private okhttp3.WebSocket okWebSocket;

    public WebSocket(Options opts) {
        super(opts);
        name = NAME;
    }

    @Override
    protected void write(Packet[] packets) throws UTF8Exception {
        writable = false;

        final Runnable done = () -> {
            // fake drain
            // defer to next tick to allow Socket to clear writeBuffer
            EventThread.nextTick(() -> {
                writable = true;
                emit(SocketEvent.EVENT_DRAIN);
            });
        };

        final int[] total = new int[]{packets.length};
        for (Packet packet : packets) {
            if (this.readyState != ReadyState.OPENING && this.readyState != ReadyState.OPEN) {
                // Ensure we don't try to send anymore packets if the socket ends up being closed due to an exception
                break;
            }

            Parser.encodePacket(packet, packet1 -> {
                try {
                    if (packet1 instanceof String) {
                        okWebSocket.send((String) packet1);
                    } else if (packet1 instanceof byte[]) {
                        okWebSocket.send(ByteString.of((byte[]) packet1));
                    }
                } catch (IllegalStateException e) {
                    Logging.e("websocket closed before we could write");
                }

                if (0 == --total[0]) {
                    done.run();
                }
            });
        }
    }

    @Override
    protected void doOpen() {
        Map<String, List<String>> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
        this.emit(EVENT_REQUEST_HEADERS, headers);

        final WebSocket self = this;
        okhttp3.WebSocket.Factory factory = webSocketFactory != null ? webSocketFactory : new OkHttpClient();
        Request.Builder builder = new Request.Builder().url(uri());
        for (Map.Entry<String, List<String>> entry : headers.entrySet()) {
            for (String v : entry.getValue()) {
                builder.addHeader(entry.getKey(), v);
            }
        }
        if (this.headers != null) {
            Set<Map.Entry<String, String>> entrySet = this.headers.entrySet();
            for (Map.Entry<String, String> entry : entrySet) {
                builder.addHeader(entry.getKey(), entry.getValue());
            }
        }
        final Request request = builder.build();
        okWebSocket = factory.newWebSocket(request, new WebSocketListener() {
            @Override
            public void onOpen(okhttp3.WebSocket webSocket, Response response) {
                final Map<String, List<String>> headers = response.headers().toMultimap();
                EventThread.exec(() -> {
                    self.emit(EVENT_RESPONSE_HEADERS, headers);
                    self.onOpen();
                });
            }

            @Override
            public void onMessage(okhttp3.WebSocket webSocket, final String text) {
                if (text == null) {
                    return;
                }
                EventThread.exec(() -> self.onData(text));
            }

            @Override
            public void onMessage(okhttp3.WebSocket webSocket, final ByteString bytes) {
                if (bytes == null) {
                    return;
                }
                EventThread.exec(() -> self.onData(bytes.toByteArray()));
            }

            @Override
            public void onClosed(okhttp3.WebSocket webSocket, int code, String reason) {
                EventThread.exec(self::onClose);
            }

            @Override
            public void onFailure(okhttp3.WebSocket webSocket, final Throwable t, Response response) {
                if (!(t instanceof Exception)) {
                    return;
                }
                EventThread.exec(() -> self.onError("websocket error", (Exception) t));
            }
        });
    }

    @Override
    protected void doClose() {
        if (okWebSocket != null) {
            okWebSocket.close(1000, "");
            okWebSocket = null;
        }
    }

    private String uri() {
        Map<String, String> query = this.query;
        if (query == null) {
            query = new HashMap<>();
        }
        String schema = this.secure ? "wss" : "ws";
        String port = "";

        /*true表示不是默认端口号*/
        boolean isNotDefault = ("wss".equals(schema) && this.port != 443) || ("ws".equals(schema) && this.port != 80);
        if (this.port > 0 && isNotDefault) {
            port = ":" + this.port;
        }

        if (this.timestampRequests) {
            query.put(this.timestampParam, Yeast.yeast());
        }

        String derivedQuery = ParseQS.encode(query);
        if (derivedQuery.length() > 0) {
            derivedQuery = "?" + derivedQuery;
        }

        boolean ipv6 = this.hostname.contains(":");
        return schema + "://" + (ipv6 ? "[" + this.hostname + "]" : this.hostname) + port + this.path + derivedQuery;
    }
}
