package com.kaolafm.opensdk.api.history.model;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 **/
public class SaveHistoryItem {

    /**
     * appid : gb7303
     * audioId : 31405685
     * duration : 0
     * kradioUid : string
     * playedTime : 0
     * radioId : 1600000000454
     * timeStamp : 1566964626171
     * type : 11
     */

    @SerializedName("appid")
    private String appid;
    @SerializedName("audioId")
    private String audioId;
    @SerializedName("duration")
    private long duration;
    @SerializedName("kradioUid")
    private String kradioUid;
    @SerializedName("playedTime")
    private long playedTime;
    @SerializedName("radioId")
    private String radioId;
    @SerializedName("timeStamp")
    private long timeStamp;
    @SerializedName("type")
    private int type;

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getAudioId() {
        return audioId;
    }

    public void setAudioId(String audioId) {
        this.audioId = audioId;
    }

    public long getDuration() {
        return duration;
    }

    public void setDuration(long duration) {
        this.duration = duration;
    }

    public String getKradioUid() {
        return kradioUid;
    }

    public void setKradioUid(String kradioUid) {
        this.kradioUid = kradioUid;
    }

    public long getPlayedTime() {
        return playedTime;
    }

    public void setPlayedTime(long playedTime) {
        this.playedTime = playedTime;
    }

    public String getRadioId() {
        return radioId;
    }

    public void setRadioId(String radioId) {
        this.radioId = radioId;
    }

    public long getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(long timeStamp) {
        this.timeStamp = timeStamp;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "SaveHistoryItem{" +
                "appid='" + appid + '\'' +
                ", audioId='" + audioId + '\'' +
                ", duration=" + duration +
                ", kradioUid='" + kradioUid + '\'' +
                ", playedTime=" + playedTime +
                ", radioId='" + radioId + '\'' +
                ", timeStamp=" + timeStamp +
                ", type=" + type +
                '}';
    }
}
