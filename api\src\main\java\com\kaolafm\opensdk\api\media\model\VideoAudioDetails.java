package com.kaolafm.opensdk.api.media.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

public class VideoAudioDetails implements Parcelable {


    /**
     * audioId : 1000018856226
     * audioName : 二货一箩筐
     * audioPic : http://img.kaolafm.net/mz/images/201703/cba97a7f-5049-49c6-baf1-9c607e55f720/default.jpg
     * audioDes : 二货怎么会如此的多呢？因为世界和平的需要,总有一些人要做出选择去发光发热为别人创造快乐。
     * albumId : 1100000012708
     * albumName : 爱上晕段子
     * albumPic : http://img.kaolafm.net/mz/images/201703/cba97a7f-5049-49c6-baf1-9c607e55f720/default.jpg
     * orderNum : 1104
     * mp3PlayUrl32 : http://image.kaolafm.net/mz/mp3_32/201806/1daceed1-c40b-475a-8aae-4aafa9fbf33e.mp3
     * mp3PlayUrl64 : http://image.kaolafm.net/mz/mp3_64/201806/1daceed1-c40b-475a-8aae-4aafa9fbf33e.mp3
     * aacPlayUrl : http://image.kaolafm.net/mz/aac_32/201806/1daceed1-c40b-475a-8aae-4aafa9fbf33e.aac
     * aacPlayUrl32 : http://image.kaolafm.net/mz/aac_32/201806/1daceed1-c40b-475a-8aae-4aafa9fbf33e.aac
     * aacPlayUrl64 : http://image.kaolafm.net/mz/aac_64/201806/1daceed1-c40b-475a-8aae-4aafa9fbf33e.aac
     * aacPlayUrl128 : http://image.kaolafm.net/mz/aac_128/201806/1daceed1-c40b-475a-8aae-4aafa9fbf33e.aac
     * aacPlayUrl320: ""
     * host : [{"name":"爱野","des":"DJ爱野，曾就职于中央人民广播电台央广都市担任主持人，主持过多档娱乐脱口秀、心灵鸡汤类节目，深受听众粉丝的喜爱。现为考拉FM签约主播。\r\n","img":"http://img.kaolafm.net/mz/images/201309/be07d754-f4fd-40a6-a287-2b55246bef30/default.jpg"}]
     * aacFileSize : 1460071
     * mp3FileSize32 : 729658
     * mp3FileSize64 : 1459873
     * updateTime : 1528127381000
     * clockId :
     * duration : 182500
     * originalDuration : 182517
     * listenNum : 45346
     * likedNum : 1
     * hasCopyright : 1
     * commentNum : 1
     * trailerStart : 0
     * trailerEnd : 0
     * categoryId : 143
     * source : null
     * isListened : 0
     * icon : null
     * contentType:2 //内容类型, 0:分类,1:专辑,2:台宣,3:在线广播,4:歌曲,5:个推,6:地域,7:直播
     * contentTypeName:"台宣",//内容类型名称
     * mainTitleName:"新闻电台", //内容主标题名称
     * subheadName:"新闻FM－ID－1".//内容副标题名称
     * adZoneId:
     */

    public static final int BUY_TYPE_FREE = 0;//免费
    public static final int BUY_TYPE_AUDITION = 1;//试听
    public static final int BUY_TYPE_AUDIO = 2;//单曲购买
    public static final int BUY_TYPE_ALBUM = 3;//专辑购买
    public static final int BUY_TYPE_VIP = 4;//vip购买

    @SerializedName("id")
    private long id;

    @SerializedName("title")
    private String title;

    @SerializedName("img")
    private String img;

    @SerializedName("desc")
    private String desc;

    @SerializedName("status")
    private int status;

    @SerializedName("validStartDate")
    private long validStartDate;

    @SerializedName("playUrlId")
    private String playUrlId;

    @SerializedName("source")
    private int source;

    @SerializedName("sourceName")
    private String sourceName;

    @SerializedName("noSubscribe")
    private int noSubscribe;

    @SerializedName("catalogId")
    private int catalogId;

    @SerializedName("catalogName")
    private String catalogName;

    @SerializedName("buyType")
    private int buyType;

    @SerializedName("buyStatus")
    private int buyStatus;

    @SerializedName("albumIsFine")
    private int albumIsFine;

    @SerializedName("albumIsVip")
    private int albumIsVip;

    @SerializedName("audition")
    private int audition;

    @SerializedName("duration")
    private int duration;

    /** 播放信息 */
    @SerializedName("playInfoList")
    private List<AudioFileInfo> playInfoList;

    @SerializedName("fileSize")
    private int fileSize;

    @Deprecated
    @SerializedName("listenNum")
    private int listenNum;

    @SerializedName("commentNum")
    private int commentNum;

    @Deprecated
    @SerializedName("followNum")
    private int followNum;

    @SerializedName("songNeedPay")
    private int songNeedPay;

    @Deprecated
    @SerializedName("isListened")
    private int isListened;

    @Deprecated
    @SerializedName("orderNum")
    private int orderNum;

    @Deprecated
    @SerializedName("albumName")
    private String albumName;

    @SerializedName("albumPic")
    private String albumPic;

    @SerializedName("albumId")
    private long albumId;

    @SerializedName("newsFlag")
    private int newsFlag;

    private boolean isSelected;

    public VideoAudioDetails(){}

    protected VideoAudioDetails(Parcel in) {
        id = in.readLong();
        title = in.readString();
        img = in.readString();
        desc = in.readString();
        status = in.readInt();
        validStartDate = in.readLong();
        playUrlId = in.readString();
        source = in.readInt();
        sourceName = in.readString();
        noSubscribe = in.readInt();
        catalogId = in.readInt();
        catalogName = in.readString();
        buyType = in.readInt();
        buyStatus = in.readInt();
        albumIsFine = in.readInt();
        albumIsVip = in.readInt();
        audition = in.readInt();
        duration = in.readInt();
        playInfoList = in.createTypedArrayList(AudioFileInfo.CREATOR);
        fileSize = in.readInt();
        listenNum = in.readInt();
        commentNum = in.readInt();
        followNum = in.readInt();
        songNeedPay = in.readInt();
        isListened = in.readInt();
        orderNum = in.readInt();
        albumName = in.readString();
        albumPic = in.readString();
        albumId = in.readLong();
        newsFlag = in.readInt();
        isSelected = in.readByte() != 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(id);
        dest.writeString(title);
        dest.writeString(img);
        dest.writeString(desc);
        dest.writeInt(status);
        dest.writeLong(validStartDate);
        dest.writeString(playUrlId);
        dest.writeInt(source);
        dest.writeString(sourceName);
        dest.writeInt(noSubscribe);
        dest.writeInt(catalogId);
        dest.writeString(catalogName);
        dest.writeInt(buyType);
        dest.writeInt(buyStatus);
        dest.writeInt(albumIsFine);
        dest.writeInt(albumIsVip);
        dest.writeInt(audition);
        dest.writeInt(duration);
        dest.writeTypedList(playInfoList);
        dest.writeInt(fileSize);
        dest.writeInt(listenNum);
        dest.writeInt(commentNum);
        dest.writeInt(followNum);
        dest.writeInt(songNeedPay);
        dest.writeInt(isListened);
        dest.writeInt(orderNum);
        dest.writeString(albumName);
        dest.writeString(albumPic);
        dest.writeLong(albumId);
        dest.writeInt(newsFlag);
        dest.writeByte((byte) (isSelected ? 1 : 0));
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<VideoAudioDetails> CREATOR = new Creator<VideoAudioDetails>() {
        @Override
        public VideoAudioDetails createFromParcel(Parcel in) {
            return new VideoAudioDetails(in);
        }

        @Override
        public VideoAudioDetails[] newArray(int size) {
            return new VideoAudioDetails[size];
        }
    };

    @Override
    public String toString() {
        return "VideoAudioDetails{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", img='" + img + '\'' +
                ", desc='" + desc + '\'' +
                ", status=" + status +
                ", validStartDate='" + validStartDate + '\'' +
                ", playUrlId='" + playUrlId + '\'' +
                ", source=" + source +
                ", sourceName='" + sourceName + '\'' +
                ", noSubscribe=" + noSubscribe +
                ", catalogId=" + catalogId +
                ", catalogName='" + catalogName + '\'' +
                ", buyType=" + buyType +
                ", buyStatus=" + buyStatus +
                ", albumIsFine=" + albumIsFine +
                ", albumIsVip=" + albumIsVip +
                ", audition=" + audition +
                ", duration=" + duration +
                ", playInfoList=" + playInfoList +
                ", fileSize=" + fileSize +
                ", listenNum=" + listenNum +
                ", commentNum=" + commentNum +
                ", followNum=" + followNum +
                ", songNeedPay=" + songNeedPay +
                ", isListened=" + isListened +
                ", orderNum=" + orderNum +
                ", albumName='" + albumName + '\'' +
                ", albumPic='" + albumPic + '\'' +
                ", albumId=" + albumId +
                ", newsFlag=" + newsFlag +
                ", isSelected=" + isSelected +
                '}';
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public long getValidStartDate() {
        return validStartDate;
    }

    public void setValidStartDate(long validStartDate) {
        this.validStartDate = validStartDate;
    }

    public String getPlayUrlId() {
        return playUrlId;
    }

    public void setPlayUrlId(String playUrlId) {
        this.playUrlId = playUrlId;
    }

    public int getSource() {
        return source;
    }

    public void setSource(int source) {
        this.source = source;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public int getNoSubscribe() {
        return noSubscribe;
    }

    public void setNoSubscribe(int noSubscribe) {
        this.noSubscribe = noSubscribe;
    }

    public int getCatalogId() {
        return catalogId;
    }

    public void setCatalogId(int catalogId) {
        this.catalogId = catalogId;
    }

    public String getCatalogName() {
        return catalogName;
    }

    public void setCatalogName(String catalogName) {
        this.catalogName = catalogName;
    }

    public int getBuyType() {
        return buyType;
    }

    public void setBuyType(int buyType) {
        this.buyType = buyType;
    }

    public int getBuyStatus() {
        return buyStatus;
    }

    public void setBuyStatus(int buyStatus) {
        this.buyStatus = buyStatus;
    }

    public int getAlbumIsFine() {
        return albumIsFine;
    }

    public void setAlbumIsFine(int albumIsFine) {
        this.albumIsFine = albumIsFine;
    }

    public int getAlbumIsVip() {
        return albumIsVip;
    }

    public void setAlbumIsVip(int albumIsVip) {
        this.albumIsVip = albumIsVip;
    }

    public int getAudition() {
        return audition;
    }

    public void setAudition(int audition) {
        this.audition = audition;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public List<AudioFileInfo> getPlayInfoList() {
        return playInfoList;
    }

    public void setPlayInfoList(List<AudioFileInfo> playInfoList) {
        this.playInfoList = playInfoList;
    }

    public int getFileSize() {
        return fileSize;
    }

    public void setFileSize(int fileSize) {
        this.fileSize = fileSize;
    }

    public int getListenNum() {
        return listenNum;
    }

    public void setListenNum(int listenNum) {
        this.listenNum = listenNum;
    }

    public int getCommentNum() {
        return commentNum;
    }

    public void setCommentNum(int commentNum) {
        this.commentNum = commentNum;
    }

    public int getFollowNum() {
        return followNum;
    }

    public void setFollowNum(int followNum) {
        this.followNum = followNum;
    }

    public int getSongNeedPay() {
        return songNeedPay;
    }

    public void setSongNeedPay(int songNeedPay) {
        this.songNeedPay = songNeedPay;
    }

    public int getIsListened() {
        return isListened;
    }

    public void setIsListened(int isListened) {
        this.isListened = isListened;
    }

    public int getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(int orderNum) {
        this.orderNum = orderNum;
    }

    public String getAlbumName() {
        return albumName;
    }

    public void setAlbumName(String albumName) {
        this.albumName = albumName;
    }

    public String getAlbumPic() {
        return albumPic;
    }

    public void setAlbumPic(String albumPic) {
        this.albumPic = albumPic;
    }

    public long getAlbumId() {
        return albumId;
    }

    public void setAlbumId(long albumId) {
        this.albumId = albumId;
    }

    public int getNewsFlag() {
        return newsFlag;
    }

    public void setNewsFlag(int newsFlag) {
        this.newsFlag = newsFlag;
    }

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }
}
