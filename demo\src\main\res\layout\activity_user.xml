<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="10dp">

    <TextView
        android:id="@+id/tv_user_gender"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="6dp"
        android:text="性别：" />

    <RadioGroup
        android:id="@+id/rg_user_gender"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintLeft_toRightOf="@id/tv_user_gender">

        <RadioButton
            android:id="@+id/rb_user_gender_male"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="男" />

        <RadioButton
            android:id="@+id/rb_user_gender_female"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_weight="1"
            android:text="女" />
    </RadioGroup>

    <TextView
        android:id="@+id/tv_user_year_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:text="出生年份："
        app:layout_constraintTop_toBottomOf="@id/tv_user_gender" />

    <NumberPicker
        android:id="@+id/np_user_year"
        android:layout_width="wrap_content"
        android:layout_height="70dp"
        android:calendarViewShown="false"
        android:orientation="horizontal"
        app:layout_constraintLeft_toRightOf="@id/tv_user_year_title"
        app:layout_constraintTop_toTopOf="@id/tv_user_year_title" />

    <Button
        android:id="@+id/et_user_commit_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="提交用户属性"
        app:layout_constraintBottom_toBottomOf="@id/np_user_year"
        app:layout_constraintRight_toRightOf="parent"

        />

    <View
        android:id="@+id/view_user_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="10dp"
        android:background="@color/colorBlack"
        app:layout_constraintTop_toBottomOf="@id/np_user_year" />

    <TextView
        android:id="@+id/tv_user_save_info_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="保存车厂用户信息(需登录状态下才能使用)"
        app:layout_constraintTop_toBottomOf="@id/view_user_divider" />

    <EditText
        android:id="@+id/et_user_input_phone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ems="10"
        android:hint="请输入手机号(必填)"
        android:inputType="phone"
        android:maxLength="11"
        app:layout_constraintTop_toBottomOf="@id/tv_user_save_info_title" />

    <EditText
        android:id="@+id/et_user_input_avatar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="请输入头像url"
        app:layout_constraintTop_toBottomOf="@id/et_user_input_phone" />

    <EditText
        android:id="@+id/et_user_input_nickname"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="请输入昵称"
        app:layout_constraintTop_toBottomOf="@id/et_user_input_avatar" />

    <TextView
        android:id="@+id/tv_user_third_gender_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="10dp"
        android:text="性别："
        app:layout_constraintTop_toBottomOf="@id/et_user_input_nickname" />

    <RadioGroup
        android:id="@+id/rg_user_input_gender"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:orientation="horizontal"
        app:layout_constraintLeft_toRightOf="@id/tv_user_third_gender_title"
        app:layout_constraintTop_toBottomOf="@id/et_user_input_nickname">

        <RadioButton
            android:id="@+id/rb_user_input_male"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="男" />

        <RadioButton
            android:id="@+id/rb_user_input_female"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_weight="1"
            android:text="女" />
    </RadioGroup>

    <EditText
        android:id="@+id/et_user_input_age"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:ems="2"
        android:hint="请输入年龄"
        android:inputType="number"
        android:maxLength="2"
        app:layout_constraintLeft_toRightOf="@id/rg_user_input_gender"
        app:layout_constraintTop_toBottomOf="@id/et_user_input_nickname" />

    <EditText
        android:id="@+id/et_user_input_city"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="请输入城市"
        app:layout_constraintTop_toBottomOf="@id/rg_user_input_gender" />

    <Button
        android:id="@+id/btn_user_commit_third_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="提交车厂用户信息"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/et_user_input_city" />
</androidx.constraintlayout.widget.ConstraintLayout>