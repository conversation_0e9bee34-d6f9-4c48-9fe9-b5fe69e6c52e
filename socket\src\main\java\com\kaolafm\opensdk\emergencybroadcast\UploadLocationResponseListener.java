package com.kaolafm.opensdk.emergencybroadcast;

import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.socket.SocketEvent;
import com.kaolafm.opensdk.socket.SocketListener;

import java.io.Serializable;
import java.lang.reflect.Type;
import java.util.Map;

/**
 * requestId	String	是	位置上报请求唯一id
 * code	String	是	错误码
 * message	String	是	错误信息
 */
public abstract class UploadLocationResponseListener implements SocketListener<String> {

    @Override
    public String getEvent() {
        return SocketEvent.LOCATION_UPDATE_RESPONSE;
    }

    @Override
    public Map<String, Object> getParams(Map<String, Object> params) {
        if (params != null) {
            String lat = (String) params.get("lat");
            String lng = (String) params.get("lng");
            if (!TextUtils.isEmpty(lat)) {
                try {
                    params.put("lat", Double.valueOf(lat));
                } catch (Exception e) {
                    params.put("lat", 0);
                }
            } else {
                params.put("lat", 0);
            }

            if (!TextUtils.isEmpty(lng)) {
                try {
                    params.put("lng", Double.valueOf(lng));
                } catch (Exception e) {
                    params.put("lng", 0);
                }
            } else {
                params.put("lng", 0);
            }
        }
        return params;
    }

    @Override
    public boolean isNeedParams() {
        return true;
    }

    @Override
    public boolean isNeedRequest() {
        return false;
    }

}
