package com.kaolafm.opensdk.demo.personalise;

import android.content.Intent;
import android.os.Bundle;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.OnClick;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.personalise.PersonalizedRequest;
import com.kaolafm.opensdk.api.personalise.model.InterestTag;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.BaseAdapter;
import com.kaolafm.opensdk.demo.BaseHolder;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> @date 2019/4/29
 */
public class InterestActivity extends BaseActivity {

    @BindView(R.id.btn_interest_commit)
    Button mBtnInterestCommit;

    @BindView(R.id.rv_interest_tags)
    RecyclerView mRvInterestTags;

    private InterestAdapter mInterestAdapter;

    private ArrayList<InterestTag> mInterestTags;

    private ArrayList<String> tagList = new ArrayList<>();

    @Override
    public int getLayoutId() {
        return R.layout.activity_interest;
    }

    @Override
    public void initArgs() {
        Intent intent = getIntent();
        if (intent != null) {
            mInterestTags = intent.getParcelableArrayListExtra("interestTags");
        }
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        setTitle("兴趣标签页");
        GridLayoutManager gridLayoutManager = new GridLayoutManager(this, 4);
        mRvInterestTags.setLayoutManager(gridLayoutManager);
        mInterestAdapter = new InterestAdapter();
        mInterestAdapter.setOnItemClickListener((view, viewType, interestTag, position) -> {
            boolean isSelected = view.isSelected();
            view.setSelected(!isSelected);
            String name = interestTag.getName();
            if (!isSelected) {
                tagList.add(name);
            }else {
                tagList.remove(name);
            }
        });
        mRvInterestTags.setAdapter(mInterestAdapter);
    }

    @Override
    public void initData() {
        if (mInterestTags != null) {
            mInterestAdapter.setDataList(mInterestTags);
        }else {
            new PersonalizedRequest().getInterestTagList(new HttpCallback<List<InterestTag>>() {
                @Override
                public void onSuccess(List<InterestTag> interestTags) {
                    mInterestAdapter.setDataList(interestTags);
                }

                @Override
                public void onError(ApiException exception) {
                    showError("获取兴趣标签错误", exception);
                }
            });
        }
    }

    @OnClick(R.id.btn_interest_commit)
    public void onViewClicked() {
        if (!ListUtil.isEmpty(tagList)) {
            new PersonalizedRequest().saveInterestTags(StringUtil.collection2String(tagList, ","),
                    new HttpCallback<Boolean>() {
                        @Override
                        public void onSuccess(Boolean aBoolean) {
                            showToast("保存用户标签"+(aBoolean? "成功":"失败"));
                        }

                        @Override
                        public void onError(ApiException exception) {
                            showError("保存用户标签错误", exception);
                        }
                    });
        }
    }

    static class InterestAdapter extends BaseAdapter<InterestTag> {

        @Override
        protected BaseHolder<InterestTag> getViewHolder(View view, int viewType) {
            return new InterestHolder(view);
        }

        @Override
        protected int getLayoutId(int viewType) {
            return R.layout.item_interest_tag;
        }
    }

    static class InterestHolder extends BaseHolder<InterestTag> {

        @BindView(R.id.tv_interest_tag)
        TextView mTvInterestTag;

        public InterestHolder(View itemView) {
            super(itemView);
        }

        @Override
        public void setupData(InterestTag interestTag, int position) {
            mTvInterestTag.setText(interestTag.getName());
        }
    }
}
