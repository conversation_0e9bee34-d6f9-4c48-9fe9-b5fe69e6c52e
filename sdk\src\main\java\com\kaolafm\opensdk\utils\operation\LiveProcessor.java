package com.kaolafm.opensdk.utils.operation;

import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.operation.model.category.CategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.LiveProgramCategoryMember;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.LiveProgramDetailColumnMember;
import javax.inject.Inject;

/**
 * 直播 分类/栏目成员处理类
 *
 * <AUTHOR>
 * @date 2018/9/25
 */

public class LiveProcessor implements IOperationProcessor {

    @Inject
    public LiveProcessor() {
    }

    @Override
    public boolean accept(CategoryMember member) {
        return member instanceof LiveProgramCategoryMember;
    }

    @Override
    public boolean accept(ColumnMember member) {
        return member instanceof LiveProgramDetailColumnMember;
    }

    @Override
    public long getId(CategoryMember member) {
        return ((LiveProgramCategoryMember) member).getLiveProgramId();
    }

    @Override
    public long getId(ColumnMember member) {
        return ((LiveProgramDetailColumnMember) member).getLiveProgramId();
    }

    @Override
    public long getListenNum(CategoryMember member) {
        return 0;
    }

    @Override
    public int getType(CategoryMember member) {
        return ResType.TYPE_LIVE;
    }

    @Override
    public int getType(ColumnMember member) {
        return ResType.TYPE_LIVE;
    }

    @Override
    public void play(CategoryMember member) {

    }

    @Override
    public void play(ColumnMember member) {

    }
}
