package com.kaolafm.opensdk.api.brandpage;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.brandpage.model.BrandPageListBean;
import com.kaolafm.opensdk.api.operation.model.column.Column;

import java.util.List;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Query;

/**
 * 品牌电台相关
 * <AUTHOR>
 * @date 2023-03-13
 */
public interface BrandPageService {


    /**
     * 品牌主页板块列表
     *
     * @return
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_BRAND_PAGE_LIST)
    Single<BaseResult<BrandPageListBean>> getBrandPageList(@Query("brandPageId") String brandPageId);

    /**
     * 品牌主页板块内容
     *
     * @return
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_BRAND_PAGE_CONTENT)
    Single<BaseResult<List<Column>>> getBrandPageContent(@Query("sectionId") String sectionId
            , @Query("open_uid") String open_uid, @Query("access_token") String access_token);
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_BRAND_PAGE_CONTENT)
    Single<BaseResult<List<Column>>> getBrandPageContent(@Query("sectionId") String sectionId);
}
