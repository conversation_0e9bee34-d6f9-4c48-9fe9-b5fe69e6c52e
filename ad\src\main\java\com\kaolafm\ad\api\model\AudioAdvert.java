package com.kaolafm.ad.api.model;

import android.os.Parcel;

/**
 * 音频广告bean
 * <AUTHOR>
 * @date 2020-01-10
 */
public class AudioAdvert extends BaseAdvert {

    public AudioAdvert() {
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        super.writeToParcel(dest, flags);
    }

    protected AudioAdvert(Parcel in) {
        super(in);
    }

    public static final Creator<AudioAdvert> CREATOR = new Creator<AudioAdvert>() {
        @Override
        public AudioAdvert createFromParcel(Parcel in) {
            return new AudioAdvert(in);
        }

        @Override
        public AudioAdvert[] newArray(int size) {
            return new AudioAdvert[size];
        }
    };

}
