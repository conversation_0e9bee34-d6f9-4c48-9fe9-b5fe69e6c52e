package com.kaolafm.opensdk.player.core.ijk;

public class <PERSON><PERSON>allB<PERSON> implements IJKVideoCallBack{

    @Override
    public void onClickExit(Object... objects) {

    }

    @Override
    public void onClickReplay(Object... objects) {

    }

    @Override
    public void onClickPlayNext(Object... objects) {

    }

    @Override
    public void onClickSpeedPlay(Object... objects) {

    }

    @Override
    public void onClickVoicePlay(Object... objects) {

    }
}
