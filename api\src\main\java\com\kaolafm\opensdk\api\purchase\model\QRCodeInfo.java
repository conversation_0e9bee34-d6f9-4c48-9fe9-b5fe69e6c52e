package com.kaolafm.opensdk.api.purchase.model;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;

/**
 * 支付二维码相关信息
 */
@Entity
public class QRCodeInfo {

    private Integer status;

    /* 二维码id */
    private String qrCodeId;

    /* 二维码地址 */
    private String qrCodeImg;

    /* 失效时长，单位秒 */
    private Long expireTime;

    /* 购买状态 0-未支付，1-已支付，2-过期 */
    private Integer payStatus;

    @Generated(hash = 472966468)
    public QRCodeInfo(Integer status, String qrCodeId, String qrCodeImg, Long expireTime,
            Integer payStatus) {
        this.status = status;
        this.qrCodeId = qrCodeId;
        this.qrCodeImg = qrCodeImg;
        this.expireTime = expireTime;
        this.payStatus = payStatus;
    }

    public QRCodeInfo() {
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getQrCodeId() {
        return qrCodeId;
    }

    public void setQrCodeId(String qrCodeId) {
        this.qrCodeId = qrCodeId;
    }

    public String getQrCodeImg() {
        return qrCodeImg;
    }

    public void setQrCodeImg(String qrCodeImg) {
        this.qrCodeImg = qrCodeImg;
    }

    public Long getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Long expireTime) {
        this.expireTime = expireTime;
    }

    public Integer getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Integer payStatus) {
        this.payStatus = payStatus;
    }

    @Override
    public String toString() {
        return "QRCodeInfo{" +
                "status=" + status +
                ", qrCodeId='" + qrCodeId + '\'' +
                ", qrCodeImg='" + qrCodeImg + '\'' +
                ", expireTime=" + expireTime +
                '}';
    }
}
