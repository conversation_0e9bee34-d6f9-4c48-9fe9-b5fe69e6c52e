<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >
    <TextView
        android:id="@+id/tv_function_name"
        android:layout_width="match_parent"
        android:layout_height="43dp"
        android:padding="10dp"
        android:paddingStart="30dp"
        android:textColor="@android:color/black"
        android:textSize="16sp" />
    <ImageView
        android:id="@+id/iv_function_ic"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_right_arrow"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:paddingEnd="10dp"
        />
</androidx.constraintlayout.widget.ConstraintLayout>