package com.kaolafm.ad.util;

import android.text.TextUtils;

import com.kaolafm.ad.AdConstant;
import com.kaolafm.ad.api.model.AdvertisingDetails;
import com.kaolafm.base.utils.FileUtil;
import com.kaolafm.base.utils.SpUtil;
import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.http.download.DownloadListener;
import com.kaolafm.opensdk.http.download.DownloadManager;
import com.kaolafm.opensdk.http.download.DownloadProgress;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.log.Logging;

import java.io.File;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2020-02-27
 */
public class DownloadUtil {

    private static final String RESOURCES_PATH = FileUtil.getDiskFilesDir(ComponentKit.getInstance().getApplication(),  "/ad/");

    private static final String IMAGE_PATH = "/img/";

    private static final String AUDIO_PATH = "/audio/";

    private DownloadUtil() {
    }

    public static void download(AdvertisingDetails details) {
        String sessionId = details.getSessionId();
        String uniquePath = RESOURCES_PATH + sessionId;
        // TODO: 2020-02-26 下载这里还需要优化。
        DownloadManager downloadManager = DownloadManager.getInstance();
        String audioUrl = details.getAudioUrl();
        downloadManager.download(audioUrl, null, uniquePath + AUDIO_PATH, new DownloadListenerWrapper(sessionId, audioUrl));

        String imageUrl = details.getImageUrl();
        downloadManager.download(imageUrl, null, uniquePath + IMAGE_PATH, new DownloadListenerWrapper(sessionId, imageUrl));

        String attachImageUrl = details.getAttachImageUrl();
        downloadManager.download(attachImageUrl, null, uniquePath + IMAGE_PATH, new DownloadListenerWrapper(sessionId, attachImageUrl));

        String interactionIcon = details.getMoreInteractionIcon();
        downloadManager.download(interactionIcon, null, uniquePath + IMAGE_PATH, new DownloadListenerWrapper(sessionId, interactionIcon));

        String moreInteractionImage = details.getMoreInteractionImage();
        downloadManager.download(moreInteractionImage, null, uniquePath + IMAGE_PATH, new DownloadListenerWrapper(sessionId, moreInteractionImage));
    }

    public static String uniqueKey(String sessionId, String url) {
        if (!TextUtils.isEmpty(url)) {
            return String.valueOf(Arrays.asList(sessionId, url).hashCode());
        }else {
            return null;
        }
    }
    public static void delete(AdvertisingDetails details) {
        delete(details.getSessionId(), details.getImageUrl(), details.getAudioUrl(), details.getMoreInteractionIcon(), details.getMoreInteractionImage(), details.getAttachImageUrl());
        FileUtil.deleteFile(RESOURCES_PATH + details.getSessionId());
    }
    private static void delete(String sessionId, String... urls) {
        String[] keys = new String[urls.length];
        for (int i = 0; i < urls.length; i++) {
            keys[i] = uniqueKey(sessionId, urls[i]);
        }
        SpUtil.removeAnyByName(AdConstant.SP_NAME, keys);
    }

    public static void deleteSPAndFile(String sessionId, String url, String localPath) {
        if (!TextUtils.isEmpty(url)) {
            SpUtil.remove(AdConstant.SP_NAME, uniqueKey(sessionId, url));
            FileUtil.deleteFile(localPath);
        }
    }

    public static String getLocalPath(String sessionId, String path) {
        return SpUtil.getString(AdConstant.SP_NAME, uniqueKey(sessionId, path), null);
    }

    private static class DownloadListenerWrapper implements DownloadListener {

        private String key;

        public DownloadListenerWrapper(String sessionId, String url) {
            key = uniqueKey(sessionId, url);
        }

        @Override
        public void onStart() {

        }

        @Override
        public void onProgress(DownloadProgress progress) {

        }

        @Override
        public void onSuccess(File file) {
            Logging.d("下载成功，%s=%s", key, file.getPath());
            SpUtil.putString(AdConstant.SP_NAME, key, file.getPath());
        }

        @Override
        public void onError(ApiException exception) {
            Logging.e("下载失败，exception="+exception);
        }
    }
}
