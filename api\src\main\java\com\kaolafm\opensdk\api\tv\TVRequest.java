package com.kaolafm.opensdk.api.tv;

import androidx.annotation.Nullable;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.tv.model.TVDetails;
import com.kaolafm.opensdk.api.tv.model.TVProgramDetails;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.List;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: TVRequest.java
 *                                                                  *
 * Created in 2018/8/13 下午4:06                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class TVRequest extends BaseRequest {


    private TVService mService;

    public TVRequest() {
        mService = obtainRetrofitService(TVService.class);
    }

    /**
     * 获取电视电台详情
     *
     * @param tvId 在线电视ID
     * @param callback    获取数据回调
     */
    public void getTVDetails(long tvId, HttpCallback<TVDetails> callback) {
        doHttpDeal(mService.getTVDetails(tvId), BaseResult::getResult, callback);
    }


    /**
     * 获取电视电台节目单列表
     *
     * @param tvId 在线电视ID
     * @param date        日期
     * @param callback    获取数据回调
     */
    public void getTVProgramList(long tvId, @Nullable String date, HttpCallback<List<TVProgramDetails>> callback) {
        doHttpDeal(mService.getTVProgramList(tvId, date), BaseResult::getResult, callback);
    }

    /**
     * 获取电视节目详情
     *
     * @param programId 电视节目ID
     * @param callback  获取数据回调
     */
    public void getTVProgramDetails(long programId, HttpCallback<TVProgramDetails> callback) {
        doHttpDeal(mService.getTVProgramDetails(programId), BaseResult::getResult, callback);

    }

    /**
     * 获取电视列表
     *
     * @param callback  获取数据回调
     */
    public void getTVNeighborList(HttpCallback<List<TVDetails>> callback) {
        doHttpDeal(mService.getTVNeighborList(), BaseResult::getResult, callback);

    }

    /**
     * 获取电视电台当前节目对象
     *
     * @param tvId 在线电视电台ID
     * @param callback    获取数据回调
     */
    public void getTVCurrentProgramDetails(long tvId, HttpCallback<TVProgramDetails> callback) {
        doHttpDeal(mService.getTVCurrentProgramDetails(tvId), BaseResult::getResult, callback);
    }
//
//    /**
//     * 根据电视id返回地方台或国家台电视列表
//     *
//     * @param tvId 在线电视电台ID
//     * @param pagenum
//     * @param pagesize
//     * @param callback    获取数据回调
//     */
//    public void getTVNeighborList(long tvId,int pagenum,int pagesize, HttpCallback<BasePageResult<List<TVDetails>>> callback) {
//        doHttpDeal(mService.getTVNeighborList(tvId,pagenum,pagesize), BaseResult::getResult, callback);
//    }
}
