package com.kaolafm.opensdk.utils.operation;

import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.operation.model.category.CategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.RadioCategoryMember;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.RadioDetailColumnMember;
import javax.inject.Inject;

/**
 * 电台 分类/栏目成员处理类
 *
 * <AUTHOR>
 * @date 2018/9/25
 */
public class RadioProcessor implements IOperationProcessor {

    @Inject
    public RadioProcessor() {
    }

    @Override
    public boolean accept(CategoryMember member) {
        return member instanceof RadioCategoryMember;
    }

    @Override
    public boolean accept(ColumnMember member) {
        return member instanceof RadioDetailColumnMember;
    }

    @Override
    public long getId(CategoryMember member) {
        return ((RadioCategoryMember) member).getRadioId();
    }

    @Override
    public long getId(ColumnMember member) {
        return ((RadioDetailColumnMember) member).getRadioId();
    }

    @Override
    public long getListenNum(CategoryMember member) {
        return ((RadioCategoryMember) member).getPlayTimes();
    }

    @Override
    public int getType(CategoryMember member) {
        return ResType.TYPE_RADIO;
    }

    @Override
    public int getType(ColumnMember member) {
        return ResType.TYPE_RADIO;
    }

    @Override
    public void play(CategoryMember member) {

    }

    @Override
    public void play(ColumnMember member) {

    }
}
