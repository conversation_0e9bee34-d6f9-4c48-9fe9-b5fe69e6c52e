package com.kaolafm.opensdk.live;

import android.util.Log;

import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.socket.SocketEvent;
import com.kaolafm.opensdk.socket.SocketListener;

import java.util.Map;

/**
 * 直播 - 长连接事件 - 退出聊天室 - 客户端推送事件
 */
public class LiveUserExitChatroomListener implements SocketListener<Object> {

    private static final String TAG = "LiveUserExitChatroom";

    private LiveUserExitChatroomListener() {
    }

    public static LiveUserExitChatroomListener INSTANCE = new LiveUserExitChatroomListener();

    @Override
    public String getEvent() {
        return SocketEvent.LIVE_EXIT_CHATROOM;
    }

    @Override
    public Map<String, Object> getParams(Map<String, Object> params) {
        return params;
    }

    @Override
    public boolean isNeedParams() {
        return true;
    }

    @Override
    public boolean isNeedRequest() {
        return true;
    }

    @Override
    public void onSuccess(Object o) {
        Log.e(TAG, "---live   LiveUserExitChatroomListener onSuccess Object=" + o);
    }

    @Override
    public void onError(ApiException exception) {
        Log.e(TAG, "---live   LiveUserExitChatroomListener onError exception=" + exception);
    }
}
