package com.kaolafm.opensdk.db.helper;

import com.kaolafm.base.utils.StringUtil;

import org.greenrobot.greendao.converter.PropertyConverter;

import java.util.Arrays;
import java.util.List;

/**
 * greendao数据中用于存储String集合的转换类。
 * <AUTHOR>
 * @date 2020-02-05
 */
public class DaoStringListConverter implements PropertyConverter<List<String>, String> {

    @Override
    public List<String> convertToEntityProperty(String databaseValue) {
        String[] strings = databaseValue.split(",");
        return Arrays.asList(strings);
    }

    @Override
    public String convertToDatabaseValue(List<String> entityProperty) {
        return StringUtil.collection2String(entityProperty, ",");
    }
}
