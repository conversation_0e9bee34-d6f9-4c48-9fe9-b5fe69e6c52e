package com.kaolafm.report.api.report;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import com.kaolafm.base.utils.MD5;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.report.util.ReportConstants;

import org.json.JSONObject;

import java.util.Iterator;
import java.util.SortedMap;
import java.util.TreeMap;

public class ReportBigDataUtils {

    private static final String KEY = "X6p5T5F2lQD2vj5Ci4g139SnedB7DuVvmkrRYVqSWtlhncOV7RBQrItsoxaN3KXpJrRYVqSWtlhncOV7RBQ";

    /**
     * 对每条数据进行格式转化
     */
    public static String getData(JSONObject o){
        // TODO 1.字典排序
        JSONObject jsonObject = getSortJson(o);
        // TODO 2.转化为get参数形式字符串string
        String getStr = jsonToGet(jsonObject.toString());
        // TODO 3.在string后面加上私钥
        String keyStr = getStr+"&="+KEY;
        // TODO 4.对NewString进行md5加密
        String md5Str = MD5.getMD5Str(keyStr);
        // TODO 5.把md5加密之后的所以字符串转为大写，赋值给字符串sign
        String upperCaseStr = md5Str.toUpperCase();
        // TODO 6.把sign作为一个参数提交
        String signStr = getStr + "&sign="+upperCaseStr;
        return signStr;
    }

    /**
     * 对单层json进行key字母排序
     * @param json
     * @return
     */
    public static JSONObject getSortJson(JSONObject json){
        Iterator iteratorKeys = json.keys();
        SortedMap map = new TreeMap();
        while (iteratorKeys.hasNext()) {
            String key = iteratorKeys.next().toString();
            String vlaue = json.optString(key);
            map.put(key, vlaue);
        }
        JSONObject json2 = new JSONObject(map);
        return json2;
    }

    /**
     * json格式转Get
     * @param jsonStr
     * @return
     */
    private static String jsonToGet(String jsonStr){
        return jsonStr.replace("{", "").replace("}", "").
                replace("\"", "").replace("'", "").
                replace(":", "=").replace(",", "&");
    }
}
