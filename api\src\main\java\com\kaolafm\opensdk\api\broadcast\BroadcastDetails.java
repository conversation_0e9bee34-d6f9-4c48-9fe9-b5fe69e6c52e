package com.kaolafm.opensdk.api.broadcast;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;
import com.kaolafm.opensdk.api.media.model.AudioFileInfo;

import java.util.List;

public class BroadcastDetails implements Parcelable {

    public static final int COPYRIGHT_FULL = 0; //版权全版
    public static final int COPYRIGHT_LITE = 1;//版权简版

    /**
     * broadcastId : 1600000000198
     * name : 上海第一财经广播
     * img : http://img.kaolafm.net/mz/images/201612/a03deda5-2a06-4a43-a81c-23c8ecedce64/default.jpg
     * classifyName : 省市台
     * isSubscribe : 0
     * playUrl : http://play.x.l.kaolafm.net/live/1600000000198/index.m3u8
     * onLineNum : 0
     * likedNum : 354
     * status : 1
     * classifyId : 2
     * roomId : 48109
     * freq : FM97.7
     * icon : http://img.kaolafm.net/icon/qingting.fm.png
     */

    /**
     * 广播id
     */
    @SerializedName("broadcastId")
    private long broadcastId;

    /**
     * 广播名称
     */
    @SerializedName("name")
    private String name;

    /**
     * 广播封面URL
     */
    @SerializedName("img")
    private String img;

    /**
     * 广播类型名称
     */
    @SerializedName("classifyName")
    private String classifyName;

    /**
     * 是否订阅,1=是，0=否
     */
    @SerializedName("isSubscribe")
    private int isSubscribe;
    /**
     * 是否可以订阅 只有为1的时候不能订阅
     */
    @SerializedName("noSubscribe")
    private int noSubscribe;

    /**
     * 直播流地址
     */
    @SerializedName("playUrl")
    private String playUrl;

    @SerializedName("playInfoList")
    private List<AudioFileInfo> playInfoList;

    /**
     * 在线收听数
     */
    @SerializedName("onLineNum")
    private int onLineNum;

    /**
     * 赞数
     */
    @SerializedName("likedNum")
    private int likedNum;

    /**
     * 上下线状态,1=上线0=下线
     */
    @SerializedName("status")
    private int status;

    /**
     * 广播类型id
     */
    @SerializedName("classifyId")
    private int classifyId;

    /**
     * 直播间id
     */
    @SerializedName("roomId")
    private int roomId;

    /**
     * 广播频道
     */
    @SerializedName("freq")
    private String freq;

    /**
     * 用来区分广播类型（音乐，交通，新闻等）
     */
    @SerializedName("type")
    private int type;

    @SerializedName("areaCode")
    private int areaCode;
    /**
     * 广播图标
     */
    @SerializedName("icon")
    private String icon;

    /**
     * 地区名称
     */
    @SerializedName("provinceName")
    private String provinceName;

    /**
     * 地区编号
     */
    @SerializedName("provinceId")
    private String provinceId;

    /**
     * 广播回放的状态
     * 0-节目单隐藏
     * 1-节目单显示，回放能播
     * 2-节目单显示，回放不能播
     * 如果为1，节目开播之后没有回放地址，为转码中状态
     */
    @SerializedName("programEnable")
    private int programEnable;

    /**
     * 是否简版（是否已有授权） 0-全版 -1简版
     */
    @SerializedName("isLite")
    private int isLite = COPYRIGHT_FULL;

    /**
     * 当前应播放的广播频道
     * 当请求国家台详情且获取到了经纬度，该值为定位所在地的广播信息
     * 当请求国家台详情且没有获取到经纬度，该值为国家台的广播信息
     * 当请求地方台的详情时，该值为该地方台的广播信息
     */
    @SerializedName("currentAreaPlayInfo")
    private BroadcastAreaInfo currentAreaInfo;

    /**
     * 当前应播放的广播详情
     * currentAreaInfo对应地区的广播详情，
     * 有且只有currentAreaInfo的id与外层BroadcastDetail的id一致时，该值为null
     */
    @SerializedName("currentAreaBroadDetail")
    private BroadcastDetails currentAreaBroadcastDetails;

    /**
     * 当前广播包含的所有地区频道
     * 某些广播会覆盖多个省份，在不同的省份会有不同的频率和内容，该字段为各个省份的频率和播放地址列表
     */
    @SerializedName("broadcastMultiAreaList")
    private List<BroadcastAreaInfo> broadcastMultiAreaList;


    public int getProgramEnable() {
        return programEnable;
    }

    public void setProgramEnable(int programEnable) {
        this.programEnable = programEnable;
    }

    public long getBroadcastId() {
        return broadcastId;
    }

    public void setBroadcastId(long broadcastId) {
        this.broadcastId = broadcastId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getClassifyName() {
        return classifyName;
    }

    public void setClassifyName(String classifyName) {
        this.classifyName = classifyName;
    }

    public int getIsSubscribe() {
        return isSubscribe;
    }

    public void setIsSubscribe(int isSubscribe) {
        this.isSubscribe = isSubscribe;
    }

    public String getPlayUrl() {
        return playUrl;
    }

    public void setPlayUrl(String playUrl) {
        this.playUrl = playUrl;
    }

    public int getOnLineNum() {
        return onLineNum;
    }

    public void setOnLineNum(int onLineNum) {
        this.onLineNum = onLineNum;
    }

    public int getLikedNum() {
        return likedNum;
    }

    public void setLikedNum(int likedNum) {
        this.likedNum = likedNum;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getNoSubscribe() {
        return noSubscribe;
    }

    public void setNoSubscribe(int noSubscribe) {
        this.noSubscribe = noSubscribe;
    }

    public int getClassifyId() {
        return classifyId;
    }

    public void setClassifyId(int classifyId) {
        this.classifyId = classifyId;
    }

    public int getRoomId() {
        return roomId;
    }

    public void setRoomId(int roomId) {
        this.roomId = roomId;
    }

    public String getFreq() {
        return freq;
    }

    public void setFreq(String freq) {
        this.freq = freq;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public List<AudioFileInfo> getPlayInfoList() {
        return playInfoList;
    }

    public void setPlayInfoList(List<AudioFileInfo> playInfoList) {
        this.playInfoList = playInfoList;
    }

    public int getIsLite() {
        return isLite;
    }

    public void setIsLite(int isLite) {
        this.isLite = isLite;
    }

    public BroadcastAreaInfo getCurrentAreaInfo() {
        return currentAreaInfo;
    }

    public void setCurrentAreaInfo(BroadcastAreaInfo currentAreaInfo) {
        this.currentAreaInfo = currentAreaInfo;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(String provinceId) {
        this.provinceId = provinceId;
    }

    public List<BroadcastAreaInfo> getBroadcastMultiAreaList() {
        return broadcastMultiAreaList;
    }

    public void setBroadcastMultiAreaList(List<BroadcastAreaInfo> broadcastMultiAreaList) {
        this.broadcastMultiAreaList = broadcastMultiAreaList;
    }

    public BroadcastDetails getCurrentAreaBroadcastDetails() {
        return currentAreaBroadcastDetails;
    }

    public void setCurrentAreaBroadcastDetails(BroadcastDetails currentAreaBroadcastDetails) {
        this.currentAreaBroadcastDetails = currentAreaBroadcastDetails;
    }

    public int getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(int areaCode) {
        this.areaCode = areaCode;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(this.broadcastId);
        dest.writeString(this.name);
        dest.writeString(this.img);
        dest.writeString(this.classifyName);
        dest.writeInt(this.isSubscribe);
        dest.writeString(this.playUrl);
        dest.writeInt(this.onLineNum);
        dest.writeInt(this.likedNum);
        dest.writeInt(this.status);
        dest.writeInt(this.classifyId);
        dest.writeInt(this.roomId);
        dest.writeString(this.freq);
        dest.writeInt(this.type);
        dest.writeInt(this.areaCode);
        dest.writeString(this.icon);
        dest.writeString(this.provinceName);
        dest.writeString(this.provinceId);
        dest.writeTypedList(this.playInfoList);
        dest.writeInt(this.programEnable);
        dest.writeInt(this.isLite);
        dest.writeParcelable(this.currentAreaInfo, 0);
        dest.writeParcelable(this.currentAreaBroadcastDetails, 0);
        dest.writeTypedList(this.broadcastMultiAreaList);
        dest.writeInt(this.noSubscribe);
    }

    public BroadcastDetails() {
    }

    protected BroadcastDetails(Parcel in) {
        this.broadcastId = in.readLong();
        this.name = in.readString();
        this.img = in.readString();
        this.classifyName = in.readString();
        this.isSubscribe = in.readInt();
        this.playUrl = in.readString();
        this.onLineNum = in.readInt();
        this.likedNum = in.readInt();
        this.status = in.readInt();
        this.classifyId = in.readInt();
        this.roomId = in.readInt();
        this.freq = in.readString();
        this.type = in.readInt();
        this.icon = in.readString();
        this.provinceName = in.readString();
        this.provinceId = in.readString();
        this.playInfoList = in.createTypedArrayList(AudioFileInfo.CREATOR);
        this.programEnable = in.readInt();
        this.isLite = in.readInt();
        this.noSubscribe = in.readInt();
        this.currentAreaInfo = in.readParcelable(BroadcastAreaInfo.class.getClassLoader());
        this.currentAreaBroadcastDetails = in.readParcelable(BroadcastDetails.class.getClassLoader());
        in.readTypedList(this.broadcastMultiAreaList, BroadcastAreaInfo.CREATOR);
    }

    public static final Creator<BroadcastDetails> CREATOR = new Creator<BroadcastDetails>() {
        @Override
        public BroadcastDetails createFromParcel(Parcel source) {
            return new BroadcastDetails(source);
        }

        @Override
        public BroadcastDetails[] newArray(int size) {
            return new BroadcastDetails[size];
        }
    };

    @Override
    public String toString() {
        return "BroadcastDetails{" +
                "broadcastId=" + broadcastId +
                ", name='" + name + '\'' +
                ", img='" + img + '\'' +
                ", classifyName='" + classifyName + '\'' +
                ", isSubscribe=" + isSubscribe +
                ", playUrl='" + playUrl + '\'' +
                ", playInfoList=" + playInfoList +
                ", onLineNum=" + onLineNum +
                ", likedNum=" + likedNum +
                ", status=" + status +
                ", classifyId=" + classifyId +
                ", roomId=" + roomId +
                ", freq='" + freq + '\'' +
                ", type=" + type +
                ", icon='" + icon + '\'' +
                ", provinceName='" + provinceName + '\'' +
                ", provinceId='" + provinceId + '\'' +
                ", programEnable=" + programEnable +
                ", isLite=" + isLite +
                ", noSubscribe=" + noSubscribe +
                ", currentAreaInfo=" + currentAreaInfo +
                ", currentAreaBroadcastDetails=" + currentAreaBroadcastDetails +
                ", broadcastMultiAreaList=" + broadcastMultiAreaList +
                '}';
    }
}