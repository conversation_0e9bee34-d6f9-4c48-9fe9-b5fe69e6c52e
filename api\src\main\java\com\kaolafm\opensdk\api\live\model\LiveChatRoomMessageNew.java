package com.kaolafm.opensdk.api.live.model;

import java.io.Serializable;

public class LiveChatRoomMessageNew implements Serializable {

    /**
     * 用户发送文本消息
     */
    public static final String MSG_TYPE_TEXT = "1";
    /**
     * 用户发送语音消息
     */
    public static final String MSG_TYPE_AUDIO = "2";
    /**
     * 系统通知
     * 其他用户进入退出聊天室通知
     * subType : 1-进入聊天室通知。2-离开聊天室通知
     */
    public static final String MSG_TYPE_NOTIFICATION = "3";
    /**
     * 自定义消息 - 排行榜更新消息
     */
    public static final String MSG_TYPE_LIVE_RANK = "4";
    /**
     * 自定义消息 - 打赏消息
     */
    public static final String MSG_TYPE_LIVE_GIFT = "5";
    /**
     * 自定义消息 - 商品消息
     */
    public static final String MSG_TYPE_LIVE_GOODS_CARD = "6";
    /**
     * 自定义消息 - 直播间状态变更 - Integer liveStatus 直播间状态 1-开播，2-下播
     */
    public static final String MSG_TYPE_LIVE_LIVE_STATUS_CHANGED = "7";
    /**
     * 自定义消息 - 评论消息状态 - Integer status 评论状态：1-通过，2-暂停
     */
    public static final String MSG_TYPE_LIVE_COMMENT_STATUS_CHANGED = "8";

    /**
     * 消息类型
     * <p>
     * {@link #MSG_TYPE_TEXT} - 用户发送文本消息
     * msgInfo -> {@link com.kaolafm.opensdk.api.yunxin.model.LiveTextMsg}
     * <p>
     * {@link #MSG_TYPE_AUDIO} -用户发送语音消息
     * msgInfo -> 与 [1-用户发送文本消息] 相同
     * <p>
     * {@link #MSG_TYPE_NOTIFICATION} -系统通知 -其他用户进入退出聊天室通知
     * msgInfo -> {@link com.kaolafm.opensdk.api.yunxin.model.LiveOtherUserEnterExitMsg}
     * <p>
     * {@link #MSG_TYPE_LIVE_RANK} -自定义消息 - 排行榜更新消息
     * msgInfo -> {@link com.kaolafm.opensdk.api.yunxin.model.GiftRankMsg}
     * <p>
     * {@link #MSG_TYPE_LIVE_GIFT} -自定义消息 - 打赏消息
     * msgInfo -> {@link com.kaolafm.opensdk.api.yunxin.model.GiftMsg}
     * <p>
     * {@link #MSG_TYPE_LIVE_GOODS_CARD} -自定义消息 - 商品消息
     * msgInfo -> {@link com.kaolafm.opensdk.api.yunxin.model.GoodsCardMsg}
     * <p>
     * {@link #MSG_TYPE_LIVE_LIVE_STATUS_CHANGED} -自定义消息 - 直播间状态变更
     * msgInfo -> {@link com.kaolafm.opensdk.api.yunxin.model.LiveStatusMsg}
     * <p>
     * {@link #MSG_TYPE_LIVE_COMMENT_STATUS_CHANGED} -评论消息状态 - 评论状态变更
     * msgInfo -> {@link com.kaolafm.opensdk.api.yunxin.model.LiveCommentStatusMsg}
     */
    private String type;
    /**
     * 消息实体
     */
    private Object msgInfo;

    public String getType() {
        return type == null ? "" : type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Object getMsgInfo() {
        return msgInfo;
    }

    public void setMsgInfo(Object msgInfo) {
        this.msgInfo = msgInfo;
    }

    @Override
    public String toString() {
        return "LiveChatRoomMessageNew{" +
                "type='" + type + '\'' +
                ", msgInfo=" + msgInfo +
                '}';
    }
}
