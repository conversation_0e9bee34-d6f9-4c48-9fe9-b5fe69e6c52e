package com.kaolafm.opensdk.api.operation.model.column;

/**
 * 栏目成员：直播
 */
public class LiveProgramDetailColumnMember extends ColumnContent {

    /** 直播间id*/
    private long liveProgramId;

    /** 主播 */
    private String anchor;

    public long getLiveProgramId() {
        return liveProgramId;
    }

    public void setLiveProgramId(long liveProgramId) {
        this.liveProgramId = liveProgramId;
    }

    public String getAnchor() {
        return anchor;
    }

    public void setAnchor(String anchor) {
        this.anchor = anchor;
    }

    @Override
    public String toString() {
        return "LiveProgramDetailColumnMember{" +
                "liveProgramId=" + liveProgramId +
                "anchor=" + anchor +
                "} " + super.toString();
    }
}
