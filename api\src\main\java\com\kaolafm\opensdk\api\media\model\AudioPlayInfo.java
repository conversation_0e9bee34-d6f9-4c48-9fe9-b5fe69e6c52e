package com.kaolafm.opensdk.api.media.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

/**
 * 单曲播放信息
 */
public class AudioPlayInfo implements Parcelable {

    /** 当前时间 单位秒数 */
    @SerializedName("currentTime")
    private long currentTime;

    /** 该秒数之后失效 单位秒数 */
    @SerializedName("distanceTime")
    private long distanceTime;

    /** 播放信息 */
    @SerializedName("playInfoList")
    private List<AudioFileInfo> playInfoList;

    /** 时长 */
    @SerializedName("duration")
    private Integer duration;

    public long getCurrentTime() {
        return currentTime;
    }

    public void setCurrentTime(long currentTime) {
        this.currentTime = currentTime;
    }

    public long getDistanceTime() {
        return distanceTime;
    }

    public void setDistanceTime(long distanceTime) {
        this.distanceTime = distanceTime;
    }

    public List<AudioFileInfo> getPlayInfoList() {
        return playInfoList;
    }

    public void setPlayInfoList(List<AudioFileInfo> playInfoList) {
        this.playInfoList = playInfoList;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    @Override
    public String toString() {
        return "AudioPlayInfo{" +
                "currentTime=" + currentTime +
                ", distanceTime=" + distanceTime +
                ", playInfoList=" + playInfoList +
                ", duration=" + duration +
                '}';
    }

    protected AudioPlayInfo(Parcel in) {
        this.currentTime = in.readLong();
        this.distanceTime = in.readLong();
        this.playInfoList = new ArrayList<>();
        in.readTypedList(this.playInfoList, AudioFileInfo.CREATOR);
        this.duration = in. readInt();
    }

    public static final Creator<AudioPlayInfo> CREATOR = new Creator<AudioPlayInfo>() {
        @Override
        public AudioPlayInfo createFromParcel(Parcel in) {
            return new AudioPlayInfo(in);
        }

        @Override
        public AudioPlayInfo[] newArray(int size) {
            return new AudioPlayInfo[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(this.currentTime);
        dest.writeLong(this.distanceTime);
        dest.writeTypedList(this.playInfoList);
        dest.writeInt(this.duration);
    }
}
