package com.kaolafm.opensdk.player.logic.factory;

import android.util.SparseArray;

import com.kaolafm.opensdk.player.logic.listener.IPlayListControl;
import com.kaolafm.opensdk.player.logic.playlist.AlbumPlayListControl;
import com.kaolafm.opensdk.player.logic.playlist.BroadcastPlayListControl;
import com.kaolafm.opensdk.player.logic.playlist.LivePlayListControl;
import com.kaolafm.opensdk.player.logic.playlist.OneKeyListenPlayListControl;
import com.kaolafm.opensdk.player.logic.playlist.PurchaseOneKeyListenPlayListControl;
import com.kaolafm.opensdk.player.logic.playlist.RadioPlayListControl;
import com.kaolafm.opensdk.player.logic.playlist.TVPlayListControl;
import com.kaolafm.opensdk.player.logic.playlist.FeaturePlayListControl;
import com.kaolafm.opensdk.player.logic.playlist.VideoAlbumPlayListControl;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

public class PlayListControlFactory {

    private static IPlayListControl mCurrentPlaylist;

    private static SparseArray<IPlayListControl> PLAY_LIST_CONTROL_ARRAY = new SparseArray<>();

    static {
        PLAY_LIST_CONTROL_ARRAY.put(PlayerConstants.RESOURCES_TYPE_ALBUM, new AlbumPlayListControl());
        PLAY_LIST_CONTROL_ARRAY.put(PlayerConstants.RESOURCES_TYPE_AUDIO, new AlbumPlayListControl());
        PLAY_LIST_CONTROL_ARRAY.put(PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM, new VideoAlbumPlayListControl());
        PLAY_LIST_CONTROL_ARRAY.put(PlayerConstants.RESOURCES_TYPE_VIDEO_AUDIO, new VideoAlbumPlayListControl());
        PLAY_LIST_CONTROL_ARRAY.put(PlayerConstants.RESOURCES_TYPE_BROADCAST, new BroadcastPlayListControl());
        PLAY_LIST_CONTROL_ARRAY.put(PlayerConstants.RESOURCES_TYPE_TV, new TVPlayListControl());
        PLAY_LIST_CONTROL_ARRAY.put(PlayerConstants.RESOURCES_TYPE_RADIO, new RadioPlayListControl());
        PLAY_LIST_CONTROL_ARRAY.put(PlayerConstants.RESOURCES_TYPE_LIVING, new LivePlayListControl());
        PLAY_LIST_CONTROL_ARRAY.put(PlayerConstants.RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE, new OneKeyListenPlayListControl());
        PLAY_LIST_CONTROL_ARRAY.put(PlayerConstants.RESOURCES_TYPE_PURCHASE_ONE_KEY_LISTENER_TYPE, new PurchaseOneKeyListenPlayListControl());
        PLAY_LIST_CONTROL_ARRAY.put(PlayerConstants.RESOURCES_TYPE_FEATURE, new FeaturePlayListControl());
    }

    private PlayListControlFactory() {
    }

    public static IPlayListControl getPlayListControl(int type) {
        IPlayListControl iPlayListControl = PLAY_LIST_CONTROL_ARRAY.get(type);
        if (iPlayListControl == null) {
            iPlayListControl = new AlbumPlayListControl();
        }
        return iPlayListControl;
    }
}
