package com.kaolafm.opensdk.api.brandpage.model;

import com.kaolafm.opensdk.api.operation.model.ImageFile;

import java.util.Map;

/**
 * 品牌主页板块列表item
 */
public class BrandSectionsBean {
    private long sectionId;//板块id
    private String name;//板块名称
    private String description;//板块描述
    /**
     * 图片信息集合
     */
    private Map<String, ImageFile> imageFiles;

    public long getSectionId() {
        return sectionId;
    }

    public void setSectionId(long sectionId) {
        this.sectionId = sectionId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Map<String, ImageFile> getImageFiles() {
        return imageFiles;
    }

    public void setImageFiles(Map<String, ImageFile> imageFiles) {
        this.imageFiles = imageFiles;
    }
}
