package com.kaolafm.opensdk.utils;

import android.text.TextUtils;

import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import retrofit2.Response;

/**
 * http相关的工具类，主要有获取响应头信息，目前主要用于下载相关的。
 * <AUTHOR>
 * @date 2020-02-11
 */
public class HttpUtil {

    private static final Pattern CONTENT_PATTERN = Pattern.compile(".*filename=(.*)");

    public static String getFileName(Response response) {
        String fileName = contentDisposition(response);
        if (TextUtils.isEmpty(fileName)) {
            fileName = getFileNameFromUrl(getUrl(response));
        }
        return fileName;
    }

    /**
     * 从URL中获取文件名
     *
     * @param url
     * @return String
     */
    private static String getFileNameFromUrl(String url) {
        if (TextUtils.isEmpty(url)) {
            return "";
        }
        String tempUrl = url;
        int fragment = tempUrl.lastIndexOf("#");
        if (fragment > 0) {
            tempUrl = tempUrl.substring(0, fragment);
        }
        int query = tempUrl.lastIndexOf("?");
        if (query > 0) {
            tempUrl = tempUrl.substring(0, query);
        }
        int fileNamePosition = tempUrl.lastIndexOf("/");
        String fileName;
        if (fileNamePosition >= 0) {
            fileName = tempUrl.substring(fileNamePosition + 1);
        } else {
            fileName = tempUrl;
        }
        if (!TextUtils.isEmpty(fileName) && Pattern.matches("[a-zA-Z_0-9.\\-()%]+", fileName)) {
            return fileName;
        }
        return "";
    }

    /**
     * 获取返回值头信息中的内容描述
     *
     * @param response
     * @return String
     */
    public static String contentDisposition(Response response) {
        String content = header(response, "Content-Disposition");
        content = content.toLowerCase(Locale.getDefault());

        Matcher matcher = CONTENT_PATTERN.matcher(content);
        if (!matcher.find()) {
            return "";
        }
        String result = matcher.group(1);
        if (result.startsWith("\"")) {
            result = result.substring(1);
        }
        if (result.endsWith("\"")) {
            result = result.substring(0, result.length() - 1);
        }
        return result.replace("/", "_");
    }

    /**
     * 获取返回值头信息
     *
     * @param response
     * @param key
     * @return String
     */
    public static String header(Response response, String key) {
        String header = response.headers().get(key);
        return header == null ? "" : header;
    }

    /**
     * 判断是否支持断点下载
     *
     * @param response
     * @return boolean
     */
    public static boolean isSupportRange(Response response) {
        return response.code() == 206 || !header(response, "Content-Range").isEmpty() || "bytes".equals(header(response, "Accept-Ranges"));
    }

    /**
     * 获取头信息中的内容大小
     * @param response
     * @return long
     */
    public static long getContentLength(Response response) {
        try {
            return Long.parseLong(header(response, "Content-Length"));
        } catch (NumberFormatException e) {
            return -1;
        }
    }

    /**
     * 是否支持断点下载
     * @param response
     * @return boolean
     */
    public static boolean isChunked(Response response) {
        return "chunked".equals(header(response, "Transfer-Encoding"));
    }

    public static String getUrl(Response response) {
        return response.raw().request().url().toString();
    }

    /**
     * 获取下载片段的个数。
     * @param response
     * @param rangeSize
     * @return long
     */
    public static long getSliceCount(Response response, long rangeSize) {
        long totalSize = getContentLength(response);
        long remainder = totalSize % rangeSize;
        long result = totalSize / rangeSize;
        return remainder == 0L ? result : result + 1;
    }
}
