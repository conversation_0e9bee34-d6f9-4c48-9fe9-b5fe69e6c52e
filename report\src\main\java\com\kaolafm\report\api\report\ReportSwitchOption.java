package com.kaolafm.report.api.report;

import com.google.gson.annotations.SerializedName;

/**
 * 埋点所用
 * <AUTHOR>
 * @date 2021-9-7
 *
 *
 */
public class ReportSwitchOption {
    /**
     * 确认有upload_big_datacenter，且为on。就上报到大数据中心
     */
    @SerializedName("upload_big_datacenter")
    private String uploadBigDatacenter;

    public String getUploadBigDatacenter() {
        return uploadBigDatacenter;
    }

    public void setUploadBigDatacenter(String uploadBigDatacenter) {
        this.uploadBigDatacenter = uploadBigDatacenter;
    }

    public boolean isUploadBigDatacenter() {
        return "on".equals(uploadBigDatacenter);
    }

    @Override
    public String toString() {
        return "ReportSwitchOption{" +
                "uploadBigDatacenter='" + uploadBigDatacenter + '\'' +
                '}';
    }
}
