package com.kaolafm.opensdk.account.token;

/**
 * 缓存接口
 * <AUTHOR>
 * @date 2018/7/29
 */
public interface TokenCache<T> {

    boolean accept(AccessToken token);

    /**
     * 获取
     * @return
     */
    T getToken();

    /**
     * 保存
     * @param t
     */
    void save(T t);

    /**
     * 加载
     * @return
     */
    T load();

    /**
     * 清空，清除所有数据
     */
    void clear();

    /**
     * 退出登录，不一定清空所有数据
     */
    void logout();
}
