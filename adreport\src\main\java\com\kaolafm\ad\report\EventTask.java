package com.kaolafm.ad.report;

import android.util.Log;

import com.kaolafm.ad.report.bean.BaseAdEvent;
import com.kaolafm.base.utils.DateUtil;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

public class EventTask implements Runnable {

    private BaseAdEvent baseAdEvent;

    private static final String TAG = EventTask.class.getSimpleName();

    public EventTask(BaseAdEvent baseAdEvent){
        this.baseAdEvent = baseAdEvent;
    }

    @Override
    public void run() {

        if (!AdReportManager.getInstance().hasInit) {
            Log.i(TAG,"adreport not init");
            return;
        }

        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd_HH:mm:ss");
        String date = dateFormat.format(new Date(DateUtil.getServerTime()));
        baseAdEvent.setDate(date);
        Log.i(TAG,"EventTask addEvent :"+baseAdEvent.toString());
        AdReportManager.getInstance().addEvent(baseAdEvent);
    }

}
