package com.kaolafm.opensdk.player.logic.db.manager;

import com.kaolafm.opensdk.apt.DBOpt;
import com.kaolafm.opensdk.db.OnQueryListener;
import com.kaolafm.opensdk.db.manager.BaseDBManager;
import com.kaolafm.opensdk.player.logic.db.greendao.RadioSortTypeItemDao;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.RadioSortTypeItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import org.greenrobot.greendao.query.QueryBuilder;

/**
 * 专辑排序方式管理类
 */
@DBOpt(name = "Player.db")
public class RadioSortTypeDaoManager extends BaseDBManager<RadioSortTypeItem, RadioSortTypeItemDao> {
    private static volatile RadioSortTypeDaoManager mInstance;

    private RadioSortTypeDaoManager() {
    }

    public static RadioSortTypeDaoManager getInstance() {
        if (mInstance == null) {
            synchronized (RadioSortTypeDaoManager.class) {
                if (mInstance == null) {
                    mInstance = new RadioSortTypeDaoManager();
                }
            }
        }
        return mInstance;
    }

    public boolean isExistObject(int radioType, long radioId) {
        QueryBuilder<RadioSortTypeItem> qb = mDao.queryBuilder();
        qb.where(RadioSortTypeItemDao.Properties.RadioId.eq(radioId), RadioSortTypeItemDao.Properties.RadioType.eq(radioType));
        long length = qb.buildCount().count();
        return length > 0;
    }

    public void save(PlayItem item, int sortType) {
        if (item == null || (item.getType() != PlayerConstants.RESOURCES_TYPE_ALBUM && item.getType() != PlayerConstants.RESOURCES_TYPE_FEATURE))
            return;
        RadioSortTypeItem radioSortTypeItem = new RadioSortTypeItem();
        radioSortTypeItem.setRadioType(item.getType());
        radioSortTypeItem.setRadioId(Long.parseLong(item.getAlbumId()));
        radioSortTypeItem.setSortType(sortType);
        save(radioSortTypeItem);
    }

    @Override
    public void save(RadioSortTypeItem radioSortTypeItem) {
        runInNewThread(() -> {
            if (radioSortTypeItem == null) return false;
            RadioSortTypeItem old = mDao.queryBuilder().where(RadioSortTypeItemDao.Properties.RadioType.eq(radioSortTypeItem.getRadioType()), RadioSortTypeItemDao.Properties.RadioId.eq(radioSortTypeItem.getRadioId()))
                    .build().unique();
            if (old != null) {
                radioSortTypeItem.setId(old.getId());
                mDao.update(radioSortTypeItem);
            } else {
                mDao.save(radioSortTypeItem);
            }
            return true;
        }, null);
    }

    /**
     * 删除
     */
    public void delete(int radioType, long radioId) {
        runInNewThread(() -> {
            RadioSortTypeItem item = mDao.queryBuilder().where(RadioSortTypeItemDao.Properties.RadioType.eq(radioType), RadioSortTypeItemDao.Properties.RadioId.eq(radioId)).build().unique();
            if (item != null) {
                mDao.delete(item);
            }
            return true;
        }, null);
    }

    public void query(int radioType, long radioId, OnQueryListener<RadioSortTypeItem> listener) {
        runInNewThread(() -> {
            RadioSortTypeItem item = mDao.queryBuilder().where(RadioSortTypeItemDao.Properties.RadioType.eq(radioType), RadioSortTypeItemDao.Properties.RadioId.eq(radioId)).build().unique();
            return item;
        }, listener);
    }
}
