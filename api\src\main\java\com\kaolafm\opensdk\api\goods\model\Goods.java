package com.kaolafm.opensdk.api.goods.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * <AUTHOR>
 * @date 2023-02-16
 */
public class Goods implements Parcelable {

    private Long id;//商品序号
    private String name;//商品名称
    private String picUrl;//商品图片
    private String sellPoint;//商品简介
    private Long marketPrice;//商品原价.#0.00
    private Long salesPrice;//商品秒杀/折扣/促销/跳楼价.#0.00
    private Integer shelf;//状态.是否上架：0-否；1-是
    private Integer pushType;//状态.是否讲解中：0-否；1-是
    private Integer stock; // 库存

    public Goods() {
    }

    protected Goods(Parcel in) {
        if (in.readByte() == 0) {
            id = null;
        } else {
            id = in.readLong();
        }
        name = in.readString();
        picUrl = in.readString();
        sellPoint = in.readString();
        if (in.readByte() == 0) {
            marketPrice = null;
        } else {
            marketPrice = in.readLong();
        }
        if (in.readByte() == 0) {
            salesPrice = null;
        } else {
            salesPrice = in.readLong();
        }
        if (in.readByte() == 0) {
            shelf = null;
        } else {
            shelf = in.readInt();
        }
        if (in.readByte() == 0) {
            pushType = null;
        } else {
            pushType = in.readInt();
        }
        if (in.readByte() == 0) {
            stock = null;
        } else {
            stock = in.readInt();
        }
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        if (id == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeLong(id);
        }
        dest.writeString(name);
        dest.writeString(picUrl);
        dest.writeString(sellPoint);
        if (marketPrice == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeLong(marketPrice);
        }
        if (salesPrice == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeLong(salesPrice);
        }
        if (shelf == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(shelf);
        }
        if (pushType == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(pushType);
        }
        if (stock == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(stock);
        }
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<Goods> CREATOR = new Creator<Goods>() {
        @Override
        public Goods createFromParcel(Parcel in) {
            return new Goods(in);
        }

        @Override
        public Goods[] newArray(int size) {
            return new Goods[size];
        }
    };

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getSellPoint() {
        return sellPoint;
    }

    public void setSellPoint(String sellPoint) {
        this.sellPoint = sellPoint;
    }

    public Long getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(Long marketPrice) {
        this.marketPrice = marketPrice;
    }

    public Long getSalesPrice() {
        return salesPrice;
    }

    public void setSalesPrice(Long salesPrice) {
        this.salesPrice = salesPrice;
    }

    public Integer getShelf() {
        return shelf;
    }

    public void setShelf(Integer shelf) {
        this.shelf = shelf;
    }

    public Integer getPushType() {
        return pushType;
    }

    public void setPushType(Integer pushType) {
        this.pushType = pushType;
    }

    public Integer getStock() {
        return stock;
    }

    public void setStock(Integer stock) {
        this.stock = stock;
    }
}
