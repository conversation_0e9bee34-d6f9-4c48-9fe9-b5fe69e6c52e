package com.kaolafm.opensdk.player.logic.listener;

import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

/******************************************
 * 类描述： 播放状态回调 类名称：IPlayerStateListener
 *
 * @version: 1.0
 * @author: shaoning<PERSON>ang
 * @time: 2016-7-21 16:40
 ******************************************/
public interface IPlayerStateVideoListener{
    void onPlayerVideoRenderingStart(PlayItem playItem);

    void onPlayerVideoSizeChanged(PlayItem playItem, int width, int height);
}
