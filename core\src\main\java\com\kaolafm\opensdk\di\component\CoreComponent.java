package com.kaolafm.opensdk.di.component;

import android.app.Application;

import com.kaolafm.opensdk.Options;
import com.kaolafm.opensdk.account.token.RealAccessTokenManager;

import dagger.BindsInstance;

/**
 * 依赖注入Component基类。
 *
 * <AUTHOR>
 * @date 2020-01-14
 */
public interface CoreComponent<S extends BaseSubcomponent> {

    void inject(RealAccessTokenManager manager);

    Application application();

    RequestComponent requestComponent();

    S subComponent();

    Options options();

    interface Builder<C extends CoreComponent, O extends Options> {

        @BindsInstance
        C.Builder application(Application application);

        @BindsInstance
        C.Builder options(O options);

        C build();
    }
}
