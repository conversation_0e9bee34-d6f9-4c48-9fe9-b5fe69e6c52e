package com.kaolafm.opensdk.api.history;

import android.util.Log;

import com.google.gson.Gson;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.history.model.SaveHistoryItem;
import com.kaolafm.opensdk.api.history.model.SyncHistoryStatus;
import com.kaolafm.opensdk.api.login.model.Success;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.HashMap;
import java.util.List;

import okhttp3.MediaType;
import okhttp3.RequestBody;

/**
 * <AUTHOR>
 **/
public class SaveHistoriesRequest extends BaseRequest {

    private static final int STATUS_SUCCESS = 1;

    private final SaveHistoriesService mHistoryService;

    public SaveHistoriesRequest() {
        mHistoryService = obtainRetrofitService(SaveHistoriesService.class);
    }

    public void saveHistories(List<SaveHistoryItem> shis, HttpCallback<Boolean> callback) {
        HashMap<String, List<SaveHistoryItem>> map = new HashMap<>();
        map.put("kradioHistorys", shis);
        String body = new Gson().toJson(map);
        Log.i("kradio.his", "saveHistories: json=" + body);
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);

        doHttpDeal(mHistoryService.saveHistories(requestBody),
                baseResult -> {
                    SyncHistoryStatus status = baseResult.getResult();
                    return status != null && STATUS_SUCCESS == status.getStatus();
                }, callback);

    }

    /**
     * 更新碎片状态
     *
     * @param id   专辑/碎片id
     * @param type 类型 1：专辑，0：碎片 （默认为0）
     * @param come 来自哪里1：来自订阅，0:其他（默认为0）
     */
    public void updateAudioState(long id, int type, int come, HttpCallback<Boolean> callback) {
        doHttpDeal(mHistoryService.updateAudioState(id, type, come), response -> {
            boolean ok = response.code() == 200;
            if (ok) {
                BaseResult<Success> baseResult = response.body();
                Success success = baseResult != null? baseResult.getResult() : null;
                return success != null && "0".equals(success.getCode());
            }
            return false;
        }, callback);

    }

}
