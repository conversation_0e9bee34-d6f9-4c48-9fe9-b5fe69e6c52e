package com.kaolafm.opensdk.api.poll;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.history.model.SyncHistoryStatus;
import com.kaolafm.opensdk.api.live.model.SocketLiveBean;
import com.kaolafm.opensdk.api.login.KRadioApiConstants;

import java.util.List;
import java.util.Map;

import io.reactivex.Single;
import okhttp3.RequestBody;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.POST;
import retrofit2.http.QueryMap;

/**
 * <AUTHOR>
 * @date 2023-03-10
 */
public interface PollingService {

    /**
     * 保存历史集合，并加积分。
     *
     * @param requestBody
     * @return
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @POST(KRadioApiConstants.SAVE_HISTORY_ACCUMULATE_URL)
    Single<BaseResult<SyncHistoryStatus>> saveHistoryAccumulate(@Body RequestBody requestBody);

    /**
     * 获取积分
     * @param params
     * @return
     */
//        @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
//        @GET(BuildConfig.NET_REQUEST_VERSION+"/kradio/integralRefresh")
//        Single<BaseResult<CoinBean>> getIntegrals(@QueryMap Map<String, Object> params);

    /**
     * 直播入流
     *
     * @param params
     * @return
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KRadioApiConstants.GET_EFFECTIVE_LIVE_URL)
    Single<BaseResult<List<SocketLiveBean>>> getRadioLiveStream(@QueryMap Map<String, Object> params);
}
