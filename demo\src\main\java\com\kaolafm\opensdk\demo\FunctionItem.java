package com.kaolafm.opensdk.demo;

/**
 * <AUTHOR>
 * @date 2018/9/5
 */

public class FunctionItem {

    public static final int TYPE_TITLE = 1;

    public static final int TYPE_CHILD = 2;

    public FunctionItem() {
    }

    public FunctionItem(String name, int type, int resType) {
        this.name = name;
        this.type = type;
        this.resType = resType;
    }

    private String name;
    private int type;
    private int resType;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getResType() {
        return resType;
    }

    public void setResType(int resType) {
        this.resType = resType;
    }
}
