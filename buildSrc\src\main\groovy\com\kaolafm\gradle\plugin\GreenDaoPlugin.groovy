package com.kaolafm.gradle.plugin

import freemarker.template.Configuration
import freemarker.template.Template
import freemarker.template.TemplateNotFoundException
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.greenrobot.greendao.gradle.GreendaoOptions

class GreenDaoPlugin implements Plugin<Project> {

    private Template template

    @Override
    void apply(Project project) {

        project.getPlugins().apply("org.greenrobot.greendao")
        project.afterEvaluate {
            Configuration configuration = getConfiguration("DaoManager.ftl")
            template = configuration.getTemplate("DaoManager.ftl")
            GreendaoOptions options = (GreendaoOptions) project.getExtensions().getByName("greendao")
            options.daoPackage
        }
    }

    private Configuration getConfiguration(String probingTemplate) throws IOException {
        Configuration config = new Configuration(Configuration.VERSION_2_3_23)
        config.setClassForTemplateLoading(getClass(), "/")

        try {
            config.getTemplate(probingTemplate)
        } catch (TemplateNotFoundException e) {
            // When running from an IDE like IntelliJ, class loading resources may fail for some reason (Gradle is OK)

            // Working dir is module dir
            File dir = new File("src/main/resources/")
            if (!dir.exists()) {
                // Working dir is base module dir
                dir = new File("buildSrc/src/main/resources/")
            }
            if (dir.exists() && new File(dir, probingTemplate).exists()) {
                config.setDirectoryForTemplateLoading(dir)
                config.getTemplate(probingTemplate)
            } else {
                throw e
            }
        }
        return config
    }
}