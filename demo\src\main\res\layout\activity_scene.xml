<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.kaolafm.opensdk.demo.scene.SceneActivity"
    android:padding="10dp"
    >
    <TextView
        android:id="@+id/tv_scene_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="场景选择（多选）"
        />
    <CheckBox
        android:id="@+id/cb_scene_acc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="点火场景"
        app:layout_constraintTop_toBottomOf="@id/tv_scene_title"
        />
    <TextView
        android:id="@+id/tv_scene_speed_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="速度场景"
        app:layout_constraintTop_toBottomOf="@id/cb_scene_acc"
        android:paddingLeft="10dp"
        />
    <CheckBox
        android:id="@+id/cb_scene_low_speed"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/tv_scene_speed_title"
        android:text="低速（堵车）"
        />
    <CheckBox
        android:id="@+id/cb_scene_medium_speed"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toRightOf="@id/cb_scene_low_speed"
        app:layout_constraintTop_toBottomOf="@id/tv_scene_speed_title"
        android:text="中速"
        />
    <CheckBox
        android:id="@+id/cb_scene_high_speed"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toRightOf="@id/cb_scene_medium_speed"
        app:layout_constraintTop_toBottomOf="@id/tv_scene_speed_title"
        android:text="高速"
        />
    <Button
        android:id="@+id/btn_scene_send"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="发送"
        app:layout_constraintTop_toBottomOf="@id/cb_scene_low_speed"
        />
    <TextView
        android:id="@+id/tv_scene_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/btn_scene_send"
        android:textSize="14sp"
        android:textColor="@color/colorBlack"
        />

</androidx.constraintlayout.widget.ConstraintLayout>
