package com.kaolafm.opensdk.account.token;

import android.os.Parcel;
import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.kaolafm.base.utils.DateUtil;

import javax.inject.Inject;

/**
 * 用于考拉网络请求的鉴权数据，OpenId、AutoId、UserId、Token等公共参数
 *
 * <AUTHOR>
 * @date 2018/7/29
 */

public final class KaolaAccessToken implements AccessToken {

    // 车载设备唯一识别，激活生成的唯一id
    private String openId;

    @SerializedName("expires_in")
    private long refreshTime;

    @SerializedName("open_uid")
    private String userId;

    @SerializedName("access_token")
    private String accessToken;

    @SerializedName("refresh_token")
    private String refreshToken;

    /**过期时间 毫秒*/
    private long expireTime;

    @Inject
    public KaolaAccessToken() {
    }

    public String getOpenId() {
        return openId;
    }

    @Override
    public boolean isLogin() {
        return !TextUtils.isEmpty(accessToken) && !TextUtils.isEmpty(userId);
    }

    @Override
    public void logout() {
        setAccessToken(null);
        setRefreshToken(null);
        setUserId(null);
        setRefreshTime(0);
    }

    @Override
    public void clear() {
        logout();
        setOpenId(null);
    }

    @Override
    public boolean isExpires() {
        return expireTime < DateUtil.getServerTime();
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public long getRefreshTime() {
        return refreshTime;
    }

    public void setExpireTime(long expireTime) {
        this.expireTime = expireTime;
    }

    public void setRefreshTime(long refreshTime) {
        this.refreshTime = refreshTime;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAccessToken() {
        return accessToken;
    }

    @Deprecated
    public String getToken() {
        return getAccessToken();
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    @Override
    public String toString() {
        return "KaolaAccessToken{" +
                "openId='" + openId + '\'' +
                ", refreshTime=" + refreshTime +
                ", userId='" + userId + '\'' +
                ", accessToken='" + accessToken + '\'' +
                ", refreshToken='" + refreshToken + '\'' +
                ", expireTime=" + expireTime +
                '}'+ "@" + Integer.toHexString(hashCode());
    }

    private KaolaAccessToken(Parcel source) {
        openId = source.readString();
        refreshTime = source.readLong();
        userId = source.readString();
        accessToken = source.readString();
        refreshToken = source.readString();
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(openId);
        dest.writeLong(refreshTime);
        dest.writeString(userId);
        dest.writeString(accessToken);
        dest.writeString(refreshToken);
    }

    public static final Creator CREATOR = new Creator() {

        @Override
        public KaolaAccessToken createFromParcel(Parcel source) {
            return new KaolaAccessToken(source);
        }

        @Override
        public KaolaAccessToken[] newArray(int size) {
            return new KaolaAccessToken[size];
        }
    };
}
