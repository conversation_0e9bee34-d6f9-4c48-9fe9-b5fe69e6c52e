package com.kaolafm.opensdk.api.operation.model.column;

import java.util.List;

/**
 * 栏目。栏目可以包含栏目成员
 */
public class Column extends ColumnGrp {

    /** 是否支持跳转至更多：1，支持；0，不支持*/
    private int forwardToMore;

    /** 跳转至更多的目标栏目成员，一般是分类*/
    private ColumnMember moreColumnMember;

    /** 栏目成员*/
    private List<? extends ColumnMember> columnMembers;

    public int getForwardToMore() {
        return forwardToMore;
    }

    public void setForwardToMore(int forwardToMore) {
        this.forwardToMore = forwardToMore;
    }

    public ColumnMember getMoreColumnMember() {
        return moreColumnMember;
    }

    public void setMoreColumnMember(ColumnMember moreColumnMember) {
        this.moreColumnMember = moreColumnMember;
    }

    public List<? extends ColumnMember> getColumnMembers() {
        return columnMembers;
    }

    public void setColumnMembers(List<? extends ColumnMember> columnMembers) {
        this.columnMembers = columnMembers;
    }

    @Override
    public String toString() {
        return "Column{" +
                "forwardToMore=" + forwardToMore +
                ", moreColumnMember=" + moreColumnMember +
                ", columnMembers=" + columnMembers +
                "} " + super.toString();
    }
}
