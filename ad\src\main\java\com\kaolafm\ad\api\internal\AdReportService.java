package com.kaolafm.ad.api.internal;

import com.kaolafm.ad.AdConstant;

import io.reactivex.Single;
import retrofit2.Response;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Query;

/**
 * <AUTHOR>
 * @date 2020-01-02
 */
public interface AdReportService {

    @Headers(AdConstant.DOMAIN_HEADER_AD_REPORT)
    @GET(AdConstant.REPORT_SKIP)
    Single<Response<Void>> skip(@Query("p") String data);

    @Headers(AdConstant.DOMAIN_HEADER_AD_REPORT)
    @GET(AdConstant.REPORT_DISPLAY)
    Single<Response<Void>> display(@Query("p") String data);

    @Headers(AdConstant.DOMAIN_HEADER_AD_REPORT)
    @GET(AdConstant.REPORT_DISPLAY_END)
    Single<Response<Void>> endDisplay(@Query("p") String data);

    @Headers(AdConstant.DOMAIN_HEADER_AD_REPORT)
    @GET(AdConstant.REPORT_DISPLAY_INTERRUPT)
    Single<Response<Void>> interruptDisplay(@Query("p") String data);

    @Headers(AdConstant.DOMAIN_HEADER_AD_REPORT)
    @GET(AdConstant.REPORT_MORE_INTERACTION_DISPLAY)
    Single<Response<Void>> displayMoreInteraction(@Query("p") String data);

    @Headers(AdConstant.DOMAIN_HEADER_AD_REPORT)
    @GET(AdConstant.REPORT_MORE_INTERACTION_DISPLAY_END)
    Single<Response<Void>> displayMoreInteractionEnd(@Query("p") String data);

    @Headers(AdConstant.DOMAIN_HEADER_AD_REPORT)
    @GET(AdConstant.REPORT_PLAY)
    Single<Response<Void>> play(@Query("p") String data);

    @Headers(AdConstant.DOMAIN_HEADER_AD_REPORT)
    @GET(AdConstant.REPORT_CLICK)
    Single<Response<Void>> click(@Query("p") String data);

    @Headers(AdConstant.DOMAIN_HEADER_AD_REPORT)
    @GET(AdConstant.REPORT_ERROR)
    Single<Response<Void>> error(@Query("p") String data);

    @Headers(AdConstant.DOMAIN_HEADER_AD_REPORT)
    @GET(AdConstant.REPORT_ACTIVE)
    Single<Response<Void>> active(@Query("p") String data);

}
