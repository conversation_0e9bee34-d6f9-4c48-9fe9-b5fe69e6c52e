package com.kaolafm.report.event;

import com.kaolafm.report.model.PlayReportParameter;
import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> on 2019-07-23.
 */
@Deprecated
public class BroadcastEndListenReportEvent extends BaseReportEventBean {
    /**
     * 单曲id（回放id）
     */
    private String audioid;
    /**
     * 节目id（在线广播id）
     */
    private String radioid;
    /**
     * 播放状态 1：直播中；2：回放中
     */
    private String status;

    /**
     * 播放时长
     */
    private String playtime;

    public BroadcastEndListenReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_BROADCAST_END_LISTEN);
    }

    public void playParameterToEvent(PlayReportParameter parameter) {
        setRadioid(parameter.getRadioid());
        setAudioid(parameter.getAudioid());
        long total = parameter.getTotalLength() / 1000;
        long play = parameter.getPlayPosition() / 1000;
        if (play > total) {
            play = total;
        }
        setPlaytime(String.valueOf(play));
        setStatus(parameter.getBroadcast_status());
    }

    public String getAudioid() {
        return audioid;
    }

    public void setAudioid(String audioid) {
        this.audioid = audioid;
    }

    public String getRadioid() {
        return radioid;
    }

    public void setRadioid(String radioid) {
        this.radioid = radioid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPlaytime() {
        return playtime;
    }

    public void setPlaytime(String playtime) {
        this.playtime = playtime;
    }
}
