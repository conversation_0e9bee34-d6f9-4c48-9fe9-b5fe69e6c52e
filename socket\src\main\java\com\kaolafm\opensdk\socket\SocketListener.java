package com.kaolafm.opensdk.socket;

import com.kaolafm.opensdk.http.core.HttpCallback;
import java.util.Map;

/**
 * socket连接的回调。需要实现四个方法
 *
 * <AUTHOR> Yan
 * @date 2019-11-05
 */
public interface SocketListener<RESULT> extends HttpCallback<RESULT> {

    /**
     * socket请求的事件名，需要和服务端统一，如{@link SocketEvent#HOME_PAGE_REFRESH}
     *
     * @return 事件名
     */
    String getEvent();

    /**
     * socket请求的参数，公共参数已经封装，这里只需要使用方法内的参数Map添加请求特有的参数并返回Map即可。
     *
     * @param params 可能带有公共参数的集合。
     * @return 全参数集合
     */
    Map<String, Object> getParams(Map<String, Object> params);

    /**
     * socket请求是否需要加上参数
     *
     * @return 是否需要加上参数
     */
    boolean isNeedParams();

    /**
     * socket是否需要主动发出请求
     *
     * @return 是否需要主动发出请求
     */
    boolean isNeedRequest();
}
