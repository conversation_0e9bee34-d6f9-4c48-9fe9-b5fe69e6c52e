package com.kaolafm.opensdk.player.logic.util;

import android.os.Handler;
import android.os.Looper;

public class UIThreadUtil {

    private static final Handler mMainHandler = new Handler(Looper.getMainLooper());

    public static void runUIThread(final UiThread mUiThread) {
        if (isMainThread()) {
            mUiThread.onSuccess();
        } else {
            mMainHandler.post(() -> mUiThread.onSuccess());
        }
    }

    private static boolean isMainThread() {
        return Looper.getMainLooper().getThread() == Thread.currentThread();
    }

    public interface UiThread {
        void onSuccess();
    }
}
