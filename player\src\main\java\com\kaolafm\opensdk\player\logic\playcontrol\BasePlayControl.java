package com.kaolafm.opensdk.player.logic.playcontrol;

import android.media.AudioAttributes;
import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.media.model.AudioFileInfo;
import com.kaolafm.opensdk.api.media.model.ToneQuality;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.core.PlayerService;
import com.kaolafm.opensdk.player.core.ijk.VideoView;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter;
import com.kaolafm.opensdk.player.core.model.AAudioFocus;
import com.kaolafm.opensdk.player.core.model.AudioFadeConfig;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.VideoAlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.opensdk.player.logic.util.PlayerPreconditions;
import com.kaolafm.opensdk.player.logic.util.ToneQualityHelper;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.List;


/**
 * 播放控制的基类
 *
 * <AUTHOR> shi qian
 */
public abstract class BasePlayControl implements IPlayControl {

    protected PlayerService.PlayerServiceBinder mPlayerBinder;
    private String TAG = "BasePlayControl";

    protected PlayItem mPlayItem;
    protected WeakReference<VideoView> mVideoViewRef;

    protected BasePlayStateListener mBasePlayControlListener;


    public BasePlayControl() {
    }

    @Override
    public void setPlayStateListener(BasePlayStateListener iPlayerStateListener) {
        mBasePlayControlListener = iPlayerStateListener;
    }

    @Override
    public void setBind(PlayerService.PlayerServiceBinder mediaPlayerServiceBind) {
        mPlayerBinder = mediaPlayerServiceBind;
    }

    @Override
    public void setVideoView(VideoView videoView) {
        mVideoViewRef = new WeakReference<>(videoView);
    }

    protected VideoView getVideoView() {
        return mVideoViewRef == null ? null : mVideoViewRef.get();
    }

    @Override
    public void play() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.play();
    }

    @Override
    public void initPlayerForce() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.initPlayerForce();
    }

    @Override
    public void pause() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.pause();
    }

    @Override
    public void stop() {
        if (!PlayerPreconditions.checkNull(mPlayerBinder)) {
            mPlayerBinder.stop();
        }
    }

    @Override
    public void reset(boolean needResetLastPlaybackRateFlag) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.reset(needResetLastPlaybackRateFlag);

    }

    @Override
    public void rePlay() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.rePlay();

    }

    @Override
    public void release() {

    }

    @Override
    public void seek(int position) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }

        mPlayerBinder.seek(position);
    }

    @Override
    public void playTempTask(String url, VideoView videoView, String playUrlId) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.start(url, 0, 0, false, videoView, playUrlId);
    }

    @Override
    public void preStart(PlayItem playItem) {

    }

    @Override
    public void start(int type, PlayItem playItem, VideoView videoView, boolean isPlayNow) {
        PlayerLogUtil.log(TAG, "start, processId= " + android.os.Process.myTid());
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            PlayerLogUtil.log(TAG, "start, mPlayerBinder = " + "null");
            return;
        }
        if (PlayerPreconditions.checkNull(playItem)) {
            PlayerLogUtil.log(TAG, "start, playItem = " + "null");
            return;
        }
        mPlayItem = playItem;

        PlayControl.getInstance().setPlayWhenReady(true);
        setPlayUrl(playItem, new OnGetPlayUrlData() {
            @Override
            public void onDataGet(String playUrl) {
                if (!PlayControl.getInstance().isPlayWhenReady()) {
                    Log.w(TAG, "onDataGet: PlayWhenReady is FALSE, cancel play Task");
                    return;
                }

                if (TextUtils.isEmpty(playUrl)) {
                    reset(true);
                    if (mBasePlayControlListener != null) {
                        mBasePlayControlListener.onPlayerFailed(playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_URL_NULL, 404);
                        mBasePlayControlListener.onProgress(playItem, 1, 0);
                    }
                } else {
                    mPlayerBinder.setPlayNowOnPrepared(isPlayNow);
                    playWithPosition(playItem, videoView);
                }
            }

            @Override
            public void onDataError(ApiException e) {
                stop();
                if (e.getCode() == PlayerConstants.ERROR_CODE_NO_COPYRIGHT) {
                    // code - 50816, message - 因版权原因，暂时无法播放
                    if (mBasePlayControlListener != null) {
                        mBasePlayControlListener.onPlayerFailed(playItem, PlayerConstants.ERROR_CODE_NO_COPYRIGHT, e.getCode());
                    }
                    return;
                }
                if (e.getCode() == PlayerConstants.ERROR_CODE_PLAY_LIST_URL_NULL) {
                    if (mBasePlayControlListener != null) {
                        mBasePlayControlListener.onPlayerFailed(playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_URL_NULL, 404);
                    }
                    return;
                }
                if (mBasePlayControlListener != null) {
                    mBasePlayControlListener.onPlayerFailed(playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_URL_DECODE, e.getCode());
                }
            }
        });
    }

    /**
     * 设置真实的播放地址
     * 逻辑如下：
     * 1. 获取保存的音质设置
     * 2. 如果没有设置，则从头开始遍历音质播放地址，找到第一个有可用地址的作为播放地址
     * 3. 如果设置了音质，则遍历音质list，找到对应码率和资源类型的播放地址，设置为真实播放地址
     * 4. 如果没有找到对应码率和资源类型的播放地址，则使用找到的第一个相同码率的地址作为最终播放地址
     *
     * @param playItem
     * @param playListUrlInfos
     */
    protected void setPlayUrl(PlayItem playItem, List<AudioFileInfo> playListUrlInfos) {
        String tempUrl = "";
        String getUrl = getPlayUrl(playItem, playListUrlInfos);
        PlayerLogUtil.log(TAG, "setPlayUrl, getUrl = " + getUrl);

        tempUrl = getUrl;

        if (!TextUtils.isEmpty(tempUrl)) {
            playItem.setPlayUrl(tempUrl);

            File file = new File(tempUrl);
            boolean exists = file.exists();
            boolean canRead = file.canRead();
            PlayerLogUtil.log(TAG, "setPlayUrl, tempUrl = " + tempUrl + "file canRead = " + canRead + "file exists = " + exists);

        }
    }

    protected interface OnGetPlayUrlData {
        void onDataGet(String playUrl);

        void onDataError(ApiException e);
    }

    /**
     * 设置播放url
     *
     * @param playItem
     * @param callback
     */
    private void setPlayUrl(PlayItem playItem, OnGetPlayUrlData callback) {
        requestPlayUrl(playItem, callback);
    }

    abstract ToneQuality getToneQuality(PlayItem playItem, List<AudioFileInfo> playListUrlInfos);

    /**
     * 请求播放url
     *
     * @param playItem
     * @param callback
     */
    abstract void requestPlayUrl(PlayItem playItem, OnGetPlayUrlData callback);

    /**
     * 根据播放的记录进行seek播放
     *
     * @param playItem
     */
    private void playWithPosition(PlayItem playItem, VideoView videoView) {
        mPlayerBinder.start(playItem.getPlayUrl(), playItem.getDuration(), Math.max(playItem.getPosition(), 0), isLiving(playItem), videoView, playItem.getPlayUrlId());
    }


    /**
     * 获取playItem的分音质播放地址
     *
     * @param playItem
     * @param playListUrlInfos
     * @return
     */
    public String getPlayUrl(PlayItem playItem, List<AudioFileInfo> playListUrlInfos) {
        Log.i("BasePlayControl", "getPlayUrl 开始选择播放地址");
        if (playItem == null || playListUrlInfos == null) return null;
        String tempUrl = null;
        ToneQuality toneQuality = ToneQualityHelper.getInstance().getToneQuality();
        PlayerLogUtil.log(getClass().getSimpleName(), "soundQuality=  " + toneQuality);

        Integer bitrateNewResult = 0;//最终设置的码率

        if (toneQuality == null) {
            //没有设置音质
            Log.i("BasePlayControl", "getPlayUrl 未设置音质，将按照对应默认格式选择播放地址");
            String resourceType = getPlayItemResourceType(playItem);
            Log.i("BasePlayControl", "getPlayUrl 查询播放资源默认格式：" + resourceType);
            Integer bitrateOldResult = 0;//旧版的码率
            for (AudioFileInfo playListUrlInfo : playListUrlInfos) {
                if (!StringUtil.isEmpty(playListUrlInfo.getPlayUrl())) {
                    //如果playItem的播放地址本身为空，只要找到一个可用的地址就设置
                    if (StringUtil.isEmpty(playItem.getPlayUrl())) {
                        tempUrl = playListUrlInfo.getPlayUrl();
                        bitrateOldResult = playListUrlInfo.getBitrate();
                        bitrateNewResult = playListUrlInfo.getBitrateNew();
                    } else {
                        tempUrl = playItem.getPlayUrl();
                        bitrateOldResult = playListUrlInfo.getBitrate();
                        bitrateNewResult = playListUrlInfo.getBitrateNew();
                    }
                    //其次，找到对应的资源类型进行设置，如果找不到对应资源，有上面的第一个可用地址进行兜底
                    if (playListUrlInfo.getFileType().equals(resourceType) ||
                            (playItem instanceof VideoAlbumPlayItem && supportVideoFormat(playListUrlInfo.getFileType(), resourceType))) {
                        Log.i("BasePlayControl", "getPlayUrl 找到对应资源格式的播放地址：旧版码率为" + playListUrlInfo.getBitrate() + " ,新版码率为" + playListUrlInfo.getBitrateNew());
                        tempUrl = playListUrlInfo.getPlayUrl();
                        bitrateOldResult = playListUrlInfo.getBitrate();
                        bitrateNewResult = playListUrlInfo.getBitrateNew();
                        break;
                    }
                }
            }
            if (!TextUtils.isEmpty(tempUrl)) {
                Log.i("BasePlayControl", "getPlayUrl 确定最终码率---->旧版码率为" + bitrateOldResult + " ,新版码率为" + bitrateNewResult);
            }
            return tempUrl;
        }

        String targetField = null, targetValue = null, targetType = null;
        if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_BROADCAST) {
            if (playItem.isLiving()) {
                targetField = toneQuality.getBroadcastTargetField();
                targetValue = toneQuality.getBroadcastTargetValue();
                targetType = toneQuality.getBroadcastTargetType();
            } else {
                targetField = toneQuality.getProgramTargetField();
                targetValue = toneQuality.getProgramTargetValue();
                targetType = toneQuality.getProgramTargetType();
            }
        } else if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_TV) {
            if (playItem.isLiving()) {
                targetField = toneQuality.getListenTVTargetField();
                targetValue = toneQuality.getListenTVTargetValue();
                targetType = toneQuality.getListenTVTargetType();
            } else {
                targetField = toneQuality.getListenTVProgramTargetField();
                targetValue = toneQuality.getListenTVProgramTargetValue();
                targetType = toneQuality.getListenTVProgramTargetType();
            }
        } else if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_RADIO ||
                playItem.getType() == PlayerConstants.RESOURCES_TYPE_ALBUM ||
                playItem.getType() == PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM ||
                playItem.getType() == PlayerConstants.RESOURCES_TYPE_FEATURE) {
            targetField = toneQuality.getAudioTargetField();
            targetValue = toneQuality.getAudioTargetValue();
            targetType = toneQuality.getAudioTargetType();
        } else if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_LIVING) {
            targetField = toneQuality.getLiveTargetField();
            targetValue = toneQuality.getLiveTargetValue();
            targetType = toneQuality.getLiveTargetType();
        }
        Log.i("BasePlayControl", "getPlayUrl 已设置音质，资源类型：" + targetType + " ,码率：" + targetValue + ",匹配字段：" + targetField);
        boolean isFindSameBitrate = false;  //是否找到了与音质设置相同的码率
        if ("bitrateNew".equals(targetField)) {
            for (AudioFileInfo playListUrlInfo : playListUrlInfos) {
                String playUrl = playListUrlInfo.getPlayUrl();
                if (StringUtil.isEmpty(playUrl)) {
                    continue;
                }
                if (StringUtil.isEmpty(tempUrl)) {
                    //先找到list里面第一个可用的地址作为播放地址
                    tempUrl = playUrl;
                    bitrateNewResult = playListUrlInfo.getBitrateNew();
                }
                if (String.valueOf(playListUrlInfo.getBitrateNew()).equals(targetValue)) {
                    if (!isFindSameBitrate) {
                        tempUrl = playUrl;
                        bitrateNewResult = playListUrlInfo.getBitrateNew();
                        isFindSameBitrate = true;
                    }
                    if (playListUrlInfo.getFileType().equals(targetType)) {
                        //找到了与设置保存的相同码率且相同资源类型的音频
                        tempUrl = playUrl;
                        bitrateNewResult = playListUrlInfo.getBitrateNew();
                        break;
                    } else {
                        //码率相同但是资源类型不同，有可能是只需要匹配码率即可
                    }
                }
            }
        } else {
            for (AudioFileInfo playListUrlInfo : playListUrlInfos) {
                String playUrl = playListUrlInfo.getPlayUrl();
                if (StringUtil.isEmpty(playUrl)) {
                    continue;
                }
                if (StringUtil.isEmpty(tempUrl)) {
                    //先找到list里面第一个可用的地址作为播放地址
                    tempUrl = playUrl;
                    bitrateNewResult = playListUrlInfo.getBitrate();
                }
                if (String.valueOf(playListUrlInfo.getBitrate()).equals(targetValue)) {
                    //找到了与设置保存的相同码率的音频
                    if (!isFindSameBitrate) {
                        tempUrl = playUrl;
                        bitrateNewResult = playListUrlInfo.getBitrate();
                        isFindSameBitrate = true;
                    }
                    if (playListUrlInfo.getFileType().equals(targetType)) {
                        //找到了与设置保存的相同码率且相同资源类型的音频
                        tempUrl = playUrl;
                        bitrateNewResult = playListUrlInfo.getBitrate();
                        break;
                    } else {
                        //码率相同但是资源类型不同，有可能是只需要匹配码率即可
                    }
                }
            }
        }
        if (!TextUtils.isEmpty(tempUrl)) {
            Log.i("BasePlayControl", "getPlayUrl 确定最终码率为：" + bitrateNewResult + " ,找到相同码率：" + isFindSameBitrate);
        }
        return tempUrl;
    }

    /**
     * 通过设置的音质获取当前节目应使用的音频资源类型
     *
     * @param playItem
     * @return
     */
    abstract String getPlayItemResourceType(PlayItem playItem);

    private boolean isLiving(PlayItem playItem) {
        if (playItem == null) {
            return false;
        }
        int type = playItem.getType();
        switch (type) {
            case PlayerConstants.RESOURCES_TYPE_TV:
            case PlayerConstants.RESOURCES_TYPE_BROADCAST:
            case PlayerConstants.RESOURCES_TYPE_LIVE_STREAM: {
                return mPlayItem.isLiving();
            }
            case PlayerConstants.RESOURCES_TYPE_LIVING: {
                return true;
            }
            default:
                return false;
        }
    }

    private boolean supportVideoFormat(String typeStr, String supportFormats) {
        if (!TextUtils.isEmpty(supportFormats)) {
            String[] formats = supportFormats.split(",");
            for (String format : formats) {
                if (typeStr.equalsIgnoreCase(format)) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public void setLoudnessNormalization(int isActive) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setLoudnessNormalization(isActive);
    }

    @Override
    public boolean isPlaying() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return false;
        }
        return mPlayerBinder.isPlaying();
    }

    @Override
    public boolean requestAudioFocus() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return false;
        }
        return mPlayerBinder.requestAudioFocus();
    }

    public void setCustomAudioAttributes(AudioAttributes customAudioAttributes) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setCustomAudioAttributes(customAudioAttributes);
    }


    @Override
    public boolean abandonAudioFocus() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return false;
        }
        return mPlayerBinder.abandonAudioFocus();
    }

    @Override
    public void setCustomAudioFocus(AAudioFocus audioFocus) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setCustomAudioFocus(audioFocus);
    }


    @Override
    public void setAudioFocusListener(OnAudioFocusChangeInter iAudioFocusListener) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setAudioFocusListener(iAudioFocusListener);
    }

    @Override
    public long getCurrentPosition() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return -1;
        }
        return mPlayerBinder.getCurrentPosition();
    }

    @Override
    public void setMediaVolume(float leftVolume, float rightVolume) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setMediaVolume(leftVolume, rightVolume);
    }

    @Override
    public void setPlaybackRate(float rate) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setPlaybackRate(rate);
    }

    @Override
    public float getPlaybackRate() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return -1;
        }
        return mPlayerBinder.getPlaybackRate();
    }

    @Override
    public int getPlayStatus() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return PlayerConstants.TYPE_PLAYER_IDLE;
        }
        return mPlayerBinder.getPlayStatus();
    }

    @Override
    public void destroy() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.release();
    }

    @Override
    public void setLogInValid() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setLogInValid();
    }

    @Override
    public void setPlayUrl(String url, long position, long duration, VideoView videoView) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setPlayUrl(url, position, duration, videoView);
    }

    @Override
    public void disableAudioFade() {
        mPlayerBinder.disableAudioFade();
    }

    @Override
    public void setHttpProxy(String httpProxy) {
        mPlayerBinder.setHttpProxy(httpProxy);
    }

    @Override
    public void clearHttpProxy() {
        mPlayerBinder.clearHttpProxy();
    }

    @Override
    public void clearDnsCache(boolean clearDnsCache) {
        mPlayerBinder.clearDnsCache(clearDnsCache);
    }

    @Override
    public void setAudioFadeConfig(AudioFadeConfig audioFadeConfig) {
        PlayerLogUtil.log(getClass().getSimpleName(), "setAudioFadeConfig, audioFadeConfig = " + audioFadeConfig.toString());
        mPlayerBinder.setAudioFadeConfig(audioFadeConfig);
    }

    @Override
    public void notifyPlayItemChangToOtherType() {

    }

    /**
     * 设置AudioTrack的 Usage和ContentType
     *
     * @param usage
     * @param contentType
     */
    @Override
    public void setUsageAndContentType(int usage, int contentType) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setUsageAndContentType(usage, contentType);
    }

    public void setPlayNowOnPrepare(boolean isPlayNow) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setPlayNowOnPrepared(isPlayNow);
    }

}
