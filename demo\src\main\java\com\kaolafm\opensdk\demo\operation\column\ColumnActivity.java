package com.kaolafm.opensdk.demo.operation.column;

import android.content.Intent;
import android.os.Bundle;
import androidx.appcompat.app.AlertDialog.Builder;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.widget.EditText;
import android.widget.Switch;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.OnClick;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.opensdk.api.operation.OperationRequest;
import com.kaolafm.opensdk.api.operation.model.column.Column;
import com.kaolafm.opensdk.api.operation.model.column.ColumnGrp;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout;
import java.util.List;

/**
 * 栏目列表页
 *
 * <AUTHOR> Yan
 * @date 2018/7/31
 */

public class ColumnActivity extends BaseActivity {

    @BindView(R.id.et_column_zone)
    EditText mEtColumnZone;

    @BindView(R.id.rv_column_list)
    RecyclerView mRvColumnList;

    @BindView(R.id.switch_with_member)
    Switch mSwitchWithMember;

    @BindView(R.id.trf_column_refresh)
    TwinklingRefreshLayout mTrfColumnRefresh;

    @BindView(R.id.tv_column_commit)
    TextView mTvColumnCommit;

    public static final int TYPE_ROOT = 1;

    public static final int TYPE_SUBCOLUMN = 2;

    public static final int TYPE_COLUMN_MEMBER = 3;

    public static final String TYPE = "type";

    public static final String CODE = "code";

    private String mCode;

    private ColumnGrpAdapter mColumnGrpAdapter;

    private OperationRequest mOperationRequest;

    private int mType;

    @Override
    public int getLayoutId() {
        return R.layout.activity_column;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        mRvColumnList.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false));
        mColumnGrpAdapter = new ColumnGrpAdapter();
        mRvColumnList.setAdapter(mColumnGrpAdapter);
        mColumnGrpAdapter.setOnItemClickListener((view, viewType, o, position) -> click(o, position));
        mTrfColumnRefresh.setEnableRefresh(false);
        mTrfColumnRefresh.setEnableLoadmore(false);
        mSwitchWithMember.setOnCheckedChangeListener((buttonView, isChecked) -> initData());
    }


    @OnClick(R.id.tv_column_commit)
    public void onViewClicked() {
        initData();
    }

    private void click(Object obj, int position) {
        if (obj instanceof ColumnMember) {
            ColumnMember columnMember = (ColumnMember) obj;
            Intent intent = new Intent(this, ColumnDetailActivity.class);
            intent.putExtra(ColumnDetailActivity.KEY_COLUMN_MEMBER, columnMember);
            startActivity(intent);
        }else if (obj instanceof Column) {
            showColumnMember((Column) obj);
        } else if (obj instanceof ColumnGrp) {
            showSubcolumnGrp((ColumnGrp) obj);
        }
    }

    /**
     * 显示栏目成员
     */
    private void showColumnMember(Column column) {
        Intent intent = new Intent(this, ColumnActivity.class);
        intent.putExtra(TYPE, TYPE_COLUMN_MEMBER);

        new Builder(this)
                .setItems(new String[]{"直接显示栏目成员", "根据code请求网络获取栏目成员"}, (dialog, which) -> {
                    switch (which) {
                        case 0:
                            setTitle("栏目成员列表");
                            List<? extends ColumnMember> columnMembers = column.getColumnMembers();
                            if (!ListUtil.isEmpty(columnMembers)) {
                                mColumnGrpAdapter.setDataList(columnMembers);
                            } else {
                                showToast("栏目成员为空");
                            }
                            break;
                        case 1:
                            intent.putExtra(CODE, column.getCode());
                            startActivity(intent);
                            break;
                        default:
                    }
                }).create().show();
    }

    /**
     * 显示子栏目
     */
    private void showSubcolumnGrp(ColumnGrp columnGrp) {
        Intent intent = new Intent(this, ColumnActivity.class);
        intent.putExtra(TYPE, TYPE_SUBCOLUMN);
        new Builder(this)
                .setItems(new String[]{"直接显示子栏目", "根据code请求网络重新获取子栏目"}, (dialog, which) -> {
                    switch (which) {
                        case 0:
                            setTitle("子栏目列表");
                            List<? extends ColumnGrp> childColumns = columnGrp.getChildColumns();
                            if (!ListUtil.isEmpty(childColumns)) {
                                mColumnGrpAdapter.setDataList(childColumns);
                            } else {
                                showToast("子栏目为空");
                            }
                            break;
                        case 1:
                            intent.putExtra(CODE, columnGrp.getCode());
                            startActivity(intent);
                            break;
                        default:
                    }
                }).create().show();
    }

    @Override
    public void initArgs() {
        super.initArgs();
        Intent intent = getIntent();
        if (intent != null) {
            mType = intent.getIntExtra(TYPE, TYPE_ROOT);
            mCode = intent.getStringExtra(CODE);
        }
    }

    @Override
    public void initData() {
        String zone = mEtColumnZone.getText().toString().trim();
        getData(zone);
    }

    private void getData(String zone) {
        mOperationRequest = new OperationRequest();
        mOperationRequest.setTag(this.toString());
        switch (mType) {
            case TYPE_ROOT:
                showColumnTree(zone);
                break;
            case TYPE_SUBCOLUMN:
                mOperationRequest.getSubcolumnList(mCode, new HttpCallback<List<Column>>() {
                    @Override
                    public void onSuccess(List<Column> columns) {
                        setTitle("子栏目列表");
                        if (!ListUtil.isEmpty(columns)) {
                            mColumnGrpAdapter.setDataList(columns);
                        } else {
                            showToast("子栏目为空");
                        }
                    }

                    @Override
                    public void onError(ApiException exception) {
                        showError("获取子栏目错误", exception);
                    }
                });
                break;
            case TYPE_COLUMN_MEMBER:
                mOperationRequest.getColumnMemberList(mCode, new HttpCallback<List<ColumnMember>>() {
                    @Override
                    public void onSuccess(List<ColumnMember> columnMembers) {
                        setTitle("栏目成员列表");
                        if (!ListUtil.isEmpty(columnMembers)) {
                            mColumnGrpAdapter.setDataList(columnMembers);
                        } else {
                            showToast("栏目成员为空");
                        }
                    }

                    @Override
                    public void onError(ApiException exception) {
                        showError("获取栏目成员错误", exception);
                    }
                });
                break;
            default:
        }

    }

    /**
     * 显示栏目树
     */
    private void showColumnTree(String zone) {
        //获取栏目组的树结构，包括所有根栏目和子栏目
        boolean isWithMember = mSwitchWithMember.isChecked();
        mOperationRequest.getColumnTree(isWithMember, zone, new HttpCallback<List<ColumnGrp>>() {
            @Override
            public void onSuccess(List<ColumnGrp> columnGrps) {
                setTitle("根栏目组列表");
                if (columnGrps != null && columnGrps.size() > 0) {
                    mColumnGrpAdapter.setDataList(columnGrps);
                } else {
                    showToast("栏目数为空");
                }
            }

            @Override
            public void onError(ApiException exception) {
                showToast("网络请求错误，错误码：" + exception.getCode() + ", 错误信息：" + exception.getMessage());

            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mOperationRequest.cancel(this.toString());
    }
}
