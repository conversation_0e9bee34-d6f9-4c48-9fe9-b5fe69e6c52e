package com.kaolafm.report.event;

import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> on 2019-07-23.
 */

public class PlayerUiControlReportEvent extends BaseReportEventBean {
    public static final String POSITION_PLAY_BAR = "1";
    public static final String POSITION_PLAY_FRAGEMNT = "2";
    public static final String POSITION_WIDGET = "3";

    public static final String TYPE_SELECT_PAUSE = "1";
    public static final String TYPE_SELECT_PLAY = "2";
    public static final String TYPE_SELECT_PREVIOUS = "3";
    public static final String TYPE_SELECT_NEXT = "4";
    public static final String TYPE_SELECT_PLAY_LIST = "5";
    public static final String TYPE_CHANGE_PROGRESS = "6";

    public static final String CONTROL_TYPE_CLICK = "1";
    public static final String CONTROL_TYPE_VOICE = "2";
    public static final String CONTROL_TYPE_HARD_WARE = "3";
    public static final String CONTROL_TYPE_OTHER = "4";
    public static final String CONTROL_TYPE_SLIDE = "5";

    /**
     * 点击类型 1：播放后暂停；2：暂停后播放；3：上一首；4：下一首；5：列表
     */
    private String type;
    /**
     * 控制类型 1：点击；2：语音；3：方控；4：其他
     */
    private String controltype;
    /**
     * 点击位置 1：播放条；2：全屏播放器；3：widget
     */
    private String position;

    public PlayerUiControlReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_PLAYER_UI_CONTROL);
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getControltype() {
        return controltype;
    }

    public void setControltype(String controltype) {
        this.controltype = controltype;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }
}
