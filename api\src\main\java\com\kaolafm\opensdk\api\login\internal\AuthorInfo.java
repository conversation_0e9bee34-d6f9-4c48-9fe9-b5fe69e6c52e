package com.kaolafm.opensdk.api.login.internal;

/**
 * 登录、注册成功返回的用户授权信息
 *
 * <AUTHOR>
 * @date 2018/8/2
 */

public class AuthorInfo {

    private String msg;

    /**授权状态，等于20000表示成功*/
    private String code;

    private String token;

    private String userId;

    private String wechatDeviceId;

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getWechatDeviceId() {
        return wechatDeviceId;
    }

    public void setWechatDeviceId(String wechatDeviceId) {
        this.wechatDeviceId = wechatDeviceId;
    }
}
