package com.kaolafm.opensdk.http.socket;


import com.kaolafm.opensdk.log.Logging;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


/**
 * event循环线程。所有的非后台的任务都在该线程中执行
 */
public class EventThread extends Thread {

    private static final String TAG = "EventThread";

    private static final ThreadFactory THREAD_FACTORY = new ThreadFactory() {
        @Override
        public Thread newThread(Runnable runnable) {
            thread = new EventThread(runnable);
            thread.setName("EventThread");
            thread.setDaemon(Thread.currentThread().isDaemon());
            return thread;
        }
    };

    private static EventThread thread;

    private static ExecutorService service;

    private static int counter = 0;


    private EventThread(Runnable runnable) {
        super(runnable);
    }

    /**
     * check if the current thread is EventThread.
     * 当前线程是不是EventThread。true表示是
     *
     * @return true if the current thread is EventThread.
     */
    public static boolean isCurrent() {
        return currentThread() == thread;
    }

    /**
     * Executes a task in EventThread.
     * 执行一个任务
     *
     * @param task
     */
    public static void exec(Runnable task) {
        if (isCurrent()) {
            task.run();
        } else {
            nextTick(task);
        }
    }

    /**
     * Executes a task on the next loop in EventThread.
     * 线程池执行一个任务。
     * @param task
     */
    public static void nextTick(final Runnable task) {
        ExecutorService executor;
        synchronized (EventThread.class) {
            counter++;
            if (service == null) {
                service = new ThreadPoolExecutor(1, 1, 0L, TimeUnit.MILLISECONDS,
                        new LinkedBlockingQueue<>(), THREAD_FACTORY);
            }
            executor = service;
        }

        executor.execute(() -> {
            try {
                task.run();
            } catch (Throwable t) {
                Logging.e("Task threw exception: %s", t);
                throw t;
            } finally {
                synchronized (EventThread.class) {
                    counter--;
                    if (counter == 0) {
                        service.shutdown();
                        service = null;
                        thread = null;
                    }
                }
            }
        });
    }
}
