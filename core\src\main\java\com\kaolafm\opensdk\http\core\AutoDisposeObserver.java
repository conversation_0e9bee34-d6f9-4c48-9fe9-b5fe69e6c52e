package com.kaolafm.opensdk.http.core;

import com.kaolafm.opensdk.http.error.ApiException;

import java.util.concurrent.atomic.AtomicReference;

import io.reactivex.disposables.Disposable;
import io.reactivex.internal.disposables.DisposableHelper;
import io.reactivex.internal.util.EndConsumerHelper;

/**
 * 用于自定义Observer、SingleObserver、DisposableSubscriber等
 * 请求结束后可以自动去掉的Observer
 * <AUTHOR>
 * @date 2018/6/11
 */
class AutoDisposeObserver<T> implements CompositeObserver<T> {

    private final AtomicReference<Disposable> mDisposableAtomicReference = new AtomicReference<>();

    private final HttpCallback<T> mCallback;

    AutoDisposeObserver(HttpCallback<T> callback) {
        mCallback = callback;
    }

    @Override
    public void dispose() {
        DisposableHelper.dispose(mDisposableAtomicReference);
    }

    @Override
    public boolean isDisposed() {
        return mDisposableAtomicReference.get() == DisposableHelper.DISPOSED;
    }

    @Override
    public void onSubscribe(Disposable d) {
        if (EndConsumerHelper.setOnce(this.mDisposableAtomicReference, d, getClass())) {
            onStart();
        }
    }

    protected void onStart() {
    }

    @Override
    public void onSuccess(T t) {
        success(t);
    }

    @Override
    public void onNext(T t) {
        success(t);
    }

    @Override
    public void onError(Throwable e) {
        error(e);
    }

    @Override
    public void onComplete() {
        dispose();
    }

    private void success(T s) {
        if (mCallback != null) {
            mCallback.onSuccess(s);
        }
        dispose();
    }

    private void error(Throwable e) {
        if (mCallback != null) {
            if (e instanceof ApiException) {
                mCallback.onError((ApiException) e);
            } else {
                mCallback.onError(new ApiException(e.getMessage()));
            }
        }
        dispose();
    }

}
