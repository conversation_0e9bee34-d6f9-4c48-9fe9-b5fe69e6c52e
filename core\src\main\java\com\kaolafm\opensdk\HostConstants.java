package com.kaolafm.opensdk;

import com.kaolafm.base.utils.SpUtil;
import com.kaolafm.core.BuildConfig;
import com.kaolafm.opensdk.account.token.KaolaAccessToken;

/**
 * <AUTHOR>
 * @date 2020-03-13
 */
public class HostConstants {

    private static final String HOST_SUFFIX = ".radio.cn";
    public static final String HOST_HTTPS = "https://";
    public static final String HOST_HTTP = "http://";

    // API 接口服务
    private static final String HOST_PREFIX_API = "iovopen";
    public static final String OPEN_KAOLA_HOST = getOpenKaolaHost();

    // SOCKET 接口服务 API长连接
    private static final String HOST_PREFIX_SOCKET = "iovwsopen";
    //    public static final String SOCKET_HOST = HOST_PREFIX_SOCKET + HOST_SUFFIX + "/" + getLastSuffix();
    public static final String SOCKET_HOST = getSocketHost();

    // 数据上报服务
    private static final String HOST_PREFIX_REPORT = "iovmsg";
    public static final String REPORT_HOST = HOST_PREFIX_REPORT + HOST_SUFFIX;

    // 搜索服务
    private static final String HOST_PREFIX_SEARCH = "iovsearch";
    public static final String SEARCH_HTTPS_HOST = getSearchHttpsHost();

    // 推荐服务
    private static final String HOST_PREFIX_RECOMMEND = "iovrec";
    public static final String EMERGENCY_HTTPS_HOST = getEmergencyHttpsHost();


    // 广告接口服务
    private static final String HOST_PREFIX_AD = "iovae";
    public static final String AD_ENGINE_HOST = HOST_HTTPS + HOST_PREFIX_AD + HOST_SUFFIX;

    // 消息网关服务长连接
    public static final String HOST_PREFIX_MSG = "iovws";
    public static final String MSG_HOST = HOST_PREFIX_MSG + HOST_SUFFIX + "/" + getLastSuffix();

    // 广告数据上报
    private static final String HOST_PREFIX_AD_REPROT = "iovat";
    public static final String AD_REPROT_HOST = HOST_PREFIX_AD_REPROT + HOST_SUFFIX;

    // 广告长连接
    private static final String HOST_PREFIX_AD_SOCKET = "iovaew";
    //    public static final String AD_SOCKET_HOST = HOST_PREFIX_AD_SOCKET + HOST_SUFFIX;
    public static final String AD_SOCKET_HOST = getAdSocketHost();
    // OLD attention：这些域名的test接口目前还没有，必须打release包才能正常获取数据
    // 没找到替换的地方
    public static final String SEARCH_HTTP_HOST = getSearchHttpHost();

    public static String getAdSocketHost(){
        if (isTest()){
            return "iovaew-test.radio.cn";
        }
        return HOST_HTTPS + HOST_PREFIX_AD_SOCKET + HOST_SUFFIX;
    }

    public static String getSearchHttpsHost(){
        if (isTest()){
            return "iovsearch-test.radio.cn";
        }
        return HOST_PREFIX_SEARCH + HOST_SUFFIX;
    }

    public static String getEmergencyHttpsHost(){
        if (isTest()){
            return "iovrec-test.radio.cn";
        }
        return HOST_PREFIX_RECOMMEND + HOST_SUFFIX;
    }

    public static String getOpenKaolaHost(){
        if (isTest()) {
            return "iovopen-test.radio.cn";
        }
        if (isPrelease()) {
            return "iovopen-prelease.radio.cn";
        }
        return HOST_PREFIX_API + HOST_SUFFIX;
    }

    public static String getSearchHttpHost(){
        if (isPrelease()){
            return "api.search.kaolafm.com";
        }
        return "api.iovsearch.radio.cn";
    }

    public static final String MALL_HOST = getMallHost();

    public static String getMallHost() {
        if (isTest()){
            return "iovmall-test.radio.cn";
        }
        if (isPrelease()){
            return "iovmall-prelease.radio.cn";
        }
        return "shop.kaolafm.com";
    }

    public String ReportBigDataApiUrl = "https://reportv2.radio.cn/" + (isTest() ? "test" : "collection") + "?";

    public static final String ENV = "env";

    public static void setRelease() {
        if (BuildConfig.DOMAIN_TYPE.contains("test")) {
            if (isRelease()) {
                SpUtil.putBoolean(ENV, false);
            } else {
                SpUtil.putBoolean(ENV, true);
            }
        }
        KaolaAccessToken kaolaAccessToken = new KaolaAccessToken();
        kaolaAccessToken.clear();
    }

    public static boolean isTest() {
        return !isRelease();
    }

    /**
     * 默认false，表示是测试环境
     */
    public static boolean isRelease() {
        if (BuildConfig.DOMAIN_TYPE.contains("test")) {
            return SpUtil.getBoolean(ENV, false);
        } else {
            return true;
        }
    }

    public static boolean isPrelease() {
        return BuildConfig.DOMAIN_TYPE.contains("prelease");
    }

    public static String getPreleaseStr() {
        if (BuildConfig.DOMAIN_TYPE.contains("prelease")) {
            return "-prelease";
        }
        return "";
    }

    private static String getLastSuffix() {
        return (BuildConfig.API_VERSION.equals("v2") ? "open" : "internal");
    }

    public static String getSocketHost() {
        if (isTest()) {
            return "iovws-test.radio.cn/" + getLastSuffix();
        }
        if (isPrelease()) {
            return "iovwsopen-prelease.radio.cn";
        }
        return "ws.kaolafm.com/" + getLastSuffix();
    }
}
