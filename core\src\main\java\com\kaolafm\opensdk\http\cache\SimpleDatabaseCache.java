package com.kaolafm.opensdk.http.cache;

import androidx.annotation.Nullable;

import com.kaolafm.opensdk.http.download.DownloadRequest;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020-02-19
 */
public class SimpleDatabaseCache implements DatabaseCache<String, DownloadRequest> {

    @Override
    public int size() {
        return 0;
    }

    @Override
    public int getMaxSize() {
        return 0;
    }

    @Nullable
    @Override
    public DownloadRequest get(String key) {
        return null;
    }

    @Nullable
    @Override
    public DownloadRequest put(String key, DownloadRequest value) {
        return null;
    }

    @Nullable
    @Override
    public DownloadRequest remove(String key) {
        return null;
    }

    @Override
    public boolean containsKey(String key) {
        return false;
    }

    @Override
    public Set<String> keySet() {
        return null;
    }

    @Override
    public void clear() {

    }
}
