package com.kaolafm.opensdk.player.logic.playlist.util;

import com.kaolafm.opensdk.api.broadcast.BroadcastDetails;
import com.kaolafm.opensdk.api.broadcast.ProgramDetails;
import com.kaolafm.opensdk.api.feature.model.FeatureAudioDetails;
import com.kaolafm.opensdk.api.feature.model.FeatureDetails;
import com.kaolafm.opensdk.api.live.model.LiveInfoDetail;
import com.kaolafm.opensdk.api.media.model.AlbumDetails;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.api.media.model.RadioDetails;
import com.kaolafm.opensdk.player.logic.model.AlbumPlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.BroadcastPlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.CustomPlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.FeaturePlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.LivePlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.RadioPlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.TVPlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.item.AlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.FeaturePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.LivePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.RadioPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.TVPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

/**
 * <AUTHOR> on 2019/4/1.
 */

@Deprecated
public class PlayerBuilderUtil {

    public static AlbumPlayerBuilder toAlbumPlayerBuilder(AlbumDetails albumDetails) {
        if (albumDetails == null) {
            return null;
        }
        AlbumPlayerBuilder playerBuilder = new AlbumPlayerBuilder();
        playerBuilder.setId(String.valueOf(albumDetails.getId()));
        return playerBuilder;
    }

    public static AlbumPlayerBuilder toAlbumPlayerBuilder(AudioDetails audioDetails) {
        if (audioDetails == null) {
            return null;
        }
        AlbumPlayerBuilder playerBuilder = new AlbumPlayerBuilder();
        playerBuilder.setId(String.valueOf(audioDetails.getAlbumId()));
        playerBuilder.setChildId(String.valueOf(audioDetails.getAudioId()));
        return playerBuilder;
    }

    public static RadioPlayerBuilder toRadioPlayerBuilder(RadioDetails radioDetails) {
        if (radioDetails == null) {
            return null;
        }
        RadioPlayerBuilder playerBuilder = new RadioPlayerBuilder();
        playerBuilder.setId(String.valueOf(radioDetails.getId()));
        return playerBuilder;
    }

    public static RadioPlayerBuilder toRadioPlayerBuilder(long radioId, AudioDetails audioDetails) {
        if (audioDetails == null || radioId <= 0) {
            return null;
        }
        RadioPlayerBuilder playerBuilder = new RadioPlayerBuilder();
        playerBuilder.setId(String.valueOf(radioId));
        playerBuilder.setChildId(String.valueOf(audioDetails.getAlbumId()));
        return playerBuilder;
    }

    public static TVPlayerBuilder toTVPlayerBuilder(BroadcastDetails broadcastDetails) {
        if (broadcastDetails == null) {
            return null;
        }
        TVPlayerBuilder playerBuilder = new TVPlayerBuilder();
        playerBuilder.setId(String.valueOf(broadcastDetails.getBroadcastId()));
        return playerBuilder;
    }

    public static TVPlayerBuilder toTVPlayerBuilder(ProgramDetails programDetails) {
        if (programDetails == null) {
            return null;
        }

        TVPlayerBuilder playerBuilder = new TVPlayerBuilder();
        playerBuilder.setId(String.valueOf(programDetails.getBroadcastId()));
        playerBuilder.setChildId(String.valueOf(programDetails.getProgramId()));
        return playerBuilder;
    }


    public static BroadcastPlayerBuilder toBroadcastPlayBuilder(BroadcastDetails broadcastDetails) {
        if (broadcastDetails == null) {
            return null;
        }
        BroadcastPlayerBuilder playerBuilder = new BroadcastPlayerBuilder();
        playerBuilder.setId(String.valueOf(broadcastDetails.getBroadcastId()));
        return playerBuilder;
    }

    public static BroadcastPlayerBuilder toBroadcastPlayBuilder(ProgramDetails programDetails) {
        if (programDetails == null) {
            return null;
        }

        BroadcastPlayerBuilder playerBuilder = new BroadcastPlayerBuilder();
        playerBuilder.setId(String.valueOf(programDetails.getBroadcastId()));
        playerBuilder.setChildId(String.valueOf(programDetails.getProgramId()));
        return playerBuilder;
    }

    public static LivePlayerBuilder toLivePlayBuilder(LiveInfoDetail liveInfoDetail) {
        if (liveInfoDetail == null) {
            return null;
        }
        LivePlayerBuilder playerBuilder = new LivePlayerBuilder();
        playerBuilder.setId(String.valueOf(liveInfoDetail.getProgramId()));
        playerBuilder.setChildId(String.valueOf(liveInfoDetail.getProgramId()));

        return playerBuilder;
    }

    public static FeaturePlayerBuilder toFeaturePlayBuilder(FeatureDetails featureDetails) {
        if (featureDetails == null) {
            return null;
        }
        FeaturePlayerBuilder playerBuilder = new FeaturePlayerBuilder();
        playerBuilder.setId(String.valueOf(featureDetails.getId()));
        return playerBuilder;
    }

    public static FeaturePlayerBuilder toFeaturePlayBuilder(FeatureAudioDetails featureAudioDetails) {
        if (featureAudioDetails == null) {
            return null;
        }

        FeaturePlayerBuilder playerBuilder = new FeaturePlayerBuilder();
        playerBuilder.setId(String.valueOf(featureAudioDetails.getFeatureId()));
        playerBuilder.setChildId(String.valueOf(featureAudioDetails.getAudioId()));
        return playerBuilder;
    }

    public static PlayerBuilder toPlayBuilder(PlayItem playItem) {
        if (playItem == null) {
            return null;
        }
        CustomPlayerBuilder playerBuilder;
        if (playItem instanceof RadioPlayItem) {
            playerBuilder = new RadioPlayerBuilder();
            playerBuilder.setId(String.valueOf(((RadioPlayItem) playItem).getRadioInfoData().getRadioId()));
            playerBuilder.setChildId(String.valueOf(playItem.getAudioId()));
            return playerBuilder;
        } else if (playItem instanceof BroadcastPlayItem) {
            playerBuilder = new BroadcastPlayerBuilder();
            playerBuilder.setId(String.valueOf(((BroadcastPlayItem) playItem).getInfoData().getAlbumId()));
        } else if (playItem instanceof TVPlayItem) {
            playerBuilder = new TVPlayerBuilder();
            playerBuilder.setId(String.valueOf(((TVPlayItem) playItem).getInfoData().getAlbumId()));
        } else if (playItem instanceof LivePlayItem) {
            playerBuilder = new LivePlayerBuilder();
            playerBuilder.setId(String.valueOf(((LivePlayItem) playItem).getInfoData().getAlbumId()));
        } else if (playItem instanceof AlbumPlayItem) {
            playerBuilder = new AlbumPlayerBuilder();
            playerBuilder.setId(String.valueOf(((AlbumPlayItem) playItem).getInfoData().getAlbumId()));
        } else if (playItem instanceof FeaturePlayItem) {
            playerBuilder = new FeaturePlayerBuilder();
            playerBuilder.setId(String.valueOf(((FeaturePlayItem) playItem).getInfoData().getAlbumId()));
        } else {
            playerBuilder = new CustomPlayerBuilder();
        }

        playerBuilder.setChildId(String.valueOf(playItem.getAudioId()));
        playerBuilder.setSeekPosition(playItem.getPosition());
        return playerBuilder;
    }
}
