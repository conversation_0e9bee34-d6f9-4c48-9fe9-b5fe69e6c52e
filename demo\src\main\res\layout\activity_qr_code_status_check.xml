<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_kaola_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tv_qrcode_id"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        android:text="请输入需要查询的二维码id"/>

    <EditText
        android:id="@+id/et_qrcode_id"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        app:layout_constraintTop_toBottomOf="@id/tv_qrcode_id"
        android:text="12XF2204060070609164339305"/>

    <Button
        android:id="@+id/btn_qr_status_get"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="获取支付结果"
        app:layout_constraintTop_toBottomOf="@id/et_qrcode_id" />

    <TextView
        android:id="@+id/tv_qr_status_history"
        android:layout_width="wrap_content"
        android:layout_height="170dp"
        android:lineSpacingExtra="5dp"
        android:paddingStart="10dp"
        android:scrollbars="vertical"
        android:textColor="@color/colorAccent"
        android:textSize="10sp"
        app:layout_constraintTop_toBottomOf="@id/btn_qr_status_get"/>

</androidx.constraintlayout.widget.ConstraintLayout>
