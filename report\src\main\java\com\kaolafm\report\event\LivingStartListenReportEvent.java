package com.kaolafm.report.event;

import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> on 2019-07-23.
 */

public class LivingStartListenReportEvent extends BaseReportEventBean{
    public static final String POSITION_RECOMMENT = "1";
    public static final String POSITION_AI_RADIO = "2";
    public static final String POSITION_PUSH = "3";

    public static final String STATUS_LIVING = "1";
    public static final String STATUS_PLAYBACK = "2";
    /**
     * 直播标识id
     */
    private String live_id;
    /**
     * 直播计划id
     */
    private String plan_id;
    /**
     * 主持人id
     */
    private String live_manager_uid;
    /**
     * 进入直播的位置 1：运营位；2：AI电台；3：电台直播开播PUSH；
     */
    private String position;
    /**
     * 直播状态 1：直播中；2：回放中
     */
    private String status;

    public LivingStartListenReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_LIVING_START_LISTEN);
    }

    public String getLive_id() {
        return live_id;
    }

    public void setLive_id(String live_id) {
        this.live_id = live_id;
    }

    public String getPlan_id() {
        return plan_id;
    }

    public void setPlan_id(String planId) {
        this.plan_id = planId;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getLive_manager_uid() {
        return live_manager_uid;
    }

    public void setLive_manager_uid(String live_manager_uid) {
        this.live_manager_uid = live_manager_uid;
    }
}
