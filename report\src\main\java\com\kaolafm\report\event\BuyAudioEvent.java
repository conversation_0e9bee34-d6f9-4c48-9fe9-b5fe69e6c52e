package com.kaolafm.report.event;

import com.kaolafm.report.util.ReportConstants;

/**
 * @Package: com.kaolafm.report.event
 * @Description: 购买单曲
 * @Author: Maclay
 * @Date: 14:24
 */
public class BuyAudioEvent extends BaseReportEventBean {
    private String audioid;
    private String albumid;
    private String remarks1;
    private String tag;

    public BuyAudioEvent() {
        setEventcode(ReportConstants.EVENT_ID_BUY_AUDIO);
    }

    public String getAudioid() {
        return audioid;
    }

    public void setAudioid(String audioid) {
        this.audioid = audioid;
    }

    public String getAlbumid() {
        return albumid;
    }

    public void setAlbumid(String albumid) {
        this.albumid = albumid;
    }

    public String getRemarks1() {
        return remarks1;
    }

    public void setRemarks1(String remarks1) {
        this.remarks1 = remarks1;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    @Override
    public String toString() {
        return "BuyAudioEvent{" +
                "audioid='" + audioid + '\'' +
                "albumid='" + albumid + '\'' +
                ", remarks1='" + remarks1 + '\'' +
                ", tag='" + tag + '\'' +
                '}';
    }
}
