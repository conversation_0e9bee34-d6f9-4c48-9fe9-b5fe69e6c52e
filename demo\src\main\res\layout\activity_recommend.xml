<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".recommed.RecommendActivity">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="400dp"
        android:padding="16dp">

        <EditText
            android:id="@+id/et_code"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:hint="输入场景code (默认1061)" />

        <Button
            android:id="@+id/btn_code_recommend"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@+id/et_code"
            android:text="场景推荐" />

        <LinearLayout
            android:id="@+id/layout_present"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/btn_code_recommend"
            android:layout_marginTop="20dp"
            android:background="#cccccc"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="MPV	301
SUV	302
豪华-大型车	303
豪华-中型车（C级）	304
中型车（B级）	305
紧凑型车（A级)	306
小型车（A0级）	307
" />

            <EditText
                android:id="@+id/et_car_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:hint="输入车型" />


            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="凌晨	101	00:00-04:59:59
清晨	102	05:00-06:59:59
早高峰	103	07:00-09:59:59
上午	104	10:00-10:59:59
中午	105	11:00-13:59:59
午后	106	14:00-15:59:59
下午	107	16:00-16:59:59
晚高峰	108	17:00-19:59:59
晚间	109	20:00-21:59:59
深夜	110	22:00-23:59:59
" />

            <EditText
                android:id="@+id/et_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:hint="输入时间" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="男	401
女	402
" />

            <EditText
                android:id="@+id/et_sex"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:hint="输入性别" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="0-17岁	201
18-25岁	202
26-30岁	203
31-35岁	204
36-40岁	205
41岁以上	206

" />

            <EditText
                android:id="@+id/et_age"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:hint="输入年龄" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="高温	501	车外温度大于35度\n
低温	502	车外温度小于0度\n
大风	503	风力大于6级小于12级\n
飓风	504	风力大于12级\n
小雨	505	0.1~9.9mm/d\n
中雨	506	10~24.9mm/d\n
大雨	507	25~49.9mm/d\n
暴雨	508	50.0~99.9mm/d\n
小雪	509	面积雪深度在3厘米以下，降水量级为24小时降雪量在0.1～2.4毫米之间。\n
中雪	510	地面积雪深度为3～5厘米，24小时降雪量达2.5～4.9毫米\n
大雪	511	地面积雪深度等于或大于5厘米，24小时降雪量达5.0～9.9毫米。\n
暴雪	512	24小时降雪量达到10.0～19.9毫米时为暴雪" />

            <EditText
                android:id="@+id/et_weather"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:hint="输入天气" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="堵车	601	行车的速度小于20km/h \n 快速	602	行车的速度大于100km/h" />

            <EditText
                android:id="@+id/et_speed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:hint="输入速度" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="长途出游	701	导航目的地距离>300KM" />

            <EditText
                android:id="@+id/et_special"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:hint="输入特殊" />

            <Button
                android:id="@+id/btn_present_recommend"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@+id/et_c"
                android:text="当前推荐" />
        </LinearLayout>

        <EditText
            android:id="@+id/et_result"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/layout_present"
            android:layout_marginTop="20dp"
            android:textColor="#ffffff"
            android:background="#111111"
            android:hint="结果在此" />
    </RelativeLayout>

</ScrollView>