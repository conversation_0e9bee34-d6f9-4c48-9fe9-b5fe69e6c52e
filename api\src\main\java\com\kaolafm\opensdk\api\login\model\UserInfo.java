package com.kaolafm.opensdk.api.login.model;

import com.google.gson.annotations.SerializedName;

/**
 * 听伴用户信息。用听伴手机App扫码获取到的用户信息
 * <AUTHOR>
 * @date 2018/9/14
 */

public class UserInfo {

    /** 听伴用户昵称*/
    @SerializedName("nickname")
    private String nickName;

    /** 听伴用户头像*/
    @SerializedName("avatar")
    private String avatar;

    /** 听伴用户性别*/
    @SerializedName("gender")
    private String gender;

    /** 听伴用户地区*/
    @SerializedName("userArea")
    private String userArea;

    /** 是否vip 1：是，0:否 */
    private int vip;

    /** 到期时间xxxx-xx-xx */
    private String vipTime;

    /** n天后到期 */
    private int vipRemainDays;

    @Deprecated
    /** 是否展示老用户提示, 当用户为听伴老用户，并且是首次登陆时，返回温馨提示。1：是，0:否 */
    private int oldUser;

    @Deprecated
    /** 老用户温馨提示 */
    private String tips;

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getUserArea() {
        return userArea;
    }

    public void setUserArea(String userArea) {
        this.userArea = userArea;
    }

    public int getOldUser() {
        return oldUser;
    }

    public void setOldUser(int oldUser) {
        this.oldUser = oldUser;
    }

    public String getTips() {
        return tips;
    }

    public void setTips(String tips) {
        this.tips = tips;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public int getVip() {
        return vip;
    }

    public void setVip(int vip) {
        this.vip = vip;
    }

    public String getVipTime() {
        return vipTime;
    }

    public void setVipTime(String vipTime) {
        this.vipTime = vipTime;
    }

    public int getVipRemainDays() {
        return vipRemainDays;
    }

    public void setVipRemainDays(int vipRemainDays) {
        this.vipRemainDays = vipRemainDays;
    }

    @Override
    public String toString() {
        return "UserInfo{" +
                "nickName='" + nickName + '\'' +
                ", avatar='" + avatar + '\'' +
                ", gender='" + gender + '\'' +
                ", userArea='" + userArea + '\'' +
                ", vip=" + vip +
                ", vipTime='" + vipTime + '\'' +
                ", vipRemainDays=" + vipRemainDays +
                ", oldUser=" + oldUser +
                ", tips='" + tips + '\'' +
                '}';
    }
}
