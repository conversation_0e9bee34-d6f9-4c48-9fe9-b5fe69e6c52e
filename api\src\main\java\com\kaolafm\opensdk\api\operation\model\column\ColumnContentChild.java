package com.kaolafm.opensdk.api.operation.model.column;

import com.kaolafm.opensdk.api.operation.model.ImageFile;

import java.util.Map;

public class ColumnContentChild {
    private long id;
    private String code;
    private String title;
    private String description;
    private int cornerMark;
    private String type;
    private String callBack;
    private String outputMode;
    private String recommendReason;
    private String tag;
    private long updateDate;//更新时间
    /**
     * 图片信息集合
     */
    private Map<String, ImageFile> imageFiles;
    private String extInfo;
    private String playTimes;
    private String playCount;
    private String areaCodes;
    private int canPlay;
    private Object columnMemberChildContents;
    private String childrenCode;
    private String broadcastSort;
    private int fine;
    private int vip;
    private String qqMusicType;
    private String freq;
    private String broadcastType;
    private String broadcastLevel;
    private String anchor;
    private String categoryCode;
    private String contentType;
    private String pageId;
    private String keyword;
    private String url;

    public long getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(long updateDate) {
        this.updateDate = updateDate;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getCornerMark() {
        return cornerMark;
    }

    public void setCornerMark(int cornerMark) {
        this.cornerMark = cornerMark;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCallBack() {
        return callBack;
    }

    public void setCallBack(String callBack) {
        this.callBack = callBack;
    }

    public String getOutputMode() {
        return outputMode;
    }

    public void setOutputMode(String outputMode) {
        this.outputMode = outputMode;
    }

    public String getRecommendReason() {
        return recommendReason;
    }

    public void setRecommendReason(String recommendReason) {
        this.recommendReason = recommendReason;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public Map<String, ImageFile> getImageFiles() {
        return imageFiles;
    }

    public void setImageFiles(Map<String, ImageFile> imageFiles) {
        this.imageFiles = imageFiles;
    }

    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }

    public String getPlayTimes() {
        return playTimes;
    }

    public void setPlayTimes(String playTimes) {
        this.playTimes = playTimes;
    }

    public String getPlayCount() {
        return playCount;
    }

    public void setPlayCount(String playCount) {
        this.playCount = playCount;
    }

    public String getAreaCodes() {
        return areaCodes;
    }

    public void setAreaCodes(String areaCodes) {
        this.areaCodes = areaCodes;
    }

    public int getCanPlay() {
        return canPlay;
    }

    public void setCanPlay(int canPlay) {
        this.canPlay = canPlay;
    }

    public Object getColumnMemberChildContents() {
        return columnMemberChildContents;
    }

    public void setColumnMemberChildContents(Object columnMemberChildContents) {
        this.columnMemberChildContents = columnMemberChildContents;
    }

    public String getChildrenCode() {
        return childrenCode;
    }

    public void setChildrenCode(String childrenCode) {
        this.childrenCode = childrenCode;
    }

    public String getBroadcastSort() {
        return broadcastSort;
    }

    public void setBroadcastSort(String broadcastSort) {
        this.broadcastSort = broadcastSort;
    }

    public int getFine() {
        return fine;
    }

    public void setFine(int fine) {
        this.fine = fine;
    }

    public int getVip() {
        return vip;
    }

    public void setVip(int vip) {
        this.vip = vip;
    }

    public String getQqMusicType() {
        return qqMusicType;
    }

    public void setQqMusicType(String qqMusicType) {
        this.qqMusicType = qqMusicType;
    }

    public String getFreq() {
        return freq;
    }

    public void setFreq(String freq) {
        this.freq = freq;
    }

    public String getBroadcastType() {
        return broadcastType;
    }

    public void setBroadcastType(String broadcastType) {
        this.broadcastType = broadcastType;
    }

    public String getBroadcastLevel() {
        return broadcastLevel;
    }

    public void setBroadcastLevel(String broadcastLevel) {
        this.broadcastLevel = broadcastLevel;
    }

    public String getAnchor() {
        return anchor;
    }

    public void setAnchor(String anchor) {
        this.anchor = anchor;
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getPageId() {
        return pageId;
    }

    public void setPageId(String pageId) {
        this.pageId = pageId;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
