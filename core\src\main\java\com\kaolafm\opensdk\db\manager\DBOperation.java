package com.kaolafm.opensdk.db.manager;

import com.kaolafm.opensdk.db.OnQueryListener;

import java.util.List;

import io.reactivex.Single;

/**
 * <AUTHOR>
 * @date 2020/8/10
 */
public interface DBOperation<T> {


    void updateTable(int oldVersion, int newVersion);

    /**
     * 插入单个对象
     */
    void insert(T t);

    /**
     * 插入一个对象集合
     */
    void insert(final List<T> list);

    /**
     * 同步插入单个对象
     */
    boolean insertSync(T t);

    /**
     * 同步插入一个对象集合
     */
    boolean insertSync(final List<T> list);

//======================保存==========================

    /**
     * 如果主键相同就更新，不相同就插入
     */
    void save(T t);

    /**
     * 保存一个对象集合。如果主键相同就更新，不相同就插入
     */
    void save(List<T> list);

    void saveSync(List<T> list);

    void saveSync( T t);
//======================更新==========================

    /**
     * 以对象形式进行数据修改
     * 其中必须要知道对象的主键ID
     */
    void update(T t);

    /**
     * 批量更新数据
     */
    void update(final List<T> list);

//======================删除==========================

    /**
     * 删除所有数据，指定类class
     */
    void deleteAll(Class<T> clazz);

    /**
     * 删除某个数据
     */
    void delete(T t);

    /**
     * 批量删除数据
     */
    void delete(final List<T> list, Class clazz);

    /**
     * 批量删除数据
     */
    void delete(final List<T> list);

//======================查询==========================

    /**
     * 获得表名
     */
    String getTableName();

    /**
     * 根据主键ID来查询，结果以回调方式显示
     */
    void queryById(long id, OnQueryListener<T> listener);

    /**
     * 查询某条件下的对象，结果以回调方式显示
     */
    void queryRaw(String where, OnQueryListener<List<T>> listener, Object... params);

    /**
     * 同步查询某条件下的对象
     */
    List<T> queryRawSync(String where, String... params);

    //-------------查询所有-----------

    /**
     * 查询所有对象
     */
    Single<List<T>> queryAll();

    /**
     * 查询所有对象， 结果以回调方式显示。
     */
    void queryAll(OnQueryListener<List<T>> listener);

    /**
     * 查询数据库数量
     */
    void queryAllCount(OnQueryListener<Long> listener);

    /**
     * 同步查询所有对象
     */
    List<T> queryAllSync();

    /**
     * 关闭数据库一般在OnDestroy中使用
     */
    void closeDataBase();
}
