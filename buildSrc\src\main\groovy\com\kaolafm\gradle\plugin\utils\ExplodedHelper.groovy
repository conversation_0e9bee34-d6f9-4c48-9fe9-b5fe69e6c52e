package com.kaolafm.gradle.plugin.utils

import com.kaolafm.gradle.plugin.model.AndroidArchiveLibrary
import org.gradle.api.Project

/**
 * process jars and classes
 * Created by <PERSON>igi on 2017/1/20.
 * Modified by k<PERSON><PERSON> on 2018/12/18
 */
class ExplodedHelper {

    static void processLibsIntoLibs(Project project,
                                    Collection<AndroidArchiveLibrary> androidLibraries, Collection<File> jarFiles,
                                    File folderOut) {
        for (androidLibrary in androidLibraries) {
            if (!androidLibrary.rootFolder.exists()) {
                Util.logInfo('[warning]' + androidLibrary.rootFolder + ' not found!')
                continue
            }
            if (androidLibrary.localJars.isEmpty()) {
                Util.logInfo("Not found jar file, Library:${androidLibrary.name}")
            } else {
                Util.logInfo("Merge ${androidLibrary.name} jar file, Library:${androidLibrary.name}")
            }
            androidLibrary.localJars.each {
                Util.logInfo(it.path)
            }
            project.copy {
                from(androidLibrary.localJars)
                into folderOut
            }
        }
        for (jarFile in jarFiles) {
            if (!jarFile.exists()) {
                Util.logInfo('[warning]' + jarFile + ' not found!')
                continue
            }
            Util.logInfo('copy jar from: ' + jarFile + " to " + folderOut.absolutePath)
            project.copy {
                from(jarFile)
                into folderOut
            }
        }
    }

    static void processClassesJarInfoClasses(Project project,
                                             Collection<AndroidArchiveLibrary> androidLibraries,
                                             File folderOut) {
        Util.logInfo('Merge ClassesJar')
        Collection<File> allJarFiles = new ArrayList<>()
        for (androidLibrary in androidLibraries) {
            if (!androidLibrary.rootFolder.exists()) {
                Util.logInfo('[warning]' + androidLibrary.rootFolder + ' not found!')
                continue
            }
            allJarFiles.add(androidLibrary.classesJarFile)
        }
        for (jarFile in allJarFiles) {
            println("jarFile=${jarFile}\nfolderOut=${folderOut}")
            project.copy {
                from project.zipTree(jarFile)
                into folderOut
                exclude 'META-INF/'
                exclude '**/BuildConfig.class'
                exclude '**/BuildConfig\$*.class'
            }
        }
    }

    static void processLibsIntoClasses(Project project,
                                   Collection<AndroidArchiveLibrary> androidLibraries, Collection<File> jarFiles,
                                   File folderOut) {
        Util.logInfo('Merge Libs')
        Collection<File> allJarFiles = new ArrayList<>()
        for (androidLibrary in androidLibraries) {
            if (!androidLibrary.rootFolder.exists()) {
                Util.logInfo('[warning]' + androidLibrary.rootFolder + ' not found!')
                continue
            }
            Util.logInfo('[androidLibrary]' + androidLibrary.getName())
            allJarFiles.addAll(androidLibrary.localJars)
        }
        for (jarFile in jarFiles) {
            if (!jarFile.exists()) {
                continue
            }
            allJarFiles.add(jarFile)
        }
        for (jarFile in allJarFiles) {
            project.copy {
                from project.zipTree(jarFile)
                into folderOut
                exclude 'META-INF/'
                exclude '**/R.class'
            }
        }
    }
}
