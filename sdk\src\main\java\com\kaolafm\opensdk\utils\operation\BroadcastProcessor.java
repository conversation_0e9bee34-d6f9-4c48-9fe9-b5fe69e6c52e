package com.kaolafm.opensdk.utils.operation;

import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.operation.model.category.BroadcastCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.CategoryMember;
import com.kaolafm.opensdk.api.operation.model.column.BroadcastDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;
import javax.inject.Inject;

/**
 * 在线广播 分类/栏目成员处理类
 *
 * <AUTHOR>
 * @date 2018/9/25
 */
public class BroadcastProcessor implements IOperationProcessor {

    @Inject
    public BroadcastProcessor() {
    }

    @Override
    public boolean accept(CategoryMember member) {
        return member instanceof BroadcastCategoryMember;
    }

    @Override
    public boolean accept(ColumnMember member) {
        return member instanceof BroadcastDetailColumnMember;
    }

    @Override
    public long getId(CategoryMember member) {
        return ((BroadcastCategoryMember) member).getBroadcastId();

    }

    @Override
    public long getId(ColumnMember member) {
        return ((BroadcastDetailColumnMember) member).getBroadcastId();
    }

    @Override
    public long getListenNum(CategoryMember member) {
        return ((BroadcastCategoryMember) member).getPlayTimes();
    }

    @Override
    public int getType(CategoryMember member) {
        return ResType.TYPE_BROADCAST;
    }

    @Override
    public int getType(ColumnMember member) {
        return ResType.TYPE_BROADCAST;
    }

    @Override
    public void play(CategoryMember member) {

    }

    @Override
    public void play(ColumnMember member) {

    }
}
