package com.kaolafm.opensdk.api.setting;


import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.setting.model.QRCodeBean;
import com.kaolafm.opensdk.api.setting.model.SettingConfigBean;
import com.kaolafm.opensdk.http.core.HttpCallback;

public class SettingRequest extends BaseRequest {
    SettingService mSettingsService;

    public SettingRequest() {
        mSettingsService = obtainRetrofitService(SettingService.class);
    }
    public void getSettingConfig(HttpCallback<SettingConfigBean> httpCallback) {
        doHttpDeal(mSettingsService.getSettingConfig(), baseResult -> {
            SettingConfigBean success = baseResult.getResult();
            return success;
        }, httpCallback);

    }
    public void getSettingQRCode(HttpCallback<QRCodeBean> httpCallback) {
        doHttpDeal(mSettingsService.getSettingQRCode(), baseResult -> {
            QRCodeBean result = baseResult.getResult();
            return result;
        }, httpCallback);

    }
}
