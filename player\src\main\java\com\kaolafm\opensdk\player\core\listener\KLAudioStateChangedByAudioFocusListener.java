package com.kaolafm.opensdk.player.core.listener;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-01-10 14:30
 ******************************************/
public interface KLAudioStateChangedByAudioFocusListener {
    /**
     * 因被动失去音频焦点导致音频暂停
     */
    void onAudioStatePausedByLossAudioFocus();

    /**
     * 因被动得到音频焦点导致音频播放
     */
    void onAudioStatePlayingByGainAudioFocus();
}
