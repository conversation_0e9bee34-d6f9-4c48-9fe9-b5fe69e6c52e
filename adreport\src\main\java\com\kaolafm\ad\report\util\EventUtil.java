package com.kaolafm.ad.report.util;

import com.google.gson.Gson;
import com.kaolafm.ad.report.bean.BaseAdEvent;
import com.kaolafm.ad.report.db.bean.EventData;


public class EventUtil {

    public static EventData getEventBean(BaseAdEvent baseAdEvent){
        EventData eventData = new EventData();
        eventData.setCreativeId(baseAdEvent.getCreativeId());
        eventData.setType(baseAdEvent.getEventType());
        eventData.setReportData(new Gson().toJson(baseAdEvent));
        return eventData;
    }

}
