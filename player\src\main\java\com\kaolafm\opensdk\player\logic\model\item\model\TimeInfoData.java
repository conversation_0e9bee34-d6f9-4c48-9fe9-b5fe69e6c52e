package com.kaolafm.opensdk.player.logic.model.item.model;

/**
 * 直播时间-数据类
 */
public class TimeInfoData {

    /**
     * 开始时间 1574931028000
     */
    private long startTime;

    /**
     * 结束时间 1574931028000
     */
    private long finishTime;

    /**
     * 直播使用开始时间  "2019-11-28 16:50:28"
     */
    private String beginTime;
    /**
     * 直播使用结束时间 "2019-11-28 16:50:28"
     */
    private String endTime;

    /**
     * 服务器当前时间
     */
    private long curSystemTime;


    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(long finishTime) {
        this.finishTime = finishTime;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public long getCurSystemTime() {
        return curSystemTime;
    }

    public void setCurSystemTime(long curSystemTime) {
        this.curSystemTime = curSystemTime;
    }
}
