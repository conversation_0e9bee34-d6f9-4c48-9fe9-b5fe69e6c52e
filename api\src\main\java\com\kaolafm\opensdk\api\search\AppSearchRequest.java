package com.kaolafm.opensdk.api.search;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.search.model.SearchProgramBean;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.List;

public class AppSearchRequest extends BaseRequest {
    AppSearchService mAppSearchService;

    public AppSearchRequest(){
        mAppSearchService = obtainRetrofitService(AppSearchService.class);
    }

    public void searchAllById(String keyword, String classifyId, int pagenum, int pagesize, HttpCallback<List<SearchProgramBean>> callback){
        doHttpDeal(mAppSearchService.searchAllById(keyword, classifyId,pagenum,pagesize), BaseResult::getResult, callback);
    }

    public void searchClassifyAll(HttpCallback<List<SearchClassify>> callback){
        doHttpDeal(mAppSearchService.searchClassifyAll(), BaseResult::getResult, callback);
    }
}
