package com.kaolafm.gradle.plugin.model


import org.gradle.api.Project
import org.gradle.api.Task
import org.gradle.api.artifacts.ProjectDependency
import org.gradle.api.component.SoftwareComponent
import org.gradle.api.tasks.javadoc.Javadoc

class AndroidAttachments extends MavenPublicationAttachments {

    AndroidAttachments(Project project, SDKFlavor flavor, def variant) {
        super(androidComponentFrom(project),
                androidSourcesJarTask(project, flavor, variant),
                androidArchivePath(variant))
    }

    private static SoftwareComponent androidComponentFrom(Project project) {
        return project.objects.newInstance(AndroidSoftwareComponentCompat) as SoftwareComponent
    }

    private static Task androidSourcesJarTask(Project project, SDKFlavor flavor, def variant) {
        def sourcePaths = variant.sourceSets.collect { it.javaDirectories }.flatten()
        project.configurations.implementation.allDependencies.each {
            if (it instanceof ProjectDependency) {
                def srcDirs = it.dependencyProject.android?.sourceSets?.main?.java?.srcDirs
                if (srcDirs != null) {
                    sourcePaths.addAll(srcDirs)
                }
            }
        }
        return sourcesJarTask(project, flavor, sourcePaths)
    }

    private static Task androidJavadocsJarTask(Project project, String publicationName, def variant) {
        Javadoc javadoc = project.task("javadoc${publicationName.capitalize()}", type: Javadoc) { Javadoc javadoc ->
            javadoc.source = variant.javaCompiler.source
            javadoc.classpath = variant.javaCompiler.classpath
        } as Javadoc
        return javadocsJarTask(project, publicationName, javadoc)
    }

    private static def androidArchivePath(def variant) {
        return variant.outputs[0].packageLibrary
    }
}
