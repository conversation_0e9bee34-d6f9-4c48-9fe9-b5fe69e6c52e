package com.kaolafm.opensdk;

import android.app.Application;
import android.util.Log;

import com.kaolafm.base.utils.DeviceUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.account.profile.AbstractProfileManager;
import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import javax.inject.Inject;

/**
 * SDK内部入口的基类，封装SDK 初始化/激活的公共部分。
 * 各个模块可以单独打包，也需要一起打包，只能有一个对外入口(对内入口需要多个，即真正执行逻辑的类。)，
 * 所以分开打包和合并打包的对外入口不一样，但是公共部分都一样。
 *
 * <AUTHOR>
 * @date 2020-03-05
 */
public abstract class BaseEngine<O extends Options, M extends AbstractProfileManager> implements Engine<O> {
    private static final String TAG = "BaseEngine";
    /**
     * 是否已经初始化过 true: 已经初始化过，false: 未初始化
     */
    private volatile boolean initialized = false;

    @Inject
    @AppScope
    public M mProfileManager;

    @Override
    public void init(Application application, O options, HttpCallback<Boolean> callback) {
        Log.i(TAG, "init: 开始初始化，initialized=" + initialized);
        if (!initialized) {
            try {
                Log.i(TAG, "init: 调用内部初始化方法");
                internalInit(application, options, callback);
                initialized = true;
                Log.i(TAG, "init: 初始化成功，设置initialized=true");
                if (callback != null) {
                    callback.onSuccess(true);
                }
            } catch (Exception e) {
                Log.e(TAG, "init: 初始化失败，异常类型=" + e.getClass().getName() + ", 异常信息=" + e.getMessage(), e);
                if (callback != null) {
                    callback.onError(new ApiException(1, "初始化失败: " + e.getMessage()));
                }
            }
        } else {
            Log.i(TAG, "init: 已经初始化过，无需再次初始化");
            if (callback != null) {
                callback.onSuccess(true);
            }
        }
    }

    /**
     * 真正的初始化。不需要关心各种限定条件，直接做初始化操作就行，SDK内部使用。
     *
     * @param application
     * @param options
     * @param callback
     */
    protected abstract void internalInit(Application application, O options, HttpCallback<Boolean> callback);

    @Override
    public void activate(HttpCallback<Boolean> callback) {
        Log.i(TAG, "activate: 开始激活，initialized=" + initialized + "，isActivated=" + isActivated());
        if (!initialized) {
            String errorMsg = "未初始化，无法激活";
            Log.e(TAG, "activate: " + errorMsg);
            if (callback != null) {
                callback.onError(new ApiException(2, errorMsg));
            } else {
                throw new RuntimeException(errorMsg);
            }
            return;
        }
        if (!isActivated()) {
            try {
                Log.i(TAG, "activate: 开始执行内部激活");
                internalActivate(callback);
            } catch (Exception e) {
                String errorMsg = "激活过程中发生异常: " + e.getMessage();
                Log.e(TAG, "activate: " + errorMsg, e);
                if (callback != null) {
                    callback.onError(new ApiException(3, errorMsg));
                }
            }
        } else {
            Log.i(TAG, "activate: 已经激活过，无需再次激活");
            if (callback != null) {
                callback.onSuccess(true);
            }
        }
    }

    /**
     * 真正的激活。不需要关心其他限定条件，直接调用激活就行，SDK内部使用。
     *
     * @param callback
     */
    protected abstract void internalActivate(HttpCallback<Boolean> callback);

    @Override
    public void setLocation(String lng, String lat) {
        Log.i(TAG, "setLocation: 设置位置信息，经度=" + lng + "，纬度=" + lat);
        if(mProfileManager != null){
            mProfileManager.setLongitude(lng);
            mProfileManager.setLatitude(lat);
        }else {
            Log.w(TAG, "setLongitude: mProfileManager为空，无法设置经度");
        }

    }

    @Override
    public void setLocation(String lng, String lat, String coordType) {
        Log.i(TAG, "setLocation: 设置位置信息，经度=" + lng + "，纬度=" + lat + "，坐标类型=" + coordType);
        if(mProfileManager != null){
            mProfileManager.setLongitude(lng);
            mProfileManager.setLatitude(lat);
            mProfileManager.setCoordTpye(coordType);
        }else {
            Log.w(TAG, "setLongitude: mProfileManager为空，无法设置经度");
        }
    }

    /**
     * 传入经度
     *
     * @param longitude
     * @deprecated 已过时，请使用{@link #setLocation(String, String)}
     */
    public void setLongitude(String longitude) {
        Log.i(TAG, "setLongitude: 设置经度=" + longitude);
        if (mProfileManager != null) {
            mProfileManager.setLongitude(longitude);
        } else {
            Log.w(TAG, "setLongitude: mProfileManager为空，无法设置经度");
        }
    }

    public String[] getLocation() {
        Log.i(TAG, "getLocation: 获取位置信息");
        String[] arr = null;
        if (mProfileManager != null) {
            arr = new String[2];
            arr[0] = mProfileManager.getLongitude();
            arr[1] = mProfileManager.getLatitude();
            Log.i(TAG, "getLocation: 位置信息，经度=" + arr[0] + "，纬度=" + arr[1]);
        } else {
            Log.w(TAG, "getLocation: mProfileManager为空，无法获取位置信息");
        }
        return arr;
    }

    /**
     * 传入纬度
     *
     * @param latitude
     * @deprecated 已过时，请使用{@link #setLocation(String, String)}
     */
    public void setLatitude(String latitude) {
        Log.i(TAG, "setLatitude: 设置纬度=" + latitude);
        if (mProfileManager != null) {
            mProfileManager.setLatitude(latitude);
        } else {
            Log.w(TAG, "setLatitude: mProfileManager为空，无法设置纬度");
        }
    }

    public String getDeviceId() {
        if (mProfileManager != null) {
            try {
                String deviceId = mProfileManager.getDeviceId();
                Log.i(TAG, "getDeviceId: 获取设备ID=" + deviceId);
                return deviceId;
            } catch (RuntimeException e) {
                Log.w(TAG, "getDeviceId: 从ProfileManager获取设备ID失败: " + e.getMessage());
            }
        }
        Log.w(TAG, "getDeviceId: mProfileManager为空或获取失败，尝试获取默认设备ID");
        Application application = ComponentKit.getInstance().getApplication();
        if (application != null) {
            try {
                String deviceId = DeviceUtil.getDeviceId(application);
                Log.i(TAG, "getDeviceId: 获取默认设备ID=" + deviceId);
                return deviceId;
            } catch (RuntimeException e) {
                Log.w(TAG, "getDeviceId: 获取默认设备ID失败: " + e.getMessage());
                return null;
            }
        } else {
            Log.w(TAG, "getDeviceId: 无法获取Application实例，返回null");
            return null;
        }
    }

    public void setDeviceId(String deviceId) {
        Log.i(TAG, "setDeviceId: 设置设备ID=" + deviceId);
        if (mProfileManager != null) {
            mProfileManager.setDeviceId(deviceId);
        } else {
            Log.w(TAG, "setDeviceId: mProfileManager为空，无法设置设备ID");
        }
    }
}
