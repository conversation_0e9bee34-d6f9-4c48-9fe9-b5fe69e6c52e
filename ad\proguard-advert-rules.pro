# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
-include ../core/proguard-core-rules.pro
# 这句话能够使我们的项目混淆后产生映射文件
# 包含有类名->混淆后类名的映射关系
-verbose
-printmapping proguardMapping-ad.txt

#项目
-dontwarn com.kaolafm.ad
-keepclasseswithmembernames class com.kaolafm.ad.Advertisement {*;}
-keep class com.kaolafm.ad.api.AdvertisingRequest {
    public *;
}
-keepclasseswithmembernames class com.kaolafm.ad.api.internal.model.**{*;}
-keepclasseswithmembernames class com.kaolafm.ad.api.model.**{*;}

-keep class com.kaolafm.ad.expose.AdvertisingManager {
    public *;
}
-keep class com.kaolafm.ad.expose.AdvertInterceptor {*;}
-keep class com.kaolafm.ad.expose.AdvertInterceptor$Chain {*;}
-keep class com.kaolafm.ad.expose.AdvertisingImager {*;}
-keep class com.kaolafm.ad.expose.AdvertisingPlayer {*;}
-keep class com.kaolafm.ad.expose.Executor {*;}
-keep class com.kaolafm.ad.expose.AdvertisingLifecycleCallback {*;}

-keep class com.kaolafm.ad.timer.TimedAdvertManager{*;}
-keepclasseswithmembernames class com.kaolafm.ad.timer.TimedAdvertManager

-keep class com.kaolafm.ad.util.** {*;}
-keep class com.kaolafm.ad.report.db.greendao.*{*;}
