package com.kaolafm.opensdk.player.logic.playlist.util;

import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.broadcast.ProgramDetails;
import com.kaolafm.opensdk.api.feature.model.FeatureAudioDetails;
import com.kaolafm.opensdk.api.live.model.LiveInfoDetail;
import com.kaolafm.opensdk.api.media.model.AIAudioDetails;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.api.media.model.VideoAudioDetails;
import com.kaolafm.opensdk.api.tv.model.TVProgramDetails;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.PlaylistInfo;
import com.kaolafm.opensdk.player.logic.model.item.AlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.FeaturePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.LivePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.OneKeyPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.PurchaseOneKeyPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.RadioPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.TVPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.VideoAlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.RadioInfoData;
import com.kaolafm.opensdk.player.logic.playlist.RadioPlayListControl;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.opensdk.player.logic.util.PlayerPreconditions;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> on 2019/3/12.
 */

public class PlayListUtils {
    private static final String TAG = "PlayListUtils";

    /**
     * AudioDetails列表转化为AlbumPlayItem列表
     *
     * @param audioDetailsList
     * @param playlistInfo
     * @return
     */
    public static ArrayList<PlayItem> audioDetailToAlbumPlayItem(List<AudioDetails> audioDetailsList, PlaylistInfo playlistInfo) {
        return audioDetailToAlbumPlayItem(audioDetailsList, playlistInfo, "");
    }

    public static ArrayList<PlayItem> audioDetailToAlbumPlayItem(List<AudioDetails> audioDetailsList, PlaylistInfo playlistInfo, String recommend) {
        return audioDetailToAlbumPlayItem(audioDetailsList, playlistInfo, recommend, false);
    }

    /**
     * AudioDetails列表转化为AlbumPlayItem列表
     *
     * @param audioDetailsList
     * @param playlistInfo
     * @param recommend        专辑推荐语
     * @param isFromAudio
     * @return
     */
    public static ArrayList<PlayItem> audioDetailToAlbumPlayItem(List<AudioDetails> audioDetailsList, PlaylistInfo playlistInfo, String recommend, boolean isFromAudio) {
        if (ListUtil.isEmpty(audioDetailsList)) {
            return null;
        }
        ArrayList<PlayItem> playItems = new ArrayList<>();
        for (int i = 0; i < audioDetailsList.size(); i++) {
            AudioDetails audioDetails = audioDetailsList.get(i);
            if (audioDetails == null) {
                continue;
            }
            AlbumPlayItem playItem = (AlbumPlayItem) translateAlbumToPlayItem(audioDetails);
            if (playItem == null) {
                continue;
            }
            playItem.setFromAudio(isFromAudio);
            if (playlistInfo != null) {
                PlayerLogUtil.log(TAG, "audioDetailToAlbumPlayItem", "playlistInfo.getNoSubscribe()" + playlistInfo.getNoSubscribe());
                playItem.setNoSubscribe(playlistInfo.getNoSubscribe());
                playItem.getAlbumInfoData().setCountNum(playlistInfo.getCountNum());
                playItem.getAlbumInfoData().setFollowedNum(playlistInfo.getFollowedNum());
                playItem.getAlbumInfoData().setListenNum(playlistInfo.getListenNum());
                playItem.getAlbumInfoData().setASentenceRecommend(recommend);
                playItem.setListenCount(playlistInfo.getListenNum());
                playItem.getAlbumInfoData().setBreakPointContinue(playlistInfo.getBreakPointContinue());
                if (StringUtil.isEmpty(playItem.getInfoData().getSourceLogo())) {
                    playItem.getInfoData().setSourceLogo(playlistInfo.getSourceLogo());
                    playItem.getInfoData().setSourceName(playlistInfo.getSourceName());
                }
                playItem.setVideoPiece(audioDetails.getVideoPiece());
            }
            playItems.add(playItem);
        }
        return playItems;
    }

    /**
     * AudioDetails列表转化为RadioPlayItem列表
     *
     * @param audioDetailsList
     * @param playlistInfo
     * @return
     */
    public static ArrayList<PlayItem> audioDetailToRadioPlayItem(List<AIAudioDetails> audioDetailsList, PlaylistInfo playlistInfo) {
        if (ListUtil.isEmpty(audioDetailsList)) {
            return null;
        }
        ArrayList<PlayItem> playItems = new ArrayList<>();
        for (int i = 0; i < audioDetailsList.size(); i++) {
            AIAudioDetails audioDetails = audioDetailsList.get(i);
            if (audioDetails == null) {
                continue;
            }
            RadioPlayItem playItem = (RadioPlayItem) translateRadioToPlayItem(audioDetails);
            if (playItem == null) {
                continue;
            }

            if (playlistInfo != null) {
                RadioInfoData radioInfoData = playItem.getRadioInfoData();

                radioInfoData.setCountNum(playlistInfo.getCountNum());
                radioInfoData.setFollowedNum(playlistInfo.getFollowedNum());
                radioInfoData.setListenNum(playlistInfo.getListenNum());
                playItem.setListenCount(playlistInfo.getListenNum());
                radioInfoData.setRadioId(Long.parseLong(playlistInfo.getId()));
                radioInfoData.setRadioName(playlistInfo.getAlbumName());
                radioInfoData.setRadioPic(playlistInfo.getAlbumPic());


                radioInfoData.setRadioType(playlistInfo.getRadioType());
                if (playlistInfo.getAdZoneChooseType() == RadioPlayListControl.TYPE_ZONE_CHOOSE_DETAILS) {
                    radioInfoData.setAdZoneId(playlistInfo.getAdZoneId());
                } else {
                    radioInfoData.setAdZoneId((int) audioDetails.getAdZoneId());
                }
                radioInfoData.setAdZoneChooseType(playlistInfo.getAdZoneChooseType());
            }

            playItems.add(playItem);
        }
        return playItems;
    }

    public static ArrayList<PlayItem> videoAudioDetailToAlbumPlayItem(List<VideoAudioDetails> audioDetailsList, PlaylistInfo playlistInfo, String recommend) {
        return videoAudioDetailToAlbumPlayItem(audioDetailsList, playlistInfo, recommend, false);
    }

    /**
     * VideoAudioDetails列表转化为AlbumPlayItem列表
     *
     * @param audioDetailsList
     * @param playlistInfo
     * @param recommend        专辑推荐语
     * @return
     */
    public static ArrayList<PlayItem> videoAudioDetailToAlbumPlayItem(List<VideoAudioDetails> audioDetailsList, PlaylistInfo playlistInfo, String recommend, boolean isFromAudio) {
        if (ListUtil.isEmpty(audioDetailsList)) {
            return null;
        }
        ArrayList<PlayItem> playItems = new ArrayList<>();
        for (int i = 0; i < audioDetailsList.size(); i++) {
            VideoAudioDetails audioDetails = audioDetailsList.get(i);
            if (audioDetails == null) {
                continue;
            }
            VideoAlbumPlayItem playItem = (VideoAlbumPlayItem) translateVideoAlbumToPlayItem(audioDetails, isFromAudio);
            if (playItem == null) {
                continue;
            }
            playItem.setFromAudio(isFromAudio);
            if (playlistInfo != null) {
                playItem.getAlbumInfoData().setCountNum(playlistInfo.getCountNum());
                playItem.getAlbumInfoData().setFollowedNum(playlistInfo.getFollowedNum());
                playItem.getAlbumInfoData().setListenNum(playlistInfo.getListenNum());
                playItem.getAlbumInfoData().setASentenceRecommend(recommend);
                playItem.setListenCount(playlistInfo.getListenNum());
                playItem.getAlbumInfoData().setBreakPointContinue(playlistInfo.getBreakPointContinue());
                if (StringUtil.isEmpty(playItem.getInfoData().getSourceLogo())) {
                    playItem.getInfoData().setSourceLogo(playlistInfo.getSourceLogo());
                    playItem.getInfoData().setSourceName(playlistInfo.getSourceName());
                }
            }
            playItems.add(playItem);
        }
        return playItems;
    }

    /**
     * AudioDetails转化为OneKeyPlayItem列表
     *
     * @param audioDetailsList
     * @param playlistInfo
     * @return
     */
    public static ArrayList<PlayItem> audioDetailToOneKeyPlayItem(List<AudioDetails> audioDetailsList, PlaylistInfo playlistInfo) {
        if (ListUtil.isEmpty(audioDetailsList)) {
            return null;
        }
        ArrayList<PlayItem> playItems = new ArrayList<>();
        for (int i = 0; i < audioDetailsList.size(); i++) {
            AudioDetails audioDetails = audioDetailsList.get(i);
            if (audioDetails == null) {
                continue;
            }
            OneKeyPlayItem playItem = (OneKeyPlayItem) translateOneKeyToPlayItem(audioDetails);
            if (playItem == null) {
                continue;
            }
            if (playlistInfo != null) {
                playItem.getRadioInfoData().setCountNum(playlistInfo.getCountNum());
                playItem.getRadioInfoData().setFollowedNum(playlistInfo.getFollowedNum());
                playItem.getRadioInfoData().setListenNum(playlistInfo.getListenNum());
                playItem.setListenCount(playlistInfo.getListenNum());
            }
            playItems.add(playItem);
        }
        return playItems;
    }

    /**
     * AudioDetails列表转化为PurchaseOneKeyPlayItem列表
     *
     * @param audioDetailsList
     * @param playlistInfo
     * @return
     */
    public static ArrayList<PlayItem> audioDetailToPurchaseOneKeyPlayItem(List<AudioDetails> audioDetailsList, PlaylistInfo playlistInfo) {
        if (ListUtil.isEmpty(audioDetailsList)) {
            return null;
        }
        ArrayList<PlayItem> playItems = new ArrayList<>();
        for (int i = 0; i < audioDetailsList.size(); i++) {
            AudioDetails audioDetails = audioDetailsList.get(i);
            if (audioDetails == null) {
                continue;
            }
            PurchaseOneKeyPlayItem playItem = (PurchaseOneKeyPlayItem) translatePurchaseOneKeyToPlayItem(audioDetails);
            if (playItem == null) {
                continue;
            }
            if (playlistInfo != null) {
                playItem.getRadioInfoData().setCountNum(playlistInfo.getCountNum());
                playItem.getRadioInfoData().setFollowedNum(playlistInfo.getFollowedNum());
                playItem.getRadioInfoData().setListenNum(playlistInfo.getListenNum());
                playItem.setListenCount(playlistInfo.getListenNum());
            }
            playItems.add(playItem);
        }
        return playItems;
    }

    /**
     * AudioDetails列表转化为AlbumPlayItem列表
     *
     * @param audioDetailsList
     * @param playlistInfo
     * @return
     */
    public static ArrayList<PlayItem> audioDetailToFeaturePlayItem(List<FeatureAudioDetails> audioDetailsList, PlaylistInfo playlistInfo) {
        // AudioDetails trans
        if (ListUtil.isEmpty(audioDetailsList)) {
            return null;
        }
        ArrayList<PlayItem> playItems = new ArrayList<>();
        for (int i = 0; i < audioDetailsList.size(); i++) {
            FeatureAudioDetails audioDetails = audioDetailsList.get(i);
            if (audioDetails == null) {
                continue;
            }
            FeaturePlayItem playItem = (FeaturePlayItem) translateFeatureToPlayItem(audioDetails);
            if (playItem == null) {
                continue;
            }
            if (playlistInfo != null) {
                playItem.getAlbumInfoData().setCountNum(playlistInfo.getCountNum());
                playItem.getAlbumInfoData().setFollowedNum(playlistInfo.getFollowedNum());
                playItem.getAlbumInfoData().setListenNum(playlistInfo.getListenNum());
                playItem.setListenCount(playlistInfo.getListenNum());
                playItem.getAlbumInfoData().setBreakPointContinue(playlistInfo.getBreakPointContinue());
                if (StringUtil.isEmpty(playItem.getInfoData().getSourceLogo())) {
                    playItem.getInfoData().setSourceLogo(playlistInfo.getSourceLogo());
                    playItem.getInfoData().setSourceName(playlistInfo.getSourceName());
                }
            }
            playItems.add(playItem);
        }
        return playItems;
    }

    public static PlayItem translateFeatureToPlayItem(FeatureAudioDetails audioDetails) {
        if (audioDetails == null) {
            return null;
        }
        FeaturePlayItem playItem = new FeaturePlayItem();
        playItem.setAudioId(audioDetails.getAudioId());
        playItem.getInfoData().setTitle(audioDetails.getAudioName());
        playItem.setDuration(audioDetails.getDuration());
        playItem.setNoSubscribe(audioDetails.getNoSubscribe());

        playItem.getInfoData().setAudioDes(audioDetails.getAudioDes());
        playItem.getInfoData().setAlbumId(audioDetails.getFeatureId());
        playItem.getInfoData().setAlbumPic(audioDetails.getFeaturePic());
        playItem.getInfoData().setAlbumName(audioDetails.getFeatureName());
        playItem.getInfoData().setAudioPic(audioDetails.getAudioPic());
        playItem.getInfoData().setOrderNum(audioDetails.getOrderNum());
        playItem.getInfoData().setUpdateTime(String.valueOf(audioDetails.getUpdateTime()));
        playItem.getInfoData().setIcon(audioDetails.getAudioPic());

        playItem.setPlayUrlData(audioDetails.getPlayInfoList());
        return playItem;
    }

    /**
     * AudioDetails转化为AlbumPlayItem
     *
     * @param audioDetails
     * @return
     */
    public static PlayItem translateAlbumToPlayItem(AudioDetails audioDetails) {
        if (audioDetails == null) {
            return null;
        }
        AlbumPlayItem playItem = new AlbumPlayItem();
        playItem.setAudioId(audioDetails.getAudioId());
        playItem.getInfoData().setTitle(audioDetails.getAudioName());
        String aacPlayUrl = audioDetails.getAacPlayUrl();
        playItem.setDuration(audioDetails.getOriginalDuration());
        playItem.setPlayUrl(aacPlayUrl);
        playItem.setNoSubscribe(audioDetails.getNoSubscribe());
        PlaylistInfo playListInfo = PlayerManager.getInstance().getPlayListInfo();
        if (playListInfo != null) {
            playItem.setNoSubscribe(PlayerManager.getInstance().getPlayListInfo().getNoSubscribe());
            PlayerLogUtil.log(TAG, "getNoSubscribe() = " + PlayerManager.getInstance().getPlayListInfo().getNoSubscribe());
        } else {
            PlayerLogUtil.log(TAG, "playListInfo = null");
        }

        // 增加购买方式及购买信息
        playItem.setPayMethod(audioDetails.getPayMethod());

        //增加audioDetails中携带的购买类型（buyType）和购买状态信息(buyState)
        playItem.setBuyStatus(audioDetails.getBuyStatus());
        playItem.setBuyType(audioDetails.getBuyType());

        //增加audioDetails中携带的试听类型（audition）
        playItem.setAudition(audioDetails.getAudition());

        playItem.setFine(audioDetails.getAlbumIsFine()); //用于保存到历史条目时展示专辑是否是精品

        playItem.setVip(audioDetails.getAlbumIsVip()); //用于保存到历史条目时展示专辑是否是vip

        //增加playUrlId供播放时通过id获取播放地址
        playItem.setPlayUrlId(audioDetails.getPlayUrlId());

        playItem.getOfflineInfoData().setOfflineUrl(aacPlayUrl);
        playItem.getInfoData().setAudioDes(audioDetails.getAudioDes());
        playItem.getInfoData().setAlbumId(audioDetails.getAlbumId());
        playItem.getInfoData().setAlbumPic(audioDetails.getAlbumPic());
        playItem.getInfoData().setAlbumName(audioDetails.getAlbumName());
        playItem.getInfoData().setAudioPic(audioDetails.getAudioPic());
        playItem.getInfoData().setOrderNum(audioDetails.getOrderNum());
        playItem.getInfoData().setUpdateTime(String.valueOf(audioDetails.getUpdateTime()));
        playItem.getInfoData().setIcon(audioDetails.getIcon());
        playItem.getInfoData().setSourceLogo(audioDetails.getSourceLogo());
        playItem.getInfoData().setSourceName(audioDetails.getSourceName());
        return playItem;
    }

    /**
     * VideoAudioDetails转化为VideoAlbumPlayItem
     *
     * @param audioDetails
     * @return
     */
    public static PlayItem translateVideoAlbumToPlayItem(VideoAudioDetails audioDetails, boolean isFromAudio) {
        if (audioDetails == null) {
            return null;
        }
        VideoAlbumPlayItem playItem = new VideoAlbumPlayItem();
        playItem.setFromAudio(isFromAudio);
        playItem.setAudioId(audioDetails.getId());
        playItem.getInfoData().setTitle(audioDetails.getTitle());
        String aacPlayUrl = audioDetails.getPlayUrlId();
        playItem.setDuration(audioDetails.getDuration());
        playItem.setPlayUrl(aacPlayUrl);


        //增加audioDetails中携带的购买类型（buyType）和购买状态信息(buyState)
        playItem.setBuyStatus(audioDetails.getBuyStatus());
        playItem.setBuyType(audioDetails.getBuyType());

        //增加audioDetails中携带的试听类型（audition）
        playItem.setAudition(audioDetails.getAudition());

        playItem.setFine(audioDetails.getAlbumIsFine()); //用于保存到历史条目时展示专辑是否是精品

        playItem.setVip(audioDetails.getAlbumIsVip()); //用于保存到历史条目时展示专辑是否是vip

        //增加playUrlId供播放时通过id获取播放地址
        playItem.setPlayUrlId(audioDetails.getPlayUrlId());

        playItem.getOfflineInfoData().setOfflineUrl(aacPlayUrl);
        playItem.getInfoData().setAudioDes(audioDetails.getDesc());
        playItem.getInfoData().setAlbumId(audioDetails.getAlbumId());
        playItem.getInfoData().setAlbumPic(audioDetails.getAlbumPic());
        playItem.getInfoData().setAlbumName(audioDetails.getAlbumName());
        playItem.getInfoData().setAudioPic(audioDetails.getAlbumPic());
        playItem.getInfoData().setOrderNum(audioDetails.getOrderNum());
        playItem.getInfoData().setUpdateTime(String.valueOf(audioDetails.getValidStartDate()));
        playItem.getInfoData().setIcon(audioDetails.getImg());
        playItem.getInfoData().setSourceLogo(audioDetails.getImg());
        playItem.getInfoData().setSourceName(audioDetails.getSourceName());
        return playItem;
    }

    /**
     * AudioDetails转化为RadioPlayItem
     *
     * @param audioDetails
     * @return
     */
    public static PlayItem translateRadioToPlayItem(AIAudioDetails audioDetails) {
        if (audioDetails == null) {
            return null;
        }
        RadioPlayItem playItem = new RadioPlayItem();
        playItem.setAudioId(audioDetails.getAudioId());
        playItem.getInfoData().setTitle(audioDetails.getAudioName());
        String aacPlayUrl = audioDetails.getAacPlayUrl();
        playItem.setDuration(audioDetails.getOriginalDuration());
        playItem.setPlayUrl(aacPlayUrl);

        // 增加购买方式及购买信息
        playItem.setPayMethod(audioDetails.getPayMethod());

        //增加audioDetails中携带的购买类型（buyType）和购买状态信息(buyState)
        playItem.setBuyStatus(audioDetails.getBuyStatus());
        playItem.setBuyType(audioDetails.getBuyType());

        //增加playUrlId供播放时通过id获取播放地址
        playItem.setPlayUrlId(audioDetails.getPlayUrlId());

        playItem.getInfoData().setAudioDes(audioDetails.getAudioDes());
        playItem.getInfoData().setAlbumId(audioDetails.getAlbumId());
        playItem.getInfoData().setAlbumPic(audioDetails.getAlbumPic());
        playItem.getInfoData().setAlbumName(audioDetails.getAlbumName());
        playItem.getInfoData().setAudioPic(audioDetails.getAudioPic());
        playItem.getInfoData().setOrderNum(audioDetails.getOrderNum());
        playItem.getInfoData().setUpdateTime(String.valueOf(audioDetails.getUpdateTime()));
        playItem.getRadioInfoData().setClockId(audioDetails.getClockId());
        playItem.getRadioInfoData().setCategoryId(audioDetails.getCategoryId());
        playItem.getInfoData().setIcon(audioDetails.getIcon());
        playItem.getInfoData().setSourceLogo(audioDetails.getSourceLogo());
        playItem.getInfoData().setSourceName(audioDetails.getSourceName());
        playItem.getRadioInfoData().setMainTitleName(audioDetails.getMainTitleName());
        playItem.getRadioInfoData().setRadioSubTag(audioDetails.getContentTypeName());
        playItem.getRadioInfoData().setRadioSubTagType(audioDetails.getContentType());
        playItem.getRadioInfoData().setSubheadName(audioDetails.getSubheadName());
        playItem.getRadioInfoData().setCallBack(audioDetails.getCallBack());
        playItem.getRadioInfoData().setSource(audioDetails.getSource());
        playItem.setTimeType(audioDetails.getTimeType());

        playItem.setPlayInfoList(audioDetails.getPlayInfoList());
        return playItem;
    }

    /**
     * AudioDetails转化为OneKeyPlayItem
     *
     * @param audioDetails
     * @return
     */
    public static PlayItem translateOneKeyToPlayItem(AudioDetails audioDetails) {
        if (audioDetails == null) {
            return null;
        }
        OneKeyPlayItem playItem = new OneKeyPlayItem();

        playItem.setAudioId(audioDetails.getAudioId());
        playItem.getInfoData().setTitle(audioDetails.getAudioName());
        String aacPlayUrl = audioDetails.getAacPlayUrl();
        playItem.setDuration(audioDetails.getOriginalDuration());
        playItem.setPlayUrl(aacPlayUrl);

        // 增加购买方式及购买信息
        playItem.setPayMethod(audioDetails.getPayMethod());

        //增加audioDetails中携带的购买类型（buyType）和购买状态信息(buyState)
        playItem.setBuyStatus(audioDetails.getBuyStatus());
        playItem.setBuyType(audioDetails.getBuyType());

        //增加playUrlId供播放时通过id获取播放地址
        playItem.setPlayUrlId(audioDetails.getPlayUrlId());

        playItem.setFine(audioDetails.getAlbumIsFine()); //用于保存到历史条目时展示专辑是否是精品

        playItem.setVip(audioDetails.getAlbumIsVip()); //用于保存到历史条目时展示专辑是否是vip

        playItem.getInfoData().setAudioDes(audioDetails.getAudioDes());
        playItem.getInfoData().setAlbumId(audioDetails.getAlbumId());
        playItem.getInfoData().setAlbumPic(audioDetails.getAlbumPic());
        playItem.getInfoData().setAlbumName(audioDetails.getAlbumName());
        playItem.getInfoData().setAudioPic(audioDetails.getAudioPic());
        playItem.getInfoData().setOrderNum(audioDetails.getOrderNum());
        playItem.getInfoData().setUpdateTime(String.valueOf(audioDetails.getUpdateTime()));
        playItem.getRadioInfoData().setRadioId(audioDetails.getCategoryId());
        playItem.getRadioInfoData().setClockId(audioDetails.getClockId());
        playItem.getRadioInfoData().setCategoryId(audioDetails.getCategoryId());
        playItem.getInfoData().setIcon(audioDetails.getIcon());
        playItem.getInfoData().setSourceLogo(audioDetails.getSourceLogo());
        playItem.getInfoData().setSourceName(audioDetails.getSourceName());
        return playItem;
    }

    /**
     * AudioDetails转化为PurchaseOneKeyPlayItem
     *
     * @param audioDetails
     * @return
     */
    public static PlayItem translatePurchaseOneKeyToPlayItem(AudioDetails audioDetails) {
        if (audioDetails == null) {
            return null;
        }
        PurchaseOneKeyPlayItem playItem = new PurchaseOneKeyPlayItem();

        playItem.setAudioId(audioDetails.getAudioId());
        playItem.getInfoData().setTitle(audioDetails.getAudioName());
        String aacPlayUrl = audioDetails.getAacPlayUrl();
        playItem.setDuration(audioDetails.getOriginalDuration());
        playItem.setPlayUrl(aacPlayUrl);

        // 增加购买方式及购买信息
        playItem.setPayMethod(audioDetails.getPayMethod());

        //增加audioDetails中携带的购买类型（buyType）和购买状态信息(buyState)
        playItem.setBuyStatus(audioDetails.getBuyStatus());
        playItem.setBuyType(audioDetails.getBuyType());

        //增加playUrlId供播放时通过id获取播放地址
        playItem.setPlayUrlId(audioDetails.getPlayUrlId());

        playItem.setFine(audioDetails.getAlbumIsFine()); //用于保存到历史条目时展示专辑是否是精品

        playItem.setVip(audioDetails.getAlbumIsVip()); //用于保存到历史条目时展示专辑是否是vip

        playItem.getInfoData().setAudioDes(audioDetails.getAudioDes());
        playItem.getInfoData().setAlbumId(audioDetails.getAlbumId());
        playItem.getInfoData().setAlbumPic(audioDetails.getAlbumPic());
        playItem.getInfoData().setAlbumName(audioDetails.getAlbumName());
        playItem.getInfoData().setAudioPic(audioDetails.getAudioPic());
        playItem.getInfoData().setOrderNum(audioDetails.getOrderNum());
        playItem.getInfoData().setUpdateTime(String.valueOf(audioDetails.getUpdateTime()));
        playItem.getRadioInfoData().setRadioId(audioDetails.getCategoryId());
        playItem.getRadioInfoData().setClockId(audioDetails.getClockId());
        playItem.getRadioInfoData().setCategoryId(audioDetails.getCategoryId());
        playItem.getInfoData().setIcon(audioDetails.getIcon());
        playItem.getInfoData().setSourceLogo(audioDetails.getSourceLogo());
        playItem.getInfoData().setSourceName(audioDetails.getSourceName());
        return playItem;
    }

    public static ArrayList<PlayItem> programDetailsToPlayItem(List<ProgramDetails> programDetailsList, String channel, int broadcastSort, long listenCount, int programEnable, String areaCode, String areaName) {
        return programDetailsToPlayItem(programDetailsList, channel, broadcastSort, 0, listenCount, programEnable, areaCode, areaName);
    }

    /**
     * ProgramDetails列表转化为BroadcastPlayItem列表
     *
     * @param programDetailsList
     * @param channel
     * @return
     */
    public static ArrayList<PlayItem> programDetailsToPlayItem(List<ProgramDetails> programDetailsList, String channel, int broadcastSort, int classifyId, long listenCount, int programEnable, String areaCode, String areaName) {
        if (ListUtil.isEmpty(programDetailsList)) {
            return null;
        }
        ArrayList<PlayItem> playItems = new ArrayList<>();
        for (int i = 0; i < programDetailsList.size(); i++) {
            ProgramDetails programDetails = programDetailsList.get(i);
            if (programDetails == null) {
                continue;
            }
            playItems.add(translateToPlayItem(programDetails, channel, broadcastSort, classifyId, listenCount, programEnable, areaCode, areaName));
        }
        return playItems;
    }

    public static int getLivingBroadcastPlayItem(ArrayList<PlayItem> playItems) {
        if (ListUtil.isEmpty(playItems)) {
            return 0;
        }
        int size = playItems.size();
        for (int i = 0; i < size; i++) {
            PlayItem playItem = playItems.get(i);
            if (PlayerPreconditions.checkNull(playItem)) {
                continue;
            }
            BroadcastPlayItem broadcastPlayItem = (BroadcastPlayItem) playItem;
            if (broadcastPlayItem.getStatus() == PlayerConstants.BROADCAST_STATUS_LIVING) {
                return i;
            }
        }
        return 0;
    }

    /**
     * ProgramDetails列表转化为TVPlayItem列表
     *
     * @param programDetailsList
     * @param channel
     * @param listenNum
     * @return
     */
    public static ArrayList<PlayItem> programDetailsToTVPlayItem(List<TVProgramDetails> programDetailsList, String channel, long listenNum) {
        if (ListUtil.isEmpty(programDetailsList)) {
            return null;
        }
        ArrayList<PlayItem> playItems = new ArrayList<>();
        for (int i = 0; i < programDetailsList.size(); i++) {
            TVProgramDetails programDetails = programDetailsList.get(i);
            if (programDetails == null) {
                continue;
            }
            playItems.add(translateToPlayItem(programDetails, channel, listenNum));
        }
        return playItems;
    }


    public static int getLivingTVPlayItem(ArrayList<PlayItem> playItems) {
        if (ListUtil.isEmpty(playItems)) {
            return 0;
        }
        int size = playItems.size();
        for (int i = 0; i < size; i++) {
            PlayItem playItem = playItems.get(i);
            if (PlayerPreconditions.checkNull(playItem)) {
                continue;
            }
            TVPlayItem tvPlayItem = (TVPlayItem) playItem;
            if (tvPlayItem.getStatus() == PlayerConstants.BROADCAST_STATUS_LIVING) {
                return i;
            }
        }
        return 0;
    }

    public static BroadcastPlayItem translateToPlayItem(ProgramDetails programDetails, String channel, int broadcastSort, long listenCount, int programEnable, String areaCode, String areaName) {
        return translateToPlayItem(programDetails, channel, broadcastSort, 0, listenCount, programEnable, areaCode, areaName);
    }


    /**
     * ProgramDetails转化为BroadcastPlayItem
     *
     * @param programDetails
     * @param channel
     * @return
     */
    public static BroadcastPlayItem translateToPlayItem(ProgramDetails programDetails, String channel, int broadcastSort, int classifyId, long listenCount, int programEnable, String areaCode, String areaName) {
        BroadcastPlayItem playItem = new BroadcastPlayItem();

        if (programDetails.getStatus() == PlayerConstants.BROADCAST_STATUS_LIVING) {
            playItem.setStatus(PlayerConstants.BROADCAST_STATUS_LIVING);
            playItem.setPlayUrl(programDetails.getPlayUrl());
            playItem.getTimeInfoData().setCurSystemTime(DateUtil.getServerTime());
        } else {
            int status = programDetails.getStatus();
            if (status == PlayerConstants.BROADCAST_STATUS_LIVING ||
                    status == PlayerConstants.BROADCAST_STATUS_DEFAULT) {
                playItem.setStatus(PlayerConstants.BROADCAST_STATUS_LIVING);
                playItem.setPlayUrl(programDetails.getPlayUrl());
                playItem.getTimeInfoData().setCurSystemTime(DateUtil.getServerTime());
            } else if (status == PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
                playItem.setStatus(PlayerConstants.BROADCAST_STATUS_PLAYBACK);
                playItem.setPlayUrl(programDetails.getBackLiveUrl());
            } else {
                playItem.setStatus(status);
                playItem.setPlayUrl(null);
            }
        }
        playItem.setClassifyId(classifyId);
        playItem.setPlayInfoList(programDetails.getPlayInfoList());
        playItem.setBackPlayInfoList(programDetails.getBackPlayInfoList());
        playItem.getInfoData().setDataSrc(PlayerConstants.RESOURCES_TYPE_BROADCAST);
        playItem.getInfoData().setAlbumId(programDetails.getBroadcastId());
        playItem.setAudioId(programDetails.getProgramId());
        playItem.getInfoData().setTitle(programDetails.getTitle());
        playItem.getInfoData().setAlbumPic(programDetails.getBroadcastImg());
        playItem.getInfoData().setImage(programDetails.getImage());
        playItem.getInfoData().setAudioPic(programDetails.getImage());
        playItem.getInfoData().setAlbumName(programDetails.getBroadcastName());
        playItem.getInfoData().setAudioDes(programDetails.getDesc());
        long start = programDetails.getStartTime();
        long end = programDetails.getFinishTime();
        long duration = end - start;
        playItem.getTimeInfoData().setStartTime(start);
        playItem.getTimeInfoData().setFinishTime(end);
        playItem.getTimeInfoData().setBeginTime(programDetails.getBeginTime());
        playItem.getTimeInfoData().setEndTime(programDetails.getEndTime());
        playItem.setDuration((int) duration);
        playItem.getInfoData().setIcon(programDetails.getIcon());
        playItem.setFrequencyChannel(channel);
        playItem.setBroadcastSort(broadcastSort);
        playItem.setListenCount(listenCount);
        playItem.setProgramEnable(programEnable);
        playItem.getInfoData().setAreaCode(areaCode);
        playItem.getInfoData().setAreaName(areaName);
        PlaylistInfo playListInfo = PlayerManager.getInstance().getPlayListInfo();
        if (playListInfo != null) {
            playItem.setNoSubscribe(playListInfo.getNoSubscribe());
        } else {
            PlayerLogUtil.log(TAG, "translateToPlayItem", "playListInfo = null");
        }
        return playItem;
    }

    /**
     * TVProgramDetails转化为TVPlayItem
     *
     * @param programDetails
     * @param channel
     * @param listenNum
     * @return
     */
    public static TVPlayItem translateToPlayItem(TVProgramDetails programDetails, String channel, long listenNum) {
        TVPlayItem playItem = new TVPlayItem();

        if (programDetails.getStatus() == PlayerConstants.BROADCAST_STATUS_LIVING) {
            playItem.setStatus(PlayerConstants.BROADCAST_STATUS_LIVING);
            playItem.setPlayUrl(programDetails.getPlayUrl());
            playItem.getTimeInfoData().setCurSystemTime(DateUtil.getServerTime());
        } else {
            int status = programDetails.getStatus();
            if (status == PlayerConstants.BROADCAST_STATUS_LIVING ||
                    status == PlayerConstants.BROADCAST_STATUS_DEFAULT) {
                playItem.setStatus(PlayerConstants.BROADCAST_STATUS_LIVING);
                playItem.setPlayUrl(programDetails.getPlayUrl());
                playItem.getTimeInfoData().setCurSystemTime(DateUtil.getServerTime());
            } else if (status == PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
                playItem.setStatus(PlayerConstants.BROADCAST_STATUS_PLAYBACK);
                playItem.setPlayUrl(programDetails.getBackLiveUrl());
            } else {
                playItem.setStatus(status);
                playItem.setPlayUrl(null);
            }
        }
        playItem.getInfoData().setDataSrc(PlayerConstants.RESOURCES_TYPE_TV);
        playItem.getInfoData().setAlbumId(programDetails.getListenTVid());
        playItem.setAudioId(programDetails.getProgramId());
        playItem.getInfoData().setTitle(programDetails.getTitle());
        playItem.getInfoData().setAlbumPic(programDetails.getListenTVImg());
        playItem.getInfoData().setAlbumName(programDetails.getListenTVName());
        playItem.getInfoData().setAudioDes(programDetails.getDesc());
        long start = programDetails.getStartTime();
        long end = programDetails.getFinishTime();
        long duration = end - start;
        playItem.getTimeInfoData().setStartTime(start);
        playItem.getTimeInfoData().setFinishTime(end);
        playItem.getTimeInfoData().setBeginTime(programDetails.getBeginTime());
        playItem.getTimeInfoData().setEndTime(programDetails.getEndTime());
        playItem.setDuration((int) duration);
        playItem.getInfoData().setIcon(programDetails.getIcon());
        playItem.setFrequencyChannel(channel);

        playItem.setPlayInfoList(programDetails.getPlayInfoList());
        playItem.setBackPlayInfoList(programDetails.getBackPlayInfoList());
        playItem.setListenCount(listenNum);
        return playItem;
    }

    /**
     * LiveInfoDetail转化为LivePlayItem
     *
     * @param detail
     * @return
     */
    public static PlayItem liveInfoToPlayItem(LiveInfoDetail detail) {
        LivePlayItem playItem = new LivePlayItem();
        playItem.setPlayUrl(detail.getLiveUrl());
        playItem.setDuration((int) (detail.getFinishTime() - detail.getStartTime()));

        playItem.getInfoData().setHosts(detail.getComperes());
        playItem.setProgramPic(detail.getProgramPic());
        playItem.getInfoData().setTitle(detail.getProgramName());
        playItem.getTimeInfoData().setBeginTime(detail.getBeginTime());
        playItem.getTimeInfoData().setEndTime(detail.getEndTime());
        playItem.getInfoData().setAudioPic(detail.getLivePic());
        playItem.getTimeInfoData().setStartTime(detail.getStartTime());
        playItem.getTimeInfoData().setFinishTime(detail.getFinishTime());
        playItem.getInfoData().setAlbumId(detail.getLiveId());
        playItem.setComperesId(String.valueOf(detail.getComperesId()));
        playItem.setLiveId(detail.getProgramId());
        playItem.setAudioId(detail.getProgramId());
        playItem.setStatus(detail.getStatus());
        playItem.setPlayListUrlInfo(detail.getPlayInfoList());
        return playItem;
    }

}
