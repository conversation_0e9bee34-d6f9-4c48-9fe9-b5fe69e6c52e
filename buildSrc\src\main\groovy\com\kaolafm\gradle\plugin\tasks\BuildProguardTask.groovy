package com.kaolafm.gradle.plugin.tasks

import com.android.build.gradle.AppPlugin
import com.android.build.gradle.LibraryPlugin
import com.kaolafm.gradle.plugin.model.SDKFlavor
import com.kaolafm.gradle.plugin.utils.Util
import org.gradle.api.Plugin
import proguard.gradle.ProGuardTask

import javax.inject.Inject

class BuildProguardTask extends ProGuardTask {

    @Inject
    BuildProguardTask() {
    }

    void config(SDKFlavor flavor, String outJarPath) {
        this.description = "构建混淆的jar包"
        this.group = Util.GROUP_NAME
        this.ignorewarnings()
        this.dontshrink()
        this.configuration(flavor.proguardConfigFile)
        addRuntime()
        def mappingFile = flavor.applyMappingFile
        if (mappingFile != null && mappingFile.length() > 0) {
            this.applymapping(mappingFile)
        }
        injars(project.buildDir.path + '/outputs/jar/')
        outjars(outJarPath)
    }

    private void addRuntime() {
        def plugins = project.getPlugins()
        def android = project.android
        Plugin plugin = plugins.hasPlugin(AppPlugin) ?
                plugins.findPlugin(AppPlugin) :
                plugins.findPlugin(LibraryPlugin)
        if (plugin != null) {
            List<String> runtimeJarList
            if (plugin.getMetaClass().getMetaMethod("getRuntimeJarList")) {
                runtimeJarList = plugin.getRuntimeJarList()
            } else {
                if (android.getMetaClass().getMetaMethod("getBootClasspath")) {
                    runtimeJarList = android.getBootClasspath()
                } else {
                    runtimeJarList = plugin.getBootClasspath()
                }
            }
            for (String runtimeJar : runtimeJarList) {
                //给 proguard 添加 runtime
                this.libraryjars(runtimeJar)
            }
        }
    }
}