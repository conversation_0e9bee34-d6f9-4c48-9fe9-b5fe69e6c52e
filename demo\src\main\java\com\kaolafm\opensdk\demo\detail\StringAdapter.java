package com.kaolafm.opensdk.demo.detail;

import android.view.View;
import android.view.View.OnClickListener;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.opensdk.demo.BaseAdapter;
import com.kaolafm.opensdk.demo.BaseHolder;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.demo.detail.StringAdapter.Item;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.List;

import butterknife.BindView;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: RecommendStringAdapter.java                                               
 *                                                                  *
 * Created in 2018/5/31 下午6:25                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class StringAdapter extends BaseAdapter<Item> {

    public static class Item {
        public PlayItem playItem;

        public long id;

        public int type;

        public String title;

        public String details;

        public int audition;

        public int buyStatus;

        public int fine;

        public boolean isSelected;

        public Object item;
    }

    private OnBtnClickListener mBtnClickListener;

    private int mPrePosition = -1;

    public StringAdapter() {

    }

    @Override
    protected ViewHolder getViewHolder(View view, int viewType) {
        ViewHolder viewHolder = new ViewHolder(view);
        viewHolder.mIvPlayerItemMore.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mBtnClickListener != null) {
                    mBtnClickListener.onClick(getItemData(viewHolder.getAdapterPosition()).item);
                }
            }
        });
        return viewHolder;
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_player_audio;
    }

    public void setSelected(int position) {

//        Item preItem = getItemData(mPrePosition);
//        if (preItem != null) {
//            preItem.isSelected = false;
//            notifyItemChanged(mPrePosition);
//        }
        List<Item> list = getDataList();
        for(int i=0; i<list.size(); i++){
            list.get(i).isSelected = false;
            notifyItemChanged(i);
        }
        Item itemData = getItemData(position);
        if (itemData != null) {
            itemData.isSelected = true;
            notifyItemChanged(position);
        }
        mPrePosition = position;
    }

    public class ViewHolder extends BaseHolder<Item> {


        @BindView(R.id.iv_player_item_more)
        ImageView mIvPlayerItemMore;

        @BindView(R.id.tv_player_item_pay_status)
        TextView mTvPlayerItemPayStatus;

        @BindView(R.id.tv_player_item_des)
        TextView mTvPlayerItemDes;

        @BindView(R.id.tv_player_item_title)
        TextView mTvPlayerItemTitle;

        public ViewHolder(View itemView) {
            super(itemView);
            itemView.setActivated(false);
        }

        @Override
        public void setupData(Item item, int position) {
            if(item.fine == 0){
                mTvPlayerItemPayStatus.setVisibility(View.GONE);
            }else{
                mTvPlayerItemPayStatus.setText(item.buyStatus==1?"已购买":(item.audition==1?"试听":(item.fine==1?"付费内容":"")));
                mTvPlayerItemPayStatus.setVisibility(View.VISIBLE);
            }
            mTvPlayerItemDes.setText(item.details);
            mTvPlayerItemTitle.setText(item.title);
            itemView.setActivated(item.isSelected);
        }
    }

    public void setOnBtnClickListener(OnBtnClickListener btnClickListener) {
        mBtnClickListener = btnClickListener;
    }

    public interface OnBtnClickListener {
        void onClick(Object item);
    }
}
