package com.kaolafm.report.event;


import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;

/**
 * <AUTHOR> on 2019/1/21.
 * 启动数据上报
 */

public class StartReportEvent extends BaseReportEventBean {
    public static final String TYPE_AUDIO = "0";
    public static final String TYPE_LAUNCH = "1";
    public static final String TYPE_WIDGET = "2";
    /**
     * 1：launch，2：widget，3：语音启动
     */
    private String type;

    /**
     * 是否首次启动（本地第一次打开，如果删除后再安装仍算首次启动，升级不算）0：否，1：是
     */
    private String flow = "0";

    public StartReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_START);
        type = TYPE_LAUNCH;

        if (ReportParameterManager.getInstance().isFirstStart()) {
            flow = "1";
        }
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getFlow() {
        return flow;
    }

    public void setFlow(String flow) {
        this.flow = flow;
    }
}
