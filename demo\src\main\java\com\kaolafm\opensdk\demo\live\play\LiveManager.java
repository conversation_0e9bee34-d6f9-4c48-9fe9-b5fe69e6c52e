package com.kaolafm.opensdk.demo.live.play;

import android.media.MediaPlayer;
import android.media.MediaRecorder;
import android.os.Environment;
import android.os.Handler;
import android.os.Message;
import android.os.SystemClock;
import android.util.Log;

import com.kaolafm.opensdk.api.live.model.LiveInfoDetail;
import com.kaolafm.opensdk.demo.live.chat.RecorderStatus;
import com.kaolafm.opensdk.demo.live.ui.LivePresenter;
import com.kaolafm.opensdk.player.logic.model.item.LivePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.InfoData;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.TimeInfoData;

import java.io.File;
import java.io.IOException;
import java.util.Observable;
import java.util.Observer;


public class LiveManager {

    private static final String TAG = "LiveManager";

    public static final long PROGRAM_ID_EMPTY = 0L;

    private static final int WHAT_LOOP_LIVE_STATUS = 1;

    private static final int LIVE_STATUS_LOOP_INTERVAL = 30 * 1000;

    public static final String RECORD_TIME_TOO_SHORT = "too_short";

    private static final int RECORD_MINIMUM_TIME = 1000;

    private LiveInfoDetail mShowingInfo;

    private LiveInfoDetail mPlayingInfo;

    private MyHandler mHandler;
    private LivePresenter mPresenter;

    private long mRecordStartTime;
    private int mRecordDuration;

    private LiveStatus mLiveStatus = LiveStatus.UNKNOWN;
    private ErrorStatus mErrorStatus = ErrorStatus.NO_ERROR;


    public static final String EXTENSION = ".aac";
    static final File RECORDINGS_DIR = new File(
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_MUSIC),
            "K-Radio-Record");

    private MediaPlayer mMediaPlayer;
    private MediaRecorder mMediaRecorder;
    private String mFilePath;
    private RecorderStatusObservable mRecorderStatusObservable;

    private LiveManager() {
        mHandler = new MyHandler();
        mRecorderStatusObservable = new RecorderStatusObservable(RecorderStatus.IDLE);
    }

    private static final class INSTANCE_HOLDER {
        private static final LiveManager INSTANCE = new LiveManager();
    }

    public static LiveManager getInstance() {
        return INSTANCE_HOLDER.INSTANCE;
    }

    public void setPresenter(LivePresenter presenter) {
        mPresenter = presenter;
    }

    public LiveStatus getLiveStatus() {
        return mLiveStatus;
    }

    public void setLiveStatus(LiveStatus status) {
        this.mLiveStatus = status;
    }

    public LiveInfoDetail getShowingInfo() {
        return mShowingInfo;
    }

    public void setShowingInfo(LiveInfoDetail info) {
        this.mShowingInfo = info;
    }

    public LiveInfoDetail getPlayingInfo() {
        return mPlayingInfo;
    }

    public void setPlayingInfo(LiveInfoDetail mPlayingInfo) {
        this.mPlayingInfo = mPlayingInfo;
    }

    public ErrorStatus getErrorStatus() {
        return mErrorStatus;
    }

    public void setErrorStatus(ErrorStatus st) {
        this.mErrorStatus = st;
    }

    private class MyHandler extends Handler {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case WHAT_LOOP_LIVE_STATUS:
                    if (mPresenter != null && mShowingInfo != null
                            && mShowingInfo != null) {
                        loopLiveStatus();
                    }
                    break;
            }
        }
    }

    public void loopLiveStatus() {
        loopLiveStatus(PROGRAM_ID_EMPTY);
    }

    public void loopLiveStatus(long programId) {
        if (programId != PROGRAM_ID_EMPTY) {
            mPresenter.getLiveInfo(programId);
        } else if (mShowingInfo != null && mShowingInfo != null) {
            mPresenter.getLiveInfo(mShowingInfo.getProgramId());
        } else {
            if (LivePresenter.DEBUG_LIVE) {
                Log.d(TAG, "loopLiveStatus: ERROR PROGRAM ID");
            }
        }
        if (mShowingInfo != null && mShowingInfo != null) {
            mPresenter.getListenerNumber(mShowingInfo.getRoomId());
        }
        if (mHandler.hasMessages(WHAT_LOOP_LIVE_STATUS)) {
            mHandler.removeMessages(WHAT_LOOP_LIVE_STATUS);
        }
        mHandler.sendEmptyMessageDelayed(WHAT_LOOP_LIVE_STATUS, LIVE_STATUS_LOOP_INTERVAL);
    }

    public void stopLoopLiveStatus() {
        mHandler.removeMessages(WHAT_LOOP_LIVE_STATUS);
    }

    /**
     * @return record file path, null if failed
     */
    public String startRecord() {
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "startRecord");
        }
        File file = createNewAudioFile();
        if (null == file) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.w(TAG, "startRecord, create file failed.");
            }
            return null;
        }
        if (file.exists()) {
            file.delete();
        }
        mFilePath = file.getAbsolutePath();
        mMediaRecorder = new MediaRecorder();
        mMediaRecorder.setAudioSource(MediaRecorder.AudioSource.MIC);
        mMediaRecorder.setOutputFormat(MediaRecorder.OutputFormat.AAC_ADTS);
        mMediaRecorder.setAudioEncoder(MediaRecorder.AudioEncoder.AAC);
        String path = file.getPath();
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "startRecord path: " + path);
        }
        mMediaRecorder.setOutputFile(path);

        try {
            mMediaRecorder.prepare();
            mMediaRecorder.start();
            mRecordStartTime = SystemClock.elapsedRealtime();
            mRecorderStatusObservable.setRecorderStatus(RecorderStatus.RECORDING);
            return mFilePath;
        } catch (IOException e) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.d(TAG, "startRecord start failed: ", e);
            }
        } catch (IllegalStateException e) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.d(TAG, "startRecord start failed: ", e);
            }
        } catch (RuntimeException e) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.d(TAG, "startRecord start failed: ", e);
            }
        } catch (Exception e) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.d(TAG, "startRecord start failed: ", e);
            }
        }
        mRecorderStatusObservable.setRecorderStatus(RecorderStatus.IDLE);
        return null;

    }

    /**
     *
     * @return 录音文件的路径，如果录音发生错误，返回null，如果录音时间过短，
     * 返回{@link LiveManager#RECORD_TIME_TOO_SHORT}
     */
    public String stopRecord() {
        return stopRecord(false);
    }

    /**
     *
     * @param cancel 停止录音是否是要取消录音
     * @return 录音文件的路径，如果录音发生错误，返回null，如果录音时间过短，
     * 返回{@link LiveManager#RECORD_TIME_TOO_SHORT}
     */
    public String stopRecord(boolean cancel) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "stopRecord");
        }
        if (mMediaRecorder != null) {
            try {
                mMediaRecorder.stop();
                mRecordDuration = (int) (SystemClock.elapsedRealtime() - mRecordStartTime);
                mMediaRecorder.release();
                mMediaRecorder = null;
            } catch (Exception e) {
                if (LivePresenter.DEBUG_LIVE) {
                    Log.d(TAG, "stopRecord error: ", e);
                }
                mRecorderStatusObservable.setRecorderStatus(RecorderStatus.IDLE);
                return null;
            }
        }
        if (cancel) {
            cancel();
            return null;
        }
        if (mRecordDuration <= RECORD_MINIMUM_TIME) {
            mRecorderStatusObservable.setRecorderStatus(RecorderStatus.CANCEL);
            cancel();
            return RECORD_TIME_TOO_SHORT;
        }
        mRecorderStatusObservable.setRecorderStatus(RecorderStatus.FINISHED);
        return mFilePath;
    }

    private File createNewAudioFile() {
        if (!RECORDINGS_DIR.exists()) {
            RECORDINGS_DIR.mkdirs();
        }
        File file = new File(RECORDINGS_DIR, "Live-Leave-message" + EXTENSION);
        return file;
    }

    public int getRecordDuration() {
        return mRecordDuration;
    }

    /**
     *
     * @return true 正在录音, false 不在录音
     */
    public boolean isRecording() {
        return mRecorderStatusObservable.getRecorderStatus() == RecorderStatus.RECORDING;
    }

    /**
     *
     * @return true 正在试听, false 不在试听
     */
    public boolean isPlaying() {
        return mRecorderStatusObservable.getRecorderStatus() == RecorderStatus.LISTENING;
    }

    /**
     *
     * @return true 录音刚刚完成，尚未进行其它操作
     */
    public boolean isFinished() {
        return mRecorderStatusObservable.getRecorderStatus() == RecorderStatus.FINISHED;
    }

    /**
     *
     * @return true 试听过了录音，包括完整听完或者听了其中一部分，但尚未进行其它操作
     */
    public boolean isListened() {
        return mRecorderStatusObservable.getRecorderStatus() == RecorderStatus.LISTENED;
    }

    /**
     *
     * @return true 空闲状态，可以录音，
     */
    public boolean isIdle() {
        return mRecorderStatusObservable.getRecorderStatus() == RecorderStatus.IDLE;
    }

    /**
     *
     * @return true 正在上传录音文件
     */
    public boolean isUploading() {
        return mRecorderStatusObservable.getRecorderStatus() == RecorderStatus.UPLOADING;
    }

    /**
     *
     * @return true 上传完成
     */
    public boolean isUploaded() {
        return mRecorderStatusObservable.getRecorderStatus() == RecorderStatus.UPLOADED;
    }

    /**
     *
     * @return true 上传失败
     */
    public boolean isFailure() {
        return mRecorderStatusObservable.getRecorderStatus() == RecorderStatus.FAILURE;
    }

    public void startListen() {
        Log.w(TAG, "startListen");
        mMediaPlayer = new MediaPlayer();
        mMediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                mp.release();
                mMediaPlayer = null;
                mRecorderStatusObservable.setRecorderStatus(RecorderStatus.LISTENED);
            }
        });
        mMediaPlayer.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
            @Override
            public void onPrepared(MediaPlayer mp) {
                mRecorderStatusObservable.setRecorderStatus(RecorderStatus.LISTENING);
                mp.start();
            }
        });
        try {
            mMediaPlayer.setDataSource(mFilePath);
        } catch (IOException e) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.w(TAG, "startListen", e);
            }
        }
        mMediaPlayer.prepareAsync();

    }

    public int getListenDuration() {
        if (mMediaPlayer != null) {
            return mMediaPlayer.getDuration();
        }
        return -1;
    }

    public String getFilePath() {
        return mFilePath;
    }

    public void stopListen() {
        if (LivePresenter.DEBUG_LIVE) {
            Log.w(TAG, "stopListen");
        }
        if (mMediaPlayer != null && mMediaPlayer.isPlaying()) {
            mMediaPlayer.stop();
            mMediaPlayer.release();
            mMediaPlayer = null;
            mRecorderStatusObservable.setRecorderStatus(RecorderStatus.LISTENED);
        }
    }

    public void cancel() {
        stopListen();
        mRecorderStatusObservable.setRecorderStatus(RecorderStatus.IDLE);
        deleteFile();
    }

    public void deleteFile() {
        if (mFilePath == null) {
            return;
        }
        File file = new File(mFilePath);
        if (file.exists()) {
            file.delete();
        }
    }

    public void setRecorderStatus(RecorderStatus status) {
        mRecorderStatusObservable.setRecorderStatus(status);
    }

    public class RecorderStatusObservable extends Observable {

        RecorderStatusObservable(RecorderStatus status) {
            mRecordStatus = status;
        }

        private RecorderStatus mRecordStatus;

        public void setRecorderStatus(RecorderStatus status) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.d(TAG, "setRecorderStatus: " + status);
            }
            mRecordStatus = status;
            setChanged();
            notifyObservers(mRecordStatus);
        }

        public RecorderStatus getRecorderStatus() {
            return mRecordStatus;
        }
    }

    public void addRecorderStatusObserver(Observer o) {
        mRecorderStatusObservable.addObserver(o);
    }

    public void resetStatus() {
        setLiveStatus(LiveStatus.UNKNOWN);
        setErrorStatus(ErrorStatus.NO_ERROR);
        mRecorderStatusObservable.setRecorderStatus(RecorderStatus.IDLE);
    }

    public void removeRecorderStatusObserver(Observer o) {
        mRecorderStatusObservable.deleteObserver(o);
    }

    public void onLiveExit() {
    }

    public PlayItem toPlayItem(LiveInfoDetail detail) {
        LivePlayItem item = new LivePlayItem();

        InfoData infoData = new InfoData();
        infoData.setTitle(detail.getLiveName());
        infoData.setAudioPic(detail.getLivePic());
        infoData.setAlbumId(detail.getProgramId());
        item.setInfoData(infoData);

        TimeInfoData timeInfoData = new TimeInfoData();
        timeInfoData.setBeginTime(detail.getBeginTime());
        timeInfoData.setEndTime(detail.getEndTime());
        timeInfoData.setStartTime(detail.getStartTime());
        timeInfoData.setFinishTime(detail.getFinishTime());
        item.setTimeInfoData(timeInfoData);

        item.setPlayUrl(detail.getLiveUrl());
        item.setDuration((int) (detail.getFinishTime() - detail.getStartTime()));

        item.setLiveId(detail.getProgramId());
        item.setStatus(detail.getStatus());
        return item;
    }
}
