package com.kaolafm.opensdk.player.logic.util;

import android.util.Log;

/**
 * @ClassName PlayerLogUtil
 * @Description
 * <AUTHOR>
 * @Date 2020/6/29 10:54
 * @Version 1.0
 */
public class PlayerLogUtil {
    public static boolean isOutPutLog = true;

    public static void disableLog() {
        isOutPutLog = false;
    }

    public static void enableLog() {
        isOutPutLog = true;
    }

    public static void log(String logStr) {
        if (!isOutPutLog) {
            return;
        }
        Log.i(PlayerConstants.LOG_TAG, logStr);
    }

    public static void log(String className, String logStr) {
        if (!isOutPutLog) {
            return;
        }
        Log.i(PlayerConstants.LOG_TAG, className + "->" + logStr);
    }

    public static void log(String className, String methodName, String logStr) {
        if (!isOutPutLog) {
            return;
        }
        Log.i(PlayerConstants.LOG_TAG, className + "." + methodName + "->" + logStr);
    }

}
