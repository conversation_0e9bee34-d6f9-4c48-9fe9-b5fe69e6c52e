package com.kaolafm.report.event;

import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> on 2019-07-23.
 */

public class LoginReportEvent extends BaseReportEventBean{
    /**
     * 登录类型: 1 扫码  2 手机号登录
     */
    public static final String TYPE_QR    = "1";
    public static final String TYPE_PHONE = "2";

    /**
     * 122113.我的——个人中心——进入登录流程
     * 122112.已购页面——点击立即登录——进入登录流程
     * 232010.收听历史——点击立即登录——进入登录流程
     * 231010.我的订阅——点击立即登录——进入登录流程
     * 141200.试听单曲轮播至需付费单曲——判断登录
     * 141201.播放详情页专辑封面下方点击“VIP会员 免费听”——判断登录
     * 141202.播放详情页内点击需付费单曲——判断登录
     * 141203 播放详情页内点击订阅--判断登录
     * 151101 首页mini播放器处点击订阅跳--判断登录
     *
     * 212新增-----------
     * 151105 话题详情页发布器点击发布--判断登录
     * 151102 付费专辑购买按钮点击 --判断登录
     * 151104 直播间语音留言点击 -- 判断登录
     * 212新增-----------
     */
    public static final String REMARKS1_USER_CENTER           = "122113";
    public static final String REMARKS1_PAYED_PAGE            = "122112";
    public static final String REMARKS1_HISTORY_PAGE          = "122111";
    public static final String REMARKS1_SUBSCRIBE_PAGE        = "122110";
    public static final String REMARKS1_PLAY_CAROUSEL         = "141200";
    public static final String REMARKS1_PLAY_VIP_BUTTON       = "141201";
    public static final String REMARKS1_PLAY_ITEM             = "141202";
    public static final String REMARKS1_PLAY_SUBSCRIBE        = "141203";
    public static final String REMARKS1_MINI_PLAYER_SUBSCRIBE = "151101";
    public static final String REMARKS1_ALUBM_PAY_BTN         = "151102";
    public static final String REMARKS1_LIVE_ROOM_MSG         = "151104";
    public static final String REMARKS1_TOPIC_SEND         = "151105";


//    在线电台版：
//250010 用户中心-会员中心--判断登录
//250030 用户中心-已购-点击立即登录--判断登录
//230020 听迹-收听历史-点击立即登录--判断登录
//230010 听迹-我的订阅-点击立即登录--判断登录
//    除直播间详情页、广播/电视详情页与用户服务提示页面均可能触发 用户试听单曲轮播至需付费单曲--判断登录
//280010 专辑/AI电台播放详情页顶部点击“VIP免费试听”--判断登录
//280010 专辑/AI电台播放详情页顶部点击“购买专辑”--判断登录
//280010 专辑/AI电台播放详情页点击需付费单曲--判断登录
//280010 专辑/AI电台播放详情页内点击单曲订阅--判断登录
//280010 专辑/AI电台播放详情页内点击专辑订阅--判断登录
//    包含底部播放器各页面 底部播放器处点击订阅--判断登录
//280030 直播间详情页点击语音留言按钮--判断登录
//260020 搜索结果页点击付费内容--判断登录
    public static final String ONLINE_REMARKS1_USER_CENTER           = "250010";
    public static final String ONLINE_REMARKS1_PAYED_PAGE            = "250030";
    public static final String ONLINE_REMARKS1_HISTORY_PAGE          = "230020";
    public static final String ONLINE_REMARKS1_SUBSCRIBE_PAGE        = "230010";
    public static final String ONLINE_REMARKS1_PLAY_CAROUSEL         = "280010";
    public static final String ONLINE_REMARKS1_LIVE_MSG = "280030";
    public static final String ONLINE_REMARKS1_SEARCH_LOGIN = "260020";

    /**
     * 登录类型, 1 扫码  2 手机号登录
     */
    private String type = "1";

    /**
     * 登录方式
     */
    private String remarks1;

    public LoginReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_LOGIN);
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRemarks1() {
        return remarks1;
    }

    public void setRemarks1(String remarks1) {
        this.remarks1 = remarks1;
    }
}
