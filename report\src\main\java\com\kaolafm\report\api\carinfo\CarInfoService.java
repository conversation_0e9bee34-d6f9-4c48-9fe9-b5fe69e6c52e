package com.kaolafm.report.api.carinfo;

import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.report.api.ReportApiConstant;
import com.kaolafm.report.api.ReportHostConstant;
import com.kaolafm.report.api.carinfo.model.CarInfoData;
import com.kaolafm.report.util.ReportConstants;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;

/**
 * <AUTHOR> on 2019/2/13.
 */

interface CarInfoService {

    @Headers(ReportHostConstant.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(ReportApiConstant.GET_CAR_INFO)
    Single<BaseResult<CarInfoData>> getReportInfo();

}
