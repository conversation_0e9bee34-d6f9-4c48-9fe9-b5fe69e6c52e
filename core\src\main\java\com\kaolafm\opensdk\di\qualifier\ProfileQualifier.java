package com.kaolafm.opensdk.di.qualifier;

import com.kaolafm.opensdk.account.profile.KaolaProfileManager;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import javax.inject.Qualifier;

/**
 * Profile的限定符，用于保证使用的参数和提供的是同一个Profile，
 * 不和{@link KaolaProfileManager}的发生冲突
 * <AUTHOR>
 * @date 2018/7/30
 */
@Qualifier
@Documented
@Retention(RetentionPolicy.RUNTIME)
public @interface ProfileQualifier {

}
