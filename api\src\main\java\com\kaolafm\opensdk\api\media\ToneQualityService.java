package com.kaolafm.opensdk.api.media;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.media.model.ToneQualityResponse;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: AudioService.java                                               
 *                                                                  *
 * Created in 2018/8/13 上午10:16                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
/*package*/ interface ToneQualityService {

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_SOUND_QUALITY)
    Single<BaseResult<ToneQualityResponse>> getSoundQualities();
}
