//package com.kaolafm.opensdk.api.history;
//
//import com.kaolafm.base.utils.ListUtil;
//import com.kaolafm.base.utils.SpUtil;
//import com.kaolafm.opensdk.api.BasePageResult;
//import com.kaolafm.opensdk.api.BaseResult;
//import com.kaolafm.opensdk.api.history.model.ListeningHistory;
//import com.kaolafm.opensdk.api.history.model.SyncHistoryStatus;
//import com.kaolafm.opensdk.db.manager.HistoryDBManager;
//import com.kaolafm.opensdk.di.component.ComponentKit;
//
//import java.util.ArrayList;
//import java.util.List;
//
//import io.reactivex.Single;
//import io.reactivex.functions.Function3;
//
///**
// * 订阅兼容类。
// * 为了兼容低版本弱账号体系，防止出现升级强账号后数据不一样导致用户体验不好。
// * 弱账号体系下，用户无论是否登录都会可以操作订阅历史相关的，并上报服务器。
// * 强账号体系下，只有在登录后才能进行订阅历史操作。
// * 兼容方案是在新版本首先拉取订阅和历史并保持到本地(只能拉取一次，服务端控制)，然后合并。
// *
// * <AUTHOR> Yan
// * @date 2020/8/10
// */
//public class HistorySupport {
//
//    private static final String SP_DEVICE_HISTORY_ONCE = "sp_device_history_once";
//
//    public HistorySupport() {
//        SpUtil.init(ComponentKit.getInstance().getApplication(), SP_DEVICE_HISTORY_ONCE);
//    }
//
//    /**
//     * 获取收听历史列表，一次获取所有的，最多99条。
//     *
//     */
//    public Single<BaseResult<BasePageResult<List<ListeningHistory>>>> getHistoryList(HistoryService service) {
//        //强账号的历史记录
//        Single<BaseResult<BasePageResult<List<ListeningHistory>>>> request = service.getHistoryList();
//        //弱账号下与deviceId绑定的历史记录
//        Single<BaseResult<BasePageResult<List<ListeningHistory>>>> deviceRequest = isDeviceHistoryFetched() ? Single.just(new BaseResult<>()) : service.getDeviceHistoryListOnce();
//        //本地数据库历史记录
//        Single<List<ListeningHistory>> localHistory = new HistoryDBManager().queryAll();
//
//        Function3<BaseResult<BasePageResult<List<ListeningHistory>>>, BaseResult<BasePageResult<List<ListeningHistory>>>, List<ListeningHistory>, BaseResult<BasePageResult<List<ListeningHistory>>>> function = (pageResult, devicePageResult, listeningHistories) -> {
//            BasePageResult<List<ListeningHistory>> basePage = pageResult.getResult();
//            if (basePage == null) {
//                basePage = new BasePageResult<>();
//            }
//            List<ListeningHistory> dataList = basePage.getDataList();
//            if (dataList == null) {
//                dataList = new ArrayList<>();
//            }
//            BasePageResult<List<ListeningHistory>> deviceBasePage = devicePageResult.getResult();
//            List<ListeningHistory> deviceHistories;
//            //先判断是否可以拉取到设备历史数据，如果有就保存本地数据库，并合并。
//            if (deviceBasePage != null && !ListUtil.isEmpty(deviceHistories = deviceBasePage.getDataList())) {
//                saveHistoryToLocal(deviceHistories);
//                dataList.addAll(deviceHistories);
//            } else if (ListUtil.isEmpty(listeningHistories)) {//没有拉取到数据，本地数据库如果有就合并本地。
//                dataList.addAll(listeningHistories);
//            }
//            int size = dataList.size();
//            basePage.setCount(size);
//            basePage.setDataList(dataList);
//            basePage.setHaveNext(size > 0 ? 1 : 0);
//            double pageSize;
//            if ((pageSize = basePage.getPageSize()) > 0) {
//                basePage.setSumPage((int) Math.ceil(size / pageSize));
//            }
//            pageResult.setResult(basePage);
//            return pageResult;
//        };
//        return Single.zip(request, deviceRequest, localHistory, function);
//    }
//
//    /**
//     * 通过本地判断是否已经拉取过数据，避免多次无意义请求。
//     * @return
//     */
//    private boolean isDeviceHistoryFetched() {
//        return false;
//    }
//
//    private void saveHistoryToLocal(List<ListeningHistory> deviceHistories) {
//
//    }
//
//    /**
//     * 清除收听历史
//     *
//     */
//    public Single<BaseResult<SyncHistoryStatus>> clearListeningHistory(HistoryService service) {
////        new HistoryDBManager().deleteAll();
//        return service.clearListeningHistory();
//    }
//}
