package com.kaolafm.opensdk.api.login.internal;

/**
 * <AUTHOR>
 * @date 2018/10/30
 */

public class LinkAccountNumberDTO {
    /**
     * 车厂用户id
     */
    private String userId;
    /**
     * 车厂用户token
     */
    private String userToken;
    /**
     * appId
     */
    private String appId;
    /**
     * SecretKey
     */
    private String secretKey;
    /**
     * 车厂认证服务地址
     */
    private String authenticateUrl;
    /**
     * token有效时间
     */
    private long tokenActiveTime;

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public void setUserToken(String userToken) {
        this.userToken = userToken;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public void setTokenActiveTime(long tokenActiveTime) {
        this.tokenActiveTime = tokenActiveTime;
    }
}
