package com.kaolafm.opensdk.emergencybroadcast;

import android.util.Log;

import com.kaolafm.opensdk.api.emergency.EmergencyRequest;
import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.di.qualifier.KaolaParam;
import com.kaolafm.opensdk.pollingrequest.IPollingRequest;
import com.kaolafm.opensdk.pollingrequest.PollingManager;
import com.kaolafm.opensdk.socket.IConnectListener;
import com.kaolafm.opensdk.socket.SocketApiConstants;
import com.kaolafm.opensdk.socket.SocketEvent;
import com.kaolafm.opensdk.socket.SocketListener;
import com.kaolafm.opensdk.socket.SocketManager;

import java.util.Map;

import javax.inject.Inject;


public class EmergencyBroadcastManager {

    private final String TAG = "EmergencyManager";

    private EmergencyRequest mRequest;

    private static volatile boolean isNeedUploadLocation = false;

    @Inject
    @KaolaParam
    Map<String, String> commonParams;

    private EmergencyBroadcastManager() {
        EmergencySubcomponent subcomponent = (EmergencySubcomponent) ComponentKit.getInstance().getAppSubcomponent();
        if (subcomponent != null) {
            subcomponent.inject(this);
        }
    }

    public static EmergencyBroadcastManager getInstance() {
        return new EmergencyBroadcastManager(); //为了刷新commonParams
    }

    /**
     * @param intervalTimeMinutes 请求间隔时间 单位分钟 10<=intervalTimeMinutes<=1440
     * @param callback            实现成功和失败两个抽象方法
     */
    public void requestEmergencyBroadcast(int intervalTimeMinutes, EmergencyBroadcastListener callback) {
        if (intervalTimeMinutes < 10 || intervalTimeMinutes > 1440) {
            intervalTimeMinutes = 10; //默认10分钟
        }
        if (mRequest == null) {
            mRequest = new EmergencyRequest();
        }
        PollingManager pollingManager = PollingManager.getInstance();
        pollingManager.setBeanToClient(intervalTimeMinutes * 60 * 1000, callback, new IPollingRequest() {
            @Override
            public void invoke(SocketListener listener) {
                if (listener instanceof EmergencyBroadcastListener) {
                    mRequest.getEmergencyPolling((EmergencyBroadcastListener) listener);
                }
            }
        });
        pollingManager.start();

    }

    public void stopPollingRequest() {
        PollingManager.getInstance().stop(SocketEvent.API_MESSAGE);
    }

    /**
     * 应急广播消息推送接口
     *
     * @param callback 实现成功和失败两个抽象方法
     */
    public void requestEmergencyBroadcast(EmergencyBroadcastListener callback) {

        if (commonParams != null) {
            commonParams.put("EIO", "3");
            commonParams.put("transport", "websocket");
        }
        // 消息网关
        SocketManager.getInstance().setMap(commonParams).setSocketHost(SocketApiConstants.SOCKET_HOST).request(callback);
        if (SocketManager.getInstance().canEmit()) {
            Log.i(TAG, "canEmit");
            isNeedUploadLocation = true;
            socketUploadLocationIfNeed();
        } else {
            SocketManager.getInstance().registerConnectListener(new IConnectListener() {
                @Override
                public void onConnected() {
                    Log.i(TAG, "设置true");
                    isNeedUploadLocation = true;
                    socketUploadLocationIfNeed();
                    SocketManager.getInstance().registerConnectListener(null);
                }
            });
        }
    }
    // API 长连接
    public void socketUploadLocationResponse(UploadLocationResponseListener listener) {
        if (commonParams != null) {
            commonParams.put("EIO", "3");
            commonParams.put("transport", "websocket");
        }
        SocketManager.getInstance()
                .setMap(commonParams)
                .setSocketHost(SocketApiConstants.SOCKET_HOST)
                .request(listener);
    }

    /**
     * 上报地理位置经纬度
     */
    //API 长连接
    public void socketUploadLocationIfNeed() {
        if (isNeedUploadLocation) {
            if (commonParams != null) {
                commonParams.put("EIO", "3");
                commonParams.put("transport", "websocket");

                String deviceId = commonParams.get("deviceid");
                if (deviceId != null) {
                    commonParams.put("deviceId", deviceId);
                }
                String requestId = "time:" + System.currentTimeMillis();
                Log.i(TAG, "requestId:" + requestId + " deviceId:" + deviceId);
                commonParams.put("requestId", requestId);
            }
            SocketManager.getInstance()
                    .setMap(commonParams)
                    .setSocketHost(SocketApiConstants.SOCKET_HOST)
                    .request(UploadLocationListener.INSTANCE);
        }
    }

    public void disconnectSocket() {
        Log.i(TAG, "disconnectSocket");
        isNeedUploadLocation = false;
        SocketManager.getInstance().close();
    }
}
