// MicroModule build file where you can declare MicroModule dependencies.
apply plugin: 'com.android.library'
def and = rootProject.ext.android
def dependent = rootProject.ext.dependencies

def VERSION_CODE = and.versionCode
def VERSION_NAME = and.versionName


android {
    compileSdkVersion and.compileSdkVersion
    buildToolsVersion and.buildToolsVersion
    defaultConfig {
        minSdkVersion and.minSdkVersion
        targetSdkVersion and.targetSdkVersion
        versionCode VERSION_CODE
        versionName VERSION_NAME
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-utils-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility and.javaSourceVersion
        targetCompatibility and.javaTargetVersion
    }
    packagingOptions {
        exclude 'LICENSE.txt'
    }

}
dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation ('io.socket:socket.io-client:1.0.1') {
        // excluding org.json which is provided by Android
        exclude group: 'org.json', module: 'json'
    }
    implementation project(path: ':core')
    implementation project(path: ':api')
//    implementation microModule(':common')
}
