package com.kaolafm.opensdk.crash;

import com.kaolafm.opensdk.api.CrashMessageBaseBean;

/**
 * 插播播放状态
 */
public interface Icrashstate {
    /**
     * 播放结束
     *
     * @param state 0-正常播放完成结束  1-未播放完成被结束
     */
    void onCrashstate(int state);

    /**
     * 缓冲完成，开始播放
     */
    void onBufferingUpdate(CrashMessageBaseBean baseBean);

    void onPlayerFailed(PlayerFailedType failedType);

    enum PlayerFailedType {
        FOCUS_GET_FAILED,
    }

    /**
     * 播放暂停
     */
    default void onPlayerPaused(){}

    /**
     * 播放恢复
     */
    default void onPlayerResumed(){}
}
