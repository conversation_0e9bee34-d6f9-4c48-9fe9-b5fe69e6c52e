//package com.kaolafm.opensdk.demo.player;
//
//import android.os.Bundle;
//
//import com.kaolafm.opensdk.demo.detail.StringAdapter.Item;
//
///**
// * <AUTHOR>
// * @date 2018/12/10
// */
//
//public class QQMusicPlayerActivity extends BasePlayerActivity {
//
////    public static final String KEY_DETAIL = "Detail";
////
////    private ColumnMember mColumnMember;
////
////    private MusicListManager mListManager;
////
////    private IPlayerListChangedListener mPlayerListChangedListener;
////
////    private QQMusicRequest mMusicRequest;
////
////    private MusicPlayerManager mPlayerManager;
//
//    @Override
//    public void initArgs() {
//        super.initArgs();
////        mColumnMember = (ColumnMember) getIntent().getSerializableExtra(KEY_DETAIL);
//
//    }
//
//    @Override
//    public void initView(Bundle savedInstanceState) {
////        mPlayerManager = MusicPlayerManager.getInstance();
////        mListManager = MusicListManager.getInstance();
////        super.initView(savedInstanceState);
////        setTitle("QQ音乐播放器");
////        mIvDetailPlayPause.setActivated(mPlayerManager.isPlaying());
////        //添加播放状态监听
////        mPlayerManager.addPlayerStateListener(mPlayerStateListener);
////        mMusicRequest = new QQMusicRequest();
////
////        String cover = OperationAssister.getCover(mColumnMember);
////        if (TextUtils.isEmpty(cover)) {
////            cover = OperationAssister.getIcon(mColumnMember);
////        }
////        showDetail(mColumnMember, cover);
////
////        mPlayerListChangedListener = arrayList -> {
////            for (int i = 0, size = arrayList.size(); i < size; i++) {
////                PlayItem playItem = arrayList.get(i);
////                Item item = new Item();
////                item.type = ResType.TYPE_MUSIC_RADIO_SCENE;
////                item.id = playItem.getAudioId();
////                item.title = playItem.getTitle();
////                item.details = playItem.getHosts()+": "+playItem.getAlbumName();
////                mAdapter.addData(item);
////            }
////            mTrfDetailPlaylist.finishLoadmore();
////        };
////        mListManager.registerPlayerListChangedListener(mPlayerListChangedListener);
//    }
//
//    @Override
//    public void initData() {
////        switch (mType) {
////            case ResType.MUSIC_MINE_PRIVATE_FM:
////                mPlayerManager.playPrivateFM();
////                mTrfDetailPlaylist.setEnableLoadmore(true);
////                break;
////            case ResType.MUSIC_MINE_LIKE:
////                mPlayerManager.playMyLike("");
////                mTrfDetailPlaylist.setEnableLoadmore(false);
////                break;
////            case ResType.MUSIC_MINE_DAY:
////                mPlayerManager.playRecommendSongs("");
////                mTrfDetailPlaylist.setEnableLoadmore(false);
////                break;
////            case ResType.TYPE_MUSIC_RADIO_LABEL:
////                mPlayerManager.playLabelRadio(mId, "");
////                getLabelRadioDetail();
//////                getLabelRadioPlayList();
////                mTrfDetailPlaylist.setEnableLoadmore(true);
////                break;
////            case ResType.TYPE_MUSIC_RADIO_SCENE:
////                mPlayerManager.playSceneRadio(mId, "");
////                getSceneRadioDetail();
//////                getSceneRadioPlayList();
////                mTrfDetailPlaylist.setEnableLoadmore(true);
////                break;
////            case ResType.TYPE_SONG_CHARTS:
////                break;
////            case ResType.TYPE_SONGMENU:
////                break;
////            default:
////        }
//    }
//
//    /**
//     * 获取场景电台的播单。建议用播放器里面的播单数据
//     */
//    private void getSceneRadioPlayList() {
////        mMusicRequest.getSongListOfRadio(mId, new HttpCallback<List<Song>>() {
////            @Override
////            public void onSuccess(List<Song> songs) {
////                if (!ListUtil.isEmpty(songs)) {
////                    for (int i = 0, size = songs.size(); i < size; i++) {
////                        Song song = songs.get(i);
////                        Item item = new Item();
////                        item.type = ResType.TYPE_MUSIC_RADIO_SCENE;
////                        item.id = song.getSongId();
////                        item.title = song.getSongName();
////                        item.details = song.getSingerName();
////                        mAdapter.addData(item);
////                    }
////                }
////            }
////
////            @Override
////            public void onError(ApiException exception) {
////                showError("获取场景电台播单失败", exception);
////            }
////        });
//    }
//
//    private void getSceneRadioDetail() {
//    }
//
//    /**
//     * 获取标签的播单。建议用播放器里面的播单数据
//     */
////    private void getLabelRadioPlayList() {
////        mMusicRequest.getSongListOfCategoryLabel(mId, new HttpCallback<List<Song>>() {
////            @Override
////            public void onSuccess(List<Song> songs) {
////                if (!ListUtil.isEmpty(songs)) {
////                    for (int i = 0, size = songs.size(); i < size; i++) {
////                        Song song = songs.get(i);
////                        Item item = new Item();
////                        item.type = ResType.TYPE_MUSIC_RADIO_LABEL;
////                        item.id = song.getSongId();
////                        item.title = song.getSongName();
////                        item.details = song.getSingerName();
////                        mAdapter.addData(item);
////                    }
////                }
////            }
////
////            @Override
////            public void onError(ApiException exception) {
////                showError("获取播单列表失败", exception);
////            }
////        });
////    }
////
////    private void getLabelRadioDetail() {
////        mMusicRequest.getCategoryDetail(mId, new HttpCallback<SubcategoryLabel>() {
////            @Override
////            public void onSuccess(SubcategoryLabel subcategoryLabel) {
////                if (subcategoryLabel != null) {
////                    showDetail(subcategoryLabel, subcategoryLabel.getCategoryPic());
////                }
////            }
////
////            @Override
////            public void onError(ApiException exception) {
////                showError("获取QQ音乐标签电台详情失败", exception);
////            }
////        });
////    }
//
//    @Override
//    protected void playPre() {
//        mPlayerManager.playPre();
//    }
//
//    @Override
//    protected void switchPlayPause() {
//        mPlayerManager.switchPlayerStatus();
//    }
//
//    @Override
//    protected void playNext() {
//        mPlayerManager.playNext();
//    }
//
//    @Override
//    protected void subscribe(boolean isSubscribe) {
//
//    }
//
//    @Override
//    protected void refresh() {
//
//    }
//
//    @Override
//    protected void loadMore() {
////        switch (mType) {
////            case ResType.MUSIC_MINE_PRIVATE_FM:
////                mPlayerManager.playPrivateFM(false);
////                break;
////            case ResType.MUSIC_MINE_LIKE:
////                mPlayerManager.playMyLike("");
////                break;
////            case ResType.MUSIC_MINE_DAY:
////                mPlayerManager.playRecommendSongs("");
////                break;
////            case ResType.TYPE_MUSIC_RADIO_LABEL:
////                mPlayerManager.playLabelRadio(mId, "", false);
////                break;
////            case ResType.TYPE_MUSIC_RADIO_SCENE:
////                mPlayerManager.playSceneRadio(mId, "", false);
////                break;
////            case ResType.TYPE_SONG_CHARTS:
////                break;
////            case ResType.TYPE_SONGMENU:
////                break;
////            default:
////        }
//    }
//
//    @Override
//    protected void playItem(Item item) {
////        PlayItem playItem = mListManager.getPlayItemById(item.id);
////        mPlayerManager.play(playItem);
//    }
//
//    @Override
//    protected void seek(int progress) {
//        mPlayerManager.seek(progress);
//    }
//
//    @Override
//    protected int getCurrentPosition() {
//        return /*mListManager.getCurPosition()*/0;
//    }
//
//    @Override
//    protected void onDestroy() {
//        super.onDestroy();
////        mPlayerManager.removeIPlayerStateListener(mPlayerStateListener);
////        mListManager.unRegisterPlayerListChangedListener(mPlayerListChangedListener);
//    }
//}
