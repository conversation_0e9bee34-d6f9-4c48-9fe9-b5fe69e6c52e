package com.kaolafm.opensdk.api.operation.model.column;

/**
 * 栏目成员：单曲
 */
public class AudioDetailColumnMember extends ColumnContent {

    /** 单曲的id*/
    private long audioId;

    /** 单曲的收听数*/
    private int playTimes;

    public long getAudioId() {
        return audioId;
    }

    public void setAudioId(long audioId) {
        this.audioId = audioId;
    }

    public int getPlayTimes() {
        return playTimes;
    }

    public void setPlayTimes(int playTimes) {
        this.playTimes = playTimes;
    }

    @Override
    public String toString() {
        return "AudioDetailColumnMember{" +
                "audioId=" + audioId +
                ", playTimes=" + playTimes +
                '}';
    }
}
