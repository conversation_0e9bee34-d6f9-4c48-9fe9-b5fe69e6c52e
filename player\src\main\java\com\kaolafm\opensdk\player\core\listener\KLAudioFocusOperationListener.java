package com.kaolafm.opensdk.player.core.listener;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-12-26 12:15
 ******************************************/
public interface KLAudioFocusOperationListener {
    /**
     * 向系统申请音频焦点前的处理逻辑
     *
     * @param args
     * @return true为成功执行有效逻辑，false为未执行或执行失败有效逻辑
     */
    boolean beforeRequestAudioFocus(Object... args);
}
