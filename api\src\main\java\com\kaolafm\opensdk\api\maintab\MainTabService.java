package com.kaolafm.opensdk.api.maintab;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.maintab.model.MainTabBean;
import com.kaolafm.opensdk.api.operation.model.column.ColumnChild;

import java.util.List;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Query;

/**
 * 一些公共的请求
 */
public interface MainTabService {
    /**
     * 首页导航数据
     * @return
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_MAIN_TAB)
    Single<BaseResult<List<MainTabBean>>> getMainTab();
    /**
     * 首页导航内容
     * @return
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_COLUMNTREE)
    Single<BaseResult<List<ColumnChild>>> getMainTabContent(@Query("withMembers") int withMembers, @Query("zone") String zone);
}
