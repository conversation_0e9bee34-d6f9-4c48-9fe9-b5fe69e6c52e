package com.kaolafm.gradle.plugin.model


import com.kaolafm.gradle.plugin.tasks.BuildSourceJarTask
import com.kaolafm.gradle.plugin.utils.TaskFactory
import org.gradle.api.Project
import org.gradle.api.Task
import org.gradle.api.component.SoftwareComponent
import org.gradle.api.publish.maven.MavenPublication
import org.gradle.api.tasks.bundling.Jar
import org.gradle.api.tasks.javadoc.Javadoc

class MavenPublicationAttachments {

    private final SoftwareComponent softwareComponent
    private final List<Object> allArtifactSources

    MavenPublicationAttachments(SoftwareComponent softwareComponent, def ... allArtifactSources) {
        this(softwareComponent, Arrays.asList(allArtifactSources).asImmutable())
    }

    MavenPublicationAttachments(SoftwareComponent softwareComponent, List<Object> allArtifactSources) {
        this.softwareComponent = softwareComponent
        this.allArtifactSources = allArtifactSources
    }

    final void attachTo(MavenPublication publication) {
        allArtifactSources.each {
            publication.artifact it
        }
        publication.from softwareComponent
    }

    protected static Task sourcesJarTask(Project project, SDKFlavor flavor, def ... sourcePaths) {
        BuildSourceJarTask sourceJarTask = TaskFactory.createBuildSourceJarTask(project, flavor.name)
        sourceJarTask.config(flavor)
        sourceJarTask.from(sourcePaths)
        return sourceJarTask
    }

    protected static Task javadocsJarTask(Project project, String publicationName, Javadoc javadoc) {
        return project.task("generateJavadocsJarFor${publicationName.capitalize()}Publication", type: Jar) { Jar jar ->
            jar.archiveClassifier.set('javadoc')
            jar.from project.files(javadoc)
        }
    }
}
