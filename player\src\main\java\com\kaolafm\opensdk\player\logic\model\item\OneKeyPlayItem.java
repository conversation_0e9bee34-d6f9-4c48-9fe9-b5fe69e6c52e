package com.kaolafm.opensdk.player.logic.model.item;

import android.os.Parcel;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.media.model.AudioFileInfo;
import com.kaolafm.opensdk.player.logic.model.item.model.InfoData;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.RadioInfoData;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import java.util.ArrayList;
import java.util.List;

/**
 * 一键-播放对象
 */
public class OneKeyPlayItem extends PlayItem {

    /**
     * 分音质播放地址
     */
    private List<AudioFileInfo> playUrlData;

    /**
     * 专辑信息相关数据
     */
    private InfoData mInfoData;

    /**
     * 电台信息
     */
    private RadioInfoData mRadioInfoData;

    public OneKeyPlayItem() {
        mInfoData = new InfoData();
        mRadioInfoData = new RadioInfoData();
        playUrlData = new ArrayList<>();
    }

    @Override
    public String getRadioId() {
        return String.valueOf(mRadioInfoData.getRadioId());
    }

    @Override
    public String getTitle() {
        String title = mInfoData.getTitle();
        if(StringUtil.isEmpty(title)){
            title = mInfoData.getAlbumName();
        }
        return title;
    }

    @Override
    public String getPicUrl() {
        String picUrl = mInfoData.getAudioPic();
        if (StringUtil.isEmpty(picUrl)) {
            picUrl = mInfoData.getAlbumPic();
        }
        return picUrl;
    }

    @Override
    public String getHost() {
        return mInfoData.getHosts();
    }

    @Override
    public String getAlbumTitle() {
        return mInfoData.getAlbumName();
    }

    @Override
    public int getType() {
        return PlayerConstants.RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE;
    }

    @Override
    public String getAlbumId() {
        return String.valueOf(mInfoData.getAlbumId());
    }

    @Override
    public String getUpdateTime() {
        return mInfoData.getUpdateTime();
    }

    public InfoData getInfoData() {
        return mInfoData;
    }

    public void setInfoData(InfoData infoData) {
        this.mInfoData = infoData;
    }

    public RadioInfoData getRadioInfoData() {
        return mRadioInfoData;
    }

    public void setRadioInfoData(RadioInfoData radioInfoData) {
        this.mRadioInfoData = radioInfoData;
    }

    @Override
    public String getSourceName() {
        return mInfoData.getSourceName();
    }

    @Override
    public String getSourceLogo() {
        return mInfoData.getSourceLogo();
    }

    public void setPlayUrlData(List<AudioFileInfo> playUrlData) {
        this.playUrlData = playUrlData;
    }

    public List<AudioFileInfo> getPlayUrlDataList() {
        return playUrlData;
    }

    private OneKeyPlayItem(Parcel parcel) {

    }

    public static final Creator<OneKeyPlayItem> CREATOR = new Creator<OneKeyPlayItem>() {

        @Override
        public OneKeyPlayItem createFromParcel(Parcel source) {
            return new OneKeyPlayItem(source);
        }

        @Override
        public OneKeyPlayItem[] newArray(int size) {
            return new OneKeyPlayItem[size];
        }
    };
}
