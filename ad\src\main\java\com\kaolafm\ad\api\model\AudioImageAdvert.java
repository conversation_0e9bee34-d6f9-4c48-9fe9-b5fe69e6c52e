package com.kaolafm.ad.api.model;

import android.os.Parcel;

/**
 * 音图广告的bean。继承自音频广告，并且包含图片广告
 * <AUTHOR>
 * @date 2020-01-17
 */
public class AudioImageAdvert extends AudioAdvert {

    private ImageAdvert imageAdvert;

    public AudioImageAdvert() {
    }

    public ImageAdvert getImageAdvert() {
        return imageAdvert;
    }

    public void setImageAdvert(ImageAdvert imageAdvert) {
        this.imageAdvert = imageAdvert;
        InteractionAdvert interactionAdvert = getInteractionAdvert();
        if (interactionAdvert != null && this.imageAdvert != null) {
            this.imageAdvert.setInteractionAdvert(interactionAdvert);
        }
    }

    @Override
    public void setSubtype(int subtype) {
        super.setSubtype(subtype);
        //图片广告也需要该类型。
        if (imageAdvert != null) {
            imageAdvert.setSubtype(subtype);
        }
    }

    @Override
    public void setInteractionAdvert(InteractionAdvert interactionAdvert) {
        super.setInteractionAdvert(interactionAdvert);
        if (this.imageAdvert != null) {
            imageAdvert.setInteractionAdvert(interactionAdvert);
        }
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        super.writeToParcel(dest, flags);
        dest.writeParcelable(this.imageAdvert, flags);
    }

    protected AudioImageAdvert(Parcel in) {
        super(in);
        this.imageAdvert = in.readParcelable(ImageAdvert.class.getClassLoader());
    }

    public static final Creator<AudioImageAdvert> CREATOR = new Creator<AudioImageAdvert>() {
        @Override
        public AudioImageAdvert createFromParcel(Parcel source) {
            return new AudioImageAdvert(source);
        }

        @Override
        public AudioImageAdvert[] newArray(int size) {
            return new AudioImageAdvert[size];
        }
    };
}
