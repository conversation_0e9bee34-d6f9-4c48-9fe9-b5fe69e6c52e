package com.kaolafm.opensdk.demo.operation.column;

import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.ViewGroup.LayoutParams;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.kaolafm.opensdk.api.operation.model.ImageFile;
import com.kaolafm.opensdk.api.operation.model.column.AlbumDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.AudioDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.BroadcastDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.CategoryColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.LiveProgramDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.RadioDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.RadioQQMusicDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.SearchResultColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.TVDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.WebViewColumnMember;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.demo.player.AlbumPlayerActivity;
import com.kaolafm.opensdk.demo.player.AudioPlayerActivity;
import com.kaolafm.opensdk.demo.player.BasePlayerActivity;
import com.kaolafm.opensdk.demo.player.BroadcastPlayerActivity;
import com.kaolafm.opensdk.demo.player.RadioPlayerActivity;
import com.kaolafm.opensdk.demo.player.TVPlayerActivity;
import com.kaolafm.opensdk.utils.operation.OperationAssister;

import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 栏目详情
 *
 * <AUTHOR> Yan
 * @date 2018/7/31
 */

public class ColumnDetailActivity extends BaseActivity {

    public static final String KEY_COLUMN_MEMBER = "ColumnMember";

    @BindView(R.id.ll_column_detail_root)
    LinearLayout mLlColumnDetailRoot;

    @BindView(R.id.tv_column_detail_info)
    TextView mTvColumnDetailInfo;

    private ColumnMember mColumnMember;

    @Override
    public int getLayoutId() {
        return R.layout.activity_column_detail;
    }


    @Override
    public void initArgs() {
        Intent intent = getIntent();
        if (intent != null) {
            mColumnMember = (ColumnMember) intent.getSerializableExtra(KEY_COLUMN_MEMBER);
        }
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        setTitle("栏目成员详情");
        StringBuilder sb = new StringBuilder();
        sb.append("栏目成员Code：").append(mColumnMember.getCode()).append("\r\n")
                .append("标题：").append(mColumnMember.getTitle()).append("\r\n")
                .append("副标题：").append(mColumnMember.getSubtitle()).append("\r\n")
                .append("描述：").append(mColumnMember.getDescription()).append("\r\n")
                .append("是否显示角标：").append(mColumnMember.getCornerMark() == 1 ? "是" : "否").append("\r\n")
                .append("栏目成员类型：").append(getTypeName()).append("\r\n")
                .append("收听数：").append(getListenNum()).append("\r\n");
                if (mColumnMember instanceof AlbumDetailColumnMember) {
                    AlbumDetailColumnMember albumDetailColumnMember = (AlbumDetailColumnMember)mColumnMember;
                    sb.append("会员：").append(albumDetailColumnMember.getVip() == 1 ? "是" : "否").append("\r\n").append("精品：").append(albumDetailColumnMember.getFine() == 1 ? "是" : "否").append("\r\n");
                }
                sb.append("额外信息：")
                .append(mColumnMember.getExtInfo() == null ? "" : mColumnMember.getExtInfo().toString())
                .append("\r\n");
        mTvColumnDetailInfo.setText(sb);
        Map<String, ImageFile> imageFiles = mColumnMember.getImageFiles();
        if (imageFiles != null && !imageFiles.isEmpty()) {
            sb.delete(0, sb.length());
            Set<Entry<String, ImageFile>> entries = imageFiles.entrySet();
            for (Entry<String, ImageFile> entry : entries) {
                sb.append("图片类型：")
                        .append(entry.getKey()).append("\r\n");
                ImageFile imageFile = entry.getValue();
                sb.append("图片大小：").append(String.valueOf(imageFile.getWidth())).append("x")
                        .append(String.valueOf(imageFile.getHeight()))
                        .append("\r\n");
                TextView imageText = new TextView(this);
                LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(LayoutParams.WRAP_CONTENT,
                        LayoutParams.WRAP_CONTENT);
                imageText.setLayoutParams(layoutParams);
                imageText.setText(sb);
                mLlColumnDetailRoot.addView(imageText);
                Glide.with(this).load(imageFile.getUrl()).into(new SimpleTarget<Drawable>() {
                    @Override
                    public void onResourceReady(@NonNull Drawable resource,
                            @Nullable Transition<? super Drawable> transition) {
                        imageText.setCompoundDrawablesWithIntrinsicBounds(resource, null, null, null);
                    }
                });

            }

        }

    }

    private Integer getListenNum() {
        Integer listenNum = 0;
        if (mColumnMember instanceof AlbumDetailColumnMember) {
            listenNum = ((AlbumDetailColumnMember) mColumnMember).getPlayTimes();
        } else if (mColumnMember instanceof AudioDetailColumnMember) {
            listenNum = ((AudioDetailColumnMember) mColumnMember).getPlayTimes();
        } else if (mColumnMember instanceof BroadcastDetailColumnMember) {
            listenNum = ((BroadcastDetailColumnMember) mColumnMember).getPlayTimes();
        } else if (mColumnMember instanceof RadioDetailColumnMember) {
            listenNum = ((RadioDetailColumnMember) mColumnMember).getPlayTimes();
        }
        return listenNum;
    }

    private String getTypeName() {
        long id = OperationAssister.getId(mColumnMember);
        if (mColumnMember instanceof AlbumDetailColumnMember) {
            return "专辑：ID=" + id;
        } else if (mColumnMember instanceof AudioDetailColumnMember) {
            return "单曲: ID=" + id;
        } else if (mColumnMember instanceof BroadcastDetailColumnMember) {
            return "在线广播: ID=" + id;
        } else if (mColumnMember instanceof CategoryColumnMember) {
            return "分类: IDS=" + ((CategoryColumnMember) mColumnMember).getCategoryCode() + ", 内容类型="
                    + ((CategoryColumnMember) mColumnMember).getContentType();
        } else if (mColumnMember instanceof LiveProgramDetailColumnMember) {
            return "直播: ID=" + id;
        } else if (mColumnMember instanceof RadioDetailColumnMember) {
            return "AI电台: ID=" + id;
        } else if (mColumnMember instanceof RadioQQMusicDetailColumnMember) {
            return "QQ音乐电台: ID=" + id;
        } else if (mColumnMember instanceof SearchResultColumnMember) {
            return "搜索结果: 关键词=" + ((SearchResultColumnMember) mColumnMember).getKeyword();
        } else if (mColumnMember instanceof WebViewColumnMember) {
            return "web页面：url=" + ((WebViewColumnMember) mColumnMember).getUrl();
        }
        return "";
    }

    @Override
    public void initData() {

    }

    @OnClick(R.id.tv_column_detail_info)
    public void showDetailsPage() {
        String id = "";
        int type = 0;
        id = String.valueOf(OperationAssister.getId(mColumnMember));
        type = OperationAssister.getType(mColumnMember);
        Intent intent = new Intent();
        if (mColumnMember instanceof AlbumDetailColumnMember) {
            intent.setClass(this, AlbumPlayerActivity.class);
        } else if (mColumnMember instanceof AudioDetailColumnMember) {
            intent.setClass(this, AudioPlayerActivity.class);
        } else if (mColumnMember instanceof BroadcastDetailColumnMember) {
            intent.setClass(this, BroadcastPlayerActivity.class);
        } else if (mColumnMember instanceof RadioDetailColumnMember) {
            intent.setClass(getApplicationContext(), RadioPlayerActivity.class);
        }else if (mColumnMember instanceof TVDetailColumnMember) {
            intent.setClass(getApplicationContext(), TVPlayerActivity.class);
        } else if (mColumnMember instanceof RadioQQMusicDetailColumnMember) {
//            intent.setClass(this, QQMusicPlayerActivity.class);
//            intent.putExtra(QQMusicPlayerActivity.KEY_DETAIL, mColumnMember);
        }
        intent.putExtra(BasePlayerActivity.KEY_ID, id);
        intent.putExtra(BasePlayerActivity.KEY_TYPE, type);
        startActivity(intent);
    }


}
