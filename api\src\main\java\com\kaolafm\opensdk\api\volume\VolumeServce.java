package com.kaolafm.opensdk.api.volume;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.volume.model.VolumeOption;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;


/**
 * <AUTHOR>
 * @date 2019-11-12
 */
interface VolumeServce {

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_VOLUME_CONFIG)
    Single<BaseResult<VolumeOption>> checkVolumeBalanceStatus();
}
