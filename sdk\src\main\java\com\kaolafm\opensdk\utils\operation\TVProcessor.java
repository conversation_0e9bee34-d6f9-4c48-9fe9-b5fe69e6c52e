package com.kaolafm.opensdk.utils.operation;

import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.operation.model.category.BroadcastCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.CategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.TVCategoryMember;
import com.kaolafm.opensdk.api.operation.model.column.BroadcastDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.TVDetailColumnMember;

import javax.inject.Inject;

/**
 * 听电视 分类/栏目成员处理类
 *
 */
public class TVProcessor implements IOperationProcessor {

    @Inject
    public TVProcessor() {
    }

    @Override
    public boolean accept(CategoryMember member) {
        return member instanceof TVCategoryMember;
    }

    @Override
    public boolean accept(ColumnMember member) {
        return member instanceof TVDetailColumnMember;
    }

    @Override
    public long getId(CategoryMember member) {
        return ((TVCategoryMember) member).getListenTVid();

    }

    @Override
    public long getId(ColumnMember member) {
        return ((TVDetailColumnMember) member).getListenTVid();
    }

    @Override
    public long getListenNum(CategoryMember member) {
        return ((TVCategoryMember) member).getPlayTimes();
    }

    @Override
    public int getType(CategoryMember member) {
        return ResType.TYPE_TV;
    }

    @Override
    public int getType(ColumnMember member) {
        return ResType.TYPE_TV;
    }

    @Override
    public void play(CategoryMember member) {

    }

    @Override
    public void play(ColumnMember member) {

    }
}
