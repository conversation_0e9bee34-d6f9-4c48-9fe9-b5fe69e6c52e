<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:gravity="center">

    <ImageView
        android:id="@+id/iv_brand_logo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:scaleType="centerInside"
        tools:src="@drawable/ic_arrow"
        />

    <TextView
        android:id="@+id/tv_brand_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="kradio Demo"
        android:layout_marginTop="10dp"
        />
    <TextView
        android:id="@+id/tv_brand_agreement"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:autoLink="all"
        android:text="服务协议"
        android:textColor="@color/Blue"
        android:layout_marginTop="10dp"
        />

    <TextView
        android:id="@+id/tv_brand_privacy_agreement"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:autoLink="all"
        android:text="隐私协议"
        android:textColor="@color/Blue"
        android:layout_marginTop="10dp"
        />
    <TextView
        android:id="@+id/tv_brand_introduce"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:autoLink="all"
        android:text="这里是测试协议啊，怎么给修改成这样的聚合总台及全国各地广播频率直播流，持续生产文化类、知识类优质景的声音产品和服务。1中央广播电视总台声音新媒体平台云听，以资资讯)和高品质有声书。\n云听已覆盖手机、车机、平板电脑、智"
        android:textColor="@color/Blue"
        android:layout_marginTop="10dp"
        />
</LinearLayout>