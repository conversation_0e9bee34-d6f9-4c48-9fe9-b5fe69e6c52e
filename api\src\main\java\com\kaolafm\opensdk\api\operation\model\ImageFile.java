package com.kaolafm.opensdk.api.operation.model;

import java.io.Serializable;

/**
 * 图片对象.
 */
public class ImageFile implements Serializable {

    /**
     * 从图片map集合中获取图标的键
     */
    public static final String KEY_ICON = "icon";
    /**
     * 从图片map集合中获取封面的键
     */
    public static final String KEY_COVER = "cover";
    /**
     * 从图片map集合中获取background的键
     */
    public static final String KEY_BG = "background";
    /**
     * 品牌电台logo
     */
    public static final String KEY_LOGO = "logo";
    /**
     * 品牌电台汽车图片
     */
    public static final String KEY_CAR_IMG = "carImg";

    /** 图片url地址*/
    private String url;

    /** 图片的宽，可为0*/
    private int width;

    /** 图片的高，可为0*/
    private int height;

    /** 图片类型，包括{@link #KEY_COVER}、{@link #KEY_ICON}*/
    private String type;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }


    @Override
    public String toString() {
        return "ImageFile{" +
                "url='" + url + '\'' +
                ", width=" + width +
                ", height=" + height +
                ", type='" + type + '\'' +
                '}';
    }
}
