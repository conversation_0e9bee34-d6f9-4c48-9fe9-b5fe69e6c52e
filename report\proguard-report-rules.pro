# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
-basedirectory ../proguard
-include proguard-common-rules.pro

# 这句话能够使我们的项目混淆后产生映射文件
# 包含有类名->混淆后类名的映射关系
-verbose
-printmapping proguardMapping-Report.txt

#数据上报混淆
-dontwarn com.kaolafm.report.**
-keep class com.kaolafm.report.api.**.model.**{*;}
-keep class com.kaolafm.report.database.greendao.*{*;}
-keep class com.kaolafm.report.util.*DBHelper{*;}
-keep class com.kaolafm.report.util.*DBHelperBigData{*;}
-keep class com.kaolafm.report.event.**{*;}
-keep class com.kaolafm.report.model.KaolaActivateData{*;}
-keep class com.kaolafm.report.ReportHelper{*;}

-dontoptimize
