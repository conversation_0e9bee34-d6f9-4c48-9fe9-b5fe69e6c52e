package com.kaolafm.opensdk.socket;

import android.util.Log;

import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.HashMap;
import java.util.Map;

/**
 * socket管理类。也是socket相关对外暴露的接口，所有和socket相关的功能都在该类中暴露。
 * Created by kaolafm on 2019/3/12.
 */
public final class SocketManager {

    private static final String TAG = "SocketManager";

    private Client mClient;

    private SocketManager() {
        if (mClient == null) {
            mClient = SocketClient.getInstance();
        }
    }

    private static final class SocketManagerHolder {
        private static final SocketManager NORMAL_INSTANCE = new SocketManager();
    }

    public static SocketManager getInstance() {
        return SocketManagerHolder.NORMAL_INSTANCE;
    }

    public void open() {
        mClient.create();
        mClient.open();
    }

    /**
     * 设置请求host
     *
     * @param socketHost
     */
    public SocketManager setSocketHost(String socketHost) {
        mClient.setSocketHost(socketHost);
        return this;
    }


    /**
     * 设置公共参数
     *
     * @param map
     */
    public SocketManager setMap(Map<String, String> map) {
        mClient.setMap(map);
        return this;
    }

    public <T> void request(SocketListener<T> callback) {
        if (mClient.canEmit()) {
            if (callback.isNeedRequest()) {
                Log.e(TAG, "===socket request 01 mClient.canEmit():true, callback.isNeedRequest():true");
                mClient.request(callback);
            } else {
                Log.e(TAG, "===socket request 01 mClient.canEmit():true, callback.isNeedRequest():false");
                mClient.addListener(callback);
            }
        } else {
            Log.e(TAG, "===socket request 01 mClient.canEmit():false");
            mClient.create();
            mClient.addListener(callback);
            mClient.open();
        }
    }

    public boolean canEmit() {
        return mClient.canEmit();
    }

    /**
     * 添加监听
     *
     * @param socketListener 监听/回调
     */
    public <T> void addListener(SocketListener<T> socketListener) {
        mClient.addListener(socketListener);
    }

    /**
     * 移除监听
     *
     * @param socketListener
     * @param <T>
     */
    public <T> void removeListener(SocketListener<T> socketListener) {
        mClient.removeListener(socketListener);
    }

    /**
     * 直接发送对应事件的长连接请求
     *
     * @param event     事件名
     * @param newParams 参数
     * @param callback  回调
     */
    public <T> void request(String event, Map<String, Object> newParams, HttpCallback<T> callback) {

        SocketListener<T> socketListener = new SocketListener<T>() {

            @Override
            public void onSuccess(T t) {
                Log.e("coin", "onSuccess: t=" + t);
                if (callback != null) {
                    callback.onSuccess(t);
                }
            }

            @Override
            public void onError(ApiException e) {
                if (callback != null) {
                    callback.onError(e);
                }
            }

            @Override
            public String getEvent() {
                return event;
            }

            @Override
            public boolean isNeedParams() {
                return false;
            }

            @Override
            public boolean isNeedRequest() {
                return true;
            }

            @Override
            public Map<String, Object> getParams(Map params) {
                return newParams;
            }
        };
        request(socketListener);
    }

    public void close() {
        if (mClient != null) {
            mClient.release();
        }
    }

    public void registerConnectErrorListener(ConnectErrorListener connectErrorListener) {
        mClient.registerConnectErrorListener(connectErrorListener);
    }

    public void registerConnectLostListener(ConnectLostListener connectLostListener) {
        mClient.registerConnectLostListener(connectLostListener);
    }

    /**
     * 重复注册会覆盖之前的监听
     *
     * @param connectListener
     */
    public void registerConnectListener(IConnectListener connectListener) {
        if (mClient instanceof SocketClient) {
            ((SocketClient) mClient).registerConnectListener(connectListener);
        } else {
            Log.i(TAG, "not instance of SocketClient");
        }
    }
}
