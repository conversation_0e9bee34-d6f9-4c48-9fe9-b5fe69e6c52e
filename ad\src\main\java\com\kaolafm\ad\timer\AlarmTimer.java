package com.kaolafm.ad.timer;

import android.app.AlarmManager;
import android.app.Application;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.SystemClock;

import com.kaolafm.ad.api.model.AdvertisingDetails;
import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.log.Logging;

import java.util.List;

import javax.inject.Inject;

/**
 * 通过闹钟实现的定时功能
 * do -> 定时广告
 *
 * <AUTHOR>
 * @date 2020-02-06
 */
@AppScope
public class AlarmTimer extends AbstractTimer {

    private Context mContext;

    private boolean started = false;

    private AlarmManager mManager;

    private static final String ACTION = "advert-timer";

    @Inject
    AlarmTimer(Application context) {
        mContext = context;
    }

    @Override
    public void start() {
        if (!started) {
            TimerReceiver receiver = new TimerReceiver();
            IntentFilter filter = new IntentFilter();
            filter.addAction("advert-timer");
            mContext.registerReceiver(receiver, filter);
            mManager = (AlarmManager) mContext.getSystemService(Context.ALARM_SERVICE);
            started = true;
        }
    }

    @Override
    public void addTask(AdvertTask task) {
        int id = task.getId();
        //这个是触发任务的起始时间。
        long triggerAtMillis = task.getTimeDuration() + SystemClock.elapsedRealtime();
        Logging.d("添加定时任务, id=%s, Timestamp=%s", id, DateUtil.formatMillis(task.getTimestamp()));
        if (mManager != null) {
            PendingIntent pendingIntent = getIntent(task.getTimestamp(), id);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                mManager.setExactAndAllowWhileIdle(AlarmManager.ELAPSED_REALTIME, triggerAtMillis, pendingIntent);
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                mManager.setExact(AlarmManager.ELAPSED_REALTIME, triggerAtMillis, pendingIntent);
            } else {
                mManager.set(AlarmManager.ELAPSED_REALTIME, triggerAtMillis, pendingIntent);
            }
        }
    }

    @Override
    public void removeTask(AdvertTask task) {
        long timestamp;
        if (task == null || (timestamp = task.getTimestamp()) < DateUtil.getServerTime()) {
            return;
        }
        removeTask(timestamp, task.getId());
    }

    @Override
    public void stop() {

        mAdvertDBManager.queryTimedAdvert(detailsList -> {
            for (AdvertisingDetails details : detailsList) {
                List<String> timestamps = details.getAdPlayTimestamps();
                if (ListUtil.isEmpty(timestamps)) {
                    continue;
                }
                for (String time : timestamps) {
                    long timestamp = DateUtil.string2Millis(time);
                    if (timestamp > DateUtil.getServerTime()) {
                        removeTask(timestamp, details.getCreativeId().intValue());
                    }
                }
            }
        });
    }

    private void removeTask(long time, int advertId) {
        if (mManager != null) {
            //取消未执行的定时任务。
            if (time > DateUtil.getServerTime()) {
                PendingIntent intent = getIntent(time, advertId);
                if (intent != null) {
                    Logging.d("取消定时任务， time=%s, advert=%s", DateUtil.formatMillis(time), advertId);
                    mManager.cancel(intent);
                }
            }
        }
    }

    /**
     * 根据时间和广告id获取intent，这里的时间作为唯一的requestCode。
     *
     * @param timestamp
     * @param advertId
     * @return
     */
    private PendingIntent getIntent(long timestamp, int advertId) {
        int time = (int) (timestamp / 1000);
        Intent intent = new Intent(ACTION);
        intent.putExtra(ID, advertId);
        intent.putExtra(TIME, time);
        return PendingIntent.getBroadcast(mContext, time, intent, PendingIntent.FLAG_UPDATE_CURRENT);
    }


    public class TimerReceiver extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent != null) {
                int id = intent.getIntExtra(ID, -1);
                int time = intent.getIntExtra(TIME, -1);
                expose(id, time);
            }
        }
    }
}
