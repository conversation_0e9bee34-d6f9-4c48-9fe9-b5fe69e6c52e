package com.kaolafm.opensdk.api.media;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.api.media.model.AudioPlayInfo;
import com.kaolafm.opensdk.api.media.model.VideoAudioDetails;

import java.util.List;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Query;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: AudioService.java                                               
 *                                                                  *
 * Created in 2018/8/13 上午10:16                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
/*package*/ interface VideoAudioService {

//    @Headers("https://26e9a8ef-3e24-456a-9bc7-2a4479e5bf57.mock.pstmn.io")
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_VIDEO_AUDIO_DETAILS_SINGLE)
    Single<BaseResult<VideoAudioDetails>> getAudioDetails(@Query("id") long id);

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_VIDEO_AUDIO_PLAYINFO_SINGLE)
    Single<BaseResult<AudioPlayInfo>> getAudioPlayInfo(@Query("playUrlId") String playUrlId);

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_VIDEO_AUDIO_DETAILS_MULTIPLE)
    Single<BaseResult<List<VideoAudioDetails>>> getAudioDetails(@Query("ids") String id);

}
