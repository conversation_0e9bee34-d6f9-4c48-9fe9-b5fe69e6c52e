package com.kaolafm.opensdk.log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * log打印的接口，实现为具体打印的方法。
 *
 * <AUTHOR>
 * @date 2018/12/19
 */
public interface Printer {

    /**
     * 本次打印使用指定的tag进行打印。
     * @param tag
     * @return
     */
    Printer tag(@Nullable String tag);

    void d(@NonNull String message, @Nullable Object... args);

    void d(@Nullable Object object);

    void e(@NonNull String message, @Nullable Object... args);

    void e(@Nullable Throwable throwable, @NonNull String message, @Nullable Object... args);

    void w(@NonNull String message, @Nullable Object... args);

    void i(@NonNull String message, @Nullable Object... args);

    void v(@NonNull String message, @Nullable Object... args);

    void wtf(@NonNull String message, @Nullable Object... args);

    /**
     * 格式化json并打印
     */
    void json(@Nullable String json);

    /**
     * 格式化xml并打印
     */
    void xml(@Nullable String xml);

    /**
     * 打印log
     *
     * @param logLevel log的等级。
     * @param tag      log的tag。
     * @param message      要打印的log的信息。
     * @param throwable 异常
     */
    void println(int logLevel, @Nullable String tag, @Nullable String message, @Nullable Throwable throwable);
}
