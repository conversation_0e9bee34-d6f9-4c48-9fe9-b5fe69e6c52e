package com.kaolafm.opensdk.player.logic.model.item;

import android.os.Parcel;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.media.model.AudioFileInfo;
import com.kaolafm.opensdk.player.logic.model.item.model.InfoData;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.TimeInfoData;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import java.util.ArrayList;
import java.util.List;

/**
 * 直播- 播放对象
 */
public class LivePlayItem extends PlayItem {

    /**
     * 信息相关数据
     */
    private InfoData mInfoData;

    /**
     * 播放时间相关数据
     */
    private TimeInfoData mTimeInfoData;

    /**
     * 聊天室id
     */
    private long liveId;

    /**
     * 直播状态
     */
    private int status;

    /**
     * 主人id
     */
    private String comperesId;

    /**
     * 节目图片
     */
    private String programPic;

    private List<AudioFileInfo> playListUrlInfo;

    public LivePlayItem() {
        mInfoData = new InfoData();
        mTimeInfoData = new TimeInfoData();
        playListUrlInfo = new ArrayList<>();
    }

    @Override
    public String getRadioId() {
        return String.valueOf(mInfoData.getAlbumId());
    }

    @Override
    public String getTitle() {
        String title = mInfoData.getTitle();
        if(StringUtil.isEmpty(title)){
            title = mInfoData.getAlbumName();
        }
        return title;
    }

    @Override
    public String getPicUrl() {
        String picUrl = mInfoData.getAudioPic();
        if (StringUtil.isEmpty(picUrl)) {
            picUrl = mInfoData.getAlbumPic();
        }
        return picUrl;
    }

    @Override
    public String getHost() {
        return mInfoData.getHosts();
    }

    @Override
    public String getAlbumTitle() {
        return mInfoData.getAlbumName();
    }


    @Override
    public String getAlbumId() {
        return String.valueOf(mInfoData.getAlbumId());
    }

    @Override
    public String getBeginTime() {
        return mTimeInfoData.getBeginTime();
    }

    @Override
    public String getEndTime() {
        return mTimeInfoData.getEndTime();
    }
    @Override
    public String getUpdateTime() {
        return mInfoData.getUpdateTime();
    }
    @Override
    public int getType() {
        return PlayerConstants.RESOURCES_TYPE_LIVING;
    }

    public long getLiveId() {
        return liveId;
    }

    public void setLiveId(long liveId) {
        this.liveId = liveId;
    }

    @Override
    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public InfoData getInfoData() {
        return mInfoData;
    }

    public void setInfoData(InfoData infoData) {
        this.mInfoData = infoData;
    }

    public TimeInfoData getTimeInfoData() {
        return mTimeInfoData;
    }

    public void setTimeInfoData(TimeInfoData timeInfoData) {
        this.mTimeInfoData = timeInfoData;
    }

    public String getComperesId() {
        return comperesId;
    }

    public void setComperesId(String comperesId) {
        this.comperesId = comperesId;
    }

    public String getProgramPic() {
        return programPic;
    }

    public void setProgramPic(String programPic) {
        this.programPic = programPic;
    }

    @Override
    public boolean isLiving() {
        return status == PlayerConstants.BROADCAST_STATUS_LIVING;
    }

    public List<AudioFileInfo> getPlayListUrlInfo() {
        return playListUrlInfo;
    }

    public void setPlayListUrlInfo(List<AudioFileInfo> playListUrlInfo) {
        this.playListUrlInfo = playListUrlInfo;
    }

    private LivePlayItem(Parcel parcel) {

    }

    public static final Creator<LivePlayItem> CREATOR = new Creator<LivePlayItem>() {

        @Override
        public LivePlayItem createFromParcel(Parcel source) {
            return new LivePlayItem(source);
        }

        @Override
        public LivePlayItem[] newArray(int size) {
            return new LivePlayItem[size];
        }
    };

    @Override
    public long getFinishTime() {
        return mTimeInfoData.getFinishTime();
    }
}
