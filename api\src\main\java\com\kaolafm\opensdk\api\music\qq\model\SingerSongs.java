package com.kaolafm.opensdk.api.music.qq.model;

import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * 歌手的歌曲信息
 * <AUTHOR>
 * @date 2018/4/23
 */

public class SingerSongs extends BaseMusicResult<List<Song>> {


    /**
     * album_sum : 24
     * area : 港台
     * singer_id : 13
     * singer_mid : 000nmQ1v0JGExN
     * singer_name : 成龙
     * singer_pic : http://y.gtimg.cn/music/photo_new/T001R120x120M000000nmQ1v0JGExN.jpg
     * singer_translator_name : <PERSON>
     * song_sum : 173
     * songlist : [{"album_id":1829928,"album_mid":"004AynMd4FHzK8","album_name":"2017北京电视台春节联欢晚会","album_pic":"http://y.gtimg.cn/music/photo_new/T002R120x120M000004AynMd4FHzK8.jpg","genre":"Pop 流行","hot":0,"isonly":1,"k_song_id":0,"k_song_mid":"","language":"国语","playable":1,"public_time":"2017-01-28","singer_id":13,"singer_mid":"000nmQ1v0JGExN","singer_name":"成龙","singer_pic":"http://y.gtimg.cn/music/photo_new/T001R120x120M000000nmQ1v0JGExN.jpg","size_try":0,"song_h5_url":"http://c.y.qq.com/v8/playsong.html?songmid=000mn6p83durhb&ADTAG=opi12345670","song_id":200558073,"song_mid":"000mn6p83durhb","song_name":"叫醒冬天","song_play_time":265,"song_play_url":"http://isure.stream.qqmusic.qq.com/C200000mn6p83durhb.m4a?vkey=4312B035E5DD1EDECE7EEF59800A3B1E4F412EC1C36275408099C51429CEBDA049A31E8B1AC4EE21108732D1092099A9AC0BC961E8E0E0D1&guid=1234713248&fromtag=50&uin=1152921504712243993","song_play_url_hq":"","song_play_url_sq":"","song_play_url_standard":"http://isure.stream.qqmusic.qq.com/C400000mn6p83durhb.m4a?vkey=E712E76466A50E0C34AB47302BA3CF97EE3D8E93CA9F5919F7BABB57ECB592139F0C689043EE2600A93A1656484E33E4574587CA6E2DF51E&guid=1234713248&fromtag=50&uin=1152921504712243993","song_size":1312564,"song_size_hq":0,"song_size_sq":0,"song_size_standard":2282272,"try_begin":0,"try_end":0}]
     */

    @SerializedName("album_sum")
    private int albumSum;

    @SerializedName("area")
    private String area;

    @SerializedName("singer_id")
    private int singerId;

    @SerializedName("singer_mid")
    private String singerMid;

    @SerializedName("singer_name")
    private String singerName;

    @SerializedName("singer_pic")
    private String singerPic;

    @SerializedName("singer_translator_name")
    private String singerTranslatorName;

    @SerializedName("song_sum")
    private int songSum;
}
