package com.kaolafm.opensdk.http.socket;

import android.os.Handler;
import android.os.Looper;

import com.google.gson.Gson;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.http.error.ApiException;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @date 2020-02-05
 */
public class ListenerWrapper<T> implements Emitter.Listener {

    private String event;

    private SocketListener<T> callback;

    private Gson mGson;

    public ListenerWrapper(Gson gson) {
        mGson = gson;
    }

    public ListenerWrapper(String event, SocketListener<T> callback) {
        this.event = event;
        this.callback = callback;
    }

    public void setCallback(String event, SocketListener<T> callback) {
        this.event = event;
        this.callback = callback;
    }

    @Override
    public void call(Object... args) {
        if (callback == null) {
            return;
        }
        if (!ListUtil.isEmpty(args)) {
            Object msg = args[0];
            T t = (T) getResult(msg, callback.getClass());
            if (t != null) {
                runOnUI(() -> callback.onSuccess(t));
                return;
            }
        }
        runOnUI(() -> callback.onError(new ApiException("数据为空")));
    }


    private void runOnUI(Runnable task) {
        new Handler(Looper.getMainLooper()).post(task);
    }

    /**
     * 解析结果，返回泛型的bean。
     * 这里可以抽出一个工具类，目前只有Socket用到，后面其他有用到在抽个工具类
     *
     * @param msg   原始json数据
     * @param clazz
     * @return 解析后的结果。
     */
    private <T> T getResult(Object msg, Class<T> clazz) {
        if (msg == null) {
            return null;
        }
        Type genericSuperclass = clazz.getGenericInterfaces()[0];
        Type typeArgument = ((ParameterizedType) genericSuperclass).getActualTypeArguments()[0];
        Type type = new ParameterizedTypeImpl(BaseResult.class, new Type[]{typeArgument});
        BaseResult<T> result = mGson.fromJson(msg.toString(), type);
        return result == null ? null : result.getResult();
    }

    /**
     * 解析泛型的类。
     */
    private class ParameterizedTypeImpl implements ParameterizedType {

        private final Class raw;
        private final Type[] args;

        public ParameterizedTypeImpl(Class raw, Type[] args) {
            this.raw = raw;
            this.args = args != null ? args : new Type[0];
        }

        @Override
        public Type[] getActualTypeArguments() {
            return args;
        }

        @Override
        public Type getRawType() {
            return raw;
        }

        @Override
        public Type getOwnerType() {
            return null;
        }
    }
}
