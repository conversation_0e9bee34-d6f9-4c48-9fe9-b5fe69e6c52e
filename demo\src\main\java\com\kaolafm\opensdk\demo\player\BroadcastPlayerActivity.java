package com.kaolafm.opensdk.demo.player;

import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.Toast;

import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.broadcast.BroadcastDetails;
import com.kaolafm.opensdk.api.broadcast.BroadcastRequest;
import com.kaolafm.opensdk.api.broadcast.ProgramDetails;
import com.kaolafm.opensdk.api.subscribe.SubscribeRequest;
import com.kaolafm.opensdk.demo.PlayerStateListenerWrapper;
import com.kaolafm.opensdk.demo.detail.StringAdapter.Item;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayListControl;
import com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.PlaylistInfo;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playlist.BroadcastPlayListControl;
import com.kaolafm.opensdk.player.logic.playlist.util.PlayListUtils;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

/**
 * 广播播放器页面
 *
 * <AUTHOR> Yan
 * @date 2018/12/7
 */

public class BroadcastPlayerActivity extends BasePlayerActivity {

    @Override
    public void initView(Bundle savedInstanceState) {
        super.initView(savedInstanceState);
        setTitle("广播播放器页面");
        mTrfDetailPlaylist.setEnableLoadmore(false);

        //添加播放状态监听
        PlayerManager.getInstance().addPlayControlStateCallback(mPlayerStateListener);
        PlayerManager.getInstance().addPlayListControlStateCallback(iPlayListStateListener);
    }

    @Override
    public void initData() {
        //根据ID播放广播
        PlayerManager.getInstance().start(new PlayerBuilder().setId(String.valueOf(mId)).setType(PlayerConstants.RESOURCES_TYPE_BROADCAST));
        getCurrentProgramDetails();
        SimpleDateFormat formatter = new SimpleDateFormat("YYYY-MM-dd");
        Date curDate = new Date(System.currentTimeMillis());
        String createDate = formatter.format(curDate);
        if (createDate.equals(mDate) || TextUtils.isEmpty(mDate)) {
            // 如果是今日播单，播单监听回调会返回节日单数据
        } else {
            // 如果是今日之前的播单，需要延时(等播单初始化完成)获取当日节目单数据
            Timer timer = new Timer();
            timer.schedule(new TimerTask() {
                @Override
                public void run() {
                    getBroadcastPlaylist();
                    this.cancel();
                }
            }, 1000);
        }
        getSubscribeState();
    }

    /**
     * 获取该广播订阅状态
     */
    private void getSubscribeState() {
        new SubscribeRequest().isSubscribed(mId, new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                isSubscribed = aBoolean;
                btnSubscribe.setText(aBoolean ? "取消订阅" : "订阅");
                btnSubscribe.setEnabled(true);
                btnSubscribe.setVisibility(View.VISIBLE);
            }

            @Override
            public void onError(ApiException exception) {
                btnSubscribe.setText(exception.getMessage());
            }
        });
    }

    private void getBroadcastPlaylist() {
        new BroadcastRequest().getBroadcastProgramList(mId, mDate, new HttpCallback<List<ProgramDetails>>() {
            @Override
            public void onSuccess(List<ProgramDetails> programDetails) {
                List<PlayItem> playItemList = PlayListUtils.programDetailsToPlayItem(programDetails, PlayerManager.getInstance().getPlayListInfo().getBroadcastChannel(), 0, 10l,0,null,null);
                showListInfo(playItemList);
            }

            @Override
            public void onError(ApiException exception) {
                showError("加载播单错误", exception);
            }
        });
    }

    private void showListInfo(List<PlayItem> playItemList) {
        if (ListUtil.isEmpty(playItemList)) {
            mAdapter.clear();
        } else {
            List<Item> datas = new ArrayList<>();
            for (int i = 0; i < playItemList.size(); i++) {
                BroadcastPlayItem item = (BroadcastPlayItem) playItemList.get(i);
                Item sai = new Item();
                sai.id = item.getAudioId();
                sai.type = ResType.TYPE_BROADCAST;
                sai.title = item.getTitle();
                sai.details = getStatus(item.getStatus()) + "  " + item.getBeginTime() + "-" + item.getEndTime();
                sai.item = item;
                sai.playItem = item;
                datas.add(sai);
            }
            mAdapter.setDataList(datas);
            boolean hasLiving = false;
            for (int i = 0; i < playItemList.size(); i++) {
                if (playItemList.get(i).getStatus() == 1) {
                    hasLiving = true;
                    return;
                }
            }
            if (!hasLiving) {
                IPlayListControl iPlayListControl = PlayerManager.getInstance().getPlayListControl();
                if (iPlayListControl instanceof BroadcastPlayListControl) {
                    ((BroadcastPlayListControl) iPlayListControl).getSongPlayList().clear();
                    ((BroadcastPlayListControl) iPlayListControl).addSongPlayItem(playItemList);
                    PlayerManager.getInstance().startPlayItemInList(playItemList.get(0),null);
                    select();
                }
            }
        }
    }

    String getStatus(int status) {
        String str = "";
        if (status == 2) {
            str = "回放";
        } else if (status == 1) {
            str = "直播中";
        } else if (status == 3) {
            str = "未开始";
        }
        return str;
    }

    /**
     * 获取当前节目详情
     */
    private void getCurrentProgramDetails() {
        new BroadcastRequest().getBroadcastCurrentProgramDetails(mId, new HttpCallback<ProgramDetails>() {
            @Override
            public void onSuccess(ProgramDetails programDetails) {
                showDetail(programDetails, programDetails.getIcon());
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取广播详情错误", exception);
            }
        });
    }

    @Override
    protected void playPre() {
        PlayerManager.getInstance().playPre();
    }

    @Override
    protected void switchPlayPause() {
        PlayerManager.getInstance().switchPlayerStatus(true);
    }

    @Override
    protected void playNext() {
        PlayerManager.getInstance().playNext();
    }

    @Override
    protected void refresh() {

    }

    @Override
    protected void loadMore() {
        //广播播单没有加载更多
    }

    @Override
    protected void playItem(Item item) {
        if (item.playItem instanceof BroadcastPlayItem) {
            BroadcastPlayItem broadcastPlayItem = (BroadcastPlayItem) item.playItem;
            Toast.makeText(BroadcastPlayerActivity.this,"programEnable字段："+broadcastPlayItem.getProgramEnable(),Toast.LENGTH_LONG).show();
//            switch (broadcastPlayItem.getProgramEnable()) {
//                //1开启节目（建议置灰，不可点击，可以提示回放生成中），0关闭节目（建议隐藏）, 2可播放。
//                case 0:
//                    Toast.makeText(BroadcastPlayerActivity.this,"0-关闭节目（建议隐藏）",Toast.LENGTH_LONG);
//                    break;
//                case 1:
//                    Toast.makeText(BroadcastPlayerActivity.this,"1-开启节目（建议置灰，不可点击，可以提示回放生成中）",Toast.LENGTH_LONG);
//                    break;
//                case 2:
//                    PlayerManager.getInstance().startPlayItemInList(item.playItem);
//                    break;
//            }
        }
        PlayerManager.getInstance().startPlayItemInList(item.playItem,null);
    }

    @Override
    protected void seek(int progress) {
        PlayerManager.getInstance().seek(progress);
    }

    @Override
    protected int getCurrentPosition() {
        if (PlayerManager.getInstance().getPlayListControl() == null) {
            return -1;
        } else {
            return PlayerManager.getInstance().getPlayListControl().getCurPosition();
        }

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        PlayerManager.getInstance().removePlayControlStateCallback(mPlayerStateListener);
        PlayerManager.getInstance().removePlayListControlStateCallback(iPlayListStateListener);
    }

    private IPlayListStateListener iPlayListStateListener = new IPlayListStateListener() {

        @Override
        public void onPlayListChange(List<PlayItem> playItemList) {
            showToast("广播播单发生变化");
            showListInfo(playItemList);
        }

        @Override
        public void onPlayListChangeError(PlayItem playItem, int i, int i1) {

        }
    };
}
