package com.kaolafm.ad.expose;

import com.kaolafm.ad.api.model.Advert;

/**
 * 广告上报接口
 *
 * <AUTHOR>
 * @date 2020-01-09
 */
public interface AdvertisingReporter {

    /**
     * 上报广告展示。图片广告开始展示时调用该接口。
     *
     * @param advert   {@link Advert}及其之类
     */
    void display(Advert advert);

    /**
     * 上报广告展示结束。该接口在广告正常展示完毕时调用。
     *
     * @param advert   {@link Advert}及其之类
     */
    void endDisplay(Advert advert);

    /**
     * 上报广告展示打断。该接口在非正常中断时调用，除了跳过、正常展示完毕的中断都是非正常中断。
     *
     * @param advert   {@link Advert}及其之类
     */
    void interruptDisplay(Advert advert);

    /**
     * 上报广告开始播放。音频广告开始播放时调用该接口。
     *
     * @param advert   {@link Advert}及其之类
     */
    void play(Advert advert);

    /**
     * 上报广告播放结束。结束播放就调用该接口
     *
     * @param advert   {@link Advert}及其之类
     */
    void endPlay(Advert advert,long playTime);

    /**
     * 上报广告被点击。
     *
     * @param advert   {@link Advert}及其之类
     */
    void click(Advert advert);

    /**
     * 上报广告跳过。
     *
     * @param advert   {@link Advert}及其之类
     */
    void skip(Advert advert);

    /**
     * 上报展示广告二次互动。
     *
     * @param advert   {@link Advert}及其之类
     */
    void displayInteraction(Advert advert);

    /**
     * 上报广告结束二次互动。
     *
     * @param advert   {@link Advert}及其之类
     */
    void endInteraction(Advert advert);

}
