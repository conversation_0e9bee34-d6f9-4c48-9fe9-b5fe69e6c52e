package com.kaolafm.ad.expose;

import com.kaolafm.ad.api.model.AudioAdvert;

/**
 * 音频广告接口
 *
 * <AUTHOR>
 * @date 2020-01-09
 */
public interface AdvertisingPlayer extends Executor {

    /**
     * 播放音频广告。所有的音频广告都会通过该方法播放。
     *
     * @param audioAdvert 音频广告
     */
    void play(AudioAdvert audioAdvert);

    /**
     * 停止播放。所有的音频广告都会通过该方法停止播放。
     *
     * @param audioAdvert 音频广告
     */
    void stop(AudioAdvert audioAdvert);

    /**
     * 暂停播放。所有的音频广告都会通过该方法暂停播放。
     *
     * @param audioAdvert 音频广告
     */
    void pause(AudioAdvert audioAdvert);

}
