package com.kaolafm.opensdk;

import android.app.Application;
import android.text.TextUtils;
import android.util.Log;


import com.kaolafm.ad.AdvertOptions;
import com.kaolafm.ad.AdvertisingEngine;
import com.kaolafm.ad.di.component.DaggerAdvertisingComponent;
import com.kaolafm.base.utils.NetworkUtil;
import com.kaolafm.base.utils.SeCoreUtils;
import com.kaolafm.base.utils.SpUtil;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.crash.InitSDKException;
import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.permission.LocationUtil;
import com.kaolafm.core.BuildConfig;

/**
 * OpenSDK初始化。合并打包时
 *
 * <AUTHOR>
 * @date 2018/4/13
 */
public final class OpenSDK {
    private static final String TAG = "OpenSDK";
    private static class SingletonHelper {
        private static final OpenSDK INSTANCE = new OpenSDK();
    }

    public static OpenSDK getInstance() {
        return SingletonHelper.INSTANCE;
    }

    private final OpenSDKEngine mEngine;

    private OpenSDK() {
        mEngine = new OpenSDKEngine();
    }

    public Engine getmEngine() {
        return mEngine;
    }

    public void enableAutoLocation() {
        LocationUtil.instance.getLocation();
    }

    public Application getContext() {
        return ComponentKit.getInstance().getApplication();
    }

    private boolean isPreInited = false;

    public void preInit(Application application) {
        Log.i(TAG, "preInit: 开始预初始化SDK");
        isPreInited = true;
    }

    public void preInit(Application application, boolean openBugReport) {
        Log.i(TAG, "preInit: 开始预初始化SDK，openBugReport=" + openBugReport);
        isPreInited = true;
    }

    /**
     * 初始化sdk
     */
    public void initSDK(Application application, String deviceId, HttpCallback<Boolean> callback) throws InitSDKException {
        Log.i(TAG, "initSDK: 开始初始化SDK，deviceId=" + deviceId);
        AdvertOptions options = new AdvertOptions();
        options.setDeviceId(deviceId);
        initSDK(application, options, callback);
    }

    public void initSDK(Application application, AdvertOptions options, HttpCallback<Boolean> callback) throws InitSDKException {
        Log.i(TAG, "initSDK: 开始初始化SDK，options=" + options);
        try {
            initStepOne(application, options.getDeviceId(), options);
            mEngine.init(application, options, callback);
            Log.i(TAG, "initSDK: SDK初始化完成");
        } catch (Exception e) {
            Log.e(TAG, "initSDK: SDK初始化失败", e);
            throw e;
        }
    }

    /***
     * 激活设备
     */
    public void activate(HttpCallback<Boolean> callback) {
        Log.i(TAG, "activate: 开始激活SDK");
        mEngine.activate(new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                Log.i(TAG, "activate: SDK激活成功");
                if (callback != null) {
                    callback.onSuccess(aBoolean);
                }
            }

            @Override
            public void onError(ApiException exception) {
                Log.e(TAG, "activate: SDK激活失败", exception);
                if (callback != null) {
                    callback.onError(exception);
                }
            }
        });
    }

    /**
     * 初始化后激活
     */
    public void initAndActivate(Application application, String deviceId, HttpCallback<Boolean> callback) throws InitSDKException {
        Log.i(TAG, "initAndActivate: 开始初始化并激活SDK，deviceId=" + deviceId);
        AdvertOptions options = new AdvertOptions();
        try {
            initStepOne(application, deviceId, options);
            mEngine.config(application, options, new HttpCallback<Boolean>() {
                @Override
                public void onSuccess(Boolean aBoolean) {
                    Log.i(TAG, "initAndActivate: SDK初始化并激活成功");
                    if (callback != null) {
                        callback.onSuccess(aBoolean);
                    }
                }

                @Override
                public void onError(ApiException exception) {
                    Log.e(TAG, "initAndActivate: SDK初始化并激活失败", exception);
                    if (callback != null) {
                        callback.onError(exception);
                    }
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "initAndActivate: SDK初始化并激活失败", e);
            throw e;
        }
    }

    private void initStepOne(Application application, String deviceId, AdvertOptions options) throws InitSDKException {
        Log.i(TAG, "initStepOne: 初始化第一步，检查预初始化状态和deviceId");
        if (!isPreInited) {
            Log.e(TAG, "initStepOne: 未进行预初始化，请先在Application的onCreate方法中调用preInit");
            throw new InitSDKException("Please do preInit first in application onCreate method");
        }
        options.setDeviceId(deviceId);
        if (TextUtils.isEmpty(deviceId) || deviceId.length() > 64) {
            Log.e(TAG, "initStepOne: deviceId为空或长度超过64，deviceId=" + deviceId);
            throw new InitSDKException("the deviceId cannot be empty or the deviceId's length > 64");
        }
        Log.i(TAG, "initStepOne: 初始化SpUtil和SeCoreUtils");
        SpUtil.init(application);
        SeCoreUtils.getInstance().SeCoreInit(application);
    }

    /**
     * 判断设备是否已经激活。
     */
    public boolean isActivate() {
        boolean isActivated = mEngine.isActivated();
        Log.i(TAG, "isActivate: 检查设备是否已激活，结果=" + isActivated);
        return isActivated;
    }

    public void clearAll() {
        Log.i(TAG, "clearAll: 清除所有数据");
        mEngine.clearAll();
    }

    public String getDeviceId() {
        String deviceId = mEngine.getDeviceId();
        Log.i(TAG, "getDeviceId: 获取设备ID=" + deviceId);
        return deviceId;
    }

    public void setLocation(String lng, String lat) {
        Log.i(TAG, "setLocation: 设置位置信息，经度=" + lng + "，纬度=" + lat);
        mEngine.setLocation(lng, lat);
    }

    /**
     * 释放资源，一般在App退出时调用
     */
    public void release() {
        Log.i(TAG, "release: 释放资源");
        mEngine.release();
    }

    public void initNetwork(Application application) {
        Log.i(TAG, "initNetwork: 初始化网络");
        ComponentKit.getInstance().inject(DaggerAdvertisingComponent.builder(), application, AdvertOptions.DEFAULT, new AdvertisingEngine());
        ComponentKit.getInstance().inject(new BaseRequest() {
            @Override
            protected <T> T obtainRetrofitService(Class<T> service) {
                return super.obtainRetrofitService(service);
            }
        });
    }
}
