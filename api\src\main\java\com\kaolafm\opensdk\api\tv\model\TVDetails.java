package com.kaolafm.opensdk.api.tv.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;
import com.kaolafm.opensdk.api.media.model.AudioFileInfo;

import java.util.List;

public class TVDetails implements Parcelable {

    /**
     * 电视id
     */
    @SerializedName("listenTVid")
    private long listenTVid;

    /**
     * 电视名称
     */
    @SerializedName("name")
    private String name;

    /**
     * 电视封面URL
     */
    @SerializedName("img")
    private String img;

    /**
     * 电视类型名称
     */
    @SerializedName("classifyName")
    private String classifyName;

    /**
     * 是否订阅,1=是，0=否
     */
    @SerializedName("isSubscribe")
    private int isSubscribe;

    /**
     * 直播流地址
     */
    @SerializedName("playUrl")
    private String playUrl;

    @SerializedName("playInfoList")
    private List<AudioFileInfo> playInfoList;

    /**
     * 在线收听数
     */
    @SerializedName("onLineNum")
    private int onLineNum;

    /**
     * 赞数
     */
    @SerializedName("likedNum")
    private int likedNum;

    /**
     * 上下线状态,1=上线0=下线
     */
    @SerializedName("status")
    private int status;

    /**
     * 电视类型id
     */
    @SerializedName("classifyId")
    private int classifyId;

    /**
     * 电视直播间id
     */
    @SerializedName("roomId")
    private int roomId;

    /**
     * 电视图标
     */
    @SerializedName("icon")
    private String icon;

    /**
     * 是否简版
     */
    @SerializedName("isLite")
    private int isLite;

    /**
     * 地区编码
     */
    @SerializedName("areaCode")
    private int areaCode;

    public long getListenTVid() {
        return listenTVid;
    }

    public void setListenTVid(long listenTVid) {
        this.listenTVid = listenTVid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getClassifyName() {
        return classifyName;
    }

    public void setClassifyName(String classifyName) {
        this.classifyName = classifyName;
    }

    public int getIsSubscribe() {
        return isSubscribe;
    }

    public void setIsSubscribe(int isSubscribe) {
        this.isSubscribe = isSubscribe;
    }

    public String getPlayUrl() {
        return playUrl;
    }

    public void setPlayUrl(String playUrl) {
        this.playUrl = playUrl;
    }

    public int getOnLineNum() {
        return onLineNum;
    }

    public void setOnLineNum(int onLineNum) {
        this.onLineNum = onLineNum;
    }

    public int getLikedNum() {
        return likedNum;
    }

    public void setLikedNum(int likedNum) {
        this.likedNum = likedNum;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getClassifyId() {
        return classifyId;
    }

    public void setClassifyId(int classifyId) {
        this.classifyId = classifyId;
    }

    public int getRoomId() {
        return roomId;
    }

    public void setRoomId(int roomId) {
        this.roomId = roomId;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public int getIsLite() {
        return isLite;
    }

    public void setIsLite(int isLite) {
        this.isLite = isLite;
    }

    public int getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(int areaCode) {
        this.areaCode = areaCode;
    }

    public List<AudioFileInfo> getPlayInfoList() {
        return playInfoList;
    }

    public void setPlayInfoList(List<AudioFileInfo> playInfoList) {
        this.playInfoList = playInfoList;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(this.listenTVid);
        dest.writeString(this.name);
        dest.writeString(this.img);
        dest.writeString(this.classifyName);
        dest.writeInt(this.isSubscribe);
        dest.writeString(this.playUrl);
        dest.writeInt(this.onLineNum);
        dest.writeInt(this.likedNum);
        dest.writeInt(this.status);
        dest.writeInt(this.classifyId);
        dest.writeInt(this.roomId);
        dest.writeString(this.icon);
        dest.writeInt(this.isLite);
        dest.writeInt(this.areaCode);
        dest.writeTypedList(this.playInfoList);
    }

    public TVDetails() {
    }

    protected TVDetails(Parcel in) {
        this.listenTVid = in.readLong();
        this.name = in.readString();
        this.img = in.readString();
        this.classifyName = in.readString();
        this.isSubscribe = in.readInt();
        this.playUrl = in.readString();
        this.onLineNum = in.readInt();
        this.likedNum = in.readInt();
        this.status = in.readInt();
        this.classifyId = in.readInt();
        this.roomId = in.readInt();
        this.icon = in.readString();
        this.isLite = in.readInt();
        this.areaCode = in.readInt();
        this.playInfoList = in.createTypedArrayList(AudioFileInfo.CREATOR);
    }

    public static final Creator<TVDetails> CREATOR = new Creator<TVDetails>() {
        @Override
        public TVDetails createFromParcel(Parcel source) {
            return new TVDetails(source);
        }

        @Override
        public TVDetails[] newArray(int size) {
            return new TVDetails[size];
        }
    };

    @Override
    public String toString() {
        return "TVDetails{" +
                "listenTVid=" + listenTVid +
                ", name='" + name + '\'' +
                ", img='" + img + '\'' +
                ", classifyName='" + classifyName + '\'' +
                ", isSubscribe=" + isSubscribe +
                ", playUrl='" + playUrl + '\'' +
                ", onLineNum=" + onLineNum +
                ", likedNum=" + likedNum +
                ", status=" + status +
                ", classifyId=" + classifyId +
                ", roomId=" + roomId +
                ", icon='" + icon + '\'' +
                ", isLite='" + isLite + '\'' +
                ", areaCode='" + areaCode + '\'' +
                '}';
    }
}