package com.kaolafm.opensdk.player.logic.playcontrol;

import android.util.Log;

import com.kaolafm.opensdk.api.media.AudioRequest;
import com.kaolafm.opensdk.api.media.model.AudioFileInfo;
import com.kaolafm.opensdk.api.media.model.AudioPlayInfo;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import java.util.List;

/**
 * 支付播放控制
 * <AUTHOR> shi qian
 */
public abstract class PaidPlayControl extends BasePlayControl {

    // 由于添加了付费的内容播放，专辑内容需要单独请求播放的url地址
    @Override
    void requestPlayUrl(PlayItem playItem, OnGetPlayUrlData callback) {
        new AudioRequest().getAudioPlayInfo(playItem.getPlayUrlId(), new HttpCallback<AudioPlayInfo>() {
            @Override
            public void onSuccess(AudioPlayInfo audioPlayInfo) {
                List<AudioFileInfo> playList = audioPlayInfo.getPlayInfoList();
                if (playList == null) {
                    onError(new ApiException(PlayerConstants.ERROR_CODE_PLAY_LIST_URL_NULL, "playList is null"));
                    return;
                }
                //设置并回调播放地址
                setPlayUrl(playItem, playList);
                callback.onDataGet(playItem.getPlayUrl());
            }

            @Override
            public void onError(ApiException e) {
                Log.i("BasePlayControl", "getPlayUrl error:" + e.toString());
                stop();
                if (e.getCode() == PlayerConstants.ERROR_CODE_NO_COPYRIGHT){
                    // code - 50816, message - 因版权原因，暂时无法播放
                    if(mBasePlayControlListener != null){
                        mBasePlayControlListener.onPlayerFailed(playItem, PlayerConstants.ERROR_CODE_NO_COPYRIGHT, e.getCode());
                    }
                    return;
                }
                if (e.getCode() == PlayerConstants.ERROR_CODE_PLAY_LIST_URL_NULL) {
                    if(mBasePlayControlListener != null){
                        mBasePlayControlListener.onPlayerFailed(playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_URL_NULL, 404);
                    }
                    return;
                }
                if(mBasePlayControlListener != null){
                    mBasePlayControlListener.onPlayerFailed(playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_URL_DECODE, e.getCode());
                }
            }
        });
    }

}
