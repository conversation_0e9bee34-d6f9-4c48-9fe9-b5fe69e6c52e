package com.kaolafm.opensdk.account.profile;

import android.app.Application;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.os.Bundle;
import android.text.TextUtils;

import com.kaolafm.base.internal.DeviceId;
import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.base.utils.DeviceUtil;
import com.kaolafm.base.utils.MD5;
import com.kaolafm.opensdk.di.scope.AppScope;

import javax.inject.Inject;

/**
 * QQ音乐的Profile管理类
 *
 * <AUTHOR>
 * @date 2018/8/10
 */
@AppScope
public class QQMusicProfileManger {

    /**
     * 在AndroidManifest中AppId的key
     */
    private static final String APP_ID_PROPERTY = "com.kaolafm.open.sdk.qqmusic.AppId";

    /**
     * 在AndroidManifest中AppKey的key
     */
    private static final String APP_KEY_PROPERTY = "com.kaolafm.open.sdk.qqmusic.AppKey";

    @Inject
    Application mApplication;

    @Inject
    QQMusicProfile mMusicProfile;

    @Inject
    public QQMusicProfileManger() {
    }

    public String getClientIp() {
        return DeviceUtil.getClientIP();
    }

    public String getDeviceId() {
        return DeviceId.getDeviceId(mApplication);
    }

    public String getTimestamp() {
        return String.valueOf(DateUtil.getServerTime() / 1000);
    }

    public void loadCurrentProfile() {
        if (!TextUtils.isEmpty(mMusicProfile.getAppId()) && !TextUtils.isEmpty(mMusicProfile.getAppKey())) {
            return;
        }
        try {
            ApplicationInfo applicationInfo = mApplication.getPackageManager()
                    .getApplicationInfo(mApplication.getPackageName(), PackageManager.GET_META_DATA);
            Bundle metaData = applicationInfo.metaData;
            String appId = String.valueOf(metaData.getInt(APP_ID_PROPERTY));
            String appKey = metaData.getString(APP_KEY_PROPERTY);
            mMusicProfile.setAppId(appId);
            mMusicProfile.setAppKey(appKey);
        } catch (NameNotFoundException | NullPointerException e) {
            e.printStackTrace();
        }
    }


    public void setAppId(String appId) {
        mMusicProfile.setAppId(appId);
    }

    public void setAppKey(String appKey) {
        mMusicProfile.setAppKey(appKey);
    }

    public String getAppId() {
        return mMusicProfile.getAppId();
    }

    public String getAppKey() {
        return mMusicProfile.getAppKey();
    }

    public String getSign() {
        String sb = "OpitrtqeGzopIlwxs" + "_" +
                getAppId() + "_" +
                getAppKey() + "_" +
                "pSDmWIWdzYidXGjJCV" + "_" +
                getTimestamp();
        return MD5.getMD5Str(sb);
    }
}
