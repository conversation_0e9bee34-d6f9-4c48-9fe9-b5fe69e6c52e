package com.kaolafm.report.util;

import android.util.Log;

import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.report.api.report.ReportShenCeRequest;

import io.reactivex.Single;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

/**
 * <AUTHOR> on 2019/1/14.
 */

class ReportNetworkHelper {
    private static final String TAG = "ReportNetworkHelper";
    private static ReportNetworkHelper reportNetworkHelper;
    //private Retrofit mRetrofit;
    //private ReportShenCeService mReportApiService;

    private ReportNetworkHelper() {
        //init();
    }

    public static ReportNetworkHelper getInstance() {
        if (reportNetworkHelper == null) {
            synchronized (ReportNetworkHelper.class) {
                if (reportNetworkHelper == null) {
                    reportNetworkHelper = new ReportNetworkHelper();
                }
            }
        }
        return reportNetworkHelper;
    }

//    private void init() {
//        mRetrofit = new Retrofit.Builder()
//                .baseUrl(ReportConstants.BASE_URL)
//                .addConverterFactory(GsonConverterFactory.create())
//                .build();
//        mReportApiService = mRetrofit.create(ReportShenCeService.class);
//    }

    public void request(String json, IRequestCallBack iRequestCallBack) {
        Logging.i(TAG, "request,json="+json);
        Single.fromCallable(() -> new ReportShenCeRequest().postReport(json)).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(aBoolean -> {
                    iRequestCallBack.result(aBoolean);
                }, throwable -> {
                    iRequestCallBack.result(false);
                });
    }

//    public void request(String json, IRequestCallBack iRequestCallBack) {
//        Single.fromCallable(() -> {
//            Log.d(ReportConstants.REPORT_TAG, "发送数据 = " + json);
//            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), json);
//            Call<String> call = mReportApiService.reportComment(requestBody);
//            Response<String> stringResponse = call.execute();
//            return stringResponse.isSuccessful();
//        }).subscribeOn(Schedulers.io())
//                .observeOn(AndroidSchedulers.mainThread())
//                .subscribe(aBoolean -> {
//                    Log.e(ReportConstants.REPORT_TAG, "执行网络请求 返回结果: " + aBoolean);
//                    iRequestCallBack.result(aBoolean);
//                }, throwable -> {
//                    Log.e(ReportConstants.REPORT_TAG, "执行网络请求 返回结果为 false");
//                    iRequestCallBack.result(false);
//                });
//    }

    public interface IRequestCallBack {
        void result(boolean isOk);
    }


}
