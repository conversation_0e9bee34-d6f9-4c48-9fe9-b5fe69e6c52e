package com.kaolafm.opensdk.account.token;

import android.app.Application;
import android.content.Context;
import android.content.SharedPreferences;

import com.google.gson.Gson;
import com.kaolafm.opensdk.di.scope.AppScope;

import javax.inject.Inject;

import dagger.Lazy;

/**
 * <AUTHOR>
 * @date 2018/8/13
 */
public class QQMusicAccessTokenCache implements TokenCache<QQMusicAccessToken> {

    private static final String SP_QQMUSIC_ACCESS_TOKEN_NAME = "sdk.QQMusicAccessToken.SharedPreferences";

    private static final String CACHED_QQMUSIC_ACCESS_TOKEN = "com.kaolafm.open.sdk.QQMusicAccessToken";

    @Inject
    @AppScope
    Lazy<Gson> mGsonLazy;

    @Inject
    QQMusicAccessToken mAccessToken;

    private final SharedPreferences mSharedPreferences;

    @Inject
    public QQMusicAccessTokenCache(Application application) {
        mSharedPreferences = application.getSharedPreferences(application.getPackageName() + SP_QQMUSIC_ACCESS_TOKEN_NAME, Context.MODE_PRIVATE);
    }

    @Override
    public boolean accept(AccessToken token) {
        return token instanceof QQMusicAccessToken;
    }

    @Override
    public QQMusicAccessToken getToken() {
        return mAccessToken;
    }

    @Override
    public void save(QQMusicAccessToken qqMusicAccessToken) {
        if (qqMusicAccessToken != null) {
            mAccessToken = qqMusicAccessToken;
            String accessTokenStr = mGsonLazy.get().toJson(qqMusicAccessToken);
            mSharedPreferences.edit().putString(CACHED_QQMUSIC_ACCESS_TOKEN, accessTokenStr).apply();
        } else {
            logout();
        }

    }

    /**
     * 加载缓存，要保证QQMusicAccessToken不为null
     * @return
     */
    @Override
    public QQMusicAccessToken load() {
        if (mSharedPreferences.contains(CACHED_QQMUSIC_ACCESS_TOKEN)) {
            String accessTokenStr = mSharedPreferences.getString(CACHED_QQMUSIC_ACCESS_TOKEN, "");
            QQMusicAccessToken accessToken = mGsonLazy.get().fromJson(accessTokenStr, QQMusicAccessToken.class);
            if (accessToken != null) {
                mAccessToken = accessToken;
            }
        }
        return mAccessToken;
    }

    @Override
    public void clear() {
        mAccessToken = null;
        mSharedPreferences.edit().remove(CACHED_QQMUSIC_ACCESS_TOKEN).apply();
    }

    @Override
    public void logout() {
        mAccessToken = null;
        mSharedPreferences.edit().remove(CACHED_QQMUSIC_ACCESS_TOKEN).apply();
    }
}
