package com.kaolafm.opensdk.api.personalise;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.personalise.internal.Status;
import com.kaolafm.opensdk.api.personalise.model.HotRecommend;
import com.kaolafm.opensdk.api.personalise.model.InterestTag;

import java.util.List;

import io.reactivex.Single;
import okhttp3.RequestBody;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * <AUTHOR>
 * @date 2019/4/28
 */
interface PersonalizedService {

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_INTEREST_TAG_LIST_UNLOGINED)
    Single<BaseResult<List<InterestTag>>> getInterestTagListUnlogined();

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_INTEREST_TAG_LIST_LOGINED)
    Single<BaseResult<List<InterestTag>>> getInterestTagListLogined();

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @POST(KaolaApiConstant.SAVE_USER_ATTRIBUTE)
    Single<BaseResult<List<InterestTag>>> saveUserAttribute(@Query("year") String year, @Query("gender") int gender);

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @POST(KaolaApiConstant.SAVE_INTEREST_TAG_STRONG)
    Single<BaseResult<Status>> saveInterestTag(@Query("tags") String interestTag);

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_PERSONAL_INTEREST_TAG_LIST)
    Single<BaseResult<List<String>>> getPersonalTags();

    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER, ApiHostConstants.HTTPS_PROTOCOL_DOMAIN_HEADER})
    @POST(KaolaApiConstant.SAVE_THIRD_USER)
    Single<BaseResult<Status>> saveThirdUser(@Body RequestBody body);

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @POST(KaolaApiConstant.SAVE_DEVICE_INFO)
    Single<BaseResult<Status>> saveDeviceInfo(@Body RequestBody body);

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_HOT_RECOMMEND)
    Single<BaseResult<HotRecommend>> getHostRecommend();
}
