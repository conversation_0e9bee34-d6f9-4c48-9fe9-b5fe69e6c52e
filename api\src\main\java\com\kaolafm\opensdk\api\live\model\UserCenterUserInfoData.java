package com.kaolafm.opensdk.api.live.model;

import com.kaolafm.base.utils.StringUtil;

public class UserCenterUserInfoData {

    public static final String BLANK_STR = "";

    //----------------------------------性别-----------------------------------------
    public static final String CODE_GENDER_MALE = "0";
    public static final String CODE_GENDER_FEMALE = "1";
    public static final String CODE_GENDER_OTHER = "2";
    public static final String CODE_GENDER_CONFIDENTIAL = "3";

    public static final String MSG_GENDER_MALE = "男";
    public static final String MSG_GENDER_FEMALE = "女";
    public static final String MSG_GENDER_OTHER = "其他";
    public static final String MSG_GENDER_CONFIDENTIAL = "未设置";//保密 FIXME 接口是保密，产品上是显示未设置
    //----------------------------------性别-----------------------------------------

    //----------------------------------冻结 禁止 OK状态-----------------------------------------
    public static final String CODE_STATUS_OK = "0";
    public static final String CODE_STATUS_FORBIDEN = "1";
    public static final String CODE_STATUS_FROZEN = "2";

    public static final String MSG_STATUS_OK = "正常";
    public static final String MSG_STATUS_FORBIDEN = "已禁止";
    public static final String MSG_STATUS_FROZEN = "已冻结";
    //----------------------------------状态-----------------------------------------

    //-----------------------------------relation 关系------------------------------------
    //0未关注 1单向关注 2互相关注
    public static final String CODE_RELATION_NO_FOLLOW = "0";
    public static final String CODE_RELATION_ONE_WAY_FOLLOW = "1";
    public static final String CODE_RELATION_DOUBLE_WAY_FOLLOW = "2";


    //----------------------------------第三方平台绑定状态-----------------------------------------
    public static final String CODE_BIND_YES = "1";
    public static final String CODE_BIND_NO = "0";

    public static final String MSG_BIND_YES = "已绑定";
    public static final String MSG_BIND_NO = "立即绑定";

    //----------------------------------第三方平台绑定状态-----------------------------------------

    //----------------------------------用户类型--------------------------------
    public static final int USER_TYPE_PERSONAGE = 1;  //个人
    public static final int USER_TYPE_COMPANY = 2;    //企业

    public static final int USER_NEED_BIND_MOBILE = 1;//需要绑定手机
    public static final int USER_UNNEED_BIND_MOBILE = 2;//不需要绑定手机

    /**
     * +V用户
     */
    public static final int USER_V_ANCHOR = 1;

    /**
     * 用户经过认证 可以打赏
     */
    public static final int USER_CAN_REWARD = 1;


    //    uid	String	 	uid
    //    avatar	String	 	头像
    //    status	int	0:正常 1:已禁用	状态
    //    nickName	String	 	昵称
    //    email	String
    //    gender	int	 0 男 1 女 2 其他 3 保密	性别
    //    birthday	String	2015-05-05	生日
    //    address	String	例：北京市 朝阳区	地址
    //    intro	String	 	简介
    //    isAnchor	Integer	1:是 0:否	是否主播
    //    mobile	int	1:是 0:否	是否绑定手机号
    //    mobileNumber	String	手机号码
    //    sinaweibo	int	1:是 0:否	是否绑定新浪微博
    //    sinaweiboId	String	微博userID	微博userID
    //    qq	int	1:是 0:否	是否绑定QQ
    //    qqId	String	QQ UserID	QQ UserID
    //    weixin	int	1:是 0:否	是否绑定微信
    //    weixinId	String	微信UserID	微信UserID
    //    relation	Integer
    //    followedNum	Long	10009884	关注数
    //    fansNum	Long	190782	粉丝数

    public boolean isAvatarUpdated() {
        return isAvatarUpdated;
    }

    public void setIsAvatarUpdated(boolean isAvatarUpdated) {
        this.isAvatarUpdated = isAvatarUpdated;
    }

    /**
     * 用户头像是否已更新（老版本没有头像的用户绑定了第三方平台后这个值变为true 用户资料页面需要更新用户头像）
     */
    private boolean isAvatarUpdated;

    /**
     * 用户类型 （第三方登录，手机登录）
     */
    private String type;
    /**
     * 用户标识 由服务器返回
     */
    private String uid;

    /**
     * 服务器返回token
     */
    private String token;

    /**
     * 用户当前状态 "status": 0, // 0 正常 1 禁用 2 冻结
     */
    private String status;

    /**
     * 用户名： 手机号码 或者 第三方id
     */
    private String userName;

    /**
     * 用户密码 （用于修改用户密码）
     */
    private String password;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户头像地址
     */
    private String avatar;

    /**
     * 用户所在区域（国家 城市）
     */
    private String address;

    /**
     * 省市代码 请求服务器更新时传这个值
     */
    private String addressCode;

    /**
     * 用户生日
     */
    private String birthday;

    /**
     * 用户性别  0 男 1 女 2 其他 3 保密
     */
    private String gender;

    /**
     * 用户自我说明（介绍）
     */
    private String intro;

    /**
     * 邮箱是否绑定
     */
    private String bindEmail;

    /**
     * 邮箱号码
     */
    private String emailAccount;

    /**
     * 是否是主播
     */
    private String isAnchor;

    /**
     * 是否V主播
     */
    private int isVanchor;

    /**
     * 是否被屏蔽（这个关系是此用户对于别的用户）
     */
    private int isShield;

    /**
     * 关注状态
     */
    private String relation;

    /**
     * 关注数
     */
    private String followedNum;

    /**
     * 粉丝数
     */
    private String fansNum;

    /**
     * 用户类型 1(普通用户), 2(企业)
     */
    private int userType;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 是否是官方账号0否，1是
     */
    private int isOfficialUser;

    /**
     *
     */
    private int needBindMobile;

    public int getNeedBindMobile() {
        return needBindMobile;
    }

    public void setNeedBindMobile(int needBindMobile) {
        this.needBindMobile = needBindMobile;
    }

    public int getIsOfficialUser() {
        return isOfficialUser;
    }

    public void setIsOfficialUser(int isOfficialUser) {
        this.isOfficialUser = isOfficialUser;
    }

    public String getLikeNum() {
        return likeNum;
    }

    public void setLikeNum(String likeNum) {
        this.likeNum = likeNum;
    }

    /**
     * 累积的赞 数 用户自己没有 其他用户有这个字段
     */
    private String likeNum;

    /**
     * 0表示未绑定手机， 1表示已绑定手机
     * 如果已经绑定手机则值为手机号码，否则为空串
     */
    private String mobile;
    private String mobileNumber;

    /**
     * 0表示未绑定新浪微博， 1表示已绑定新浪微博
     * 如果已经绑定新浪微博则值为otherid，否则为空串
     */
    private String sinaweibo;
    private String sinaweiboId;
    private String personalPageUrl;//个人主页

    /**
     * 0表示未绑定QQ账号，1表示已绑定QQ账号
     * 如果已绑定QQ账号则值为qq的otherid，否则为空串
     */
    private String qq;
    private String qqId;

    /**
     * 0表示未绑定微信账号，1表示已绑定微信账号
     */
    private String weixin;
    private String weixinId;

    /**
     * 用户积分
     */
    private String score;


    public String getType() {
        return type;
    }

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score;
    }

    public void setType(String userType) {
        this.type = userType;
    }

    public String getUid() {
        if (StringUtil.isEmpty(uid)) {
            uid = BLANK_STR;
        }
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getSinaweibo() {
        return sinaweibo;
    }

    public void setSinaweibo(String sinaweibo) {
        this.sinaweibo = sinaweibo;
    }

    public String getSinaweiboId() {
        return sinaweiboId;
    }

    public void setSinaweiboId(String sinaweiboId) {
        this.sinaweiboId = sinaweiboId;
    }

    public String getQq() {
        return qq;
    }

    public void setQq(String qq) {
        this.qq = qq;
    }

    public String getQqId() {
        return qqId;
    }

    public void setQqId(String qqId) {
        this.qqId = qqId;
    }

    public String getWeixin() {
        return weixin;
    }

    public void setWeixin(String weixin) {
        this.weixin = weixin;
    }

    public String getWeixinId() {
        return weixinId;
    }

    public void setWeixinId(String weixinId) {
        this.weixinId = weixinId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAddressCode() {
        return addressCode;
    }

    public void setAddressCode(String addressCode) {
        this.addressCode = addressCode;
    }


    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getIntro() {
        return intro;
    }

    public void setIntro(String intro) {
        this.intro = intro;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getBindEmail() {
        return bindEmail;
    }

    public void setBindEmail(String bindEmail) {
        this.bindEmail = bindEmail;
    }

    public String getEmailAccount() {
        return emailAccount;
    }

    public void setEmailAccount(String emailAccount) {
        this.emailAccount = emailAccount;
    }

    public String getIsAnchor() {
        return isAnchor;
    }

    public void setIsAnchor(String isAnchor) {
        this.isAnchor = isAnchor;
    }

    public String getRelation() {
        return relation;
    }

    public void setRelation(String relation) {
        this.relation = relation;
    }

    public String getFollowedNum() {
        return followedNum;
    }

    public void setFollowedNum(String followedNum) {
        this.followedNum = followedNum;
    }

    public String getFansNum() {
        return fansNum;
    }

    public void setFansNum(String fansNum) {
        this.fansNum = fansNum;
    }

    public String getPersonalPageUrl() {
        return personalPageUrl;
    }

    public void setPersonalPageUrl(String personalPageUrl) {
        this.personalPageUrl = personalPageUrl;
    }


    /**
     * 清空用户对象数据
     */
    public void clearUserInfo() {

        type = null;
        uid = null;
        token = null;
        status = null;
        userName = null;
        password = null;
        nickName = null;
        avatar = null;
        address = null;
        birthday = null;
        gender = null;
        intro = null;
        bindEmail = null;
        emailAccount = null;
        isAnchor = null;
        relation = null;
        followedNum = null;
        fansNum = null;
        mobile = null;
        mobileNumber = null;
        sinaweibo = null;
        sinaweiboId = null;
        qq = null;
        qqId = null;
        weixin = null;
        weixinId = null;
        userType = 0;
        companyName = null;
        needBindMobile = 0;
    }

    public int getIsVanchor() {
        return isVanchor;
    }

    public void setIsVanchor(int isVanchor) {
        this.isVanchor = isVanchor;
    }

    public void setIsVanchor(boolean isVanchor) {
        this.isVanchor = isVanchor ? USER_V_ANCHOR : 0;
    }

    public int getIsShield() {
        return isShield;
    }

    public void setIsShield(int isShield) {
        this.isShield = isShield;
    }

    /**
     * 是否 V 用户
     *
     * @return
     */
    public int isVanchor() {
        return isVanchor;
    }

    /**
     * 用户是不是VIP
     *
     * @return
     */
    public boolean isUserVanchor() {

        return isVanchor == USER_V_ANCHOR;
    }

    /**
     * 进入直播直播间 点击用户头像 获取其他用户信息时 其他用户和登录用户之间的关系
     *
     * @return
     */
    public boolean isFollowed() {

        return !CODE_RELATION_NO_FOLLOW.equals(relation);
    }


    /**
     * 根据服务器返回性别码获取中文的用户真实性别
     *
     * @return string 字符串
     */
    public String getGenderInChinese() {

        if (gender == null) {
            return MSG_GENDER_CONFIDENTIAL;
        }

        if (gender.equals(CODE_GENDER_MALE)) {

            return MSG_GENDER_MALE;
        } else if (gender.equals(CODE_GENDER_FEMALE)) {

            return MSG_GENDER_FEMALE;
        } else if (gender.equals(CODE_GENDER_OTHER)) {

            return MSG_GENDER_OTHER;
        } else if (gender.equals(CODE_GENDER_CONFIDENTIAL)) {

            return MSG_GENDER_CONFIDENTIAL;
        }

        return MSG_GENDER_CONFIDENTIAL;
    }

    /**
     * 是男
     *
     * @return
     */
    public boolean isUserMale() {

        return CODE_GENDER_MALE.equals(gender);
    }

    /**
     * 是女
     *
     * @return
     */
    public boolean isUserFemale() {

        return CODE_GENDER_FEMALE.equals(gender);
    }

    /**
     * 根据服务器返回状态码获取中文的用户状态
     *
     * @param status 状态码
     * @return string 字符串
     */
    public static String getStatusInChinese(String status) {

        if (status == null) {
            return null;
        }

        if (status.equals(CODE_STATUS_OK)) {

            return MSG_STATUS_OK;
        } else if (status.equals(CODE_STATUS_FORBIDEN)) {

            return MSG_STATUS_FORBIDEN;
        } else if (status.equals(CODE_STATUS_FROZEN)) {

            return MSG_STATUS_FROZEN;
        }

        return null;
    }

    /**
     * 根据服务器返回性别码获取中文的用户绑定状态
     *
     * @param bindStatus 绑定状态
     * @return string 字符串
     */
    public static String getBindStatusInChinese(String bindStatus) {

        switch (bindStatus) {
            case CODE_BIND_YES:

                return MSG_BIND_YES;
            case CODE_BIND_NO:

                return MSG_BIND_NO;
        }
        return null;
    }


    /**
     * email是否绑定
     *
     * @return
     */
    public boolean isEmailBound() {

        if (bindEmail == null || bindEmail.length() == 0) {

            return false;
        }
        return bindEmail.equals(CODE_BIND_YES);
    }


    /**
     * 手机号是否绑定
     *
     * @return
     */
    public boolean isMobileBound() {

        if (mobile == null || mobile.length() == 0) {

            return false;
        }
        return mobile.equals(CODE_BIND_YES);
    }


    /**
     * 微信是否绑定
     *
     * @return
     */
    public boolean isWeChatBound() {

        if (weixin == null || weixin.length() == 0) {

            return false;
        }
        return weixin.equals(CODE_BIND_YES);
    }

    /**
     * QQ是否绑定
     *
     * @return
     */
    public boolean isQQBound() {

        if (qq == null || qq.length() == 0) {

            return false;
        }
        return qq.equals(CODE_BIND_YES);
    }


    /**
     * 微薄是否绑定
     *
     * @return
     */
    public boolean isWeiboBound() {

        if (sinaweibo == null || sinaweibo.length() == 0) {

            return false;
        }
        return sinaweibo.equals(CODE_BIND_YES);
    }


    @Override
    public String toString() {
        return "UserCenterUserInfoData{" +
                "address='" + address + '\'' +
                ", isAvatarUpdated=" + isAvatarUpdated +
                ", type='" + type + '\'' +
                ", uid='" + uid + '\'' +
                ", token='" + token + '\'' +
                ", status='" + status + '\'' +
                ", userName='" + userName + '\'' +
                ", password='" + password + '\'' +
                ", nickName='" + nickName + '\'' +
                ", avatar='" + avatar + '\'' +
                ", addressCode='" + addressCode + '\'' +
                ", birthday='" + birthday + '\'' +
                ", gender='" + gender + '\'' +
                ", intro='" + intro + '\'' +
                ", bindEmail='" + bindEmail + '\'' +
                ", emailAccount='" + emailAccount + '\'' +
                ", isAnchor='" + isAnchor + '\'' +
                ", isVanchor=" + isVanchor +
                ", relation='" + relation + '\'' +
                ", followedNum='" + followedNum + '\'' +
                ", fansNum='" + fansNum + '\'' +
                ", likeNum='" + likeNum + '\'' +
                ", mobile='" + mobile + '\'' +
                ", mobileNumber='" + mobileNumber + '\'' +
                ", sinaweibo='" + sinaweibo + '\'' +
                ", sinaweiboId='" + sinaweiboId + '\'' +
                ", personalPageUrl='" + personalPageUrl + '\'' +
                ", qq='" + qq + '\'' +
                ", qqId='" + qqId + '\'' +
                ", weixin='" + weixin + '\'' +
                ", weixinId='" + weixinId + '\'' +
                ", companyName='" + companyName + '\'' +
                ", userType=" + userType + '\'' +
                ", needBindMobile=" + needBindMobile +
                '}';
    }


    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (!(o instanceof UserCenterUserInfoData))
            return false;

        UserCenterUserInfoData that = (UserCenterUserInfoData) o;

        if (isAvatarUpdated != that.isAvatarUpdated)
            return false;
        if (isVanchor != that.isVanchor)
            return false;
        if (isShield != that.isShield)
            return false;
        if (isOfficialUser != that.isOfficialUser)
            return false;
        /*if (type != null ? !type.equals(that.type) : that.type != null) return false;*/
        if (uid != null ? !uid.equals(that.uid) : that.uid != null)
            return false;
        if (token != null ? !token.equals(that.token) : that.token != null)
            return false;
        if (status != null ? !status.equals(that.status) : that.status != null)
            return false;
        if (userName != null ? !userName.equals(that.userName) : that.userName != null)
            return false;
        if (password != null ? !password.equals(that.password) : that.password != null)
            return false;
        if (nickName != null ? !nickName.equals(that.nickName) : that.nickName != null)
            return false;
        if (avatar != null ? !avatar.equals(that.avatar) : that.avatar != null)
            return false;
        if (address != null ? !address.equals(that.address) : that.address != null)
            return false;
        if (addressCode != null ? !addressCode.equals(that.addressCode) : that.addressCode != null)
            return false;
        if (birthday != null ? !birthday.equals(that.birthday) : that.birthday != null)
            return false;
        if (gender != null ? !gender.equals(that.gender) : that.gender != null)
            return false;
        if (intro != null ? !intro.equals(that.intro) : that.intro != null)
            return false;
        if (bindEmail != null ? !bindEmail.equals(that.bindEmail) : that.bindEmail != null)
            return false;
        if (emailAccount != null ? !emailAccount.equals(that.emailAccount) : that.emailAccount != null)
            return false;
        if (isAnchor != null ? !isAnchor.equals(that.isAnchor) : that.isAnchor != null)
            return false;
        if (relation != null ? !relation.equals(that.relation) : that.relation != null)
            return false;
        if (followedNum != null ? !followedNum.equals(that.followedNum) : that.followedNum != null)
            return false;
        if (fansNum != null ? !fansNum.equals(that.fansNum) : that.fansNum != null)
            return false;
        if (likeNum != null ? !likeNum.equals(that.likeNum) : that.likeNum != null)
            return false;
        if (mobile != null ? !mobile.equals(that.mobile) : that.mobile != null)
            return false;
        if (mobileNumber != null ? !mobileNumber.equals(that.mobileNumber) : that.mobileNumber != null)
            return false;
        if (sinaweibo != null ? !sinaweibo.equals(that.sinaweibo) : that.sinaweibo != null)
            return false;
        if (sinaweiboId != null ? !sinaweiboId.equals(that.sinaweiboId) : that.sinaweiboId != null)
            return false;
        if (personalPageUrl != null ? !personalPageUrl.equals(that.personalPageUrl) : that.personalPageUrl != null)
            return false;
        if (qq != null ? !qq.equals(that.qq) : that.qq != null)
            return false;
        if (qqId != null ? !qqId.equals(that.qqId) : that.qqId != null)
            return false;
        if (weixin != null ? !weixin.equals(that.weixin) : that.weixin != null)
            return false;
        if (weixinId != null ? !weixinId.equals(that.weixinId) : that.weixinId != null)
            return false;
        if (companyName != null ? !companyName.equals(that.companyName) : that.companyName != null)
            return false;
        if (userType != that.userType)
            return false;
        if (needBindMobile != that.needBindMobile)
            return false;

        return score != null ? score.equals(that.score) : that.score == null;

    }

    @Override
    public int hashCode() {
        int id = -1;
        try {
            id = Integer.valueOf(uid);
        } catch (Exception e) {

        }

        return id;
    }

    public int getUserType() {
        return userType;
    }

    public void setUserType(int userType) {
        this.userType = userType;
    }

    public String getCompanyName() {
        if (companyName == null) {
            companyName = BLANK_STR;
        }
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
}
