package com.kaolafm.opensdk.api.search;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.search.model.SearchProgramBean;

import java.util.List;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Query;

public interface AppSearchService {

    @Headers(ApiHostConstants.SEARCH_DOMAIN_HEADER)
    @GET("/resource/searchall")
    Single<BaseResult<List<SearchProgramBean>>> searchAllById(@Query("q") String keyword, @Query("classifyid") String classifyid, @Query("pagenum") int pagenum, @Query("pagesize") int pagesize);

    @Headers(ApiHostConstants.SEARCH_DOMAIN_HEADER)
    @GET("/resource/searchclassifyall")
    Single<BaseResult<List<SearchClassify>>> searchClassifyAll();
}
