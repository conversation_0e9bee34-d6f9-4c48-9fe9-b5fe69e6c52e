package com.kaolafm.opensdk.api.music.qq;

import androidx.annotation.IntDef;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * QQ音乐登录类型注解
 * <AUTHOR>
 * @date 2018/8/9
 */
@Target({ElementType.PARAMETER, ElementType.FIELD, ElementType.METHOD})
@Retention(RetentionPolicy.SOURCE)
@IntDef({QQMusicConstant.LOGIN_TYPE_TICKET, QQMusicConstant.LOGIN_TYPE_QQ,
        QQMusicConstant.LOGIN_TYPE_WECHAT, QQMusicConstant.LOGIN_TYPE_DEVICE,
        QQMusicConstant.LOGIN_TYPE_QQMUSIC})
public @interface LoginType {

}
