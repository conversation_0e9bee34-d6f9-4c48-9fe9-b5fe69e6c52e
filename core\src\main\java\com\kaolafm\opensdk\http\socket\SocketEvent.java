package com.kaolafm.opensdk.http.socket;

/**
 * <AUTHOR>
 * @date 2020-01-07
 */
public class SocketEvent {
    /**
     * Called on a successful connection.
     */
    public static final String EVENT_OPEN = "open";
    /**
     * Called on a disconnection.
     */
    public static final String EVENT_CLOSE = "close";

    public static final String EVENT_PACKET = "packet";

    public static final String EVENT_ERROR = "error";

    public static final String EVENT_CONNECT = "start";

    public static final String EVENT_CONNECTING = "connecting";
    /**
     * Called on a connection error.
     */
    public static final String EVENT_CONNECT_ERROR = "connect_error";

    /**
     * Called on a connection timeout.
     */
    public static final String EVENT_CONNECT_TIMEOUT = "connect_timeout";

    /**
     * Called on a successful reconnection.
     */
    public static final String EVENT_RECONNECT = "reconnect";

    /**
     * Called on a reconnection attempt error.
     */
    public static final String EVENT_RECONNECT_ERROR = "reconnect_error";

    public static final String EVENT_RECONNECT_FAILED = "reconnect_failed";

    public static final String EVENT_RECONNECT_ATTEMPT = "reconnect_attempt";

    public static final String EVENT_RECONNECTING = "reconnecting";

    public static final String EVENT_PING = "ping";

    public static final String EVENT_PONG = "pong";

    public static final String EVENT_DATA = "data";

    public static final String EVENT_MESSAGE = "message";

    /**
     * Called when a new transport is created. (experimental)
     */
    public static final String EVENT_TRANSPORT = "transport";

    public static final String EVENT_DRAIN = "drain";

    public static final String EVENT_DISCONNECT = "disconnect";



}
