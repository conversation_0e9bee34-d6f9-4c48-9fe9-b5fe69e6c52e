package com.kaolafm.opensdk.demo.live.ui;

import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.View;
import android.view.animation.LinearInterpolator;


import com.kaolafm.opensdk.demo.R;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 播放跳动动画
 *
 * <AUTHOR>
 * @date 2018/1/18
 */
public class PlayingIndicator extends View {

    private boolean canRun = true;

    /**
     * true表示从中间向两边缩放，false表示从下往上缩放。
     */
    private boolean isTwoWay;

    /**
     * 指针最低高度
     */
    private int minHeight;

    private Paint paint;

    private int stepNum;

    private int duration;

    private int barNum;

    private int barColor = 0xff000000;

    private int viewHeight;

    private float viewWidth;

    private List<ValueAnimator> animList;

    private ValueAnimator mAnim;

    public float mSpaceWidth;

    public float mBarWidth;

    private double mSumWidth;

    public PlayingIndicator(Context context) {
        this(context, null);
    }

    public PlayingIndicator(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public PlayingIndicator(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, @Nullable AttributeSet attrs) {
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.PlayingIndicator, 0, 0);
        try {
            barNum = ta.getInt(R.styleable.PlayingIndicator_bar_num, 4);
            //每一次增加的高度
            stepNum = ta.getInt(R.styleable.PlayingIndicator_step_num, 10);
            duration = ta.getInt(R.styleable.PlayingIndicator_duration, 3000);
            barColor = ta.getColor(R.styleable.PlayingIndicator_bar_color, 0xff000000);
            isTwoWay = ta.getBoolean(R.styleable.PlayingIndicator_is_two_way, false);
            minHeight = ta.getDimensionPixelSize(R.styleable.PlayingIndicator_min_height, 0);
        } finally {
            ta.recycle();
        }
        mSpaceWidth = 4;
        mBarWidth = 4;
        paint = new Paint();
        paint.setColor(barColor);
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        List<Float> floatList = getGraduateFloatList(stepNum, viewHeight);
        generateAnim(floatList, barNum);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        drawIndicator(canvas, barNum);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        viewWidth = MeasureSpec.getSize(widthMeasureSpec);
        viewHeight = MeasureSpec.getSize(heightMeasureSpec);
        mSpaceWidth = mBarWidth = viewWidth / ((2 * barNum));
        mSumWidth = mSpaceWidth + mBarWidth;
        paint.setStrokeWidth(mBarWidth);
        this.setMeasuredDimension((int) viewWidth, viewHeight);
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    private List<Float> getGraduateFloatList(int arraySize, int max) {
        List<Float> floatList = new ArrayList<>();
        double dividedMax = (max - minHeight) / arraySize;
        for (int i = 1; i <= arraySize; i++) {
            floatList.add((float) (i * dividedMax) + minHeight);
        }
        floatList.set(floatList.size() - 1, floatList.get(0));
        return floatList;
    }

    private void generateAnim(List<Float> floatList, int barNum) {
        animList = new ArrayList<>();
        for (int i = 0; i < barNum; i++) {
            Collections.shuffle(floatList);
            floatList.set(floatList.size() - 1, floatList.get(0));

            float[] floatArray = new float[floatList.size()];
            int j = 0;
            for (Float f : floatList) {
                floatArray[j++] = (f != null ? f : Float.NaN);
            }

            mAnim = ValueAnimator.ofFloat(floatArray);
            mAnim.setDuration(duration);
            mAnim.setRepeatCount(ValueAnimator.INFINITE);
            mAnim.setInterpolator(new LinearInterpolator());
            mAnim.start();

            animList.add(mAnim);
        }
    }

    private void drawIndicator(Canvas canvas, int barNum) {

        for (int i = 0; i < barNum; i++) {
            float nextHeight = (float) (animList.get(i).getAnimatedValue());
            float startX = (float) (i * mSumWidth + mSpaceWidth / 2);
            float startY, stopY;
            if (isTwoWay) {
                startY = (canvas.getHeight() - nextHeight) / 2;
                stopY = (canvas.getHeight() + nextHeight) / 2;
            } else {
                startY = canvas.getHeight();
                stopY = canvas.getHeight() - nextHeight;
            }
            canvas.drawLine(startX, startY, startX, stopY, paint);
        }
        if (canRun) {
            invalidate();
        }
    }

    public void setStepNum(int stepNum) {
        this.stepNum = stepNum;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public void setBarNum(int barNum) {
        this.barNum = barNum;
    }

    public void setBarColor(int barColor) {
        this.barColor = barColor;
    }

    public void start() {
        canRun = true;
        invalidate();
    }

    public void stop() {
        canRun = false;
    }
}
