package com.kaolafm.opensdk.api.music.qq.model;

import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * 排行榜榜单
 * <AUTHOR>
 * @date 2018/4/23
 */

public class SongCharts{


    public static class GroupTopListBean {

        public static class SongBean {

            /**
             * singer_name : <PERSON>-<PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON>
             * song_name : Good Life
             */

            @SerializedName("singer_name")
            private String singerName;

            @SerializedName("song_name")
            private String songName;

            public String getSingerName() {
                return singerName;
            }

            public String getSongName() {
                return songName;
            }

            public void setSingerName(String singerName) {
                this.singerName = singerName;
            }

            public void setSongName(String songName) {
                this.songName = songName;
            }
        }

        /**
         * listen_num : 9300000
         * show_time : 2017-04-18
         * song_list : [{"singer_name":"G-<PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON>","song_name":"Good Life"},{"singer_name":"张杰","song_name":"星辰"},{"singer_name":"Lil Uzi Vert/Quavo/Travis <PERSON>","song_name":"Go Off"},{"singer_name":"林忆莲","song_name":"我不能忘记你"}]
         * top_banner_pic : http://y.gtimg.cn/music/common/upload/iphone_order_channel/20150820174934.jpg
         * top_header_pic : http://y.gtimg.cn/music/common/upload/iphone_order_channel/toplist_27_300_200981920.jpg
         * top_id : 27
         * top_name : 巅峰榜·新歌
         * top_type : 0
         */

        @SerializedName("listen_num")
        private int listenNum;

        @SerializedName("show_time")
        private String showTime;

        @SerializedName("song_list")
        private List<SongBean> songList;

        @SerializedName("top_banner_pic")
        private String topBannerPic;

        @SerializedName("top_header_pic")
        private String topHeaderPic;

        @SerializedName("top_id")
        private int topId;

        @SerializedName("top_name")
        private String topName;

        @SerializedName("top_type")
        private int topType;

        public int getListenNum() {
            return listenNum;
        }

        public String getShowTime() {
            return showTime;
        }

        public List<SongBean> getSongList() {
            return songList;
        }

        public String getTopBannerPic() {
            return topBannerPic;
        }

        public String getTopHeaderPic() {
            return topHeaderPic;
        }

        public int getTopId() {
            return topId;
        }

        public String getTopName() {
            return topName;
        }

        public int getTopType() {
            return topType;
        }

        public void setListenNum(int listenNum) {
            this.listenNum = listenNum;
        }

        public void setShowTime(String showTime) {
            this.showTime = showTime;
        }

        public void setSongList(List<SongBean> songList) {
            this.songList = songList;
        }

        public void setTopBannerPic(String topBannerPic) {
            this.topBannerPic = topBannerPic;
        }

        public void setTopHeaderPic(String topHeaderPic) {
            this.topHeaderPic = topHeaderPic;
        }

        public void setTopId(int topId) {
            this.topId = topId;
        }

        public void setTopName(String topName) {
            this.topName = topName;
        }

        public void setTopType(int topType) {
            this.topType = topType;
        }
    }

    /**
     * group_id : 0
     * group_name : QQ音乐巅峰榜
     * group_top_list : [{"listen_num":9300000,"show_time":"2017-04-18","song_list":[{"singer_name":"G-Eazy/Kehlani","song_name":"Good Life"},{"singer_name":"张杰","song_name":"星辰"},{"singer_name":"Lil Uzi Vert/Quavo/Travis Scott","song_name":"Go Off"},{"singer_name":"林忆莲","song_name":"我不能忘记你"}],"top_banner_pic":"http://y.gtimg.cn/music/common/upload/iphone_order_channel/20150820174934.jpg","top_header_pic":"http://y.gtimg.cn/music/common/upload/iphone_order_channel/toplist_27_300_200981920.jpg","top_id":27,"top_name":"巅峰榜·新歌","top_type":0},{"listen_num":9700000,"show_time":"2017-04-13","song_list":[{"singer_name":"李雨寿","song_name":"漂洋过海来看你"},{"singer_name":"王冕","song_name":"勉为其难"},{"singer_name":"庄心妍","song_name":"从心出发"},{"singer_name":"凤凰传奇","song_name":"瞄着你就爱"}],"top_banner_pic":"http://y.gtimg.cn/music/common/upload/t_order_channel_hitlist_conf/47907.jpg","top_header_pic":"http://y.gtimg.cn/music/common/upload/iphone_order_channel/toplist_28_300_201645286.jpg","top_id":28,"top_name":"巅峰榜·网络歌曲","top_type":0}]
     * group_type : 1
     */

    @SerializedName("group_id")
    private int groupId;

    @SerializedName("group_name")
    private String groupName;

    @SerializedName("group_top_list")
    private List<GroupTopListBean> groupTopList;

    @SerializedName("group_type")
    private int groupType;

    public int getGroupId() {
        return groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public List<GroupTopListBean> getGroupTopList() {
        return groupTopList;
    }

    public int getGroupType() {
        return groupType;
    }

    public void setGroupId(int groupId) {
        this.groupId = groupId;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public void setGroupTopList(List<GroupTopListBean> groupTopList) {
        this.groupTopList = groupTopList;
    }

    public void setGroupType(int groupType) {
        this.groupType = groupType;
    }
}
