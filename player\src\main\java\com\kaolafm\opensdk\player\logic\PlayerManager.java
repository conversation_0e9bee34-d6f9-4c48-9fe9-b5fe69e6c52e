package com.kaolafm.opensdk.player.logic;

import android.content.Context;
import android.util.Log;

import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.NetworkUtil;
import com.kaolafm.opensdk.api.broadcast.BroadcastRequest;
import com.kaolafm.opensdk.api.broadcast.ProgramDetails;
import com.kaolafm.opensdk.api.media.AudioRequest;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.core.ijk.VideoView;
import com.kaolafm.opensdk.player.core.listener.IPlayerAsncStartExecutingListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter;
import com.kaolafm.opensdk.player.core.model.AudioFadeConfig;
import com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager;
import com.kaolafm.opensdk.player.logic.factory.PlayListControlFactory;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.listener.ICheckCopyrightListener;
import com.kaolafm.opensdk.player.logic.listener.IGeneralListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayListControl;
import com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayerInteractionFiredListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateVideoListener;
import com.kaolafm.opensdk.player.logic.model.CustomPlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.PlaylistInfo;
import com.kaolafm.opensdk.player.logic.model.TimeDiscontinuousBroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.VideoPlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.InvalidPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.TempTaskPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playcontrol.PlayControl;
import com.kaolafm.opensdk.player.logic.playlist.AlbumPlayListControl;
import com.kaolafm.opensdk.player.logic.playlist.BroadcastPlayListControl;
import com.kaolafm.opensdk.player.logic.playlist.TVPlayListControl;
import com.kaolafm.opensdk.player.logic.playlist.util.PlayListUtils;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerListenerHelper;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.opensdk.player.logic.util.PlayerPreconditions;
import com.kaolafm.opensdk.player.logic.util.SDKReportManager;
import com.kaolafm.opensdk.player.logic.util.ToneQualityHelper;
import com.kaolafm.report.util.ReportConstants;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> on 2019/3/18.
 */

public class PlayerManager {
    private static final String TAG = "PlayerManager";
    private boolean playIsNow = false;
    private IPlayListControl mIPlayListControl;
    private PlayControl mIPlayControl;
    /**
     * 当前播放的类型
     */
    private int mCustomType = PlayerConstants.RESOURCES_TYPE_INVALID;
    /**
     * 当前播放的对象
     */
    private PlayerBuilder mBuilder;
    private WeakReference<Context> mContext;
    private boolean mAutoPlay = true; // 是否自动连播
    private boolean mIsLimit = false; // 是否超过安全车速

    public  boolean isPlayIsNow() {
        return playIsNow;
    }

    public  void setPlayIsNow(boolean playIsNow) {
        PlayerManager.getInstance().playIsNow = playIsNow;
    }

    public Context getContext() {
        if (mContext == null) {
            PlayerLogUtil.log(TAG, "mContext is null");
            mPlayerListenerHelper.notifyPlayerInitError(PlayerConstants.ERROR_CODE_PLAYER_NOT_INIT);
            return null;
        }
        return mContext.get();
    }

    /**
     * 自定义播单管理控制
     */
    private HashMap<Integer, Class<? extends IPlayListControl>> mCustomIPlayListControlHashMap;

    /**
     * 播放器监听管理
     */
    private final PlayerListenerHelper mPlayerListenerHelper;

    private PlayerManager() {
        mPlayerListenerHelper = new PlayerListenerHelper();
        mBuilder = new PlayerBuilder();
        mCustomIPlayListControlHashMap = new HashMap<>();
    }

    private static class PlayManagerInstance {
        private static final PlayerManager PLAYERMANAGER = new PlayerManager();
    }

    public static PlayerManager getInstance() {
        return PlayManagerInstance.PLAYERMANAGER;
    }

    public void init(Context context) {
        PlayerLogUtil.log(TAG, "init", "");
        mContext = new WeakReference<>(context);
        SDKReportManager.getInstance();
        ToneQualityHelper.getInstance().initToneSetValue(context);
        //TODO：总感觉这里初始化很诡异。
        mPlayerListenerHelper.addPlayerInitComplete(flag -> {
            checkPlayControl();
            PlayControl.getInstance().setAudioFocusListener(mPlayerListenerHelper.getOnAudioFocusChangeInter());
            PlayerCustomizeManager.getInstance().registerAudioStateChangedByAudioFocusListener(mPlayerListenerHelper.getAudioStateChangedByAudioFocusListener());
        });
        PlayControl.getInstance().init(context,
                mPlayerListenerHelper.getBasePlayStateListener(),
                mPlayerListenerHelper.getPlayerStateVideoListener(),
                mPlayerListenerHelper.getPlayerInitCompleteListener()
        );
    }

    private boolean executePlayerMethod() {
        boolean hasPlayControl = !PlayerPreconditions.checkNull(mIPlayControl);
        if (!hasPlayControl) {
            PlayerLogUtil.log(TAG, "executePlayerMethod", "mIPlayControl is null");
        }
        boolean isPlayerInitSuccess = isPlayerInitSuccess();
        if (!isPlayerInitSuccess) {
            PlayerLogUtil.log(TAG, "executePlayerMethod", "player is not init success");
            mPlayerListenerHelper.notifyPlayerInitError(PlayerConstants.ERROR_CODE_PLAYER_NOT_INIT);
        }
        return hasPlayControl && isPlayerInitSuccess;
    }

    @FunctionalInterface
    private interface PMFunction {
        void call();
    }

    private void performAction(PMFunction function) {
        if (!executePlayerMethod()) {
            return;
        }
        try {
            function.call();
        } catch (Exception e) {
            PlayerLogUtil.log(TAG, " error:" + e);
        }
    }

    public void setTempTaskPlayListener(BasePlayStateListener listener) {
        PlayControl.getInstance().setTempTaskPlayListener(listener);
    }

    /**
     * 注册用户自定义播单控制
     *
     * @param type
     * @param iPlayListClass
     */
    public void registerCustomPlayList(int type, Class<? extends IPlayListControl> iPlayListClass) {
        if (PlayerPreconditions.checkNull(mCustomIPlayListControlHashMap)) {
            mCustomIPlayListControlHashMap = new HashMap<>();
        }
        if (type < PlayerConstants.RESOURCES_TYPE_MIN_SIZE) {
            PlayerPreconditions.checkArgument(false, "定义的播放类型必须大于" + PlayerConstants.RESOURCES_TYPE_MIN_SIZE);
        }

        mCustomIPlayListControlHashMap.put(type, iPlayListClass);
    }

    public boolean isAutoPlay() {
        return mAutoPlay;
    }

    public void setAutoPlay(boolean autoPlay) {
        this.mAutoPlay = autoPlay;
    }

    /**
     * 播放
     */
    public void play() {
        play(false);
    }

    /**
     * 播放
     *
     * @param fromUser 用户自主调用
     */
    public void play(boolean fromUser) {
        PlayerLogUtil.log(TAG, "play", "fromUser = " + fromUser);
        performAction(() -> {
            mPlayerListenerHelper.setPauseFromUser(false);
            // 修复：用户主动播放时，设置isPlayIsNow标志，确保能够重新获取音频焦点
            if (fromUser) {
                setPlayIsNow(true);
                PlayerLogUtil.log(TAG, "play", "set isPlayIsNow = true for user action");
            }
            mIPlayControl.play();
        });
    }

    /**
     * 非特殊情况下不要调用此方法
     */
    public void initPlayerForce() {
        PlayerLogUtil.log(TAG, "initPlayerForce");
        performAction(() -> {
            mPlayerListenerHelper.setPlayInitComplete(false);
            mIPlayControl.initPlayerForce();
        });
    }

    /**
     * 暂停
     */
    public void pause() {
        pause(false);
    }

    /**
     * 暂停
     *
     * @param fromUser 用户自主调用
     */
    public void pause(Boolean fromUser) {
        PlayerLogUtil.log(TAG, "pause", "fromUser = " + fromUser);
        performAction(() -> {
            mPlayerListenerHelper.setPauseFromUser(fromUser);
            mIPlayControl.pause();
            mPlayerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_PLAYER_PAUSED, getCurPlayItem());
        });
    }

    /**
     * 停止
     */
    public void stop() {
        stop(false);
    }

    /**
     * 停止
     *
     * @param fromUser 用户自主调用
     */
    public void stop(boolean fromUser) {
        PlayerLogUtil.log(TAG, "stop: ", "fromUser = " + fromUser);
        performAction(() -> {
            mPlayerListenerHelper.setPauseFromUser(fromUser);
            String reason;
            if (fromUser) {
                reason = ReportConstants.PLAY_CHANGE_BY_CLICK;
            } else {
                reason = ReportConstants.PLAY_CHANGE_BY_OTHER;
            }
            SDKReportManager.getInstance().reportEndPlay(reason, true);
            mIPlayControl.stop();
        });
    }

    public void reset() {
        reset(false);
    }

    public void reset(boolean fromUser) {
        PlayerLogUtil.log(TAG, "reset", "fromUser = " + fromUser);
        performAction(() -> {
            mPlayerListenerHelper.setPauseFromUser(fromUser);
            mIPlayControl.reset();
        });
    }

    public void rePlay() {
        PlayerLogUtil.log(TAG, "rePlay", "start");
        performAction(() -> {
            mIPlayControl.rePlay();
        });
    }

    /**
     * 切换播放状态
     */
    public void switchPlayerStatus() {
        switchPlayerStatus(false);
    }

    /**
     * 切换播放状态
     *
     * @param fromUser 是否是用户自主调用
     */
    public void switchPlayerStatus(boolean fromUser) {
        PlayerLogUtil.log(TAG, "switchPlayerStatus", "fromUser = " + fromUser);
        performAction(() -> {
            if (isPlaying()) {
                mPlayerListenerHelper.setPauseFromUser(fromUser);
            }
            mIPlayControl.switchPlayerStatus();
        });
    }


    /**
     * 快进
     *
     * @param position
     */
    public void seek(int position) {
        PlayerLogUtil.log(TAG, "seek", "position = " + position);
        performAction(() -> {
            mIPlayControl.seek(position);
        });
    }

    public PlayItem getPre() {
        final PlayItem[] prePlayItem = {null};
        PlayerLogUtil.log(TAG, "getPre");
        if (getContext() == null) {
            Log.i(TAG, "getPre getContext is null");
            return null;
        }
        if (!NetworkUtil.isNetworkAvailable(getContext())) {
            Log.i(TAG, "is not Network Available");
            mPlayerListenerHelper.notifyPlayError(-1);
            return null;
        }
        checkPlayListControl(mBuilder.getType());
        checkPlayControl();
        mIPlayListControl.getPrePlayItem(new IPlayListGetListener() {
            @Override
            public void onDataGet(PlayItem playItem, List<PlayItem> playItemArrayList) {
                if (PlayerPreconditions.checkNull(playItem)) {
                    mPlayerListenerHelper.notifyGetPlayListError(new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_ITEM_NULL, -1);
                    return;
                }
                mIPlayListControl.setCurPosition(playItem);
                prePlayItem[0] = playItem;
            }

            @Override
            public void onDataGetError(PlayItem playItem, int errorCode, int errorExtra) {
                PlayerLogUtil.log(TAG, "playPre", "get pre error");
                mPlayerListenerHelper.notifyGetPlayListError(playItem, errorCode, errorExtra);
            }
        });
        return prePlayItem[0];
    }

    /**
     * 播放上一首
     */
    public void playPre() {
        playPre(true);
    }

    public void playPre(boolean fromUser) {
        PlayerLogUtil.log(TAG, "playPre");
        if (getContext() == null) {
            PlayerLogUtil.log(TAG, "playPre getContext is null");
            return;
        }
     /*   if (!NetworkUtil.isNetworkAvailable(getContext())) {
            Log.i(TAG, "is not Network Available");
            mPlayerListenerHelper.notifyPlayError(-1);
            return;
        }*/
        checkPlayListControl(mBuilder.getType());
        checkPlayControl();
        mIPlayListControl.getPrePlayItem(new IPlayListGetListener() {
            @Override
            public void onDataGet(PlayItem playItem, List<PlayItem> playItemArrayList) {
                PlayerLogUtil.log(TAG, "playPre.onDataGet,processId= " + android.os.Process.myTid());
                if (PlayerPreconditions.checkNull(playItem)) {
                    mPlayerListenerHelper.notifyGetPlayListError(new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_ITEM_NULL, -1);
                    return;
                }
                mIPlayListControl.setCurPosition(playItem);
                boolean isPlayNow = !mPlayerListenerHelper.isPauseFromUser() || !((getPlayStatus() == PlayerConstants.TYPE_PLAYER_PAUSED) && !fromUser);
                startPlayItem(mBuilder.getType(), playItem, mBuilder.getVideoView(), isPlayNow);
            }

            @Override
            public void onDataGetError(PlayItem playItem, int errorCode, int errorExtra) {
                PlayerLogUtil.log(TAG, "playPre", "get pre error");
                mPlayerListenerHelper.notifyGetPlayListError(playItem, errorCode, errorExtra);
            }
        });
    }

    /**
     * 获取下一首
     */
    public PlayItem getNext() {
        final PlayItem[] nextPlayItem = {null};
        PlayerLogUtil.log(TAG, "getNext");
        if (getContext() == null) {
            PlayerLogUtil.log(TAG, "getNext getContext is null");
            return null;
        }
//        if (!NetworkUtil.isNetworkAvailable(getContext())) {
//            mPlayerListenerHelper.notifyPlayError(-1);
//            Log.i(TAG, "playNext notifyPlayError");
//            return null;
//        }
        checkPlayListControl(mBuilder.getType());
        checkPlayControl();
        mIPlayListControl.getNextPlayItem(new IPlayListGetListener() {
            @Override
            public void onDataGet(PlayItem playItem, List<PlayItem> playItemArrayList) {
                PlayerLogUtil.log(TAG, "playNext", "success");
                if (PlayerPreconditions.checkNull(playItem)) {
                    mPlayerListenerHelper.notifyGetPlayListError(new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_ITEM_NULL, -1);
                    return;
                }
                mIPlayListControl.setCurPosition(playItem);
                nextPlayItem[0] = playItem;
            }

            @Override
            public void onDataGetError(PlayItem playItem, int errorCode, int errorExtra) {
                PlayerLogUtil.log(TAG, "playNext", "get next error");
                mIPlayControl.setInvalidPlayItem();
                mPlayerListenerHelper.notifyGetPlayListError(playItem, errorCode, errorExtra);
            }
        });
        return nextPlayItem[0];
    }

    /**
     * 播放下一首
     */
    public void playNext() {
        playNext(true);
    }

    public void playNext(boolean fromUser) {
        PlayerLogUtil.log(TAG, "playNext");
        if (getContext() == null) {
            PlayerLogUtil.log(TAG, "playNext getContext is null");
            return;
        }
        if (!NetworkUtil.isNetworkAvailable(getContext())) {
            Log.i(TAG, "is not Network Available");
            mPlayerListenerHelper.notifyPlayError(-1);
            return;
        }
        checkPlayListControl(mBuilder.getType());
        checkPlayControl();
        mIPlayListControl.getNextPlayItem(new IPlayListGetListener() {
            @Override
            public void onDataGet(PlayItem playItem, List<PlayItem> playItemArrayList) {
                PlayerLogUtil.log(TAG, "playNext.onDataGet,processId= " + android.os.Process.myTid());
                if (PlayerPreconditions.checkNull(playItem)) {
                    mPlayerListenerHelper.notifyGetPlayListError(new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_ITEM_NULL, -1);
                    return;
                }
                mIPlayListControl.setCurPosition(playItem);
                //boolean isPlayNow = !mPlayerListenerHelper.isPauseFromUser() || !((getPlayStatus() == PlayerConstants.TYPE_PLAYER_PAUSED) && !fromUser);
                boolean isPausedFromUser = mPlayerListenerHelper.isPauseFromUser();
                boolean isPlayerPaused = getPlayStatus() == PlayerConstants.TYPE_PLAYER_PAUSED;
                // 打印每个条件
                PlayerLogUtil.log(TAG, "isPausedFromUser: " + isPausedFromUser);
                PlayerLogUtil.log(TAG, "isPlayerPaused: " + isPlayerPaused);
                PlayerLogUtil.log(TAG, "isFromUser: " + fromUser);
                // 将条件分开
                boolean condition1 = !isPausedFromUser;
                boolean condition2 = !(isPlayerPaused && !fromUser);
                // 计算最终结果
                boolean isPlayNow = condition1 || condition2;

                startPlayItem(playItem.getType(), playItem, mBuilder.getVideoView(), isPlayNow);
            }

            @Override
            public void onDataGetError(PlayItem playItem, int errorCode, int errorExtra) {
                PlayerLogUtil.log(TAG, "playNext", "get next error");
                mIPlayControl.setInvalidPlayItem();
                mPlayerListenerHelper.notifyGetPlayListError(playItem, errorCode, errorExtra);
            }
        });
    }

    public void startTempTask(TempTaskPlayItem playItem, VideoView videoView, boolean pausePlayItemStateUpdate) {
        PlayerLogUtil.log(TAG, "startTempTask,processId= " + android.os.Process.myTid() + ", pausePlayItemStateUpdate = " + pausePlayItemStateUpdate);
        if (PlayerPreconditions.checkNull(playItem)) {
            return;
        }
        PlayerLogUtil.log(TAG, "startTempTask", "playurl = " + playItem.getPlayUrl());
        checkPlayControl();
        mIPlayControl.startTempTask(playItem, videoView, pausePlayItemStateUpdate);
    }

    /**
     * 播放临时任务
     *
     * @param tempTaskPlayItem         临时任务item
     * @param pausePlayItemStateUpdate true:暂停当前播放item的状态，false:不暂停当前正在播放item状态。非特殊情况应当使用true。
     */
    public void startTempTask(TempTaskPlayItem tempTaskPlayItem, boolean pausePlayItemStateUpdate) {
        startTempTask(tempTaskPlayItem, null, pausePlayItemStateUpdate);
    }

    public void startTempTask(TempTaskPlayItem tempTaskPlayItem, VideoView videoView) {
        startTempTask(tempTaskPlayItem, videoView, true);
    }

    /**
     * 播放一个临时任务
     */
    public void startTempTask(TempTaskPlayItem playItem) {
        startTempTask(playItem, null, true);
    }

    /**
     * 停止一个临时任务
     */
    public void stopTempTask() {
        stopTempTask(true);
    }

    public void stopTempTask(boolean continueStartPlayItem) {
        stopTempTask(continueStartPlayItem, true);
    }

    /**
     * 停止一个临时任务
     *
     * @param continueStartPlayItem 继续播放碎片
     */
    public void stopTempTask(boolean continueStartPlayItem, boolean needResetLastPlaybackRateFlag) {
        checkPlayControl();
        mIPlayControl.stopTempTask(continueStartPlayItem, needResetLastPlaybackRateFlag);
    }

    public void justStopTempTask(int type) {
        mIPlayControl.justStopTempTask(type);
    }

    /**
     * 播放播放列表里面的数据
     *
     * @param playItem
     */
    public void startPlayItemInList(PlayItem playItem, VideoView videoView) {
        PlayerLogUtil.log(TAG, "startPlayItemInList,processId= " + android.os.Process.myTid());
        if (playItem == null) {
            return;
        }
        if (PlayerPreconditions.checkNull(mIPlayListControl)) {
            return;
        }
        if (mIPlayListControl instanceof BroadcastPlayListControl && playItem instanceof TimeDiscontinuousBroadcastPlayItem) {
            if (!mIPlayListControl.isExistPlayItem(playItem)) {
                //如果是广播补全的节目，则需要额外判断开始时间
                return;
            }
        } else if (!mIPlayListControl.isExistPlayItem(playItem.getAudioId())) {
            //其他节目或碎片，只需要判断audioId
            return;
        }
        startPlayItem(playItem, videoView);
    }

    /**
     * 初始化播放操作，根据builder.getIsPlayNow传入参数决定是否立即播放
     *
     * @param builder
     */
    public void start(PlayerBuilder builder) {
        PlayerLogUtil.log(TAG, "start", "PlayerBuilder = " + (builder == null ? "null" : builder.toString()));
        if (PlayerPreconditions.checkNull(builder)) {
            return;
        }

        checkPlayListControl(builder.getType());
        checkPlayControl();

        pause(builder.isPauseFromUser());

        if (builder instanceof VideoPlayerBuilder) {
            PlayerLogUtil.log(TAG, "start", "if (builder instanceof VideoPlayerBuilder)");
            startNewBuilder(builder);
        } else {
            PlayItem playItem = mIPlayListControl.getPlayItem(builder);
            if (playItem != null) {
                startPlayItem(builder, playItem);
            } else {
                startNewBuilder(builder);
            }
        }
    }

    /**
     * 开始播放播单补全的广播节目
     */
    public void startTimeDiscontinuousBroadcastPlayItem(TimeDiscontinuousBroadcastPlayItem playItem) {
        if (PlayerPreconditions.checkNull(playItem) || PlayerPreconditions.checkNull(mBuilder) || PlayerPreconditions.checkNull(mIPlayListControl) || PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }
        if (!mBuilder.getId().equals(String.valueOf(playItem.getRadioId()))) {
            PlayerLogUtil.log(TAG, "startTimeDiscontinuousBroadcastPlayItem", "The program is not owned by the current radio!");
            return;
        }
        if (!mIPlayListControl.isExistPlayItem(playItem)) {
            PlayerLogUtil.log(TAG, "startTimeDiscontinuousBroadcastPlayItem", "This program does not belong to the current playlist!");
            return;
        }
        startPlayItem(playItem, null);
    }

    /**
     * 禁用淡入淡出效果
     */
    public void disableAudioFade() {
        performAction(() -> {
            mIPlayControl.disableAudioFade();
        });
    }

    public void setHttpProxy(String httpProxy) {
        performAction(() -> {
            mIPlayControl.setHttpProxy(httpProxy);
        });
    }

    public void clearHttpProxy() {
        performAction(() -> {
            mIPlayControl.clearHttpProxy();
        });
    }

    // 清除dns缓存
    public void clearDnsCache(boolean clearDnsCache) {
        performAction(() -> {
            mIPlayControl.clearDnsCache(clearDnsCache);
        });
    }

    /**
     * 设置淡入淡出效果配置
     *
     * @param audioFadeConfig
     */
    public void setAudioFadeConfig(AudioFadeConfig audioFadeConfig) {
        performAction(() -> {
            mIPlayControl.setAudioFadeConfig(audioFadeConfig);
        });
    }

    /**
     * 校验 PlayListControl
     *
     * @param type
     */
    private void checkPlayListControl(int type) {
        if (PlayerPreconditions.checkNull(mIPlayListControl)) {
            initCustomPlayListControl(type);
            if (PlayerPreconditions.checkNull(mIPlayListControl)) {
                Log.i(TAG, "mIPlayListControl is null");
                mIPlayListControl = PlayListControlFactory.getPlayListControl(type);
                mIPlayListControl.setCallback(mPlayerListenerHelper.getPlayListStateListener());
            }
        }
    }

    /**
     * 校验playControl
     */
    private void checkPlayControl() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            Log.i(TAG, "PlayControl is null,PlayControl.getInstance");
            mIPlayControl = PlayControl.getInstance();
        }
    }

    /**
     * 开始播放
     *
     * @param playItem
     */
    private void startPlayItem(PlayItem playItem, VideoView videoView) {
        PlayerLogUtil.log(TAG, "startPlayItem, video view != null,processId= " + android.os.Process.myTid());
        mIPlayListControl.setCurPosition(playItem);
        startPlayItem(playItem.getType(), playItem, videoView, true);
    }

    /**
     * 开始播放
     *
     * @param builder
     * @param playItem
     */
    private void startPlayItem(PlayerBuilder builder, PlayItem playItem) {
        PlayerLogUtil.log(TAG, "startPlayItem, video view == null,processId= " + android.os.Process.myTid());
        mIPlayListControl.setCurPosition(playItem);
        long position = getSeekPosition(builder);
        if (position > 0) {
            playItem.setPosition((int) position);
        }
        startPlayItem(playItem.getType(), playItem, builder.getVideoView(), builder.isPlayNow());
    }

    /**
     * startPlayItem 最终执行方法
     */
    private void startPlayItem(int type, PlayItem playItem, VideoView videoView, boolean isPlayNow) {
        mIPlayControl.start(type, playItem, videoView, isPlayNow);
    }

    /**
     * 开始一个新的builder播放
     *
     * @param builder
     */
    private void startNewBuilder(PlayerBuilder builder) {
        PlayerLogUtil.log(TAG, "startNewBuilder", "PlayerBuilder = " + (builder == null ? "null" : builder.toString()));
        int type = builder.getType();
        if (type == PlayerConstants.RESOURCES_TYPE_INVALID || type == PlayerConstants.RESOURCES_TYPE_ERROR) {
            PlayerLogUtil.log(TAG, "startNewBuilder", "【RESOURCES_TYPE_INVALID || RESOURCES_TYPE_ERROR】");
            return;
        }
        IPlayListControl curPlayListControl;

        if (type == mCustomType) {
            curPlayListControl = mIPlayListControl;
        } else {
            curPlayListControl = PlayListControlFactory.getPlayListControl(builder.getType());
        }

        checkCopyright(builder, curPlayListControl, new ICheckCopyrightListener() {
            @Override
            public void onGranted() {
                if (type == mCustomType) {
                    PlaylistInfo playlistInfo = mIPlayListControl.getPlayListInfo();
                    playlistInfo.setTempId(builder.getId());
                    if (builder instanceof CustomPlayerBuilder) {
                        playlistInfo.setTempChildId(((CustomPlayerBuilder) builder).getChildId());
                    }
                }
                if (mIPlayListControl != curPlayListControl) {
                    mIPlayListControl.release();
                    mIPlayListControl = curPlayListControl;
                    mIPlayListControl.setCallback(mPlayerListenerHelper.getPlayListStateListener());
                }

                initBuilder(builder);

                curPlayListControl.initPlayList(builder, new IPlayListGetListener() {
                    @Override
                    public void onDataGet(PlayItem playItem, List<PlayItem> playItemArrayList) {
                        PlayerLogUtil.log(TAG, "startNewBuilder", "get play list success");

                        long position = getSeekPosition(builder);
                        if (position > 0) {
                            playItem.setPosition((int) position);
                        }
                        mIPlayListControl.setCurPosition(playItem);
                        playBuilder(playItem, builder.isPlayNow());
                    }

                    @Override
                    public void onDataGetError(PlayItem playItem, int errorCode, int errorExtra) {
                        PlayerLogUtil.log(TAG, "onDataGetError,processId= " + android.os.Process.myTid());
                        PlayerLogUtil.log(TAG, "startNewBuilder", "get play list error");
                        mIPlayListControl.setCurPosition(playItem);
                        // do 开始走 起播 - 拦截器 - 播放
                        startPlayItem(playItem.getType(), playItem, mBuilder.getVideoView(), false);
                        initErrorBuilder(mBuilder);
                        // do 唤起 LauncherActivity#generalListener#getPlayListError Toast
                        mPlayerListenerHelper.notifyGetPlayListError(playItem, errorCode, errorExtra);
                    }
                });
            }

            @Override
            public void onError(PlayItem playItem, int errorCode, int errorExtra) {
                PlayerLogUtil.log(TAG, "startNewBuilder", "check copyright error: errorcode=" + errorCode + " ,errorextra=" + errorExtra);
                mPlayerListenerHelper.notifyGetPlayListError(playItem, errorCode, errorExtra);
                if (errorCode != PlayerConstants.ERROR_CODE_RADIO_COPYRIGHT_LITE) {
                    //按照以前的逻辑，只要是已经授权了，哪怕是请求数据有问题，也要接着走流程,现在保持逻辑不变
                    continueOnError(builder, curPlayListControl, playItem);
                } else {
                    if (!mPlayerListenerHelper.isPauseFromUser()) {
                        play();
                    }
                }
            }
        });
    }

    public void playBuilder(PlayItem playItem, boolean isPlayNow) {
        PlayerLogUtil.log(TAG, "playBuilder,processId= " + android.os.Process.myTid());
        if (mBuilder != null && mIPlayControl != null) {
            startPlayItem(playItem.getType(), playItem, mBuilder.getVideoView(), isPlayNow);
        }
    }

    /**
     * 当检查简版出错后，继续按照以前的逻辑走流程
     * 按照以前的逻辑，只要是已经授权了，哪怕是请求数据有问题，也要接着走流程,现在保持逻辑不变
     *
     * @param builder
     * @param tempIPlayListControl
     * @param playItem
     */
    private void continueOnError(PlayerBuilder builder, IPlayListControl tempIPlayListControl, PlayItem playItem) {
        PlayerLogUtil.log(TAG, "continueOnError,processId= " + android.os.Process.myTid());
        if (builder.getType() == mCustomType) {
            PlaylistInfo playlistInfo = mIPlayListControl.getPlayListInfo();
            playlistInfo.setTempId(builder.getId());
            if (builder instanceof CustomPlayerBuilder) {
                playlistInfo.setTempChildId(((CustomPlayerBuilder) builder).getChildId());
            }
        }
        if (mIPlayListControl != tempIPlayListControl) {
            PlayerLogUtil.log(TAG, "startNewBuilder", "get play list success");
            mIPlayListControl.release();
            mIPlayListControl = tempIPlayListControl;
            mIPlayListControl.setCallback(mPlayerListenerHelper.getPlayListStateListener());
        }

        initBuilder(builder);

        mIPlayListControl.setCurPosition(playItem);
        startPlayItem(playItem.getType(), playItem, mBuilder.getVideoView(), true);
        initErrorBuilder(mBuilder);
    }

    /**
     * 校验广播/电视的版权
     */
    private void checkCopyright(PlayerBuilder builder, IPlayListControl playListControl, ICheckCopyrightListener listener) {
        if (playListControl instanceof BroadcastPlayListControl) {
            ((BroadcastPlayListControl) playListControl).checkCopyright(builder, listener);
        } else if (playListControl instanceof TVPlayListControl) {
            ((TVPlayListControl) playListControl).checkCopyright(builder, listener);
        } else if (listener != null) {
            listener.onGranted();
        }
    }

    /**
     * 校验广播/电视的版权
     *
     * @param builder
     * @param iCheckCopyrightListener
     */
    public void checkCopyright(PlayerBuilder builder, ICheckCopyrightListener iCheckCopyrightListener) {
        if (builder.getType() == PlayerConstants.RESOURCES_TYPE_INVALID) {
            return;
        }
        IPlayListControl tempIPlayListControl;

        if (builder.getType() == mCustomType) {
            PlayerLogUtil.log(TAG, "checkCopyright", "the same type");
            tempIPlayListControl = mIPlayListControl;
        } else {
            PlayerLogUtil.log(TAG, "checkCopyright", "different type");
            tempIPlayListControl = PlayListControlFactory.getPlayListControl(builder.getType());
        }
        checkCopyright(builder, tempIPlayListControl, iCheckCopyrightListener);
    }

    private void initErrorBuilder(PlayerBuilder builder) {
        if (builder.getType() == PlayerConstants.RESOURCES_TYPE_BROADCAST
                || builder.getType() == PlayerConstants.RESOURCES_TYPE_TV) {
            pause();
            mIPlayControl.stopTimer();
        }
    }

    /**
     * 是否有下一页数据
     *
     * @return
     */
    public boolean hasNextPage() {
        if (PlayerPreconditions.checkNull(mIPlayListControl)) {
            return false;
        }
        return mIPlayListControl.hasNextPage();
    }

    /**
     * 是否有上一页数据
     *
     * @return
     */
    public boolean hasPrePage() {
        if (PlayerPreconditions.checkNull(mIPlayListControl)) {
            return false;
        }
        return mIPlayListControl.hasPrePage();
    }

    /**
     * 获取当前播放的playitem
     *
     * @return
     */
    public PlayItem getCurPlayItem() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return new InvalidPlayItem();
        }
        return mIPlayControl.getCurrentPlayItem();
    }

    /**
     * 获取当前播放的VideoView
     *
     * @return
     */
    public VideoView getCurVideoView() {
        return mIPlayControl.getCurrentVideoView();
    }

    /**
     * 获取当前播放的在播单位置
     *
     * @return
     */
    public int getPlayListCurrentPosition() {
        if (PlayerPreconditions.checkNull(mIPlayListControl)) {
            return -1;
        }
        return mIPlayListControl.getCurPosition();
    }

    /**
     * 是否有上一首
     *
     * @return
     */
    public boolean hasPre() {
        if (PlayerPreconditions.checkNull(mIPlayListControl)) {
            return false;
        }
        return mIPlayListControl.hasPre();
    }

    /**
     * 是否有下一首
     *
     * @return
     */
    public boolean hasNext() {
        if (PlayerPreconditions.checkNull(mIPlayListControl)) {
            return false;
        }
        return mIPlayListControl.hasNext();
    }


    /**
     * 请求音频焦点
     *
     * @return
     */
    public boolean requestAudioFocus() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            mIPlayControl = PlayControl.getInstance();
        }
        return mIPlayControl.requestAudioFocus();
    }


    /**
     * 释放音频焦点
     *
     * @return
     */
    public boolean abandonAudioFocus() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            mIPlayControl = PlayControl.getInstance();
        }
        return mIPlayControl.abandonAudioFocus();
    }


    /**
     * 获取播单
     *
     * @return
     */
    public List<PlayItem> getPlayList() {
        if (PlayerPreconditions.checkNull(mIPlayListControl)) {
            return null;
        }
        return mIPlayListControl.getPlayList();
    }

    /**
     * 加载下一页数据
     *
     * @param iPlayListGetListener
     */
    public void loadNextPage(IPlayListGetListener iPlayListGetListener) {
        if (PlayerPreconditions.checkNull(mIPlayListControl)) {
            return;
        }
        mIPlayListControl.loadNextPage(iPlayListGetListener);
    }

    /**
     * 加载上一页数据
     *
     * @param iPlayListGetListener
     */
    public void loadPrePage(IPlayListGetListener iPlayListGetListener) {
        if (PlayerPreconditions.checkNull(mIPlayListControl)) {
            return;
        }
        mIPlayListControl.loadPrePage(iPlayListGetListener);
    }

    public void loadPageData(long audioId, int pageNum, IPlayListGetListener iPlayListGetListener) {
        if (PlayerPreconditions.checkNull(mIPlayListControl)) {
            return;
        }
        mIPlayListControl.loadPageData(AlbumPlayListControl.LOAD_DATA_PAGE,
                audioId, pageNum, iPlayListGetListener);
    }

    /**
     * 获取当前播单信息
     *
     * @return
     */
    public PlaylistInfo getPlayListInfo() {
        if (PlayerPreconditions.checkNull(mIPlayListControl)) {
            return null;
        }
        return mIPlayListControl.getPlayListInfo();
    }

    /**
     * 获取播单控制
     *
     * @return
     */
    public IPlayListControl getPlayListControl() {
        return mIPlayListControl;
    }

    /**
     * 设置是否支持音量均衡
     *
     * @param isActive
     */
    public void setLoudnessNormalization(int isActive) {
        performAction(() -> {
            mIPlayControl.setLoudnessNormalization(isActive);
        });
    }

    /**
     * 获取当前操作是否是用户自主获取
     *
     * @return
     */
    public boolean isPauseFromUser() {
        return mPlayerListenerHelper.isPauseFromUser();
    }

    private void initBuilder(PlayerBuilder playerBuilder) {
        if (PlayerPreconditions.checkNull(playerBuilder)) {
            return;
        }
        PlayerLogUtil.log(TAG, "initBuilder: ");
        if (playerBuilder.getType() != PlayerConstants.RESOURCES_TYPE_INVALID) {
            mBuilder = playerBuilder;
            mCustomType = mBuilder.getType();
            PlayerLogUtil.log(TAG, "initBuilder", "type = " + mCustomType + " id = " + mBuilder.getId());
        }
        if (mContext == null) {
            PlayerLogUtil.log(TAG, "initBuilder mContext is null");
        }
        String ip = NetworkUtil.getIPAddress(mContext.get());
        Log.i(TAG, "ip:" + ip);
    }

    private void initCustomPlayListControl(int type) {
        if (!ListUtil.isEmpty(mCustomIPlayListControlHashMap)) {
            Class<? extends IPlayListControl> tempClass = mCustomIPlayListControlHashMap.get(type);
            if (!PlayerPreconditions.checkNull(tempClass)) {
                try {
                    mIPlayListControl = tempClass.newInstance();
                    mIPlayListControl.setCallback(mPlayerListenerHelper.getPlayListStateListener());
                } catch (Exception e) {

                }
            }
        }
    }

    private long getSeekPosition(PlayerBuilder playerBuilder) {
        if (playerBuilder instanceof CustomPlayerBuilder) {
            CustomPlayerBuilder customPlayerBuilder = (CustomPlayerBuilder) playerBuilder;
            return customPlayerBuilder.getSeekPosition();
        }
        return 0;
    }

    public boolean isPlayerInitSuccess() {
        return mPlayerListenerHelper.isPlayerInitSuccess();
    }

    public boolean isPlaying() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return false;
        }
        return mIPlayControl.isPlaying();
    }

    public int getPlayStatus() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return PlayerConstants.TYPE_PLAYER_IDLE;
        }
        return mIPlayControl.getPlayStatus();
    }

    public void setLogInValid() {
        performAction(() -> {
            mIPlayControl.setLogInValid();
        });
    }

    public void enableSDKPlayerLog() {
        PlayerLogUtil.enableLog();
    }

    public void disableSDKPlayerLog() {
        PlayerLogUtil.disableLog();
    }

    public long getCurrentPlayPlace() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return -1;
        }
        return mIPlayControl.getCurrentPosition();
    }

    public void setMediaVolume(float leftVolume, float rightVolume) {
        performAction(() -> {
            mIPlayControl.setMediaVolume(leftVolume, rightVolume);
        });
    }

    public int getCurrentAudioFocusStatus() {
        return mPlayerListenerHelper.getAudioFocusStatus();
    }

    /**
     * 添加播单改变监听
     *
     * @param iPlayListControlListener
     */
    public void addPlayListControlStateCallback(IPlayListStateListener iPlayListControlListener) {
        mPlayerListenerHelper.addPlayListControlStateCallback(iPlayListControlListener);
    }

    /**
     * 删除播单改变监听
     *
     * @param iPlayListControlListener
     */
    public void removePlayListControlStateCallback(IPlayListStateListener iPlayListControlListener) {
        mPlayerListenerHelper.removePlayListControlStateCallback(iPlayListControlListener);
    }

    /**
     * 添加播放状态监听
     *
     * @param iPlayControlListener
     */
    public void addPlayControlStateCallback(IPlayerStateListener iPlayControlListener) {
        mPlayerListenerHelper.addPlayControlStateCallback(iPlayControlListener);
    }

    /**
     * 删除播放状态监听
     *
     * @param iPlayControlListener
     */
    public void removePlayControlStateCallback(IPlayerStateListener iPlayControlListener) {
        mPlayerListenerHelper.removePlayControlStateCallback(iPlayControlListener);
    }

    /**
     * 添加视频状态监听
     *
     * @param iPlayerStateVideoListener
     */
    public void addPlayerControlStateVideoCallBack(IPlayerStateVideoListener iPlayerStateVideoListener) {
        mPlayerListenerHelper.addPlayerControlStateVideoCallBack(iPlayerStateVideoListener);
    }

    /**
     * 删除视频状态监听
     *
     * @param iPlayerStateVideoListener
     */
    public void removePlayerControlStateVideoCallBack(IPlayerStateVideoListener iPlayerStateVideoListener) {
        mPlayerListenerHelper.removePlayerControlStateVideoCallBack(iPlayerStateVideoListener);
    }

    /**
     * 添加音频焦点状态监听
     *
     * @param iAudioFocusListener
     */
    public void addAudioFocusListener(OnAudioFocusChangeInter iAudioFocusListener) {
        mPlayerListenerHelper.addAudioFocusListener(iAudioFocusListener);
    }

    /**
     * 删除音频焦点状态监听
     *
     * @param iAudioFocusListener
     */
    public void removeAudioFocusListener(OnAudioFocusChangeInter iAudioFocusListener) {
        mPlayerListenerHelper.removeAudioFocusListener(iAudioFocusListener);
    }

    /**
     * 添加播放器初始化完成监听
     *
     * @param iPlayerInitCompleteListener
     */
    public void addPlayerInitComplete(IPlayerInitCompleteListener iPlayerInitCompleteListener) {
        mPlayerListenerHelper.addPlayerInitComplete(iPlayerInitCompleteListener);
    }

    /**
     * 删除播放器初始化完成监听
     *
     * @param iPlayerInitCompleteListener
     */
    public void removePlayerInitComplete(IPlayerInitCompleteListener iPlayerInitCompleteListener) {
        mPlayerListenerHelper.removePlayerInitComplete(iPlayerInitCompleteListener);
    }

    /**
     * 添加播放器通用回调
     *
     * @param iGeneralListener
     */
    public void addGeneralListener(IGeneralListener iGeneralListener) {
        mPlayerListenerHelper.addGeneralListener(iGeneralListener);
    }

    /**
     * 删除播放器通用回调
     *
     * @param generalListener
     */
    public void removeGeneralListener(IGeneralListener generalListener) {
        mPlayerListenerHelper.removeGeneralListener(generalListener);
    }


    public void setPlayerInteractionFiredListener(IPlayerInteractionFiredListener interactionFiredListener) {
        checkPlayControl();
        mIPlayControl.setPlayerInteractionFiredListener(interactionFiredListener);
    }

    public void setPlayerAsncStartExecutingListener(IPlayerAsncStartExecutingListener mPlayerAsncStartExecutingListener) {
        checkPlayControl();
        mIPlayControl.setPlayerAsncStartExecutingListener(mPlayerAsncStartExecutingListener);
    }

    public int getCustomType() {
        if (mBuilder != null) {
            return mBuilder.getType();
        }
        return PlayerConstants.RESOURCES_TYPE_INVALID;
    }

    public PlayItem getCurrentTempTaskPlayItem() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return null;
        }
        return mIPlayControl.getCurrentTempTaskPlayItem();
    }

    /**
     * destroy
     */
    public void destroy() {
        if (!PlayerPreconditions.checkNull(mIPlayControl)) {
            mIPlayControl.destroy();
        }
        mPlayerListenerHelper.release();
    }


    public void notifyAudioFocus(int state) {
        mPlayerListenerHelper.notifyAudioFocus(state);
    }


    /**
     * 通过audio请求数据拿到playItem
     *
     * @param audio
     * @return
     */
    public void getPlayItemFromAudioId(long audio, GetPlayItemListener listener) {
        new AudioRequest().getAudioDetails(audio, new HttpCallback<AudioDetails>() {
            @Override
            public void onSuccess(AudioDetails audioDetails) {
                PlayItem albumPlayItem = PlayListUtils.translateAlbumToPlayItem(audioDetails);
                listener.success(albumPlayItem);
            }

            @Override
            public void onError(ApiException exception) {
                listener.error(exception);
            }
        });
    }

    public interface GetPlayItemListener {
        void success(PlayItem playitem);

        void error(ApiException exception);
    }


    /**
     * 用户状态变化或支付状态变化时重新拉取播单时调用
     */
    public void resetPlayListControl() {
        if (mIPlayListControl != null) {
            mIPlayListControl.clearPlayList();
            mIPlayListControl = null;
        }
        mCustomIPlayListControlHashMap = null;
        reset(false);
    }


    /**
     * 设置AudioTrack的AudioAttributes的参数Usage和ContentType
     * 备注：在prepare()函数之前调用
     *
     * @param usage       场景
     * @param contentType 内容类型
     */
    public void setUsageAndContentType(int usage, int contentType) {
        checkPlayControl();
        mIPlayControl.setUsageAndContentType(usage, contentType);
    }

    public void setPlaybackRate(float rate) {
        performAction(() -> {
            mIPlayControl.setPlaybackRate(rate);
        });
    }

    public float getPlaybackRate() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return -1;
        }
        return mIPlayControl.getPlaybackRate();
    }

    public void setSpeedLimitState(boolean isLimit) {
        this.mIsLimit = isLimit;
    }

    public boolean getSpeedLimitState() {
        return mIsLimit;
    }

    public void setPauseFromUser(boolean fromUser) {
        PlayerLogUtil.log(TAG, "setPauseFromUser", "fromUser = " + fromUser);
        mPlayerListenerHelper.setPauseFromUser(fromUser);
    }

    /**
     * 刷新广播节目详情信息
     *
     * @param iPlayListGetListener 回调监听器
     * @param playItem 需要刷新的播放项
     */
    public void refreshBroadcastProgramDetails(IPlayListGetListener iPlayListGetListener, PlayItem playItem) {
        if (PlayerPreconditions.checkNull(playItem)) {
            PlayerLogUtil.log(TAG, "refreshBroadcastProgramDetails", "playItem is null");
            if (iPlayListGetListener != null) {
                iPlayListGetListener.onDataGetError(playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_NULL, -1);
            }
            return;
        }

        if (!(playItem instanceof BroadcastPlayItem)) {
            PlayerLogUtil.log(TAG, "refreshBroadcastProgramDetails", "playItem is not BroadcastPlayItem");
            if (iPlayListGetListener != null) {
                iPlayListGetListener.onDataGetError(playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_NULL, -1);
            }
            return;
        }

        PlayerLogUtil.log(TAG, "refreshBroadcastProgramDetails", "refreshing details for audioId=" + playItem.getAudioId());

        new BroadcastRequest().getBroadcastProgramDetails(playItem.getAudioId(), new HttpCallback<ProgramDetails>() {
            @Override
            public void onSuccess(ProgramDetails programDetails) {
                PlayerLogUtil.log(TAG, "refreshBroadcastProgramDetails", "get detail success");
                if (PlayerPreconditions.checkNull(programDetails)) {
                    PlayerLogUtil.log(TAG, "refreshBroadcastProgramDetails", "programDetails is null");
                    if (iPlayListGetListener != null) {
                        iPlayListGetListener.onDataGetError(playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_NULL, -1);
                    }
                    return;
                }

                PlayerLogUtil.log(TAG, "refreshBroadcastProgramDetails", "get detail success status: " + programDetails.getStatus());
                BroadcastPlayItem broadcastPlayItem = (BroadcastPlayItem) playItem;

                // 根据服务器返回的节目状态设置正确的播放状态和URL
                int programStatus = programDetails.getStatus();
                if (programStatus == PlayerConstants.BROADCAST_STATUS_LIVING ||
                        programStatus == PlayerConstants.BROADCAST_STATUS_DEFAULT) {
                    // 直播节目
                    broadcastPlayItem.setStatus(PlayerConstants.BROADCAST_STATUS_LIVING);
                    PlayerLogUtil.log(TAG, "refreshBroadcastProgramDetails", "success, old url = " + playItem.getPlayUrl() + " , new live url= " + programDetails.getPlayUrl());
                    broadcastPlayItem.setPlayUrl(programDetails.getPlayUrl());
                } else if (programStatus == PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
                    // 回放节目
                    broadcastPlayItem.setStatus(PlayerConstants.BROADCAST_STATUS_PLAYBACK);
                    String backUrl = programDetails.getBackLiveUrl();
                    PlayerLogUtil.log(TAG, "refreshBroadcastProgramDetails", "success, old url = " + playItem.getPlayUrl() + " , new back url= " + backUrl);
                    broadcastPlayItem.setPlayUrl(backUrl);
                } else {
                    // 其他状态（如未开播等）
                    broadcastPlayItem.setStatus(programStatus);
                    broadcastPlayItem.setPlayUrl(null);
                    PlayerLogUtil.log(TAG, "refreshBroadcastProgramDetails", "success, program status = " + programStatus + ", set url to null");
                }

                // 更新时间信息
                broadcastPlayItem.getTimeInfoData().setStartTime(programDetails.getStartTime());
                broadcastPlayItem.getTimeInfoData().setFinishTime(programDetails.getFinishTime());
                broadcastPlayItem.getTimeInfoData().setBeginTime(programDetails.getBeginTime());
                broadcastPlayItem.getTimeInfoData().setEndTime(programDetails.getEndTime());
                broadcastPlayItem.getTimeInfoData().setCurSystemTime(DateUtil.getServerTime());

                // 更新播放信息
                broadcastPlayItem.setPlayInfoList(programDetails.getPlayInfoList());
                broadcastPlayItem.setBackPlayInfoList(programDetails.getBackPlayInfoList());

                // 通知刷新成功
                if (iPlayListGetListener != null) {
                    iPlayListGetListener.onDataGet(broadcastPlayItem, null);
                }

                PlayerLogUtil.log(TAG, "refreshBroadcastProgramDetails", "refresh completed successfully");
            }

            @Override
            public void onError(ApiException exception) {
                PlayerLogUtil.log(TAG, "refreshBroadcastProgramDetails", "get detail error: " + exception.getMessage());

                // 通知刷新失败
                if (iPlayListGetListener != null) {
                    int errorCode = exception.getCode();
                    iPlayListGetListener.onDataGetError(playItem, errorCode, -1);
                }
            }
        });
    }

    /**
     * 异步获取下一首
     *
     * @param iPlayListGetListener 回调监听器
     */
    public void getNextAsync(IPlayListGetListener iPlayListGetListener) {
        PlayerLogUtil.log(TAG, "getNextAsync");
        if (getContext() == null) {
            PlayerLogUtil.log(TAG, "getNextAsync getContext is null");
            if (iPlayListGetListener != null) {
                iPlayListGetListener.onDataGetError(null, PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_ITEM_NULL, -1);
            }
            return;
        }
        if (!NetworkUtil.isNetworkAvailable(getContext())) {
            PlayerLogUtil.log(TAG, "getNextAsync is not Network Available");
            mPlayerListenerHelper.notifyPlayError(-1);
            if (iPlayListGetListener != null) {
                iPlayListGetListener.onDataGetError(null, PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_ITEM_NULL, -1);
            }
            return;
        }

        checkPlayListControl(mBuilder.getType());
        checkPlayControl();

        // 保存后备方案数据
        final int originalPosition = mIPlayListControl.getCurPosition();
        final List<PlayItem> originalPlayList = mIPlayListControl.getPlayList();

        mIPlayListControl.getNextPlayItem(new IPlayListGetListener() {
            @Override
            public void onDataGet(PlayItem playItem, List<PlayItem> playItemArrayList) {
                PlayerLogUtil.log(TAG, "getNextAsync.onDataGet", "success");
                if (PlayerPreconditions.checkNull(playItem)) {
                    PlayerLogUtil.log(TAG, "getNextAsync", "playItem is null, trying backup");
                    mPlayerListenerHelper.notifyGetPlayListError(new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_ITEM_NULL, -1);

                    // 尝试后备方案
                    PlayItem backupItem = getBackupNextItem(originalPlayList, originalPosition);
                    if (iPlayListGetListener != null) {
                        if (backupItem != null) {
                            iPlayListGetListener.onDataGet(backupItem, null);
                        } else {
                            iPlayListGetListener.onDataGetError(new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_ITEM_NULL, -1);
                        }
                    }
                    return;
                }

                //mIPlayListControl.setCurPosition(playItem);
                if (iPlayListGetListener != null) {
                    iPlayListGetListener.onDataGet(playItem, playItemArrayList);
                }
            }

            @Override
            public void onDataGetError(PlayItem playItem, int errorCode, int errorExtra) {
                PlayerLogUtil.log(TAG, "getNextAsync", "get next error, trying backup");
                mIPlayControl.setInvalidPlayItem();
                mPlayerListenerHelper.notifyGetPlayListError(playItem, errorCode, errorExtra);

                // 尝试后备方案
                PlayItem backupItem = getBackupNextItem(originalPlayList, originalPosition);
                if (iPlayListGetListener != null) {
                    if (backupItem != null) {
                        PlayerLogUtil.log(TAG, "getNextAsync", "using backup item");
                        iPlayListGetListener.onDataGet(backupItem, null);
                    } else {
                        iPlayListGetListener.onDataGetError(playItem, errorCode, errorExtra);
                    }
                }
            }
        });
    }



    /**
     * 获取后备的下一首
     */
    private PlayItem getBackupNextItem(List<PlayItem> originalPlayList, int originalPosition) {
        try {
            if (originalPlayList != null && originalPosition >= 0 && originalPosition + 1 < originalPlayList.size()) {
                PlayerLogUtil.log(TAG, "getBackupNextItem", "使用后备方案，位置: " + (originalPosition + 1));
                return originalPlayList.get(originalPosition + 1);
            }
        } catch (Exception e) {
            PlayerLogUtil.log(TAG, "getBackupNextItem", "后备方案异常: " + e.getMessage());
        }
        return null;
    }


    /**
     * 异步获取上一首
     *
     * @param iPlayListGetListener 回调监听器
     */
    public void getPreAsync(IPlayListGetListener iPlayListGetListener) {
        PlayerLogUtil.log(TAG, "getPreAsync");
        if (getContext() == null) {
            PlayerLogUtil.log(TAG, "getPreAsync getContext is null");
            if (iPlayListGetListener != null) {
                iPlayListGetListener.onDataGetError(null, PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_ITEM_NULL, -1);
            }
            return;
        }
        if (!NetworkUtil.isNetworkAvailable(getContext())) {
            PlayerLogUtil.log(TAG, "getPreAsync is not Network Available");
            mPlayerListenerHelper.notifyPlayError(-1);
            if (iPlayListGetListener != null) {
                iPlayListGetListener.onDataGetError(null, PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_ITEM_NULL, -1);
            }
            return;
        }

        checkPlayListControl(mBuilder.getType());
        checkPlayControl();

        // 保存后备方案数据
        final int originalPosition = mIPlayListControl.getCurPosition();
        final List<PlayItem> originalPlayList = mIPlayListControl.getPlayList();

        mIPlayListControl.getPrePlayItem(new IPlayListGetListener() {
            @Override
            public void onDataGet(PlayItem playItem, List<PlayItem> playItemArrayList) {
                PlayerLogUtil.log(TAG, "getPreAsync.onDataGet", "success");
                if (PlayerPreconditions.checkNull(playItem)) {
                    PlayerLogUtil.log(TAG, "getPreAsync", "playItem is null, trying backup");
                    mPlayerListenerHelper.notifyGetPlayListError(new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_ITEM_NULL, -1);

                    // 尝试后备方案
                    PlayItem backupItem = getBackupPreItem(originalPlayList, originalPosition);
                    if (iPlayListGetListener != null) {
                        if (backupItem != null) {
                            iPlayListGetListener.onDataGet(backupItem, null);
                        } else {
                            iPlayListGetListener.onDataGetError(new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_ITEM_NULL, -1);
                        }
                    }
                    return;
                }

                //mIPlayListControl.setCurPosition(playItem);
                if (iPlayListGetListener != null) {
                    iPlayListGetListener.onDataGet(playItem, playItemArrayList);
                }
            }

            @Override
            public void onDataGetError(PlayItem playItem, int errorCode, int errorExtra) {
                PlayerLogUtil.log(TAG, "getPreAsync", "get pre error, trying backup");
                mPlayerListenerHelper.notifyGetPlayListError(playItem, errorCode, errorExtra);

                // 尝试后备方案
                PlayItem backupItem = getBackupPreItem(originalPlayList, originalPosition);
                if (iPlayListGetListener != null) {
                    if (backupItem != null) {
                        PlayerLogUtil.log(TAG, "getPreAsync", "using backup item");
                        iPlayListGetListener.onDataGet(backupItem, null);
                    } else {
                        iPlayListGetListener.onDataGetError(playItem, errorCode, errorExtra);
                    }
                }
            }
        });
    }

    /**
     * 获取后备的上一首
     */
    private PlayItem getBackupPreItem(List<PlayItem> originalPlayList, int originalPosition) {
        try {
            if (originalPlayList != null && originalPosition > 0 && originalPosition < originalPlayList.size()) {
                PlayerLogUtil.log(TAG, "getBackupPreItem", "使用后备方案，位置: " + (originalPosition - 1));
                return originalPlayList.get(originalPosition - 1);
            }
        } catch (Exception e) {
            PlayerLogUtil.log(TAG, "getBackupPreItem", "后备方案异常: " + e.getMessage());
        }
        return null;
    }



}
