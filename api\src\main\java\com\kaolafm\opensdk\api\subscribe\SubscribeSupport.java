package com.kaolafm.opensdk.api.subscribe;

import com.kaolafm.opensdk.http.core.HttpCallback;

/**
 * 订阅兼容类。
 * 为了兼容低版本弱账号体系，防止出现升级强账号后数据不一样导致用户体验不好。
 * 弱账号体系下，用户无论是否登录都会可以操作订阅历史相关的，并上报服务器。
 * 强账号体系下，只有在登录后才能进行订阅历史操作。
 * 兼容方案是在新版本首先拉取订阅和历史并保持到本地(只能拉取一次，服务端控制)，然后合并。
 *
 * <AUTHOR> Yan
 * @date 2020/8/10
 */
public class SubscribeSupport {

    /**
     * 订阅列表(All)
     *
     * @param pageNum  第几页
     * @param pageSize 每页数据个数
     * @param callback 回调结果
     */
    public void getSubscribeList(SubscribeService service) {

    }

    /**
     * 取消订阅
     *
     * @param id 专辑、PGC、广播的id
     */
    public void unsubscribe(long id, HttpCallback<Boolean> callback) {

    }

    /**
     * 是否订阅
     *
     * @param id       专辑、PGC、广播的id
     * @param callback true 已订阅；false 未订阅
     */
    public void isSubscribed(SubscribeService service) {

    }

    public void getUserFollowRadio() {

    }
}
