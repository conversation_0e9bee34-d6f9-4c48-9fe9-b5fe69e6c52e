package com.kaolafm.opensdk.api.purchase.model;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;

/**
 * vip套餐信息
 */
@Entity
public class VipMeals {

    /* 套餐id */
    private Long mealId;

    /* 套餐名称 */
    private String mealName;

    /* 套餐原价 */
    private Long originPrice;

    /* 套餐折扣价 */
    private Long discountPrice;

    /* Vip天数 */
    private Long vipDays;

    /* 商品说明 */
    private String description;

    /* 折扣说明 */
    private String discountDes;

    /* vip展示标签 */
    private String vipLableDisplay;

    @Generated(hash = 1542559156)
    public VipMeals(Long mealId, String mealName, Long originPrice, Long discountPrice, Long vipDays, String description, String discountDes,
            String vipLableDisplay) {
        this.mealId = mealId;
        this.mealName = mealName;
        this.originPrice = originPrice;
        this.discountPrice = discountPrice;
        this.vipDays = vipDays;
        this.description = description;
        this.discountDes = discountDes;
        this.vipLableDisplay = vipLableDisplay;
    }

    public VipMeals() {
    }

    public Long getMealId() {
        return mealId;
    }

    public void setMealId(Long mealId) {
        this.mealId = mealId;
    }

    public String getMealName() {
        return mealName;
    }

    public void setMealName(String mealName) {
        this.mealName = mealName;
    }

    public Long getOriginPrice() {
        return originPrice;
    }

    public void setOriginPrice(Long originPrice) {
        this.originPrice = originPrice;
    }

    public Long getDiscountPrice() {
        return discountPrice;
    }

    public void setDiscountPrice(Long discountPrice) {
        this.discountPrice = discountPrice;
    }

    public Long getVipDays() {
        return vipDays;
    }

    public void setVipDays(Long vipDays) {
        this.vipDays = vipDays;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDiscountDes() {
        return discountDes;
    }

    public void setDiscountDes(String discountDes) {
        this.discountDes = discountDes;
    }

    public String getVipLableDisplay() {
        return vipLableDisplay;
    }

    public void setVipLableDisplay(String vipLableDisplay) {
        this.vipLableDisplay = vipLableDisplay;
    }

    @Override
    public String toString() {
        return "VipMeats{" +
                "mealId=" + mealId +
                ", mealName='" + mealName + '\'' +
                ", originPrice='" + originPrice + '\'' +
                ", discountPrice='" + discountPrice + '\'' +
                ", vipDays=" + vipDays +
                ", description='" + description + '\'' +
                ", discountDes='" + discountDes + '\'' +
                '}';
    }
}
