package com.kaolafm.opensdk.utils.operation;

import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.operation.model.category.AlbumCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.CategoryMember;
import com.kaolafm.opensdk.api.operation.model.column.AlbumDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;

import javax.inject.Inject;

/**
 * 专辑 分类/栏目成员处理类
 *
 * <AUTHOR>
 * @date 2018/9/21
 */

public class AlbumProcessor implements IOperationProcessor {

    @Inject
    public AlbumProcessor() {
    }

    @Override
    public boolean accept(CategoryMember member) {
        return member instanceof AlbumCategoryMember;
    }

    @Override
    public boolean accept(ColumnMember member) {
        return member instanceof AlbumDetailColumnMember;
    }

    @Override
    public long getId(CategoryMember member) {
        return ((AlbumCategoryMember) member).getAlbumId();
    }

    @Override
    public long getId(ColumnMember member) {
        return ((AlbumDetailColumnMember) member).getAlbumId();
    }

    @Override
    public long getListenNum(CategoryMember member) {
        return ((AlbumCategoryMember) member).getPlayTimes();
    }

    @Override
    public int getType(CategoryMember member) {
        return ResType.TYPE_ALBUM;
    }

    @Override
    public int getType(ColumnMember member) {
        return ResType.TYPE_ALBUM;
    }

    @Override
    public void play(CategoryMember member) {
//        PlayerManager.getInstance(OpenSDK.getInstance().getContext()).playAlbum(getId(member));
    }

    @Override
    public void play(ColumnMember member) {
//        PlayerManager.getInstance(OpenSDK.getInstance().getContext()).playAlbum(getId(member));
    }
}
