package com.kaolafm.report.util;

import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.api.carinfo.CarInfoRequest;
import com.kaolafm.report.api.carinfo.model.CarInfoData;
import com.kaolafm.report.model.ReportCarParameter;
import com.kaolafm.report.model.ReportParameter;

/**
 * <AUTHOR> on 2019/2/26.
 */

public class InitUtil {

    public static void initCarInfo() {
        if (isNeedUpdateReportInfo(DateUtil.getServerTime())) {
            new CarInfoRequest().getReportInfo(new HttpCallback<CarInfoData>() {
                @Override
                public void onSuccess(CarInfoData reportInfoData) {
                    if (reportInfoData != null) {
                        CarInfoData.CarInfoBean carInfoBean = reportInfoData.getCarInfo();
                        CarInfoData.CarConfigBean carConfigBean = reportInfoData.getCarConfig();
                        if (carConfigBean == null || carInfoBean == null) {
                            return;
                        }
                        ReportCarParameter reportCarParameter = new ReportCarParameter();
                        reportCarParameter.setAppIdType(carInfoBean.getAppIdType());
                        reportCarParameter.setCarBrand(carInfoBean.getCarBrand());
                        reportCarParameter.setFirstAppId(carInfoBean.getFirstAppId());
                        reportCarParameter.setFirstAppIdName(carInfoBean.getFirstAppIdName());
                        reportCarParameter.setMarketType(carInfoBean.getMarketType());
                        reportCarParameter.setDeveloper(carInfoBean.getDeveloper());
                        reportCarParameter.setOem(carInfoBean.getOem());
                        reportCarParameter.setTimer(carConfigBean.getReportInterval());

                        reportCarParameter.setSystemTime(DateUtil.getServerTime());
                        ReportHelper.getInstance().setCarParameter(reportCarParameter);
                    }
                }

                @Override
                public void onError(ApiException exception) {
                }
            });
        }
    }

    public static ReportParameter init(String deviceId, String appId, String openId, String userId, String libVersion, String channel) {
        ReportParameter parameter = new ReportParameter();
        parameter.setAppid(appId);
        parameter.setOpenid(openId);
        parameter.setUid(userId);
        parameter.setLib_version(libVersion);
        parameter.setDeviceId(deviceId);
        parameter.setChannel(channel);
        return parameter;
    }

    /**
     * 是否需要更新上报信息
     *
     * @return
     */
    private static boolean isNeedUpdateReportInfo(long systemTime) {
        ReportParameterManager reportParameterManager = ReportParameterManager.getInstance();
        if (reportParameterManager.getReportCarParameter() == null) {
            return true;
        }
        if (reportParameterManager.getReportCarParameter().getSystemTime() == 0) {
            return true;
        }
        long second = (systemTime - reportParameterManager.getReportCarParameter().getSystemTime()) / 1000;
        long days = second / (60 * 60 * 24);
        return days >= ReportConstants.UPDATE_REPORT_INFO_MAX_DAY;
    }

}
