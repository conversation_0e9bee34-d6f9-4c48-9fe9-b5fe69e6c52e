package com.kaolafm.ad.expose;

import com.kaolafm.ad.AdConstant;
import com.kaolafm.ad.api.internal.AdInternalRequest;
import com.kaolafm.ad.api.model.Advert;
import com.kaolafm.ad.api.model.AdvertisingDetails;
import com.kaolafm.ad.api.model.AttachImage;
import com.kaolafm.ad.api.model.ImageAdvert;
import com.kaolafm.ad.api.model.InteractionAdvert;
import com.kaolafm.ad.db.manager.AdvertDBManager;
import com.kaolafm.ad.util.AdBeanUtil;
import com.kaolafm.ad.util.DownloadUtil;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.List;

import javax.inject.Inject;

/**
 * 广告仓库。
 *
 * <AUTHOR>
 * @date 2020-02-05
 */
@AppScope
public class AdvertRepository {

    @Inject
    AdInternalRequest mRequest;

    @Inject
    @AppScope
    AdvertDBManager mAdvertDBManager;

    @Inject
    AdvertRepository() {
    }

    /**
     * 根据广告位获取该广告位的一个预加载的广告信息。
     * 该接口会查询本地数据库中是否有该广告位的广告，有返回{@link Advert}, 没有返回null。
     * 并且会请求接口获取下一次要显示的广告并覆盖数据中该广告位的上一个广告。
     *
     * @param zoneId
     * @param picWidth
     * @param picHeight
     * @param acceptedAdTypes
     * @param callback
     */
    void getPreloadingAdvert(String zoneId,
                             int subtype,
                             String picWidth,
                             String picHeight,
                             String acceptedAdTypes,
                             String advancedAttrs,
                             HttpCallback<Advert> callback) {
        loadAdvert(zoneId, callback);

        preloadAdvert(zoneId, subtype, picWidth, picHeight, acceptedAdTypes, advancedAttrs);
    }

    /**
     * 预加载广告。
     * 网络请求获取下次广告详情并下载对应的图片音频资源，然后更新数据库。
     *
     * @param zoneId
     * @param picWidth
     * @param picHeight
     * @param acceptedAdTypes
     */
    private void preloadAdvert(String zoneId, int subtype, String picWidth, String picHeight, String acceptedAdTypes, String advancedAttrs) {
        mRequest.getAdvertisingList(zoneId, picWidth, picHeight, acceptedAdTypes, advancedAttrs, new HttpCallback<List<AdvertisingDetails>>() {
            @Override
            public void onSuccess(List<AdvertisingDetails> detailsList) {
                if (!ListUtil.isEmpty(detailsList)) {
                    AdvertisingDetails details = detailsList.get(0);
                    details.setSubtype(subtype);
                    DownloadUtil.download(details);
                    mAdvertDBManager.updateByZoneId(details);
                }
            }

            @Override
            public void onError(ApiException exception) {

            }
        });
    }

    /**
     * 根据广告位加载已经下载的广告
     *
     * @param zoneId
     * @param callback
     */
    private void loadAdvert(String zoneId, HttpCallback<Advert> callback) {
        mAdvertDBManager.queryByZoneId(zoneId, entities -> {
            if (callback != null) {
                if (!ListUtil.isEmpty(entities)) {
                    Advert advert = AdBeanUtil.transform(entities.get(0));
                    if (advert != null) {
                        callback.onSuccess(advert);
                        return;
                    }
                }
                callback.onError(new ApiException("没有该广告位的广告"));
            }
        });
    }

    /**
     * 请求广告并曝光广告
     *
     * @param adZoneId        必填 广告位ID
     * @param picWidth        必填 图片广告位的宽 可传多个 以逗号分隔 在请求包含图片信息的广告位时为必填。
     * @param picHeight       必填 图片广告位的高 可传多个 以逗号分隔 在请求包含图片信息的广告位时为必填。
     *                        对于图片分辨率参数的说明:
     *                        如需要1280×960和1920×1080的图片时 c_picWidth应传"1280,1920" c_picHeight应传"960,1080"
     * @param acceptedAdTypes 必填 多种类型逗号隔开。可接受广告类型，客户端（非SDK）必传。3音频广告（新）；4图片广告（新）；5音图广告（新）
     * @param advancedAttrs   选填 json格式，需要编码。用于精准投放广告或广告服务判断投放广告时机等。
     *                        通过
     *                        <p>
     *                        {@link com.kaolafm.ad.util.AdParamsUtil#getAdvanceAttrs(long, long, long, long, int)}、
     *                        <p>
     *                        {@link com.kaolafm.ad.util.AdParamsUtil#getAlbumAttrs(long, long, int)}、
     *                        <p>
     *                        {@link com.kaolafm.ad.util.AdParamsUtil#getRadioAttrs(long, long, int)}
     *                        获取就是已经处理好的，直接填入就可以了。
     */
    public void getAdvertisingList(String adZoneId,
                                   String picWidth,
                                   String picHeight,
                                   String acceptedAdTypes,
                                   String advancedAttrs,
                                   HttpCallback<List<Advert>> callback) {

        mRequest.getAdvertList(adZoneId, picWidth, picHeight, acceptedAdTypes, advancedAttrs, callback);
    }

    /**
     * 删除指定广告的缓存
     * @param advert
     */
    public void delete(Advert advert) {
        //定时广告不删除
        if (advert != null && advert.getSubtype() != AdConstant.TYPE_TIMED_ADVERT) {
            mAdvertDBManager.deleteBySessionId(advert.getSessionId());
            String sessionId = advert.getSessionId();
            delete(sessionId, advert.getUrl(), advert.getLocalPath());
            if (advert instanceof ImageAdvert) {
                AttachImage attachImage = ((ImageAdvert) advert).getAttachImage();
                if (attachImage != null) {
                    delete(sessionId, attachImage.getUrl(), attachImage.getLocalPath());
                }
            }
            if (advert instanceof InteractionAdvert) {
                delete(sessionId, ((InteractionAdvert) advert).getDestUrl(), ((InteractionAdvert) advert).getLocalImagePath());
            }
        }
    }

    /**
     * 删除指定地址的缓存
     * @param sessionId
     * @param url
     * @param localPath
     */
    public void delete(String sessionId, String url, String localPath) {
        DownloadUtil.deleteSPAndFile(sessionId, url, localPath);
    }
}
