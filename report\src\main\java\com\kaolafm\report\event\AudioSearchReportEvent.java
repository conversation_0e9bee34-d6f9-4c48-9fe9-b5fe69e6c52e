package com.kaolafm.report.event;


import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> on 2019/1/21.
 * 语音搜索
 */

public class AudioSearchReportEvent extends BaseReportEventBean {
    public static final String RESULT_TIME_OUT = "0";
    public static final String RESULT_ERROR = "1";
    public static final String RESULT_EMPTY = "2";
    public static final String RESULT_SUCCESS = "3";

    /**
     * 原始文本	 用户原始文本，比如 ‘我要听刘德华的冰雨
     */
    private String text;
    /**
     * 搜索返回情况	0：超时；1：参数有误；2：无结果；3：正常
     */
    private String result;
    /**
     * 直接播放类型	0：否，1：是
     */
    private String playtype;
    /**
     * 搜索词	语义理解的结果，比如‘刘德华，冰雨’
     */
    private String remarks1;

    /**
     * 搜索结果追踪号	搜索服务端透传的数据
     */
    private String remarks9;

    public AudioSearchReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_AUDIO_SEARCH);
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getPlaytype() {
        return playtype;
    }

    public void setPlaytype(String playtype) {
        this.playtype = playtype;
    }

    public String getRemarks1() {
        return remarks1;
    }

    public void setRemarks1(String remarks1) {
        this.remarks1 = remarks1;
    }

    public String getRemarks9() {
        return remarks9;
    }

    public void setRemarks9(String remarks9) {
        this.remarks9 = remarks9;
    }
}
