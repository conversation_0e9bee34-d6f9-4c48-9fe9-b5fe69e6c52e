package com.kaolafm.opensdk.demo.purchase;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import com.google.gson.GsonBuilder;
import com.kaolafm.opensdk.api.purchase.PurchaseRequest;
import com.kaolafm.opensdk.api.purchase.model.PurchaseSucess;
import com.kaolafm.opensdk.api.purchase.model.QRCodeInfo;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 通用
 *
 */
public class QRCodeStatusActivity extends BaseActivity {
    public static final String KEY_QRCODE_ID = "qrCodeId";

    @BindView(R.id.et_qrcode_id)
    EditText qrCodeIdEt;

    @BindView(R.id.tv_qr_status_history)
    TextView qrStatusHistoryTv;

    @Override
    public int getLayoutId() {
        return R.layout.activity_qr_code_status_check;
    }

    @Override
    public void initArgs() {
        super.initArgs();
        Intent intent = getIntent();
        if(intent != null){
            String qrCodeId = intent.getStringExtra(KEY_QRCODE_ID);
            if(!TextUtils.isEmpty(qrCodeId)){
                qrCodeIdEt.setText(intent.getStringExtra(KEY_QRCODE_ID));
            }
        }
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        setTitle("二维码状态");
    }

    @Override
    public void initData() {

    }

    @OnClick({R.id.btn_qr_status_get})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.btn_qr_status_get:
                getQrCodeStatus();
                break;
        }
    }

    private void getQrCodeStatus(){
        new PurchaseRequest().qrCodeStatus(qrCodeIdEt.getText().toString().trim(),  new HttpCallback<PurchaseSucess>() {
            @Override
            public void onSuccess(PurchaseSucess status) {
                String infoStr = new GsonBuilder()
                        .setPrettyPrinting()
                        .serializeNulls()
                        .create().toJson(status);
                qrStatusHistoryTv.setText(infoStr);
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取失败", exception);
            }
        });
    }
}
