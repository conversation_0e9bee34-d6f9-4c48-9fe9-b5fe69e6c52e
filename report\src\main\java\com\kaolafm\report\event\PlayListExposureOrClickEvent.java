package com.kaolafm.report.event;

import com.kaolafm.report.util.ReportConstants;

import static com.kaolafm.report.util.ReportConstants.CONTROL_TYPE_SCREEN;

public class PlayListExposureOrClickEvent extends BaseReportEventBean {
    /**
     * 曝光或点击
     */
    public static final String MODE_CLICK = "click";    //点击
    public static final String MODE_EXPOSURE = "exposure";      //曝光

    private String mode = MODE_CLICK;
    private String pageId;  //所在页面的编码id
    private String controltype = CONTROL_TYPE_SCREEN;   //交互方式 1. 屏幕 2. 语音 3. 方控 4. 其他
    private int order;     //在列表中的序号
    private String radioid;     //如果按钮有内容对象，则上报对象的合集id
    private String audioid;     //如果按钮有内容对象，则上报对象的单曲
    /**
     * 播放详情页
     * 专辑id
     * 专题id
     * 智能电台id
     * 听广播详情页上报传统广播id
     * 听电视详情页上报电台id
     * 直播间上报直播间id
     */
    private String contentid;       //详情页的合集id

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getPageId() {
        return pageId;
    }

    public void setPageId(String pageId) {
        this.pageId = pageId;
    }

    public String getControltype() {
        return controltype;
    }

    public void setControltype(String controltype) {
        this.controltype = controltype;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public String getRadioid() {
        return radioid;
    }

    public void setRadioid(String radioid) {
        this.radioid = radioid;
    }

    public String getAudioid() {
        return audioid;
    }

    public void setAudioid(String audioid) {
        this.audioid = audioid;
    }

    public String getContentid() {
        return contentid;
    }

    public void setContentid(String contentid) {
        this.contentid = contentid;
    }

    public PlayListExposureOrClickEvent() {
        setEventcode(ReportConstants.EVENT_ID_PLAYLIST_EXPOSURE_CLICK);
    }

    public PlayListExposureOrClickEvent(String mode, String pageId, String controltype, int order, String radioid, String audioid, String contentid) {
        setEventcode(ReportConstants.EVENT_ID_PLAYLIST_EXPOSURE_CLICK);
        this.mode = mode;
        this.pageId = pageId;
        this.controltype = controltype;
        this.order = order;
        this.radioid = radioid;
        this.audioid = audioid;
        this.contentid = contentid;
    }
}
