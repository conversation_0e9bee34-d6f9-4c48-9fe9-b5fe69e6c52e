package com.kaolafm.opensdk.demo.player;

import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.media.RadioRequest;
import com.kaolafm.opensdk.api.media.model.AIAudioDetails;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.api.media.model.RadioDetails;
import com.kaolafm.opensdk.api.subscribe.SubscribeRequest;
import com.kaolafm.opensdk.demo.detail.StringAdapter.Item;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener;
import com.kaolafm.opensdk.player.logic.model.item.RadioPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.ArrayList;
import java.util.List;

/**
 * AI电台播放器页面。
 * <AUTHOR> Yan
 * @date 2018/12/6
 */

public class RadioPlayerGetListByAreaActivity extends BasePlayerActivity {

    /**
     * 是否有下一页。
     */
    private boolean hasNextPage = true;

    private String mClockId = "0";

    @Override
    public void initView(Bundle savedInstanceState) {
        super.initView(savedInstanceState);
        setTitle("AI电台(地区)播放器页面");
        //添加播放状态监听
        PlayerManager.getInstance().addPlayControlStateCallback(mPlayerStateListener);
        PlayerManager.getInstance().addPlayListControlStateCallback(mPlayListStateListener);
    }

    @Override
    public void initData() {
        getRadioDetails();
        getPlaylistRadio(false);
        // 1.6.0暂时没有提供带区域的电台播单的播放逻辑
//        PlayerManager.getInstance().start(new PlayerBuilder().setId(String.valueOf(mId)).setType(PlayerConstants.RESOURCES_TYPE_RADIO));
        getSubscribeState();
    }

    /**
     * 获取该电台订阅状态
     */
    private void getSubscribeState() {
        new SubscribeRequest().isSubscribed(mId, new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                isSubscribed = aBoolean;
                btnSubscribe.setText(aBoolean ? "取消订阅" : "订阅");
                btnSubscribe.setEnabled(true);
                btnSubscribe.setVisibility(View.VISIBLE);
            }

            @Override
            public void onError(ApiException exception) {
                btnSubscribe.setText(exception.getMessage());
            }
        });
    }

    /**
     * 获取播单列表。
     * 如果PlayItem里面的数据已经满足页面暂时需求，
     * 能保证一致性，就不需要再手动将数据添加到播单中了，拉取更多播单{@link }
     *
     * @param isLoadMore true表示是加载更多
     */
    private void getPlaylistRadio(boolean isLoadMore) {
        play2(isLoadMore);
    }

    private void play2(boolean isLoadMore) {
        if (isLoadMore && !hasNextPage) {
            showToast(isLoadMore ? "没有更多了" : "列表为空");
            return;
        }
        if(TextUtils.isEmpty(mAreaCode) && TextUtils.isEmpty(mAreaCityName)){
            new RadioRequest().getPlaylist(mId, mClockId, new HttpCallback<List<AIAudioDetails>>() {
                @Override
                public void onSuccess(List<AIAudioDetails> audioDetails) {
                    showRadioSucess(audioDetails, isLoadMore);
                }

                @Override
                public void onError(ApiException exception) {
                    showRadioError(exception);
                }
            });
        }else{
            new RadioRequest().getPlaylist(mId, mClockId, mAreaCode, mAreaCityName, new HttpCallback<List<AIAudioDetails>>() {
                @Override
                public void onSuccess(List<AIAudioDetails> audioDetails) {
                    showRadioSucess(audioDetails, isLoadMore);
                }

                @Override
                public void onError(ApiException exception) {
                    showRadioError(exception);
                }
            });
        }

    }

    private void showRadioError(ApiException exception) {
        if (mTrfDetailPlaylist != null) {
            mTrfDetailPlaylist.finishLoadmore();
        }
        showToast("获取播单错误，错误码="+exception.getCode()+", 错误信息="+exception.getMessage());
    }

    private void showRadioSucess(List<AIAudioDetails> audioDetails, boolean isLoadMore) {
        if (!ListUtil.isEmpty(audioDetails)) {
            List<Item> datas = new ArrayList<>();
            ArrayList<PlayItem> playItemList = new ArrayList<>();
            mClockId = audioDetails.get(0).getClockId();
            //等于1表示还有更多，等于0表示没有了
            hasNextPage = audioDetails.get(0).getHasNextPage() == 1;
            Log.e("RadioPlayerActivity", "onSuccess: mClockId="+mClockId);
            for (int i = 0; i < audioDetails.size(); i++) {
                AudioDetails item = audioDetails.get(i);
                Item sai = new Item();
                sai.id = item.getAudioId();
                sai.type = ResType.TYPE_RADIO;
                sai.title = item.getAudioName();
                sai.details = item.getAlbumName();
                sai.item = item;
                playItemList.add(translateToPlayItem(item));
                datas.add(sai);
            }
            if (isLoadMore) {
                if (mAdapter != null) {
                    mAdapter.addDataList(datas);
                }
                if (mTrfDetailPlaylist != null) {
                    mTrfDetailPlaylist.finishLoadmore();
                }
            } else {
                if (mAdapter != null) {
                    mAdapter.setDataList(datas);
                }
            }

        } else {
            showToast(isLoadMore ? "没有更多了" : "列表为空");
        }
    }

    /**
     * 获取电台详情
     */
    private void getRadioDetails() {
        new RadioRequest().getRadioDetails(mId, new HttpCallback<RadioDetails>() {
            @Override
            public void onSuccess(RadioDetails radioDetails) {
                showDetail(radioDetails, radioDetails.getImg());
            }

            @Override
            public void onError(ApiException exception) {
                showDetail(exception, "");
            }
        });
    }

    @Override
    protected void playPre() {
        PlayerManager.getInstance().playPre();
    }

    @Override
    protected void switchPlayPause() {
        PlayerManager.getInstance().switchPlayerStatus();
    }

    @Override
    protected void playNext() {
        PlayerManager.getInstance().playNext();
    }

    @Override
    protected void refresh() {

    }

    @Override
    protected void loadMore() {
        getPlaylistRadio(true);
    }

    @Override
    protected void playItem(Item item) {
        // 1.6.0暂时没有提供带区域的电台播单的播放逻辑
        //根据ID从播单中获取PlayItem。由于前面获取播单是自己手动请求网络获取的，所以有可能为空，可能出现和播放器中的播单不一致
//        PlayerManager.getInstance().getPlayItemFromAudioId(item.id, new PlayerManager.GetPlayItemListener() {
//            @Override
//            public void success(PlayItem playitem) {
//                if (playitem != null) {
//                    mPlayerManager.start(new PlayerBuilder().setId(String.valueOf(playitem.getAudioId())).setType(PlayerConstants.RESOURCES_TYPE_RADIO));
//                }//如果播放器里面的播单没有该数据就直播播放单个单曲。注意：调用该方法是播放单曲所在的专辑，不是该电台。
//                else {
//                    mPlayerManager.start(new PlayerBuilder().setId(String.valueOf(item.id)).setType(PlayerConstants.RESOURCES_TYPE_RADIO));
//                }
//            }
//
//            @Override
//            public void error(ApiException exception) {
//
//            }
//        });
    }

    @Override
    protected void seek(int progress) {
        PlayerManager.getInstance().seek(progress);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        PlayerManager.getInstance().removePlayControlStateCallback(mPlayerStateListener);
        PlayerManager.getInstance().removePlayListControlStateCallback(mPlayListStateListener);
    }

    private IPlayListStateListener mPlayListStateListener = new IPlayListStateListener() {
        @Override
        public void onPlayListChange(List<PlayItem> list) {
            showToast("电台播单发生变化");
        }

        @Override
        public void onPlayListChangeError(PlayItem playItem, int i, int i1) {

        }
    };

    public static PlayItem translateToPlayItem(AudioDetails audioDetails) {
        if (audioDetails == null) {
            return null;
        }
        RadioPlayItem playItem = new RadioPlayItem();
        playItem.setAudioId(audioDetails.getAudioId());
        playItem.getInfoData().setTitle(audioDetails.getAudioName());
        String aacPlayUrl = audioDetails.getAacPlayUrl();
        playItem.setDuration(audioDetails.getOriginalDuration());
        playItem.setPlayUrl(aacPlayUrl);

        // 增加购买方式及购买信息
        playItem.setPayMethod(audioDetails.getPayMethod());

        //增加audioDetails中携带的购买类型（buyType）和购买状态信息(buyState)
        playItem.setBuyStatus(audioDetails.getBuyStatus());
        playItem.setBuyType(audioDetails.getBuyType());

        //增加playUrlId供播放时通过id获取播放地址
        playItem.setPlayUrlId(audioDetails.getPlayUrlId());

        playItem.getInfoData().setAudioDes(audioDetails.getAudioDes());
        playItem.getInfoData().setAlbumId(audioDetails.getAlbumId());
        playItem.getInfoData().setAlbumPic(audioDetails.getAlbumPic());
        playItem.getInfoData().setAlbumName(audioDetails.getAlbumName());
        playItem.getInfoData().setAudioPic(audioDetails.getAudioPic());
        playItem.getInfoData().setOrderNum(audioDetails.getOrderNum());
        playItem.getInfoData().setUpdateTime(String.valueOf(audioDetails.getUpdateTime()));
        playItem.getRadioInfoData().setClockId(audioDetails.getClockId());
        playItem.getRadioInfoData().setCategoryId(audioDetails.getCategoryId());
        playItem.getInfoData().setIcon(audioDetails.getIcon());
        playItem.getInfoData().setSourceLogo(audioDetails.getSourceLogo());
        playItem.getInfoData().setSourceName(audioDetails.getSourceName());
        playItem.getRadioInfoData().setMainTitleName(audioDetails.getMainTitleName());
        playItem.getRadioInfoData().setRadioSubTag(audioDetails.getContentTypeName());
        playItem.getRadioInfoData().setRadioSubTagType(audioDetails.getContentType());
        playItem.getRadioInfoData().setSubheadName(audioDetails.getSubheadName());
        playItem.getRadioInfoData().setCallBack(audioDetails.getCallBack());
        playItem.getRadioInfoData().setSource(audioDetails.getSource());

        playItem.setPlayInfoList(audioDetails.getPlayInfoList());
        return playItem;
    }
}
