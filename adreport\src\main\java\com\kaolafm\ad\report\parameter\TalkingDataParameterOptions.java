package com.kaolafm.ad.report.parameter;

public class TalkingDataParameterOptions extends ParameterOptions {

    private String motocity;
    private String location;
    private String mototype;
    private String motostate;
    private String screentype;
    private String vin;

    public TalkingDataParameterOptions(){
        this(new Builder());
    }

    TalkingDataParameterOptions(Builder builder){
        this.motocity = builder.motocity;
        this.location = builder.location;
        this.mototype = builder.mototype;
        this.motostate = builder.motostate;
        this.screentype = builder.screentype;
        this.vin = builder.vin;
    }

    public String getMotocity() {
        return motocity;
    }

    public String getLocation() {
        return location;
    }

    public String getMototype() {
        return mototype;
    }

    public String getMotostate() {
        return motostate;
    }

    public String getScreentype() {
        return screentype;
    }

    public String getVin() {
        return vin;
    }

    public static final class Builder{
        String motocity;
        String location;
        String mototype;
        String motostate;
        String screentype;
        String vin;

        public Builder(){
//            motocity = "__MOCI__";
//            location = "__LOCA__";
//            mototype = "__MOTY__";
//            motostate = "__MOST__";
//            screentype = "__SCRTY__";
//            vin = "__VIN__";
        }

        public Builder motocity(String motocity){
            this.motocity = motocity;
            return this;
        }

        public Builder location(String location){
            this.location = location;
            return this;
        }

        public Builder mototype(String mototype){
            this.mototype = mototype;
            return this;
        }

        public Builder motostate(String motostate){
            this.motostate = motostate;
            return this;
        }

        public Builder screentype(String screentype){
            this.screentype = screentype;
            return this;
        }

        public Builder vin(String vin){
            this.vin = vin;
            return this;
        }

        public TalkingDataParameterOptions build(){
            return new TalkingDataParameterOptions(this);
        }
    }
}
