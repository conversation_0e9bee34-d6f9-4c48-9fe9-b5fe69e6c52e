package com.kaolafm.opensdk.http.core;

import com.google.gson.annotations.SerializedName;

/**
 * 网络请求错误基类，主要处理返回的code和message信息
 *
 * <AUTHOR>
 * @date 2018/6/8
 */

public class Response {

    @SerializedName(value = "errCode", alternate = {"ret", "status", "errcode", "code"})
    private int code;

    @SerializedName(value = "errMsg", alternate = {"msg", "errmsg", "message"})
    private String message;

    @SerializedName(value = "success")
    private boolean success;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    @Override
    public String toString() {
        return "Response{" +
                "code=" + code +
                ", message='" + message + '\'' +
                '}';
    }
}
