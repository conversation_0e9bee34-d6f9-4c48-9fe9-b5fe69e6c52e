package com.kaolafm.report.event;

import com.kaolafm.report.util.ReportConstants;

/**
 * @Package: com.kaolafm.report.event
 * @Description: 购买专辑
 * @Author: Maclay
 * @Date: 14:24
 */
public class BuyAlbumEvent extends BaseReportEventBean {
    private String albumid;
    private String remarks1;
    private String tag;

    public BuyAlbumEvent() {
        setEventcode(ReportConstants.EVENT_ID_BUY_ALBUM);
    }

    public String getAlbumid() {
        return albumid;
    }

    public void setAlbumid(String albumid) {
        this.albumid = albumid;
    }

    public String getRemarks1() {
        return remarks1;
    }

    public void setRemarks1(String remarks1) {
        this.remarks1 = remarks1;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    @Override
    public String toString() {
        return "BuyAlbumEvent{" +
                "albumid='" + albumid + '\'' +
                ", remarks1='" + remarks1 + '\'' +
                ", tag='" + tag + '\'' +
                '}';
    }
}
