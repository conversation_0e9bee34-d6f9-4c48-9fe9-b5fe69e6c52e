<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="8dp">

    <ImageView
        android:id="@+id/iv_history_item_icon"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:scaleType="centerCrop"
        tools:layout_editor_absoluteX="8dp"
        tools:layout_editor_absoluteY="8dp" />

    <TextView
        android:visibility="gone"
        tools:visibility="visible"
        android:padding="5dp"
        tools:text="VIP"
        android:id="@+id/item_tag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/color_black_50_transparent"
        android:textColor="#fff"
        app:layout_constraintStart_toStartOf="@+id/iv_history_item_icon"
        app:layout_constraintTop_toTopOf="@+id/iv_history_item_icon" />

    <TextView
        android:id="@+id/tv_history_item_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingLeft="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/iv_history_item_icon"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_history_item_listen"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingLeft="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/iv_history_item_icon"
        app:layout_constraintTop_toBottomOf="@+id/tv_history_item_title" />
</androidx.constraintlayout.widget.ConstraintLayout>