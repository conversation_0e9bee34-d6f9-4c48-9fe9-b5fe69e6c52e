package com.kaolafm.base.internal;

import android.content.Context;

import java.util.ArrayList;
import java.util.List;

/**
 * 虽然命名是工厂类，但其实是责任链模式完成的获取uuid
 * <AUTHOR>
 * @date 2019-06-17
 */
public class DeviceIdFactory {

    private static List<ObtainDeviceId> mList = new ArrayList<>();

    private static DeviceIdFactory mInstance;

    public static DeviceIdFactory getInstance() {
        if (mInstance == null) {
            synchronized (DeviceIdFactory.class) {
                if (mInstance == null) {
                    mInstance = new DeviceIdFactory();
                }
            }
        }
        return mInstance;
    }

    private DeviceIdFactory() {
        //顺序不能变。
        mList.add(new CacheObtainDeviceId());
        mList.add(new AndroidIdObtainDeviceId());
        mList.add(new RandomObtainDeviceId());
        mList.add(new DefaultObtainDeviceId());
    }

    public String getDeviceId(Context context) {
        String uuid = null;
        int index = 0;
        ObtainDeviceId obtainDeviceId = mList.get(index);
        obtainDeviceId.setNextChain(uuid, mList, index + 1);
        return obtainDeviceId.getUUID(context);
    }

    public void add(ObtainDeviceId obtainUUID) {
        mList.add(obtainUUID);
    }

    public void add(int index, ObtainDeviceId obtainDeviceId) {
        mList.add(index, obtainDeviceId);
    }

    public void set(int index, ObtainDeviceId obtainDeviceId) {
        mList.set(index, obtainDeviceId);
    }

    public void remove(int index) {
        mList.remove(index);
    }
}
