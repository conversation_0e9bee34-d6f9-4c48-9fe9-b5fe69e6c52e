package com.kaolafm.opensdk.demo.purchase;

import android.os.Bundle;

import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.purchase.PurchaseRequest;
import com.kaolafm.opensdk.api.purchase.model.Order;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.ArrayList;
import java.util.List;

/**
 * vip套餐
 *
 */
public class OrderActivity extends CommonActivity {

    @Override
    public void initView(Bundle savedInstanceState) {
        super.initView(savedInstanceState);
        setTitle("订单列表");
    }

    @Override
    public void initData() {
        new PurchaseRequest().getOrderList(1, 10, new HttpCallback<BasePageResult<List<Order>>>() {
            @Override
            public void onSuccess(BasePageResult<List<Order>> result) {
                if (adapter != null) {
                    List<Object> list = new ArrayList<>();
                    for (Order i : result.getDataList()) {
                        list.add(i);
                    }
                    adapter.setDataList(list);
                }
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取v列表失败", exception);
            }
        });
    }
}
