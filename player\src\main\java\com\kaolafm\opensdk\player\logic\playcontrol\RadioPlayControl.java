package com.kaolafm.opensdk.player.logic.playcontrol;

import com.kaolafm.opensdk.api.media.model.AudioFileInfo;
import com.kaolafm.opensdk.api.media.model.ToneQuality;
import com.kaolafm.opensdk.player.logic.model.item.RadioPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.List;

/**
 * 电台播放控制
 * <AUTHOR>
 */
public class RadioPlayControl extends BasePlayControl {

    @Override
    ToneQuality getToneQuality(PlayItem playItem, List<AudioFileInfo> playListUrlInfos) {
        return null;
    }

    @Override
    void requestPlayUrl(PlayItem playItem, OnGetPlayUrlData callback) {
        RadioPlayItem radioPlayItem = (RadioPlayItem) playItem;
        setPlayUrl(playItem, radioPlayItem.getPlayInfoList());
        callback.onDataGet(radioPlayItem.getPlayUrl());
    }

    @Override
    String getPlayItemResourceType(PlayItem playItem) {
        return "m3u8";
    }
}
