apply plugin: 'com.android.application'
apply plugin: 'android-aspectjx'

def config = rootProject.ext.config
def lastedReleaseMavenVersionName = rootProject.ext.android.versionName
def configAndroid = [
        compileSdkVersion: 28,
        buildToolsVersion: "28.0.3",
        minSdkVersion    : 23,
        targetSdkVersion : 28,
        versionCode      : 10800,
        versionName      : "1.8.0",
        adVersionCode    : 10200,
        adVersionName    : "1.2.0",
        javaSourceVersion: JavaVersion.VERSION_1_8,
        javaTargetVersion: JavaVersion.VERSION_1_8
]
def version = [

        androidSupportSdkVersion     : "28.0.0",
        retrofitSdkVersion           : "2.4.0",
        butterknifeSdkVersion        : "10.2.1",
        rxlifecycle3SdkVersion       : "3.1.0",
        canarySdkVersion             : "1.5.4",
        glideVersion                 : "4.11.0",//4.9.0 开始支持androidx
        dagger2Version               : "2.19"
]
def dependent = [
        "junit"                      : "junit:junit:4.12",
        "appcompat-v4"               : 'androidx.legacy:legacy-support-v4:1.0.0',
        "appcompat-v7"               : 'androidx.appcompat:appcompat:1.1.0',
        "constraint"                 : 'androidx.constraintlayout:constraintlayout:1.1.3',
        "design"                     : 'com.google.android.material:material:1.0.0',
        "cardview"                   : 'androidx.cardview:cardview:1.0.0',
        "recyclerview-v7"            : 'androidx.recyclerview:recyclerview:1.0.0',
        "annotations"                : "androidx.annotation:annotation:1.3.0",
        "mediaCompat"                : "androidx.media:media:1.0.0",
        "annotations-java5"          : "org.jetbrains:annotations-java5:15.0",
        "eventbus"                   : "org.greenrobot:eventbus:3.1.1",
        "butterknife"                : "com.jakewharton:butterknife:${version["butterknifeSdkVersion"]}",
        "butterknife-compiler"       : "com.jakewharton:butterknife-compiler:${version["butterknifeSdkVersion"]}",
        "butterknife-plugin"         : "com.jakewharton:butterknife-gradle-plugin:${version["butterknifeSdkVersion"]}",
        "retrofit2"                  : "com.squareup.retrofit2:retrofit:${version["retrofitSdkVersion"]}",
        "retrofit2-gson"             : "com.squareup.retrofit2:converter-gson:${version["retrofitSdkVersion"]}",
        "retrofit2-rxjava2"          : "com.squareup.retrofit2:adapter-rxjava2:${version["retrofitSdkVersion"]}",
        "disklrucache"               : "com.jakewharton:disklrucache:2.0.2",
        "okhttp3"                    : "com.squareup.okhttp3:okhttp:3.10.0",
        "okhttp3-log"                : "com.squareup.okhttp3:logging-interceptor:3.3.1",
        "rxandroid2"                 : "io.reactivex.rxjava2:rxandroid:2.1.1",
        "rxjava2"                    : "io.reactivex.rxjava2:rxjava:2.1.16",
        "rxlifecycle3"               : "com.trello.rxlifecycle3:rxlifecycle:${version["rxlifecycle3SdkVersion"]}",
        "rxlifecycle3-components"    : "com.trello.rxlifecycle3:rxlifecycle-components:${version["rxlifecycle3SdkVersion"]}",
        "rxcache2"                   : "com.github.VictorAlbertos.RxCache:runtime:1.8.3-2.x",
        "rxcache2-gson"              : "com.github.VictorAlbertos.Jolyglot:gson:0.0.4",
//            "canary-debug"               : "com.squareup.leakcanary:leakcanary-android:${version["canarySdkVersion"]}",
//            "canary-release"             : "com.squareup.leakcanary:leakcanary-android-no-op:${version["canarySdkVersion"]}",
        "multidex"                   : 'androidx.multidex:multidex:2.0.1',
        "lottie"                     : "com.airbnb.android:lottie:2.8.0",
        "logger"                     : "com.orhanobut:logger:2.2.0",
        "glide"                      : "com.github.bumptech.glide:glide:${version["glideVersion"]}",
        "glide-compiler"             : "com.github.bumptech.glide:compiler:${version["glideVersion"]}",
        "gson"                       : "com.google.code.gson:gson:2.8.2",
        "greenDao"                   : "org.greenrobot:greendao:3.3.0",
        "arouter"                    : "com.alibaba:arouter-api:1.3.1",
        "arouter-compiler"           : "com.alibaba:arouter-compiler:1.1.4",
        "zxing"                      : "com.google.zxing:core:3.3.2",
        "bugly_crashreport"          : "com.tencent.bugly:crashreport:latest.release",
        "bugly_nativecrashreport"    : "com.tencent.bugly:nativecrashreport:latest.release",
        "dagger2"                    : "com.google.dagger:dagger:${version["dagger2Version"]}",
        "dagger2-compiler"           : "com.google.dagger:dagger-compiler:${version["dagger2Version"]}",
        "dagger2-android"            : "com.google.dagger:dagger-android:${version["dagger2Version"]}",
        "dagger2-android-support"    : "com.google.dagger:dagger-android-support:${version["dagger2Version"]}",
        "dagger2-android-processor"  : "com.google.dagger:dagger-android-processor:${version["dagger2Version"]}",
//        "openSDK"                    : "com.kaolafm:open-sdk:3.0.6.pre3",
        "ad"                         : "com.kaolafm:ad:1.2.0.1"

]
android {
    compileSdkVersion configAndroid.compileSdkVersion
    buildToolsVersion configAndroid.buildToolsVersion
    defaultConfig {
        applicationId "com.kaolafm.sdk.demo"
        minSdkVersion configAndroid.minSdkVersion
        targetSdkVersion configAndroid.targetSdkVersion
        versionCode configAndroid.versionCode
        versionName configAndroid.versionName
        multiDexEnabled true
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

//        ndk {
//            abiFilters 'armeabi', 'arm64-v8a'
//        }

        ndk {
            abiFilter("arm64-v8a")
        }

    }
    //安装错误  解决
//    splits {
//        abi {
//            enable true
//            reset()
//            include 'x86', 'armeabi-v7a', 'x86_64'
//            universalApk true
//        }
//    }

    signingConfigs {
        release {
            storeFile file('demo.keystore')//签名文件路径，
            storePassword 'demo123' //密码
            keyAlias 'demo'
            keyPassword 'demodemo'  //密码
            v1SigningEnabled true
            v2SigningEnabled true
        }
    }
    buildTypes {

        debug {
            testCoverageEnabled false
            minifyEnabled false
            buildConfigField "boolean", "INCLUDE_OLD_SDK", "true"
        }

        release {
            minifyEnabled false
            signingConfig signingConfigs.release
            buildConfigField "boolean", "INCLUDE_OLD_SDK", "true"
        }
    }
    compileOptions {
        sourceCompatibility configAndroid.javaSourceVersion
        targetCompatibility configAndroid.javaTargetVersion
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE.txt'
    }


    configurations.all {
        resolutionStrategy.eachDependency { DependencyResolveDetails details ->
            def requested = details.requested
            if (requested.group == 'com.android.support') {
                if (!requested.name.startsWith("multidex")) {
                    details.useVersion '27.0.2'
                }
            }
        }
    }

    lintOptions {
        abortOnError false
    }
    def DEFAULT_FLAVOR_DIMENSION = 'kl_car'
    flavorDimensions DEFAULT_FLAVOR_DIMENSION
    productFlavors {

        ceshizhuangyong {
            dimension DEFAULT_FLAVOR_DIMENSION
            applicationId "com.edog.car.ceshizhuanyong_kradio"
            resValue "string", "app_name", "SdkDemo测试专用"
            manifestPlaceholders = [
                    APP_ID : "ye8192",
                    APP_KEY: "f6dff42133bf06810a52a1d392b9906b",
                    CHANNEL: "ceshizhuanyong_kradio"
            ]
        }
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.0'
    implementation dependent["appcompat-v7"]
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    implementation 'androidx.recyclerview:recyclerview:1.0.0'
    implementation dependent["glide"]
    annotationProcessor dependent["glide-compiler"]
    implementation 'com.jakewharton:butterknife:10.2.1'
    annotationProcessor 'com.jakewharton:butterknife-compiler:10.2.1'
    implementation 'com.lcodecorex:tkrefreshlayout:1.0.7'
    implementation 'com.github.tbruyelle:rxpermissions:v0.11'
//    implementation(dependent["openSDK"]) {
//        changing = true
//    }
    implementation project(':sdk')
    //网宿云存储，用于上传录音文件
    implementation 'com.netease.nimlib:basesdk:5.1.1'
    //网易即时通讯基础组件
    implementation 'com.netease.nimlib:chatroom:5.1.1'
    //网易聊天室
    implementation 'com.elvishew:xlog:1.6.1'
    implementation 'com.orhanobut:logger:2.2.0'
//    implementation project(":log")
    implementation dependent["rxlifecycle3"]
    implementation dependent["rxlifecycle3-components"]
    implementation 'androidx.multidex:multidex:2.0.1'
}
buildscript {

    repositories {
        //阿里云的maven镜像，提升下载依赖库的速度，理论上只有这个就可以了，下面的都不需要
        maven { url 'https://iovnexus.radio.cn/repository/maven-public/' }
        google()
        jcenter()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:3.6.4'
        classpath 'org.greenrobot:greendao-gradle-plugin:3.3.0'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
//        classpath files('BuildSrc/BuildSrc.jar')
        classpath 'com.hujiang.aspectjx:gradle-android-plugin-aspectjx:2.0.10'
    }
}

allprojects {
    repositories {
        //阿里云的maven镜像，提升下载依赖库的速度，理论上只有这个就可以了，下面的都不需要
        maven { url 'https://iovnexus.radio.cn/repository/maven-public/' }
        maven{
            url "https://iovnexus.radio.cn/nexus/content/repositories/releases/"
            credentials {
                username = "OnlyReader"
                password = "ewX64rxnDXGK7D6x"
            }
        }
    }
}