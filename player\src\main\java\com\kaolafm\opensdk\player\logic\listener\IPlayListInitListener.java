//package com.kaolafm.opensdk.player.logic.listener;
//
//import com.kaolafm.opensdk.player.logic.model.item.InvalidPlayItem;
//import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
//
//import java.util.List;
//
//public interface IPlayListInitListener {
//
//    void onInitSucess(PlayItem playItem, List<PlayItem> playItemList);
//
//    void onInitError(InvalidPlayItem playItem, int errorCode);
//}
