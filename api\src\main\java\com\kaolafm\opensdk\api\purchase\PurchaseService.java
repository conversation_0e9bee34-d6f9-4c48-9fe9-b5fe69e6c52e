package com.kaolafm.opensdk.api.purchase;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.api.purchase.model.Order;
import com.kaolafm.opensdk.api.purchase.model.PurchaseSucess;
import com.kaolafm.opensdk.api.purchase.model.PurchasedItem;
import com.kaolafm.opensdk.api.purchase.model.QRCodeInfo;
import com.kaolafm.opensdk.api.purchase.model.VipMeals;

import java.util.List;

import io.reactivex.Single;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * 支付相关服务接口
 */
public interface PurchaseService {

    /**
     * 已购列表
     * @param pageNum 页码
     * @param pageSize 行数
     * @return 已购列表
     */
    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER})
    @GET(KaolaApiConstant.PURCHASED_PATH_LIST_STRONG)
    Single<BaseResult<BasePageResult<List<PurchasedItem>>>> getPurchasedList(@Query("pageNum") int pageNum, @Query("pageSize") int pageSize);

    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER})
    @GET(KaolaApiConstant.PURCHASED_PATH_LIST_STRONG)
    Call<BaseResult<BasePageResult<List<PurchasedItem>>>> getPurchasedListSync(@Query("pageNum") int pageNum, @Query("pageSize") int pageSize);

    /**
     * 已购列表一键播放
     * @param clockid 如果为空播放首页，非空为返回结果里的clockId。
     * @return 播放列表
     */
    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER})
    @GET(KaolaApiConstant.PURCHASED_PATH_PLAY)
    Single<BaseResult<List<AudioDetails>>> getPlayPurchasedList( @Query("clockid") String clockid);

    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER})
    @GET(KaolaApiConstant.PURCHASED_PATH_PLAY)
    Call<BaseResult<BasePageResult<List<AudioDetails>>>> getPlayPurchasedListSync(@Query("clockid") long clockid);

    /**
     * 订单列表
     * @param pageNum 页码
     * @param pageSize 行数
     * @return 订单列表
     */
    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER})
    @GET(KaolaApiConstant.ORDER_PATH_LIST_STRONG)
    Single<BaseResult<BasePageResult<List<Order>>>> getOrderList(@Query("pageNum") int pageNum, @Query("pageSize") int pageSize);

    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER})
    @GET(KaolaApiConstant.ORDER_PATH_LIST_STRONG)
    Call<BaseResult<BasePageResult<List<Order>>>> getOrderListSync(@Query("pageNum") int pageNum, @Query("pageSize") int pageSize);


    /**
     * 获取vip套餐信息列表
     * @return vip套餐信息列表
     */
    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER})
    @GET(KaolaApiConstant.VIP_MEALS_PATH_STRONG)
    Single<BaseResult<List<VipMeals>>> getVipMeals();

    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER})
    @GET(KaolaApiConstant.VIP_MEALS_PATH_STRONG)
    Call<BaseResult<List<VipMeals>>> getVipMealsSync();

    /**
     * 生成购买vip二维码
     * @param mealId vip套餐id
     * @param mealMoney 套餐金额
     * @return 二维码
     */
    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER})
    @POST(KaolaApiConstant.VIP_QRCODE_PATH_STRONG)
    Single<BaseResult<QRCodeInfo>> getVipQRCode(@Query("mealId") Long mealId, @Query("mealMoney") Long mealMoney);

    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER})
    @POST(KaolaApiConstant.VIP_QRCODE_PATH_STRONG)
    Call<BaseResult<QRCodeInfo>> getVipQRCodeSync(@Query("mealId") Long mealId, @Query("mealMoney") Long mealMoney);

    /**
     * 生成购买专辑二维码
     * @param albumId 专辑id
     * @param money 专辑金额
     * @return 二维码
     */
    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER})
    @POST(KaolaApiConstant.ALBUM_QRCODE_PATH_STRONG)
    Single<BaseResult<QRCodeInfo>> getAlbumQRCode(@Query("albumId") Long albumId, @Query("money") Long money);

    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER})
    @POST(KaolaApiConstant.ALBUM_QRCODE_PATH_STRONG)
    Call<BaseResult<QRCodeInfo>> getAlbumQRCodeSync(@Query("albumId") Long albumId, @Query("money") Long money);

    /**
     * 生成购买单曲二维码
     * @param audioIds 单曲id 逗号分隔的id字符串
     * @param money 金额
     * @return 二维码
     */
    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER})
    @POST(KaolaApiConstant.AUDIO_QRCODE_PATH_STRONG)
    Single<BaseResult<QRCodeInfo>> getAudioQRCode(@Query("audioIds") String audioIds, @Query("albumId") Long albumId, @Query("money") Long money);

    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER})
    @POST(KaolaApiConstant.AUDIO_QRCODE_PATH_STRONG)
    Call<BaseResult<QRCodeInfo>> getAudioQRCodeSync(@Query("audioIds") String audioIds, @Query("albumId") Long albumId, @Query("money") Long money);

    /**
     * 查看二维码状态
     * @param qrCodeId 二维码id
     * @return 0-未支付，1-已支付，2-过期
     */
    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER})
    @GET(KaolaApiConstant.QRCODE_STATUS_PATH_STRONG)
    Single<BaseResult<PurchaseSucess>> qrCodeStatus(@Query("qrCodeId") String qrCodeId);

    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER})
    @GET(KaolaApiConstant.QRCODE_STATUS_PATH_STRONG)
    Call<BaseResult<PurchaseSucess>> qrCodeStatusSync(@Query("qrCodeId") String qrCodeId);

    /**
     * 云币购买专辑
     * @param albumId 专辑id
     * @param money 专辑金额
     * @return 是否成功
     */
    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER})
    @POST(KaolaApiConstant.BUY_ALBUM_BY_COIN_PATH_STRONG)
    Single<BaseResult<PurchaseSucess>> buyAlbumByCoin(@Query("albumId") Long albumId, @Query("money") Long money);


    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER})
    @POST(KaolaApiConstant.BUY_ALBUM_BY_COIN_PATH_STRONG)
    Call<BaseResult<PurchaseSucess>> buyAlbumByCoinSync(@Query("albumId") Long albumId, @Query("money") Long money);

    /**
     * 云币购买单曲
     * @param audioIds 单曲id 逗号分隔的id字符串
     * @param money 金额
     * @return 是否成功
     */
    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER})
    @POST(KaolaApiConstant.BUY_AUDIO_BY_COIN_PATH_STRONG)
    Single<BaseResult<PurchaseSucess>> buyAudiosByCoin(@Query("audioIds") String audioIds, @Query("albumId") Long albumId, @Query("money") Long money);

    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER})
    @POST(KaolaApiConstant.BUY_AUDIO_BY_COIN_PATH_STRONG)
    Call<BaseResult<PurchaseSucess>> buyAudiosByCoinSync(@Query("audioIds") String audioIds, @Query("albumId") Long albumId, @Query("money") Long money);


    /**
     * 生成购买商品的二维码
     * @param goodsId 商品id
     * @param money 支付金额
     * @param payType 支付方式
     * @return
     */
    @Headers(ApiHostConstants.MALL_DOMAIN_HEADER)
    @POST(KaolaApiConstant.REQUEST_GOODS_QRCODE)
    Single<BaseResult<QRCodeInfo>> getGoodsQRCode(@Query("goodsId") Long goodsId,
                                                  @Query("money") Long money,
                                                  @Query("payType") Integer payType);

    /**
     * 查看商品购买的二维码状态
     * @param qrCodeId 二维码id
     * @return 0-未支付，1-已支付，2-过期
     */
    @Headers({ApiHostConstants.MALL_DOMAIN_HEADER})
    @GET(KaolaApiConstant.REQUEST_GOODS_QRCODE_CHECK)
    Single<BaseResult<PurchaseSucess>> getQRCodeStatus(@Query("qrCodeId") String qrCodeId);
}
