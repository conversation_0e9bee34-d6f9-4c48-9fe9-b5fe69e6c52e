package com.kaolafm.gradle.plugin.model

class MavenConfig {
    /**公司内部仓库*/
    public static final int TYPE_MAVEN_NEXUS = 1

    /**bintray 仓库*/
    public static final int TYPE_MAVEN_BINTRAY = 2

    /**git仓库*/
    public static final int TYPE_MAVEN_GIT = 3

    def artifactId = 'K-radioSDK'
    def groupId = "com.kaolafm"
    def libType = "jar"
    def description = 'Kradio SDK'
    /**
     * 仓库地址或名称。
     * 如果是发布bintray仓库，是仓库名称。
     * 与userOrg一起组成url地址。
     */
    String repository = "maven"
    String userName
    String password
    def versionName = '1.0.0'
    def name = 'SDK'
    def mavenType = TYPE_MAVEN_NEXUS
    /**
     * 用户组织名称。对应于bintray仓库中设置的组织
     */
    def userOrg = "tingban"

    public void mavenType(mavenType) {
        this.mavenType = mavenType
    }

    def setMavenType(mavenType) {
        this.mavenType = mavenType
    }

    def getMavenType() {
        return mavenType
    }

    def getArtifactId() {
        return artifactId
    }

    public void artifactId(artifactId) {
        setArtifactId(artifactId)
    }

    void setArtifactId(artifactId) {
        this.artifactId = artifactId
    }

    def getGroupId() {
        return groupId
    }

    public void groupId(groupId) {
        setGroupId(groupId)
    }

    void setGroupId(groupId) {
        this.groupId = groupId
    }

    def getLibType() {
        return libType
    }

    public void libType(libType) {
        setLibType(libType)
    }

    void setLibType(libType) {
        this.libType = libType
    }

    def getDescription() {
        return description
    }
    public void description(libDescription) {
        setDescription(libDescription)
    }
    void setDescription(libDescription) {
        this.description = libDescription
    }

    def getRepository() {
        return repository
    }

    public void repository(repository) {
        setRepository(repository)
    }
    void setRepository(repository) {
        this.repository = repository
    }

    def getUserName() {
        return userName
    }

    public void userName (userName) {
        setUserName(userName)
    }
    void setUserName(userName) {
        this.userName = userName
    }

    def getPassword() {
        return password
    }

    public void password(password) {
        setPassword(password)
    }

    void setPassword(password) {
        this.password = password
    }

    def getVersionName() {
        return versionName
    }

    public void versionName(version) {
        setVersionName(version)
    }

    void setVersionName(version) {
        this.versionName = version
    }

    def getName() {
        return name
    }

    void setName(name) {
        this.name = name
    }

    @Override
    public String toString() {
        return "MavenConfig{" +
                "artifactId=" + artifactId +
                ", groupId=" + groupId +
                ", libType=" + libType +
                ", description=" + description +
                ", repository=" + repository +
                ", userName=" + userName +
                ", password=" + password +
                ", versionName=" + versionName +
                '}'
    }
}