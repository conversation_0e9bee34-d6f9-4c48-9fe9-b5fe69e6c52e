package com.kaolafm.opensdk.api.media.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

public class ToneQuality implements Parcelable {

    /*
    {
			"title": "标准音质",
			"des": "流量消耗小",
			"audioTargetField": "bitrateNew",
			"audioTargetValue": 48,
			"audioTargetType": "mp3",
			"broadcastTargetField": "bitrateNew",
			"broadcastTargetValue": 48,
			"broadcastTargetType": "m3u8",
			"listenTVTargetField": "bitrateNew",
			"listenTVTargetValue": 48,
			"listenTVTargetType": "m3u8",
			"listenTVProgramTargetField": "bitrateNew",
			"listenTVProgramTargetValue": 48,
			"listenTVProgramTargetType": "m3u8",
			"programTargetField": "bitrateNew",
			"programTargetValue": 48,
			"programTargetType": "m3u8",
			"liveTargetField": "bitrateNew",
			"liveTargetValue": 48,
			"liveTargetType": "m3u8",
			"type": 2
		}
     */

    /**
     * 类型
     */
    @SerializedName("type")
    private int type;
    /**
     * 标题
     */
    @SerializedName("title")
    private String title;
    /**
     * 描述
     */
    @SerializedName("des")
    private String des;
    /**
     * 专辑、ai电台目标码率属性
     */
    @SerializedName("audioTargetField")
    private String audioTargetField;
    /**
     * 专辑、ai电台码率
     */
    @SerializedName("audioTargetValue")
    private String audioTargetValue;
    /**
     * 专辑、ai电台源文件类型
     */
    @SerializedName("audioTargetType")
    private String audioTargetType;
    /**
     * 广播直播属性
     */
    @SerializedName("broadcastTargetField")
    private String broadcastTargetField;
    /**
     * 广播直播码率
     */
    @SerializedName("broadcastTargetValue")
    private String broadcastTargetValue;
    /**
     * 广播直播源文件类型
     */
    @SerializedName("broadcastTargetType")
    private String broadcastTargetType;
    /**
     * 广播回放属性
     */
    @SerializedName("programTargetField")
    private String programTargetField;
    /**
     * 广播回放码率
     */
    @SerializedName("programTargetValue")
    private String programTargetValue;
    /**
     * 广播回放源文件类型
     */
    @SerializedName("programTargetType")
    private String programTargetType;
    /**
     * 直播目标码率使用的属性名
     */
    @SerializedName("liveTargetField")
    private String liveTargetField;
    /**
     * 直播目标码率
     */
    @SerializedName("liveTargetValue")
    private String liveTargetValue;
    /**
     * 直播源文件类型
     */
    @SerializedName("liveTargetType")
    private String liveTargetType;
    /**
     * 听电视直播源属性名
     */
    @SerializedName("listenTVTargetField")
    private String listenTVTargetField;
    /**
     * 听电视直播源码率
     */
    @SerializedName("listenTVTargetValue")
    private String listenTVTargetValue;
    /**
     * 听电视直播源文件类型
     */
    @SerializedName("listenTVTargetType")
    private String listenTVTargetType;
    /**
     * 听电视回听节目源属性名
     */
    @SerializedName("listenTVProgramTargetField")
    private String listenTVProgramTargetField;
    /**
     * 听电视回听节目源文件码率
     */
    @SerializedName("listenTVProgramTargetValue")
    private String listenTVProgramTargetValue;
    /**
     * 听电视回听节目源文件类型
     */
    @SerializedName("listenTVProgramTargetType")
    private String listenTVProgramTargetType;


    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public String getAudioTargetField() {
        return audioTargetField;
    }

    public void setAudioTargetField(String audioTargetField) {
        this.audioTargetField = audioTargetField;
    }

    public String getAudioTargetValue() {
        return audioTargetValue;
    }

    public void setAudioTargetValue(String audioTargetValue) {
        this.audioTargetValue = audioTargetValue;
    }

    public String getAudioTargetType() {
        return audioTargetType;
    }

    public void setAudioTargetType(String audioTargetType) {
        this.audioTargetType = audioTargetType;
    }

    public String getBroadcastTargetField() {
        return broadcastTargetField;
    }

    public void setBroadcastTargetField(String broadcastTargetField) {
        this.broadcastTargetField = broadcastTargetField;
    }

    public String getBroadcastTargetValue() {
        return broadcastTargetValue;
    }

    public void setBroadcastTargetValue(String broadcastTargetValue) {
        this.broadcastTargetValue = broadcastTargetValue;
    }

    public String getBroadcastTargetType() {
        return broadcastTargetType;
    }

    public void setBroadcastTargetType(String broadcastTargetType) {
        this.broadcastTargetType = broadcastTargetType;
    }

    public String getProgramTargetField() {
        return programTargetField;
    }

    public void setProgramTargetField(String programTargetField) {
        this.programTargetField = programTargetField;
    }

    public String getProgramTargetValue() {
        return programTargetValue;
    }

    public void setProgramTargetValue(String programTargetValue) {
        this.programTargetValue = programTargetValue;
    }

    public String getProgramTargetType() {
        return programTargetType;
    }

    public void setProgramTargetType(String programTargetType) {
        this.programTargetType = programTargetType;
    }

    public String getLiveTargetField() {
        return liveTargetField;
    }

    public void setLiveTargetField(String liveTargetField) {
        this.liveTargetField = liveTargetField;
    }

    public String getLiveTargetValue() {
        return liveTargetValue;
    }

    public void setLiveTargetValue(String liveTargetValue) {
        this.liveTargetValue = liveTargetValue;
    }

    public String getLiveTargetType() {
        return liveTargetType;
    }

    public void setLiveTargetType(String liveTargetType) {
        this.liveTargetType = liveTargetType;
    }

    public String getListenTVTargetField() {
        return listenTVTargetField;
    }

    public void setListenTVTargetField(String listenTVTargetField) {
        this.listenTVTargetField = listenTVTargetField;
    }

    public String getListenTVTargetValue() {
        return listenTVTargetValue;
    }

    public void setListenTVTargetValue(String listenTVTargetValue) {
        this.listenTVTargetValue = listenTVTargetValue;
    }

    public String getListenTVTargetType() {
        return listenTVTargetType;
    }

    public void setListenTVTargetType(String listenTVTargetType) {
        this.listenTVTargetType = listenTVTargetType;
    }

    public String getListenTVProgramTargetField() {
        return listenTVProgramTargetField;
    }

    public void setListenTVProgramTargetField(String listenTVProgramTargetField) {
        this.listenTVProgramTargetField = listenTVProgramTargetField;
    }

    public String getListenTVProgramTargetValue() {
        return listenTVProgramTargetValue;
    }

    public void setListenTVProgramTargetValue(String listenTVProgramTargetValue) {
        this.listenTVProgramTargetValue = listenTVProgramTargetValue;
    }

    public String getListenTVProgramTargetType() {
        return listenTVProgramTargetType;
    }

    public void setListenTVProgramTargetType(String listenTVProgramTargetType) {
        this.listenTVProgramTargetType = listenTVProgramTargetType;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.type);
        dest.writeString(this.title);
        dest.writeString(this.des);
        dest.writeString(this.audioTargetField);
        dest.writeString(this.audioTargetValue);
        dest.writeString(this.audioTargetType);
        dest.writeString(this.broadcastTargetField);
        dest.writeString(this.broadcastTargetValue);
        dest.writeString(this.broadcastTargetType);
        dest.writeString(this.programTargetField);
        dest.writeString(this.programTargetValue);
        dest.writeString(this.programTargetType);
        dest.writeString(this.liveTargetField);
        dest.writeString(this.liveTargetValue);
        dest.writeString(this.liveTargetType);
        dest.writeString(this.listenTVProgramTargetField);
        dest.writeString(this.listenTVProgramTargetValue);
        dest.writeString(this.listenTVProgramTargetType);
        dest.writeString(this.listenTVTargetField);
        dest.writeString(this.listenTVTargetValue);
        dest.writeString(this.listenTVTargetType);
    }

    protected ToneQuality(Parcel in) {
        this.type = in.readInt();
        this.title = in.readString();
        this.des = in.readString();
        this.audioTargetField = in.readString();
        this.audioTargetValue = in.readString();
        this.audioTargetType = in.readString();
        this.broadcastTargetField = in.readString();
        this.broadcastTargetValue = in.readString();
        this.broadcastTargetType = in.readString();
        this.programTargetField = in.readString();
        this.programTargetValue = in.readString();
        this.programTargetType = in.readString();
        this.liveTargetField = in.readString();
        this.liveTargetValue = in.readString();
        this.liveTargetType = in.readString();
        this.listenTVProgramTargetField = in.readString();
        this.listenTVProgramTargetValue = in.readString();
        this.listenTVProgramTargetType = in.readString();
        this.listenTVTargetField = in.readString();
        this.listenTVTargetValue = in.readString();
        this.listenTVTargetType = in.readString();
    }

    public static final Creator<ToneQuality> CREATOR = new Creator<ToneQuality>() {
        @Override
        public ToneQuality createFromParcel(Parcel in) {
            return new ToneQuality(in);
        }

        @Override
        public ToneQuality[] newArray(int size) {
            return new ToneQuality[size];
        }
    };

    @Override
    public String toString() {
        return "ToneQuality{" +
                "type=" + type +
                ", title='" + title + '\'' +
                ", des='" + des + '\'' +
                ", audioTargetField='" + audioTargetField + '\'' +
                ", audioTargetValue='" + audioTargetValue + '\'' +
                ", audioTargetType='" + audioTargetType + '\'' +
                ", broadcastTargetField='" + broadcastTargetField + '\'' +
                ", broadcastTargetValue='" + broadcastTargetValue + '\'' +
                ", broadcastTargetType='" + broadcastTargetType + '\'' +
                ", programTargetField='" + programTargetField + '\'' +
                ", programTargetValue='" + programTargetValue + '\'' +
                ", programTargetType='" + programTargetType + '\'' +
                ", liveTargetField='" + liveTargetField + '\'' +
                ", liveTargetValue='" + liveTargetValue + '\'' +
                ", liveTargetType='" + liveTargetType + '\'' +
                ", listenTVTargetField='" + listenTVTargetField + '\'' +
                ", listenTVTargetValue='" + listenTVTargetValue + '\'' +
                ", listenTVTargetType='" + listenTVTargetType + '\'' +
                ", listenTVProgramTargetField='" + listenTVProgramTargetField + '\'' +
                ", listenTVProgramTargetValue='" + listenTVProgramTargetValue + '\'' +
                ", listenTVProgramTargetType='" + listenTVProgramTargetType + '\'' +
                '}';
    }
}
