//package com.kaolafm.opensdk.demo.live.play;
//
//import android.media.MediaPlayer;
//import android.media.MediaRecorder;
//import android.os.Environment;
//import android.os.SystemClock;
//import android.util.Log;
//
//import com.kaolafm.opensdk.BuildConfig;
//import com.kaolafm.opensdk.demo.live.chat.RecorderStatus;
//import com.kaolafm.opensdk.di.component.ComponentKit;
//import com.kaolafm.opensdk.live.KradioRecorder;
//import com.kaolafm.opensdk.live.KradioRecorderInterface;
//import com.kaolafm.opensdk.player.logic.PlayerManager;
//import com.kaolafm.opensdk.player.logic.listener.IPlayListControl;
//import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
//
//import java.io.File;
//import java.io.IOException;
//import java.util.Observable;
//import java.util.Observer;
//
///**
// * 管理直播信息的轮询，状态解析，录音管理，试听，默认播放，上传状态维护，是UI的一个辅助类
// *
// * <AUTHOR> Huangui
// */
//public class LiveManager2 {
//
//    /**
//     * KRadio开始录音广播消息
//     *
//     * @apiNote 此action针对KRadio客户端开始录音时发出的广播消息
//     */
//    public final static String START_RECORD_REQUEST_ACTION = "com.kaolafm.kradio.request.action.START_RECORD";
////    /**
////     * KRadio开始录音广播消息携带参数
////     *
////     * @apiNote 此extra针对KRadio客户端开始录音时发出的 START_RECORD_REQUEST_ACTION 消息额外携带的参数
////     */
////    public final static String START_RECORD_REQUEST_EXTRA = "com.kaolafm.kradio.request.extra.START_RECORD";
//
//    /**
//     * KRadio开始录音广播回复消息
//     *
//     * @apiNote 此action针对KRadio客户端开始录音时得到的回复广播消息（由非KRadio端发出）
//     */
//    public final static String START_RECORD_RESPONSSE_ACTION = "com.kaolafm.kradio.response.action.START_RECORD";
//    /**
//     * KRadio开始录音广播回复消息携带参数
//     *
//     * @apiNote 此extra针对KRadio客户端开始录音时收到的 START_RECORD_RESPONSE_ACTION 消息额外携带的参数（true 为同意KRadio的录音申请消息，false 为拒绝KRadio的录音申请消息）
//     */
//    public final static String START_RECORD_RESPONSE_BOOLEAN_EXTRA = "com.kaolafm.kradio.response.extra.START_RECORD";
//
//    /**
//     * KRadio停止录音广播消息
//     *
//     * @apiNote 此action针对KRadio客户端停止录音时发出的广播消息
//     */
//    public final static String STOP_RECORD_REQUEST_ACTION = "com.kaolafm.kradio.request.action.STOP_RECORD";
////    /**
////     * KRadio停止录音广播消息携带参数
////     *
////     * @apiNote 此extra针对KRadio客户端停止录音时发出的 STOP_RECORD_REQUEST_ACTION 消息额外携带的参数
////     */
////    public final static String STOP_RECORD_REQUEST_EXTRA = "com.kaolafm.kradio.request.extra.STOP_RECORD";
//
//    /**
//     * KRadio停止录音广播回复消息
//     *
//     * @apiNote 此action针对KRadio客户端停止录音时得到的回复广播消息（由非KRadio端发出）
//     */
//    public final static String STOP_RECORD_RESPONSSE_ACTION = "com.kaolafm.kradio.response.action.STOP_RECORD";
//    /**
//     * KRadio停止录音广播回复消息携带参数
//     *
//     * @apiNote 此extra针对KRadio客户端停止录音时收到的 STOP_RECORD_RESPONSSE_ACTION 消息额外携带的参数（true 为同意KRadio的停止录音申请消息，false 为拒绝KRadio的停止录音申请消息）
//     */
//    public final static String STOP_RECORD_RESPONSE_EXTRA = "com.kaolafm.kradio.response.extra.STOP_RECORD";
//
//
//    private static final String TAG = "LiveManager2";
//
//    public static final String RECORD_TIME_TOO_SHORT = "too_short";
//
//    private static final int RECORD_MINIMUM_TIME = 1000;
//    ;
//
//    private long mRecordStartTime;
//    private int mRecordDuration;
//
////    private LiveRecordhelper mLiveRecordhelper;
//
//    //录音机对象
//    private KradioRecorderInterface mKradioRecorderInterface;
//
//    public static final String EXTENSION = ".aac";
//    public static final File RECORDINGS_DIR = new File(
//            ComponentKit.getInstance().getApplication().getApplicationContext().getExternalFilesDir(Environment.DIRECTORY_MUSIC),
//            "K-Radio-Record");
//
//    private MediaPlayer mMediaPlayer;
//    private MediaRecorder mMediaRecorder;
//    private String mFilePath;
//    private RecorderStatusObservable mRecorderStatusObservable;
//
//    private LiveManager2() {
//        mRecorderStatusObservable = new RecorderStatusObservable(RecorderStatus.IDLE);
//
//        //设置为考拉默认录音
//        mKradioRecorderInterface = new KradioRecorder();
////        if (mKRadioAudioRecorderInter != null) {
////            mLiveRecordhelper = new LiveRecordhelper();
////            mKRadioAudioRecorderInter.initVR(AppDelegate.getInstance().getContext());
////            mKRadioAudioRecorderInter.setVrStatusListener(new KRadioAudioRecorderInter.OnVRStatusListener() {
////                @Override
////                public void onSpeakBegin() {
////                    Log.i(TAG, "onSpeakBegin--------->");
////                }
////
////                @Override
////                public void onSpeakCompleted() {
////                    Log.i(TAG, "onSpeakCompleted--------->");
////                }
////            });
////
////        } else {
////
////        }
//    }
//
//    private static final class INSTANCE_HOLDER {
//        private static final LiveManager2 INSTANCE = new LiveManager2();
//    }
//
//    public static LiveManager2 getInstance() {
//        return INSTANCE_HOLDER.INSTANCE;
//    }
//
//    public void startRecordDeal() {
//        startRecord();
//    }
//
//    public String startRecord() {
//        if (BuildConfig.DEBUG) {
//            Log.i(TAG, "startRecord");
//        }
//
////        if (mKradioRecorderInterface != null) {
////            mKradioRecorderInterface.startRecord();
////            mRecordStartTime = SystemClock.elapsedRealtime();
////            mFilePath = mKradioRecorderInterface.getFilePath();
////            mRecorderStatusObservable.setRecorderStatus(RecorderStatus.RECORDING);
////            Log.i(TAG, "mKradioRecorderInterface startRecord");
////        } else {
////            mRecorderStatusObservable.setRecorderStatus(RecorderStatus.IDLE);
////        }
//        mRecorderStatusObservable.setRecorderStatus(RecorderStatus.IDLE);
//
//        return null;
//
//    }
//
//    /**
//     * @return 录音文件的路径，如果录音发生错误，返回null，如果录音时间过短，
//     * 返回{@link LiveManager2#RECORD_TIME_TOO_SHORT}
//     */
//    public String stopRecord() {
//        return stopRecord(false);
//    }
//
//    /**
//     * @param cancel 停止录音是否是要取消录音
//     * @return 录音文件的路径，如果录音发生错误，返回null，如果录音时间过短，
//     * 返回{@link LiveManager2#RECORD_TIME_TOO_SHORT}
//     */
//    public String stopRecord(boolean cancel) {
//        if (BuildConfig.DEBUG) {
//            Log.i(TAG, "stopRecord");
//        }
////        if (mKRadioAudioRecorderInter != null) {
////            mKRadioAudioRecorderInter.onAudioRecordStop();
////        }
//
//        boolean stop = false;
//
//        if (mKradioRecorderInterface != null) {
//            stop = mKradioRecorderInterface.stopRecord();
//            mRecordDuration = (int) (SystemClock.elapsedRealtime() - mRecordStartTime);
//            Log.i(TAG, "mKradioRecorderInterface stopRecord");
//        }
//        Log.i(TAG, "mKradioRecorderInterface stopRecord status:"+stop);
//
//        //兼容以前逻辑 stop failed 返回null
//        if (!stop) {
//            mRecorderStatusObservable.setRecorderStatus(RecorderStatus.IDLE);
//            return null;
//        }
//
//        if (cancel) {
//            cancel();
//            return null;
//        }
//        if (mRecordDuration <= RECORD_MINIMUM_TIME) {
//            mRecorderStatusObservable.setRecorderStatus(RecorderStatus.CANCEL);
//            cancel();
//            return RECORD_TIME_TOO_SHORT;
//        }
//        mRecorderStatusObservable.setRecorderStatus(RecorderStatus.FINISHED);
//        return mFilePath;
//    }
//
//    private File createNewAudioFile() {
//        if (!RECORDINGS_DIR.exists()) {
//            RECORDINGS_DIR.mkdirs();
//        }
//        File file = new File(RECORDINGS_DIR, "Live-Leave-message" + EXTENSION);
//        return file;
//    }
//
//    public int getRecordDuration() {
//        return mRecordDuration;
//    }
//
//    /**
//     * @return true 正在录音, false 不在录音
//     */
//    public boolean isRecording() {
//        return mRecorderStatusObservable.getRecorderStatus() == RecorderStatus.RECORDING;
//    }
//
//    /**
//     * @return true 正在试听, false 不在试听
//     */
//    public boolean isPlaying() {
//        return mRecorderStatusObservable.getRecorderStatus() == RecorderStatus.LISTENING;
//    }
//
//    /**
//     * @return true 录音刚刚完成，尚未进行其它操作
//     */
//    public boolean isFinished() {
//        return mRecorderStatusObservable.getRecorderStatus() == RecorderStatus.FINISHED;
//    }
//
//    /**
//     * @return true 试听过了录音，包括完整听完或者听了其中一部分，但尚未进行其它操作
//     */
//    public boolean isListened() {
//        return mRecorderStatusObservable.getRecorderStatus() == RecorderStatus.LISTENED;
//    }
//
//    /**
//     * @return true 空闲状态，可以录音，
//     */
//    public boolean isIdle() {
//        return mRecorderStatusObservable.getRecorderStatus() == RecorderStatus.IDLE;
//    }
//
//    /**
//     * @return true 正在上传录音文件
//     */
//    public boolean isUploading() {
//        return mRecorderStatusObservable.getRecorderStatus() == RecorderStatus.UPLOADING;
//    }
//
//    /**
//     * @return true 上传完成
//     */
//    public boolean isUploaded() {
//        return mRecorderStatusObservable.getRecorderStatus() == RecorderStatus.UPLOADED;
//    }
//
//    /**
//     * @return true 上传失败
//     */
//    public boolean isFailure() {
//        return mRecorderStatusObservable.getRecorderStatus() == RecorderStatus.FAILURE;
//    }
//
//    public void startListen() {
//        Log.w(TAG, "listen");
//        mMediaPlayer = new MediaPlayer();
//        mMediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
//            @Override
//            public void onCompletion(MediaPlayer mp) {
//                mp.release();
//                mMediaPlayer = null;
//                mRecorderStatusObservable.setRecorderStatus(RecorderStatus.LISTENED);
//            }
//        });
//        mMediaPlayer.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
//            @Override
//            public void onPrepared(MediaPlayer mp) {
//                mRecorderStatusObservable.setRecorderStatus(RecorderStatus.LISTENING);
//                mp.start();
//            }
//        });
//        try {
//            mMediaPlayer.setDataSource(mFilePath);
//            mMediaPlayer.prepareAsync();
//        } catch (IOException e) {
//            if (BuildConfig.DEBUG) {
//                Log.w(TAG, "listen", e);
//            }
//        }
//        // 解决https://bugly.qq.com/v2/crash-reporting/crashes/bedf20cb76/40006/report?pid=1&search=&searchType=detail&bundleId=&channelId=dongfengqichen_chengdu&version=all&tagList=&start=0&date=all 问题
//        catch (IllegalStateException e) {
//            e.printStackTrace();
//        }
//    }
//
//    public int getListenDuration() {
//        if (mMediaPlayer != null) {
//            return mMediaPlayer.getDuration();
//        }
//        return -1;
//    }
//
//    public String getFilePath() {
//        return mFilePath;
//    }
//
//    public void stopListen() {
//        if (BuildConfig.DEBUG) {
//            Log.w(TAG, "stopListen");
//        }
//        if (mMediaPlayer != null && mMediaPlayer.isPlaying()) {
//            mMediaPlayer.stop();
//            mMediaPlayer.release();
//            mMediaPlayer = null;
//            mRecorderStatusObservable.setRecorderStatus(RecorderStatus.LISTENED);
//        }
//    }
//
//    public void cancel() {
//        stopListen();
//        mRecorderStatusObservable.setRecorderStatus(RecorderStatus.IDLE);
//        deleteFile();
//    }
//
//    public void deleteFile() {
//        if (mFilePath == null) {
//            return;
//        }
//        File file = new File(mFilePath);
//        if (file.exists()) {
//            file.delete();
//        }
//    }
//
//    public void setRecorderStatus(RecorderStatus status) {
//        mRecorderStatusObservable.setRecorderStatus(status);
//    }
//
//    public class RecorderStatusObservable extends Observable {
//
//        RecorderStatusObservable(RecorderStatus status) {
//            mRecordStatus = status;
//        }
//
//        private RecorderStatus mRecordStatus;
//
//        public void setRecorderStatus(RecorderStatus status) {
//            if (BuildConfig.DEBUG) {
//                Log.i(TAG, "setRecorderStatus: " + status);
//            }
//            mRecordStatus = status;
//            setChanged();
//            notifyObservers(mRecordStatus);
//        }
//
//        public RecorderStatus getRecorderStatus() {
//            return mRecordStatus;
//        }
//    }
//
//    public void addRecorderStatusObserver(Observer o) {
//        mRecorderStatusObservable.addObserver(o);
//    }
//
//    public void removeRecorderStatusObserver(Observer o) {
//        mRecorderStatusObservable.deleteObserver(o);
//    }
//
//    public void onLiveExit() {
//        IPlayListControl iPlayListControl = PlayerManager.getInstance().getPlayListControl();
//        int position = iPlayListControl.getCurPosition();
//        if (position < 0 || iPlayListControl.getPlayList().size() <= position) {
//            return;
//        }
//        PlayItem playItem = iPlayListControl.getPlayList().get(position);
//        PlayerManager.getInstance().startPlayItemInList(playItem);
//    }
//
//    public void unRegisterResponseRecord() {
////        if (mLiveRecordhelper != null) {
////            mLiveRecordhelper.unRegisterResponseRecord();
////        }
//    }
//
//    public void registerResponseRecord() {
////        if (mLiveRecordhelper != null) {
////            mLiveRecordhelper.registerResponseBroadcast();
////        }
//    }
//}
