package com.kaolafm.opensdk.db.manager;

import com.kaolafm.opensdk.api.history.model.ListeningHistory;
import com.kaolafm.opensdk.db.OnQueryListener;
import com.kaolafm.opensdk.db.greendao.DaoMaster;
import com.kaolafm.opensdk.db.greendao.DaoSession;
import com.kaolafm.opensdk.db.greendao.ListeningHistoryDao;
import com.kaolafm.opensdk.di.component.ComponentKit;

import java.util.List;

import io.reactivex.Single;

/**
 * <AUTHOR>
 * @date 2020/8/10
 */
public class GreenDaoOperation implements DBOperation<ListeningHistory> {

    private DaoSession mDaoSession;

    public GreenDaoOperation() {
        SQLiteUpdateOpenHelper helper = new SQLiteUpdateOpenHelper(ComponentKit.getInstance().getApplication(), getDatabaseName());
        DaoMaster daoMaster = new DaoMaster(helper.getWritableDb());
        mDaoSession = daoMaster.newSession();
    }

    private String getDatabaseName() {
        return null;
    }

    @Override
    public void updateTable(int oldVersion, int newVersion) {
    }

    @Override
    public void insert(ListeningHistory listeningHistory) {

    }

    @Override
    public void insert(List<ListeningHistory> list) {

    }

    @Override
    public boolean insertSync(ListeningHistory listeningHistory) {
        return false;
    }

    @Override
    public boolean insertSync(List<ListeningHistory> list) {
        return false;
    }

    @Override
    public void save(ListeningHistory listeningHistory) {

    }

    @Override
    public void save(List<ListeningHistory> list) {

    }

    @Override
    public void saveSync(List<ListeningHistory> list) {

    }

    @Override
    public void saveSync(ListeningHistory listeningHistory) {

    }

    @Override
    public void update(ListeningHistory listeningHistory) {

    }

    @Override
    public void update(List<ListeningHistory> list) {

    }

    @Override
    public void deleteAll(Class<ListeningHistory> clazz) {

    }

    @Override
    public void delete(ListeningHistory listeningHistory) {

    }

    @Override
    public void delete(List<ListeningHistory> list, Class clazz) {

    }

    @Override
    public void delete(List<ListeningHistory> list) {

    }

    @Override
    public String getTableName() {
        return mDaoSession.getDao(ListeningHistoryDao.class).getTablename();
    }

    @Override
    public void queryById(long id, OnQueryListener<ListeningHistory> listener) {

    }

    @Override
    public void queryRaw(String where, OnQueryListener<List<ListeningHistory>> listener, Object... params) {

    }

    @Override
    public List<ListeningHistory> queryRawSync(String where, String... params) {
        return null;
    }

    @Override
    public Single<List<ListeningHistory>> queryAll() {
        return null;
    }

    @Override
    public void queryAll(OnQueryListener<List<ListeningHistory>> listener) {

    }

    @Override
    public void queryAllCount(OnQueryListener<Long> listener) {

    }

    @Override
    public List<ListeningHistory> queryAllSync() {
        return null;
    }

    @Override
    public void closeDataBase() {

    }
}
