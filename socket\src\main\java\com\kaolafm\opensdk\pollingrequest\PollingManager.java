package com.kaolafm.opensdk.pollingrequest;

import android.util.Log;

import com.kaolafm.opensdk.socket.SocketListener;

public class PollingManager {
    private static final String TAG = "PollingManager";

    private PollingClient mClient;
    private PollingRequestBean mBean;

    private PollingManager() {
        if (mClient == null) {
            mClient = PollingClient.getInstance();
            mClient.init();
        }
    }

    public static PollingManager getInstance() {
        return PollingManagerHolder.INSTANCE;
    }

    private static final class PollingManagerHolder {
        private static final PollingManager INSTANCE = new PollingManager();
    }

    public void setBeanToClient(int intervalTime, SocketListener callback, IPollingRequest request) {
        mBean = new PollingRequestBean();
        mBean.event = callback.getEvent();
        mBean.intervalTime = Math.max(intervalTime, PollingRequestBean.INTERVAL_TIME);
        mBean.listener = callback;
        mBean.request = request;
        Log.i(TAG, "add:" + mBean.event);
        mClient.addPollingBean(mBean);
    }

    public void start() {
        if (mBean == null) {
            Log.i(TAG, "currentBean is null");
        }
        Log.i(TAG, "start:" + mBean.event);
        mClient.start(mBean.event);
    }

    public void stopAll() {
        Log.i(TAG, "stop: all");
        mClient.releaseAll();
    }

    public void stop(String event) {
        Log.i(TAG, "stop: " + event);
        mClient.releaseOne(event);
    }
}
