package com.kaolafm.opensdk.utils.operation;

import com.kaolafm.opensdk.api.operation.model.category.CategoryMember;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;

/**
 * 运营成员处理接口
 *
 * <AUTHOR>
 * @date 2018/9/21
 */

public interface IOperationProcessor {

    /**
     * 判断分类成员属于哪个子类
     *
     * @param member 分类成员
     */
    boolean accept(CategoryMember member);

    /**
     * 判断栏目成员属于哪个子类
     *
     * @param member 栏目成员
     */
    boolean accept(ColumnMember member);

    /**
     * 获取分类成员id
     *
     * @param member 分类成员
     */
    long getId(CategoryMember member);

    /**
     * 获取栏目成员id
     *
     * @param member 栏目成员
     */
    long getId(ColumnMember member);

    /**
     * 获取分类成员收听数
     *
     * @param member 分类成员
     */
    long getListenNum(CategoryMember member);

    /**
     * 获取分类成员类型
     *
     * @param member 分类成员
     */
    int getType(CategoryMember member);

    /**
     * 获取栏目成员类型
     *
     * @param member 栏目成员
     */
    int getType(ColumnMember member);

    /**
     * 播放分类成员
     *
     * @param member 分类成员
     */
    void play(CategoryMember member);

    /**
     * 播放栏目成员
     *
     * @param member 栏目成员
     */
    void play(ColumnMember member);
}
