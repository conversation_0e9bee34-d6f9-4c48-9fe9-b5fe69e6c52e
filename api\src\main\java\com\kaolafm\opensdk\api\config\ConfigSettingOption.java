package com.kaolafm.opensdk.api.config;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;


public class ConfigSettingOption implements Serializable {

    /**
     * 确认有upload_big_datacenter，且为on。就上报到大数据中心
     */
    @SerializedName("upload_big_datacenter")
    private String uploadBigDatacenter;
    /**
     * 音量均衡
     */
    @SerializedName("volume_balance")
    private String volumeBalance;
    /**
     * 取消授权时文案
     */
    @SerializedName("revokeContent")
    private String revokeContent;
    /**
     * 关于我们文案
     */
    @SerializedName("aboutUsImg")
    private String aboutUsImg;
    /**
     * 关于我们文案
     */
    @SerializedName("aboutUsContent")
    private String aboutUsContent;
    /**
     * 服务端最新服务协议版本
     */
    @SerializedName("agreementVersion")
    private Integer agreementVersion;
    /**
     * 服务端最新隐私政策版本
     */
    @SerializedName("privacyPolicyVersion")
    private Integer privacyPolicyVersion;
    /**
     * 协议版本更新提示文案
     */
    @SerializedName("promptCopy")
    private String promptCopy;

    /**
     * 首页搜索热词轮播间隔
     */
    @SerializedName("wheelingTime")
    private Long searchHotWordIntegererval;
    /**
     * 是否静默 0-否 1- 静默
     */
    @SerializedName("isSilent")
    private Integer isSilent;

    /**
     * 是否有直播动效
     */
    @SerializedName("liveFlash")
    private Integer liveFlash;// 0-关闭，1-开启

    
    /**
     * 品牌主页id
     */
    @SerializedName("brandPageId")
    private Long brandPageId;

    /**
     * 是否展示品牌主页固定推荐位
     */
    @SerializedName("showBrandEntries")
    private Integer showBrandEntries;// 0-关，1-展示

    /**
     * 是否开启动画 0-否 1- 静默
     */
    @SerializedName("isPlayAnim")
    private Integer isPlayAnim;

    @SerializedName("msgTimeArrange")
    private Integer msgTimeArrange;
    @SerializedName("msgLimit")
    private Integer msgLimit;

    public Integer getMsgTimeArrange() {
        return msgTimeArrange;
    }

    public void setMsgTimeArrange(Integer msgTimeArrange) {
        this.msgTimeArrange = msgTimeArrange;
    }

    public Integer getMsgLimit() {
        return msgLimit;
    }

    public void setMsgLimit(Integer msgLimit) {
        this.msgLimit = msgLimit;
    }

    public ConfigSettingOption() {
    }

    public ConfigSettingOption(String uploadBigDatacenter, String volumeBalance, String revokeContent, String aboutUsImg, String aboutUsContent, Integer agreementVersion, Integer privacyPolicyVersion, String promptCopy, Long searchHotWordIntegererval, Integer isSilent, Integer liveFlash, Long brandPageId, Integer showBrandEntries, Integer isPlayAnim,
                               Integer msgTimeArrange, Integer msgLimit) {
        this.uploadBigDatacenter = uploadBigDatacenter;
        this.volumeBalance = volumeBalance;
        this.revokeContent = revokeContent;
        this.aboutUsImg = aboutUsImg;
        this.aboutUsContent = aboutUsContent;
        this.agreementVersion = agreementVersion;
        this.privacyPolicyVersion = privacyPolicyVersion;
        this.promptCopy = promptCopy;
        this.searchHotWordIntegererval = searchHotWordIntegererval;
        this.isSilent = isSilent;
        this.liveFlash = liveFlash;
        this.brandPageId = brandPageId;
        this.showBrandEntries = showBrandEntries;
        this.isPlayAnim = isPlayAnim;
        this.msgTimeArrange = msgTimeArrange;
        this.msgLimit = msgLimit;
    }

    public String getUploadBigDatacenter() {
        return uploadBigDatacenter;
    }

    public void setUploadBigDatacenter(String uploadBigDatacenter) {
        this.uploadBigDatacenter = uploadBigDatacenter;
    }

    public String getVolumeBalance() {
        return volumeBalance;
    }

    public void setVolumeBalance(String volumeBalance) {
        this.volumeBalance = volumeBalance;
    }

    public String getRevokeContent() {
        return revokeContent;
    }

    public void setRevokeContent(String revokeContent) {
        this.revokeContent = revokeContent;
    }

    public String getAboutUsImg() {
        return aboutUsImg;
    }

    public void setAboutUsImg(String aboutUsImg) {
        this.aboutUsImg = aboutUsImg;
    }

    public String getAboutUsContent() {
        return aboutUsContent;
    }

    public void setAboutUsContent(String aboutUsContent) {
        this.aboutUsContent = aboutUsContent;
    }

    public Integer getAgreementVersion() {
        return agreementVersion;
    }

    public void setAgreementVersion(Integer agreementVersion) {
        this.agreementVersion = agreementVersion;
    }

    public Integer getPrivacyPolicyVersion() {
        return privacyPolicyVersion;
    }

    public void setPrivacyPolicyVersion(Integer privacyPolicyVersion) {
        this.privacyPolicyVersion = privacyPolicyVersion;
    }

    public String getPromptCopy() {
        return promptCopy;
    }

    public void setPromptCopy(String promptCopy) {
        this.promptCopy = promptCopy;
    }

    public Long getSearchHotWordIntegererval() {
        return searchHotWordIntegererval;
    }

    public void setSearchHotWordIntegererval(Long searchHotWordIntegererval) {
        this.searchHotWordIntegererval = searchHotWordIntegererval;
    }

    public Integer getIsSilent() {
        return isSilent;
    }

    public void setIsSilent(Integer isSilent) {
        this.isSilent = isSilent;
    }

    public Integer getLiveFlash() {
        return liveFlash;
    }

    public void setLiveFlash(Integer liveFlash) {
        this.liveFlash = liveFlash;
    }

    public Long getBrandPageId() {
        return brandPageId;
    }

    public void setBrandPageId(Long brandPageId) {
        this.brandPageId = brandPageId;
    }

    public Integer getShowBrandEntries() {
        return showBrandEntries;
    }

    public void setShowBrandEntries(Integer showBrandEntries) {
        this.showBrandEntries = showBrandEntries;
    }

    public Integer getIsPlayAnim() {
        return isPlayAnim;
    }

    public void setIsPlayAnim(Integer isPlayAnim) {
        this.isPlayAnim = isPlayAnim;
    }

    @Override
    public String toString() {
        return "ConfigSettingOption{" +
                "uploadBigDatacenter='" + uploadBigDatacenter + '\'' +
                ", volumeBalance='" + volumeBalance + '\'' +
                ", revokeContent='" + revokeContent + '\'' +
                ", aboutUsImg='" + aboutUsImg + '\'' +
                ", aboutUsContent='" + aboutUsContent + '\'' +
                ", agreementVersion=" + agreementVersion +
                ", privacyPolicyVersion=" + privacyPolicyVersion +
                ", promptCopy='" + promptCopy + '\'' +
                ", searchHotWordIntegererval=" + searchHotWordIntegererval +
                ", isSilent=" + isSilent +
                ", liveFlash=" + liveFlash +
                ", brandPageId=" + brandPageId +
                ", showBrandEntries=" + showBrandEntries +
                ", isPlayAnim=" + isPlayAnim +
                ", msgTimeArrange=" + msgTimeArrange +
                ", msgLimit=" + msgLimit +
                '}';
    }
}
