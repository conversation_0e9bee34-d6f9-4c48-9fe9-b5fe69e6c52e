// Top-level build file where you can add configuration options common to all sub-projects/modules.
apply from: "config.gradle"

buildscript {

    repositories {
        maven { url 'https://jitpack.io' }
        google()
        //阿里云的maven镜像，提升下载依赖库的速度，理论上只有这个就可以了，下面的都不需要
        maven { url 'https://iovnexus.radio.cn/repository/maven-public/' }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:3.6.4'
        classpath 'org.greenrobot:greendao-gradle-plugin:3.3.0'
    }
}

allprojects {
    repositories {
        maven { url 'https://jitpack.io' }
        //阿里云的maven镜像，提升下载依赖库的速度，理论上只有这个就可以了，下面的都不需要
        maven {
            url "https://iovnexus.radio.cn/repository/maven-public/"
            credentials {
                username = "OnlyReader"
                password = "ewX64rxnDXGK7D6x"
            }
        }
        maven {
            def mavenUrl = readLocalProperties("local.maven.url")
            if (mavenUrl != null) {
                url mavenUrl
            }
        }
    }
}

def readLocalProperties(String name) {
    def properties = new Properties()
    File file = project.rootProject.file('local.properties')
    if (file.exists()) {
        def inputStream = file.newDataInputStream()
        properties.load(inputStream)
        if (properties.containsKey(name)) {
            return properties.getProperty(name)
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

subprojects {
    // 定义检查依赖变化的时间间隔,!!配置为0实时刷新
    configurations.all {
        // check for updates every build
        resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    }
}