package com.kaolafm.report.util;

/**
 * <AUTHOR> on 2019/1/10.
 */

public class ReportConstants {

    public static final String REPORT_TAG = "report_tag";
    public static final String REPORT_BIGDATA_TAG = "report_bigdata_tag";


    public static final int READ_DATE_BASE_TIMER = 60;
    public static final int READ_DATE_BASE_MAX_TIMER = 60 * 10;
    public static final int READ_DATA_BASE_MAX_COUNT = 10;
    public static final int READ_BIG_DATA_BASE_MAX_COUNT = 1;

    public static final int TASK_TYPE_INSERT = 1;
    public static final int TASK_TYPE_SEND_DATA = 2;
    public static final int TASK_TYPE_DELETE = 3;

    public static final int UPLOAD_TASK_TYPE_NORMAL = 1;
    public static final int UPLOAD_TASK_TYPE_BY_DATA_BASE = 2;

    public static final String EVENT_ID_START = "100010";
    public static final String EVENT_ID_BUFFER_START = "300030";
    public static final String EVENT_ID_BUFFER_END = "300022";
    public static final String EVENT_ID_UPDATE = "100005";
    public static final String EVENT_ID_VERSION_CHANGE = "100007";
    public static final String EVENT_ID_LISTEN_START = "101013";
    public static final String EVENT_ID_LISTEN_END = "101010";
    public static final String EVENT_ID_REQUEST_ERROR = "992000";
    public static final String EVENT_ID_CRASH = "999999";
    public static final String EVENT_ID_AUDIO_SEARCH = "100022";
    public static final String EVENT_ID_SEARCH_RESULT_SELECT = "100023";
    public static final String EVENT_ID_SEARCH_RESULT = "100024";

    public static final String EVENT_ID_KAOLA_FM_TO_K_RADIO = "100099";

    /**
     * 断点续传
     */
    public static final String EVENT_ID_BREAKPOINT_PLAY = "210003";

    /* 首页滑动 */
    public static final String EVENT_ID_HOME_SCROLL = "210001";

    /* 全部栏目滑动 */
    public static final String EVENT_ID_ALL_CATEGORY_SCROLL = "210002";
    /**
     * 购买专辑
     */
    public static final String EVENT_ID_BUY_ALBUM = "510001";
    /**
     * 购买单曲
     */
    public static final String EVENT_ID_BUY_AUDIO = "510002";
    /**
     * 购买会员
     */
    public static final String EVENT_ID_BUY_VIP = "510003";

    /**
     * kradio 1.2.1版本添加 ------------- begin
     */
    /**
     * 在线广播开始
     */
    public static final String EVENT_ID_BROADCAST_START_LISTEN = "101014";
    /**
     * 在线广播结束
     */
    public static final String EVENT_ID_BROADCAST_END_LISTEN = "101015";
    /**
     * 传统广播播放中
     */
    public static final String EVENT_ID_BROADCAST_PLAYING = "101018";
    /**
     * 直播开始
     */
    public static final String EVENT_ID_LIVING_START_LISTEN = "101019";
    /**
     * 直播结束
     */
    public static final String EVENT_ID_LIVING_END_LISTEN = "101020";

    /**
     * 播放器操作 当用户点击上一首（专辑、在线广播）、下一首、播放、播放后暂停、列表操作时
     */
    public static final String EVENT_ID_PLAYER_UI_CONTROL = "300006";
    /**
     * 点击/取消订阅
     */
    public static final String EVENT_ID_SUBSCIBE = "300005";

    /**
     * 正反馈
     */
    public static final String EVENT_ID_PLUS_FEEDBACK = "300032";
    /**
     * 负反馈
     */
    public static final String EVENT_ID_MINUS_FEEDBACK = "300033";
    /**
     * 直播留言
     */
    public static final String EVENT_ID_LIVING_LEAVE_MESSAGE = "300031";
    /**
     * 播放音质选择
     */
    public static final String EVENT_ID_TONE_SELECT = "300034";
    /**
     * 登录
     */
    public static final String EVENT_ID_LOGIN = "300013";
    /**
     * 推荐展示
     */
    public static final String EVENT_ID_RECOMMEND_SHOW = "300040";
    /**
     * 推荐点击
     */
    public static final String EVENT_ID_RECOMMEND_SELECT = "300041";
    /**
     * --------------- end
     */

    /**
     * 页面曝光 290版本
     */
    public static final String EVENT_ID_PAGE_SHOW = "210004";
    /**
     * 内容曝光 290版本
     */
    public static final String EVENT_ID_CONTENT_SHOW = "210005";
    /**
     * 内容点击 290版本
     */
    public static final String EVENT_ID_CONTENT_CLICK = "310001";
    /**
     * 按钮点击 290版本
     */
    public static final String EVENT_ID_BUTTON_CLICK = "310002";

    /**
     * 按钮曝光和点击 2120
     */
    public static final String EVENT_ID_BUTTON_EXPOSURE_CLICK = "330003";

    /**
     * 弹窗曝光 2120
     */
    public static final String EVENT_ID_DIALOG_EXPOSURE = "330004";

    /**
     * 播放详情播单item的曝光点击事件 2120
     */
    public static final String EVENT_ID_PLAYLIST_EXPOSURE_CLICK = "330005";

    /**
     * 点击消息设置语音播报开关 2110
     */
    public static final String PAGE_ID_SETTING_VIOCE_SWITCH = "300035";

    /**
     * 消息泡泡内容曝光 2110
     */
    public static final String PAGE_ID_MESSAGE_SHOW = "220015";
    /**
     * 消息泡泡内容点击 2110
     */
    public static final String PAGE_ID_MESSAGE_CLICK = "320001";
    /**
     * 首页组件点击、曝光事件 2120
     */
    public static final String EVENT_COMPONENT_SHOW_AND_CLICK = "330001";
    /**
     * app内页面滑动 2120
     */
    public static final String EVENT_PAGE_SLIDE = "330002";


    public static final String VALUE_OS_ANDROID = "android";
    /**
     * 离线
     */
    public static final String PLAY_TYPE_OFF_LINE = "0";
    /**
     * 在线
     */
    public static final String PLAY_TYPE_ON_LINE = "1";

    /**
     * 播放内容方式获取
     * （1：播放器自动切换；2: 其他；3：手动（点击）；4：语音；5：方控）
     */
    // 播放器自动切换
    public static final String PLAY_CHANGE_BY_AUTO = "1";
    // 其他
    public static final String PLAY_CHANGE_BY_OTHER = "2";
    // 手动（点击）
    public static final String PLAY_CHANGE_BY_CLICK = "3";
    // 语音
    public static final String PLAY_CHANGE_BY_SPEECH = "4";
    // 方控
    public static final String PLAY_CHANGE_BY_HANDWARE_CONTROL = "5";

    /**
     * 内部播放器
     */
    public static final String POSITION_INNER_APP = "1";
    /**
     * 外部播放器
     */
    public static final String POSITION_OUT_APP = "2";

    //1语音点播；2搜索结果选择；3其他
    public static final String COTENT_BY_AUDIO = "1";
    public static final String COTENT_BY_SEARCH = "2";
    public static final String COTENT_BY_OTHER = "3";
    public static final String COTENT_BY_BREAKPOINT = "4";
    public static final String COTENT_BY_ONEKEY = "5";

    // 0、未知，1、移动，2、联通，3、电信
    public static final String CARRIER_UN_KNOW = "0";
    public static final String CARRIER_YIDONG = "1";
    public static final String CARRIER_LIANTONG = "2";
    public static final String CARRIER_DIANXIN = "3";

    /**
     * 首次收听. 开机收听
     */
    public static final String FIRST_LISTEN = "1";

    /**
     * 当前通过qqapi接口获取的内容（排行榜等）单曲id为qqapi
     */
    public static final String QQ_API_AUDIO_ID = "qqapi";
    /**
     * QQ音乐电台（一人一首招牌歌等）中单曲，上报的audioid 为 qqradio
     */
    public static final String QQ_RADIO_AUDIO_ID = "qqradio";

    /**
     * 更新数据上报需要的信息天数
     */
    public static final int UPDATE_REPORT_INFO_MAX_DAY = 7;

    /**
     * 普通数据上报类型
     */
    public static final int REPORT_EVENT_NORMAL = 0;
    /**
     * 立即上报类型
     */
    public static final int REPORT_EVENT_RIGHT_NOW = 1;

    /**
     * 专辑
     */
    public static final int SOURCE_TYPE_ALBUM = 1;
    /**
     * 在线广播
     */
    public static final int SOURCE_TYPE_BROADCAST = 2;
    /**
     * 直播
     */
    public static final int SOURCE_TYPE_LIVING = 3;
    /**
     * 听电视
     */
    public static final int SOURCE_TYPE_TV = 2;

    /**
     * 竖屏
     */
    public static final String ORIENTATION_PORTRAIT = "1";

    /**
     * 横屏
     */
    public static final String ORIENTATION_LANDSCAPE = "0";


    /**
     * 弹窗DialogId
     * 启用版本：2120
     */
    public static final String DIALOG_ID_PRIVACY_POLICY = "1";  //隐私协议弹窗
    public static final String DIALOG_ID_MESSAGE_BUBBLE = "2";  //消息泡泡
    public static final String DIALOG_ID_VIP_ORDER = "3";  //vip购买
    public static final String DIALOG_ID_ALBUM_ORDER = "4";  //专辑购买
    public static final String DIALOG_ID_AUDIO_ORDER = "5";  //单曲购买
    public static final String DIALOG_ID_ADVERT = "6";  //广告弹窗
    public static final String DIALOG_ID_LIVE_ROOM_MERCHANDISE = "7";  //直播间商品
    public static final String DIALOG_ID_LIVE_ROOM_MERCHANDISE_PURCHASE = "8";  //直播间付费弹窗
    public static final String DIALOG_ID_LIVE_ROOM_GIFT_LIST = "9";  //直播间礼物列表
    public static final String DIALOG_ID_LIVE_ROOM_SEND_MESSAGE = "10";  //直播间留言
    public static final String DIALOG_ID_POSTS_PUBLISH = "11";  //贴子发布
    public static final String DIALOG_ID_LIVE_ROOM_NOTICE = "12";  //直播间公告

    /**
     * 控制方式
     */
    public static final String CONTROL_TYPE_SCREEN = "1";    //屏幕
    public static final String CONTROL_TYPE_VOICE = "2";      //语音
    public static final String CONTROL_TYPE_STEERING_WHEEL_CONTROLLER = "3";      //方控
    public static final String CONTROL_TYPE_OTHER = "4";      //其他
}
