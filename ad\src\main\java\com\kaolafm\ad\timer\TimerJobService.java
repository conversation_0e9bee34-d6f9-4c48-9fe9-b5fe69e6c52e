package com.kaolafm.ad.timer;

import android.app.job.JobParameters;
import android.app.job.JobService;
import android.os.Build;
import android.os.PersistableBundle;
import androidx.annotation.RequiresApi;

/**
 * <AUTHOR>
 * @date 2020-02-26
 */
@RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
public class TimerJobService extends JobService {

    public TimerJobService() {
    }

    @Override
    public boolean onStartJob(JobParameters params) {
        if (params != null) {
            JobTimer timer = (JobTimer) TimedAdvertManager.getInstance().getTimer();
            if (timer != null) {
                PersistableBundle bundle = params.getExtras();
                timer.expose(bundle.getInt("id", -1), bundle.getInt("time", -1));
            }
        }
        return false;
    }

    @Override
    public boolean onStopJob(JobParameters params) {
        return false;
    }
}
