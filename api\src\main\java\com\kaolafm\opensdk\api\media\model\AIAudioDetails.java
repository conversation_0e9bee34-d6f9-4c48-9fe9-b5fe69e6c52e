package com.kaolafm.opensdk.api.media.model;

import android.os.Parcel;

import com.google.gson.annotations.SerializedName;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: AudioDetails.java                                               
 *                                                                  *
 * Created in 2018/8/13 上午10:17                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class AIAudioDetails extends AudioDetails {
    /**
     * 时间显示使用默认方式：详细显示
     */
    public static final int TIME_TYPE_DEFAULT = 0;
    /**
     * 时间显示使用简单方式：不显示具体时间
     */
    public static final int TIME_TYPE_SIMPLICITY = 1;
    /**
     * 时间显示隐藏
     */
    public static final int TIME_TYPE_HIDE = 2;

    /**
     * 时间显示类型
     * 0-默认 1-不显示具体时间 2-隐藏
     */
    @SerializedName("timeType")
    private int timeType = TIME_TYPE_DEFAULT;

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        super.writeToParcel(dest, flags);
        dest.writeInt(this.timeType);
    }

    public int getTimeType() {
        return timeType;
    }

    public void setTimeType(int timeType) {
        this.timeType = timeType;
    }

    protected AIAudioDetails(Parcel in) {
        super(in);
        this.timeType = in.readInt();
    }

    public static final Creator<AIAudioDetails> CREATOR = new Creator<AIAudioDetails>() {
        @Override
        public AIAudioDetails createFromParcel(Parcel source) {
            return new AIAudioDetails(source);
        }

        @Override
        public AIAudioDetails[] newArray(int size) {
            return new AIAudioDetails[size];
        }
    };
}
