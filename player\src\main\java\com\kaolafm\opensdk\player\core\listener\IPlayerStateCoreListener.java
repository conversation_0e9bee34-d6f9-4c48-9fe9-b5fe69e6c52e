package com.kaolafm.opensdk.player.core.listener;

/**
 * <AUTHOR> on 2019-05-28.
 */

public interface IPlayerStateCoreListener {

    void onIdle(String url);

    void onPlayerPreparingComplete(String url);

    void onPlayerPreparing(String url);

    void onPlayerPlaying(String url);

    void onPlayerPaused(String url);

    void onProgress(String url, long progress, long total);

    void onPlayerFailed(String url, int what, int extra, String dnsAddress);

    void onPlayerEnd(String url);

    void onSeekStart(String url);

    void onSeekComplete(String url);

    void onBufferingStart(String url);

    void onBufferingEnd(String url);

    /**
     * ijk 软广告添加
     *
     * @param url
     * @param position
     * @param id
     */
    void onInteractionFired(String url, int position, int id);
}
