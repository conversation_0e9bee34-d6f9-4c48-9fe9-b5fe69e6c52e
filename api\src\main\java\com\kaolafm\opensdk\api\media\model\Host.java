package com.kaolafm.opensdk.api.media.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

/**
 * 主持人信息
 */
public class Host implements Parcelable {
        /**
         * name : 小胆
         * des : 东北逆生长帅哥，前东北DJ大佬，彩铃天王！叼根烟能讲26个小时段子，活着的本身就是一个笑话接着一个笑话。
         * img : http://img.kaolafm.net/mz/images/201604/bc48d175-f018-45ef-be01-18ae2becff0f/default.jpg
         */

        /** 支持人名称*/
        @SerializedName("name")
        private String name;

        /** 主持人描述*/
        @SerializedName("des")
        private String des;

        /** 主持人头像*/
        @SerializedName("img")
        private String img;


    protected Host(Parcel in) {
        name = in.readString();
        des = in.readString();
        img = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(name);
        dest.writeString(des);
        dest.writeString(img);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<Host> CREATOR = new Creator<Host>() {
        @Override
        public Host createFromParcel(Parcel in) {
            return new Host(in);
        }

        @Override
        public Host[] newArray(int size) {
            return new Host[size];
        }
    };

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public static Creator<Host> getCREATOR() {
        return CREATOR;
    }

    @Override
    public String toString() {
        return "Host{" +
                "name='" + name + '\'' +
                ", des='" + des + '\'' +
                ", img='" + img + '\'' +
                '}';
    }
}