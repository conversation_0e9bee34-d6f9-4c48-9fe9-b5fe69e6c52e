package com.kaolafm.opensdk.api.broadcast;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: BroadcastArea.java                                               
 *                                                                  *
 * Created in 2018/8/14 上午10:57                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class BroadcastArea implements Parcelable {


    /**
     * name : 北京
     * id : 1
     */

    @SerializedName("id")
    private int id;

    @SerializedName("name")
    private String name;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.id);
        dest.writeString(this.name);
    }

    public BroadcastArea() {
    }

    protected BroadcastArea(Parcel in) {
        this.id = in.readInt();
        this.name = in.readString();
    }

    public static final Creator<BroadcastArea> CREATOR = new Creator<BroadcastArea>() {
        @Override
        public BroadcastArea createFromParcel(Parcel source) {
            return new BroadcastArea(source);
        }

        @Override
        public BroadcastArea[] newArray(int size) {
            return new BroadcastArea[size];
        }
    };
}
