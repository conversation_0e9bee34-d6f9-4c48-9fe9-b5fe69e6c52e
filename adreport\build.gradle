apply plugin: 'com.android.library'
apply plugin: 'org.greenrobot.greendao'

def and = rootProject.ext.android

def VERSION_CODE = and.versionCode
def VERSION_NAME = and.versionName

android {
    compileSdkVersion and.compileSdkVersion
    buildToolsVersion and.buildToolsVersion


    defaultConfig {
        minSdkVersion and.minSdkVersion
        targetSdkVersion and.targetSdkVersion
        versionCode 1
        versionName "1.0.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        ndk {
            abiFilter("arm64-v8a")
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility = and.javaSourceVersion
        targetCompatibility = and.javaTargetVersion
    }

    //配置数据库相关信息
    greendao {
        schemaVersion 1 //数据库版本号
        daoPackage 'com.kaolafm.ad.report.db.greendao'
        targetGenDir 'src/main/java'//设置DaoMaster、DaoSession、Dao目录
    }

    lintOptions {
        abortOnError false
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    compileOnly 'org.greenrobot:greendao:3.3.0'
    compileOnly project(':core')
}

task adReportMakeJar(type: Copy) {
    //删除存在的
    delete 'build/libs/adreport-sdk-'+android.defaultConfig.versionName+'+.jar'
    //设置拷贝的文件
    from('build/intermediates/packaged-classes/release/')
    //打进jar包后的文件目录
    into('build/libs/')
    //将classes.jar放入build/libs/目录下
    //include ,exclude参数来设置过滤
    //（我们只关心classes.jar这个文件）
    include('classes.jar')
    //重命名
    rename ('classes.jar', 'adreport-sdk-'+android.defaultConfig.versionName+'.jar')
}

adReportMakeJar.dependsOn(build)
