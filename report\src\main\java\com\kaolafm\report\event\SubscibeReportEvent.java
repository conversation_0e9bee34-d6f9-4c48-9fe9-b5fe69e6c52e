package com.kaolafm.report.event;

import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> on 2019-07-23.
 */

public class SubscibeReportEvent extends BaseReportEventBean {
    public static final String SUBSCRIBE_CANCLE = "0";
    public static final String SUBSCRIBE_YES = "1";

    public static final String POSITION_PLAY_BAR = "1"; //播放条（对应在线电台版底部播放器）
    public static final String POSITION_PLAY_FRAGEMNT = "2";    //全屏播放器（对应在线电台版播放详情页）
    public static final String POSITION_WIDGET = "3";   //widget
    public static final String POSITION_SUBSCRIBE_LIST_PAGE = "4";  //订阅列表页
    public static final String POSITION_PLAY_LIST = "5";    //播放列表

    public static final String SUBSCRIBE_TYPE_ALBUM = "1";
    public static final String SUBSCRIBE_TYPE_RADIO = "2";
    public static final String SUBSCRIBE_TYPE_BROADCAST = "3";
    public static final String SUBSCRIBE_TYPE_MUSIC = "4";

    /**
     * 0：取消订阅；1：订阅
     */
    private String type;
    /**
     * 订阅类型 1：专辑；2：电台；3：在线广播；4：音乐电台（第三方内容源）
     */
    private String subscribetype;
    /**
     * 点击位置 1：播放条；2：全屏播放器；3：widget
     */
    private String position;
    /**
     * 专辑取专辑id，电台取电台id，在线广播取在线广播id，音乐取音乐电台id
     */
    private String radioid;

    public SubscibeReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_SUBSCIBE);
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSubscribetype() {
        return subscribetype;
    }

    public void setSubscribetype(String subscribetype) {
        this.subscribetype = subscribetype;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getRadioid() {
        return radioid;
    }

    public void setRadioid(String radioid) {
        this.radioid = radioid;
    }
}
