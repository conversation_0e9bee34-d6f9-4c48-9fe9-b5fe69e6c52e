package com.kaolafm.opensdk.player.logic.playlist;

import android.text.TextUtils;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.live.LiveRequest;
import com.kaolafm.opensdk.api.live.model.LiveInfoDetail;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.item.LivePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playlist.util.PlayListUtils;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerPreconditions;

/**
 * <AUTHOR> on 2019/3/27.
 */

public class LivePlayListControl extends BasePlayListControl {
    @Override
    public void initPlayList(PlayerBuilder playerBuilder, final IPlayListGetListener iPlayListGetListener) {
        super.initPlayList(playerBuilder, iPlayListGetListener);
        long liveId = string2Long(playerBuilder.getId());
        LivePlayItem livePlayItem = new LivePlayItem();
        livePlayItem.setAudioId(liveId);

        new LiveRequest().getLiveInfo(liveId+"", new HttpCallback<LiveInfoDetail>() {
            @Override
            public void onSuccess(LiveInfoDetail liveInfoDetail) {
                if (PlayerPreconditions.checkNull(liveInfoDetail)) {
                    notifyPlayListGetError(iPlayListGetListener, livePlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_NULL, -1);
                    notifyPlayListChangeError(livePlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_NULL, -1);
                    return;
                }
                LivePlayItem livePlayItem1 = (LivePlayItem) PlayListUtils.liveInfoToPlayItem(liveInfoDetail);
                if (PlayerPreconditions.checkNull(livePlayItem1)) {
                    notifyPlayListGetError(iPlayListGetListener, livePlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_NULL, -1);
                    notifyPlayListChangeError(livePlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_NULL, -1);
                    return;
                }
                if(TextUtils.isEmpty(livePlayItem1.getPlayUrl())){
                    notifyPlayListGetError(iPlayListGetListener, livePlayItem1, PlayerConstants.ERROR_CODE_PLAY_LIST_URL_LIVE_NULL, livePlayItem1.getStatus());
                    notifyPlayListChangeError(livePlayItem1, PlayerConstants.ERROR_CODE_PLAY_LIST_URL_LIVE_NULL, livePlayItem1.getStatus());
                    return;
                }
                if(livePlayItem1.getStatus()!=1){
                    notifyPlayListGetError(iPlayListGetListener, livePlayItem1, PlayerConstants.ERROR_CODE_PLAY_LIST_URL_LIVE_STATUS, livePlayItem1.getStatus());
                    notifyPlayListChangeError(livePlayItem1, PlayerConstants.ERROR_CODE_PLAY_LIST_URL_LIVE_STATUS, livePlayItem1.getStatus());
                    return;
                }
                mPlayItemArrayList.add(livePlayItem1);
                notifyPlayListGet(iPlayListGetListener, mPlayItemArrayList.get(0), mPlayItemArrayList);
                notifyPlayListChange(mPlayItemArrayList);
            }

            @Override
            public void onError(ApiException e) {
                notifyPlayListGetError(iPlayListGetListener, livePlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_SERVER, e.getCode());
                notifyPlayListChangeError( livePlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_SERVER, e.getCode());
            }
        });
    }


    private long string2Long(String strValue) {
        long longValue = 0;
        if (!StringUtil.isEmpty(strValue)) {
            try {
                longValue = Long.parseLong(strValue);
            } catch (Exception e) {

            }
        }
        return longValue;
    }
}
