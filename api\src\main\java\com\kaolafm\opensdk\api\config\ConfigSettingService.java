package com.kaolafm.opensdk.api.config;

import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.KaolaApiConstant;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;

public interface ConfigSettingService {

    /**
     * config配置信息接口
     * @return
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_CONFIG_SETTINGS)
    Single<BaseResult<ConfigSettingOption>> getConfigSwitchInfos();
    /**
     * config配置信息接口
     * @return
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_CONFIG_FILING_INFO)
    Single<BaseResult<FlingBean>> getConfigFilingInfos();
}
