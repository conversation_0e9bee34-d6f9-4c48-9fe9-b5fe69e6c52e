package com.kaolafm.opensdk.http.download.engine;

import android.text.TextUtils;

import com.kaolafm.opensdk.http.download.DownloadProgress;
import com.kaolafm.opensdk.http.download.DownloadRequest;
import com.kaolafm.opensdk.utils.HttpUtil;

import java.io.File;

import io.reactivex.Flowable;
import okhttp3.ResponseBody;
import retrofit2.Response;

/**
 * <AUTHOR> @date 2020-02-11
 */
public abstract class BaseDownloadEngine implements DownloadEngine {

    private boolean alreadyDownloaded = false;
    protected File file;
    protected File shadow;

    @Override
    public Flowable<DownloadProgress> download(DownloadRequest request, Response<ResponseBody> response) {
        if (TextUtils.isEmpty(request.fileName)) {
            request.fileName = HttpUtil.getFileName(response);
        }
        beforeDownload(request, response);
        if (alreadyDownloaded) {
            long contentLength = HttpUtil.getContentLength(response);
            return Flowable.just(new DownloadProgress(contentLength, contentLength));
        }
        return startDownload(request, response);
    }

    protected void beforeDownload(DownloadRequest request, Response<ResponseBody> response) {
        file = request.getFile();
        shadow = request.getShadow();
        File dir = request.getDir();
        if (!dir.exists() || !dir.isDirectory()) {
            dir.mkdirs();
        }
        if (file.exists()) {
            if (file.length() == HttpUtil.getContentLength(response)) {
                alreadyDownloaded = true;
            } else {
                file.delete();
                recreate();
            }
        } else {
            create();
        }
    }

    /**
     * 重新创建下载文件
     */
    protected abstract void recreate();

    /**
     * 创建下载文件
     */
    protected abstract void create();

    /**
     * 开始下载
     * @param request
     * @param response
     * @return
     */
    abstract Flowable<DownloadProgress> startDownload(DownloadRequest request, Response<ResponseBody> response);



}
