package com.kaolafm.opensdk.api.live.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 直播间功能按钮
 * <AUTHOR>
 * @date 2023-03-08
 */
public class LiveAbility implements Parcelable {
    //发送消息
    public static final int LIVE_ABILITY_SEND_MESSAGE = 1;
    //打赏
    public static final int LIVE_ABILITY_REWARD = 2;
    //购物
    public static final int LIVE_ABILITY_SHOPPING = 3;

    private Integer type;// 类型:1-发送消息;2-直播间打赏功能;3-购物功能
    private String name;// 功能名称
    private String img;// 功能图片

    public LiveAbility() {
    }

    public LiveAbility(Integer type, String name, String img) {
        this.type = type;
        this.name = name;
        this.img = img;
    }

    protected LiveAbility(Parcel in) {
        if (in.readByte() == 0) {
            type = null;
        } else {
            type = in.readInt();
        }
        name = in.readString();
        img = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        if (type == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(type);
        }
        dest.writeString(name);
        dest.writeString(img);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<LiveAbility> CREATOR = new Creator<LiveAbility>() {
        @Override
        public LiveAbility createFromParcel(Parcel in) {
            return new LiveAbility(in);
        }

        @Override
        public LiveAbility[] newArray(int size) {
            return new LiveAbility[size];
        }
    };

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public static Creator<LiveAbility> getCREATOR() {
        return CREATOR;
    }
}
