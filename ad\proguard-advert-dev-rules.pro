# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
-include ./proguard-advert-rules.pro
-include ../core/proguard-core-dev-rules.pro
# 这句话能够使我们的项目混淆后产生映射文件
# 包含有类名->混淆后类名的映射关系
-verbose
-printmapping proguardMapping-ad.txt

-keepclasseswithmembernames class com.kaolafm.ad.AdConstant {*;}
-keep class com.kaolafm.ad.expose.AdvertisingReporter {*;}
-keep class com.kaolafm.ad.report.AdReportAgent {*;}
-keep class com.kaolafm.ad.report.AdReportAgent$EventType {*;}
-keepclasseswithmembernames class com.kaolafm.ad.report.bean.** {*;}
