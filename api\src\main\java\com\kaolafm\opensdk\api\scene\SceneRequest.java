package com.kaolafm.opensdk.api.scene;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import java.util.Map;
import okhttp3.MediaType;
import okhttp3.RequestBody;

/**
 * 场景推荐
 *
 * <AUTHOR>
 **/
public class SceneRequest extends BaseRequest {


    private SceneService mService;

    public SceneRequest() {
        mService = obtainRetrofitService(SceneService.class);
    }

    /**
     * 获取推送信息
     *
     * @param callback 选填 回调
     * @param scenes   必填 场景类型参数
     */
    public void getSceneInfo(HttpCallback<SceneInfo> callback, Scene... scenes) {
        getSceneInfo(callback, null, scenes);
    }


    /**
     * 获取推送信息,带附件信息
     *
     * @param callback 选填 回调
     * @param extInfo  选填 额外信息
     * @param scenes   必填 场景类型参数
     */
    public void getSceneInfo(HttpCallback<SceneInfo> callback, Map<String, String> extInfo, Scene... scenes) {
        if (scenes != null && scenes.length > 0) {
            SceneParam param = new SceneParam();

            for (int i = 0; i < scenes.length; i++) {
                param.addScene(scenes[i]);
            }

            param.setExtInfo(extInfo);

            String body = mGsonLazy.get().toJson(param);
            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);

            doHttpDeal(mService.getSceneInfo(requestBody), BaseResult::getResult, callback);
        } else {
            if (callback != null) {
                callback.onError(new ApiException("参数scenes为空."));
            }
        }
    }
}
