package com.kaolafm.opensdk.demo.login;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.webkit.WebSettings;
import android.webkit.WebSettings.LayoutAlgorithm;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;
import android.widget.TextView;
import butterknife.BindView;
import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.api.music.qq.QQMusicRequest;
import com.kaolafm.opensdk.api.music.qq.model.TencentUserInfo;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

/**
 * QQ音乐账号登录
 *
 * <AUTHOR> Yan
 * @date 2018/9/17
 */

public class QQMusicLoginActivity extends BaseActivity {

    private static final String SP_NAME = "DEMO";

    private static final String SP_KEY_USER_INFO = "TencentUserInfo";

    @BindView(R.id.iv_qqmusic_user_image)
    ImageView mIvQqmusicUserImage;

    @BindView(R.id.tv_qqmusic_user_info)
    TextView mTvQqmusciUserInfo;

    @BindView(R.id.web_qqmusic_qr_code)
    WebView mWebQqmusicQrCode;

    @Override
    public int getLayoutId() {
        return R.layout.activity_qqmusic_login;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        setTitle("微信登录");
        WebSettings settings = mWebQqmusicQrCode.getSettings();
        settings.setDisplayZoomControls(false);
        settings.setSupportZoom(false);
        settings.setDomStorageEnabled(true);
        settings.setCacheMode(WebSettings.LOAD_NO_CACHE);
        settings.setLoadsImagesAutomatically(true);
        settings.setUseWideViewPort(true);
        settings.setLoadWithOverviewMode(true);
        settings.setLayoutAlgorithm(LayoutAlgorithm.SINGLE_COLUMN);
        settings.setJavaScriptEnabled(true);
        mWebQqmusicQrCode.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                String code = StringUtil.getRequestParams(url).get("code");
                loginByCode(code);
                return true;
            }
        });

    }

    private void loginByCode(String code) {
        new QQMusicRequest().loginQQMusicWithWechatAuthorizationCode(code, new HttpCallback<TencentUserInfo>() {
            @Override
            public void onSuccess(TencentUserInfo tencentUserInfo) {
                saveInfo(tencentUserInfo);
                showUserInfo(tencentUserInfo);
            }

            @Override
            public void onError(ApiException exception) {
                showToast("登录错误， 错误码：" + exception.getCode() + "->" + exception.getMessage());
            }
        });
    }

    private void showUserInfo(TencentUserInfo tencentUserInfo) {
        if (tencentUserInfo != null) {
            Glide.with(this).load(tencentUserInfo.getHeadImg()).into(mIvQqmusicUserImage);
            StringBuilder sb = new StringBuilder();
            sb.append("昵称：").append(tencentUserInfo.getNickname()).append("\r\n")
                    .append("性别：").append(tencentUserInfo.getSex() == 1?"男":"女").append("\r\n")
                    .append("国家：").append(tencentUserInfo.getCountry()).append("\r\n")
                    .append("省：").append(tencentUserInfo.getProvince()).append("\r\n")
                    .append("城市：").append(tencentUserInfo.getCity());
            mTvQqmusciUserInfo.setText(sb);
        }
    }

    private void saveInfo(TencentUserInfo tencentUserInfo) {
        if (tencentUserInfo != null) {
            SharedPreferences sp = getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
            Editor edit = sp.edit();
            edit.putString(SP_KEY_USER_INFO, new Gson().toJson(tencentUserInfo));
            edit.apply();
        }
    }
    private void clearInfo() {
        SharedPreferences sp = getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().clear().apply();
    }

    @Override
    public void initData() {
        boolean login = AccessTokenManager.getInstance().getQQMusicAccessToken().isLogin();
        if (!login) {
            new QQMusicRequest().getWeChatQRCodeForLogin(new HttpCallback<String>() {
                @Override
                public void onSuccess(String s) {
                    if (!TextUtils.isEmpty(s)) {
                        mWebQqmusicQrCode.loadUrl(s);
                    } else {
                        showToast("获取二维码失败");
                    }
                }

                @Override
                public void onError(ApiException exception) {
                    showToast("获取二维码错误，错误码：" + exception.getCode() + "->" + exception.getMessage());
                }
            });
        }else {
            SharedPreferences sp = getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
            String string = sp.getString(SP_KEY_USER_INFO, "");
            TencentUserInfo tencentUserInfo = new Gson().fromJson(string, TencentUserInfo.class);
            showUserInfo(tencentUserInfo);
        }
    }

    public void logout(View view) {
        AccessTokenManager.getInstance().logoutQQMusic();
        clearInfo();
        finish();
    }
}
