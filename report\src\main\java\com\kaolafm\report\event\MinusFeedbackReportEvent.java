package com.kaolafm.report.event;

import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> on 2019-07-23.
 */

public class MinusFeedbackReportEvent extends BaseReportEventBean {
    public static final String POSITION_PLAY_BAR = "1";
    public static final String POSITION_PLAY_FRAGEMNT = "2";

    private String audioid;
    private String radioid;
    private String albumid;
    /**
     * 正反馈追踪号
     */
    private String remarks11;
    /**
     * 1：播放条；2：全屏播放器；
     */
    private String position;

    public MinusFeedbackReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_MINUS_FEEDBACK);
    }

    public String getAudioid() {
        return audioid;
    }

    public void setAudioid(String audioid) {
        this.audioid = audioid;
    }

    public String getRadioid() {
        return radioid;
    }

    public void setRadioid(String radioid) {
        this.radioid = radioid;
    }

    public String getAlbumid() {
        return albumid;
    }

    public void setAlbumid(String albumid) {
        this.albumid = albumid;
    }

    public String getRemarks11() {
        return remarks11;
    }

    public void setRemarks11(String remarks11) {
        this.remarks11 = remarks11;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }
}
