package com.kaolafm.opensdk.api.live.model;

import androidx.room.ColumnInfo;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

public class LiveDetails implements Serializable {
    /**
     * liveName : 每天·一点声音
     * liveId : 1445805850
     * livePic : http://img.kaolafm.net/mz/images/201812/8ed3c42a-37de-44a7-aaf4-11c0a93e17ca/default.jpg
     * liveDesc : test
     * programId : 1514563012
     * programName : 奥迪朝圣之旅
     * programPic : http://img.kaolafm.net/mz/images/201812/ad0e48f2-6fb2-44ea-bfdd-dea65d74e446/default.jpg
     * programDesc : 奥迪朝圣之旅
     * begintime : 2018-12-24 00:00:00
     * endtime : 2020-01-30 00:00:00
     * status : 1
     * showStartTime : 直播中
     * period : 1
     * comperes : 恒宇、依言
     * guests :
     * albumId : 0
     * liveUrl : http://play.c.l.kaolafm.net/ugc/1445805850_1514563012/playlist.m3u8
     * shareUrl : http://m.kaolafm.com/share/liveplay/index.html
     * onLineNum : 0
     * isCanSubscribe : 0
     * isAlreadySubscribe : 0
     * subscribeNum : 0
     * bgColor :
     * programLikedNum : 0
     * programSharedNum : 0
     * programFollowedNum : 6
     * uid : 2758502
     * avatar : http://img.kaolafm.net/mz/images/201609/96f43582-5bb3-4859-b722-624cc848e11b/default.jpg
     * isVanchor : 1
     * gender : 0
     * backLiveUrl :
     * timeLength :
     * startTime : 1545580800000
     * finshTime : 1580313600000
     * serveTime : 1563850278232
     * duration : 0
     * canPlayBack : 0
     * pushHost : pub.c.l.kaolafm.net
     * accessKey : ugc
     * lockType : 0
     * isAlreadyFollowed : 0
     * roomId : 59814518
     * rtmpUrl : rtmp://play.c.l.kaolafm.net/ugc/1445805850_1514563012
     */

    @SerializedName("liveName")
    @ColumnInfo(name = "liveName")
    public String liveName;

    @SerializedName("liveId")
    @ColumnInfo(name = "liveId")
    public long liveId;

    @SerializedName("livePic")
    @ColumnInfo(name = "livePic")
    public String livePic;

    @SerializedName("liveDesc")
    @ColumnInfo(name = "liveDesc")
    public String liveDesc;

    @SerializedName("programId")
    @ColumnInfo(name = "programId")
    public long programId;

    @SerializedName("programName")
    @ColumnInfo(name = "programName")
    public String programName;

    @SerializedName("programPic")
    @ColumnInfo(name = "programPic")
    public String programPic;


    @SerializedName("programDesc")
    @ColumnInfo(name = "programDesc")
    public String programDesc;

    @SerializedName("begintime")
    @ColumnInfo(name = "begintime")
    public String begintime;

    @SerializedName("endtime")
    @ColumnInfo(name = "endtime")
    public String endtime;


    @SerializedName("status")
    @ColumnInfo(name = "ld_status")
    public int status;

    @SerializedName("showStartTime")
    @ColumnInfo(name = "showStartTime")
    public String showStartTime;


    @SerializedName("period")
    @ColumnInfo(name = "ld_period")
    public int period;

    @SerializedName("comperes")
    @ColumnInfo(name = "ld_comperes")
    public String comperes;

    @SerializedName("guests")
    @ColumnInfo(name = "ld_guests")
    public String guests;

    @SerializedName("albumId")
    @ColumnInfo(name = "albumId")
    public long albumId;

    @SerializedName("liveUrl")
    @ColumnInfo(name = "liveUrl")
    public String liveUrl;


    @SerializedName("shareUrl")
    @ColumnInfo(name = "shareUrl")
    public String shareUrl;

    @SerializedName("onLineNum")
    @ColumnInfo(name = "onLineNum")
    public long onLineNum;

    @SerializedName("isCanSubscribe")
    @ColumnInfo(name = "isCanSubscribe")
    public long isCanSubscribe;


    @SerializedName("isAlreadySubscribe")
    @ColumnInfo(name = "isAlreadySubscribe")
    public long isAlreadySubscribe;

    @SerializedName("subscribeNum")
    @ColumnInfo(name = "subscribeNum")
    public long subscribeNum;

    @SerializedName("bgColor")
    @ColumnInfo(name = "bgColor")
    public String bgColor;

    @SerializedName("programLikedNum")
    @ColumnInfo(name = "programLikedNum")
    public long programLikedNum;

    @SerializedName("programSharedNum")
    @ColumnInfo(name = "programSharedNum")
    public long programSharedNum;

    @SerializedName("programFollowedNum")
    @ColumnInfo(name = "programFollowedNum")
    public long programFollowedNum;


    @SerializedName("uid")
    @ColumnInfo(name = "ld_uid")
    public long uid;

    @SerializedName("avatar")
    @ColumnInfo(name = "ld_avatar")
    public String avatar;

    @SerializedName("isVanchor")
    @ColumnInfo(name = "isVanchor")
    public int isVanchor;

    @SerializedName("gender")
    @ColumnInfo(name = "ld_gender")
    public int gender;

    @SerializedName("backLiveUrl")
    @ColumnInfo(name = "backLiveUrl")
    public String backLiveUrl;

    @SerializedName("timeLength")
    @ColumnInfo(name = "timeLength")
    public String timeLength;

    @SerializedName("startTime")
    @ColumnInfo(name = "startTime")
    public long startTime;

    @SerializedName("finshTime")
    @ColumnInfo(name = "finshTime")
    public long finshTime;

    @SerializedName("serveTime")
    @ColumnInfo(name = "serveTime")
    public long serveTime;

    @SerializedName("duration")
    @ColumnInfo(name = "ld_duration")
    public long duration;

    @SerializedName("canPlayBack")
    @ColumnInfo(name = "canPlayBack")
    public int canPlayBack;


    @SerializedName("pushHost")
    @ColumnInfo(name = "pushHost")
    public String pushHost;

    @SerializedName("accessKey")
    @ColumnInfo(name = "accessKey")
    public String accessKey;

    @SerializedName("lockType")
    @ColumnInfo(name = "lockType")
    public int lockType;


    @SerializedName("isAlreadyFollowed")
    @ColumnInfo(name = "isAlreadyFollowed")
    public int isAlreadyFollowed;

    @SerializedName("roomId")
    @ColumnInfo(name = "roomId")
    public long roomId;

    @SerializedName("rtmpUrl")
    @ColumnInfo(name = "rtmpUrl")
    public String rtmpUrl;

    @Override
    public String toString() {
        return "LiveDetails{" +
                "liveName='" + liveName + '\'' +
                ", liveId=" + liveId +
                ", programId=" + programId +
                ", livePic='" + livePic + '\'' +
                ", liveDesc='" + liveDesc + '\'' +
                ", programName='" + programName + '\'' +
                ", programPic='" + programPic + '\'' +
                ", programDesc='" + programDesc + '\'' +
                ", begintime='" + begintime + '\'' +
                ", endtime='" + endtime + '\'' +
                ", status=" + status +
                ", showStartTime='" + showStartTime + '\'' +
                ", period=" + period +
                ", comperes='" + comperes + '\'' +
                ", guests='" + guests + '\'' +
                ", albumId=" + albumId +
                ", liveUrl='" + liveUrl + '\'' +
                ", shareUrl='" + shareUrl + '\'' +
                ", onLineNum=" + onLineNum +
                ", isCanSubscribe=" + isCanSubscribe +
                ", isAlreadySubscribe=" + isAlreadySubscribe +
                ", subscribeNum=" + subscribeNum +
                ", bgColor='" + bgColor + '\'' +
                ", programLikedNum=" + programLikedNum +
                ", programSharedNum=" + programSharedNum +
                ", programFollowedNum=" + programFollowedNum +
                ", uid=" + uid +
                ", avatar='" + avatar + '\'' +
                ", isVanchor=" + isVanchor +
                ", gender=" + gender +
                ", backLiveUrl='" + backLiveUrl + '\'' +
                ", timeLength='" + timeLength + '\'' +
                ", startTime=" + startTime +
                ", finshTime=" + finshTime +
                ", serveTime=" + serveTime +
                ", duration=" + duration +
                ", canPlayBack=" + canPlayBack +
                ", pushHost='" + pushHost + '\'' +
                ", accessKey='" + accessKey + '\'' +
                ", lockType=" + lockType +
                ", isAlreadyFollowed=" + isAlreadyFollowed +
                ", roomId=" + roomId +
                ", rtmpUrl='" + rtmpUrl + '\'' +
                '}';
    }
}