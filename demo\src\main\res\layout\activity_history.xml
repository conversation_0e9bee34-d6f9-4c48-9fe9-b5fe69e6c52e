<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <Button
        android:id="@+id/btn_clear_history"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="清空历史"
        />
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_history_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/btn_clear_history"
        />

</androidx.constraintlayout.widget.ConstraintLayout>