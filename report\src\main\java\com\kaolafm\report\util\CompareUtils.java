package com.kaolafm.report.util;

import java.util.Comparator;

/**
 * Created by <PERSON> on 2018/1/15
 */
public class CompareUtils implements Comparator<Paramaters>{
    @Override
    public int compare(Paramaters o1, Paramaters o2) {
        if (o1.getKey().compareTo(o2.getKey()) > 0) {
            return 1;
        }else if (o1.getKey().compareTo(o2.getKey()) < 0){
            return -1;
        }else{
            return 0;
        }

    }
}
