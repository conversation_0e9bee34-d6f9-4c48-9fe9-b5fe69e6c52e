package com.kaolafm.opensdk.api.login;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.account.token.KaolaAccessToken;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.login.model.Success;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.report.ReportHelper;

public class KRadioLoginRequest extends BaseRequest {
    KRadioLoginService mKRadioLoginService;

    public KRadioLoginRequest() {
        mKRadioLoginService = obtainRetrofitService(KRadioLoginService.class);
    }

    /**
     * 电话号码登录
     *
     * @param phoneNum         电话号码
     * @param verificationCode 验证码
     * @param httpCallback
     */
    public void login(String phoneNum, String verificationCode, HttpCallback<KaolaAccessToken> httpCallback) {
        doHttpDeal(mKRadioLoginService.login(phoneNum, verificationCode),
                baseResult -> {
                    KaolaAccessToken tempKaolaAccessToken = AccessTokenManager.getInstance().getKaolaAccessToken();
                    KaolaAccessToken kaolaAccessToken =  baseResult.getResult();

                    if (tempKaolaAccessToken != null && !StringUtil.isEmpty(tempKaolaAccessToken.getOpenId())) {
                        kaolaAccessToken.setOpenId(tempKaolaAccessToken.getOpenId());
                    }
                    AccessTokenManager.getInstance().setCurrentAccessToken(kaolaAccessToken);
                    ReportHelper.getInstance().initUid(kaolaAccessToken.getUserId());
                    return kaolaAccessToken;
                }, httpCallback);
    }

    /**
     * 获取验证码
     *
     * @param phoneNum     电话号码
     * @param httpCallback
     */
    public void getVerificationCode(String phoneNum, HttpCallback<Boolean> httpCallback) {
        doHttpDeal(mKRadioLoginService.getVerificationCode(phoneNum), baseResult -> {
            Success success = baseResult.getResult();
            return success != null && (Success.STATUS_SUCCESS + "").equals(success.getCode());
        }, httpCallback);
    }

    /**
     * 获取token
     * @param uuid
     * @param secretKey
     * @param httpCallback
     */
    public void getToken(String uuid, String secretKey, HttpCallback<KaolaAccessToken> httpCallback) {
        doHttpDeal(mKRadioLoginService.getToken(secretKey, uuid), BaseResult::getResult, httpCallback);
    }

    /**
     * 退出登录
     * @param secretKey
     * @param httpCallback
     */
    public void logout(String secretKey, HttpCallback<Boolean> httpCallback) {
        doHttpDeal(mKRadioLoginService.logout(secretKey), baseResult -> {
            Success success = baseResult.getResult();
            boolean isSuccess = success != null && Integer.parseInt(success.getCode()) == Success.STATUS_SUCCESS;
            if (isSuccess) {
                AccessTokenManager.getInstance().logoutKaola();
            }
            return isSuccess;
        }, httpCallback);
    }
}
