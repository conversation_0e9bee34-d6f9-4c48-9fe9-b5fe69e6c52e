package com.kaolafm.opensdk.demo;

import android.os.Handler;
import android.util.Log;

import com.kaolafm.ad.api.model.ImageAdvert;
import com.kaolafm.ad.api.model.InteractionAdvert;
import com.kaolafm.ad.expose.AdvertisingImager;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.opensdk.http.error.ApiException;

/**
 * <AUTHOR>
 * @date 2020-02-21
 */
public class AdvertImagerTest implements AdvertisingImager {
    @Override
    public void display(ImageAdvert advert) {
        Log.e("AdvertImagerTest", "display， LocalPath=: "+advert.getLocalPath());
        Log.e("AdvertImagerTest", "display: AttachImageLocalPath ="+advert.getAttachImage());
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                AdvertisingManager.getInstance().close(advert);
            }
        }, 5000);
    }

    @Override
    public void hide(ImageAdvert advert) {
        Log.e("AdvertImagerTest", "hide: "+advert);
    }

    @Override
    public void skip(ImageAdvert advert) {

    }

    @Override
    public void displayInteraction(InteractionAdvert advert) {
        Log.e("AdvertImagerTest", "displayInteraction: "+advert);
    }

    @Override
    public void hideInteraction(InteractionAdvert advert) {
    }

    @Override
    public void click(InteractionAdvert advert) {

    }

    @Override
    public void error(String adZoneId, int subtype, ApiException e) {

    }
}
