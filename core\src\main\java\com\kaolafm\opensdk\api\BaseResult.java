package com.kaolafm.opensdk.api;

import com.google.gson.annotations.SerializedName;
import com.kaolafm.opensdk.http.core.Response;

/**
 * 考拉，Kradio网络请求返回正常结果基类
 * <AUTHOR>
 * @date 2018/7/25
 */

public class BaseResult<RESULT> extends Response {

    private String requestId;

    private String serverTime;

    @SerializedName(value = "result", alternate = {"dataList"})
    private RESULT result;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getServerTime() {
        return serverTime;
    }

    public void setServerTime(String serverTime) {
        this.serverTime = serverTime;
    }

    public RESULT getResult() {
        return result;
    }

    public void setResult(RESULT result) {
        this.result = result;
    }

    @Override
    public String toString() {
        return "BaseResult{" +
                "requestId='" + requestId + '\'' +
                ", serverTime='" + serverTime + '\'' +
                ", result=" + result +
                '}'+super.toString();
    }

}
