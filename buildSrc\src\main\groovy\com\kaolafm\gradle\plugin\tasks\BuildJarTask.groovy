package com.kaolafm.gradle.plugin.tasks

import com.kaolafm.gradle.plugin.model.SDKFlavor
import com.kaolafm.gradle.plugin.utils.Util
import org.gradle.api.tasks.bundling.Jar

import javax.inject.Inject

class BuildJarTask extends Jar {

    @Inject
    BuildJarTask() {
    }

    def config(SDKFlavor flavor, String classPath) {
        archiveClassifier.set("release")
        description = "构建Jar包"
        group = Util.GROUP_NAME
        archiveFileName.set("${Util.getArchiveName(flavor)}.jar")
        destinationDirectory.set(project.file(project.buildDir.path + '/outputs/jar/'))

        from(classPath)

        //过滤R文件和BuildConfig文件
        exclude("**/BuildConfig.class", "**/BuildConfig\$*.class", "**/R.class", "**/R\$*.class")
        includeEmptyDirs = false

        //过滤指定class
        flavor.excludeClass.forEach {
            exclude(it)
        }
        //过滤指定包名下的class
        flavor.excludePackage.forEach {
            exclude("$it/**/*.class")
        }
        //仅仅打包指定的class
        flavor.includeClass.forEach {
            include(it)
        }
        //仅仅打包指定包名下的class
        flavor.includePackage.forEach {
            include("$it/**/*.class")
        }
    }

}