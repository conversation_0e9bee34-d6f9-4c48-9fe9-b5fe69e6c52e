package com.kaolafm.opensdk.api.broadcast;


import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

import java.util.List;


    public class BroadcastCityDetail implements Parcelable {
        public List<BroadcastCityDetail> dataList;
        public List<Long> errorBids;
        @SerializedName("broadcastDetails")
        public BroadcastDetails broadcastDetails;
        @SerializedName("programDetails")
        public ProgramDetails programDetails;
        @SerializedName("programDetailsList")
        public List<ProgramDetails> programDetailsList;
        protected BroadcastCityDetail(Parcel in) {
            dataList = in.createTypedArrayList(BroadcastCityDetail.CREATOR);
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeTypedList(dataList);
        }

        @Override
        public int describeContents() {
            return 0;
        }

        public static final Creator<com.kaolafm.opensdk.api.broadcast.BroadcastCityDetail> CREATOR = new Creator<com.kaolafm.opensdk.api.broadcast.BroadcastCityDetail>() {
            @Override
            public com.kaolafm.opensdk.api.broadcast.BroadcastCityDetail createFromParcel(Parcel in) {
                return new com.kaolafm.opensdk.api.broadcast.BroadcastCityDetail(in);
            }

            @Override
            public com.kaolafm.opensdk.api.broadcast.BroadcastCityDetail[] newArray(int size) {
                return new com.kaolafm.opensdk.api.broadcast.BroadcastCityDetail[size];
            }
        };
    }
