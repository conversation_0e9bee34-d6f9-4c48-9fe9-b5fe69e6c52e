package com.kaolafm.opensdk.api.music.qq.model;

import com.google.gson.annotations.SerializedName;

/**
 * 专辑信息
 * <AUTHOR>
 * @date 2018/4/23
 */

public class MusicAlbum {


    /**
     * album_desc : 前奏一响就是回忆杀！由唐季礼执导，成龙、李治廷、张艺兴主演的动作喜剧《功夫瑜伽》今日曝光了电影主题曲《美丽的神话》。电影《功夫瑜伽》讲述了成龙、李治廷、张艺兴组成的“功夫三傻”，为寻找宝藏，游遍全球历...
     * album_id : 1792942
     * album_mid : 001tWKC93ywEY6
     * album_name : 美丽的神话
     * album_translator_name :
     * company_name : 北京听见时代娱乐传媒有限公司
     * listen_num : 3851918
     * public_time : 1482768000
     * singer_id : 13
     * singer_name : 成龙
     * song_id_list : *********,
     */

    @SerializedName("album_desc")
    private String albumDesc;

    @SerializedName("album_id")
    private int albumId;

    @SerializedName("album_mid")
    private String albumMid;

    @SerializedName("album_name")
    private String albumName;

    /**
     * album_pic : http://y.gtimg.cn/music/photo_new/T002R300x300M000001dy3ES15Gya6.jpg
     * public_time : 2003-01-01 00:00:00
     * url :
     */

    @SerializedName("album_pic")
    private String albumPic;

    @SerializedName("album_translator_name")
    private String albumTranslatorName;

    @SerializedName("company_name")
    private String companyName;

    @SerializedName("listen_num")
    private int listenNum;

    @SerializedName("public_time")
    private String publicTime;

    @SerializedName("singer_id")
    private int singerId;

    @SerializedName("singer_name")
    private String singerName;

    @SerializedName("song_id_list")
    private String songIdList;

    @SerializedName("url")
    private String url;

    public String getAlbumDesc() {
        return albumDesc;
    }

    public int getAlbumId() {
        return albumId;
    }

    public String getAlbumMid() {
        return albumMid;
    }

    public String getAlbumName() {
        return albumName;
    }

    public String getAlbumPic() {
        return albumPic;
    }

    public String getAlbumTranslatorName() {
        return albumTranslatorName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public int getListenNum() {
        return listenNum;
    }

    public String getPublicTime() {
        return publicTime;
    }

    public int getSingerId() {
        return singerId;
    }

    public String getSingerName() {
        return singerName;
    }

    public String getSongIdList() {
        return songIdList;
    }

    public String getUrl() {
        return url;
    }

    public void setAlbumDesc(String albumDesc) {
        this.albumDesc = albumDesc;
    }

    public void setAlbumId(int albumId) {
        this.albumId = albumId;
    }

    public void setAlbumMid(String albumMid) {
        this.albumMid = albumMid;
    }

    public void setAlbumName(String albumName) {
        this.albumName = albumName;
    }

    public void setAlbumPic(String albumPic) {
        this.albumPic = albumPic;
    }

    public void setAlbumTranslatorName(String albumTranslatorName) {
        this.albumTranslatorName = albumTranslatorName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public void setListenNum(int listenNum) {
        this.listenNum = listenNum;
    }


    public void setPublicTime(String publicTime) {
        this.publicTime = publicTime;
    }

    public void setSingerId(int singerId) {
        this.singerId = singerId;
    }

    public void setSingerName(String singerName) {
        this.singerName = singerName;
    }

    public void setSongIdList(String songIdList) {
        this.songIdList = songIdList;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
