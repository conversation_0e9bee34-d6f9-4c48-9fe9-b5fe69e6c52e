package com.kaolafm.report.util;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.database.ConfigData;
import com.kaolafm.report.database.greendao.DaoMaster;
import com.kaolafm.report.database.greendao.DaoSession;

import org.greenrobot.greendao.database.Database;

import java.util.List;

import io.reactivex.Single;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

/**
 * <AUTHOR> on 2019/2/18.
 */

public class ConfigDBHelper {
//    public static final int TYPE_PLAY_PARAMETER = 1;
//    public static final int TYPE_REPORT_PRIVATE_PARAMETER = 2;
//    public static final int TYPE_REPORT_CAR_PARAMETER = 3;


    private static final String DATA_BASE_NAME = "reportConfig.db";
    private static ConfigDBHelper configDBHelper;
    private DaoSession daoSession;

    private ConfigDBHelper() {
    }


    public static ConfigDBHelper getInstance() {
        if (configDBHelper == null) {
            synchronized (ConfigDBHelper.class) {
                if (configDBHelper == null) {
                    configDBHelper = new ConfigDBHelper();
                }
            }
        }
        return configDBHelper;
    }

    public void init() {
        DaoMaster.DevOpenHelper devOpenHelper = new DaoMaster.DevOpenHelper(ReportHelper.getInstance().getContext(), DATA_BASE_NAME);
        Database database;
        try {
            database = devOpenHelper.getWritableDb();
        } catch (Exception e) {
            e.printStackTrace();
            return;
        }
        DaoMaster daoMaster = new DaoMaster(database);
        daoSession = daoMaster.newSession();
    }


    public Single<Long> insertData(ConfigData configData) {
        if (isDaoSessionUnavailable()) {
            return null;
        }
        return Single.fromCallable(() -> {
            daoSession.insertOrReplace(configData);
            return daoSession.getConfigDataDao().count();
        }).observeOn(AndroidSchedulers.mainThread())
                .subscribeOn(Schedulers.io());
    }

    public Single<Long> deleteData(Long id) {
        if (isDaoSessionUnavailable()) {
            return null;
        }
        return Single.fromCallable(() -> {
            daoSession.getConfigDataDao().deleteByKey(id);
            return daoSession.getConfigDataDao().count();
        }).observeOn(AndroidSchedulers.mainThread()).subscribeOn(Schedulers.io());
    }

    public Single<ConfigData> read(String type) {
        if (isDaoSessionUnavailable()) {
            return null;
        }
        return Single.fromCallable(() -> {
            List<ConfigData> configDataList = daoSession.queryRaw(ConfigData.class, " where type = ?", type);
            if (ListUtil.isEmpty(configDataList)) {
                return null;
            }
            return configDataList.get(0);
        }).observeOn(AndroidSchedulers.mainThread()).subscribeOn(Schedulers.io());
    }


    public Single<List<ConfigData>> readAll() {
        if (isDaoSessionUnavailable()) {
            return null;
        }
        return Single.fromCallable(() -> daoSession.loadAll(ConfigData.class)).observeOn(AndroidSchedulers.mainThread()).subscribeOn(Schedulers.io());
    }

    private boolean isDaoSessionUnavailable() {
        return daoSession == null;
    }

    public void release() {
        if (isDaoSessionUnavailable()) {
            return;
        }
        daoSession.getDatabase().close();
    }

}
