package com.kaolafm.ad.di.component;

import com.kaolafm.ad.api.internal.AdInternalRequest;
import com.kaolafm.ad.api.internal.AdReportRequest;
import com.kaolafm.ad.di.scope.AdRequestScope;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.ad.timer.TimedAdvertManager;
import com.kaolafm.opensdk.di.component.BaseSubcomponent;

import dagger.Subcomponent;

/**
 * 广告子组件
 * <AUTHOR>
 * @date 2020-01-14
 */
@AdRequestScope
@Subcomponent
public interface AdvertSubcomponent extends BaseSubcomponent {

    void inject(AdReportRequest reportRequest);

    void inject(AdInternalRequest request);

    void inject(AdvertisingManager manager);

    void inject(TimedAdvertManager manager);
}
