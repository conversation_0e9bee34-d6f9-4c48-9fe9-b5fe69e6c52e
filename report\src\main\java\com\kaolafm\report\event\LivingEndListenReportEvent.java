package com.kaolafm.report.event;

import com.kaolafm.report.model.PlayReportParameter;
import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> on 2019-07-23.
 */

public class LivingEndListenReportEvent extends BaseReportEventBean {

    /**
     * 直播标识id
     */
    private String live_id;
    /**
     * 直播计划id
     */
    private String plan_id;
    /**
     * 主持人id
     */
    private String live_manager_uid;
    /**
     * 直播状态 1：直播中；2：回放中
     */
    private String status;
    /**
     * 播放时长
     */
    private String playtime;

    /**
     * 进入直播的位置 1：运营位；2：AI电台；3：电台直播开播PUSH；
     */
    private String position;

    public LivingEndListenReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_LIVING_END_LISTEN);
    }

    public void playParameterToEvent(PlayReportParameter parameter) {
//        long total = parameter.getTotalLength() / 1000;
        long play = parameter.getPlayPosition() / 1000;
        setPlaytime(String.valueOf(play));
        setLive_id(parameter.getLiveType_live_id());
        setPlan_id(parameter.getLiveType_plan_id());
        setLive_manager_uid(parameter.getLiveType_compereid());
        setStatus(parameter.getLiveType_status());
        setPosition(parameter.getLiveType_position());
    }

    public String getLive_id() {
        return live_id;
    }

    public void setLive_id(String live_id) {
        this.live_id = live_id;
    }

    public String getPlan_id() {
        return plan_id;
    }

    public void setPlan_id(String plan_id) {
        this.plan_id = plan_id;
    }


    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPlaytime() {
        return playtime;
    }

    public void setPlaytime(String playtime) {
        this.playtime = playtime;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getLive_manager_uid() {
        return live_manager_uid;
    }

    public void setLive_manager_uid(String live_manager_uid) {
        this.live_manager_uid = live_manager_uid;
    }
}
