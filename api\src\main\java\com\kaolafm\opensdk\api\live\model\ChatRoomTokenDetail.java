package com.kaolafm.opensdk.api.live.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 聊天室Token详细信息，包含用户信息
 * <AUTHOR>
 */
public class ChatRoomTokenDetail implements Parcelable {

    /** 进入聊天室的id*/
    private String accid;

    /** 进入聊天室的token*/
    private String token;

    /** 昵称*/
    private String nickName;

    public ChatRoomTokenDetail() {
    }

    protected ChatRoomTokenDetail(Parcel in) {
        accid = in.readString();
        token = in.readString();
        nickName = in.readString();
    }

    public String getAccid() {
        return accid;
    }

    public String getToken() {
        return token;
    }

    public String getNickName() {
        return nickName;
    }

    public static final Creator<ChatRoomTokenDetail> CREATOR = new Creator<ChatRoomTokenDetail>() {
        @Override
        public ChatRoomTokenDetail createFromParcel(Parcel in) {
            return new ChatRoomTokenDetail(in);
        }

        @Override
        public ChatRoomTokenDetail[] newArray(int size) {
            return new ChatRoomTokenDetail[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(accid);
        dest.writeString(token);
        dest.writeString(nickName);
    }

}
