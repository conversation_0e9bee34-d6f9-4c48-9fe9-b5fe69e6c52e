package com.kaolafm.opensdk.live;

import com.kaolafm.opensdk.api.live.model.LiveChatRoomMessageNew;
import com.kaolafm.opensdk.api.live.model.LiveStatusCode;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class LiveSocketManager {

    private List<OnLiveRecvMsgListener> liveRecvMsgListeners = new ArrayList<>();
    private List<OnUserForbiddenListener> userForbiddenListeners = new ArrayList<>();

    private static volatile LiveSocketManager instance;

    private LiveSocketManager() {
    }

    public static LiveSocketManager getInstance() {
        if (instance == null) {
            synchronized (LiveSocketManager.class) {
                if (instance == null) {
                    instance = new LiveSocketManager();
                }
            }
        }
        return instance;
    }


    /**
     * 直播 - 实时接收聊天室消息（用户消息以及直播间通知） - 客户端监听事件
     */
    public synchronized void callLiveRecvMsgListener(LiveChatRoomMessageNew liveChatRoomMessageNew) {
        if (liveRecvMsgListeners.isEmpty()) {
            return;
        }
        try {
            Iterator<OnLiveRecvMsgListener> iterator = liveRecvMsgListeners.iterator();
            while (iterator.hasNext()) {
                OnLiveRecvMsgListener listener = iterator.next();
                listener.onLiveRecvMsg(liveChatRoomMessageNew);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public synchronized void registerLiveRecvMsgListener(OnLiveRecvMsgListener onLiveRecvMsgListener) {
        if (onLiveRecvMsgListener == null) {
            return;
        }
        if (liveRecvMsgListeners.contains(onLiveRecvMsgListener)) {
            return;
        }
        liveRecvMsgListeners.add(onLiveRecvMsgListener);
    }

    public synchronized void unRegisterLiveRecvMsgListener(OnLiveRecvMsgListener onLiveRecvMsgListener) {
        if (onLiveRecvMsgListener == null) {
            return;
        }
        if (!liveRecvMsgListeners.contains(onLiveRecvMsgListener)) {
            return;
        }
        liveRecvMsgListeners.remove(onLiveRecvMsgListener);
    }

    /**
     * 用户被禁言，被踢出直播间
     */
    public synchronized void callUserForbiddenListener(LiveStatusCode liveStatusCode) {
        if (userForbiddenListeners.isEmpty()) {
            return;
        }
        try {
            Iterator<OnUserForbiddenListener> iterator = userForbiddenListeners.iterator();
            while (iterator.hasNext()) {
                OnUserForbiddenListener listener = iterator.next();
                listener.onUserForbidden(liveStatusCode);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public synchronized void registerUserForbiddenListener(OnUserForbiddenListener onUserForbiddenListener) {
        if (onUserForbiddenListener == null) {
            return;
        }
        if (userForbiddenListeners.contains(onUserForbiddenListener)) {
            return;
        }
        userForbiddenListeners.add(onUserForbiddenListener);
    }

    public synchronized void unRegisterUserForbiddenListener(OnUserForbiddenListener onUserForbiddenListener) {
        if (onUserForbiddenListener == null) {
            return;
        }
        if (!userForbiddenListeners.contains(onUserForbiddenListener)) {
            return;
        }
        userForbiddenListeners.remove(onUserForbiddenListener);
    }

    public interface OnLiveRecvMsgListener {
        void onLiveRecvMsg(LiveChatRoomMessageNew liveChatRoomMessageNew);
    }

    public interface OnUserForbiddenListener {
        void onUserForbidden(LiveStatusCode liveStatusCode);
    }

}
