package com.kaolafm.gradle.plugin.processors

import com.android.build.gradle.api.LibraryVariant
import com.kaolafm.gradle.plugin.model.AndroidArchiveLibrary
import com.kaolafm.gradle.plugin.utils.TaskFactory
import com.kaolafm.gradle.plugin.utils.Util
import com.kaolafm.gradle.plugin.utils.VersionAdapter
import org.gradle.api.Project
import org.gradle.api.Task
import org.gradle.api.tasks.compile.JavaCompile
import org.gradle.jvm.tasks.Jar
/**
 * R file processor
 * <AUTHOR> on 2019/7/16.
 */
class RProcessor {

    private final Project mProject
    private final LibraryVariant mVariant

    private final File mJavaDir
    private final File mClassDir
    private final File mJarDir
    private final File mAarUnZipDir
    private final File mAarOutputDir
    private final String mGradlePluginVersion
    private String mAarOutputPath
    private VersionAdapter mVersionAdapter
    private final Collection<AndroidArchiveLibrary> mLibraries

    RProcessor(Project project, LibraryVariant variant, Collection<AndroidArchiveLibrary> libraries, VersionAdapter versionAdapter) {
        mProject = project
        mVariant = variant
        mLibraries = libraries
        mVersionAdapter = versionAdapter
        mGradlePluginVersion = versionAdapter.gradleVersion
        // R.java dir
        mJavaDir = mProject.file("${mProject.getBuildDir()}/intermediates/fat-R/r/${mVariant.dirName}")
        // R.class compile dir
        mClassDir = mProject.file("${mProject.getBuildDir()}/intermediates/fat-R/r-class/${mVariant.dirName}")
        // R.jar dir
        mJarDir = versionAdapter.jarDir
        // aar zip file
        mAarUnZipDir = mJarDir.getParentFile()
        // aar output dir
        mAarOutputDir = versionAdapter.aarOutputDir
        mAarOutputPath = versionAdapter.aarOutputPath
    }

    void inject(Task bundleTask) {
        def RFileTask = createRFileTask(mJavaDir)
        def RClassTask = createRClassTask(mJavaDir, mClassDir)
        def RJarTask = createRJarTask(mClassDir, mJarDir)

        bundleTask.doFirst {
            File f = new File(mAarOutputPath)
            if (f.exists()) {
                f.delete()
            }
            mJarDir.getParentFile().deleteDir()
            mJarDir.mkdirs()
        }

        bundleTask.finalizedBy(RFileTask)
        RFileTask.finalizedBy(RClassTask)
        RClassTask.finalizedBy(RJarTask)
    }

    private def createRFile(AndroidArchiveLibrary library, def rFolder, ConfigObject symbolsMap) {
        def libPackageName = mVariant.getApplicationId()
        def aarPackageName = library.getPackageName()

        String packagePath = aarPackageName.replace('.', '/')

        def rTxt = library.getSymbolFile()
        def rMap = new ConfigObject()
        if (rTxt.exists()) {
            rTxt.eachLine { line ->
                def (type, subclass, name, value) = line.tokenize(' ')
                if (symbolsMap.containsKey(subclass) && symbolsMap.get(subclass)[name] == type) {
                    rMap[subclass][name] = type
                }
            }
        }
        def sb = "package $aarPackageName;" << '\n' << '\n'
        sb << 'public final class R {' << '\n'
        rMap.each { subclass, values ->
            sb << "  public static final class $subclass {" << '\n'
            values.each { name, type ->
                sb << "    public static final $type $name = ${libPackageName}.R.${subclass}.${name};" << '\n'
            }

            sb << "    }" << '\n'
        }

        sb << '}' << '\n'

        new File("${rFolder.path}/$packagePath").mkdirs()
        FileOutputStream outputStream = new FileOutputStream("${rFolder.path}/$packagePath/R.java")
        outputStream.write(sb.toString().getBytes())
        outputStream.close()
    }

    private def getSymbolsMap() {
        def file = mVersionAdapter.getSymbolFile()
        if (!file.exists()) {
            throw IllegalAccessException("{$file.absolutePath} not found")
        }

        def map = new ConfigObject()
        file.eachLine { line ->
            def (type, subclass, name, value) = line.tokenize(' ')
            (map[subclass][name] = type)
        }

        return map
    }

    private Task createRFileTask(final File destFolder) {
        def task = TaskFactory.create(mProject, "createRsFile${mVariant.name}")
        task.doLast {
            if (destFolder.exists()) {
                destFolder.deleteDir()
            }
            if (mLibraries != null && mLibraries.size() > 0) {
                def symbolsMap = getSymbolsMap()
                mLibraries.each {
                    Util.logInfo("Generate R File, Library:${it.name}")
                    createRFile(it, destFolder, symbolsMap)
                }
            }
        }

        return task
    }

    private Task createRClassTask(final def sourceDir, final def destinationDir) {
        mProject.mkdir(destinationDir)

        def classpath = mVersionAdapter.getRClassPath()
        String taskName = "compileRs${mVariant.name.capitalize()}"
        Task task = TaskFactory.createTask(mProject, taskName, JavaCompile.class, {
            it.source = sourceDir.path
            it.sourceCompatibility = mProject.android.compileOptions.sourceCompatibility
            it.targetCompatibility = mProject.android.compileOptions.targetCompatibility
            it.classpath = classpath
            it.destinationDir destinationDir
        })

        task.doFirst {
            Util.logInfo("Compile R.class, Dir:${sourceDir.path}")
            Util.logInfo("Compile R.class, classpath:${classpath.first().absolutePath}")

            if (mGradlePluginVersion != null && Util.compareVersion(mGradlePluginVersion, "3.3.0") >= 0) {
                mProject.copy {
                    from mProject.zipTree(mVersionAdapter.getRClassPath().first().absolutePath + "/R.jar")
                    into mVersionAdapter.getRClassPath().first().absolutePath
                }
            }
        }
        return task
    }

    private Task createRJarTask(final def fromDir, final File desFile) {
        String taskName = "createRsJar${mVariant.name.capitalize()}"
        Task task = TaskFactory.createTask(mProject, taskName, Jar.class, {
            it.from fromDir.path
            it.archiveName = "r-classes.jar"
            it.destinationDir desFile
        })
        task.doFirst {
            Util.logInfo("Generate R.jar, Dir：$fromDir")
        }
        return task
    }

}
