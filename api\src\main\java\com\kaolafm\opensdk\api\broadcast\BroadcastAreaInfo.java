package com.kaolafm.opensdk.api.broadcast;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;
import com.kaolafm.opensdk.api.media.model.AudioFileInfo;

import java.util.List;

/**
 * 广播地区信息
 * 目前只有中国交通广播区分国家台和地方台共20+个电台
 */
public class BroadcastAreaInfo implements Parcelable {
    /**
     * 广播id
     */
    @SerializedName("id")
    private long broadcastId;
    /**
     * 地区名称
     */
    @SerializedName("areaName")
    private String areaName;
    /**
     * 地区编号
     */
    @SerializedName("areaCode")
    private String areaCode;

    public BroadcastAreaInfo(Parcel in) {
        broadcastId = in.readLong();
        areaName = in.readString();
        areaCode = in.readString();
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(broadcastId);
        dest.writeString(areaName);
        dest.writeString(areaCode);
    }

    public static final Parcelable.Creator<BroadcastAreaInfo> CREATOR = new Parcelable.Creator<BroadcastAreaInfo>() {
        @Override
        public BroadcastAreaInfo createFromParcel(Parcel in) {
            return new BroadcastAreaInfo(in);
        }

        @Override
        public BroadcastAreaInfo[] newArray(int size) {
            return new BroadcastAreaInfo[size];
        }
    };


    public long getBroadcastId() {
        return broadcastId;
    }

    public void setBroadcastId(long broadcastId) {
        this.broadcastId = broadcastId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    @Override
    public String toString() {
        return "BroadcastAreaInfo{" +
                "broadcastId=" + broadcastId +
                ", areaName='" + areaName + '\'' +
                ", areaCode='" + areaCode + '\'' +
                '}';
    }
}
