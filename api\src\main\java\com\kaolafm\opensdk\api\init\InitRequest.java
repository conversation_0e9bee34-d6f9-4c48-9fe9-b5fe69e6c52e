package com.kaolafm.opensdk.api.init;

import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.di.component.SessionComponent;
import com.kaolafm.opensdk.di.qualifier.KaolaInitParam;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.model.KaolaActivateData;

import java.util.Map;

import javax.inject.Inject;
import javax.inject.Provider;

import io.reactivex.Single;
import io.reactivex.SingleEmitter;

/**
 * 初始化激活相关网络请求
 *
 * <AUTHOR>
 * @date 2018/7/25
 */
@AppScope
public class InitRequest extends BaseRequest {
    private static final String TAG = "InitRequest";
    private static final int ERROR_CODE_REPEAT_ACTIVATION = 50500;

    /**
     * 公共参数在{@link com.kaolafm.opensdk.di.module.CommonParamModule}中提供, 每次get()都会重新获取参数
     */
    @Inject
    @KaolaInitParam
    Provider<Map<String, String>> mKaolaInitParams;


    private InitService mInitService;

    @Inject
    public InitRequest() {
        Log.i(TAG, "InitRequest: 构造函数");
        SessionComponent subcomponent = ComponentKit.getInstance().getSubcomponent();
        if (subcomponent != null) {
            subcomponent.inject(this);
            Log.i(TAG, "InitRequest: 依赖注入完成");
        } else {
            Log.e(TAG, "InitRequest: 获取subcomponent失败");
        }
        mInitService = obtainRetrofitService(InitService.class);
        Log.i(TAG, "InitRequest: 获取InitService完成");
    }

    /**
     * 初始化考拉,获取openId
     *
     * @param callback 回调,返回openId
     */
    private void initKaola(HttpCallback<String> callback) {
        Log.i(TAG, "initKaola: 开始初始化");
        doHttpDeal(mInitService.initKaola(), baseResult -> {
            if (baseResult != null) {
                Log.i(TAG, "initKaola: 初始化请求成功，baseResult不为空，code=" + baseResult.getCode() + ", message=" + baseResult.getMessage());
                KaolaActivateData activateData = baseResult.getResult();
                if (activateData != null) {
                    Log.i(TAG, "initKaola: 获取到激活数据，设置车辆信息到报告系统");
                    String openId = activateData.getOpenid();
                    Log.i(TAG, "initKaola: 获取到openId=" + (TextUtils.isEmpty(openId) ? "为空" : openId));
                    setCarInfoToReport(activateData);
                    return openId;
                } else {
                    Log.e(TAG, "initKaola: 激活数据为空，无法获取openId");
                }
            } else {
                Log.e(TAG, "initKaola: 初始化请求失败，baseResult为空");
            }
            return "";
        }, new HttpCallback<String>() {
            @Override
            public void onSuccess(String result) {
                Log.i(TAG, "initKaola: 回调成功，openId是否为空=" + TextUtils.isEmpty(result));
                if (callback != null) {
                    callback.onSuccess(result);
                } else {
                    Log.w(TAG, "initKaola: callback为空，无法回调结果");
                }
            }

            @Override
            public void onError(ApiException exception) {
                Log.e(TAG, "initKaola: 回调失败，错误码=" + exception.getCode() + ", 错误信息=" + exception.getMessage(), exception);
                if (callback != null) {
                    callback.onError(exception);
                } else {
                    Log.w(TAG, "initKaola: callback为空，无法回调错误");
                }
            }
        });
    }


    /**
     * 设备激活考拉
     *
     * @param callback 回调,返回openId
     */
    private void activeKaola(HttpCallback<String> callback) {
        Log.i(TAG, "activeKaola: 开始激活考拉");
        Map<String, String> params = getInitParams();
        Log.i(TAG, "activeKaola: 获取初始化参数完成，参数数量=" + (params != null ? params.size() : 0));

        // 记录关键参数，但不记录敏感信息
        if (params != null && params.size() > 0) {
            StringBuilder sb = new StringBuilder();
            sb.append("激活参数：");
            if (params.containsKey("deviceid")) {
                sb.append("deviceid=").append(params.get("deviceid")).append(", ");
            }
            if (params.containsKey("appid")) {
                sb.append("appid=").append(params.get("appid")).append(", ");
            }
            if (params.containsKey("channel")) {
                sb.append("channel=").append(params.get("channel")).append(", ");
            }
            if (params.containsKey("version")) {
                sb.append("version=").append(params.get("version")).append(", ");
            }
            if (params.containsKey("sdkversion")) {
                sb.append("sdkversion=").append(params.get("sdkversion")).append(", ");
            }
            if (params.containsKey("os")) {
                sb.append("os=").append(params.get("os")).append(", ");
            }
            if (params.containsKey("osversion")) {
                sb.append("osversion=").append(params.get("osversion")).append(", ");
            }
            if (params.containsKey("packagename")) {
                sb.append("packagename=").append(params.get("packagename"));
            }
            Log.i(TAG, sb.toString());
        }

        doHttpDeal(mInitService.activeKaola(params), baseResult -> {
            if (baseResult != null) {
                Log.i(TAG, "activeKaola: 激活请求成功，baseResult不为空，code=" + baseResult.getCode() + ", message=" + baseResult.getMessage());
                KaolaActivateData activateData = baseResult.getResult();
                if (activateData != null) {
                    Log.i(TAG, "activeKaola: 获取到激活数据，设置车辆信息到报告系统");
                    String openId = activateData.getOpenid();
                    Log.i(TAG, "activeKaola: 获取到openId=" + (TextUtils.isEmpty(openId) ? "为空" : openId));
                    setCarInfoToReport(activateData);
                    return openId;
                } else {
                    Log.e(TAG, "activeKaola: 激活数据为空，无法获取openId");
                }
            } else {
                Log.e(TAG, "activeKaola: 激活请求失败，baseResult为空");
            }
            return "";
        }, new HttpCallback<String>() {
            @Override
            public void onSuccess(String result) {
                Log.i(TAG, "activeKaola: 回调成功，openId是否为空=" + TextUtils.isEmpty(result));
                if (callback != null) {
                    callback.onSuccess(result);
                } else {
                    Log.w(TAG, "activeKaola: callback为空，无法回调结果");
                }
            }

            @Override
            public void onError(ApiException exception) {
                Log.e(TAG, "activeKaola: 回调失败，错误码=" + exception.getCode() + ", 错误信息=" + exception.getMessage(), exception);
                if (callback != null) {
                    callback.onError(exception);
                } else {
                    Log.w(TAG, "activeKaola: callback为空，无法回调错误");
                }
            }
        });
    }

    private Map<String, String> getInitParams() {
        Map<String, String> params = mKaolaInitParams.get();
        Log.i(TAG, "getInitParams: 获取初始化参数，参数数量=" + (params != null ? params.size() : 0));
        return params;
    }

    /**
     * 激活或者初始化考拉接口，先调用{@link #activeKaola(HttpCallback)}接口，
     * 当已经激活或者激活成功没有获取openId就自动调用{@link #initKaola(HttpCallback)}接口获取openId.
     */
    private Single<String> activateOrInitKaola() {
        Log.i(TAG, "activateOrInitKaola: 开始激活或初始化考拉");
        return Single.create(emitter -> activeKaola(new HttpCallback<String>() {
            @Override
            public void onSuccess(String s) {
                Log.i(TAG, "activateOrInitKaola: 激活成功，openId是否为空=" + TextUtils.isEmpty(s) + (TextUtils.isEmpty(s) ? "" : ", openId=" + s));
                if (!TextUtils.isEmpty(s)) {
                    Log.i(TAG, "activateOrInitKaola: openId不为空，直接返回结果");
                    InitRequest.this.onSuccess(emitter, s);
                } else {//如果获取openId为空就初始化一下。
                    Log.i(TAG, "activateOrInitKaola: openId为空，尝试初始化获取openId");
                    initKaola(emitter);
                }
            }

            @Override
            public void onError(ApiException exception) {
                Log.e(TAG, "activateOrInitKaola: 激活失败，错误码=" + exception.getCode() + ", 错误信息=" + exception.getMessage(), exception);
                //如果重复激活，就自动调用init方法获取autoId
                if (exception != null && exception.getCode() == ERROR_CODE_REPEAT_ACTIVATION) {
                    Log.i(TAG, "activateOrInitKaola: 重复激活错误(code=" + ERROR_CODE_REPEAT_ACTIVATION + ")，尝试初始化获取openId");
                    initKaola(emitter);
                } else {
                    Log.e(TAG, "activateOrInitKaola: 非重复激活错误，直接返回错误");
                    InitRequest.this.onError(emitter, exception);
                }
            }
        }));
    }

    public void activateOrInitKaola(HttpCallback<String> callback) {
        Log.i(TAG, "activateOrInitKaola: 开始激活或初始化考拉");
        doHttpDeal(activateOrInitKaola(), new HttpCallback<String>() {
            @Override
            public void onSuccess(String result) {
                Log.i(TAG, "activateOrInitKaola: 激活或初始化成功，openId是否为空=" + TextUtils.isEmpty(result) + (TextUtils.isEmpty(result) ? "" : ", openId=" + result));
                if (callback != null) {
                    callback.onSuccess(result);
                } else {
                    Log.w(TAG, "activateOrInitKaola: callback为空，无法回调结果");
                }
            }

            @Override
            public void onError(ApiException exception) {
                Log.e(TAG, "activateOrInitKaola: 激活或初始化失败，错误码=" + exception.getCode() + ", 错误信息=" + exception.getMessage(), exception);
                if (callback != null) {
                    callback.onError(exception);
                } else {
                    Log.w(TAG, "activateOrInitKaola: callback为空，无法回调错误");
                }
            }
        });
    }

    private void initKaola(SingleEmitter<String> emitter) {
        Log.i(TAG, "initKaola: 开始初始化考拉(SingleEmitter)");
        initKaola(new HttpCallback<String>() {
            @Override
            public void onSuccess(String s) {
                Log.i(TAG, "initKaola: 初始化成功，openId是否为空=" + TextUtils.isEmpty(s));
                if (!TextUtils.isEmpty(s)) {
                    InitRequest.this.onSuccess(emitter, s);
                } else {
                    Log.e(TAG, "initKaola: 初始化成功但openId为空，激活失败");
                    InitRequest.this.onError(emitter, new ApiException("激活失败"));
                }
            }

            @Override
            public void onError(ApiException exception) {
                Log.e(TAG, "initKaola: 初始化失败", exception);
                InitRequest.this.onError(emitter, exception);
            }
        });
    }

    private <T> void onSuccess(SingleEmitter<T> emitter, T t) {
        if (emitter != null && !emitter.isDisposed()) {
            Log.i(TAG, "onSuccess: 发送成功结果到SingleEmitter");
            emitter.onSuccess(t);
        } else {
            Log.w(TAG, "onSuccess: SingleEmitter为空或已释放，无法发送结果");
        }
    }

    private <T> void onError(SingleEmitter<T> emitter, ApiException e) {
        if (emitter != null && !emitter.isDisposed()) {
            Log.e(TAG, "onError: 发送错误到SingleEmitter", e);
            emitter.onError(e);
        } else {
            Log.w(TAG, "onError: SingleEmitter为空或已释放，无法发送错误");
        }
    }

    private void setCarInfoToReport(KaolaActivateData activateData) {
        if (activateData == null) {
            Log.w(TAG, "setCarInfoToReport: 激活数据为空，无法设置车辆信息");
            return;
        }
        Log.i(TAG, "setCarInfoToReport: 设置车辆信息到报告系统");
        ReportHelper.getInstance().setCarParameter(activateData);
    }

    String activate() {
        Log.i(TAG, "activate: 开始同步激活");
        Map<String, String> params = getInitParams();
        // 记录关键参数，但不记录敏感信息
        if (params != null && params.size() > 0) {
            StringBuilder sb = new StringBuilder();
            sb.append("同步激活参数：");
            if (params.containsKey("deviceid")) {
                sb.append("deviceid=").append(params.get("deviceid")).append(", ");
            }
            if (params.containsKey("appid")) {
                sb.append("appid=").append(params.get("appid")).append(", ");
            }
            if (params.containsKey("channel")) {
                sb.append("channel=").append(params.get("channel")).append(", ");
            }
            if (params.containsKey("version")) {
                sb.append("version=").append(params.get("version")).append(", ");
            }
            if (params.containsKey("sdkversion")) {
                sb.append("sdkversion=").append(params.get("sdkversion"));
            }
            Log.i(TAG, sb.toString());
        }

        BaseResult<KaolaActivateData> baseResult = doHttpDealSync(mInitService.activeKaolaSync(params));
        if (baseResult == null) {
            Log.e(TAG, "activate: 同步激活失败，baseResult为空");
            return "";
        }
        int code = baseResult.getCode();
        Log.i(TAG, "activate: 同步激活返回码=" + code + ", 消息=" + baseResult.getMessage());
        KaolaActivateData activateData = null;
        if (code != 0 && code != 10000) {
            if (code == ERROR_CODE_REPEAT_ACTIVATION) {
                Log.i(TAG, "activate: 重复激活错误(code=" + ERROR_CODE_REPEAT_ACTIVATION + ")，尝试同步初始化");
                BaseResult<KaolaActivateData> initResult = doHttpDealSync(mInitService.initKaolaSync());
                if (initResult == null) {
                    Log.e(TAG, "activate: 同步初始化失败，initResult为空");
                    return "";
                }
                int initCode = initResult.getCode();
                Log.i(TAG, "activate: 同步初始化返回码=" + initCode + ", 消息=" + initResult.getMessage());
                if (initCode == 0 || initCode == 10000) {
                    activateData = initResult.getResult();
                    Log.i(TAG, "activate: 同步初始化成功，获取到激活数据");
                } else {
                    Log.e(TAG, "activate: 同步初始化失败，返回码=" + initCode + "，消息=" + initResult.getMessage());
                }
            } else {
                Log.e(TAG, "activate: 同步激活失败，返回码=" + code + "，消息=" + baseResult.getMessage());
            }
        } else {
            activateData = baseResult.getResult();
            Log.i(TAG, "activate: 同步激活成功，获取到激活数据");
        }
        if (activateData != null) {
            setCarInfoToReport(activateData);
            String openId = activateData.getOpenid();
            Log.i(TAG, "activate: 获取到openId=" + (TextUtils.isEmpty(openId) ? "为空" : openId));
            return openId;
        }
        Log.e(TAG, "activate: 激活数据为空，激活失败");
        return "";
    }

    public void getBrand(HttpCallback<String> callback) {
        Log.i(TAG, "getBrand: 开始获取品牌信息");
        doHttpDeal(mInitService.getBrand(), BaseResult::getResult, callback);
    }
}
