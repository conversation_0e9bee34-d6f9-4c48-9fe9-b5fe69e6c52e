package com.kaolafm.opensdk.api.tv;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.tv.model.TVDetails;
import com.kaolafm.opensdk.api.tv.model.TVProgramDetails;

import java.util.List;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Query;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: TVService.java
 *                                                                  *
 * Created in 2018/8/13 下午4:06                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
/*package*/ interface TVService {

//    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
//    @GET(KaolaApiConstant.GET_LISTEN_TV_LIST)
//    Single<BaseResult<BasePageResult<List<TVDetails>>>> getTVList(@Query("type") int type, @Query("classifyid") int classifyId, @Query("pagenum") int pageNum, @Query("pagesize") int pageSize, @Query("area") int area);
//
//    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
//    @GET(KaolaApiConstant.GET_LISTEN_TV_NEIGHBOR_LIST)
//    Single<BaseResult<BasePageResult<List<TVDetails>>>> getTVNeighborList(@Query("id") long id, @Query("pagenum") int pagenum, @Query("pagesize") int pagesize);


    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_LISTEN_TV_DETAILS)
    Single<BaseResult<TVDetails>> getTVDetails(@Query("listenTVid") long broadcastId);

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_LISTEN_TV_PROGRAM_LIST)
    Single<BaseResult<List<TVProgramDetails>>> getTVProgramList(@Query("listenTVid") long listenTVid, @Query("date") String date);
//
//    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
//    @GET(KaolaApiConstant.GET_LISTEN_TV_DETAILS_TOMORROW)
//    Single<BaseResult<List<TVProgramDetails>>> getTVTomorrowProgramList(@Query("bid") long broadcastId, @Query("date") String date);
//
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_LISTEN_TV_PROGRAM_DETAILS)
    Single<BaseResult<TVProgramDetails>> getTVProgramDetails(@Query("programid") long programId);

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_TV_NEIGHBOR_LIST)
    Single<BaseResult<List<TVDetails>>> getTVNeighborList();


    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_LISTEN_TV_CURRENT_PROGRAM)
    Single<BaseResult<TVProgramDetails>> getTVCurrentProgramDetails(@Query("listenTVid") long broadcastId);

//
//    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
//    @GET(KaolaApiConstant.GET_LISTEN_TV_AREA_LIST)
//    Single<BaseResult<List<BroadcastArea>>> getTVAreaList();
//
//    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
//    @GET(KaolaApiConstant.GET_LISTEN_TV_AREA)
//    Single<BaseResult<BroadcastArea>> getTVArea(@Query("lon") float longitude, @Query("lat") float latitude);

}
