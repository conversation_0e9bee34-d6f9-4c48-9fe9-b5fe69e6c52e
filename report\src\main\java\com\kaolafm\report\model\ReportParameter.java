package com.kaolafm.report.model;

/**
 * <AUTHOR> on 2019/1/24.
 * 需要外部传入的公共参数
 */

public class ReportParameter {
    private String deviceId;
    private String appid;
    private String uid;
    private String openid;
    private String lib_version;
    /**
     * 鉴权时使用的包名
     */
    private String channel;

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getLib_version() {
        return lib_version;
    }

    public void setLib_version(String lib_version) {
        this.lib_version = lib_version;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }
}
