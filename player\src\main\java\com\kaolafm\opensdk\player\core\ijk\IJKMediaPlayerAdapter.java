package com.kaolafm.opensdk.player.core.ijk;
import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.net.Uri;
import android.util.Log;
import android.view.Surface;
import android.view.SurfaceHolder;

import com.kaolafm.base.utils.NetworkUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.player.core.listener.IPlayerBufferProgressListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerStateCoreListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerStateVideoCoreListener;
import com.kaolafm.opensdk.player.core.media.IRenderView;
import com.kaolafm.opensdk.player.core.model.AMediaPlayer;
import com.kaolafm.opensdk.player.core.model.AudioFadeConfig;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.ref.WeakReference;

import io.reactivex.Single;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import tv.danmaku.ijk.media.player.IjkMediaPlayer;

/**
 * <AUTHOR> on 2019-05-24.
 */

public class IJKMediaPlayerAdapter extends AMediaPlayer {
    private static final String TAG = "IJKMediaPlayerAdapter";

    private static final float DEF_PLAY_RATE = 1.0f;
    /**
     * 播放状态
     */
    private IPlayerStateCoreListener mIPlayerStateListener;
    private IPlayerStateVideoCoreListener mIPlayerStateVideoCoreListener;

    /**
     * 缓冲回调
     */
    private IPlayerBufferProgressListener mIPlayerBufferProgressListener;

    /**
     * 播放器初始化成功
     */
    private IPlayerInitCompleteListener mIPlayerInitCompleteListener;

    private final IjkMediaPlayer mIjkMediaPlayer;
    /**
     * 正在播放的url
     */
    private String mDataSource;
    private WeakReference<VideoView> mVideoViewRef;
    /**
     * 正在播放的SurfaceHolder
     */
    private SurfaceHolder mSurfaceHolder;

    /**
     * 当前播放状态
     */
    private int mPlayStatus = PlayerConstants.TYPE_PLAYER_DEFAULT;

    /**
     * 当前是否是 默认播放. 为了满足特殊需求.
     */
    private boolean isPlayNow = true;

    /**
     * 视频宽高
     */
    private int mVideoWidth, mVideoHeight;

    /**
     * 倍速
     */
    private float mPlaybackRate = DEF_PLAY_RATE;

    private boolean mNeedResetLastPlaybackRateFlag;

    private final IJKCallBack ijkCallBack;

    private Context mContext;

    public IJKMediaPlayerAdapter(Context context) {
        this.mContext = context;
        mIjkMediaPlayer = new IjkMediaPlayer();
        ijkCallBack = new IJKCallBack(IJKMediaPlayerAdapter.this);
        mIjkMediaPlayer.setIjkPlayerCallBack(ijkCallBack);
    }

    @FunctionalInterface
    private interface PlayerFunction {
        void call() throws Exception;
    }

    private void performAction(PlayerFunction function) {
        try {
            function.call();
        } catch (Exception e) {
            PlayerLogUtil.log(TAG, " error:" + e);
        }
    }

    @Override
    public long getDuration() {
        return mIjkMediaPlayer.getDur();
    }

    @Override
    public long getCurrentPosition() {
        return mIjkMediaPlayer.getCurrentPos();
    }

    @Override
    public boolean isPlaying() {
        return mIjkMediaPlayer.isPlay();
    }

    @Override
    public void pause() {
        performAction(mIjkMediaPlayer::pause);
    }

    @Override
    public void initPlayerForce() {
        performAction(mIjkMediaPlayer::initPlayerForce);
    }

    @Override
    public void play() {
        performAction(mIjkMediaPlayer::play);
    }

    @Override
    public void preload(String url) {

    }

    @Override
    public void reset(boolean needResetLastPlaybackRateFlag) {
        PlayerLogUtil.log(TAG, "reset() --- needResetLastPlaybackRateFlag=" + needResetLastPlaybackRateFlag);
        mNeedResetLastPlaybackRateFlag = needResetLastPlaybackRateFlag;
        notifyPlayerIdle();
        resetIjk();
        if (mNeedResetLastPlaybackRateFlag) {
            resetPlaybackRate("reset()");
        }
    }

    private void resetIjk() {
        performAction(mIjkMediaPlayer::reset);
    }

    @Override
    public void release() {
        ijkCallBack.clear();
        performAction(mIjkMediaPlayer::release);
    }

    @Override
    public void prepare() {
        prepare(0);
    }

    @Override
    public void prepareAsync() {
        performAction(mIjkMediaPlayer::prepareAsync);
    }

    @Override
    public void prepare(int needSeek) {
        prepare(needSeek, IjkMediaPlayer.STREAM_MUSIC);
    }

    @Override
    public void prepare(int needSeek, int streamTypeChannel) {
        performAction(() -> mIjkMediaPlayer.prepare(needSeek, streamTypeChannel));
        notifyPlayerPreparing();
    }

    @Override
    public void seek(long msec) {
        performAction(() -> mIjkMediaPlayer.seek(msec));
        notifySeekStart();
    }

    @Override
    public void setDataSource(String source) {
        mDataSource = source;
        performAction(() -> mIjkMediaPlayer.setDataSource(source));
    }

    @Override
    public void setPlaybackRate(float rate) {
        mPlaybackRate = rate;
        performAction(() -> mIjkMediaPlayer.setPlaybackRate(rate));
    }

    @Override
    public float getPlaybackRate() {
        return mIjkMediaPlayer.getPlaybackRate();
    }

    @Override
    public void resetPlaybackRate(String refMethodName) {
        mPlaybackRate = DEF_PLAY_RATE;
        PlayerLogUtil.log(TAG, "resetPlaybackRate() --- refMethodName=" + refMethodName + ", mPlaybackRate=" + mPlaybackRate);
    }

    @Override
    public void setDisplay(SurfaceHolder sh) {
        mSurfaceHolder = sh;
        performAction(() -> mIjkMediaPlayer.setDisplay(sh));
    }

    @Override
    public void setSurface(Surface surface) {
        performAction(() -> mIjkMediaPlayer.setSurface(surface));
    }

    @Override
    public boolean getCurrentFrame(Bitmap bitmap) {
        try {
            return mIjkMediaPlayer.getCurrentFrame(bitmap);
        } catch (Exception e) {
            PlayerLogUtil.log(TAG, "getCurrentFrame() --- error:" + e);
            return false;
        }
    }

    @Override
    public void stop() {
        notifyPlayerIdle();
        stopIjk();
    }

    @Override
    public void rePlay() {
        performAction(() -> {
            mIjkMediaPlayer.reset();
            mIjkMediaPlayer.setDisplay(mSurfaceHolder);
            mIjkMediaPlayer.setDataSource(mDataSource);
            mIjkMediaPlayer.prepare(0, IjkMediaPlayer.STREAM_MUSIC);
            mIjkMediaPlayer.play();
        });
    }

    private void stopIjk() {
        performAction(mIjkMediaPlayer::stop);
    }

    @Override
    public void start(String url, long duration, long position, int streamTypeChannel,
                      boolean audioFadeEnabled, AudioFadeConfig audioFadeConfig, String httpProxy,
                      boolean clearDnsCache, VideoView videoView, boolean needUseLastPlaybackRate, boolean shouldResetPlaybackRate) {
        PlayerLogUtil.log(TAG, "start");

        mDataSource = url;
        mVideoViewRef = new WeakReference<>(videoView);
        notifyPlayerIdle();
        stopIjk();
        resetIjk();

        if (needUseLastPlaybackRate && mPlaybackRate > 0) {
            // 此处是最终设置上次播放倍速的地方
            setPlaybackRate(mPlaybackRate);
        } else {
            if (shouldResetPlaybackRate || mNeedResetLastPlaybackRateFlag) {
                resetPlaybackRate("start()");
            }
        }
        mNeedResetLastPlaybackRateFlag = true;

        //TODO:不需要每次设置，默认这边是false即可
        performAction(() -> mIjkMediaPlayer.setAutoPlayOnPrepared(false));

        try {
            if (!StringUtil.isEmpty(httpProxy)) {
                mIjkMediaPlayer.setProxyAddress(httpProxy);
            } else {
                mIjkMediaPlayer.clearProxyAddress();
            }
            if (clearDnsCache) {
                mIjkMediaPlayer.clearDnsCache("1");
            }
        } catch (IOException e) {
            PlayerLogUtil.log(TAG, "start error:" + e);
        }

        // 设置淡入淡出效果
//        setAudioFadeEnabled(audioFadeEnabled);
//        if (audioFadeEnabled) {
//            if (null == audioFadeConfig) {
//                AudioFadeConfig audioFadeConfig1 = new AudioFadeConfig();
//                setAudioFadeConfig(audioFadeConfig1);
//            } else {
//                setAudioFadeConfig(audioFadeConfig);
//            }
//        }

        if (videoView != null) {
            videoView.setMediaPlayer(this);
            videoView.setAspectRatio(IRenderView.AR_ASPECT_FIT_PARENT);
            videoView.setVideoURI(Uri.parse(url));
        }

        setDataSource(url);

        if (duration > 0) {
            setDuration(duration, duration);
        }

        if (position > 0) {
            seekAtStart(position);
            prepare(1, streamTypeChannel);
        } else {
            prepare(0, streamTypeChannel);
        }

        if (videoView != null) {
            videoView.notifyPreparing();
        }
    }

    @Override
    public void setAudioFadeConfig(AudioFadeConfig audioFadeConfig) {
        PlayerLogUtil.log(TAG, "->setAudioFadeConfig, audioFadeConfig" + audioFadeConfig.toString());

        if (audioFadeConfig.isPlayStartFadeIn()) {
            setAudioFadeInDuration(0x11, audioFadeConfig.getPlayStartFadeInDuration());
        }
        if (audioFadeConfig.isSeekStartFadeIn()) {
            setAudioFadeInDuration(0x12, audioFadeConfig.getSeekStartFadeInDuration());
        }
        if (audioFadeConfig.isPauseStartFadeIn()) {
            setAudioFadeInDuration(0x14, audioFadeConfig.getPauseStartFadeInDuration());
        }
        if (audioFadeConfig.isPlayFinishFadeOut()) {
            setAudioFadeOutDuration(0x21, audioFadeConfig.getPlayFinishFadeOutDuration());
        }
        if (audioFadeConfig.isPlayStopFadeOut()) {
            setAudioFadeOutDuration(0x22, audioFadeConfig.getPlayStopFadeOutDuration());
        }
        if (audioFadeConfig.isPauseFadeOut()) {
            setAudioFadeOutDuration(0x24, audioFadeConfig.getPauseFadeOutDuration());
        }

    }

    @Override
    public String getDnsAddress() {
        return mIjkMediaPlayer.getDnsAddress();
    }

    @Override
    public void setMediaVolume(float leftVolume, float rightVolume) {
        performAction(() -> mIjkMediaPlayer.setMediaVolume(leftVolume, rightVolume));
    }

    /**
     * 设置AudioTrack的 Usage和ContentType
     *
     * @param usage
     * @param contentType
     */
    @Override
    public void setUsageAndContentType(int usage, int contentType) {
        mIjkMediaPlayer.setAttributesUsage(usage);
        mIjkMediaPlayer.setAttributesContentType(contentType);
    }

    @Override
    public void setPlayerStateListener(IPlayerStateCoreListener iPlayerState) {
        mIPlayerStateListener = iPlayerState;
    }

    @Override
    public void setPlayerStateVideoListener(IPlayerStateVideoCoreListener iPlayerStateVideoCoreListener) {
        mIPlayerStateVideoCoreListener = iPlayerStateVideoCoreListener;
    }

    @Override
    public void setBufferProgressListener(IPlayerBufferProgressListener progressListener) {
        mIPlayerBufferProgressListener = progressListener;
    }

    @Override
    public void setInitPlayerInitCompleteListener(IPlayerInitCompleteListener initPlayerInitCompleteListener) {
        mIPlayerInitCompleteListener = initPlayerInitCompleteListener;
    }

    @Override
    public void setDuration(long urlDuration, long totalDuration) {
        performAction(() -> mIjkMediaPlayer.setDuration(urlDuration, totalDuration));
    }

    @Override
    public void setPlayRatio(float ratio) {

    }

    @Override
    public void seekAtStart(long msec) {
        performAction(() -> mIjkMediaPlayer.seekAtStart(msec));
    }

    @Override
    public int getPlayStatus() {
        return mPlayStatus;
    }

    @Override
    public void setPlayStatus(int playStatus) {
        this.mPlayStatus = playStatus;
    }

    private static class IJKCallBack implements IjkMediaPlayer.ijkPlayerCallBack {
        private int mArg1;
        private int mArg2;
        private WeakReference<IJKMediaPlayerAdapter> ijkMediaPlayerAdapterWeakReference;
        private Disposable initDisposable;


        public IJKCallBack(IJKMediaPlayerAdapter ijkMediaPlayerAdapter) {
            ijkMediaPlayerAdapterWeakReference = new WeakReference<>(ijkMediaPlayerAdapter);
        }

        public void clear() {
            if (initDisposable != null && !initDisposable.isDisposed()) {
                initDisposable.dispose();
            }
        }


        private void rePlay() {
            IJKMediaPlayerAdapter ijkMediaPlayerAdapter = ijkMediaPlayerAdapterWeakReference.get();
            if (ijkMediaPlayerAdapter == null) {
                return;
            }
            try {
                ijkMediaPlayerAdapter.reset(true);
                ijkMediaPlayerAdapter.setDisplay(ijkMediaPlayerAdapter.mSurfaceHolder);
                ijkMediaPlayerAdapter.setDataSource(ijkMediaPlayerAdapter.mDataSource);
                ijkMediaPlayerAdapter.prepare();
                ijkMediaPlayerAdapter.play();
            } catch (Throwable t) {
                t.printStackTrace();
            }
        }


        @Override
        public void message(int what, int arg1, int arg2, Object obj) {
            //ijkplayer中抛出 post_event(env, weak_thiz, MEDIA_ERROR（what）, MEDIA_ERROR_IJK_PLAYER(arg1), msg.arg1(arg2));
            IJKMediaPlayerAdapter ijkMediaPlayerAdapter = ijkMediaPlayerAdapterWeakReference.get();
            if (ijkMediaPlayerAdapter == null) {
                return;
            }
            if (what != IjkMediaPlayer.MEDIA_PLAYING_TIME_UPDATE && what != IjkMediaPlayer.MEDIA_PRELOAD_TIME_UPDATE &&
                    what != IjkMediaPlayer.MEDIA_BUFFERING_UPDATE) {
                PlayerLogUtil.log(TAG, "message = " + what + ", arg1 = " + arg1 + ", arg2 = " + arg2);
            }
            switch (what) {
                case IjkMediaPlayer.MEDIA_PREPARED: {
                    /**
                     * 没有播放开始回调. prepare结束后,就开始播放了.
                     */
//                    if (ijkMediaPlayerAdapter.isPlayNow) {
                        ijkMediaPlayerAdapter.notifyPlayerPreparingComplete();
//                    } else {
//                        ijkMediaPlayerAdapter.notifyPlayerPaused();
//                    }
                    ijkMediaPlayerAdapter.isPlayNow = true;
                }
                break;
                case IjkMediaPlayer.MEDIA_PLAYBACK_COMPLETE: {
                    ijkMediaPlayerAdapter.notifyPlayerEnd();
                }
                break;
                case IjkMediaPlayer.MEDIA_STARTED: {
                    ijkMediaPlayerAdapter.notifyPlayerPlaying();
                }
                break;
                case IjkMediaPlayer.MEDIA_PAUSED: {
                    // 暂停
                     ijkMediaPlayerAdapter.notifyPlayerPaused();
                }
                break;
                case IjkMediaPlayer.MEDIA_PRELOAD_TIME_UPDATE: {
                    if (arg1 == arg2) {
                        ijkMediaPlayerAdapter.notifyBufferProgress(arg1, arg2);
                        return;
                    }
                    if (arg1 < 0 || arg2 < 0) {
                        return;
                    }
                    ijkMediaPlayerAdapter.notifyBufferProgress(arg1, arg2);
                }
                break;
                case IjkMediaPlayer.MEDIA_PLAYING_TIME_UPDATE: {
                    ijkMediaPlayerAdapter.notifyProgress(arg1, arg2);
                }
                break;
                case IjkMediaPlayer.MEDIA_BUFFERING_UPDATE:
                break;
                case IjkMediaPlayer.MEDIA_SEEK_COMPLETE: {
                    ijkMediaPlayerAdapter.notifySeekComplete();
                }
                break;
                case IjkMediaPlayer.MEDIA_ERROR: {
                    // 处理setDataSource/prepareAsync失败的错误（错误码-3）
                    if (arg1 == IjkMediaPlayer.MEDIA_ERROR_IJK_PLAYER && arg2 == -3) {
                        PlayerLogUtil.log(TAG, "底层错误：setDatasource/prepareAsync因代码-3失败，强迫重置");
                        
                        // 强制重置播放器到IDLE状态
                        IJKMediaPlayerAdapter adapter = ijkMediaPlayerAdapterWeakReference.get();
                        if (adapter != null) {
                            adapter.reset(true);
                            
                            // 确保上层知道播放器已重置并处于错误状态
                            String dnsAddress = adapter.getDnsAddress();
                            adapter.notifyPlayerFailed(arg1, arg2, dnsAddress);
                            adapter.notifyPlayerIdle();
                        }
                        return;  // 直接返回，不走后续通用错误处理
                    }
                    
                    if (arg1 == IjkMediaPlayer.MEDIA_ERROR_IJK_PLAYER && arg2 == IjkMediaPlayer.MEDIA_ERROR_TIMED_OUT &&
                            mArg1 != IjkMediaPlayer.MEDIA_ERROR_IJK_PLAYER && mArg2 != IjkMediaPlayer.MEDIA_ERROR_TIMED_OUT) {
                        rePlay();
                    }
                    mArg1 = arg1;
                    mArg2 = arg2;
                    String dnsAddress = ijkMediaPlayerAdapter.getDnsAddress();
                    PlayerLogUtil.log(TAG, "notifyPlayerFailed:" + "IJKERROR:" + arg1 + "---->" + arg2);
                    if (arg2 == IjkMediaPlayer.MEDIA_AVERROR_HTTP_NOT_FOUND) {
                        ijkMediaPlayerAdapter.notifyPlayerFailed(IjkMediaPlayer.NO_ID_SUB_ERROR_IJK_PLAYER, arg2, dnsAddress);
                    } else {
                        ijkMediaPlayerAdapter.notifyPlayerFailed(arg1, arg2, dnsAddress);//此处将arg1(IJK的错误码)作为what
                    }
                }
                break;
                case IjkMediaPlayer.MEDIA_INFO: {
                    if (arg1 == IjkMediaPlayer.MEDIA_INFO_BUFFERING_START) {
                        ijkMediaPlayerAdapter.notifyBufferingStart();
                    } else if (arg1 == IjkMediaPlayer.MEDIA_INFO_BUFFERING_END) {
                        ijkMediaPlayerAdapter.notifyBufferingEnd();
                    } else if(arg1 == IjkMediaPlayer.MEDIA_INFO_VIDEO_RENDERING_START){
                        ijkMediaPlayerAdapter.notifyVideoRenderingStart();
                    }
                }
                break;
                case IjkMediaPlayer.MEDIA_IJK_SO_INIT_SUCCESS: {
                    initDisposable = Single.fromCallable(() -> {
                                ijkMediaPlayerAdapter.setAvCodecOption();
                                return 1; // 如果返回值没有使用，可以返回 null
                            })
                            .subscribeOn(Schedulers.io()) // 指定在 I/O 线程执行
                            .observeOn(AndroidSchedulers.mainThread()) // 指定观察在主线程执行
                            .subscribe(
                                    integer -> ijkMediaPlayerAdapter.notifyInitComplete(), // 处理成功结果
                                    throwable -> {
                                        // 处理错误
                                        PlayerLogUtil.log(TAG, "Error initializing codec options", throwable.toString());
                                    }
                            );
                }
                break;
                case IjkMediaPlayer.MEDIA_INTERACTION_FIRED: {
                    //arg1 是 int 类型：当前触发事件的毫秒数（软广告是秒级触发，这里给的是真实触发的毫秒数，可自行转换为秒）
                    //arg2 是 int 类型：广告位ID
                    ijkMediaPlayerAdapter.notifyInteractionFired(arg1, arg2);
                }
                break;
                case IjkMediaPlayer.MEDIA_SET_VIDEO_SIZE: {
                    //视频尺寸获取
                    ijkMediaPlayerAdapter.notifyVideoSize(arg1, arg2);
                }
                break;
                default:
                    break;
            }
        }
    }

    @SuppressLint("Wakelock")
    public void setWakeMode(Context context, int mode) {
    }

    private void notifyPlayerPreparingComplete() {
        PlayerLogUtil.log(TAG, "notifyPlayerPreparingComplete");
        setPlayStatus(PlayerConstants.TYPE_PLAYER_PREPARING_COMPLETE);
        if (mIPlayerStateListener != null) {
            mIPlayerStateListener.onPlayerPreparingComplete(mDataSource);
        }
    }

    private void notifyPlayerPreparing() {
        PlayerLogUtil.log(TAG, "notifyPlayerPreparing");
        setPlayStatus(PlayerConstants.TYPE_PLAYER_PREPARING);
        if (mIPlayerStateListener != null) {
            mIPlayerStateListener.onPlayerPreparing(mDataSource);
        }
    }

    private void notifyPlayerIdle() {
        PlayerLogUtil.log(TAG, "notifyPlayerIdle");
        setPlayStatus(PlayerConstants.TYPE_PLAYER_IDLE);
        if (mIPlayerStateListener != null) {
            mIPlayerStateListener.onIdle(mDataSource);
        }
    }

    private void notifyPlayerPlaying() {
        Log.i(TAG, "notifyPlayerPlaying");
        setPlayStatus(PlayerConstants.TYPE_PLAYER_PLAYING);
        if (mIPlayerStateListener != null) {
            mIPlayerStateListener.onPlayerPlaying(mDataSource);
            NetworkUtil.getNetworkState();
        }
    }

    private void notifyPlayerPaused() {
        PlayerLogUtil.log(TAG, "notifyPlayerPaused");
        setPlayStatus(PlayerConstants.TYPE_PLAYER_PAUSED);
        if (mIPlayerStateListener != null) {
            mIPlayerStateListener.onPlayerPaused(mDataSource);
        }
    }

    private void notifyProgress(long progress, long total) {
        if (mIPlayerStateListener != null) {
            mIPlayerStateListener.onProgress(mDataSource, progress, total);
        }
    }

    private void notifyPlayerFailed(int what, int extra, String dnsAddress) {
        PlayerLogUtil.log(TAG, "notifyPlayerFailed:" + "IJKERROR:" + what + "---->" + extra);
        setPlayStatus(PlayerConstants.TYPE_PLAYER_FAILED);
        if (mIPlayerStateListener != null) {
            mIPlayerStateListener.onPlayerFailed(mDataSource, what, extra, dnsAddress);
            NetworkUtil.getNetworkState();
        }
    }

    private void notifyPlayerEnd() {
        PlayerLogUtil.log(TAG, "notifyPlayerEnd");
        setPlayStatus(PlayerConstants.TYPE_PLAYER_END);
        if (mIPlayerStateListener != null) {
            mIPlayerStateListener.onPlayerEnd(mDataSource);
        }
    }

    private void notifySeekStart() {
        PlayerLogUtil.log(TAG, "notifySeekStart");
        setPlayStatus(PlayerConstants.TYPE_SEEK_START);
        if (mIPlayerStateListener != null) {
            mIPlayerStateListener.onSeekStart(mDataSource);
            NetworkUtil.getNetworkState();
        }
    }

    private void notifySeekComplete() {
        PlayerLogUtil.log(TAG, "notifySeekComplete");
        setPlayStatus(PlayerConstants.TYPE_SEEK_COMPLETE);
        if (mIPlayerStateListener != null) {
            mIPlayerStateListener.onSeekComplete(mDataSource);
        }
    }

    private void notifyBufferingStart() {
        PlayerLogUtil.log(TAG, "notifyBufferingStart");
        setPlayStatus(PlayerConstants.TYPE_BUFFERING_START);
        if (mIPlayerStateListener != null) {
            mIPlayerStateListener.onBufferingStart(mDataSource);
            NetworkUtil.getNetworkState();
        }
    }

    private void notifyBufferingEnd() {
        PlayerLogUtil.log(TAG, "notifyBufferingEnd");
        setPlayStatus(PlayerConstants.TYPE_BUFFERING_END);
        if (mIPlayerStateListener != null) {
            mIPlayerStateListener.onBufferingEnd(mDataSource);
        }
    }

    private void notifyBufferProgress(long position, long total) {
        if (mIPlayerBufferProgressListener != null) {
            mIPlayerBufferProgressListener.onBufferProgress(position, total);
        }
    }

    private void notifyInitComplete() {
        PlayerLogUtil.log(TAG, "notifyInitComplete");
        if (mIPlayerInitCompleteListener != null) {
            mIPlayerInitCompleteListener.onPlayerInitComplete(true);
        }
    }

    private void notifyInteractionFired(int position, int id) {
        PlayerLogUtil.log(TAG, "notifyInteractionFired");
        if (mIPlayerStateListener != null) {
            mIPlayerStateListener.onInteractionFired(mDataSource, position, id);
        }
    }

    private void notifyVideoRenderingStart(){
        PlayerLogUtil.log(TAG, "notifyVideoRenderingStart");
        if (mIPlayerStateVideoCoreListener != null) {
            mIPlayerStateVideoCoreListener.onPlayerVideoRenderingStart(mDataSource);
        }
    }

    private void notifyVideoSize(int arg1, int arg2) {
        this.mVideoWidth = arg1;
        this.mVideoHeight = arg2;
        if (mIPlayerStateVideoCoreListener != null) {
            mIPlayerStateVideoCoreListener.onPlayerVideoSizeChanged(mDataSource, this.mVideoWidth, this.mVideoHeight);
        }
    }

    @Override
    public void setLogInValid() {
        mIjkMediaPlayer.setLogInValid();
    }

    /**
     * 设置是否在prepare完成后自动播放
     * @param enabled boolean
     * true: 自动播放 默认值
     */
    @Override
    public void setPlayNowOnPrepared(boolean enabled) {
        isPlayNow = enabled;
        performAction(() -> mIjkMediaPlayer.setAutoPlayOnPrepared(enabled));
    }

    @Override
    public boolean getPlayNow() {
        return isPlayNow;
    }

    @Override
    public void setAudioFadeEnabled(boolean enabled) {
        mIjkMediaPlayer.setAudioFadeEnabled(enabled);
    }

    /**
     * 设置音频淡入时长
     *
     * @param type 淡入类型（或类型的组合）0x11, 0x12, 0x13：播放开始淡入、Seek起播淡入、Pause起播淡入
     * @param msec 淡入时长，单位为毫秒（ms）
     */
    private void setAudioFadeInDuration(int type, long msec) {
        mIjkMediaPlayer.setAudioFadeInDuration(type, msec);
    }

    /**
     * 设置音频淡出时长
     *
     * @param type 淡出类型（或类型的组合）0x21, 0x22, 0x23：播放结束淡出、Stop起播淡出、Pause停播淡出
     * @param msec 淡出时长，单位为毫秒（ms）
     */
    private void setAudioFadeOutDuration(int type, long msec) {
        performAction(() -> mIjkMediaPlayer.setAudioFadeOutDuration(type, msec));
    }

    /**
     * 设置解码参数，环路滤波
     * 这段代码需要运行在子线程：注意
     */
    private void setAvCodecOption() {
        performAction(() -> {
            if (mContext != null) {
                copyMcuFile(mContext, "model.bin", new File(mContext.getFilesDir() + "/model.bin"));
                mIjkMediaPlayer.setAvCodecOption("model_path", mContext.getFilesDir() + "/model.bin");
            } else {
                PlayerLogUtil.log(TAG, "Context is null, cannot set codec options");
            }
        });
    }

    private void copyMcuFile(Context context, String sourceFileName, File dest) throws IOException {
        InputStream in = null;
        OutputStream out = null;
        final int READ_BUFFER_SIZE = 4 * 1024 * 1024;
        try {
            in = context.getAssets().open(sourceFileName);
            out = new FileOutputStream(dest);
            byte[] buf = new byte[READ_BUFFER_SIZE];
            int bytesRead;
            while ((bytesRead = in.read(buf)) > 0) {
                out.write(buf, 0, bytesRead);
            }
            PlayerLogUtil.log(TAG, "copyFile  finished");
        } catch (Exception e) {
            PlayerLogUtil.log(TAG, "copyFile  Exception:"+ e);
        } finally {
            if (in != null) {
                in.close();
            }
            if (out != null) {
                out.close();
            }
        }
    }

}
