package com.kaolafm.opensdk.http.cache;

import androidx.annotation.Nullable;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Set;

/**
 * lru缓存。当缓存满了，会优先清理那些最近不常用的数据。
 * <AUTHOR>
 * @date 2018/4/18
 */

public class LruCache<K, V> implements Cache<K, V> {
    private final LinkedHashMap<K, V> mCache = new LinkedHashMap<>(100, 0.75f, true);

    private int mCurrentSize = 0;

    private final int mInitialMaxSize;

    private final int mMaxSize;

    public LruCache(int cacheSize) {
        mMaxSize = cacheSize;
        mInitialMaxSize = cacheSize;
    }

    @Override
    public synchronized int size() {
        return mCurrentSize;
    }

    @Override
    public synchronized int getMaxSize() {
        return mMaxSize;
    }

    @Nullable
    @Override
    public synchronized V get(K key) {
        return mCache.get(key);
    }

    @Nullable
    @Override
    public synchronized V put(K key, V value) {
        final int itemSize = getItemSize(value);
        if (itemSize >= mMaxSize){
            onItemEvicted(key, value);
            return null;
        }
        final V result = mCache.put(key, value);
        if (value != null){
            mCurrentSize += getItemSize(value);
        }
        if (result != null){
            mCurrentSize -= getItemSize(value);
        }
        evict();
        return result;
    }

    /**
     * 当缓存中已占用的总 size 大于所能允许的最大 size ,会使用  {@link #trimToSize(int)} 开始清除满足条件的条目
     */
    private void evict() {
        trimToSize(mMaxSize);
    }

    /**
     * 当指定的 size 小于当前缓存已占用的总 size 时,会开始清除缓存中最近最少使用的条目
     *
     * @param size {@code size}
     */
    private synchronized void trimToSize(int size) {
        HashMap.Entry<K, V> last;
        while (mCurrentSize > size){
            last = mCache.entrySet().iterator().next();
            final V toRemove = last.getValue();
            mCurrentSize -= getItemSize(toRemove);
            final K key = last.getKey();
            mCache.remove(key);
            onItemEvicted(key, toRemove);
        }
    }

    /**
     * 当缓存中有被驱逐的条目时,会回调此方法,默认空实现,子类可以重写这个方法
     *
     * @param key   被驱逐条目的 {@code key}
     * @param value 被驱逐条目的 {@code value}
     */
    private void onItemEvicted(K key, V value) {

    }

    /**
     * 返回每个 {@code item} 所占用的 size,默认为1,这个 size 的单位必须和构造函数所传入的 size 一致
     * 子类可以重写这个方法以适应不同的单位,比如说 bytes
     *
     * @param item 每个 {@code item} 所占用的 size
     * @return 单个 item 的 {@code size}
     */
    private int getItemSize(V item) {
        return 1;
    }

    @Nullable
    @Override
    public synchronized V remove(K key) {
        return mCache.remove(key);
    }

    @Override
    public synchronized boolean containsKey(K key) {
        return mCache.containsKey(key);
    }

    @Override
    public Set<K> keySet() {
        return mCache.keySet();
    }

    @Override
    public void clear() {
        trimToSize(0);
    }
}
