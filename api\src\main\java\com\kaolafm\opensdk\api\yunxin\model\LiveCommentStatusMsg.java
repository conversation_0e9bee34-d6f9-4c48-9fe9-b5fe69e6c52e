package com.kaolafm.opensdk.api.yunxin.model;

import android.os.Parcel;
import android.os.Parcelable;

public class LiveCommentStatusMsg implements Parcelable {

    /**
     * 评论状态：1-通过
     */
    public static final int STATUS_PASS = 1;
    /**
     * 评论状态：2-暂停
     */
    public static final int STATUS_REMOVE = 2;

    // 评论消息id
    private String id;

    // 评论消息内容
    private String content;

    // 评论状态：1-通过，2-暂停
    private Integer status;

    public LiveCommentStatusMsg() {
    }

    protected LiveCommentStatusMsg(Parcel in) {
        id = in.readString();
        content = in.readString();
        if (in.readByte() == 0) {
            status = null;
        } else {
            status = in.readInt();
        }
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(id);
        dest.writeString(content);
        if (status == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(status);
        }
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<LiveCommentStatusMsg> CREATOR = new Creator<LiveCommentStatusMsg>() {
        @Override
        public LiveCommentStatusMsg createFromParcel(Parcel in) {
            return new LiveCommentStatusMsg(in);
        }

        @Override
        public LiveCommentStatusMsg[] newArray(int size) {
            return new LiveCommentStatusMsg[size];
        }
    };

    public String getId() {
        return id == null ? "" : id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getContent() {
        return content == null ? "" : content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "LiveCommentStatusMsg{" +
                "id='" + id + '\'' +
                ", content='" + content + '\'' +
                ", status=" + status +
                '}';
    }
}
