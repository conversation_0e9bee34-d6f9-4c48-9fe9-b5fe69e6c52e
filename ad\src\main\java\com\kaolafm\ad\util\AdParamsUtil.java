package com.kaolafm.ad.util;

import com.kaolafm.ad.api.internal.OrderedPair;
import com.kaolafm.base.utils.DateUtil;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;

/**
 * 广告参数相关工具类
 * <AUTHOR>
 * @date 2020-01-13
 */
public class AdParamsUtil {

    public static String createParamData(List<Object> list) {
        StringBuilder sb = new StringBuilder();
        for (Object obj : list) {
            sb.append(obj == null ? "" : obj).append("|");
        }
        return sb.deleteCharAt(sb.length() - 1).toString();
    }

    public static String createParamData(Set<OrderedPair> set, String... exclude) {
        StringBuilder sb = new StringBuilder();
        for (OrderedPair pair : new TreeSet<>(set)) {
            if (!exclude(exclude, pair.getKey())) {

                //服务器那边如果为空，不要传null。（例：||||）
                if (pair.getValue() != null) {
                    sb.append(pair.getValue());
                }
                sb.append("|");
            }
        }
        return sb.deleteCharAt(sb.length() - 1).toString();
    }

    private static boolean exclude(String[] a, String key) {
        if (a == null) {
            return false;
        }
        for (String s : a) {
            if (s.equals(key)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取当前格式化后的服务器时间，格式化方式为yyyy-MM-dd_HH:mm:ss
     *
     * @return
     */
    public static String getTime() {
        return DateUtil.formatMillis("yyyy-MM-dd_HH:mm:ss", DateUtil.getServerTime());
    }

    /**
     * 根据id获取请求广告详情需要的参数加密字符串
     *
     * @param nextAudioId     下一个准备播放的单曲id。播放专辑或AI电台时必传
     * @param playingAlbumId  正在播放的专辑id 与playingRadioId互斥。播放专辑单曲切换时必传
     * @param playingRadioId  正在播放的电台di 与playingAlbumId互斥。播放AI电台单曲切换时必传
     * @param columnId        需要替换封面的栏目ID， 栏目成员封面替换广告传该参数，请求栏目封面替换广告时必传
     * @param exposureSceneId 当前广告曝光场景ID，必传
     * @return
     */
    public static String getAdvanceAttrs(long nextAudioId, long playingAlbumId, long playingRadioId, long columnId, int exposureSceneId) {
        HashMap<String, Object> attrs = new HashMap<>(4);
        if (nextAudioId > 0) {
            attrs.put("PLAYING_AUDIO_ID", nextAudioId);
        }
        if (playingAlbumId > 0) {
            attrs.put("PLAYING_ALBUM_ID", playingAlbumId);
        }
        if (playingRadioId > 0) {
            attrs.put("PLAYING_RADIO_ID", playingRadioId);
        }
        if (columnId > 0) {
            attrs.put("COLUMN_ID", columnId);
        }
        if (exposureSceneId > 0) {
            attrs.put("AD_EXPOSURE_SCENE", exposureSceneId);
        }
        try {
            return URLEncoder.encode(attrs.toString(), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 正在播放专辑时，获取请求广告的参数
     *
     * @param nextAudioId     下一个准备播放的单曲id。播放专辑或AI电台时必传
     * @param playingAlbumId  正在播放的专辑id。播放专辑单曲切换时必传
     * @param exposureSceneId 当前广告曝光场景ID，必传
     * @return
     */
    public static String getAlbumAttrs(long nextAudioId, long playingAlbumId, int exposureSceneId) {
        return getAdvanceAttrs(nextAudioId, playingAlbumId, -1, -1, exposureSceneId);
    }

    /**
     * 正在播放电台时，获取请求广告的参数
     *
     * @param nextAudioId     下一个准备播放的单曲id。播放专辑或AI电台时必传
     * @param playingRadioId  正在播放的电台di。播放AI电台单曲切换时必传
     * @param exposureSceneId 当前广告曝光场景ID，必传
     * @return
     */
    public static String getRadioAttrs(long nextAudioId, long playingRadioId, int exposureSceneId) {
        return getAdvanceAttrs(nextAudioId, -1, playingRadioId, -1, exposureSceneId);
    }
}
