<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".emergency.EmergencyActivity">

    <LinearLayout
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <Button
            android:id="@+id/btn_launch_request"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="启动拉取一次" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp">

            <Button
                android:id="@+id/btn_begin_polling"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="开始轮询请求" />

            <Button
                android:id="@+id/btn_stop_polling"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:text="停止轮询请求" />
        </LinearLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="短链接请求结果:" />

        <EditText
            android:id="@+id/et_http_result"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/layout_present"
            android:background="#111111"
            android:hint="结果在此"
            android:textColor="#ffffff" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp">

            <Button
                android:id="@+id/btn_start_socket_listener"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="11dp"
                android:text="监听应急广播(会上报位置一次)" />

            <Button
                android:id="@+id/btn_response_listener"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="11dp"
                android:text="监听上报地址结果" />
            <Button
                android:id="@+id/btn_stop_socket_listener"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="11dp"
                android:text="停止请求" />

        </LinearLayout>


        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="请求过程:" />

        <EditText
            android:id="@+id/et_socket_progress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#111111"
            android:textSize="12dp"
            android:hint="结果在此"
            android:textColor="#ffffff" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="长链接请求结果:" />

        <EditText
            android:id="@+id/et_socket_result"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#111111"
            android:hint="结果在此"
            android:textColor="#ffffff" />

    </LinearLayout>
</ScrollView>