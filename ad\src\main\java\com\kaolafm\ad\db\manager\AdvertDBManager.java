package com.kaolafm.ad.db.manager;

import android.util.Log;

import com.kaolafm.ad.AdConstant;
import com.kaolafm.ad.api.model.AdvertisingDetails;
import com.kaolafm.ad.db.greendao.AdvertisingDetailsDao;
import com.kaolafm.ad.util.DownloadUtil;
import com.kaolafm.opensdk.apt.DBOpt;
import com.kaolafm.opensdk.db.OnQueryListener;
import com.kaolafm.opensdk.db.manager.BaseDBManager;
import com.kaolafm.opensdk.di.scope.AppScope;

import java.util.List;

import javax.inject.Inject;

/**
 * 广告数据库管理类 do
 * <AUTHOR>
 * @date 2020-02-05
 */
@AppScope
@DBOpt(name = "Advertising.db")
public class AdvertDBManager extends BaseDBManager<AdvertisingDetails, AdvertisingDetailsDao> {

    @Inject
    AdvertDBManager() {
    }

    public void updateByZoneId(AdvertisingDetails details) {
        Log.e("AdvertDBManager", "===/2.0.0/eloadAdContent updateByZoneId moreInteractionDestUrl=" + (details == null ? "null" : details.getMoreInteractionDestUrl()));
        if (details == null) {
            return;
        }
        runInNewThread(() -> {
            AdvertisingDetails oldAdvert = mDao
                    .queryBuilder()
                    .where(AdvertisingDetailsDao.Properties.AdZoneId.eq(details.getAdZoneId()))
                    .unique();
            if (oldAdvert != null) {
                mDao.delete(oldAdvert);
            }
            //由于key不是由数据库自动生成且不确定key是否存在于数据库，所以不能使用save。
            mDao.insertOrReplace(details);
            return true;
        }, null);
    }

    public void queryByZoneId(String zoneId, OnQueryListener<List<AdvertisingDetails>> listener) {
        runInNewThread(() -> mDao
                .queryBuilder()
                .where(AdvertisingDetailsDao.Properties.AdZoneId.eq(zoneId))
                .list(), listener);
    }

    /**
     * 根据SessionId删除广告，就是删除指定某次请求的广告
     *
     * @param sessionId
     */
    public void deleteBySessionId(String sessionId) {
        runInNewThread(() -> {
            List<AdvertisingDetails> list = mDao
                    .queryBuilder()
                    .where(AdvertisingDetailsDao.Properties.SessionId.eq(sessionId))
                    .list();

            delete(list);
            return true;
        }, null);
    }

    public void queryTimedAdvert(OnQueryListener<List<AdvertisingDetails>> listener) {
        runInNewThread(() -> mDao
                .queryBuilder()
                .where(AdvertisingDetailsDao.Properties.Subtype.eq(AdConstant.TYPE_TIMED_ADVERT))
                .list(), listener);
    }

    public void deleteTimedAdvert() {
        runInNewThread(() -> {
            List<AdvertisingDetails> list = mDao
                    .queryBuilder()
                    .where(AdvertisingDetailsDao.Properties.Subtype.eq(AdConstant.TYPE_TIMED_ADVERT))
                    .list();
            for (AdvertisingDetails details : list) {
                DownloadUtil.delete(details);
            }
            delete(list);
            return true;
        }, null);
    }
}
