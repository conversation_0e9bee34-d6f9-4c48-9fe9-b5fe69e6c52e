package com.kaolafm.opensdk.api.search.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

public class Compere implements Parcelable {
    /**
     * 主持人名字
     */
    @SerializedName("name")
    private String name;

    /**
     * 主持人简介
     */
    @SerializedName("des")
    private String des;

    /**
     * 主持人头像url
     */
    @SerializedName("img")
    private String img;


    protected Compere(Parcel in) {
        name = in.readString();
        des = in.readString();
        img = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(name);
        dest.writeString(des);
        dest.writeString(img);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<Compere> CREATOR = new Creator<Compere>() {
        @Override
        public Compere createFromParcel(Parcel in) {
            return new Compere(in);
        }

        @Override
        public Compere[] newArray(int size) {
            return new Compere[size];
        }
    };

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public static Creator<Compere> getCREATOR() {
        return CREATOR;
    }

    @Override
    public String toString() {
        return "Compere{" +
                "name='" + name + '\'' +
                ", des='" + des + '\'' +
                ", img='" + img + '\'' +
                '}';
    }
}
