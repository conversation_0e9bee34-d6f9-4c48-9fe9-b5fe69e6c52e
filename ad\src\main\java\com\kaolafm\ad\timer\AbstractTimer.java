package com.kaolafm.ad.timer;

import com.kaolafm.ad.db.manager.AdvertDBManager;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.log.Logging;

import javax.inject.Inject;

/**
 * 广告定时抽象类
 *
 * <AUTHOR>
 * @date 2020-02-24
 */
public abstract class AbstractTimer implements Timer {

    protected static final String TIME = "time";

    protected static final String ID = "id";

    private int mId = 0;
    private int mTime = 0;

    @Inject
    @AppScope
    AdvertDBManager mAdvertDBManager;

    /**
     * 数据库查询广告，并曝光
     * @param id
     */
    protected void expose(int id, int time) {
        //防止同一时间多次定时。
        if (mId == id && mTime == time) {
            return;
        }
        Logging.d("开始曝光定时广告，id=%s, time=%s", id, DateUtil.formatMillis((long) time * 1000));
        mId = id;
        mTime = time;
        mAdvertDBManager.queryById(id, details -> {
            AdvertisingManager.getInstance().expose(details);
            TimedAdvertManager.getInstance().removeTask(new AdvertTask(id, time * 1000));
        });

    }
}
