package com.kaolafm.opensdk.http.socket;

import android.os.SystemClock;

import com.kaolafm.opensdk.http.error.SocketEngineIOException;
import com.kaolafm.opensdk.http.socket.parser.Parser;
import com.kaolafm.opensdk.log.Logging;

import java.net.URI;
import java.util.Timer;
import java.util.TimerTask;

import javax.inject.Inject;

/**
 * socket连接类，用来管理连接
 *
 * <AUTHOR>
 * @date 2020-02-03
 */
public class SocketConnection extends Emitter {

    ReadyState readyState;

    @Inject
    SocketEngine socketEngine;

    private URI uri;
    private Options options;

    private boolean skipReconnect = false;
    private EventHandler eventHandler;
    private long timeout;
    private boolean reconnecting = false;
    private boolean reconnection;
    private int reconnectionAttempts;
    private Backoff backoff;
    private Parser.Decoder decoder;
    private long lastPingTime;
    private Socket socket;

    @Inject
    public SocketConnection() {
    }

    public SocketConnection(Socket socket, URI uri, Options options) {
        init(socket, uri, options);
    }

    void init(Socket socket, URI uri, Options options) {
        this.socket = socket;
        if (options == null) {
            options = new Options();
        }
        if (options.path == null) {
            options.path = "/socket.io";
        }
        this.uri = uri;
        this.options = options;
        this.timeout = options.timeout;
        reconnection = options.reconnection;
        reconnectionAttempts = options.reconnectionAttempts == 0 ? Integer.MAX_VALUE : options.reconnectionAttempts;
        long reconnectionDelay = options.reconnectionDelay == 0 ? 1000 : options.reconnectionDelay;
        long reconnectionDelayMax = options.reconnectionDelayMax == 0 ? 5000 : options.reconnectionDelayMax;
        double randomizationFactor = Math.abs(options.randomizationFactor - 0.0F) < 1e-6 ? 0.5F : options.randomizationFactor;
        backoff = new Backoff().setMin(reconnectionDelay).setMax(reconnectionDelayMax).setJitter(randomizationFactor);
        decoder = new Parser.Decoder();
        readyState = ReadyState.CLOSED;
    }

    /**
     * 开始连接
     */
    void start() {
        start(null);
    }

    void start(final OpenListener listener) {
        EventThread.exec(() -> {
            if (readyState == ReadyState.OPEN || readyState == ReadyState.OPENING) {
                return;
            }
            socketEngine.options(uri, options);
            readyState = ReadyState.OPENING;
            skipReconnect = false;
            socketEngine.on(SocketEvent.EVENT_TRANSPORT, args -> emit(SocketEvent.EVENT_TRANSPORT, args));
            eventHandler = new EventHandler(socketEngine);
            EventHandler.Handler openListener = eventHandler.add(SocketEvent.EVENT_OPEN, args -> {
                onOpen();
                if (listener != null) {
                    listener.call(null);
                }
            });
            eventHandler.add(SocketEvent.EVENT_ERROR, args -> {
                Object data = args.length > 0 ? args[0] : null;
                Logging.e("socket connect error");
                cleanup();
                readyState = ReadyState.CLOSED;
                emitAll(SocketEvent.EVENT_CONNECT_ERROR, data);
                if (listener != null) {
                    Exception err = new SocketEngineIOException("Connection error", data instanceof Exception ? (Exception) data : null);
                    listener.call(err);
                } else {
                    // 当没有回调的时候，尝试重连。
                    maybeReconnectOnOpen();
                }
            });
            if (timeout >= 0) {
                //处理超时。如果超时时间内完成了连接会走cleanup()，将该定时任务取消。
                timer(timeout, new TimerTask() {
                    @Override
                    public void run() {
                        openListener.destroy();
                        socketEngine.close();
                        socketEngine.emit(SocketEvent.EVENT_ERROR, new SocketEngineIOException("timeout"));
                        emitAll(SocketEvent.EVENT_CONNECT_TIMEOUT, timeout);
                    }
                });
            }
            socketEngine.open();
        });
    }

    private void maybeReconnectOnOpen() {
        if (!reconnecting && reconnection && backoff.getAttempts() == 0) {
            reconnect();
        }
    }

    private void reconnect() {
        if (reconnecting || skipReconnect) {
            return;
        }
        if (backoff.getAttempts() >= reconnectionAttempts) {
            backoff.reset();
            emitAll(SocketEvent.EVENT_RECONNECT_FAILED);
            reconnecting = false;
        } else {
            reconnecting = true;
            long delay = backoff.duration();
            Logging.d("will wait %dms before reconnect attempt", delay);
            timer(delay, new TimerTask() {
                @Override
                public void run() {
                    if (skipReconnect) {
                        return;
                    }
                    int attempts = backoff.getAttempts();
                    emitAll(SocketEvent.EVENT_RECONNECT_ATTEMPT, attempts);
                    emitAll(SocketEvent.EVENT_RECONNECTING, attempts);
                    //添加完上面两个事件后，再次检查是否需要跳过重连。
                    if (skipReconnect) {
                        return;
                    }
                    start(e -> {
                        //如果异常不为空，说明是第一次连接失败。为空说明是重连成功。
                        if (e != null) {
                            Logging.e("reconnect attempt error");
                            reconnecting = false;
                            reconnect();
                            emitAll(SocketEvent.EVENT_RECONNECT_ERROR, e);
                        } else {
                            Logging.d("reconnect success");
                            onReconnect();
                        }
                    });
                }
            });
        }
    }

    private void onReconnect() {
        int attempts = this.backoff.getAttempts();
        this.reconnecting = false;
        this.backoff.reset();
        this.emitAll(SocketEvent.EVENT_RECONNECT, attempts);
    }

    private void emitAll(String event, Object... data) {
        emit(event, data);
        socket.emit(event, data);
    }

    private void onOpen() {
        Logging.d("socket open");
        cleanup();
        readyState = ReadyState.OPEN;
        emit(SocketEvent.EVENT_OPEN);
        eventHandler.add(SocketEvent.EVENT_DATA, args -> onData(args[0]));
        eventHandler.add(SocketEvent.EVENT_PING, args -> onPing());
        eventHandler.add(SocketEvent.EVENT_PONG, args -> onPong());
        eventHandler.add(SocketEvent.EVENT_ERROR, args -> onError((Exception) args[0]));
        eventHandler.add(SocketEvent.EVENT_CLOSE, args -> onClose((String) args[0]));
        decoder.onDecoded(this::onDecoded);

    }

    private void onDecoded(Packet packet) {
        emit(SocketEvent.EVENT_PACKET, packet);
    }

    private void onClose(String reason) {
        Logging.d("close because %s", reason);
        cleanup();
        backoff.reset();
        readyState = ReadyState.CLOSED;
        emit(SocketEvent.EVENT_CLOSE, reason);
        if (reconnection && !skipReconnect) {
            reconnect();
        }
    }

    private void onError(Exception e) {
        Logging.e(e.getCause(), e.getMessage());
        emitAll(SocketEvent.EVENT_ERROR, e);
    }

    private void onPong() {
        emitAll(SocketEvent.EVENT_PONG, lastPingTime > 0 ? SystemClock.elapsedRealtime() - lastPingTime : 0);
    }

    private void onPing() {
        lastPingTime = SystemClock.elapsedRealtime();
        emitAll(SocketEvent.EVENT_PING);
    }

    private void onData(Object data) {
        if (data instanceof String) {
            decoder.add((String) data);
        } else if (data instanceof byte[]) {
            decoder.add((byte[]) data);
        }
    }

    private void timer(long time, TimerTask task) {
        //处理超时。如果超时内完成了连接会走cleanup()，将该定时任务取消。
        try{
            Timer timer = new Timer();
            timer.schedule(task, time);
            eventHandler.add(timer::cancel);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public void release() {
        close();
    }

    private void close() {
        skipReconnect = true;
        reconnecting = false;
        if (readyState != ReadyState.OPEN) {
            cleanup();
        }
        backoff.reset();
        readyState = ReadyState.CLOSED;
        socketEngine.close();
    }

    private void cleanup() {
        Logging.d("cleanup event, encode and decode");
        eventHandler.cleanup();
        socket.enableEncoding();
        lastPingTime = -1;
        decoder.destroy();
    }

    public void write(Object data) {
        if (data instanceof String) {
            socketEngine.write((String) data);
        } else if (data instanceof byte[]) {
            socketEngine.write((byte[]) data);
        }
    }


    public interface OpenListener {
        /**
         * 长连接连接成功或失败回调
         *
         * @param e 为null表示连接成功，不为null表示连接失败
         */
        void call(Exception e);
    }

    public static class Options extends SocketEngine.Options {
        public boolean reconnection = true;
        public int reconnectionAttempts;
        public long reconnectionDelay;
        public long reconnectionDelayMax;
        public double randomizationFactor;

        /**
         * Connection timeout (ms). Set -1 to disable.
         */
        public long timeout = 20000;
    }
}
