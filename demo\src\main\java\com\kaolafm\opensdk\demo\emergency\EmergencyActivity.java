package com.kaolafm.opensdk.demo.emergency;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.widget.EditText;
import android.widget.Toast;

import com.google.gson.Gson;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.CrashMessageBaseBean;
import com.kaolafm.opensdk.api.emergency.EmergencyRequest;
import com.kaolafm.opensdk.api.emergency.model.EmergencyBroadcast;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.DemoApplication;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.emergencybroadcast.EmergencyBroadcastListener;
import com.kaolafm.opensdk.emergencybroadcast.EmergencyBroadcastManager;
import com.kaolafm.opensdk.emergencybroadcast.UploadLocationResponseListener;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.socket.ConnectErrorListener;
import com.kaolafm.opensdk.socket.ConnectLostListener;
import com.kaolafm.opensdk.socket.SocketManager;

public class EmergencyActivity extends BaseActivity {

    public static final String TAG = "EmergencyActivity";

    private EditText httpResultEt;
    private EditText socketResultEt;
    private EditText socketProgressEt;

    @Override
    public int getLayoutId() {
        return R.layout.activity_emergency;
    }

    @Override
    public void initView(Bundle savedInstanceState) {

        httpResultEt = findViewById(R.id.et_http_result);
        socketResultEt = findViewById(R.id.et_socket_result);
        socketProgressEt = findViewById(R.id.et_socket_progress);

        findViewById(R.id.btn_launch_request).setOnClickListener(view -> {
            new EmergencyRequest().getEmergencyMessage(new HttpCallback<EmergencyBroadcast>() {
                @Override
                public void onSuccess(EmergencyBroadcast emergencyBroadcast) {
                    Gson gson = new Gson();
                    String json = gson.toJson(emergencyBroadcast);
                    httpResultEt.setText(json);
                }

                @Override
                public void onError(ApiException e) {
                    httpResultEt.setText(e.toString());
                }
            });
        });
        findViewById(R.id.btn_begin_polling).setOnClickListener(view -> {
            EmergencyBroadcastManager.getInstance().requestEmergencyBroadcast(10, new EmergencyBroadcastListener() {
                @Override
                public void onSuccess(EmergencyBroadcast emergencyBroadcast) {
                    Gson gson = new Gson();
                    String json = gson.toJson(emergencyBroadcast);
                    String text = "本次时间:" + System.currentTimeMillis() + "\n";
                    toast("轮询短链接：" + json);
                    httpResultEt.setText(text + json);
                }

                @Override
                public void onError(ApiException e) {
                    String text = "本次error时间:" + System.currentTimeMillis() + "\n";
                    toast("短链接报错：" + e.toString());
                    httpResultEt.setText(text + e.toString());
                }
            });
        });

        findViewById(R.id.btn_stop_polling).setOnClickListener(view -> {
            EmergencyBroadcastManager.getInstance().stopPollingRequest();
            httpResultEt.setText("关闭轮询");
        });


        findViewById(R.id.btn_start_socket_listener).setOnClickListener(view -> {
            addText2Progress("开始监听应急广播，上报一次位置信息，打开定位刷新时上报位置");
            EmergencyBroadcastManager.getInstance().requestEmergencyBroadcast(new EmergencyBroadcastListener() {
                @Override
                public void onSuccess(EmergencyBroadcast emergencyBroadcast) {
                    Gson gson = new Gson();
                    String json = gson.toJson(emergencyBroadcast);
                    String text = "本次时间:" + System.currentTimeMillis() + "\n";
                    toast("收到应急广播" + json);
                    socketResultEt.setText(text + json);
                }

                @Override
                public void onError(ApiException e) {
                    String text = "本次error时间:" + System.currentTimeMillis() + "\n";
                    toast("应急广播报错：" + e.toString());
                    socketResultEt.setText(text + e.toString());
                }
            });
        });
        findViewById(R.id.btn_stop_socket_listener).setOnClickListener(view -> {
            EmergencyBroadcastManager.getInstance().disconnectSocket();
            addText2Progress("断开socket连接");
        });

        findViewById(R.id.btn_response_listener).setOnClickListener(view -> {
            addText2Progress("开始监听位置上报");
            EmergencyBroadcastManager.getInstance().socketUploadLocationResponse(new UploadLocationResponseListener() {

                @Override
                public void onSuccess(String s) {
                    String text = "本次时间:" + System.currentTimeMillis() + "\n";
                    toast("位置上报成功：" + s);
                    socketResultEt.setText(text + s);
                }

                @Override
                public void onError(ApiException e) {
                    String text = "本次error时间:" + System.currentTimeMillis() + "\n";
                    toast("上报位置错误：" + e.toString());
                    socketResultEt.setText(text + e.toString());
                }
            });
        });

    }

    @Override
    public void initData() {
        SocketManager.getInstance().registerConnectLostListener(new ConnectLostListener() {
            @Override
            public void onConnectLost(Object... objects) {
                Toast.makeText(EmergencyActivity.this, "onConnectLost", Toast.LENGTH_LONG).show();
                String result = "";
                if (objects[0] != null) {
                    result += objects[0].toString();
                }
                String text = "本次error时间:" + System.currentTimeMillis() + "\n";
                toast("连接断开" + result);
                addText2Progress(text + "已断开" + result);
            }
        });

        SocketManager.getInstance().registerConnectErrorListener(new ConnectErrorListener() {
            @Override
            public void onConnectError(Object... objects) {
                Toast.makeText(EmergencyActivity.this, "onConnectError", Toast.LENGTH_LONG).show();
                String result = "";
                if (objects[0] != null) {
                    result += objects[0].toString();
                }
                String text = "本次error时间:" + System.currentTimeMillis() + "\n";
                toast("连接错误" + result);
                addText2Progress(text + "连接出现错误" + result);

            }
        });
    }

    private void addText2Progress(String currentProgress) {
        String result = socketProgressEt.getText().toString();
        if (TextUtils.isEmpty(result)) {
            socketProgressEt.setText(currentProgress);
        } else {
            result += "\n" + currentProgress;
            socketProgressEt.setText(result);
        }
    }

    public void toast(String toast) {
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                Toast.makeText(DemoApplication.getContext(), toast, Toast.LENGTH_LONG).show();
            }
        });
    }
}