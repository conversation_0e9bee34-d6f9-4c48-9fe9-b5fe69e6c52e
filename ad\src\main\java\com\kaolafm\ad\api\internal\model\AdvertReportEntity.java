package com.kaolafm.ad.api.internal.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 对外暴露的关于上报使用到的参数
 *
 * <AUTHOR>
 * @date 2020-02-27
 */
public class AdvertReportEntity implements Parcelable {

    /**秒针*/
    public static final int MONITOR_TYPE_MIAOZHEN = 1;

    /**talkingdata*/
    public static final int MONITOR_TYPE_TALKINGDATA = 3;
    /**
     * 广告展示后续动作需要带回的sessionId。每次的请求唯一，一个广告集合共用一个。用于数据上报。
     */
    private String sessionId;

    /**监控类型：0为不监控：1为秒针；2为AdMaster；3为talkingdata*/
    private int monitorType;

    /**第三方点击监控地址*/
    private String clickMonitorUrl;

    /**第三方展示监控地址*/
    private String pvMonitorUrl;

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public int getMonitorType() {
        return monitorType;
    }

    public void setMonitorType(int monitorType) {
        this.monitorType = monitorType;
    }

    public String getClickMonitorUrl() {
        return clickMonitorUrl;
    }

    public void setClickMonitorUrl(String clickMonitorUrl) {
        this.clickMonitorUrl = clickMonitorUrl;
    }

    public String getPvMonitorUrl() {
        return pvMonitorUrl;
    }

    public void setPvMonitorUrl(String pvMonitorUrl) {
        this.pvMonitorUrl = pvMonitorUrl;
    }

    public AdvertReportEntity() {
    }

    protected AdvertReportEntity(Parcel in) {
        sessionId = in.readString();
        monitorType = in.readInt();
        clickMonitorUrl = in.readString();
        pvMonitorUrl = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(sessionId);
        dest.writeInt(monitorType);
        dest.writeString(clickMonitorUrl);
        dest.writeString(pvMonitorUrl);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<AdvertReportEntity> CREATOR = new Creator<AdvertReportEntity>() {
        @Override
        public AdvertReportEntity createFromParcel(Parcel in) {
            return new AdvertReportEntity(in);
        }

        @Override
        public AdvertReportEntity[] newArray(int size) {
            return new AdvertReportEntity[size];
        }
    };
}
