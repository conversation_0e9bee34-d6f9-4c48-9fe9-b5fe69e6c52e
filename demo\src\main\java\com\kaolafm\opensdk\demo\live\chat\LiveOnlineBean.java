package com.kaolafm.opensdk.demo.live.chat;

import android.graphics.Bitmap;
import android.os.SystemClock;

public class LiveOnlineBean {
    /**
     * 是否为马甲用户
     */
    private boolean isRobotUser;
    /**
     * 当前用户的昵称
     */
    private String nickName;
    /**
     * 当前用户的uid
     */
    private String uid;
    /**
     * 当前用户的图像
     */
    private String avatar;
    private Bitmap userBitmap;
    /**
     * 0是默认的类型，可以从 uig 里面获取用户的图像列表； 1只能从 userBitmap 获取用户图像
     * <p>
     * 默认去除 1 的 bitmap 对象，因此这里会一直是0
     */
    private int type = 0;

    /**
     * 当前类实例初始化时间
     */
    private long birthTime = SystemClock.elapsedRealtime();

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public int getType() {
//        return type;
        return 0; //默认去除 1 的 bitmap 对象，因此这里会一直是0
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public Bitmap getUserBitmap() {
        return userBitmap;
    }

    public void setUserBitmap(Bitmap userBitmap) {
        this.userBitmap = userBitmap;
    }

    public long getBirthTime() {
        return birthTime;
    }

    public void setBirthTime(long birthTime) {
        this.birthTime = birthTime;
    }

    public boolean isRobotUser() {
        return isRobotUser;
    }

    public void setRobotUser(boolean robotUser) {
        isRobotUser = robotUser;
    }

    @Override
    public boolean equals(Object o) {
        if (o instanceof LiveOnlineBean) {
            LiveOnlineBean liveOnlineBean = (LiveOnlineBean) o;
            boolean flag = this.uid.equals(liveOnlineBean.getUid());
            return flag;
        }
        return super.equals(o);
    }
}