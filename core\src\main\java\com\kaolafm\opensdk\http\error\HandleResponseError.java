package com.kaolafm.opensdk.http.error;

import android.net.ParseException;
import androidx.annotation.NonNull;

import com.google.gson.JsonParseException;
import com.kaolafm.base.utils.NetworkUtil;
import com.kaolafm.opensdk.di.component.ComponentKit;

import org.json.JSONException;

import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;

import javax.net.ssl.SSLHandshakeException;

import retrofit2.HttpException;

/**
 * 处理网络常见错误
 * <AUTHOR>
 * @date 2018/4/18
 */
public class HandleResponseError {


    private HandleResponseError() {
    }

    @NonNull
    private static ApiException getApiException(Throwable t) {
        if (t instanceof ApiException) {
            return (ApiException) t;
        }
        String msg;
        int code;
        ApiException apiException = new ApiException(t);
        if (!NetworkUtil.isNetworkAvailable(ComponentKit.getInstance().getApplication())) {
            msg = "网络不可用";
            code = ErrorCode.HTTP_NET_NOT_AVAILABLE;
        }else if (t instanceof UnknownHostException) {
            msg = "请求地址不可用";
            code = ErrorCode.HTTP_UNKNOWN_HOST;
        } else if (t instanceof SocketTimeoutException) {
            msg = "请求网络超时";
            code = ErrorCode.HTTP_CONNECT_TIMEOUT;
        } else if (t instanceof ConnectException) {
            msg = "网络连接错误";
            code = ErrorCode.HTTP_CONNECT_ERROR;
        } else if (t instanceof HttpException) {
            HttpException httpException = (HttpException) t;
            msg = convertStatusCode(httpException);
            code = httpException.code();
        } else if (t instanceof JsonParseException || t instanceof ParseException || t instanceof JSONException) {
            msg = "数据解析错误";
            code = ErrorCode.JSON_PARSE_ERROR;
        } else if (t instanceof NullPointerException) {
            msg = "数据为空";
            code = ErrorCode.HTTP_RESULT_NULL;
        } else if (t instanceof SSLHandshakeException) {
            msg = "证书错误";
            code = ErrorCode.HTTPS_CERTIFICATE_ERROR;
        } else {
            msg = t != null? t.getMessage() : "未知错误";
            code = ErrorCode.UNKNOWN_ERROR;
            apiException.addSuppressed(t);
        }
        apiException.setCode(code);
        apiException.setMessage(msg);
        return apiException;
    }

    private static String convertStatusCode(HttpException httpException) {
        String msg;
        switch (httpException.code()) {
            case ErrorCode.HTTP_SERVICE_ERROR:
            case ErrorCode.HTTP_SERVICE_UNAVAILABLE:
                msg = "服务暂不可用";
                break;
            case ErrorCode.HTTP_HOST_NOT_EXIST:
                msg = "请求地址不存在";
                break;
            case ErrorCode.HTTP_SERVICE_REJECTED:
                msg = "请求被服务器拒绝";
                break;
            case ErrorCode.HTTP_REQUEST_REDIRECTED:
                msg = "请求被重定向到其他链接";
                break;
            case ErrorCode.HTTP_REQUEST_TIME_OUT:
                msg = null;
                break;
            default:
                msg = httpException.message();
                break;
        }
        return msg;
    }

    public static ApiException handleError(Throwable throwable) {
        return getApiException(throwable);
    }

}
