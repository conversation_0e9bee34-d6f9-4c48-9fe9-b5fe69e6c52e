package com.kaolafm.opensdk.api.live;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.live.model.ChatRoomMessageInfoResult;
import com.kaolafm.opensdk.api.live.model.ChatRoomTokenDetail;
import com.kaolafm.opensdk.api.goods.model.GoodsResult;
import com.kaolafm.opensdk.api.live.model.GiftGivingResult;
import com.kaolafm.opensdk.api.live.model.GiftsResult;
import com.kaolafm.opensdk.api.live.model.LiveChatRoomMemberInfoResult;
import com.kaolafm.opensdk.api.live.model.LiveInfoDetail;

import java.util.HashMap;

import io.reactivex.Single;
import okhttp3.RequestBody;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.POST;
import retrofit2.http.Query;
import retrofit2.http.QueryMap;


/**
 * <AUTHOR> Huangui
 */
public interface LiveService {

    /**
     * 直播info
     *
     * @param id
     * @return
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.REQUEST_KAOLA_LIVE_INFO)
    Single<BaseResult<LiveInfoDetail>> getLiveInfo(@Query(LiveRequest.KEY_PROGRAM_ID) String id);


    /**
     * 聊天室token
     *
     * @param tempMap
     * @return
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.REQUEST_KAOLA_CHAT_ROOM_TOKEN)
    Single<BaseResult<ChatRoomTokenDetail>> getChatRoomToken(@QueryMap HashMap<String, String> tempMap);

    /**
     * 根据开发者的唯一标识、头像、昵称获取进入直播的token。
     * @return
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @POST(KaolaApiConstant.GET_CHAT_ROOM_TOKEN_BY_ID)
    Single<BaseResult<ChatRoomTokenDetail>> getChatRoomTokenByUnique(@Body RequestBody requestBody);


    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER, ApiHostConstants.HTTPS_PROTOCOL_DOMAIN_HEADER})
    @POST(KaolaApiConstant.LIVE_SEND_MESSAGE)
    Single<BaseResult<String>> sendMessage(@Query("open_uid") String open_uid,
                                                        @Query("appId") String appId,
                                                        @Query("voiceUrl") String voiceUrl,
                                                        @Query("prgramid") long prgramid);

    /**
     * 获取礼物列表
     * @param liveId    直播id
     * @return
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.REQUEST_GIFTS)
    Single<BaseResult<GiftsResult>> getGifts(@Query("liveId") Integer liveId);

    /**
     * 送礼物
     * @param giftId    礼物id
     * @param liveId    直播id
     * @param count     礼物数量
     * @return
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @POST(KaolaApiConstant.REQUEST_GIFTS_GIVING)
    Single<BaseResult<GiftGivingResult>> givingGifts(@Query("giftId") Long giftId,
                                                     @Query("liveId") Long liveId,
                                                     @Query("count") Integer count);


    /**
     * 获取商品列表
     * @param liveId    直播id
     * @return
     */
    @Headers(ApiHostConstants.MALL_DOMAIN_HEADER)
    @GET(KaolaApiConstant.REQUEST_GOODS_LIST)
    Single<BaseResult<GoodsResult>> getGoodsList(@Query("liveId") Integer liveId);

    /**
     * 获取聊天室成员信息
     * @param liveId    直播间id
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.REQUEST_FETCH_ROOM_MEMBERS)
    Single<BaseResult<LiveChatRoomMemberInfoResult>> fetchRoomMembers(@Query("liveId") Long liveId);

    /**
     * 获取聊天室历史消息
     * @param liveId    直播间id
     * @param roomId    聊天室id
     * @param msgCount  查询历史消息最大条数, 不填默认为500条（暂定）
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.REQUEST_GET_HISTORY_MESSAGES)
    Single<BaseResult<ChatRoomMessageInfoResult>> getHistoryMessages(@Query("liveId") Long liveId,
                                                                     @Query("roomId") Long roomId,
                                                                     @Query("msgCount") Long msgCount);


}
