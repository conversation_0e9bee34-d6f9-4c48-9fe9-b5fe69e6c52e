package com.kaolafm.opensdk.demo.player;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.media.RadioRequest;
import com.kaolafm.opensdk.api.media.model.AIAudioDetails;
import com.kaolafm.opensdk.api.media.model.RadioDetails;
import com.kaolafm.opensdk.api.subscribe.SubscribeRequest;
import com.kaolafm.opensdk.demo.detail.StringAdapter.Item;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.playlist.util.PlayListUtils;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.ArrayList;
import java.util.List;

/**
 * AI电台播放器页面。
 * <AUTHOR> Yan
 * @date 2018/12/6
 */

public class RadioPlayerActivity extends BasePlayerActivity {

    private boolean isLoadMore = false;
    /**
     * 是否有下一页。
     */
    private boolean hasNextPage = true;

    private String mClockId = "0";

    @Override
    public void initView(Bundle savedInstanceState) {
        super.initView(savedInstanceState);
        setTitle("AI电台播放器页面");
        //添加播放状态监听
        PlayerManager.getInstance().addPlayControlStateCallback(mPlayerStateListener);
        PlayerManager.getInstance().addPlayListControlStateCallback(mPlayListStateListener);
    }

    @Override
    public void initData() {
        getRadioDetails();
//        getPlaylistRadio(false);
        PlayerManager.getInstance().start(new PlayerBuilder().setId(String.valueOf(mId)).setType(PlayerConstants.RESOURCES_TYPE_RADIO));
        getSubscribeState();
    }

    /**
     * 获取该电台订阅状态
     */
    private void getSubscribeState() {
        new SubscribeRequest().isSubscribed(mId, new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                isSubscribed = aBoolean;
                btnSubscribe.setText(aBoolean ? "取消订阅" : "订阅");
                btnSubscribe.setEnabled(true);
                btnSubscribe.setVisibility(View.VISIBLE);
            }

            @Override
            public void onError(ApiException exception) {
                btnSubscribe.setText(exception.getMessage());
            }
        });
    }

    /**
     * 获取播单列表。
     * 如果PlayItem里面的数据已经满足页面暂时需求，
     * 能保证一致性，就不需要再手动将数据添加到播单中了，拉取更多播单{@link }
     *
     * @param isLoadMore true表示是加载更多
     */
    private void getPlaylistRadio(boolean isLoadMore) {
        play2(isLoadMore);
    }

    private void play2(boolean isLoadMore) {
        if (isLoadMore && !hasNextPage) {
            showToast(isLoadMore ? "没有更多了" : "列表为空");
            return;
        }
        new RadioRequest().getPlaylist(mId, mClockId, new HttpCallback<List<AIAudioDetails>>() {
            @Override
            public void onSuccess(List<AIAudioDetails> audioDetails) {
                showRadioSucess(isLoadMore, audioDetails);
            }

            @Override
            public void onError(ApiException exception) {
                showRadioError(exception);
            }
        });
    }

    private void showRadioError(ApiException exception) {
        if (mTrfDetailPlaylist != null) {
            mTrfDetailPlaylist.finishLoadmore();
        }
        showToast("获取播单错误，错误码="+exception.getCode()+", 错误信息="+exception.getMessage());
    }

    private void showRadioSucess(boolean isLoadMore, List<AIAudioDetails> audioDetails) {
        if (!ListUtil.isEmpty(audioDetails)) {
            mClockId = audioDetails.get(0).getClockId();
            Log.e("RadioPlayerActivity", "onSuccess: mClockId="+mClockId);
            //等于1表示还有更多，等于0表示没有了
            hasNextPage = audioDetails.get(0).getHasNextPage() == 1;
            List<PlayItem> playItemList = PlayListUtils.audioDetailToRadioPlayItem(audioDetails, PlayerManager.getInstance().getPlayListInfo());
            showListinfo(isLoadMore, playItemList);
        } else {
            showToast(isLoadMore ? "没有更多了" : "列表为空");
        }
    }

    void showListinfo(boolean isLoadMore, List<PlayItem> playItemList){
        List<Item> datas = new ArrayList<>();
        for (int i = 0; i < playItemList.size(); i++) {
            PlayItem item = playItemList.get(i);
            Item sai = new Item();
            sai.id = item.getAudioId();
            sai.type = ResType.TYPE_RADIO;
            sai.title = item.getTitle();
            sai.details = item.getAlbumTitle();
            sai.item = item;
            sai.playItem = item;
            datas.add(sai);
        }
        if (isLoadMore) {
            if (mAdapter != null) {
                mAdapter.addDataList(datas);
            }
            if (mTrfDetailPlaylist != null) {
                mTrfDetailPlaylist.finishLoadmore();
            }
        } else {
            if (mAdapter != null) {
                mAdapter.setDataList(datas);
            }
        }
    }

    /**
     * 获取电台详情
     */
    private void getRadioDetails() {
        new RadioRequest().getRadioDetails(mId, new HttpCallback<RadioDetails>() {
            @Override
            public void onSuccess(RadioDetails radioDetails) {
                showDetail(radioDetails, radioDetails.getImg());
            }

            @Override
            public void onError(ApiException exception) {
                showDetail(exception, "");
            }
        });
    }

    @Override
    protected void playPre() {
        PlayerManager.getInstance().playPre();
    }

    @Override
    protected void switchPlayPause() {
        PlayerManager.getInstance().switchPlayerStatus();
    }

    @Override
    protected void playNext() {
        PlayerManager.getInstance().playNext();
    }

    @Override
    protected void refresh() {

    }

    @Override
    protected void loadMore() {
//        getPlaylistRadio(true);
        PlayerManager.getInstance().loadNextPage(new IPlayListGetListener() {
            @Override
            public void onDataGet(PlayItem playItem, List<PlayItem> playItemList) {
                showListinfo(true, playItemList);
            }

            @Override
            public void onDataGetError(PlayItem playItem, int i, int i1) {

            }
        });
    }

    @Override
    protected void playItem(Item item) {
        PlayerManager.getInstance().startPlayItemInList(item.playItem,null);
        //根据ID从播单中获取PlayItem。由于前面获取播单是自己手动请求网络获取的，所以有可能为空，可能出现和播放器中的播单不一致
//        PlayerManager.getInstance().getPlayItemFromAudioId(item.id, new PlayerManager.GetPlayItemListener() {
//            @Override
//            public void success(PlayItem playitem) {
//                if (playitem != null) {
//                    mPlayerManager.start(new PlayerBuilder().setId(String.valueOf(playitem.getRadioId())).setType(PlayerConstants.RESOURCES_TYPE_RADIO));
//                }//如果播放器里面的播单没有该数据就直播播放单个单曲。注意：调用该方法是播放单曲所在的专辑，不是该电台。
//                else {
//                    mPlayerManager.start(new PlayerBuilder().setId(String.valueOf(item.id)).setType(PlayerConstants.RESOURCES_TYPE_RADIO));
//                }
//            }
//
//            @Override
//            public void error(ApiException exception) {
//                showToast("播放失败");
//            }
//        });
    }

    @Override
    protected void seek(int progress) {
        PlayerManager.getInstance().seek(progress);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        PlayerManager.getInstance().removePlayControlStateCallback(mPlayerStateListener);
        PlayerManager.getInstance().removePlayListControlStateCallback(mPlayListStateListener);
    }

    private IPlayListStateListener mPlayListStateListener = new IPlayListStateListener() {
        @Override
        public void onPlayListChange(List<PlayItem> list) {
            showToast("电台播单发生变化");
            showListinfo(isLoadMore, list);
            isLoadMore = true;
        }

        @Override
        public void onPlayListChangeError(PlayItem playItem, int i, int i1) {

        }

    };
}
