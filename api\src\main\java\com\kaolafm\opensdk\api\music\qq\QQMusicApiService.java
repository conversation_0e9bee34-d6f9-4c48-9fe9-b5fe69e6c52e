package com.kaolafm.opensdk.api.music.qq;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.music.qq.model.AlbumResult;
import com.kaolafm.opensdk.api.music.qq.model.BaseMusicResult;
import com.kaolafm.opensdk.api.music.qq.model.CategoryLabel;
import com.kaolafm.opensdk.api.music.qq.model.MusicRadioCategory;
import com.kaolafm.opensdk.api.music.qq.model.MusicSearchResult;
import com.kaolafm.opensdk.api.music.qq.model.Singer;
import com.kaolafm.opensdk.api.music.qq.model.SingerSongs;
import com.kaolafm.opensdk.api.music.qq.model.Song;
import com.kaolafm.opensdk.api.music.qq.model.SongCharts;
import com.kaolafm.opensdk.api.music.qq.model.SongChartsResult;
import com.kaolafm.opensdk.api.music.qq.model.SongMenu;
import com.kaolafm.opensdk.api.music.qq.model.SubcategoryLabel;
import com.kaolafm.opensdk.api.music.qq.model.TencentLoginResult;
import com.kaolafm.opensdk.api.music.qq.model.VipInfo;
import io.reactivex.Single;
import java.util.List;
import java.util.Map;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Query;
import retrofit2.http.QueryMap;

/**
 * QQ音乐api接口
 *
 * <AUTHOR>
 * @date 2018/4/18
 */
 interface QQMusicApiService {

    //====================================随便听听电台=====================================
    //====================================歌词============================================

    /**
     * 根据歌曲的songId获取歌词
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_LYRICS)
    Single<BaseMusicResult<String>> getLyrics(@Query("song_id") long songId);
    //====================================登录============================================

    /**
     * OPI业务方获取音乐微信二维码url。
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_WECHAT_QR_CODE_FOR_LOGIN)
    Single<TencentLoginResult> getWeChatQRCodeForLogin();

    /**
     * OPI业务方通过微信用户授权code，登录音乐，并换取、刷新音乐相关凭证接口。
     * 其中返回参数中music_id，music_key用于音乐接口访问的凭证，有具有有效期。
     * 功能一：通过微信授权码code，登录音乐，并拿到相应凭证
     * 功能二：刷新音乐帐号musicid的登录key
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_QQMUSIC_DOCUMENTS_BY_WECHAT)
    Single<TencentLoginResult> getQQMusicDocumentsByWeChat(@QueryMap Map<String, String> params);

    /**
     * 同步方式请求
     * OPI业务方通过微信用户授权code，登录音乐，并换取、刷新音乐相关凭证接口。
     * 其中返回参数中music_id，music_key用于音乐接口访问的凭证，有具有有效期。
     * 功能一：通过微信授权码code，登录音乐，并拿到相应凭证
     * 功能二：刷新音乐帐号musicid的登录key
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_QQMUSIC_DOCUMENTS_BY_WECHAT)
    Call<TencentLoginResult> getQQMusicDocumentsByWeChatSynch(@QueryMap Map<String, String> params);

    /**
     * OPI业务方申请业务绑定得到bind_music_third_appname，
     * 然后通过微信用户登录第三方的openid，access_token，
     * 登录音乐，并换取、刷新音乐相关凭证接口。
     * 其中返回参数中music_id，music_key用于音乐接口访问的凭证，具有有效期。
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.WECHAT_LOGIN_WITH_THIRD_PARTY)
    Single<TencentLoginResult> loginQQMusicByThirdPartyBindWecaht(@QueryMap Map<String, String> params);

    /**
     * OPI业务方获取音乐QQ二维码url， 登录，获取用户信息，刷新票据等功能。
     * cmd
     * 操作类型：
     * 0：获取二维码
     * 1：用户登录
     * 2：刷新票据
     * 3：获取用户信息
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.QQ_LOGIN_FUNCTIONS)
    Single<TencentLoginResult> qqLoginFunctions(@QueryMap Map<String, String> params);

    /**
     * OPI业务方获取音乐QQ二维码url， 登录，获取用户信息，刷新票据等功能。同步的方式
     * cmd
     * 操作类型：
     * 0：获取二维码
     * 1：用户登录
     * 2：刷新票据
     * 3：获取用户信息
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.QQ_LOGIN_FUNCTIONS)
    Call<TencentLoginResult> qqLoginFunctionsSynch(@QueryMap Map<String, String> params);

    //====================================歌曲============================================

    /**
     * 通过歌曲mid，获取歌曲的信息。一次访问，最多拉取50首歌曲。
     * 可批量获取，多mid有逗号隔开
     *
     * @param songMids 歌曲mid,多mid用逗号隔开
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_SONG_LIST_BATCH)
    Single<BaseMusicResult<List<Song>>> getSongListBatch(@Query("song_mid") String songMids);

    /**
     * 通过歌曲mid，获取歌曲的信息。一次访问，最多拉取50首歌曲。
     * 可批量获取，多mid有逗号隔开
     * 同步方法
     * @param songMids 歌曲mid,多mid用逗号隔开
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_SONG_LIST_BATCH)
    Call<BaseMusicResult<List<Song>>> getSongListBatchSynch(@Query("song_mid") String songMids);

    //====================================搜索============================================

    /**
     * 根据关键词搜索歌曲
     *
     * @param keyword   搜索词
     * @param pageIndex 搜索第几页，默认取值1
     * @param pageSize  每页个数，默认取值10，不得超过30
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.SEARCH_SONG_BY_KEYWORD)
    Single<MusicSearchResult> searchSongByKeyword(@Query("w") String keyword, @Query("p") int pageIndex,
            @Query("num") int pageSize);

    //====================================歌单============================================

    /***
     * 获取歌单广场歌单列表
     * @param startIndex 开始索引
     * @param size 拉取歌单数量
     * @param order 拉取方式，2：最新 5：最热
     * @return
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_SONGMENU_LIST_OF_SQUARE)
    Single<BaseMusicResult<List<SongMenu>>> getSongMenuListOfSquare(@Query("start") int startIndex,
            @Query("size") int size,
            @Query("order") int order);

    /**
     * 歌单广场的歌单进行收藏、取消收藏操作，同时可获取用户收藏歌单。
     *
     * @param cmd    操作类型，1：收藏，2取消收藏，3 获取收藏歌单
     * @param dissId 要收藏/取消收藏的歌单id
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.OPERATE_SONGMENU_OF_SQUATE)
    Single<BaseMusicResult<List<SongMenu>>> opareteSongMenuOfSquare(@Query("cmd") int cmd,
            @Query("dissid") String dissId);

    /**
     * 获取个人歌单列表
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_SELF_SONGMENU_LIST)
    Single<BaseMusicResult<List<SongMenu>>> getSelfSongMenuList();

    /**
     * 歌单中增加/删除歌曲
     *
     * @param cmd     操作类型，1：增加，2删除
     * @param dissId  要操作的歌单id
     * @param songIds 歌曲id，多个歌曲id用逗号分隔
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.OPERATE_SONG_OF_SONGMENU)
    Single<BaseMusicResult> opareteSongOfSongMenu(@Query("cmd") int cmd, @Query("dissid") long dissId,
            @Query("song_ids") String songIds);

    /**
     * 获取歌单中歌曲列表。（由于歌单歌曲数不可控，目前只返回简要索引信息，播放再请求获取批量歌曲信息接口{@link #getSongListBatch(String)} ()}
     *
     * @param dissId 要操作的歌单id
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_SONG_LIST_OF_SONGMENU)
    Single<BaseMusicResult<List<Song>>> getSongListOfSongMenu(@Query("dissid") long dissId);

    //====================================公共电台=========================================

    /***
     * 获取公共电台列表
     * @return
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_PUBLIC_RADIO_LIST)
    Single<BaseMusicResult<List<MusicRadioCategory>>> getPublicRadioList();

    /**
     * 根据电台id获取电台歌曲列表
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_SONG_LIST_OF_RADIO)
    Single<BaseMusicResult<List<Song>>> getSongListOfRadio(@Query("radio_id") long radioId);

    //====================================个性化推荐========================================

    /**
     * 获取每日推荐30首
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_DAY_RECOMMEND_SONGS)
    Single<BaseMusicResult<List<Song>>> getDayRecommendSongs();

    /**
     * 获取音乐后台每日推荐的歌单。
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_DAY_RECOMMEND_SONGMENUS)
    Single<BaseMusicResult<List<SongMenu>>> getDayRecommendSongMenu();

    /**
     * 依据传入的qq号，获取个性化算法所推荐的歌曲
     *
     * @param operType 参数<br/>
     *                 <p>
     *                 oper_type 操作类型<br/>
     *                 获取操作<br/>
     *                 get_recommand_song:获取歌曲<br/>
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_INDIVIDUAL_SONGS_OF_RADIO)
    Single<BaseMusicResult<List<Song>>> getIndividualSongsOfRadio(@Query("oper_type") String operType);

    /**
     * 推荐上报操作 歌曲收藏、歌曲删除、播放下一首、一直听到结束等反馈功能，用于动态调整推荐数据。
     *
     * @param params 参数集合 <br/>
     *               上报操作：<br/>
     *               report_collect_song：上报收藏（我喜欢）<br/>
     *               report_delete_song：上报删除（不喜欢）<br/>
     *               report_play_next_song：上报播放下一首<br/>
     *               report_listen_to_end：上报完整收听<br/>
     *               <p>
     *               repo_listen_time<br/>
     *               当上报操作时带上此参数：表示上报时当前歌听的播放时长，单位s
     *               repo_song_id<br/>
     *               当上报时带上此参数：表示上报的歌曲id
     *               repo_pingpong<br/>
     *               当上报时带上此参数：表示获取到歌曲的推荐码（代表推荐原因。oper_type =get_recommand_song，获得歌曲时，每首歌下面有返回码pingpong）
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_INDIVIDUAL_SONGS_OF_RADIO)
    Single<BaseMusicResult> reportOperateForRecommand(@QueryMap Map<String, Object> params);

    //====================================排行榜===========================================

    /**
     * 获取qq音乐的排行榜的歌单的歌曲列表。
     *
     * @param chartsId 榜单id
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_SONG_LIST_OF_SONG_CHARTS)
    Single<SongChartsResult> getSongListOfSongCharts(@Query("top_id") long chartsId);

    /**
     * 获取榜单列表
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_SONG_CHARTS_LIST)
    Single<BaseMusicResult<List<SongCharts>>> getSongChartsList();

    //====================================专辑============================================

    /**
     * 通过专辑id，获取专辑内的歌曲信息。同时获取多张专辑，最多5张
     *
     * @param albumIds 专辑id，多张专辑之间用逗号分割，最多一次查询支持5张
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_SONG_LIST_OF_ALBUM)
    Single<BaseMusicResult<List<Song>>> getSongListOfAlbum(@Query("album_id") String albumIds);

    //====================================歌手============================================

    /**
     * 通过歌手id，获取歌手下的歌曲信息，可分页拉取。
     *
     * @param params 参数集合<br/>
     *               singer_id 歌手id ，只支持单次拉取<br/>
     *               page_index 分页索引，从0开始<br/>
     *               num_per_page 每页歌曲数目，最大为50，需大于0<br/>
     *               order 歌曲排序方式，0：表示按时间，1：表示按热度<br/>
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_SONG_LIST_OF_SINGER)
    Single<SingerSongs> getSongListOfSinger(@QueryMap Map<String, Object> params);

    /**
     * 获取热门歌手列表。按照分类索引（支持地区，性别，流派），拉取相应分类下的热门歌手id列表。（注意：目前至多返回200个，部分分类少于200个）
     *
     * @param area  歌手所在地区索引：<br/>
     *              -100：全部
     *              200 ：内地
     *              2：港台
     *              3：韩国
     *              4：日本
     *              5：欧美
     *              6：其它
     * @param type  歌手性别索引：<br/>
     *              -100：全部
     *              0：男
     *              1：女
     *              2：组合
     * @param genre 歌手所属流派索引：<br/>
     *              -100：全部
     *              1：流行
     *              2：摇滚
     *              3：民谣
     *              4：电子
     *              5：爵士
     *              6：嘻哈
     *              7：金属
     *              8：R&B
     *              9：轻音乐
     *              10：民歌
     *              14：古典
     *              20：蓝调
     *              25：乡村
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_SINGER_LIST)
    Single<BaseMusicResult<List<Singer>>> getSingerList(@Query("area") int area, @Query("type") int type,
            @Query("genre") int genre);

    /**
     * 通过歌手id，获取歌手下的专辑信息，可分页拉取。
     *
     * @param params 参数集合<br/>
     *               singer_id 歌手id ，只支持单次拉取<br/>
     *               page_index 分页索引，从0开始<br/>
     *               num_per_page 每页歌曲数目，最大为50，需大于0<br/>
     *               order 歌曲排序方式，0：表示按时间，1：表示按热度<br/>
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_ALBUM_LIST_OF_SINGER)
    Single<AlbumResult> getAlbumListOfSinger(@QueryMap Map<String, Object> params);

    //====================================分类============================================

    /**
     * 提供拉取音乐馆分类标签，分类子标签，分类歌曲，分类专辑等四功能
     *
     * @param params oper_type 操作类型，取值如下：<br/>
     *               get_category_group：获取主分类标签<br/>
     *               get_category_detail：获取标签详情，同时若该标签下有子标签，返回子标签信息<br/>
     *               get_category_song：获取分类标签下的歌曲信息<br/>
     *               get_category_album：获取分类标签下的专辑信息<br/>
     *               order 排序方式，取值如下：<br/>
     *               0：最新
     *               1：最热<br/>
     *               category_id 分类标签id<br/>
     *               pagenum 页索引<br/>
     *               pagesize 每页信息条目数<br/>
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_CATEGORY_LIBRARY)
    Single<BaseMusicResult> getCategoryLibrary(@QueryMap Map<String, Object> params);

    /**
     * 获取主标签
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_CATEGORY_LIBRARY)
    Single<BaseMusicResult<List<CategoryLabel>>> getMainCategoryLabels(@Query("oper_type") String operType);

    /**
     * 获取标签详情，同时若该标签下有子标签，返回子标签信息
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_CATEGORY_LIBRARY)
    Single<BaseMusicResult<SubcategoryLabel>> getCategoryDetail(@Query("oper_type") String operType,
            @Query("category_id") Long categoryId);

    /**
     * 获取分类标签下歌曲
     * 每次请求随机返回该分类下的200首歌曲。
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_CATEGORY_LIBRARY)
    Single<BaseMusicResult<List<Song>>> getSongListOfCategoryLabel(@Query("oper_type") String operType,
            @Query("category_id") Long categoryId);

    /**
     * 获取分类标签下的专辑信息
     * 分页返回专辑列表。
     */
    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_CATEGORY_LIBRARY)
    Single<BaseMusicResult<List<AlbumResult>>> getAlbumListOfCategoryLabel(@QueryMap Map<String, Object> params);

    //====================================用户信息=========================================

    @Headers(ApiHostConstants.QQMUSIC_DOMAIN_HEADER)
    @GET(QQMusicApiConstant.GET_USER_VIP_INFO)
    Single<BaseMusicResult<VipInfo>> getUserVipInfo();

}
