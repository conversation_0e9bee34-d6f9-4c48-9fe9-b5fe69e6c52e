package com.kaolafm.opensdk.api.topic.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

/**
 * 话题帖子
 */
public class TopicPosts implements Parcelable {
    /**
     * 未点赞
     */
    public static final int STATUS_UNLIKE = 0;
    /**
     * 已点赞
     */
    public static final int STATUS_LIKED = 1;

    /**
     * 帖子id
     */
    @SerializedName("postId")
    private long postId;
    /**
     * 用户头像
     */
    @SerializedName("avatar")
    private String avatar;
    /**
     * 用户id
     */
    @SerializedName("openUid")
    private String openUid;
    /**
     * 用户昵称
     */
    @SerializedName("nickname")
    private String nickname;
    /**
     * 发布时间
     */
    @SerializedName("publishTime")
    private long publishTime;
    /**
     * 文本内容
     */
    @SerializedName("content")
    private String content;
    /**
     * 媒体信息
     */
    @SerializedName("mediaInfo")
    private String mediaInfo;
    /**
     * 点赞数
     */
    @SerializedName("likeCount")
    private long likeCount;
    /**
     * 点赞状态
     * 0-未点赞
     * 1-已点赞
     */
    @SerializedName("likeStatus")
    private int likeStatus;


    public long getPostId() {
        return postId;
    }

    public void setPostId(long postId) {
        this.postId = postId;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getOpenUid() {
        return openUid;
    }

    public void setOpenUid(String openUid) {
        this.openUid = openUid;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public long getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(long publishTime) {
        this.publishTime = publishTime;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getMediaInfo() {
        return mediaInfo;
    }

    public void setMediaInfo(String mediaInfo) {
        this.mediaInfo = mediaInfo;
    }

    public long getLikeCount() {
        return likeCount;
    }

    public void setLikeCount(long likeCount) {
        this.likeCount = likeCount;
    }

    public int getLikeStatus() {
        return likeStatus;
    }

    public void setLikeStatus(int likeStatus) {
        this.likeStatus = likeStatus;
    }

    protected TopicPosts(Parcel in) {
        postId = in.readLong();
        avatar = in.readString();
        openUid = in.readString();
        nickname = in.readString();
        publishTime = in.readLong();
        content = in.readString();
        mediaInfo = in.readString();
        likeCount = in.readLong();
        likeStatus = in.readInt();
    }

    public static final Creator<TopicPosts> CREATOR = new Creator<TopicPosts>() {
        @Override
        public TopicPosts createFromParcel(Parcel in) {
            return new TopicPosts(in);
        }

        @Override
        public TopicPosts[] newArray(int size) {
            return new TopicPosts[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(postId);
        dest.writeString(avatar);
        dest.writeString(openUid);
        dest.writeString(nickname);
        dest.writeLong(publishTime);
        dest.writeString(content);
        dest.writeString(mediaInfo);
        dest.writeLong(likeCount);
        dest.writeInt(likeStatus);
    }

}
