package com.kaolafm.opensdk.api.login.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

/**
 * 用户财富
 */
public class UserWealth implements Parcelable {

    /** 云币余额 */
    @SerializedName("balance")
    private Long balance;

    public Long getBalance() {
        return balance;
    }

    public void setBalance(Long balance) {
        this.balance = balance;
    }

    @Override
    public String toString() {
        return "UserWealth{" +
                ", balance=" + balance +
                '}';
    }

    protected UserWealth(Parcel in) {
        balance = in.readLong();
    }

    public static final Creator<UserWealth> CREATOR = new Creator<UserWealth>() {
        @Override
        public UserWealth createFromParcel(Parcel in) {
            return new UserWealth(in);
        }

        @Override
        public UserWealth[] newArray(int size) {
            return new UserWealth[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(balance);
    }
}
