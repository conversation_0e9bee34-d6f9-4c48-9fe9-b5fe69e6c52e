package com.kaolafm.ad.api.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.HashMap;
import java.util.Map;

/**
 * 通用广告bean类
 *
 * <AUTHOR> Yan
 * @date 2020-01-10
 */
public class Advert implements Parcelable {

    /**
     * 广告的唯一id。现在是创意id {@link com.kaolafm.ad.api.model.AdvertisingDetails#creativeId}
     */
    private long id;

    /**
     * 每次请求广告的唯一id。
     */
    private String sessionId;

    /**
     * 广告的类型，该类型是用于区分图片广告、音频广告、音图广告。一般使用，因为根据子类的类型就可以区分广告类型
     * 3音频广告；4图片广告；5音图广告
     */
    private int type;

    /**
     * 次级类型，区别于{@link #type}类型，由调用方传入。
     */
    private int subtype;

    /**
     * 广告资源url。图片、音频、二次互动icon等的url
     */
    private String url;

    /**
     * 总广告时长。单位秒
     */
    private long duration;

    /**曝光时长。主图时长、音频时长、获取二次交互icon显示时长*/
    private int exposeDuration;

    /**
     * 是否可以跳过。false为不跳过 true为跳过
     */
    private boolean jump;

    /**
     * 可跳过时间。单位秒
     */
    private int jumpSeconds;

    /**
     * 额外信息。目前只有有监控地址，可以继续追加。
     */
    private Map<String, Object> extra;

    /**
     * 本地缓存的路径
     */
    private String localPath;

    public Advert() {
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public long getDuration() {
        return duration;
    }

    public void setDuration(long duration) {
        this.duration = duration;
    }

    public int getExposeDuration() {
        return exposeDuration;
    }

    public void setExposeDuration(int exposeDuration) {
        this.exposeDuration = exposeDuration;
    }

    public boolean isJump() {
        return jump;
    }

    public void setJump(boolean jump) {
        this.jump = jump;
    }

    public int getJumpSeconds() {
        return jumpSeconds;
    }

    public void setJumpSeconds(int jumpSeconds) {
        this.jumpSeconds = jumpSeconds;
    }

    public int getSubtype() {
        return subtype;
    }

    public void setSubtype(int subtype) {
        this.subtype = subtype;
    }

    public Map<String, Object> getExtra() {
        return extra;
    }

    public void setExtra(Map<String, Object> extra) {
        this.extra = extra;
    }

    public String getLocalPath() {
        return localPath;
    }

    public void setLocalPath(String localPath) {
        this.localPath = localPath;
    }

    /**
     * 添加额外数据
     *
     * @param key
     * @param value
     */
    public void putExtra(String key, Object value) {
        if (extra == null) {
            extra = new HashMap<>();
        }
        extra.put(key, value);
    }

    /**
     * 获取额外数据的值
     *
     * @param key
     * @return
     */
    public <T> T getExtraValue(String key) {
        if (extra != null) {
            return (T) extra.get(key);
        }
        return null;
    }

    protected Advert(Parcel in) {
        this.id = in.readLong();
        this.sessionId = in.readString();
        this.type = in.readInt();
        this.subtype = in.readInt();
        this.url = in.readString();
        this.duration = in.readLong();
        this.exposeDuration = in.readInt();
        this.jump = in.readByte() == 1;
        this.jumpSeconds = in.readInt();
        this.extra = in.readHashMap(getClass().getClassLoader());
        this.localPath = in.readString();
    }

    public static final Creator<Advert> CREATOR = new Creator<Advert>() {
        @Override
        public Advert createFromParcel(Parcel in) {
            return new Advert(in);
        }

        @Override
        public Advert[] newArray(int size) {
            return new Advert[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(id);
        dest.writeString(sessionId);
        dest.writeInt(type);
        dest.writeInt(subtype);
        dest.writeString(url);
        dest.writeLong(duration);
        dest.writeInt(exposeDuration);
        dest.writeByte((byte) (jump ? 1 : 0));
        dest.writeInt(jumpSeconds);
        dest.writeMap(extra);
        dest.writeString(localPath);
    }
}
