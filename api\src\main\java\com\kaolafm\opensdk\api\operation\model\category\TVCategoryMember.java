package com.kaolafm.opensdk.api.operation.model.category;

/**
 * 人工运营分类成员：听电视
 */
public class TVCategoryMember extends CategoryMember {

    /** 听电视资源id*/
    private long listenTVid;

    /** 听电视的播放次数*/
    private int playTimes;

    private String freq;

    public long getListenTVid() {
        return listenTVid;
    }

    public void setListenTVid(long listenTVid) {
        this.listenTVid = listenTVid;
    }

    public int getPlayTimes() {
        return playTimes;
    }

    public void setPlayTimes(int playTimes) {
        this.playTimes = playTimes;
    }

    public String getFreq() {
        return freq;
    }

    public void setFreq(String freq) {
        this.freq = freq;
    }

    @Override
    public String toString() {
        return "TVCategoryMember{" +
                "listenTVid=" + listenTVid +
                ", playTimes=" + playTimes +
                ", freq='" + freq + '\'' +
                '}';
    }
}
