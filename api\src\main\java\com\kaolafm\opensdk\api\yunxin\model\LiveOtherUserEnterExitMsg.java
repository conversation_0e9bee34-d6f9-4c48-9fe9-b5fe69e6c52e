package com.kaolafm.opensdk.api.yunxin.model;

import android.os.Parcel;
import android.os.Parcelable;


/**
 * 云信自定义消息-其他用户进入退出聊天室通知-subType : 1-进入聊天室通知。2-离开聊天室通知
 *
 * <AUTHOR>
 * @date 2023-02-16
 */
public class LiveOtherUserEnterExitMsg implements Parcelable {

    // 1-进入聊天室通知。2-离开聊天室通知
    private Integer subType;

    // 设备号
    private String deviceId;

    // 账号
    private String account;

    // 头像
    private String avatar;

    // 昵称
    private String nickName;

    // 身份:0-游客，1-账号用户
    private Integer userType;

    public Integer getSubType() {
        return subType;
    }

    public void setSubType(Integer subType) {
        this.subType = subType;
    }

    public String getDeviceId() {
        return deviceId == null ? "" : deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getAccount() {
        return account == null ? "" : account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getAvatar() {
        return avatar == null ? "" : avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getNickName() {
        return nickName == null ? "" : nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeValue(this.subType);
        dest.writeString(this.deviceId);
        dest.writeString(this.account);
        dest.writeString(this.avatar);
        dest.writeString(this.nickName);
        dest.writeValue(this.userType);
    }

    public LiveOtherUserEnterExitMsg() {
    }

    protected LiveOtherUserEnterExitMsg(Parcel in) {
        this.subType = (Integer) in.readValue(Integer.class.getClassLoader());
        this.deviceId = in.readString();
        this.account = in.readString();
        this.avatar = in.readString();
        this.nickName = in.readString();
        this.userType = (Integer) in.readValue(Integer.class.getClassLoader());
    }

    public static final Creator<LiveOtherUserEnterExitMsg> CREATOR = new Creator<LiveOtherUserEnterExitMsg>() {
        @Override
        public LiveOtherUserEnterExitMsg createFromParcel(Parcel source) {
            return new LiveOtherUserEnterExitMsg(source);
        }

        @Override
        public LiveOtherUserEnterExitMsg[] newArray(int size) {
            return new LiveOtherUserEnterExitMsg[size];
        }
    };

    @Override
    public String toString() {
        return "LiveOtherUserEnterExitMsg{" +
                "subType=" + subType +
                ", deviceId='" + deviceId + '\'' +
                ", account='" + account + '\'' +
                ", avatar='" + avatar + '\'' +
                ", nickName='" + nickName + '\'' +
                ", userType=" + userType +
                '}';
    }
}
