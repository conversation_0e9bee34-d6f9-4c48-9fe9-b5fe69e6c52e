{"config": {"emas.appKey": "*********", "emas.appSecret": "e88e1aa048214275992f8d01d9ea5ffd", "emas.packageName": "com.edog.car", "hotfix.idSecret": "*********-1", "hotfix.rsaSecret": "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCWnJFUILAxpANzdNxqyjdg7Q9IrVJIFyQrQvfs5w5yoW3tPanepUEtfuqJc10MLAkbEYrooogcu88Rvi+DXDNMfiiTG3jV9eApSnZs3ezdLpS08BwnpUpBBovAn55Di3Ei4lPLuTAxW7+TK0SyjnqyKhsqN2l8M/wfOwJ32twyiOYPjeZa1Y8yxMykLRSCStOiPqXDG+hGfNcegVI5/eU10VF6JQny31IZETcOc3iOc7nxrlT3dLy9J9fkrXfiSGgcT354Jxe2r54Zk1DwOQZHNW+0MAUtZCaU27uJH6520DofmUv50E4InHMMb9Jyopr1pJiVeltXOS1Py8zAUMMBAgMBAAECggEAPnEtI4bl5b7NG3oQxEmQSiQemRAas/68JR9/sH1fFRFuhNRy7/btndInpNLqedhr8ggE4kw4SElpIT8Lbde1APqxEcRrBbIJvLS9godD47OjPZzq31j8/xaarELBF9nhDwhsm2Ls4xnZxEdFFQ5TOjiQyKDun+rhlYUekdhwQJa0Cpv0etTZVv5TCVAH0vC2yykcOOoibQ/ZhE/LcMvr0BzpTARnutmTgwvpu+cjP3/aT3Yy3juj/CLrNeaiTr2FYnPgKcXzuzD7cEgcZjVdiAQMqb4DFzlnktORWsqWLy0DEzctQirSkfyxgnNTFzpEBQgTZNqV5WCECw89FfSiEQKBgQDUzonbdP+sNlq2ZMbyZt9Dr+0kDSFgBgt8soNSRAbLGqsg1tx68bv2AVER95dlxzXGwLY+AJMaMwkDCpyKvs+d9gRzFMNspOqPSxWyI/+l8Ks/lqegx+02MpYKynnFrUfP+ydo9fCs2Lqymh5mS7WRVnOVSEx2B140hLHO29UL/QKBgQC1LlxS95yBmB9Vf+s73dpdg0YaSZPNk1e619c41a7GQGo/0Sk5W5gS3rnL8R36POaOF6EJ2sV2LwprqLTrnj1YpTFruFqn/x4FpWgMIBvU/GLuJ71ajsi0EDKP+1DPz/AXAI4I4M4IPDP9c5A0TcTJCFQa6ceFTdXQbMbyUhloVQKBgQCoKX3umYngQCN2tjQwIPKUvlSahHW3N4+kPjxfqbnkjXJlROR9ksCBKZEyrYBJwR3RrT9Vx4Z20ZVxfdUuHpJZtKrXdbToCXoBOcgvORsvufrZCOf1uYbNqTO/2zhW6RMyWrlNy+sf6zO9JuSW2YTlEHfNo5AoRLJAtgr79+Nm0QKBgCsYxQNDPOoiqklnLShHZFrcD//Owqaixrp9/wbY5ULpLZzSfvDS1cCfVCiryQquE9V2z2qNrGypUdw7/BUTxdnhKXlLF91gNN748l/3/0bRZiLqNwNkdV9Hfw96o0VbW+vMyPxZpQiWcXN3WrTmTsnW/lKMi1YtJo/2X/hiCQMlAoGAF0BCBGLUtQxOOMw5VR/fAIc+XDeM91v6TBBKetjcVFr3YpyeX/Dfe+Sc2PRXcsIsy7IfZnv+QvyfKpWNyxjbWNdeI8Gf6cSriAHlwta6IH+q5KR9Bgoikl86Su+HTKd/QFWnvBgopVLDIvxHMh/oAgGVOjaq90EDMnpB0IbSbNI=", "httpdns.accountId": "175421", "httpdns.secretKey": "d19c391296790ff03403232ebffd6ab2", "appmonitor.tlog.rsaSecret": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC06//xXP8EQ3ar7I+RtVFb6XTDsutEyHLZvRWeq4AXv3gJUOAKfk7c8089KndiY9ha6ceKbNvbtpF62W0KEfHAeLI1jvFtzD1Aa5hwnGCP/EaPNvMYBKq0TRYr3gdSXDbHqVKLfWuBvlYWQfFsUiCIi/EkSuC+srGUaXvgbIYPIwIDAQAB", "appmonitor.rsaSecret": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC06//xXP8EQ3ar7I+RtVFb6XTDsutEyHLZvRWeq4AXv3gJUOAKfk7c8089KndiY9ha6ceKbNvbtpF62W0KEfHAeLI1jvFtzD1Aa5hwnGCP/EaPNvMYBKq0TRYr3gdSXDbHqVKLfWuBvlYWQfFsUiCIi/EkSuC+srGUaXvgbIYPIwIDAQAB"}, "services": {"hotfix_service": {"status": 0, "version": "3.4.1"}, "ha-adapter_service": {"status": 1, "version": "1.2.5.0-open"}, "feedback_service": {"status": 0, "version": "3.4.3"}, "httpdns_service": {"status": 0, "version": "2.4.3"}, "tlog_service": {"status": 0, "version": "1.1.8.0-open"}, "apm_service": {"status": 0, "version": "1.1.4.0-open"}, "cps_service": {"status": 1, "version": "3.9.0"}, "man_service": {"status": 0, "version": "1.2.7"}}, "use_maven": true, "proguard_keeplist": "\n#httpdns\n-keep class com.taobao.** {*;}\n-keep class com.alibaba.** {*;}\n-keep class com.ta.**{*;}\n-keep class com.ut.**{*;}\n-dontwarn com.taobao.**\n-dontwarn com.alibaba.**\n-dontwarn com.ta.**\n-dontwarn com.ut.**\n\n#cps\n-keep class com.taobao.** {*;}\n-keep class com.alibaba.** {*;}\n-keep class com.ta.**{*;}\n-keep class com.ut.**{*;}\n-dontwarn com.taobao.**\n-dontwarn com.alibaba.**\n-dontwarn com.ta.**\n-dontwarn com.ut.**\n-keepclasseswithmembernames class ** {\nnative <methods>;\n}\n-keepattributes Signature\n-keep class sun.misc.Unsafe { *; }\n-keep class com.alipay.** {*;}\n-dontwarn com.alipay.**\n-keep class anet.**{*;}\n-keep class org.android.spdy.**{*;}\n-keep class org.android.agoo.**{*;}\n-dontwarn anet.**\n-dontwarn org.android.spdy.**\n-dontwarn org.android.agoo.**\n\n#hotfix\n#基线包使用，生成mapping.txt\n-printmapping mapping.txt\n#生成的mapping.txt在app/buidl/outputs/mapping/release路径下，移动到/app路径下\n#修复后的项目使用，保证混淆结果一致\n#-applymapping mapping.txt\n#hotfix\n-keep class com.taobao.sophix.**{*;}\n-keep class com.ta.utdid2.device.**{*;}\n#防止inline\n-dontoptimize\n\n#man\n-keep class com.taobao.** {*;}\n-keep class com.alibaba.** {*;}\n-keep class com.ta.**{*;}\n-keep class com.ut.**{*;}\n-dontwarn com.taobao.**\n-dontwarn com.alibaba.**\n-dontwarn com.ta.**\n-dontwarn com.ut.**\n\n#feedback\n-keep class com.taobao.** {*;}\n-keep class com.alibaba.** {*;}\n-keep class com.ta.**{*;}\n-keep class com.ut.**{*;}\n-dontwarn com.taobao.**\n-dontwarn com.alibaba.**\n-dontwarn com.ta.**\n-dontwarn com.ut.**\n"}