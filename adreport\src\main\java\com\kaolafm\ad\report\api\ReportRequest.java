package com.kaolafm.ad.report.api;

import com.kaolafm.opensdk.http.core.HttpCallback;

/**
 * <AUTHOR>
 * @date 2020/5/22
 */
public interface ReportRequest {

    /**
     * 显示广告上报
     *
     * @param sessionId 每次请求广告返回的唯一值
     * @param time     广告显示时间 yyyy-MM-dd_HH:mm:ss
     * @param callback 回调
     */
    void display(String sessionId, String time, HttpCallback<Boolean> callback);

    /**
     * 上报广告显示结束时间
     *
     * @param sessionId 每次请求广告返回的唯一值
     * @param endTime  广告图片显示结束时间 yyyy-MM-dd_HH:mm:ss
     * @param callback 回调
     */
    void endDisplay(String sessionId, String endTime, HttpCallback<Boolean> callback);

    /**
     * 上报打断广告显示
     *
     * @param      sessionId 每次请求广告返回的唯一值
     * @param interruptTime 广告图片因阻碍用户操作而被关闭的时间，格式为yyyy-MM-dd_HH:mm:ss
     * @param callback      回调
     */
    void interruptDisplay(String sessionId, String interruptTime, HttpCallback<Boolean> callback);

    /**
     * 上报开始播放
     *
     * @param sessionId 每次请求广告返回的唯一值
     * @param time     开始播放时间，格式为yyyy-MM-dd_HH:mm:ss
     * @param callback 回调
     */
    void play(String sessionId, String time, HttpCallback<Boolean> callback);

    /**
     * 上报播放结束
     *
     * @param sessionId 每次请求广告返回的唯一值
     * @param time     播放结束时间, 格式为yyyy-MM-dd_HH:mm:ss
     * @param playTime 播放时长，单位秒
     * @param callback 回调
     */
    void endPlay(String sessionId, String time, long playTime, HttpCallback<Boolean> callback);

    /**
     * 上报跳过广告
     *
     * @param sessionId 每次请求广告返回的唯一值
     * @param skipTime 用户跳过广告的时间，格式为yyyy-MM-dd_HH:mm:ss
     * @param callback 回调
     */
    void skip(String sessionId, String skipTime, HttpCallback<Boolean> callback);

    /**
     * 上报二次互动图片显示时间
     *
     * @param    sessionId 每次请求广告返回的唯一值
     * @param displayTime 二次互动图标显示的时间，格式为yyyy-MM-dd_HH:mm:ss
     * @param callback    回调
     */
    void displayMoreInteraction(String sessionId, String displayTime, HttpCallback<Boolean> callback);

    /**
     * 上报二次互动广告图片显示结束时间
     *
     * @param       sessionId 每次请求广告返回的唯一值
     * @param displayEndTime 二次互动广告图片显示结束时间，格式为yyyy-MM-dd_HH:mm:ss
     * @param callback       回调
     */
    void displayMoreInteractionEnd(String sessionId, String displayEndTime, HttpCallback<Boolean> callback);

    /**
     * 上报广告点击事件
     *
     * @param  sessionId 每次请求广告返回的唯一值
     * @param clickTime 二次互动广告图片显示结束时间，格式为yyyy-MM-dd_HH:mm:ss
     * @param callback  回调
     */
    void click(String sessionId, String clickTime, HttpCallback<Boolean> callback);
}
