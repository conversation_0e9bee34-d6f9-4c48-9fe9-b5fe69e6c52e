package com.kaolafm.opensdk.demo.operation.category;

import android.content.Intent;
import android.os.Bundle;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.widget.EditText;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;

import com.google.gson.Gson;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.operation.OperationRequest;
import com.kaolafm.opensdk.api.operation.model.category.Category;
import com.kaolafm.opensdk.api.operation.model.category.LeafCategory;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.permission.IPermissionListener;
import com.kaolafm.opensdk.permission.PermissionUtil;
import com.lcodecore.tkrefreshlayout.RefreshListenerAdapter;
import com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout;

import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 分类页面
 *
 * <AUTHOR> Yan
 * @date 2018/7/31
 */

public class CategoryActivity extends BaseActivity {

    @BindView(R.id.et_column_extra)
    EditText mEtColumnExtra;

    @BindView(R.id.et_column_zone)
    EditText mEtColumnZone;

    @BindView(R.id.rv_category_list)
    RecyclerView mRvCategoryList;

    @BindView(R.id.switch_category_stratify)
    Switch mSwitchCategoryStratify;

    @BindView(R.id.trf_category_refresh)
    TwinklingRefreshLayout mTrfCategoryRefresh;

    @BindView(R.id.tv_column_commit)
    TextView mTvColumnCommit;

    private CategoryAdapter mCategoryAdapter;

    private int mContentType = ResType.TYPE_ALL;

    private String mContentName = "综合";

    private OperationRequest mOperationRequest;

    private String mParentCode = "0";

    @Override
    public int getLayoutId() {
        return R.layout.activity_category;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        setTitle("分类列表");
        mRvCategoryList.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false));
        mCategoryAdapter = new CategoryAdapter();
        mRvCategoryList.setAdapter(mCategoryAdapter);
        mCategoryAdapter.setOnItemClickListener((view, viewType, category, position) -> {
            //叶子分类下面才有分类成员
            if (category instanceof  LeafCategory) {
                if(((LeafCategory) category).getName().equals("本地广播")){
                    new PermissionUtil(CategoryActivity.this).requestLocationPermissions(new IPermissionListener() {
                        @Override
                        public void onSuccess(String s) {
                            Intent intent = new Intent(CategoryActivity.this, CategoryMemberActivity.class);
                            intent.putExtra(CategoryMemberActivity.KEY_CODE, ((LeafCategory)category).getCode());
                            startActivity(intent);
                        }

                        @Override
                        public void onFail(String s) {
                            Toast.makeText(CategoryActivity.this, "未获得地理位置授权", Toast.LENGTH_SHORT).show();
                        }
                    });
                }else {
                    Intent intent = new Intent(this, CategoryMemberActivity.class);
                    intent.putExtra(CategoryMemberActivity.KEY_CODE, ((LeafCategory)category).getCode());
                    startActivity(intent);
                }
                //分类下面可以有子分类
            } else {
                mParentCode = ((Category)category).getCode();
                getSucategoryList();
            }
        });
        mRvCategoryList.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));

        mTrfCategoryRefresh.setOnRefreshListener(new RefreshListenerAdapter() {
            @Override
            public void onLoadMore(TwinklingRefreshLayout refreshLayout) {
                super.onLoadMore(refreshLayout);
            }

            @Override
            public void onRefresh(TwinklingRefreshLayout refreshLayout) {
                super.onRefresh(refreshLayout);
                initData();
            }
        });
        mTrfCategoryRefresh.setEnableLoadmore(false);
        mSwitchCategoryStratify.setOnCheckedChangeListener((buttonView, isChecked) -> initData());
    }

    private void getSucategoryList() {
        mOperationRequest.getSubcategoryList(mParentCode, new HttpCallback<List<LeafCategory>>() {
            @Override
            public void onSuccess(List<LeafCategory> leafCategories) {
                if (!ListUtil.isEmpty(leafCategories)) {
                    mCategoryAdapter.setDataList(leafCategories);
                    showToast("显示子分类");
                    setTitle("子分类列表");
                }else {
                    showToast(mContentName + "的子分类为空");
                }
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取子分类错误", exception);
            }
        });
    }

    @Override
    public void initData() {
        mParentCode = "0";
        if (mOperationRequest == null) {
            mOperationRequest = new OperationRequest();
        }
        Log.e("CategoryActivity", "initData: ");
        getCategoryList(mContentType);
    }

    @OnClick(R.id.tv_column_commit)
    public void onViewClicked() {
        initData();
    }

    /**
     * 获取分类列表
     */
    private void getCategoryList(int contentType) {
        HttpCallback<List<Category>> callback = new HttpCallback<List<Category>>() {
            @Override
            public void onSuccess(List<Category> categories) {
                if (!ListUtil.isEmpty(categories)) {
                    if (!TextUtils.equals("0", mParentCode)) {
                        categories = categories.get(0).getChildCategories();
                    }
                }else {
                    showToast("数据为空");
                }
                mCategoryAdapter.setDataList(categories);
                mTrfCategoryRefresh.finishLoadmore();
                mTrfCategoryRefresh.finishRefreshing();
            }

            @Override
            public void onError(ApiException exception) {
                showToast("网络请求错误，错误码：" + exception.getCode() + ", 错误信息：" + exception.getMessage());
                if (mTrfCategoryRefresh != null) {
                    mTrfCategoryRefresh.finishLoadmore();
                    mTrfCategoryRefresh.finishRefreshing();
                }
            }
        };
        boolean isStratify = mSwitchCategoryStratify.isChecked();
        String zone = mEtColumnZone.getText().toString().trim();
        String extra = mEtColumnExtra.getText().toString().trim();
        HashMap<String, String> hashMap = new HashMap<>();
        if (!TextUtils.isEmpty(extra)) {
            try {
                hashMap = new Gson().fromJson(extra, HashMap.class);
            }catch (Exception e) {
                showToast("额外信息需要是一个map集合");
            }
        }
        if (isStratify) {//分层获取
            showToast("分层获取"+mContentName+"分类列表");
            mOperationRequest.getCategoryRoot(contentType, zone, hashMap, callback);
        } else {//一次获取所有
            showToast("获取"+mContentName+"整个分类树");
            mOperationRequest.getCategoryTree(contentType, zone, hashMap, callback);
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_category_type, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int itemId = item.getItemId();
        switch (itemId) {
            case R.id.action_category_all:
                mContentType = ResType.TYPE_ALL;
                mContentName = "综合";
                break;
            case R.id.action_category_album:
                mContentType = ResType.TYPE_ALBUM;
                mContentName = "专辑";
                break;
            case R.id.action_category_broadcast:
                mContentType = ResType.TYPE_BROADCAST;
                mContentName = "在线广播";
                break;
            case R.id.action_category_live:
                mContentType = ResType.TYPE_LIVE;
                mContentName = "直播";
                break;
            case R.id.action_category_radio:
                mContentType = ResType.TYPE_RADIO;
                mContentName = "电台";
                break;
            case R.id.action_category_news:
                mContentType = ResType.TYPE_NEWS;
                mContentName = "新闻碎片";
                break;
                case R.id.action_category_tv:
                mContentType = ResType.TYPE_TV;
                mContentName = "听电视";
                break;
            default:
        }
        initData();
        return super.onOptionsItemSelected(item);
    }
}
