package com.kaolafm.opensdk.http.error;

/**
 * 网络异常类
 *
 * <AUTHOR>
 * @date 2018/6/8
 */
public class ApiException extends Exception {

    private int code;

    private String message;

    /**
     * 是否已经toast提示，true表示已经弹toast提示
     */
    private boolean haveShow = false;

    public ApiException() {
    }

    public ApiException(String message) {
        this.message = message;
    }

    public ApiException(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public ApiException(Throwable cause) {
        super(cause);
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public String getDetailMessage() {
        return getCause().getMessage();
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public boolean isHaveShow() {
        return haveShow;
    }

    public void setHaveShow(boolean haveShow) {
        this.haveShow = haveShow;
    }

    @Override
    public String toString() {
        return "ApiException{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", haveShow=" + haveShow +
                '}';
    }
}
