package com.kaolafm.opensdk.http.download;

import android.os.Environment;
import android.text.TextUtils;

/**
 * 下载管理类
 * <AUTHOR>
 * @date 2020-02-07
 */
public class DownloadManager {

    private static volatile DownloadManager mInstance;

    Downloader mDownloader;

    private DownloadManager() {
        mDownloader = new DownloaderImpl();
    }

    public static DownloadManager getInstance() {
        if (mInstance == null) {
            synchronized (DownloadManager.class) {
                if (mInstance == null) {
                    mInstance = new DownloadManager();
                }
            }
        }
        return mInstance;
    }

    public void download(String url, DownloadListener listener) {
        String path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).getPath();
        download(url, null, path, listener);
    }


    public void download(String url, String fileName, String path, DownloadListener listener) {
        if (TextUtils.isEmpty(url)) {
            return;
        }
        DownloadRequest downloadRequest = new NormalDownloadRequest();
        downloadRequest.url = url;
        downloadRequest.fileName = fileName;
        downloadRequest.filePath = path;
        download(downloadRequest, listener);
    }

    public void download(DownloadRequest request, DownloadListener listener) {
        if (request != null) {
            mDownloader.download(request, listener);
        }
    }

    public void download(String url, String path) {
        download(url, null, path);
    }

    public void download(String url, String name, String path) {
        download(url, name, path, null);
    }

    public void pause(String url){
        mDownloader.pause(url);
    }

    public void cancel(String url) {
        mDownloader.cancel(url);
    }

    public void download(int sessionId, String... url) {

    }


}
