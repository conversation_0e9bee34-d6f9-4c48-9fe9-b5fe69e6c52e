package com.kaolafm.base.internal;

import android.content.Context;
import android.text.TextUtils;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-06-18
 */
public abstract class BaseObtainDeviceId implements ObtainDeviceId {

    protected String mId;

    private int mIndex;

    private List<ObtainDeviceId> mList;

    private ObtainDeviceId mNextChain;

    @Override
    public String getUUID(Context context) {
        //只有在上一个责任链没有生成uuid时才往下传，使用下一个链生成
        if (TextUtils.isEmpty(mId)) {
            mId = createUUID(context);
            if (mNextChain != null) {
                mNextChain.setNextChain(mId, mList, mIndex + 1);
                return mNextChain.getUUID(context);
            }
        } else {//如果上一个传下的有id，就保存并回传给上一个链。
            saveUUID(mId, mIndex - 1);
        }
        return mId;
    }

    @Override
    public void setNextChain(String uuid, List<ObtainDeviceId> list, int index) {
        mId = uuid;
        mList = list;
        mIndex = index;
        if (index < list.size() && index >= 0) {
            mNextChain = list.get(index);
        }
    }

    @Override
    public void saveUUID(String uuid, int index) {
        //可以在这里保存
        //👇️这里是回传给上一个责任链保存。
        int pre = index - 1;
        if (pre >= 0 && pre < mList.size()) {
            ObtainDeviceId obtainDeviceId = mList.get(pre);
            obtainDeviceId.saveUUID(uuid, pre);
        }
    }

    protected abstract String createUUID(Context context);

}
