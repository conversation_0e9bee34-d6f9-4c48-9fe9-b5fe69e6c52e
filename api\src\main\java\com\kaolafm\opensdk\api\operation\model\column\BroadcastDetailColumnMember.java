package com.kaolafm.opensdk.api.operation.model.column;

/**
 * 栏目成员：在线广播
 */
public class BroadcastDetailColumnMember extends ColumnContent {

    /** 在线广播的id*/
    private long broadcastId;

    /** 在线在线广播的频段*/
    private String freq;

    /** 在线广播的收听数*/
    private int playTimes;

    /** 用来区分广播类型（音乐，交通，新闻等）*/
    private int broadcastSort;

    public long getBroadcastId() {
        return broadcastId;
    }

    public void setBroadcastId(long broadcastId) {
        this.broadcastId = broadcastId;
    }

    public int getPlayTimes() {
        return playTimes;
    }

    public void setPlayTimes(int playTimes) {
        this.playTimes = playTimes;
    }


    public String getFreq() {
        return freq;
    }

    public void setFreq(String freq) {
        this.freq = freq;
    }

    public int getBroadcastSort() {
        return broadcastSort;
    }

    public void setBroadcastSort(int broadcastSort) {
        this.broadcastSort = broadcastSort;
    }

    @Override
    public String toString() {
        return "BroadcastDetailColumnMember{" +
                "broadcastId=" + broadcastId +
                ", freq='" + freq + '\'' +
                ", playTimes=" + playTimes +
                ", broadcastSort=" + broadcastSort +
                '}';
    }
}
