package com.kaolafm.opensdk.http.error;

import org.reactivestreams.Publisher;

import java.util.List;

import io.reactivex.Flowable;

/**
 * <AUTHOR>
 * @date 2020-02-16
 */
public class FlowableErrorFunc<E> extends BaseErrorFunc<Publisher<E>> {

    public FlowableErrorFunc(List<ResponseErrorListener> errorListenerList) {
        super(errorListenerList);
    }

    @Override
    Publisher<E> getError(ApiException e) {
        return Flowable.error(e);
    }
}
