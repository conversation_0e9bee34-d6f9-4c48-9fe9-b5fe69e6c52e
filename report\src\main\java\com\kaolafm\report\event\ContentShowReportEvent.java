package com.kaolafm.report.event;

import com.kaolafm.report.util.ReportConstants;

/**
 * 内容曝光上报 290版本新增
 */
public class ContentShowReportEvent extends BaseReportEventBean{

    // 0专辑 1广播 2AI电台 3直播 4功能入口 5学习强国模块
    public static final String CONTENT_TYPE_ALBUM      = "0";
    public static final String CONTENT_TYPE_BROADCAST  = "1";
    public static final String CONTENT_TYPE_RADIO      = "2";
    public static final String CONTENT_TYPE_LIVE       = "3";
    public static final String CONTENT_TYPE_FUNCTION   = "4";
    public static final String CONTENT_TYPE_STUDY      = "5";

    public static final String AUDIO_TYPE_FREE = "0";
    public static final String AUDIO_TYPE_VIP  = "1";

    public static final String TAG_JINGPIN = "精品";
    public static final String TAG_VIP     = "VIP";
    public static final String TAG_NO      = "无";

    //单曲id: 播放的单曲id
    private String audioid;
    //内容类型: 0专辑 1广播 2AI电台 3直播 4功能入口 5学习强国模块
    private String radiotype;
    //单曲类型: 0免费 1付费
    private String audioidtype;
    //专辑id: 如果是专辑的话就是专辑ID，如果是AI电台的话就是AI电台ID
    private String radioid;
    //标签: 精品 VIP 无
    private String tag;
    //页面地址
    private String pageid;
    //运营位id
    private String remarks1;
    //成员位置:服务端吐出的顺序位置
    private String remarks2;

    public ContentShowReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_CONTENT_SHOW);
    }

    public String getAudioId() {
        return audioid;
    }

    public void setAudioId(String audioid) {
        this.audioid = audioid;
    }

    public String getRadioType() {
        return radiotype;
    }

    public void setRadioType(String radiotype) {
        this.radiotype = radiotype;
    }

    public String getAudioidType() {
        return audioidtype;
    }

    public void setAudioidType(String audioidtype) {
        this.audioidtype = audioidtype;
    }

    public String getRadioId() {
        return radioid;
    }

    public void setRadioId(String radioid) {
        this.radioid = radioid;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getPageId() {
        return pageid;
    }

    public void setPageId(String pageid) {
        this.pageid = pageid;
    }

    public String getRemarks1() {
        return remarks1;
    }

    public void setRemarks1(String remarks1) {
        this.remarks1 = remarks1;
    }

    public String getRemarks2() {
        return remarks2;
    }

    public void setRemarks2(String remarks2) {
        this.remarks2 = remarks2;
    }
}
