package com.kaolafm.opensdk;

import android.app.Application;

import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.di.component.DaggerKradioComponent;
import com.kaolafm.opensdk.http.core.HttpCallback;

/**
 * 该类是Kradio SDK 单独打包时的真正入口。
 * 为了兼容旧版本，无论是单独打包还是分开打包，只要带有Kradio SDK模块的入口都是{@link OpenSDK}。
 * <AUTHOR>
 * @date 2019-05-29
 */
public class KradioSDK extends KradioSDKInternalEngine<Options> {


    @Override
    protected void internalInit(Application application, Options options, HttpCallback<Boolean> callback) {
        ComponentKit.getInstance().inject(DaggerKradioComponent.builder(), application, options, this);
        super.internalInit(application, options, callback);
    }

    public void init(Application application, HttpCallback<Boolean> callback) {
        init(application, null, callback);
    }

    @Override
    public void release() {
        super.release();
    }
}
