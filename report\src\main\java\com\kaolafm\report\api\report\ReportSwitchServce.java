package com.kaolafm.report.api.report;

import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.report.api.ReportApiConstant;
import com.kaolafm.report.api.ReportHostConstant;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;

/**
 * <AUTHOR>
 * @date 2019-11-12
 */
interface ReportSwitchServce {
    // TODO: 2023/1/13  
    @Headers(ReportHostConstant.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(ReportApiConstant.CONFIG_SWITCH_REPORT)
    Single<BaseResult<ReportSwitchOption>> isUploadBigDatacenter();
}
