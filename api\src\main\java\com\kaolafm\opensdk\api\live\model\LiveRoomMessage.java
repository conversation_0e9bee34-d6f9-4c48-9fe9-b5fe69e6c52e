package com.kaolafm.opensdk.api.live.model;


import androidx.annotation.Nullable;

import org.greenrobot.greendao.annotation.NotNull;

public class LiveRoomMessage {
    @NotNull
    private MessageType messageType;
    @Nullable
    private ChatUserInfo userInfo;
    @Nullable
    private MessageBean message;

    @NotNull
    public MessageType getMessageType() {
        return this.messageType;
    }

    @Nullable
    public ChatUserInfo getUserInfo() {
        return this.userInfo;
    }

    @Nullable
    public MessageBean getMessage() {
        return this.message;
    }

    public void setMessageType(MessageType messageType) {
        this.messageType = messageType;
    }

    public void setUserInfo(@Nullable ChatUserInfo userInfo) {
        this.userInfo = userInfo;
    }

    public void setMessage(@Nullable MessageBean message) {
        this.message = message;
    }

    public LiveRoomMessage(@NotNull MessageType messageType, @Nullable ChatUserInfo userInfo, @Nullable MessageBean message) {
        this.messageType = messageType;
        this.userInfo = userInfo;
        this.message = message;
    }

    public enum MessageType {
        MESSAGE_ENTER_ROOM,
        MESSAGE_NORMAL,
        MESSAGE_SYSTEM,
        MESSAGE_THEME,
        MESSAGE_SEND,
        MESSAGE_RECEIVE
    }
}