package com.kaolafm.opensdk.api.operation.model.category;

import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.operation.model.ImageFile;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 分类。分类下只有子分类。
 */
public class Category implements Serializable {

    /**
     * 分类code，用于请求分类下的子分类或分类成员。该值是可变的
     */
    private String code;

    /**
     * SDK内部使用，开发者不需要关心，会一直为空
     */
    private String type;

    /**
     * 分类名
     */
    private String name;

    /**
     * 分类描述
     */
    @Deprecated
    private String description;

    /**
     * 分类的内容类型。0：综合1:专辑，2:在线广播，3:直播，4:AI电台，5:QQ音乐电台 6：新闻 7：听电视
     */
    private int contentType;

    /**
     * 是否是落地页 0-否 1-是
     */
    private int isLandingPage;

    /**
     * 子分类列表
     */
    private List<Category> childCategories;

    /**
     * 图片信息集合
     */
    private Map<String, ImageFile> imageFiles;

    /**
     * 额外信息，用于一些定制需求
     */
    private Map<String, String> extInfo;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getContentType() {
        return contentType;
    }

    public int getIsLandingPage() {
        return isLandingPage;
    }

    public void setIsLandingPage(int isLandingPage) {
        this.isLandingPage = isLandingPage;
    }

    /**
     * 获取ResType对应的类型
     * @return
     */
    public int getContenResType() {
        switch (contentType) {
            case 0:
                return ResType.TYPE_ALL;
            case 1:
                return ResType.TYPE_ALBUM;
            case 2:
                return ResType.TYPE_BROADCAST;
            case 3:
                return ResType.TYPE_LIVE;
            case 4:
                return ResType.TYPE_RADIO;
            case 5:
                return ResType.TYPE_QQ_MUSIC;
            case 6:
                return ResType.TYPE_NEWS;
            case 7:
                return ResType.TYPE_TV;
            case 8:
                return ResType.TYPE_FEATURE;
            default:
        }
        return contentType;
    }

    public void setContentType(Integer contentType) {
        this.contentType = contentType;
    }

    public List<Category> getChildCategories() {
        return childCategories;
    }

    public void setChildCategories(List<Category> childCategories) {
        this.childCategories = childCategories;
    }

    public Map<String, ImageFile> getImageFiles() {
        return imageFiles;
    }

    public void setImageFiles(Map<String, ImageFile> imageFiles) {
        this.imageFiles = imageFiles;
    }

    public Map<String, String> getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(Map<String, String> extInfo) {
        this.extInfo = extInfo;
    }

    @Override
    public String toString() {
        return "Category{" +
                "code='" + code + '\'' +
                ", type='" + type + '\'' +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", contentType=" + contentType +
                ", isLandingPage=" + isLandingPage +
                ", childCategories=" + childCategories +
                ", imageFiles=" + imageFiles +
                ", extInfo=" + extInfo +
                '}';
    }
}
