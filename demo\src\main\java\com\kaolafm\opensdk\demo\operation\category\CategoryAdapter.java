package com.kaolafm.opensdk.demo.operation.category;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import butterknife.BindView;
import com.bumptech.glide.Glide;
import com.kaolafm.opensdk.api.operation.model.ImageFile;
import com.kaolafm.opensdk.api.operation.model.category.Category;
import com.kaolafm.opensdk.demo.BaseAdapter;
import com.kaolafm.opensdk.demo.BaseHolder;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.utils.operation.OperationAssister;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/8/7
 */

public class CategoryAdapter extends BaseAdapter {

    @Override
    protected BaseHolder getViewHolder(View view, int viewType) {
        return new CategoryHolder(view);
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_category;
    }

    static class CategoryHolder extends BaseHolder {

        @BindView(R.id.iv_item_category_icon)
        ImageView mIvItemCategoryIcon;

        @BindView(R.id.tv_item_category_des)
        TextView mTvItemCategoryDes;

        @BindView(R.id.tv_item_category_name)
        TextView mTvItemCategoryName;

        public CategoryHolder(View itemView) {
            super(itemView);
        }

        @Override
        public void setupData(Object object, int position) {
            Category category = (Category) object;
            Map<String, ImageFile> imageFiles = category.getImageFiles();
            String cover = OperationAssister.getImage(ImageFile.KEY_COVER, imageFiles);
            if (TextUtils.isEmpty(cover)) {
                cover = OperationAssister.getImage(ImageFile.KEY_ICON, imageFiles);
            }
            Glide.with(itemView).load(cover).into(mIvItemCategoryIcon);
            mTvItemCategoryName.setText(category.getName());
            mTvItemCategoryDes.setText(category.getDescription());
        }
    }
}
