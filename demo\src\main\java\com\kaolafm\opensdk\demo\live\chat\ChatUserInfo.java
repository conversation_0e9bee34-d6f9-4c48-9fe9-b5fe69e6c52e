package com.kaolafm.opensdk.demo.live.chat;

import com.netease.nimlib.sdk.chatroom.model.ChatRoomMember;

public class ChatUserInfo extends UserCenterUserInfoData  {

    /**
     * bundle携带数据的键名称
     */
    private static final String CHAT_USER_INFO = "chatUserInfo";

    /**
     * 用户的角色状态
     */
    private String role;


    public ChatUserInfo() {
    }


    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public ChatUserInfo liveOnlineBean2ChatUserInfo(LiveOnlineBean userInfoData) {
        ChatUserInfo userInfo = new ChatUserInfo();

        if (userInfoData != null) {
            userInfo.setAvatar(userInfoData.getAvatar());
            userInfo.setNickName(userInfoData.getNickName());
            userInfo.setUid(userInfoData.getUid());
        }
        return userInfo;
    }

    public static ChatUserInfo chatRoomMember2ChatUserInfo(ChatRoomMember chatRoomMember) {
        ChatUserInfo userInfo = new ChatUserInfo();

        if (chatRoomMember != null) {
            userInfo.setAvatar(chatRoomMember.getAvatar());
            userInfo.setNickName(chatRoomMember.getNick());
            userInfo.setUid(chatRoomMember.getAccount());
        }
        return userInfo;
    }

    public static ChatUserInfo messageBean2ChatUserInfo(MessageBean messageBean) {
        ChatUserInfo userInfo = new ChatUserInfo();

        if (messageBean != null) {
            userInfo.setAvatar(messageBean.userIconUrl);
            userInfo.setNickName(messageBean.nickName);
            userInfo.setUid(messageBean.account);
        }
        return userInfo;
    }

}