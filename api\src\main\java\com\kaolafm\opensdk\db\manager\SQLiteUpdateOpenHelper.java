package com.kaolafm.opensdk.db.manager;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;

import com.kaolafm.opensdk.db.greendao.DaoMaster;
import com.kaolafm.opensdk.db.greendao.ListeningHistoryDao;
import com.kaolafm.opensdk.db.helper.MigrationHelper;

import org.greenrobot.greendao.database.Database;

/**
 * <AUTHOR>
 * @date 2020/8/10
 */
public class SQLiteUpdateOpenHelper extends DaoMaster.OpenHelper {

    public SQLiteUpdateOpenHelper(Context context, String name) {
        super(context, name);
    }

    public SQLiteUpdateOpenHelper(Context context, String name, SQLiteDatabase.CursorFactory factory) {
        super(context, name, factory);
    }

    @Override
    public void onUpgrade(Database db, int oldVersion, int newVersion) {
        MigrationHelper.migrate(db, ListeningHistoryDao.class);
    }
}
