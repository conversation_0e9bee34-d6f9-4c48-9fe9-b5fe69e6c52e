package com.kaolafm.base.utils;

import android.app.Application;
import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

public class SeCoreUtils {
    private static SeCoreUtils mInstance;
    private SeCore mSeCore = new SeCore();

    private SharedPreferences mSharedPreferences = null;

    private static final String SP_KAOLA_ENCRYPT_NAME
            = "sdk.KaolaEncrypt.SharedPreferences";

    public static final String KAOLA_ENCRYPT
            = "com.kaolafm.open.sdk.kaola.encrypt";

    public static final String KEY_ACCESSTOKEN = "accesstoken";
    public static final String KEY_UDID = "udid";

    public interface SecureCallBack{
        public void encrypt(String str);
    }
    private SeCoreUtils()
    {
        Log.e("SeCoreUtils",mSeCore.hello());
    }
    public static SeCoreUtils getInstance() {
        if (mInstance == null) {
            synchronized (SeCoreUtils.class) {
                if (mInstance == null) {
                    mInstance = new SeCoreUtils();
                }
            }
        }
        return mInstance;
    }

    public void SeCoreInit(Application application){
        mSharedPreferences = application
                .getSharedPreferences(application.getPackageName() + SP_KAOLA_ENCRYPT_NAME, Context.MODE_PRIVATE);
    }

    public String callEncrypt(String plain,String key){
        //标识已经有加密
        SharedPreferences.Editor editor = mSharedPreferences.edit().putBoolean(KAOLA_ENCRYPT+key, true);
        editor.apply();
        return mSeCore.encrypt(plain);
    }

    public static String encrypt(String originalStr) {
        return getInstance().mSeCore.encrypt(originalStr);
    }

    public static String decrypt(String cipheredStr) {
        return getInstance().mSeCore.decrypt(cipheredStr);
    }

    public String callDecrypt(String plain,String key,SecureCallBack cb){
        //判断是否加过密
        boolean isEncrypt = mSharedPreferences.getBoolean(KAOLA_ENCRYPT+key, false);
        if(isEncrypt){
            return mSeCore.decrypt(plain);
        }else{
            cb.encrypt(plain);
            return plain;
        }
    }

    public void updateCrypt(){

    }
}
