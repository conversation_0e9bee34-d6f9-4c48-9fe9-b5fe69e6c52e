apply plugin: 'com.android.library'
apply plugin: 'build-jar'
apply plugin: 'org.greenrobot.greendao'
def and = rootProject.ext.android
def dependent = rootProject.ext.dependencies
def config = rootProject.ext.config

def VERSION_CODE = and.versionCode
def VERSION_NAME = and.versionName

android {
    compileSdkVersion and.compileSdkVersion
    buildToolsVersion and.buildToolsVersion
    defaultConfig {
        minSdkVersion and.minSdkVersion
        targetSdkVersion and.targetSdkVersion
        versionCode VERSION_CODE
        versionName VERSION_NAME
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
        ndk {
            abiFilter("arm64-v8a")
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            //Zipalign优化
            zipAlignEnabled false
            buildConfigField "String", "API_VERSION", "\""+config["api_version"]+"\""
        }
        debug {
            buildConfigField "String", "API_VERSION", "\""+config["api_version"]+"\""
        }
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }

    compileOptions {
        sourceCompatibility and.javaSourceVersion
        targetCompatibility and.javaTargetVersion
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }

    packagingOptions {
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/LICENSE'
    }
    //配置数据库相关信息
    greendao {
        schemaVersion 1 //数据库版本号
        daoPackage 'com.kaolafm.opensdk.db.greendao'
        targetGenDir 'src/main/java'//设置DaoMaster、DaoSession、Dao目录
    }

}

dependencies {
    api fileTree(include: ['*.jar'], dir: 'libs')
    api dependent["lifecycle-runtime"]
    api dependent["lifecycle-extensions"]
    api dependent["room-runtime"]
    annotationProcessor dependent["room-compiler"]
    api dependent["room-rxjava2"]
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.0'
    annotationProcessor dependent["dagger2-compiler"]
    implementation project(":core")
    embed project(path:":report", configuration:"default")
    compileOnly(dependent["rxlifecycle3-components"]) {
        exclude module: 'rxjava'
        exclude module: 'appcompat-v7'
        exclude module: 'rxandroid'
        exclude module: 'support-annotations'
    }
}




