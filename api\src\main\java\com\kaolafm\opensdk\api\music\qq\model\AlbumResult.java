package com.kaolafm.opensdk.api.music.qq.model;

import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * 请求获取专辑结果
 * <AUTHOR>
 * @date 2018/4/23
 */

public class AlbumResult extends BaseMusicResult<List<MusicAlbum>> {

    /**
     * album_list : [{"album_desc":"前奏一响就是回忆杀！由唐季礼执导，成龙、李治廷、张艺兴主演的动作喜剧《功夫瑜伽》今日曝光了电影主题曲《美丽的神话》。电影《功夫瑜伽》讲述了成龙、李治廷、张艺兴组成的\u201c功夫三傻\u201d，为寻找宝藏，游遍全球历...","album_id":1792942,"album_mid":"001tWKC93ywEY6","album_name":"美丽的神话","album_translator_name":"","company_name":"北京听见时代娱乐传媒有限公司","listen_num":3851918,"public_time":1482768000,"singer_id":13,"singer_name":"成龙","song_id_list":"*********,"},{"album_desc":"成龙获奥斯卡奖后首支单曲崔恕赵佳霖金牌搭档联手打造这首歌是成龙大哥获得奥斯卡终身成就奖后首次录制的正式单曲，歌曲的内容配合电影《铁道飞虎》的上映，完美展示了成龙大哥的龙式唱腔与铁汉柔情。歌曲讲述了战争...","album_id":1786445,"album_mid":"004FegkV3PoVqd","album_name":"琵琶行","album_translator_name":"","company_name":"北京希望无限文化传媒有限公司","listen_num":216335,"public_time":1482336000,"singer_id":13,"singer_name":"成龙","song_id_list":"*********,*********,"}]
     * total : 29
     */

    @SerializedName(value = "total", alternate = {"total_num"})
    private int total;
    @SerializedName("order")
    private int order;

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }
}
