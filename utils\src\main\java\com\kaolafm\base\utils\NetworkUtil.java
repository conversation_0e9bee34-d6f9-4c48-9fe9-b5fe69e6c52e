package com.kaolafm.base.utils;

import static android.content.Context.CONNECTIVITY_SERVICE;

import android.annotation.SuppressLint;
import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.net.NetworkInfo.State;
import android.net.TrafficStats;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.telephony.TelephonyManager;
import android.util.Log;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Collections;
import java.util.Enumeration;
import java.util.List;

public class NetworkUtil {
    private static final String TAG = "SDK_NetworkUtil";

    public static final int NETWORK_TYPE_UNKNOW = -1;
    public static final int NETWORK_TYPE_NO_WORK = 0;
    public static final int NETWORK_TYPE_WIFI = 1;
    public static final int NETWORK_TYPE_2G = 2;
    public static final int NETWORK_TYPE_3G = 3;
    public static final int NETWORK_TYPE_4G = 4;
    private static final int PRINT_COUNT = 5; // 每轮打印次数
    private static final int PRINT_INTERVAL = 1000; // 打印间隔时间，秒
    private static CountDownUtil mCountDownUtil;
    public static long mLastRx, mLastTx;
    public static long mLastProcessRx, mLastProcessTx;

    /**
     * 获取当前设备的Mac地址。如果获取失败，返回空字符串。
     */
    public static String getMacAddr(Context context) {
        return getMacAddrPostAndroidM();
    }

    @SuppressLint("HardwareIds")
    private static String getMacAddrPreAndroidM(Context context) {
        try {
            WifiManager wifi = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            if (wifi != null) {
                WifiInfo info = wifi.getConnectionInfo();
                if (info != null) {
                    return info.getMacAddress();
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting MAC address on pre-Android M", e);
        }
        return "";
    }

    private static String getMacAddrPostAndroidM() {
        try {
            List<NetworkInterface> all = Collections.list(NetworkInterface.getNetworkInterfaces());
            for (NetworkInterface nif : all) {
                if (nif.getName().equalsIgnoreCase("wlan0")) {
                    byte[] macBytes = nif.getHardwareAddress();
                    if (macBytes == null) {
                        return "";
                    }

                    StringBuilder res1 = new StringBuilder();
                    for (byte b : macBytes) {
                        res1.append(String.format("%02X:", b));
                    }

                    if (res1.length() > 0) {
                        res1.deleteCharAt(res1.length() - 1);
                    }
                    return res1.toString();
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting MAC address on post-Android M", e);
        }
        return "";
    }

    /**
     * 检测当前网络是否为WAP上网方式
     *
     * @return true为是，false为否
     */
    public static boolean isWAPStatic(Context context) {
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager == null) {
            return false;
        }

        try {
            Network network = connectivityManager.getActiveNetwork();
            if (network != null) {
                NetworkCapabilities networkCapabilities = connectivityManager.getNetworkCapabilities(network);
                if (networkCapabilities != null && networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)) {
                    NetworkInfo networkInfo = connectivityManager.getNetworkInfo(network);
                    return isWAPNetwork(networkInfo);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking if network is WAP", e);
        }
        return false;
    }

    private static boolean isWAPNetwork(NetworkInfo networkInfo) {
        if (networkInfo != null) {
            String extraInfo = networkInfo.getExtraInfo();
            if (extraInfo != null) {
                return extraInfo.toLowerCase().contains("wap");
            }
        }
        return false;
    }

    /**
     * Returns whether the network is available
     */
    public static boolean isNetworkAvailable(Context context) {
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager == null) {
            return false;
        }

        try {
            Network network = connectivityManager.getActiveNetwork();
            if (network != null) {
                NetworkCapabilities networkCapabilities = connectivityManager.getNetworkCapabilities(network);
                return networkCapabilities != null && networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) && networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking network availability", e);
        }

        return false;
    }


    /**
     * 判断当前是否为漫游连接
     */
    public static boolean isNetworkRoaming(Context context) {
        try {
            ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            if (connectivityManager == null) {
                return false;
            }

            Network network = connectivityManager.getActiveNetwork();
            if (network != null) {
                NetworkCapabilities networkCapabilities = connectivityManager.getNetworkCapabilities(network);
                if (networkCapabilities != null && networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)) {
                    return isTelephonyNetworkRoaming(context);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking if network is roaming", e);
        }
        return false;
    }

    private static boolean isTelephonyNetworkRoaming(Context context) {
        TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
        if (telephonyManager != null) {
            return telephonyManager.isNetworkRoaming();
        }
        return false;
    }

    /**
     * wifi连接
     *
     * @return 如果是wifi 并且 也处于连接状态中则返回真
     */
    public static boolean isWifiNetworkAvailable(Context context) {
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager == null) {
            return false;
        }

        try {
            Network network = connectivityManager.getActiveNetwork();
            if (network != null) {
                NetworkCapabilities networkCapabilities = connectivityManager.getNetworkCapabilities(network);
                if (networkCapabilities != null && networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)) {
                    return true;
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking if Wi-Fi network is available", e);
        }

        return false;
    }

    /**
     * 3G网
     * 3G 类型繁多，如果非2G, 4G ，则归为3G
     *
     * @return 如果是手机上网并且 也处于连接状态中则返回真
     */
    public static boolean isNGNetworkAvailable(Context context) {
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager == null) {
            return false;
        }

        try {
            Network network = connectivityManager.getActiveNetwork();
            if (network != null) {
                NetworkCapabilities networkCapabilities = connectivityManager.getNetworkCapabilities(network);
                if (networkCapabilities != null && networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)) {
                    return true;
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking if mobile network is available", e);
        }

        return false;
    }

    /**
     * 判断当前网络是否已经连接，并且是2G状态.
     *
     * @return true, or false
     */
    public static boolean is2GMobileNetwork(Context context) {
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager == null) {
            return false;
        }

        try {
            Network network = connectivityManager.getActiveNetwork();
            if (network != null) {
                NetworkCapabilities networkCapabilities = connectivityManager.getNetworkCapabilities(network);
                if (networkCapabilities != null && networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)) {
                    return is2GNetwork(context);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking if network is 2G", e);
        }

        return false;
    }

    private static boolean is2GNetwork(Context context) {
        TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
        if (telephonyManager != null) {
            int networkType = telephonyManager.getNetworkType();
            return networkType == TelephonyManager.NETWORK_TYPE_GPRS || networkType == TelephonyManager.NETWORK_TYPE_CDMA || networkType == TelephonyManager.NETWORK_TYPE_EDGE || networkType == TelephonyManager.NETWORK_TYPE_1xRTT || networkType == TelephonyManager.NETWORK_TYPE_IDEN;
        }
        return false;
    }

    /**
     * 判断当前网络是否已经连接，并且是4G状态. 根据产品的定义，4G为LTE;
     *
     * @return true, or false
     */
    public static boolean is4GMobileNetwork(Context context) {
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager == null) {
            return false;
        }

        try {
            Network network = connectivityManager.getActiveNetwork();
            if (network != null) {
                NetworkCapabilities networkCapabilities = connectivityManager.getNetworkCapabilities(network);
                if (networkCapabilities != null && networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)) {
                    return is4GNetwork(context);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking if network is 4G", e);
        }

        return false;
    }

    private static boolean is4GNetwork(Context context) {
        TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
        if (telephonyManager != null) {
            int networkType = telephonyManager.getNetworkType();
            return networkType == TelephonyManager.NETWORK_TYPE_LTE;
        }
        return false;
    }

    public static State getState(Context context) {
        NetworkInfo info = ((ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE)).getActiveNetworkInfo();
        if (info == null) {
            return State.UNKNOWN;
        }
        return info.getState();
    }

    /**
     * 获取当前可使用的网络类型。
     *
     * @return 结果可能为：wifi 4g 3g 2g nonet none
     */
    public static String getCurrentAvailableNetworkType(Context context) {
        try {
            ConnectivityManager connectivity = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            if (connectivity == null) {
                return "none";
            }
            if (!isNetworkAvailable(context)) {
                return "nonet";
            }
            if (isWifiNetworkAvailable(context)) {
                return "wifi";
            }
            if (is2GMobileNetwork(context)) {
                return "2g";
            }
            if (is4GMobileNetwork(context)) {
                return "4g";
            }
            if (isNGNetworkAvailable(context)) {
                return "3g";
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting current available network type", e);
        }
        return "nonet";
    }

    public static int getNetworkIndex(Context context) {
        try {
            ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
            if (networkInfo == null) {
                return 0;
            }
            if (networkInfo.getType() == ConnectivityManager.TYPE_WIFI) {
                return 1;
            } else if ((networkInfo.getSubtype() == TelephonyManager.NETWORK_TYPE_EDGE || networkInfo.getSubtype() == TelephonyManager.NETWORK_TYPE_GPRS || networkInfo.getSubtype() == TelephonyManager.NETWORK_TYPE_CDMA)) {
                return 2;
            } else if (networkInfo.getType() == ConnectivityManager.TYPE_MOBILE) {
                return 3;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting network index", e);
        }
        return 0;
    }

    public static int getNetwork(Context context) {
        try {
            ConnectivityManager connectivity = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            if (connectivity == null) {
                return NETWORK_TYPE_UNKNOW;
            }
            if (!isNetworkAvailable(context)) {
                return NETWORK_TYPE_NO_WORK;
            }
            if (isWifiNetworkAvailable(context)) {
                return NETWORK_TYPE_WIFI;
            }
            if (is2GMobileNetwork(context)) {
                return NETWORK_TYPE_2G;
            }
            if (is4GMobileNetwork(context)) {
                return NETWORK_TYPE_4G;
            }
            if (isNGNetworkAvailable(context)) {
                return NETWORK_TYPE_3G;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting current available network type", e);
        }
        return NETWORK_TYPE_UNKNOW;
    }

    public static String getIPAddress(Context context) {
        if (context == null) {
            Log.e(TAG, "Context is null, you must init first!");
            return null;
        }

        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(CONNECTIVITY_SERVICE);
        NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();

        // Check if the network is connected and is either mobile or wifi
        if (networkInfo != null && networkInfo.isConnected() &&
                (networkInfo.getType() == ConnectivityManager.TYPE_MOBILE ||
                        networkInfo.getType() == ConnectivityManager.TYPE_WIFI)) {

            try {
                return getValidIPAddress();
            } catch (SocketException e) {
                Log.e(TAG, "Error getting IP address", e);
            }
        } else {
            Log.e(TAG, "No active network connection. Please enable network in settings.");
        }

        return null;
    }

    private static String getValidIPAddress() throws SocketException {
        Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();

        while (networkInterfaces.hasMoreElements()) {
            NetworkInterface networkInterface = networkInterfaces.nextElement();

            // Skip inactive interfaces
            if (!networkInterface.isUp() || networkInterface.isLoopback()) {
                continue;
            }

            Enumeration<InetAddress> inetAddresses = networkInterface.getInetAddresses();
            while (inetAddresses.hasMoreElements()) {
                InetAddress inetAddress = inetAddresses.nextElement();
                if (inetAddress instanceof Inet4Address && !inetAddress.isLoopbackAddress()) {
                    return inetAddress.getHostAddress();
                }
            }
        }

        return null; // Return null if no valid IP address found
    }


    public static void getNetworkState() {
        Log.i(TAG,"getNetworkState dev");
        if (mCountDownUtil != null) {
            mCountDownUtil.cancel();
            mCountDownUtil = null;
        }
        mLastRx = -1;
        mLastTx = -1;
        mLastProcessRx = -1;
        mLastProcessTx = -1;
        mCountDownUtil = new CountDownUtil(PRINT_COUNT * PRINT_INTERVAL, PRINT_INTERVAL, new CountDownUtil.CountDownListener() {
            @Override
            public void onTick(long millisUntilFinished) {
                long rxBytes = TrafficStats.getUidRxBytes(android.os.Process.myUid());
                long txBytes = TrafficStats.getUidTxBytes(android.os.Process.myUid());
                long totalRxBytes = TrafficStats.getTotalRxBytes();   // 获取所有网络接收的字节数
                long totalTxBytes = TrafficStats.getTotalTxBytes();   // 获取所有网络发送的字节数
                if (mLastRx != -1 && mLastTx != -1) {
                    long deRx = totalRxBytes - mLastRx;
                    long deTx = totalTxBytes - mLastTx;
                    Log.i(TAG, "安卓系统下行字节数Rx = " + (deRx / PRINT_INTERVAL) + "kbs，上行字节数Tx = " + (deTx / PRINT_INTERVAL) + "kbs");
                }
                if (mLastProcessTx != -1 && mLastProcessRx != -1) {
                    if (rxBytes != TrafficStats.UNSUPPORTED && txBytes != TrafficStats.UNSUPPORTED) {
                        long peRxKBs = rxBytes - mLastProcessRx; // 转换为 KB/s
                        long peTxKBs = txBytes - mLastProcessTx; // 转换为 KB/s
                        Log.i(TAG, "所在进程下行字节数Rx = " + (peRxKBs / PRINT_INTERVAL) + "kbs，上行字节数Tx = " + (peTxKBs / PRINT_INTERVAL) + "kbs");
                    }
                }
                mLastRx = totalRxBytes;
                mLastTx = totalTxBytes;
                mLastProcessRx = rxBytes;
                mLastProcessTx = txBytes;
            }

            @Override
            public void onFinish() {

                long totalRxBytes = TrafficStats.getTotalRxBytes();   // 获取所有网络接收的字节数
                long totalTxBytes = TrafficStats.getTotalTxBytes();   // 获取所有网络发送的字节数
                long rxBytes = TrafficStats.getUidRxBytes(android.os.Process.myUid());
                long txBytes = TrafficStats.getUidTxBytes(android.os.Process.myUid());
                if (mLastRx != -1 && mLastTx != -1) {
                    long deRx = totalRxBytes - mLastRx;
                    long deTx = totalTxBytes - mLastTx;
                    Log.i(TAG, "下行字节数Rx = " + (deRx / PRINT_INTERVAL) + "kbs，上行字节数Tx = " + (deTx / PRINT_INTERVAL) + "kbs");
                }
                if (mLastProcessTx != -1 && mLastProcessRx != -1) {
                    if (rxBytes != TrafficStats.UNSUPPORTED && txBytes != TrafficStats.UNSUPPORTED) {
                        long peRxKBs = rxBytes - mLastProcessRx; // 转换为 KB/s
                        long peTxKBs = txBytes - mLastProcessTx; // 转换为 KB/s
                        Log.i(TAG, "所在进程下行字节数Rx = " + (peRxKBs / PRINT_INTERVAL) + "kbs，上行字节数Tx = " + (peTxKBs / PRINT_INTERVAL) + "kbs");
                    }
                }
                mLastRx = -1;
                mLastTx = -1;
                mLastProcessRx = -1;
                mLastProcessTx = -1;
                if (mCountDownUtil != null){
                    mCountDownUtil.cancel();
                }
            }
        });
        mCountDownUtil.start();
    }
}
