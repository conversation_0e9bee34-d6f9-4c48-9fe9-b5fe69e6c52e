package com.kaolafm.opensdk.api.bind;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.bind.model.BindDeviceResult;

import io.reactivex.Single;
import retrofit2.http.Headers;
import retrofit2.http.POST;


/**
 * <AUTHOR>
 **/
interface BindUserService {

    @Headers({ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER, ApiHostConstants.HTTPS_PROTOCOL_DOMAIN_HEADER})
    @POST(KaolaApiConstant.BIND_DEVICE)
    Single<BaseResult<BindDeviceResult>> bindDevice();
}
