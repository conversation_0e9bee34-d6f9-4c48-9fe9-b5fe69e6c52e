package com.kaolafm.opensdk.demo;

import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.view.View.OnClickListener;
import butterknife.ButterKnife;

/**
 * ViewHolder 基类，只需要实现setupData()就可以填充数据。
 *
 * <AUTHOR>
 * @date 2017/12/27
 */

public abstract class BaseHolder<T> extends RecyclerView.ViewHolder implements OnClickListener {

    private OnViewClickListener mViewClickListener;

    public BaseHolder(final View itemView) {
        super(itemView);
        ButterKnife.bind(this, itemView);
        itemView.setOnClickListener(this);
    }

    @Override
    public void onClick(final View v) {

        if ((!AntiShake.check(v.getId())) && mViewClickListener != null) {
            mViewClickListener.onViewClick(v, getAdapterPosition());
        }
    }

    /**
     * 释放holder中的资源
     */
    public void onRelease() {

    }

    void setOnViewHolderClickListener(OnViewClickListener listener) {
        mViewClickListener = listener;
    }

    interface OnViewClickListener {

        /**
         * 点击事件
         *
         * @param position 可能会返回-1，当改条目被remove后会返回-1.
         */
        void onViewClick(View view, int position);
    }

    /**
     * 填充数据
     */
    public abstract void setupData(T t, int position);
}
