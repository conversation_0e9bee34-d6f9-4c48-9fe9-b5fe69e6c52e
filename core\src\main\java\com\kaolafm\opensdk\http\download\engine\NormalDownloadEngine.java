package com.kaolafm.opensdk.http.download.engine;

import com.kaolafm.base.utils.FileUtil;
import com.kaolafm.opensdk.http.download.DownloadProgress;
import com.kaolafm.opensdk.http.download.DownloadRequest;
import com.kaolafm.opensdk.utils.HttpUtil;

import io.reactivex.Flowable;
import okhttp3.ResponseBody;
import okio.BufferedSink;
import okio.BufferedSource;
import okio.Okio;
import retrofit2.Response;

/**
 * <AUTHOR>
 * @date 2020-02-11
 */
public class NormalDownloadEngine extends BaseDownloadEngine {

    @Override
    public Flowable<DownloadProgress> download(DownloadRequest request, Response<ResponseBody> response) {
        if (response.body() == null) {
            throw new RuntimeException("ResponseBody is Null");
        }
        return super.download(request, response);
    }

    @Override
    protected void recreate() {
        FileUtil.recreate(shadow);
    }

    @Override
    protected void create() {
        recreate();
    }

    @Override
    Flowable<DownloadProgress> startDownload(DownloadRequest request, Response<ResponseBody> response) {
        final DownloadProgress progress = new DownloadProgress(HttpUtil.getContentLength(response), HttpUtil.isChunked(response));
        return Flowable.generate(() -> new Buffer(response.body().source(), Okio.buffer(Okio.sink(shadow))), (buffer, emitter) -> {
            try {
                long read = buffer.source.read(buffer.buffer, 8 * 1024L);
                if (read == -1L) {
                    buffer.sink.flush();
                    shadow.renameTo(file);
                    emitter.onComplete();
                } else {
                    buffer.sink.emit();
                    DownloadProgress value = progress.increaseSize(read);
                    emitter.onNext(value);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }, Buffer::close);
    }

    private class Buffer {
        private BufferedSource source;
        private BufferedSink sink;
        private okio.Buffer buffer;

        Buffer(BufferedSource source, BufferedSink sink) {
            this.source = source;
            this.sink = sink;
            this.buffer = sink != null ? sink.buffer() : null;
        }

        void close() {
            FileUtil.closeIO(sink, source);
        }
    }
}
