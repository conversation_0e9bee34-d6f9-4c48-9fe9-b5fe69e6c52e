<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    >
    <EditText
        android:id="@+id/et_link_account_id"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="请输入userId"
        />
    <EditText
        android:id="@+id/et_link_account_token"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="请输入userToken"
        app:layout_constraintTop_toBottomOf="@id/et_link_account_id"
        />
    <EditText
        android:id="@+id/et_link_account_time"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="请输入userToken有效时间"
        />

    <Button
        android:id="@+id/btn_link_account_commit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="提交"
        app:layout_constraintTop_toBottomOf="@id/et_link_account_token" />


</LinearLayout>