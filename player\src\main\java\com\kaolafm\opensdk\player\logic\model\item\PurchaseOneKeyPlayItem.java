package com.kaolafm.opensdk.player.logic.model.item;

import android.os.Parcel;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.player.logic.model.item.model.InfoData;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayUrlData;
import com.kaolafm.opensdk.player.logic.model.item.model.RadioInfoData;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

/**
 * @Package: com.kaolafm.opensdk.player.logic.model.item
 * @Description: 已购的一键-播放对象
 * @Author: Maclay
 * @Date: 2021/9/24 17:39
 */
public class PurchaseOneKeyPlayItem extends OneKeyPlayItem {

    @Override
    public int getType() {
        return PlayerConstants.RESOURCES_TYPE_PURCHASE_ONE_KEY_LISTENER_TYPE;
    }
}
