package com.kaolafm.opensdk.demo.live.ui;

import com.kaolafm.opensdk.account.token.AccessTokenManager;

public class UserInfoManager {

    private String mToken;
    private String userId;
    private String nickName;
    private String phone;
    private String avatar;
    private String gender;//性别
    private String userArea;//地区
    private boolean isKradio;

    private static final class UserInfoManagerHolder {
        private static final UserInfoManager INSTANCE = new UserInfoManager();
    }

    public static UserInfoManager getInstance() {
        return UserInfoManagerHolder.INSTANCE;
    }

    private UserInfoManager() {
    }

    public boolean isUserLogin() {
        return AccessTokenManager.getInstance().getKaolaAccessToken().isLogin();
    }

    public String getUserId() {
        return userId;
    }

    public void setLivingUidToken(String token) {
        mToken = token;
    }

    public String getLivingUidToken() {
        return mToken;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public boolean isUsePhone() {
        return isKradio;
    }

    public void setUsePhone(boolean kradio) {
        isKradio = kradio;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getUserArea() {
        return userArea;
    }

    public void setUserArea(String userArea) {
        this.userArea = userArea;
    }
}
