package com.kaolafm.opensdk.api.coin;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.http.core.HttpCallback;

/**
 * <AUTHOR>
 * <p>
 * 积分商城,入口二维码
 **/
public class ShoppingMallQrRequest extends BaseRequest {

    private final ShoppingMallQrService mService;

    public ShoppingMallQrRequest() {
        mService = obtainRetrofitService(ShoppingMallQrService.class);
    }

    public void getQrOfShoppingMall(int width, HttpCallback<String> callback) {
        doHttpDeal(mService.getQrOfShoppingMall(width), BaseResult::getResult, callback);
    }
}
