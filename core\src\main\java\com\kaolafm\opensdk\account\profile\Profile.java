package com.kaolafm.opensdk.account.profile;


/**
 * 属性基类
 *
 * <AUTHOR>
 * @date 2020/6/2
 */
public class Profile {

    private String appId;

    /**
     * 如果有多个，用英文逗号（,）隔开
     */
    private String capabilities;

    private String versionName;

    /**
     * 经度
     */
    private String lng;

    /**
     * 维度
     */
    private String lat;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 坐标类型
     */
    private String coordTpye;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getCapabilities() {
        return capabilities;
    }

    public void setCapabilities(String capabilities) {
        this.capabilities = capabilities;
    }

    public String getVersionName() {
        return versionName;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public String getLng() {
        return lng;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getCoordTpye() {
        return coordTpye;
    }

    public void setCoordTpye(String coordTpye) {
        this.coordTpye = coordTpye;
    }
}
