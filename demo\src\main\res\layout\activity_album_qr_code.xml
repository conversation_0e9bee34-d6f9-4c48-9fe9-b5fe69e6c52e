<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_kaola_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tv_album_id"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        android:text="请输入专辑id"/>

    <EditText
        android:id="@+id/et_album_id"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        app:layout_constraintTop_toBottomOf="@id/tv_album_id"
        android:text="1100002162528"/>

    <TextView
        android:id="@+id/tv_album_money"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/et_album_id"
        android:text="请输入专辑价格"/>

    <EditText
        android:id="@+id/et_album_money"
        android:layout_width="200dp"
        android:layout_height="50dp"
        app:layout_constraintTop_toBottomOf="@id/tv_album_money"
        android:text="100"/>

    <Button
        android:id="@+id/btn_album_qr_code_get"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="获取专辑支付二维码"
        app:layout_constraintTop_toBottomOf="@id/et_album_money" />

    <TextView
        android:id="@+id/info_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/btn_album_qr_code_get" />

    <ImageView
        android:id="@+id/iv_album_qr_code"
        android:layout_width="170dp"
        android:layout_height="170dp"
        android:contentDescription="@null"
        app:layout_constraintTop_toBottomOf="@id/info_view" />

    <Button
        android:id="@+id/btn_qr_code_check"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="查询支付结果"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/iv_album_qr_code" />

</androidx.constraintlayout.widget.ConstraintLayout>
