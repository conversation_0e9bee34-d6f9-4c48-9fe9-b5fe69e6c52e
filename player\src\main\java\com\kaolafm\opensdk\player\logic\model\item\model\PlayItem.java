package com.kaolafm.opensdk.player.logic.model.item.model;

import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.kaolafm.opensdk.api.media.model.PayMethod;
import com.kaolafm.opensdk.api.media.model.VideoPiece;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 单曲信息类
 */
public abstract class PlayItem implements Parcelable {
    /**
     * 单曲id
     */
    private long audioId;

    /**
     * 播放地址
     */
    private String playUrl;

    private String playUrlId;

    /**
     * 当前播放位置
     */
    private int position = 0;

    /**
     * 参考com.kaolafm.opensdk.api.media.model.AlbumDetails BUY_TYPE_开头的变量
     */
    private int buyType = 0;

    /**
     * 参考com.kaolafm.opensdk.api.media.model.AlbumDetails BUY_STATUS_开头的变量
     */
    private int buyStatus = 0;

    /**
     * 是否试听 1位是，0为否
     */
    private int audition;

    /**
     * 是否vip 1位是，0为否
     */
    private int vip;

    /**
     * 是否精品 1位是，0为否
     */
    private int fine;

    /**
     * 时长
     */
    private int duration;

    private Map mDataMap;

    /**
     * 专辑总播放量、广播/听电视的总播放量
     */
    private long listenCount;


    /**
     * 视频碎片
     */
    private VideoPiece videoPiece;

    /**
     * 是否可以订阅 只有为1的时候不能订阅
     */
    private int noSubscribe;

    /**
     * 支付方式
     */
    @SerializedName("payMethod")
    private List<PayMethod> payMethod;

    public List<PayMethod> getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(List<PayMethod> payMethod) {
        this.payMethod = payMethod;
    }

    public PlayItem() {
        this.mDataMap = new HashMap();
    }


    public void setAudioId(long audioId) {
        this.audioId = audioId;
    }

    public long getAudioId() {
        return audioId;
    }

    public void setPlayUrl(String playUrl) {
        this.playUrl = playUrl;
    }

    public String getPlayUrl() {
        return playUrl;
    }


    public void setPosition(int position) {
        this.position = position;
    }

    public int getPosition() {
        return position;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public int getDuration() {
        return duration;
    }

    public int getBuyType() {
        return buyType;
    }

    public void setBuyType(int buyType) {
        this.buyType = buyType;
    }

    public int getBuyStatus() {
        return buyStatus;
    }

    public void setBuyStatus(int buyStatus) {
        this.buyStatus = buyStatus;
    }

    public abstract String getRadioId();

    public abstract String getAlbumId();

    public abstract String getTitle();

    public abstract String getPicUrl();

    public abstract String getHost();

    public abstract String getAlbumTitle();

    public boolean isLiving() {
        return false;
    }

    public int getStatus() {
        return -1;
    }

    public int getRadioSubTagType() {
        return 0;
    }

    public int getIsThirdParty() {
        return 0;
    }

    public String getBeginTime() {
        return "";
    }

    public String getEndTime() {
        return "";
    }

    public abstract int getType();

    public String getUpdateTime() {
        return "";
    }

    public String getCallback() {
        return "";
    }

    public String getSource() {
        return "";
    }

    public String getSourceName() {
        return "";
    }

    public String getSourceLogo() {
        return "";
    }

    public String getRadioName() {
        return "";
    }

    public long getFinishTime() {
        return 0;
    }

    public long getStartTime() {
        return 0;
    }

    public int getAudition() {
        return audition;
    }

    public void setAudition(int audition) {
        this.audition = audition;
    }

    public int getVip() {
        return vip;
    }

    public void setVip(int vip) {
        this.vip = vip;
    }

    public int getFine() {
        return fine;
    }

    public void setFine(int fine) {
        this.fine = fine;
    }

    public String getPlayUrlId() {
        return playUrlId;
    }

    public void setPlayUrlId(String playUrlId) {
        this.playUrlId = playUrlId;
    }

    public long getListenCount() {
        return listenCount;
    }

    public void setListenCount(long listenCount) {
        this.listenCount = listenCount;
    }

    public VideoPiece getVideoPiece() {
        return videoPiece;
    }

    public void setVideoPiece(VideoPiece videoPiece) {
        this.videoPiece = videoPiece;
    }

    public boolean videoPieceValidate() {
        return videoPiece != null && videoPiece.getId() != 0 && !TextUtils.isEmpty(videoPiece.getPlayUrl());
    }

    public int getNoSubscribe() {
        return noSubscribe;
    }

    public void setNoSubscribe(int noSubscribe) {
        this.noSubscribe = noSubscribe;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public void addMapCacheData(String key, String value) {
        if (key == null || value == null) {
            return;
        }
        mDataMap.put(key, value);
    }

    public String getMapCacheData(String key) {
        if (key == null) {
            return null;
        }

        return (String) mDataMap.get(key);
    }

    public void removeMapCacheData(String key) {
        if (key == null) {
            return;
        }
        mDataMap.remove(key);
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(this.audioId);
        dest.writeString(this.playUrl);
        dest.writeInt(this.position);
        dest.writeInt(this.duration);
        dest.writeInt(this.buyStatus);
        dest.writeInt(this.buyType);
        dest.writeInt(this.vip);
        dest.writeInt(this.fine);
        dest.writeLong(this.listenCount);
        dest.writeParcelable(this.videoPiece, flags);
        dest.writeInt(this.noSubscribe);
    }

    protected PlayItem(Parcel in) {
        this.audioId = in.readLong();
        this.playUrl = in.readString();
        this.position = in.readInt();
        this.duration = in.readInt();
        this.buyStatus = in.readInt();
        this.buyType = in.readInt();
        this.vip = in.readInt();
        this.fine = in.readInt();
        this.listenCount = in.readLong();
        this.videoPiece = in.readParcelable(VideoPiece.class.getClassLoader());
        this.noSubscribe = in.readInt();
    }

    @Override
    public String toString() {
        return "PlayItem{" +
                "audioId=" + audioId +
                ", playUrl='" + playUrl + '\'' +
                ", playUrlId='" + playUrlId + '\'' +
                ", position=" + position +
                ", buyType=" + buyType +
                ", buyStatus=" + buyStatus +
                ", audition=" + audition +
                ", vip=" + vip +
                ", fine=" + fine +
                ", duration=" + duration +
                ", mDataMap=" + mDataMap +
                ", listenCount=" + listenCount +
                ", payMethod=" + payMethod +
                ", type=" + getType() +
                ", videoPiece=" + videoPiece +
                ", noSubscribe=" + noSubscribe +
                '}';
    }
}
