package com.kaolafm.opensdk.api.music.qq.model;

import com.google.gson.annotations.SerializedName;

/**
 * 微信、QQ用户信息
 *
 * <AUTHOR>
 * @date 2018/5/1
 */
public class TencentUserInfo {

    /**
     * city : Shenzhen
     * country : CN
     * headimgurl : xxxxxxxxxxxxxx
     * nickname : 刘晶
     * province : Guangdong
     * sex : 1
     */

    @SerializedName("city")
    private String city;

    @SerializedName("country")
    private String country;

    /**
     * gender : 男
     * head_img : http://q.qlogo.cn/qqapp/101448240/BDB28310A1D7960C10F0BF5911A0E12C/100
     * qzone_img : http://qzapp.qlogo.cn/qzapp/101448240/BDB28310A1D7960C10F0BF5911A0E12C/100
     * year : 1987
     */

    @SerializedName("gender")
    private String gender;

    @SerializedName(value = "head_img", alternate = {"headimgurl"})
    private String headImg;

    /**
     * is_user_info_lost : 0
     */

    /**
     * 表示用户信息是否丢失
     * 0：表示无
     * 1：表示有，业务方可使用功能三重新获取用户信息
     */
    @SerializedName("is_user_info_lost")
    private int isUserInfoLost;

    @SerializedName("nickname")
    private String nickname;

    @SerializedName("province")
    private String province;

    @SerializedName("qzone_img")
    private String qzoneImg;

    @SerializedName("sex")
    private int sex;

    @SerializedName("year")
    private String year;

    public String getCity() {
        return city;
    }

    public String getCountry() {
        return country;
    }

    public String getGender() {
        return gender;
    }

    public String getHeadImg() {
        return headImg;
    }

    public int getIsUserInfoLost() {
        return isUserInfoLost;
    }

    public String getNickname() {
        return nickname;
    }

    public String getProvince() {
        return province;
    }

    public String getQzoneImg() {
        return qzoneImg;
    }

    public int getSex() {
        return sex;
    }

    public String getYear() {
        return year;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public void setHeadImg(String headImg) {
        this.headImg = headImg;
    }


    public void setIsUserInfoLost(int isUserInfoLost) {
        this.isUserInfoLost = isUserInfoLost;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public void setQzoneImg(String qzoneImg) {
        this.qzoneImg = qzoneImg;
    }

    public void setSex(int sex) {
        this.sex = sex;
    }

    public void setYear(String year) {
        this.year = year;
    }

}
