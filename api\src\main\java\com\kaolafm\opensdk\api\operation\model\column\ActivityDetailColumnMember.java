package com.kaolafm.opensdk.api.operation.model.column;

/**
 * 栏目成员：活动
 */
public class ActivityDetailColumnMember extends ColumnContent {
    /**
     * 活动id
     */
    private String activityId;
    /**
     * 活动类型 0-常驻类型活动 1-携带按钮类型活动
     */
    private String activityType;
    /**
     * 活动样式
     * 1.绿色系
     * 2.蓝色系
     * 3.红色系
     * 4.黄色系
     * 5.粉色系
     * 6.紫色系
     */
    private String styleType;
    /**
     * 活动状态 0-下线 1-上线
     */
    private String activityStatus ;

    /**
     * 活动开始时间
     */
    private long activityStartTime;
    /**
     * 活动结束时间
     */
    private long activityEndTime;
    /**
     * 按钮文案
     */
    private String buttonContent;

    public String getButtonContent() {
        return buttonContent;
    }

    public void setButtonContent(String buttonContent) {
        this.buttonContent = buttonContent;
    }

    public long getActivityStartTime() {
        return activityStartTime;
    }

    public void setActivityStartTime(long activityStartTime) {
        this.activityStartTime = activityStartTime;
    }

    public long getActivityEndTime() {
        return activityEndTime;
    }

    public void setActivityEndTime(long activityEndTime) {
        this.activityEndTime = activityEndTime;
    }

    public String getActivityStatus() {
        return activityStatus;
    }

    public void setActivityStatus(String activityStatus) {
        this.activityStatus = activityStatus;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getActivityType() {
        return activityType;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType;
    }

    public String getStyleType() {
        return styleType;
    }

    public void setStyleType(String styleType) {
        this.styleType = styleType;
    }
}
