package com.kaolafm.opensdk.pollingrequest;

import android.app.AlarmManager;
import android.app.Application;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.SystemClock;
import android.util.Log;

import com.kaolafm.opensdk.di.component.ComponentKit;

import java.util.HashMap;
import java.util.Map;

/**
 * 轮询拉取客户端。
 */
public class PollingClient {

    private static final String TAG = "PollingClient";

    private static final String ACTION = "POLLING_REQUEST";
    private static final String EVENT_NAME = "EVENT_NAME";

    private AlarmManager mAlarmManager;
    private Application mContext;

    private Map<String, PollingRequestBean> mBeans;

    private PollingClient() {
    }

    private static final class PollingClientHolder {
        private static final PollingClient INSTANCE = new PollingClient();
    }

    public static PollingClient getInstance() {
        return PollingClientHolder.INSTANCE;
    }

    public void init() {
        mContext = ComponentKit.getInstance().getApplication();
        RequestReceiver receiver = new RequestReceiver();
        IntentFilter filter = new IntentFilter();
        filter.addAction(ACTION);
        mContext.registerReceiver(receiver, filter);
        mAlarmManager = (AlarmManager) mContext.getSystemService(Context.ALARM_SERVICE);
        mBeans = new HashMap<>();
    }

    public void addPollingBean(PollingRequestBean bean) {
        if (bean != null) {
            mBeans.put(bean.event, bean);
        }
    }

    public void start(String event) {
        PollingRequestBean request = mBeans.get(event);
        if (request != null) {
            start(getIntent(event), request.intervalTime);
            request(request);
        } else {
            Log.i(TAG, "event无效");
        }
    }

    private void start(PendingIntent intent, int time) {
        long triggerAtMillis = time + SystemClock.elapsedRealtime();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            mAlarmManager.setExactAndAllowWhileIdle(AlarmManager.ELAPSED_REALTIME, triggerAtMillis, intent);
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            mAlarmManager.setExact(AlarmManager.ELAPSED_REALTIME, triggerAtMillis, intent);
        } else {
            mAlarmManager.set(AlarmManager.ELAPSED_REALTIME, triggerAtMillis, intent);
        }
    }

    private PendingIntent getIntent(String event) {
        Intent intent = new Intent(ACTION);
        intent.putExtra(EVENT_NAME, event);
        return PendingIntent.getBroadcast(mContext, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT);
    }

    private void request(PollingRequestBean bean) {
        if (bean != null) {
            bean.request.invoke(bean.listener);
        }
    }

    public void releaseAll() {
        mBeans.clear();
    }

    public void releaseOne(String event) {
        mBeans.remove(event);
    }

    private class RequestReceiver extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent != null) {
                String event = intent.getStringExtra(EVENT_NAME);
                PollingRequestBean bean = mBeans.get(event);
                if (bean != null) {
                    start(getIntent(event), bean.intervalTime);
                    request(bean);
                }
            }
        }
    }
}
