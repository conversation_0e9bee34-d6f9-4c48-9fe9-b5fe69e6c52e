<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:fitsSystemWindows="true"
    android:id="@+id/item_root"
    android:layout_height="wrap_content"
    >

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/activity_item_guideline"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"/>

    <ImageView
        android:id="@+id/qrCode_image"
        android:layout_width="160px"
        android:layout_height="160px"
        android:layout_marginEnd="180px"
        android:padding="6px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/activity_item_guideline"
        app:layout_constraintTop_toTopOf="parent"
        android:background="#ffffff"
        tools:ignore="MissingConstraints" />
    <ImageView
        android:id="@+id/act_image"
        android:layout_width="160px"
        android:layout_height="160px"
        android:layout_marginEnd="20px"
        android:padding="6px"
        app:layout_constraintLeft_toRightOf="@+id/qrCode_image"
        app:layout_constraintRight_toLeftOf="@+id/activity_item_guideline"
        app:layout_constraintTop_toTopOf="parent"
        android:background="#000"
        tools:ignore="MissingConstraints" />
    <TextView
        android:id="@+id/qrCode_textView"
        android:layout_width="200px"
        android:layout_height="wrap_content"
        android:layout_marginTop="10px"
        android:paddingLeft="5px"
        android:paddingRight="5px"
        android:gravity="center_horizontal"
        android:lineSpacingMultiplier="1.25"
        android:maxLines="2"
        android:textSize="24px"
        android:textColor="@color/text_color_2"
        app:layout_constraintTop_toBottomOf="@+id/qrCode_image"
        app:layout_constraintLeft_toLeftOf="@+id/qrCode_image"
        app:layout_constraintRight_toRightOf="@+id/qrCode_image"
        tools:text="扫码扫码扫码扫码扫码扫码"
        tools:ignore="MissingConstraints" />
    <TextView
        android:id="@+id/qrCode_textView2"
        android:layout_width="200px"
        android:layout_height="wrap_content"
        android:layout_marginTop="10px"
        android:paddingLeft="5px"
        android:paddingRight="5px"
        android:gravity="center_horizontal"
        android:lineSpacingMultiplier="1.25"
        android:maxLines="2"
        android:textSize="24px"
        android:textColor="@color/text_color_2"
        app:layout_constraintTop_toBottomOf="@+id/act_image"
        app:layout_constraintLeft_toLeftOf="@+id/act_image"
        app:layout_constraintRight_toRightOf="@+id/act_image"
        tools:text="音频"
        tools:ignore="MissingConstraints" />
    <TextView
        android:id="@+id/title_activity"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingStart="30px"
        android:paddingEnd="30px"
        android:paddingTop="20px"
        android:paddingBottom="20px"
        android:textSize="26px"
        android:maxLines="2"
        android:layout_marginEnd="6px"
        android:lineSpacingMultiplier="1.20"
        android:textColor="@color/text_color_1"
        android:gravity="center_vertical"
        android:background="#D6D6D6"
        app:layout_constraintLeft_toRightOf="@+id/activity_item_guideline"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="test"
        />
    <TextView
        android:id="@+id/des_activity"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:paddingStart="30px"
        android:paddingEnd="30px"
        android:paddingTop="30px"
        android:paddingBottom="30px"
        android:lineSpacingMultiplier="1.25"
        android:layout_marginEnd="6px"
        android:textColor="@color/text_color_3"
        app:layout_constraintLeft_toLeftOf="@+id/title_activity"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title_activity"
        android:textSize="26px"
        android:background="#FBFBFB"
        tools:text="新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动"
        tools:ignore="MissingConstraints" />

    <View
        android:id="@+id/qr_view_expire"
        android:layout_width="160px"
        android:layout_height="160px"
        android:background="#B3000000"
        app:layout_constraintLeft_toLeftOf="@+id/qrCode_image"
        app:layout_constraintRight_toRightOf="@+id/qrCode_image"
        app:layout_constraintTop_toTopOf="@+id/qrCode_image"
        app:layout_constraintBottom_toBottomOf="@+id/qrCode_image"
        android:visibility="gone"
        tools:visibility="visible"
        tools:ignore="MissingConstraints" />
    <ImageView
        android:id="@+id/qr_expire_icon"
        android:layout_width="108px"
        android:layout_height="70px"
        android:src="@drawable/qr_expire_icon"
        app:layout_constraintTop_toTopOf="@+id/qr_view_expire"
        app:layout_constraintBottom_toBottomOf="@+id/qr_view_expire"
        app:layout_constraintLeft_toLeftOf="@+id/qr_view_expire"
        app:layout_constraintRight_toRightOf="@+id/qr_view_expire"
        tools:ignore="MissingConstraints" />
</androidx.constraintlayout.widget.ConstraintLayout>