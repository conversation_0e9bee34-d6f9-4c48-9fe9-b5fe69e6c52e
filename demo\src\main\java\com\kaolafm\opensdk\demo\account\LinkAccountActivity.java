package com.kaolafm.opensdk.demo.account;

import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Button;
import android.widget.EditText;
import butterknife.BindView;
import butterknife.OnClick;
import com.kaolafm.opensdk.api.login.LoginRequest;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

/**
 * <AUTHOR>
 * @date 2018/10/30
 */

public class LinkAccountActivity extends BaseActivity {

    @BindView(R.id.btn_link_account_commit)
    Button mBtnLinkAccountCommit;

    @BindView(R.id.et_link_account_id)
    EditText mEtLinkAccountId;

    @BindView(R.id.et_link_account_time)
    EditText mEtLinkAccountTime;

    @BindView(R.id.et_link_account_token)
    EditText mEtLinkAccountToken;

    @Override
    public int getLayoutId() {
        return R.layout.activity_link_account;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        setTitle("账号打通");
    }

    @Override
    public void initData() {

    }

    @OnClick(R.id.btn_link_account_commit)
    public void onViewClicked() {
        String userId = mEtLinkAccountId.getText().toString().trim();
        if (TextUtils.isEmpty(userId)) {
            showToast("请输入UserId");
            return;
        }
        String token = mEtLinkAccountToken.getText().toString().trim();
        if (TextUtils.isEmpty(token)) {
            showToast("请输入UserToken");
            return;
        }
        String timeStr = mEtLinkAccountTime.getText().toString().trim();
        long time = 0L;
        if (!TextUtils.isEmpty(timeStr)) {
            time = Long.parseLong(timeStr);
        }
        new LoginRequest().linkAccount(userId, token, time,
                new HttpCallback<Boolean>() {
                    @Override
                    public void onSuccess(Boolean aBoolean) {
                        Log.e("LinkAccountActivity", "onSuccess: "+aBoolean);
                    }

                    @Override
                    public void onError(ApiException exception) {
                        Log.e("LinkAccountActivity", "onError: "+exception);
                    }
                });
    }
}
