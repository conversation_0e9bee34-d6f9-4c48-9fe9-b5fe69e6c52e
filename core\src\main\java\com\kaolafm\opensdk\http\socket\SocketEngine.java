package com.kaolafm.opensdk.http.socket;

import android.text.TextUtils;

import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.opensdk.http.error.SocketEngineIOException;
import com.kaolafm.opensdk.http.socket.parser.ParseQS;
import com.kaolafm.opensdk.http.socket.parser.Parser;

import org.json.JSONException;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

import dagger.Lazy;
import okhttp3.Call;
import okhttp3.OkHttpClient;

/**
 * socket长连接引擎。核心实现类。
 *
 * <AUTHOR> <PERSON>
 * @date 2020-01-07
 */
public class SocketEngine extends Emitter {


    /**
     * 刷新流
     */
    public static final String EVENT_FLUSH = "flush";
    /**
     * 心跳
     */
    public static final String EVENT_HEARTBEAT = "heartbeat";
    /**
     * 数据
     */
    public static final String EVENT_DATA = "data";
    /**
     * 握手
     */
    public static final String EVENT_HANDSHAKE = "handshake";

    public static final String EVENT_PACKET_CREATE = "packetCreate";

    public static final String EVENT_MESSAGE = "message";
    private static boolean priorWebSocketSuccess = false;

    /**
     * 是否安全的请求，true表示使用https或wss。
     */
    private boolean secure;
    /**
     * host地址。默认localhost。
     */
    private String hostname;
    /**
     * 端口
     */
    private int port;
    /**
     * 参数
     */
    private Map<String, String> query;
    private String path;
    /**
     * 时间戳参数
     */
    private String timestampParam;
    /**
     * 参数中是否添加时间戳
     */
    private boolean timestampRequests;
    /**
     * 通信协议集合，可以用来兼容多种协议
     */
    private List<String> transports;
    /**
     * 通信协议配置集合
     */
    private Map<String, Transport.Options> transportOptions;

    private int policyPort;
    private boolean rememberUpgrade;
    private Call.Factory callFactory;
    private okhttp3.WebSocket.Factory webSocketFactory;

    @AppScope
    @Inject
    Lazy<OkHttpClient> okHttpClientLazy;

    /**
     * 长连接是否已经连接， true表示已连接
     */
    private boolean connected = false;

    /**
     * 当前读取状态
     */
    private ReadyState readyState;

    /**
     * 是否跳过重连。主动关闭后不再重连，置为true
     */
    private boolean skipReconnect = false;

    private String id;
    /**
     * 当前通信协议
     */
    private Transport transport;
    /**
     * 上一次的buffer长度
     */
    private int prevBufferLen;
    private LinkedList<Packet> writeBuffer = new LinkedList<>();
    private Listener onHeartbeatAsListener = args -> onHeartbeat(args.length > 0 ? (Long) args[0] : 0);
    /**
     * 心跳时间间隔
     */
    private long pingInterval;
    /**
     * 用于支持多种协议的，目前只有WebSocket协议，所以没有用
     */
    private List<String> upgrades;
    /**
     * 心跳超时时间
     */
    private long pingTimeout;
    private Future pingIntervalTimer;
    private ScheduledExecutorService heartbeatScheduler;
    private ScheduledFuture<?> pingTimeoutTimer;
    private Map<String, String> headers;

    @Inject
    public SocketEngine() {

    }

    public void options(String url) {
        try {
            options(TextUtils.isEmpty(url) ? null : new URI(url), null);
        } catch (URISyntaxException e) {
            e.printStackTrace();
        }
    }

    public void options(URI uri, Options opts) {
        if (opts == null) {
            opts = new Options();
        }
        if (uri != null) {
            opts.deployFromUri(uri);
        }
        if (opts.host != null) {
            String hostname = opts.host;
            boolean ipv6 = hostname.split(":").length > 2;
            if (ipv6) {
                int start = hostname.indexOf('[');
                if (start != -1) {
                    hostname = hostname.substring(start + 1);
                }
                int end = hostname.lastIndexOf(']');
                if (end != -1) {
                    hostname = hostname.substring(0, end);
                }
            }
            opts.hostname = hostname;
        }

        this.secure = opts.secure;

        if (opts.port == -1) {
            // if no port is specified manually, use the protocol default
            opts.port = this.secure ? 443 : 80;
        }

        this.hostname = opts.hostname != null ? opts.hostname : "localhost";
        this.port = opts.port;
        this.query = opts.query != null ? ParseQS.decode(opts.query) : new HashMap<>();
        this.path = (opts.path != null ? opts.path : "/engine.io").replaceAll("/$", "") + "/";
        this.timestampParam = opts.timestampParam != null ? opts.timestampParam : "t";
        this.timestampRequests = opts.timestampRequests;
        this.transports = new ArrayList<String>() {{
            add(WebSocket.NAME);
        }};
        this.transportOptions = opts.transportOptions != null ? opts.transportOptions : new HashMap<>();
        this.policyPort = opts.policyPort != 0 ? opts.policyPort : 843;
        this.rememberUpgrade = opts.rememberUpgrade;
        this.callFactory = opts.callFactory != null ? opts.callFactory : okHttpClientLazy.get();
        this.webSocketFactory = opts.webSocketFactory != null ? opts.webSocketFactory : okHttpClientLazy.get();
        this.headers = opts.headers;
    }

    public void open() {
        EventThread.exec(() -> {
            String transportName;
            if (rememberUpgrade && SocketEngine.priorWebSocketSuccess && transports.contains(WebSocket.NAME)) {
                transportName = WebSocket.NAME;
            } else if (0 == transports.size()) {
                // Emit error on next tick so it can be listened to
                EventThread.nextTick(() -> emit(SocketEvent.EVENT_ERROR, new SocketEngineIOException("没有添加通信协议")));
                return;
            } else {
                transportName = transports.get(0);
            }
            readyState = ReadyState.OPENING;
            Transport transport = createTransport(transportName);
            setTransport(transport);
            transport.open();
        });
    }

    /**
     * 根据名称创建对应的通信协议
     *
     * @param name
     * @return
     */
    private Transport createTransport(String name) {
        Map<String, String> query = new HashMap<>(this.query);
        query.put("EIO", String.valueOf(Parser.PROTOCOL));
        query.put("transport", name);
        if (this.id != null) {
            query.put("sid", this.id);
        }

        // per-transport options
        Transport.Options options = this.transportOptions.get(name);

        Transport.Options opts = new Transport.Options();
        opts.query = query;

        opts.hostname = options != null ? options.hostname : this.hostname;
        opts.port = options != null ? options.port : this.port;
        opts.secure = options != null ? options.secure : this.secure;
        opts.path = options != null ? options.path : this.path;
        opts.timestampRequests = options != null ? options.timestampRequests : this.timestampRequests;
        opts.timestampParam = options != null ? options.timestampParam : this.timestampParam;
        opts.policyPort = options != null ? options.policyPort : this.policyPort;
        opts.callFactory = options != null ? options.callFactory : this.callFactory;
        opts.webSocketFactory = options != null ? options.webSocketFactory : this.webSocketFactory;
        opts.headers = options != null? options.headers : this.headers;

        Transport transport;
        if (WebSocket.NAME.equals(name)) {
            transport = new WebSocket(opts);
        } else {
            throw new RuntimeException();
        }

        this.emit(SocketEvent.EVENT_TRANSPORT, transport);

        return transport;
    }

    private void setTransport(Transport transport) {

        if (this.transport != null) {
            this.transport.off();
        }

        this.transport = transport;

        transport.on(SocketEvent.EVENT_DRAIN, args -> onDrain())
                .on(SocketEvent.EVENT_PACKET, args -> onPacket(args.length > 0 ? (Packet) args[0] : null))
                .on(SocketEvent.EVENT_ERROR, args -> onError(args.length > 0 ? (Exception) args[0] : null))
                .on(SocketEvent.EVENT_CLOSE, args -> onClose("transport close"));
    }

    private void onDrain() {
        for (int i = 0; i < this.prevBufferLen; i++) {
            this.writeBuffer.poll();
        }

        this.prevBufferLen = 0;
        if (0 == this.writeBuffer.size()) {
            this.emit(SocketEvent.EVENT_DRAIN);
        } else {
            this.flush();
        }
    }

    private void flush() {
        if (this.readyState != ReadyState.CLOSED && this.transport.writable && this.writeBuffer.size() != 0) {
            this.prevBufferLen = this.writeBuffer.size();
            this.transport.send(this.writeBuffer.toArray(new Packet[this.writeBuffer.size()]));
            this.emit(EVENT_FLUSH);
        }
    }

    private void onPacket(Packet packet) {
        Logging.d("packet(type=%s) received with socket readyState '%s'", packet.type, readyState);
        if (this.readyState == ReadyState.OPENING ||
                this.readyState == ReadyState.OPEN ||
                this.readyState == ReadyState.CLOSING) {

            this.emit(SocketEvent.EVENT_PACKET, packet);
            this.emit(EVENT_HEARTBEAT);

            switch (packet.type) {
                case Packet.OPEN:
                    try {
                        this.onHandshake(new HandshakeData((String) packet.data));
                    } catch (JSONException e) {
                        this.emit(SocketEvent.EVENT_ERROR, new SocketEngineIOException(e));
                    }
                    break;
                case Packet.PONG:
                    this.setPing();
                    this.emit(SocketEvent.EVENT_PONG);
                    break;
                case Packet.ERROR:
                    SocketEngineIOException err = new SocketEngineIOException("server error");
                    err.code = packet.data;
                    this.onError(err);
                    break;
                case Packet.MESSAGE:
                    this.emit(EVENT_DATA, packet.data);
                    this.emit(EVENT_MESSAGE, packet.data);
                    break;
                default:
                    Logging.d("packet received with socket type %s", packet.type);
            }
        }
    }

    private void onHandshake(HandshakeData data) {
        this.emit(EVENT_HANDSHAKE, data);
        this.id = data.sid;
        this.transport.query.put("sid", data.sid);
        this.upgrades = this.filterUpgrades(Arrays.asList(data.upgrades));
        this.pingInterval = data.pingInterval;
        this.pingTimeout = data.pingTimeout;
        this.onOpen();
        // In case open handler closes socket
        if (ReadyState.CLOSED == this.readyState) {
            return;
        }
        this.setPing();

        this.off(EVENT_HEARTBEAT, this.onHeartbeatAsListener);
        this.on(EVENT_HEARTBEAT, this.onHeartbeatAsListener);
    }

    private void onOpen() {
        this.readyState = ReadyState.OPEN;
        SocketEngine.priorWebSocketSuccess = WebSocket.NAME.equals(this.transport.name);
        this.emit(SocketEvent.EVENT_OPEN);
        this.flush();
    }

    private List<String> filterUpgrades(List<String> upgrades) {
        List<String> filteredUpgrades = new ArrayList<>();
        for (String upgrade : upgrades) {
            if (this.transports.contains(upgrade)) {
                filteredUpgrades.add(upgrade);
            }
        }
        return filteredUpgrades;
    }

    private void onHeartbeat(long timeout) {
        if (this.pingTimeoutTimer != null) {
            pingTimeoutTimer.cancel(false);
        }

        if (timeout <= 0) {
            timeout = this.pingInterval + this.pingTimeout;
        }

        this.pingTimeoutTimer = this.getHeartbeatScheduler().schedule(() -> {
            if (readyState == ReadyState.CLOSED) {
                return;
            }
            onClose("ping timeout");
        }, timeout, TimeUnit.MILLISECONDS);
    }

    private void setPing() {
        if (this.pingIntervalTimer != null) {
            pingIntervalTimer.cancel(false);
        }
        this.pingIntervalTimer = this.getHeartbeatScheduler().schedule(() -> {
            Logging.d("writing ping packet - expecting pong within %sms", SocketEngine.this.pingTimeout);
            ping();
            onHeartbeat(SocketEngine.this.pingTimeout);
        }, this.pingInterval, TimeUnit.MILLISECONDS);
    }

    private void ping() {
        sendPacket(new Packet(Packet.PING), args -> SocketEngine.this.emit(SocketEvent.EVENT_PING));
    }

    /**
     * 发送数据包
     *
     * @param packet 数据包
     * @param fn
     */
    private void sendPacket(Packet packet, final Listener fn) {
        if (ReadyState.CLOSING == this.readyState || ReadyState.CLOSED == this.readyState) {
            return;
        }
        this.emit(EVENT_PACKET_CREATE, packet);
        this.writeBuffer.offer(packet);
        if (fn != null) {
            this.once(EVENT_FLUSH, fn);
        }
        this.flush();
    }

    private ScheduledExecutorService getHeartbeatScheduler() {
        if (this.heartbeatScheduler == null || this.heartbeatScheduler.isShutdown()) {
            this.heartbeatScheduler = new ScheduledThreadPoolExecutor(1);
        }
        return this.heartbeatScheduler;
    }

    private void onError(Exception err) {
        SocketEngine.priorWebSocketSuccess = false;
        this.emit(SocketEvent.EVENT_ERROR, err);
        this.onClose("transport error", err);
    }

    /**
     * 关闭长连接
     */
    public void close() {
        EventThread.exec(() -> {
            if (readyState == ReadyState.OPENING || readyState == ReadyState.OPEN) {
                readyState = ReadyState.CLOSING;

                final SocketEngine self = SocketEngine.this;

                final Runnable close = () -> {
                    self.onClose("forced close");
                    Logging.d("socket closing - telling transport to close");
                    self.transport.close();
                };

                if (writeBuffer.size() > 0) {
                    once(SocketEvent.EVENT_DRAIN, args -> close.run());
                } else {
                    close.run();
                }
            }
        });
    }

    private void onClose(String reason) {
        this.onClose(reason, null);
    }

    private void onClose(String reason, Exception desc) {
        if (ReadyState.OPENING == this.readyState || ReadyState.OPEN == this.readyState || ReadyState.CLOSING == this.readyState) {
            Logging.d("socket close with reason: %s, exception=%s——because %s", reason, desc, desc!= null ? desc.getCause():"");

            // clear timers
            if (this.pingIntervalTimer != null) {
                this.pingIntervalTimer.cancel(false);
            }
            if (this.pingTimeoutTimer != null) {
                this.pingTimeoutTimer.cancel(false);
            }
            if (this.heartbeatScheduler != null) {
                this.heartbeatScheduler.shutdownNow();
            }

            // stop event from firing again for transport
            this.transport.off(SocketEvent.EVENT_CLOSE);

            // ensure transport won't stay open
            this.transport.close();

            // ignore further transport communication
            this.transport.off();

            // set ready state
            this.readyState = ReadyState.CLOSED;

            // clear session id
            this.id = null;

            // emit close events
            this.emit(SocketEvent.EVENT_CLOSE, reason, desc);

            // clear buffers after, so users can still
            // grab the buffers on `close` event
            writeBuffer.clear();
            prevBufferLen = 0;
        }
    }

    public void write(String msg) {
        write(msg, null);
    }

    public void write(String msg, Listener listener) {
        send(msg, listener);
    }

    public void write(byte[] msg) {
        write(msg, null);
    }

    public void write(byte[] msg, Listener listener) {
        send(msg, listener);
    }

    private void send(String msg, Listener listener) {
        EventThread.exec(() -> sendPacket(new Packet<>(Packet.MESSAGE, msg), listener));
    }

    private void send(byte[] msg, Listener listener) {
        EventThread.exec(() -> sendPacket(new Packet<>(Packet.MESSAGE, msg), listener));
    }

    public static class Options extends Transport.Options {
        /**
         * List of transport names.
         */
//        public String[] transports;
//
        public boolean rememberUpgrade;
        public String host;
        public String query;
        public Map<String, Transport.Options> transportOptions;

        /**
         * 通过解析URI配置属性
         *
         * @param uri
         */
        private void deployFromUri(URI uri) {
            this.host = uri.getHost();
            this.secure = "https".equals(uri.getScheme()) || "wss".equals(uri.getScheme());
            this.port = uri.getPort();

            String query = uri.getRawQuery();
            if (query != null) {
                this.query = query;
            }
        }
    }
}
