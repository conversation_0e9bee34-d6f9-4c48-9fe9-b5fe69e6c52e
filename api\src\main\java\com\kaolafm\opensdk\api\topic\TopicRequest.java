package com.kaolafm.opensdk.api.topic;

import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.topic.model.OperationResponse;
import com.kaolafm.opensdk.api.topic.model.TopicDetail;
import com.kaolafm.opensdk.api.topic.model.TopicPosts;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import okhttp3.MediaType;
import okhttp3.RequestBody;

/**
 * 话题相关
 *
 * <AUTHOR>
 * @date 2019-03-28
 */
public class TopicRequest extends BaseRequest {
    /**
     * 按时间排序
     */
    public static final int ORDER_WAY_BY_TIME = 0;
    /**
     * 按热度排序
     */
    public static final int ORDER_WAY_BY_POPULARITY = 1;
    /**
     * 点赞
     */
    public static final int POSTS_OPERATE_LIKE = 0;
    /**
     * 取消点赞
     */
    public static final int POSTS_OPERATE_UNLIKE = 1;

    private final TopicService mTopicService;

    public TopicRequest() {
        mTopicService = obtainRetrofitService(TopicService.class);
    }

    /**
     * 获取话题详情
     */
    public void getTopicDetail(long topicId, HttpCallback<TopicDetail> callback) {
        doHttpDeal(mTopicService.getTopicDetail(topicId), BaseResult::getResult, callback);
    }

    /**
     * 获取话题下帖子列表
     *
     * @param orderWay 排序方式，{@link TopicRequest#ORDER_WAY_BY_TIME}:时间；{@link TopicRequest#ORDER_WAY_BY_POPULARITY}：热度
     */
    public void getTopicPostsList(long topicId, int orderWay, int pageNum, int pageSize, HttpCallback<BasePageResult<List<TopicPosts>>> callback) {
        doHttpDeal(mTopicService.getTopicPostsList(topicId, orderWay, pageNum, pageSize), BaseResult::getResult, callback);
    }

    /**
     * 点赞、取消点赞帖子
     *
     * @param postsId
     * @param type     0:点赞  1:取消点赞
     * @param callback
     */
    public void operatePosts(long postsId, int type, HttpCallback<OperationResponse> callback) {
        doHttpDeal(mTopicService.operatePosts(postsId, type), BaseResult::getResult, callback);
    }

    /**
     * 发帖
     *
     * @param topicId      话题id
     * @param postsContent 帖子内容
     * @param callback
     */
    public void publishPosts(long topicId, String postsContent, HttpCallback<OperationResponse> callback) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("topicId", topicId);
        params.put("content", postsContent);
        String body = mGsonLazy.get().toJson(params);
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);
        doHttpDeal(mTopicService.publishPosts(requestBody), BaseResult::getResult, callback);
    }
}
