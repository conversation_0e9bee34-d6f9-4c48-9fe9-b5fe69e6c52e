package com.kaolafm.opensdk.player.logic.playlist;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.api.purchase.PurchaseRequest;
import com.kaolafm.opensdk.api.subscribe.SubscribeRequest;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.item.InvalidPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.OneKeyPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.PurchaseOneKeyPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playlist.util.PlayListUtils;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerPreconditions;

import java.util.ArrayList;
import java.util.List;

/**
 * @Package: com.kaolafm.opensdk.player.logic.playlist
 * @Description: 已购的一键收听
 * @Author: Maclay
 * @Date: 2021/9/10 10:36
 */
public class PurchaseOneKeyListenPlayListControl extends BasePlayListControl {
    private int LOAD_INIT_PAGE =1;
    private int LOAD_NEXT_PAGE =2;
    private int LOAD_PRE_PAGE  =3;
    /**
     * 是否有下一页
     */
    boolean hasNext = false;
    /**
     * 下一页的请求参数 clockId
     */
    String nextClockId = "";

    @Override
    public void initPlayList(PlayerBuilder playerBuilder, IPlayListGetListener iPlayListGetListener) {
        super.initPlayList(playerBuilder, iPlayListGetListener);
        mPlaylistInfo.setId("");
        loadData(LOAD_INIT_PAGE,"", iPlayListGetListener);
    }

    @Override
    public void loadPrePage(IPlayListGetListener iPlayListGetListener) {
        loadData(LOAD_PRE_PAGE,"", iPlayListGetListener);
    }

    @Override
    public void loadNextPage(IPlayListGetListener iPlayListGetListener) {
        loadData(LOAD_NEXT_PAGE,nextClockId, iPlayListGetListener);
    }

    private void loadData(int type, String clockId, final IPlayListGetListener iPlayListGetListener) {
        if (!StringUtil.isEmpty(mPlaylistInfo.getId())) {
            try {
                clockId = mPlaylistInfo.getId();
            } catch (Exception e) {

            }
        }
        int errorCode1 = -1;
        int errorCode2 = -1;
        if(type == LOAD_INIT_PAGE){
            errorCode1 = PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_PAGE_SERVER;
            errorCode2 = PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_PAGE_NULL;
        }else if(type == LOAD_NEXT_PAGE){
            errorCode1 = PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_PAGE_SERVER;
            errorCode2 = PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_PAGE_NULL;
        }else if(type == LOAD_PRE_PAGE){
            errorCode1 = PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_PAGE_SERVER;
            errorCode2 = PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_PAGE_NULL;
        }
        int finalErrorCode1 = errorCode1;
        int finalErrorCode2 = errorCode2;
        PurchaseOneKeyPlayItem purchaseOneKeyPlayItem = new PurchaseOneKeyPlayItem();
        purchaseOneKeyPlayItem.getRadioInfoData().setClockId(clockId);

        new PurchaseRequest().getPlayPurchasedList(clockId, new HttpCallback<List<AudioDetails>>() {
            @Override
            public void onSuccess(List<AudioDetails> audioDetails) {
                if (audioDetails != null && audioDetails.size() > 0) {
                    if (PlayerPreconditions.checkNull(audioDetails)) {
                        notifyPlayListGetError(iPlayListGetListener,purchaseOneKeyPlayItem, finalErrorCode2, -1);
                        notifyPlayListChangeError(purchaseOneKeyPlayItem, finalErrorCode2, -1);
                        return;
                    }
                    hasNext = false;
                    nextClockId = audioDetails.get(audioDetails.size() - 1).getClockId();
                    updateListInfo(audioDetails);
                    ArrayList<PlayItem> playItemArrayList = PlayListUtils.audioDetailToPurchaseOneKeyPlayItem(audioDetails, mPlaylistInfo);
                    if (ListUtil.isEmpty(playItemArrayList)) {
                        notifyPlayListGetError(iPlayListGetListener,purchaseOneKeyPlayItem, finalErrorCode2, -1);
                        notifyPlayListChangeError(purchaseOneKeyPlayItem, finalErrorCode2, -1);
                        return;
                    }

                    mPlayItemArrayList.addAll(playItemArrayList);
                    notifyPlayListChange(playItemArrayList);
                    notifyPlayListGet(iPlayListGetListener, playItemArrayList.get(0), playItemArrayList);
                }
            }

            @Override
            public void onError(ApiException e) {
                notifyPlayListGetError(iPlayListGetListener,purchaseOneKeyPlayItem, finalErrorCode1, e.getCode());
                notifyPlayListChangeError(purchaseOneKeyPlayItem, finalErrorCode1, e.getCode());
            }
        });
//        new SubscribeRequest().getUserFollowRadio(clockId + "", new HttpCallback<List<AudioDetails>>() {
//            @Override
//            public void onSuccess(List<AudioDetails> audioDetails) {
//                if (PlayerPreconditions.checkNull(audioDetails)) {
//                    notifyPlayListChangeError(-1);
//                    return;
//                }
//                updateListInfo(audioDetails);
//                ArrayList<PlayItem> playItemArrayList = PlayListUtils.audioDetailToOneKeyPlayItem(audioDetails, mPlaylistInfo);
//                if (ListUtil.isEmpty(playItemArrayList)) {
//                    notifyPlayListChangeError(-1);
//                    return;
//                }
//
//                mPlayItemArrayList.addAll(playItemArrayList);
//                notifyPlayListChange(playItemArrayList);
//                notifyPlayListGet(iPlayListGetListener, playItemArrayList.get(0), playItemArrayList);
//            }
//
//            @Override
//            public void onError(ApiException e) {
//                notifyPlayListChangeError(e.getCode());
//                notifyPlayListGetError(iPlayListGetListener, e.getCode());
//            }
//        });
    }

    private void updateListInfo(List<AudioDetails> audioDetailsList) {
        if (ListUtil.isEmpty(audioDetailsList)) {
            return;
        }
        int size = audioDetailsList.size();
        AudioDetails audioDetails = audioDetailsList.get(size - 1);

        if (PlayerPreconditions.checkNull(audioDetails)) {
            return;
        }
        mPlaylistInfo.setId(audioDetails.getClockId());
    }

    @Override
    public boolean hasNextPage() {
        return hasNext;
    }

}
