package com.kaolafm.opensdk.demo.live.chat;

public enum RecorderStatus {
    /**
     * 录音未开始，或者取消后，或者发送成功后的状态，下一个状态是RECORDING
     */
    IDLE,
    /**
     * 录音被取消，例如时间小于1秒
     */
    CANCEL,
    /**
     * 正在录音的状态，点下录音按钮以后的状态，下一个状态是FINISH
     */
    RECORDING,
    /**
     * 录制完成状态，点下结束录音按钮或者录制到达录制时长后的状态，下一个状态是PLAYING，或者IDLE
     */
    FINISHED,
    /**
     * 试听状态，可以通过播放按钮暂停，变成PAUSED,或者IDLE
     */
    LISTENING,
    /**
     * 试听完成了
     */
    LISTENED,
    /**
     * 上传中
     */
    UPLOADING,
    /**
     * 上传完成
     */
    UPLOADED,
    /**
     * 上传失败
     */
    FAILURE,

}
