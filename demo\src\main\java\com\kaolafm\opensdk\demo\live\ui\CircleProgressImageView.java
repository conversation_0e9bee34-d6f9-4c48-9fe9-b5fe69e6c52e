package com.kaolafm.opensdk.demo.live.ui;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LightingColorFilter;
import android.graphics.Paint;
import android.graphics.RectF;
import androidx.annotation.Nullable;
import android.util.AttributeSet;

import com.kaolafm.opensdk.demo.R;

/**
 * <AUTHOR>
 */
public class CircleProgressImageView extends androidx.appcompat.widget.AppCompatImageView {

    private int mProgressColor;
    private Paint mPaint;

    private int mBackColor;
    private Paint mBackPaint;

    private int mProgressWidth;

    private int mProgress;
    private int mMaxProgress = 100;
    private boolean bDrawProgress;

    public CircleProgressImageView(Context context) {
        this(context, null);
    }

    public CircleProgressImageView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.CircleProgressImageView);
        mProgressColor = ta.getColor(R.styleable.CircleProgressImageView_progress_color,
                Color.parseColor("#FF06B5D2"));
        mProgressWidth = ta.getDimensionPixelOffset(
                R.styleable.CircleProgressImageView_progress_width, 8);
        mBackColor = ta.getColor(R.styleable.CircleProgressImageView_back_color,
                Color.parseColor("#FF545A6A"));
        ta.recycle();

        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(mProgressWidth);
        mPaint.setColor(mProgressColor);

        mBackPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mBackPaint.setStyle(Paint.Style.STROKE);
        mBackPaint.setStrokeWidth(mProgressWidth);
        mBackPaint.setColor(mBackColor);
        mBackPaint.setColorFilter(new LightingColorFilter(mBackColor, mBackColor));
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        if (bDrawProgress) {
            // 这个图是正方形，长宽一样，
            int width = getMeasuredWidth();
            int half = width / 2;
            int radius = half - mProgressWidth / 2;
            RectF rect = new RectF(half - radius, half - radius, half + radius, half + radius);
            canvas.drawArc(rect, -90, 360 * mProgress / mMaxProgress, false, mPaint);
        } else {
            super.onDraw(canvas);
        }

    }

    public int getProgress() {
        return mProgress;
    }

    public void setProgress(int progress) {
        this.mProgress = progress;
        invalidate();
    }

    public int getMaxProgress() {
        return mMaxProgress;
    }

    public void setMaxProgress(int maxProgress) {
        this.mMaxProgress = maxProgress;
    }

    public boolean isDrawProgress() {
        return bDrawProgress;
    }

    public void setDrawProgress(boolean drawProgress) {
        this.bDrawProgress = drawProgress;
    }

}
