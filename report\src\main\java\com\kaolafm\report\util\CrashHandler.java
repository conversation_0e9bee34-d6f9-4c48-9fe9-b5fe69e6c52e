package com.kaolafm.report.util;

import android.content.Context;
import java.lang.Thread.UncaughtExceptionHandler;

/**
 * <AUTHOR> 参考 http://www.ophonesdn.com/article/show/308
 * 在crash的时候记录日志,后续可以考虑上报异常,并提示用户更友好的信息
 */
public class CrashHandler implements UncaughtExceptionHandler {

    private UncaughtExceptionHandler mDefaultHandler;
    private static CrashHandler sInstance;

    private Context mContext;

    private OnCrashListener mCrashListener;

    private CrashHandler() {
    }

    public static CrashHandler getInstance() {
        if (sInstance == null) {
            synchronized (CrashHandler.class) {
                if (sInstance == null) {
                    sInstance = new CrashHandler();
                }
            }
        }
        return sInstance;
    }

    public void programStart(Context ctx) {
        mContext = ctx;
        mDefaultHandler = Thread.getDefaultUncaughtExceptionHandler();
        Thread.setDefaultUncaughtExceptionHandler(this);
    }

    public void setOnCrashListener(OnCrashListener listener) {
        mCrashListener = listener;
    }

    @Override
    public void uncaughtException(Thread thread, Throwable ex) {
        ex.printStackTrace();
        if (mDefaultHandler != null) {
            if (null != mCrashListener) {
                mCrashListener.onCrash(ex);
            }
            mDefaultHandler.uncaughtException(thread, ex);
        } else {
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            android.os.Process.killProcess(android.os.Process.myPid());
            System.exit(10);
        }
    }

    public interface OnCrashListener {
        void onCrash(Throwable ex);
    }
}
