package com.kaolafm.opensdk.api.login.model;

/**
 * 用户收听时长
 */
public class CumPlaytimeInfo {

    /**
     * 用户id
     */
    private String uid;
    /**
     * 收听排名（超过的用户百分比 "90%"）
     */
    private String listenRank;
    /**
     * 收听时长
     */
    private long cumulativeDuration;
    /**
     * 当月收听时长
     */
    private long monthCumulative;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public long getCumulativeDuration() {
        return cumulativeDuration;
    }

    public void setCumulativeDuration(long cumulativeDuration) {
        this.cumulativeDuration = cumulativeDuration;
    }

    public String getListenRank() {
        return listenRank;
    }

    public void setListenRank(String listenRank) {
        this.listenRank = listenRank;
    }

    public long getMonthCumulative() {
        return monthCumulative;
    }

    public void setMonthCumulative(long monthCumulative) {
        this.monthCumulative = monthCumulative;
    }

    @Override
    public String toString() {
        return "CumPlaytimeInfo{" +
                "uid='" + uid + '\'' +
                ", listenRank='" + listenRank + '\'' +
                ", cumulativeDuration=" + cumulativeDuration +
                ", monthCumulative=" + monthCumulative +
                '}';
    }
}
