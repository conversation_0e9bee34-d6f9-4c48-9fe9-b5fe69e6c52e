package com.kaolafm.opensdk.demo.activity;

import android.annotation.SuppressLint;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.kaolafm.opensdk.api.activity.model.Activity;
import com.kaolafm.opensdk.demo.BaseHolder;
import com.kaolafm.opensdk.demo.R;

import butterknife.BindView;

public class ActivityViewHolder extends BaseHolder<Activity> {

    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.title_activity)
    TextView titleTextView;
    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.des_activity)
    TextView desTextView;
    @BindView(R.id.qrCode_image)
    ImageView qrCodeImage;
    @BindView(R.id.act_image)
    ImageView act_image;
    @BindView(R.id.qrCode_textView)
    TextView qrTextView;
    @BindView(R.id.qrCode_textView2)
    TextView qrCode_textView2;
    @BindView(R.id.qr_view_expire)
    View qrExpireView;
    @BindView(R.id.qr_expire_icon)
    View qrExpireIcon;

    public ActivityViewHolder(View itemView) {
        super(itemView);
    }

    @Override
    public void setupData(Activity activity, int position) {
        titleTextView.setText(activity.getName());
        desTextView.setText(activity.getDescription());

        if (!TextUtils.isEmpty(activity.getVedioUrl())) {
            Glide.with(itemView)
                    .load(activity.getVedioUrl())
                    .into(act_image);
            qrCode_textView2.setText("视频");
        }else if (!TextUtils.isEmpty(activity.getImgUrl())) {
            Glide.with(itemView)
                    .load(activity.getImgUrl())
                    .into(act_image);
            qrCode_textView2.setText("图片");
        }else if (!TextUtils.isEmpty(activity.getRadioUrl())){
            qrCode_textView2.setText("音频");
        }else {
            qrCode_textView2.setText("无");
        }

        Glide.with(itemView)
                .load(activity.getQrCodeUrl())
                .into(qrCodeImage);
        qrTextView.setText(activity.getCodeDes());

        if (activity.getStatus() == 1) {
            qrExpireView.setVisibility(View.GONE);
            qrExpireIcon.setVisibility(View.GONE);
            titleTextView.setTextColor(itemView.getResources().getColor(R.color.text_color_1));
            desTextView.setTextColor(itemView.getResources().getColor(R.color.text_color_1));
        } else {
            qrExpireView.setVisibility(View.VISIBLE);
            qrExpireIcon.setVisibility(View.VISIBLE);
            titleTextView.setTextColor(itemView.getResources().getColor(R.color.text_color_2));
            desTextView.setTextColor(itemView.getResources().getColor(R.color.text_color_2));
        }
    }
}
