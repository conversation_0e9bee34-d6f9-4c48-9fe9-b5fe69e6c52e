package com.kaolafm.opensdk.http.download.engine;

import com.kaolafm.opensdk.http.download.DownloadProgress;
import com.kaolafm.opensdk.http.download.DownloadRequest;

import io.reactivex.Flowable;
import okhttp3.ResponseBody;
import retrofit2.Response;

/**
 * <AUTHOR>
 * @date 2020-02-11
 */
public interface DownloadEngine {

    Flowable<DownloadProgress> download(DownloadRequest request, Response<ResponseBody> response);
}
