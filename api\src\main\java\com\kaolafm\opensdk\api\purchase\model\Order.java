package com.kaolafm.opensdk.api.purchase.model;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;

/**
 * 支付订单
 */
@Entity
public class Order {

    /* 订单内容类型，1-vip，2-专辑，3-单曲 */
    private int productType;

    /* 订单id */
    private long id;

    /* 订单显示名称  根据专辑类型：
    1. VIP会员x天、
    2. 专辑：专辑名称，
    3：单条单曲：专辑名（单曲名称）。
    多条单曲：专辑名（共xx集）
    */
    private String title;

    /* 订单编号 */
    private String billNo;

    /* 支付方式，1:微信，2:支付宝，3:云币 */
    private int payType;

    @Deprecated
    /* 1-Android，2-iOS */
    private int plantform;

    /* 订单状态 0-未支付，1-已取消，2-已支付 */
    private int status;

    /* 下单时间,xxxx（年）-xx（月）-xx（日） xx（时）：xx（分 */
    private String createTime;

    /* vip生效时间 xxxx（年）-xx（月）-xx（日） */
    private String vipEffectiveDate;

    /* vip到期时间 xxxx（年）-xx（月）-xx（日） */
    private String vipExpireDate;

    /*支付结果文字描述 */
    private String statusDesc;

    @Generated(hash = 995777661)
    public Order(int productType, long id, String title, String billNo, int payType, int plantform, int status, String createTime, String vipEffectiveDate, String vipExpireDate, String statusDesc) {
        this.productType = productType;
        this.id = id;
        this.title = title;
        this.billNo = billNo;
        this.payType = payType;
        this.plantform = plantform;
        this.status = status;
        this.createTime = createTime;
        this.vipEffectiveDate = vipEffectiveDate;
        this.vipExpireDate = vipExpireDate;
        this.statusDesc = statusDesc;
    }
    @Generated(hash = 1105174599)
    public Order() {
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public int getProductType() {
        return productType;
    }

    public void setProductType(int productType) {
        this.productType = productType;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getPayType() {
        return payType;
    }

    public void setPayType(int payType) {
        this.payType = payType;
    }

    public int getPlantform() {
        return plantform;
    }

    public void setPlantform(int plantform) {
        this.plantform = plantform;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    @Override
    public String toString() {
        return "Order{" +
                "productType=" + productType +
                ", id=" + id +
                ", title='" + title + '\'' +
                ", billNo='" + billNo + '\'' +
                ", payType=" + payType +
                ", plantform=" + plantform +
                ", status=" + status +
                ", createTime='" + createTime + '\'' +
                ", vipEffectiveDate='" + vipEffectiveDate + '\'' +
                ", vipExpireDate='" + vipExpireDate + '\'' +
                ", statusDesc='" + statusDesc + '\'' +
                '}';
    }
    public String getVipEffectiveDate() {
        return this.vipEffectiveDate;
    }
    public void setVipEffectiveDate(String vipEffectiveDate) {
        this.vipEffectiveDate = vipEffectiveDate;
    }
    public String getVipExpireDate() {
        return this.vipExpireDate;
    }
    public void setVipExpireDate(String vipExpireDate) {
        this.vipExpireDate = vipExpireDate;
    }
}
