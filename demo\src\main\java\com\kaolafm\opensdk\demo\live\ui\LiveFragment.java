package com.kaolafm.opensdk.demo.live.ui;

import android.Manifest;
import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.PropertyValuesHolder;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.drawable.Drawable;
import android.media.AudioManager;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Message;
import android.os.SystemClock;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.ActivityCompat;
import androidx.fragment.app.Fragment;
import androidx.core.content.ContextCompat;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.AnimationSet;
import android.view.animation.AnimationUtils;
import android.view.animation.DecelerateInterpolator;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.kaolafm.opensdk.api.live.model.LiveInfoDetail;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.demo.live.chat.ChatUserInfo;
import com.kaolafm.opensdk.demo.live.chat.MessageBean;
import com.kaolafm.opensdk.demo.live.chat.NimManager;
import com.kaolafm.opensdk.demo.live.chat.RecordUploadHelper;
import com.kaolafm.opensdk.demo.live.chat.RecorderStatus;
import com.kaolafm.opensdk.demo.live.play.ErrorStatus;
import com.kaolafm.opensdk.demo.live.play.LiveManager;
import com.kaolafm.opensdk.demo.live.play.LiveStatus;
import com.kaolafm.opensdk.demo.login.KaolaLoginActivity;
import com.kaolafm.opensdk.player.LivePlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.model.item.LivePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Observable;
import java.util.Observer;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;

import static android.media.AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK;

public class LiveFragment extends Fragment
        implements IPlayerStateListener, View.OnClickListener, LiveIView {

    private static final String TAG = "LiveFragment";

    private static final String EXTRA_PROGRAM_ID = "extra_program_id";

    private static final int CODE_PERMISSION_REQUEST = 1;

    private static final int RECORD_DURATION = 20 * 1000;
    private static final int RECORD_COUNTDOWN = 1000;

    private static final int PLAY_ERROR_TOLERANCE_TIME = 60 * 1000;

    public static final int DURATION_LONG = 1500;

    private long mProgramId;

    @BindView(R.id.live_minimum)
    ImageView mMinimumImage;
    @BindView(R.id.live_stop)
    ImageView mStopLiveImage;

    @BindView(R.id.live_image)
    ImageView mLiveImage;

    @BindView(R.id.live_name)
    TextView mLiveNameText;

    @BindView(R.id.live_player)
    TextView mLivePlayerText;

    @BindView(R.id.live_not_start_text)
    TextView mForecastText;

    @BindView(R.id.live_listening_number)
    TextView mListenerNumberText;

    @BindView(R.id.live_login_prompt_text)
    TextView mLoginPromptText;

    @BindView(R.id.live_not_start_layout)
    ViewGroup mLiveNotStartLayout;

    @BindView(R.id.live_finish_layout)
    ViewGroup mLiveAlreadyFinishLayout;

    @BindView(R.id.live_coming_layout)
    ViewGroup mComingLayout;

    @BindView(R.id.live_error_layout)
    ViewGroup mErrorLayout;

    @BindView(R.id.live_speak_image)
    ImageView mSpeakImage;

    @BindView(R.id.live_record_anim_image)
    ImageView mRecordSoundImage;

    @BindView(R.id.live_leave_a_message)
    CircleProgressImageView mRecordButtonImage;

    @BindView(R.id.live_listen_button_layout)
    ViewGroup mListenButton;

    @BindView(R.id.live_cancel_button_layout)
    ViewGroup mCancelButton;

    @BindView(R.id.live_listen_anim_image)
    PlayingIndicator mListenButtonAnim;

    @BindView(R.id.live_listen_message_text)
    TextView mListenButtonText;

    @BindView(R.id.live_cancel_message_text)
    TextView mCancelButtonText;

    @BindView(R.id.live_countdown_text)
    TextView mRecordButtonText;

    @BindView(R.id.live_screen_bullet_layout)
    FrameLayout mScreenBulletContainer;

    @BindView(R.id.live_top_gradient)
    View mTopGradientImage;

    @BindView(R.id.live_bottom_gradient)
    View mBottomGradientImage;

    @BindView(R.id.live_finish_image)
    View mLiveFinishImage;

    @BindView(R.id.live_root_layout)
    ConstraintLayout mRootLayout;


    private PlayItem mPlayItem;

    private boolean bPlayerEnabled;

    private TextView mLastMessageText;

    private String mRecordFile;

    private String mForecastString;

    private int mSystemVolume;

    private CountDownTimer mRecordTimer;
    private CountDownTimer mForecastTimer;


    private boolean bDoMemberEnterAnim;
    private LinkedList<ChatUserInfo> mMemberEnterQueue = new LinkedList<ChatUserInfo>();

    private boolean bDoMessageReceiveAnim;
    private LinkedList<MessageBean> mMessageReceiveQueue = new LinkedList<MessageBean>();

    private long mLastRecordTime;
    private long mLastListenTime;

    private Unbinder mUnbinder;
    private LivePresenter mPresenter;
    private LiveHandler mHandler;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(getLayoutId(), container, false);
        mUnbinder = ButterKnife.bind(this, view);
        //消费事件, 防止touch事件传递到地图
        view.setOnClickListener(v -> {
        });
        initView(view);
        return view;
    }

    protected int getLayoutId() {
        return R.layout.fragment_live;
    }

    protected LivePresenter createPresenter() {
        LivePresenter presenter = new LivePresenter(this);
        LiveManager.getInstance().setPresenter(presenter);
        NimManager.getInstance().initNim();
        NimManager.getInstance().setLivePresenter(presenter);
        return presenter;
    }

    public static LiveFragment create(String firstId) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "create: " + firstId);
        }
        Bundle args = new Bundle();
        args.putString(EXTRA_PROGRAM_ID, firstId);
        LiveFragment lf = new LiveFragment();
        lf.setArguments(args);
        return lf;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initData();
        mPresenter = createPresenter();
        mForecastString = getString(R.string.live_hour_minute_second);
    }

    @Override
    public void onStart() {
        super.onStart();
        mPresenter.start();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "onResume");
        }
        LiveManager.getInstance().addRecorderStatusObserver(mRecorderStatusObserver);
        boolean isLogin = UserInfoManager.getInstance().isUserLogin();
        if (!isLogin) {
            mLoginPromptText.setText(R.string.live_leave_message_after_login);
        } else {
            mLoginPromptText.setText(R.string.live_click_to_speak);
        }
        Bundle arg = getArguments();
        boolean useOldProgramId = false;
        long programId = 0L;
        if (null == arg) {
            Log.e(TAG, "onResume, null Bundle");
            useOldProgramId = true;
        } else {
            String id = arg.getString(EXTRA_PROGRAM_ID);
            if (id == null) {
                Log.e(TAG, "onResume, null programid, use old data");
                useOldProgramId = true;
            } else {
                programId = Long.parseLong(id);
            }
        }

        if (useOldProgramId) {
            LiveInfoDetail info = LiveManager.getInstance().getPlayingInfo();
            if (info == null) {
                info = LiveManager.getInstance().getShowingInfo();
            }
            if (info != null) {
                programId = info.getProgramId();
            }
        }
        if (0L == programId) {
            Log.e(TAG, "onResume, null programid, no old data, show error");
            showErrorInfo(ErrorStatus.ERROR_ARGUMENT);
            return;
        }
        mProgramId = programId;
        LiveManager.getInstance().loopLiveStatus(programId);
    }

    public void initData() {
        mHandler = new LiveHandler(this);
    }


    public void initView(View view) {
    }

    @OnClick({
            R.id.live_minimum,
            R.id.live_stop,
            R.id.live_leave_a_message,
            R.id.live_cancel_button_layout,
            R.id.live_listen_button_layout,
    })
    @Override
    public void onClick(View v) {
        int id = v.getId();

        if (id == R.id.live_minimum) {
            getActivity().finish();
        } else if (id == R.id.live_stop) {
            LivePlayerManager.getInstance().reset();
            LiveManager.getInstance().onLiveExit();
            exitChatRoom();
            bPlayerEnabled = false;
            getActivity().finish();
        } else if (id == R.id.live_cancel_button_layout) {
            cancel();
        } else if (id == R.id.live_listen_button_layout) {
            if (LiveManager.getInstance().isPlaying()) {
                stopListen();
            } else if (LiveManager.getInstance().isFinished()) {
                startListen();
            } else if (LiveManager.getInstance().isListened()) {
                startListen();
            }
        } else if (id == R.id.live_leave_a_message) {
            if (LiveManager.getInstance().isRecording()) {
                stopRecord();
            } else if (LiveManager.getInstance().isIdle()) {
                startRecordWithPermissionCheck();
            } else if (LiveManager.getInstance().isUploading()) {
                //
            } else if (LiveManager.getInstance().isUploaded()) {
                LiveManager.getInstance().setRecorderStatus(RecorderStatus.IDLE);
            } else {
                sendMessage();
            }
        }
    }

    @Override
    public void onSubscribeSuccess() {

    }

    @Override
    public void onSubscribeError() {

    }

    @Override
    public void onCheckSubscribeSuccess() {

    }

    @Override
    public void onCheckSubscribeError() {

    }

    @Override
    public void onGetMediaInfoError() {

    }

    @Override
    public void onIdle(PlayItem playItem) {

    }

    @Override
    public void onPlayerPreparing(PlayItem playItem) {

    }

    @Override
    public void onPlayerPreparingComplete(PlayItem playItem) {

    }

    @Override
    public void onPlayerPlaying(PlayItem playItem) {

    }

    @Override
    public void onPlayerPaused(PlayItem playItem) {

    }

    @Override
    public void onProgress(PlayItem playItem, long progress, long total) {
        
    }

    @Override
    public void onPlayerFailed(PlayItem playItem, int i, int i1) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "onPlayerFailed");
        }
        showError(ErrorStatus.ERROR_PLAY);
    }

    @Override
    public void onPlayerEnd(PlayItem playItem) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "onPlayerEnd");
        }
    }

    @Override
    public void onSeekStart(PlayItem playItem) {
        
    }

    @Override
    public void onSeekComplete(PlayItem playItem) {
        
    }

    @Override
    public void onBufferingStart(PlayItem playItem) {

    }

    @Override
    public void onBufferingEnd(PlayItem playItem) {

    }

    @Override
    public void onDownloadProgress(PlayItem playItem, long progress, long total) {

    }

    public boolean onActivityMsg(Intent iData) {
        return false;
    }


    @Override
    public void showFileNotExist() {
        String msg = getResources().getString(R.string.live_upload_failed) + ": "
                + getResources().getString(R.string.live_file_not_exist);
        Toast.makeText(getContext(), msg, Toast.LENGTH_SHORT).show();
    }

    @Override
    public void showRecordUploadProgress(int progress) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "showRecordUploadProgress: " + progress);
        }
        //由于文件上传的回调是在子线程进行的，所以用post的方式让它在UI线程更新UI
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                mRecordButtonImage.setProgress(progress);
            }
        });

    }

    @Override
    public void showRecordUploadSuccess() {
        //由于文件上传的回调是在子线程进行的，所以用post的方式让它在UI线程更新UI
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                LiveManager.getInstance().setRecorderStatus(RecorderStatus.UPLOADED);
                LiveManager.getInstance().deleteFile();
            }
        });
        String room = NimManager.getInstance().getRoomId();
        mPresenter.sendEmptyTextMessage(room, getString(R.string.live_default_message));
    }

    @Override
    public void showRecordUploadFailure() {
        //由于文件上传的回调是在子线程进行的，所以用post的方式让它在UI线程更新UI
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                Toast.makeText(getContext(), R.string.live_upload_failed, Toast.LENGTH_SHORT).show();
                LiveManager.getInstance().setRecorderStatus(RecorderStatus.FAILURE);
            }
        });
    }

    @Override
    public void showLiveInfo(LiveInfoDetail info) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "showLiveInfo");
        }
        if (info == null) {
            showErrorInfo(ErrorStatus.ERROR_CONTENT);
        } else {
            setContent(info);
            String roomId = info.getRoomId();
            int status = info.getStatus();
            if (LivePresenter.DEBUG_LIVE) {
                Log.d(TAG, "showLiveInfo roomId: " + roomId + ", STATUS: " + status);
            }
            if (!TextUtils.isEmpty(roomId)) {
                mPresenter.enterChatRoom(getContext(), roomId);
            }
        }
    }

    @Override
    public void showErrorInfo(ErrorStatus status) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "getShowingInfo onError msg: " + status);
        }
        showError(ErrorStatus.ERROR_REQUEST);
    }

    private void setContent(LiveInfoDetail info) {
        LiveManager.getInstance().setShowingInfo(info);
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "setContent: " + info.getStatus());
        }
        showCommonInfo(info);
        mPresenter.getListenerNumber(NimManager.getInstance().getRoomId());
        int status = info.getStatus();
        if (status == LiveInfoDetail.STATUS_NOT_START) {
            if (LiveManager.getInstance().getLiveStatus() != LiveStatus.NOT_START
                    && !mLiveNotStartLayout.isShown()) {
                showForecast(info);
            }
            mStopLiveImage.setVisibility(View.GONE);
        } else if (status == LiveInfoDetail.STATUS_FINISHED) {
            if (LiveManager.getInstance().getLiveStatus() != LiveStatus.FINISHED
                    && !mLiveAlreadyFinishLayout.isShown()) {
                showFinish();
            }
            mStopLiveImage.setVisibility(View.GONE);
        } else if (status == LiveInfoDetail.STATUS_LIVING) {
            if (LiveManager.getInstance().getLiveStatus() != LiveStatus.LIVING) {
                showLive(info);
            }
            mStopLiveImage.setVisibility(View.VISIBLE);
        } else if (status == LiveInfoDetail.STATUS_COMING) {
            if (LiveManager.getInstance().getLiveStatus() != LiveStatus.COMING
                    && !mComingLayout.isShown()) {
                showComing();
            }
            mStopLiveImage.setVisibility(View.GONE);
        } else if (status == LiveInfoDetail.STATUS_DELAYED) {
            if (LiveManager.getInstance().getLiveStatus() != LiveStatus.NOT_START
                    && !mLiveNotStartLayout.isShown()) {
                showForecast(info);
            }
            mStopLiveImage.setVisibility(View.GONE);
        } else {
            //未知状态
            hideRecordButton();
        }
        PlayItem item = LivePlayerManager.getInstance().getCurrentPlayItem();
        if (null != item && Long.valueOf(item.getAlbumId()) == info.getProgramId()) {
            if(item instanceof LivePlayItem){
                ((LivePlayItem)item).setStatus(status);
            }
        }
    }

    private void showCommonInfo(LiveInfoDetail result) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "showCommonInfo");
        }
        loadImage(result.getProgramPic());
        mLiveNameText.setText(result.getProgramName());
        mLivePlayerText.setText(String.format(getString(R.string.live_player), result.getComperes()));
    }

    private void loadImage(String imageUrl) {
        Activity act = getActivity();
        if (null == act) {
            return;
        }
        Glide.with(this).load(imageUrl).into(new SimpleTarget<Drawable>() {
            @Override
            public void onResourceReady(@NonNull Drawable resource,
                                        @Nullable Transition<? super Drawable> transition) {
                if (mLiveImage != null) {
                    mLiveImage.setScaleType(ImageView.ScaleType.CENTER_CROP);
                    mLiveImage.setImageDrawable(resource);
                }
            }

            @Override
            public void onLoadFailed(@Nullable Drawable errorDrawable) {
                if (mLiveImage != null) {
                    mLiveImage.setScaleType(ImageView.ScaleType.CENTER);
                }
            }
        });
    }


    private void showMemberEnter(ChatUserInfo member) {
        final TextView textView = createMemberEnterTextView(member);
        int height = (int) (mScreenBulletContainer.getHeight() * 0.0728);
        FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.WRAP_CONTENT, height);
        textView.setTextSize(height / 5);
        lp.topMargin = (int) (mScreenBulletContainer.getHeight() * 0.11);
        mScreenBulletContainer.addView(textView, lp);
        int start = mScreenBulletContainer.getRight();
        //最小化按钮右边50px慢下来
        int middle = mMinimumImage.getRight() + 50;
        int left = mMinimumImage.getLeft();

        ObjectAnimator oar = ObjectAnimator.ofFloat(textView, "x", start, middle);
        oar.setDuration(500);
        oar.setInterpolator(new DecelerateInterpolator());

        ObjectAnimator oam = ObjectAnimator.ofFloat(textView, "x", middle, left);
        oam.setDuration(3000);

        //滑出屏幕，需要一个负值,由于View刚Add，还没有宽度，所以写一个参考值
        ObjectAnimator oal = ObjectAnimator.ofFloat(textView, "x", left, -300);
        oal.setDuration(300);
        oal.setInterpolator(new AccelerateInterpolator());

        AnimatorSet animSet = new AnimatorSet();
        animSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
                bDoMemberEnterAnim = true;
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                bDoMemberEnterAnim = false;
                if (mScreenBulletContainer != null && textView != null) {
                    mScreenBulletContainer.removeView(textView);
                }
                if (mMemberEnterQueue.size() > 0) {
                    ChatUserInfo member = mMemberEnterQueue.removeFirst();
                    showMemberEnter(member);
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                bDoMemberEnterAnim = false;
            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        animSet.play(oal).after(oam).after(oar);
        animSet.start();
    }

    private void showMessageReceived(MessageBean message) {
        final TextView textView = createMessageSendTextView(message);
        int height = (int) (mScreenBulletContainer.getHeight() * 0.09);
        textView.setTextSize(height / 6);
        final FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.WRAP_CONTENT, height);
        lp.leftMargin = mMinimumImage.getLeft();
        mScreenBulletContainer.addView(textView, lp);
        int bottom = mScreenBulletContainer.getBottom();

        if (mLastMessageText != null) {
            int s = (int) mLastMessageText.getY();
            int e = (int) (bottom * 0.60);

            ObjectAnimator oan = ObjectAnimator.ofFloat(mLastMessageText, "y", s, e);
            oan.setDuration(300);

            ObjectAnimator oaa = ObjectAnimator.ofFloat(mLastMessageText, "alpha", 1, 0);
            oaa.setDuration(300);

            AnimatorSet as = new AnimatorSet();
            as.playTogether(oan, oaa);
            as.start();
        }

        int middle = (int) (bottom * 0.70);
        ObjectAnimator oan = ObjectAnimator.ofFloat(textView, "y", bottom, middle);
        oan.setDuration(500);
        oan.setInterpolator(new DecelerateInterpolator());
        AnimatorSet animSet = new AnimatorSet();
        animSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
                bDoMessageReceiveAnim = true;
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                bDoMessageReceiveAnim = false;
                if (mLastMessageText != null) {
                    mScreenBulletContainer.removeView(mLastMessageText);
                }
                mLastMessageText = textView;
                if (mMessageReceiveQueue.size() > 0) {
                    MessageBean mb = mMessageReceiveQueue.removeFirst();
                    showMessageReceived(mb);
                }
                mLastMessageText.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (mLastMessageText != null && mScreenBulletContainer != null) {
                            mScreenBulletContainer.removeView(mLastMessageText);
                        }
                    }
                }, 4000);
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                bDoMessageReceiveAnim = false;
            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        animSet.play(oan);
        animSet.start();
    }

    private void hideRecordButton() {
        cancelRecord();
        mRecordButtonImage.setVisibility(View.GONE);
        mRecordButtonText.setVisibility(View.GONE);
        mLoginPromptText.setVisibility(View.GONE);
        mSpeakImage.setVisibility(View.GONE);
        mListenButton.setVisibility(View.GONE);
        mCancelButton.setVisibility(View.GONE);
    }

    private void showImageButton() {
        mRecordButtonImage.setVisibility(View.VISIBLE);
        mLoginPromptText.setVisibility(View.VISIBLE);
        mSpeakImage.setVisibility(View.VISIBLE);
    }

    private void showForecast(LiveInfoDetail result) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "showForecast");
        }
        LiveManager.getInstance().setLiveStatus(LiveStatus.NOT_START);
        hideRecordButton();
        mLiveNotStartLayout.setVisibility(View.VISIBLE);
        mLiveAlreadyFinishLayout.setVisibility(View.GONE);
        mComingLayout.setVisibility(View.GONE);
        mErrorLayout.setVisibility(View.GONE);
        startForecastTimer(result.getStartTime(), result.getServerTime());
    }

    private void showFinish() {
        boolean isPlaying = LivePlayerManager.getInstance().isPlaying();
        PlayItem playItem = LivePlayerManager.getInstance().getCurrentPlayItem();
        LiveInfoDetail liveInfo = LiveManager.getInstance().getPlayingInfo();
        boolean isSame = false;
        if(playItem instanceof LivePlayItem){
            if (playItem != null && ((LivePlayItem)playItem).isLiving() && liveInfo != null
                    && ((LivePlayItem)playItem).getLiveId() == liveInfo.getProgramId()) {
                isSame = true;
            }
        }
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "showFinish isPlaying: " + isPlaying + ", isSame: " + isSame);
        }
        if (isPlaying && isSame) {
            //轮询到播放状态是结束，但播放器还在进行，先不显示播放结束
            return;
        }
        //轮询到结束并且播放器也没有播放，就不在轮询了。
        LiveManager.getInstance().stopLoopLiveStatus();
        LiveManager.getInstance().setLiveStatus(LiveStatus.FINISHED);
        mLiveAlreadyFinishLayout.setVisibility(View.VISIBLE);
        mLiveNotStartLayout.setVisibility(View.GONE);
        mComingLayout.setVisibility(View.GONE);
        mErrorLayout.setVisibility(View.GONE);
        mLiveFinishImage.setVisibility(View.VISIBLE);
        mTopGradientImage.setVisibility(View.GONE);
        mBottomGradientImage.setVisibility(View.GONE);
        hideRecordButton();
        //showImageButton();
        if (mPlayItem != null && bPlayerEnabled
                && LiveManager.getInstance().getLiveStatus() == LiveStatus.LIVING) {
            bPlayerEnabled = false;
            LivePlayerManager.getInstance().stop();
            LivePlayerManager.getInstance().reset();

        }
    }

    private void showLive(LiveInfoDetail info) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "showLive result: " + info.getProgramName());
        }
        LiveManager.getInstance().setLiveStatus(LiveStatus.LIVING);
        mLiveAlreadyFinishLayout.setVisibility(View.GONE);
        mLiveNotStartLayout.setVisibility(View.GONE);
        mComingLayout.setVisibility(View.GONE);
        mErrorLayout.setVisibility(View.GONE);
        showImageButton();
        mPlayItem = LiveManager.getInstance().toPlayItem(info);
        String liveUrl = mPlayItem.getPlayUrl();
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "showLive play liveUrl: " + liveUrl);
        }
        if (liveUrl == null) {
            showComing();
        } else {
            boolean isPlaying = LivePlayerManager.getInstance().isPlaying();
            PlayItem playItem = LivePlayerManager.getInstance().getPlayItem();
            if (playItem instanceof LivePlayItem) {
                if (isPlaying && playItem != null && ((LivePlayItem)mPlayItem).getLiveId() == ((LivePlayItem)playItem).getLiveId()) {
                    if (LivePresenter.DEBUG_LIVE) {
                        Log.d(TAG, "showLive play but is playing and have same id, return");
                    }
                    return;
                }
            }
            LivePlayerManager.getInstance().enablePlayer();
            bPlayerEnabled = true;
            LivePlayerManager.getInstance().addIPlayerStateListener(this);
            LivePlayerManager.getInstance().play(mPlayItem);
            LiveManager.getInstance().setPlayingInfo(info);
        }

    }

    private void showComing() {
        LiveManager.getInstance().setLiveStatus(LiveStatus.COMING);
        mComingLayout.setVisibility(View.VISIBLE);
        mLiveAlreadyFinishLayout.setVisibility(View.GONE);
        mLiveNotStartLayout.setVisibility(View.GONE);
        mErrorLayout.setVisibility(View.GONE);
        hideRecordButton();
    }

    private void showError(ErrorStatus status) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.e(TAG, "showError reason: " + status);
        }
        if (status == ErrorStatus.ERROR_PLAY) {
            //播放错误，如果状态是结束，则显示结束
            LiveInfoDetail info = LiveManager.getInstance().getPlayingInfo();
            if (info == null) {
                LiveManager.getInstance().setErrorStatus(status);
                showCannotPlayError();
                return;
            }
            if (info.getStatus() == LiveInfoDetail.STATUS_FINISHED) {
                showFinish();
                return;
            } else if (info.getStatus() == LiveInfoDetail.STATUS_LIVING) {
                //直播倒计时结束，开始播放，由于流的延迟性，很可能播放失败，所以在直播开始后的1分钟内的
                //播放失败，当做预告处理
                long serverTime = info.getServerTime();
                long startTime = info.getStartTime();
                if (LivePresenter.DEBUG_LIVE) {
                    Log.d(TAG, "showError living serverTime: " + serverTime
                            + ", startTime: " + startTime);
                }
                if (serverTime - startTime <= PLAY_ERROR_TOLERANCE_TIME) {
                    showComing();
                    return;
                }
            }
        }
        LiveManager.getInstance().setErrorStatus(status);
        showCannotPlayError();
    }

    private void showCannotPlayError() {
        hideRecordButton();
        LiveManager.getInstance().setLiveStatus(LiveStatus.ERROR);
        LivePlayerManager.getInstance().removeIPlayerStateListener(this);
        LivePlayerManager.getInstance().reset();
        bPlayerEnabled = false;
        mErrorLayout.setVisibility(View.VISIBLE);
        mComingLayout.setVisibility(View.GONE);
        mLiveAlreadyFinishLayout.setVisibility(View.GONE);
        mLiveNotStartLayout.setVisibility(View.GONE);
    }

    @Override
    public void showListenerNumber(int number) {
        mListenerNumberText.setText(String.format(getString(R.string.live_listening_number), number));
    }

    @Override
    public void showRoomMemberEnter(ChatUserInfo member) {
        if (member != null) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.d(TAG, "showRoomMemberEnter: " + member.getNickName());
            }
            //如果正在做成员进入动画，或者成员进入队列中有元素在排队，添加到队尾，否则展示此条进入提示
            if (mMemberEnterQueue.size() > 0 || bDoMemberEnterAnim) {
                mMemberEnterQueue.addLast(member);
            } else {
                showMemberEnter(member);
            }
        }

    }

    @Override
    public void showChatMessageReceived(ArrayList<MessageBean> messageList) {
        if (messageList == null || messageList.size() <= 0) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.d(TAG, "showChatMessageReceived empty message list");
            }
            return;
        }
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "showChatMessageReceived message count: " + messageList.size());
        }
        //消息以列表的方式组织，所以如果消息队列中有消息或者正在进行消息提示的动画，把所有
        //新来的消息添加到队列，
        if (mMessageReceiveQueue.size() > 0 || bDoMessageReceiveAnim) {
            mMessageReceiveQueue.addAll(messageList);
        } else {
            //否则，取列表中的第一条展示，如果还有剩下的，进入消息队列
            MessageBean message = messageList.remove(0);
            showMessageReceived(message);
            if (messageList.size() > 0) {
                mMessageReceiveQueue.addAll(messageList);
            }
        }

    }

    @Override
    public void onDestroy() {
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "onDestroy");
        }
//        LivePlayerManager.getInstance().removeIPlayerStateListener(this);
        LiveManager.getInstance().removeRecorderStatusObserver(mRecorderStatusObserver);
        if (LiveManager.getInstance().isPlaying()) {
            LiveManager.getInstance().stopListen();
        }
        if (LiveManager.getInstance().isRecording()) {
            LiveManager.getInstance().stopRecord(true);
        }

        stopRecordTimer();
        stopForecastTimer();
//        LiveManager.getInstance().resetStatus();
        cancelBind();
        super.onDestroy();
    }

    private void cancelBind() {
        if (mPresenter != null) {
            mPresenter.destroy();
        }
        mPresenter = null;
        if (mUnbinder != null) {
            mUnbinder.unbind();
        }
        mUnbinder = null;
    }

    private void exitChatRoom() {
        mPresenter.exitChatRoom();
        clearChatRoomQueue();
    }

    private void clearChatRoomQueue() {
        mMemberEnterQueue.clear();
        mMessageReceiveQueue.clear();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        switch (requestCode) {
            case CODE_PERMISSION_REQUEST:
                if (grantResults.length > 0
                        && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    startRecord();
                } else {

                }
                break;
        }
    }

    private void startRecordWithPermissionCheck() {
        List<String> needPermission = new ArrayList<String>();
        if (!UserInfoManager.getInstance().isUserLogin()) {
            startActivity(new Intent(getContext(), KaolaLoginActivity.class));
            return;
        }
        boolean needRecordAudio = ContextCompat.checkSelfPermission(getContext(),
                Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED;
        if (needRecordAudio) {
            needPermission.add(Manifest.permission.RECORD_AUDIO);
        }
        boolean needReadExternal = ContextCompat.checkSelfPermission(getContext(),
                Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED;
        if (needReadExternal) {
            needPermission.add(Manifest.permission.READ_EXTERNAL_STORAGE);
        }
        boolean needWriteExternal = ContextCompat.checkSelfPermission(getContext(),
                Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED;
        if (needWriteExternal) {
            needPermission.add(Manifest.permission.WRITE_EXTERNAL_STORAGE);
        }
        if (needPermission.size() > 0) {
            String[] requestPermissions = new String[needPermission.size()];
            for (int i = 0; i < needPermission.size(); i++) {
                requestPermissions[i] = needPermission.get(i);
            }
            ActivityCompat.requestPermissions(getActivity(), requestPermissions,
                    CODE_PERMISSION_REQUEST);
        } else {
            startRecord();
        }
    }

    private void startRecord() {
        long delta = SystemClock.elapsedRealtime() - mLastRecordTime;
        if (delta < DURATION_LONG) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.d(TAG, "startRecord but period to last recording is too short");
            }
            return;
        }
        mRecordFile = LiveManager.getInstance().startRecord();
        startCountdownTimer();
    }

    private void startForecastTimer(long startTime, long serverTime) {
        if (mForecastTimer != null) {
            mForecastTimer.cancel();
            mForecastTimer = null;
        }
        mForecastTimer = new CountDownTimer(startTime - serverTime, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                mForecastText.setText(generateForestString(millisUntilFinished));
            }

            @Override
            public void onFinish() {
                mPresenter.getLiveInfo(mProgramId);
            }
        };
        mForecastTimer.start();
    }

    private String generateForestString(long period) {
        int seconds = (int) (period / 1000);
        int hour = seconds / (60 * 60);
        int left = seconds % (60 * 60);
        int minute = left / 60;
        int second = left % 60;
        String minuteStr = null;
        if (minute < 10) {
            minuteStr = "0" + minute;
        } else {
            minuteStr = String.valueOf(minute);
        }
        String secondStr = null;
        if (second < 10) {
            secondStr = "0" + second;
        } else {
            secondStr = String.valueOf(second);
        }
        String ret = String.format(mForecastString, hour, minuteStr, secondStr);
        return ret;
    }

    private void startCountdownTimer() {
        if (mRecordTimer != null) {
            mRecordTimer.cancel();
            mRecordTimer = null;
        }
        String s = getResources().getString(R.string.live_second);
        mRecordButtonText.setText(RECORD_DURATION / 1000 + s);
        mRecordTimer = new CountDownTimer(RECORD_DURATION, RECORD_COUNTDOWN) {
            @Override
            public void onTick(long millisUntilFinished) {
                mRecordButtonText.setText(String.valueOf(millisUntilFinished / 1000) + s);
            }

            @Override
            public void onFinish() {
                stopRecord();
            }
        };
        mRecordTimer.start();
    }

    private void startListenTimer() {
        mListenButtonAnim.setVisibility(View.VISIBLE);
        mListenButtonText.setVisibility(View.GONE);
    }

    private void stopListenTimer() {
        mListenButtonText.setVisibility(View.VISIBLE);
        mListenButtonAnim.setVisibility(View.GONE);
    }

    private void stopRecord() {
        String path = LiveManager.getInstance().stopRecord();
        stopRecordTimer();
        if (mRecordFile != null && mRecordFile.equals(path)) {
            startRecordFinishAnim();
        } else if (LiveManager.RECORD_TIME_TOO_SHORT.equals(path)) {
            Toast.makeText(getContext(), R.string.live_speak_too_short, Toast.LENGTH_SHORT).show();
        }
    }

    private void cancelRecord() {
        stopRecordTimer();
        stopRecordingAnim();
        mRecordButtonText.setVisibility(View.INVISIBLE);
        LiveManager.getInstance().stopRecord(true);
    }

    private void startRecordFinishAnim() {
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "startRecordFinishAnim");
        }
        if (LiveManager.getInstance().getLiveStatus() != LiveStatus.LIVING) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.d(TAG, "startRecordFinishAnim but live status is not live, return");
            }
            return;
        }
        int recordWidth = mRecordButtonImage.getMeasuredWidth();
        int middle = mRecordButtonImage.getLeft() + recordWidth / 2;
        int cancelLeft = mCancelButton.getLeft();
        int listenLeft = mListenButton.getLeft();

        AnimatorSet animSet = new AnimatorSet();
        animSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
                mCancelButton.setVisibility(View.VISIBLE);
                mListenButton.setVisibility(View.VISIBLE);
            }

            @Override
            public void onAnimationEnd(Animator animation) {

            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });

        int animDuration = 300;

        PropertyValuesHolder pvhr = PropertyValuesHolder.ofFloat("x", middle, cancelLeft);
        PropertyValuesHolder pvhl = PropertyValuesHolder.ofFloat("x", middle, listenLeft);

        PropertyValuesHolder pvha = PropertyValuesHolder.ofFloat("alpha", 0, 1);

        ObjectAnimator oali = ObjectAnimator.ofPropertyValuesHolder(mListenButton, pvhl);
        oali.setDuration(animDuration);
        ObjectAnimator oala = ObjectAnimator.ofPropertyValuesHolder(mListenButton, pvha);
        oala.setDuration(animDuration);

        ObjectAnimator oari = ObjectAnimator.ofPropertyValuesHolder(mCancelButton, pvhr);
        oari.setDuration(animDuration);
        ObjectAnimator oara = ObjectAnimator.ofPropertyValuesHolder(mCancelButton, pvha);
        oala.setDuration(animDuration);

        animSet.playTogether(oali, oari, oala, oara);
        animSet.setInterpolator(new DecelerateInterpolator());
        animSet.start();

    }

    private void sendMessage() {
        if (LivePresenter.DEBUG_LIVE) {
            Log.d(TAG, "sendMessage");
        }
        //可上传状态有：录音完成FINISHED，试听完成LISTENED，正在试听LISTENING, 上传失败FAILURE
        //其他状态：录音中RECORDING，空闲IDLE，正在上传UPLOADING，上传完成UPLOADED则不能上传
        //以此避免无效上传, 但这个判断在按钮点击的时候已经处理了，这里留个备忘
//        if (LiveManager.getInstance().isListened() || LiveManager.getInstance().isPlaying() ||
//                LiveManager.getInstance().isFinished() || LiveManager.getInstance().isFailure()) {
//        }
        stopListen();
        String uid = UserInfoManager.getInstance().getUserId();
        String nickName = NimManager.getInstance().getNickName();
        int timeLength = LiveManager.getInstance().getRecordDuration();
        String uploadFileName = RecordUploadHelper.generateFileUploadName();
        RecordUploadHelper.UploadParam param = new RecordUploadHelper.UploadParam(mProgramId, uid,
                nickName, timeLength, uploadFileName);
        mPresenter.uploadRecord(getContext(), param, uploadFileName);
        mRecordButtonImage.setProgress(0);
    }

    private void recordAndSetSystemVolume() {
        //音量设为0
        AudioManager am = (AudioManager) getContext().getSystemService(Context.AUDIO_SERVICE);
        mSystemVolume = am.getStreamVolume(AudioManager.STREAM_MUSIC);
        am.setStreamVolume(AudioManager.STREAM_MUSIC, 0, AudioManager.FLAG_PLAY_SOUND);
    }

    private void restoreSystemVolume() {
        //还原系统音量
        AudioManager am = (AudioManager) getContext().getSystemService(Context.AUDIO_SERVICE);
        am.setStreamVolume(AudioManager.STREAM_MUSIC, mSystemVolume, AudioManager.FLAG_PLAY_SOUND);
    }

    private boolean requestAudioFocus() {
        AudioManager am = (AudioManager) getContext().getSystemService(Context.AUDIO_SERVICE);
        return AudioManager.AUDIOFOCUS_REQUEST_GRANTED ==
                am.requestAudioFocus(mAudioFocusListener, AudioManager.STREAM_MUSIC,
                        AudioManager.AUDIOFOCUS_GAIN);
    }

    private boolean abandonAudioFocus() {
        AudioManager am = (AudioManager) getContext().getSystemService(Context.AUDIO_SERVICE);
        return AudioManager.AUDIOFOCUS_REQUEST_GRANTED ==
                am.abandonAudioFocus(mAudioFocusListener);
    }

    private AudioManager.OnAudioFocusChangeListener mAudioFocusListener
            = new AudioManager.OnAudioFocusChangeListener() {
        public void onAudioFocusChange(int focusChange) {
            if (focusChange == AudioManager.AUDIOFOCUS_LOSS_TRANSIENT) {
                Log.d(TAG, "OnAudioFocusChangeListener:AUDIOFOCUS_LOSS_TRANSIENT");
            } else if (focusChange == AudioManager.AUDIOFOCUS_LOSS) {
                Log.d(TAG, "OnAudioFocusChangeListener:AUDIOFOCUS_LOSS");
            } else if (focusChange == AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK) {
                Log.d(TAG, "OnAudioFocusChangeListener:AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK");
            } else if (focusChange == AudioManager.AUDIOFOCUS_GAIN) {
                Log.d(TAG, "OnAudioFocusChangeListener:AUDIOFOCUS_GAIN");
            }
        }
    };

    private void stopRecordTimer() {
        if (mRecordTimer != null) {
            mRecordTimer.cancel();
            mRecordTimer = null;
        }
    }


    private void stopForecastTimer() {
        if (mForecastTimer != null) {
            mForecastTimer.cancel();
            mForecastTimer = null;
        }
    }

    private void startListen() {
        long delta = SystemClock.elapsedRealtime() - mLastListenTime;
        if (delta < DURATION_LONG) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.d(TAG, "startRecord but period to last listening is too short");
            }
            return;
        }
        if (requestAudioFocus()) {
            LiveManager.getInstance().startListen();
        }
    }

    private void stopListen() {
        LiveManager.getInstance().stopListen();
        stopListenTimer();
        mListenButtonText.setText(R.string.live_listen_message);
    }

    private void cancel() {
        LiveManager.getInstance().cancel();
        mCancelButton.setVisibility(View.INVISIBLE);
        mListenButton.setVisibility(View.INVISIBLE);
    }

    private void startRecordingAnim() {
        mRecordSoundImage.setVisibility(View.VISIBLE);
        AnimationSet as = (AnimationSet) AnimationUtils.loadAnimation(getContext(), R.anim.anim_live_record_sound);
        mRecordSoundImage.startAnimation(as);
    }

    private void stopRecordingAnim() {
        mRecordSoundImage.clearAnimation();
        mRecordSoundImage.setVisibility(View.GONE);
    }

    private void updateLastRecordTime() {
        mLastRecordTime = SystemClock.elapsedRealtime();
    }

    private void updateLastListenTime() {
        mLastListenTime = SystemClock.elapsedRealtime();
    }

    private Observer mRecorderStatusObserver = new Observer() {
        @Override
        public void update(Observable o, Object arg) {
            //录音相关状态的变化只有在直播过程中体现
            if (LiveManager.getInstance().getLiveStatus() != LiveStatus.LIVING) {
                if (LivePresenter.DEBUG_LIVE) {
                    Log.d(TAG, "mRecorderStatusObserver update but live status is not live");
                }
                return;
            }
            RecorderStatus status = (RecorderStatus) arg;
            if (LivePresenter.DEBUG_LIVE) {
                Log.d(TAG, "RecorderStatusObserver update: " + status);
            }
            switch (status) {
                case CANCEL:
                    updateLastRecordTime();
                    break;
                case IDLE:
                    mCancelButton.setVisibility(View.INVISIBLE);
                    mListenButton.setVisibility(View.INVISIBLE);
                    mRecordButtonImage.setDrawProgress(false);
                    mRecordButtonImage.setImageResource(R.drawable.live_leave_a_message);
                    mSpeakImage.setVisibility(View.VISIBLE);
                    mLoginPromptText.setVisibility(View.VISIBLE);
                    mRecordButtonText.setVisibility(View.INVISIBLE);
                    mLoginPromptText.setText(R.string.live_click_to_speak);
                    stopRecordingAnim();
                    abandonAudioFocus();
                    break;
                case RECORDING:
                    mRecordButtonText.setVisibility(View.VISIBLE);
                    mSpeakImage.setVisibility(View.GONE);
                    mLoginPromptText.setText(R.string.live_click_to_finish);
                    mRecordButtonImage.setImageResource(R.drawable.live_live_message_recording);
                    startRecordingAnim();
                    requestAudioFocus();
                    break;
                case LISTENING:
                    requestAudioFocus();
                    startListenTimer();
                    break;
                case LISTENED:
                    stopListenTimer();
                    abandonAudioFocus();
                    updateLastListenTime();
                    break;
                case FINISHED:
                    mRecordButtonText.setText(R.string.live_send_message);
                    mLoginPromptText.setVisibility(View.INVISIBLE);
                    mRecordButtonImage.setImageResource(R.drawable.live_send_message_selector);
                    mRecordButtonImage.setVisibility(View.VISIBLE);
                    stopRecordingAnim();
                    abandonAudioFocus();
                    updateLastRecordTime();
                    break;
                case UPLOADING:
                    mCancelButton.setVisibility(View.INVISIBLE);
                    mListenButton.setVisibility(View.INVISIBLE);
                    mRecordButtonText.setText(R.string.live_uploading);
                    mLoginPromptText.setVisibility(View.GONE);
                    mRecordButtonImage.setDrawProgress(true);
                    mRecordButtonImage.setVisibility(View.VISIBLE);
                    break;
                case UPLOADED:
                    mRecordButtonImage.setDrawProgress(false);
                    mRecordButtonImage.setImageResource(R.drawable.live_message_send_success);
                    mRecordButtonImage.setVisibility(View.VISIBLE);
                    mRecordButtonText.setVisibility(View.INVISIBLE);
                    mRecordButtonImage.removeCallbacks(mChangeToIdleRunnable);
                    mRecordButtonImage.postDelayed(mChangeToIdleRunnable, 500);
                    showChatMessageReceived(createMineMessage());
                    break;
                case FAILURE:
                    mRecordButtonText.setText(R.string.live_upload_again);
                    mLoginPromptText.setVisibility(View.GONE);
                    mRecordButtonImage.setDrawProgress(false);
                    mRecordButtonImage.setImageResource(R.drawable.live_send_failure_selector);
                    mRecordButtonImage.setVisibility(View.VISIBLE);
                    mCancelButton.setVisibility(View.VISIBLE);
                    mListenButton.setVisibility(View.VISIBLE);
                    break;
                default:

            }
        }
    };

    private Runnable mChangeToIdleRunnable = new Runnable() {
        @Override
        public void run() {
            LiveManager.getInstance().setRecorderStatus(RecorderStatus.IDLE);
        }
    };

    private ArrayList<MessageBean> createMineMessage() {
        MessageBean mb = new MessageBean();
        String nickName = NimManager.getInstance().getNickName();
        if (nickName == null || nickName.equals("null")) {
            nickName = getResources().getString(R.string.live_nickname_me);
        }
        mb.nickName = nickName;
        ArrayList<MessageBean> list = new ArrayList<MessageBean>(1);
        list.add(mb);
        return list;
    }

    private static class LiveHandler extends Handler {
        private WeakReference<LiveFragment> mReference;

        public LiveHandler(LiveFragment fragment) {
            mReference = new WeakReference(fragment);
        }

        @Override
        public void handleMessage(Message msg) {
            LiveFragment fragment = mReference.get();
            if (fragment == null) {
                Log.d(TAG, "handleMessage but fragment no longer exists ");
                return;
            }
        }
    }

    private TextView createMemberEnterTextView(ChatUserInfo member) {
        int imageWidth = mLiveImage.getWidth();
        int paddingLeft = (int) (imageWidth * 0.025);
        TextView textView = new TextView(getContext());
        textView.setText(String.format(getString(R.string.live_member_enter), member.getNickName()));
        textView.setBackgroundResource(R.drawable.live_screen_bullet_bg);
        textView.setGravity(Gravity.CENTER_VERTICAL);
        textView.setPadding(paddingLeft, 0, 0, 0);
        return textView;
    }

    private TextView createMessageSendTextView(MessageBean message) {
        int imageWidth = mLiveImage.getWidth();
        int paddingLeft = (int) (imageWidth * 0.0203);
        TextView textView = new TextView(getContext());
        textView.setText(String.format(getString(R.string.live_message_received), message.nickName));
        textView.setBackgroundResource(R.drawable.live_message_received_bg);
        textView.setGravity(Gravity.CENTER_VERTICAL);
        textView.setPadding(paddingLeft, 0, 0, 0);
        return textView;
    }
}
