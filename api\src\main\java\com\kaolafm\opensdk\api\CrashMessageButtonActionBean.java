package com.kaolafm.opensdk.api;

/**
 *  配置了按钮肯定会有动作对应，不考虑按钮动作为null的情况；
 *  应急广播泡泡处的按钮是固定的查看详情和取消；
 */
public class CrashMessageButtonActionBean {
    private int actionType; //0：do nothing；1：根据<resId,resType>跳转；2：根据url跳H5页面
    private String resId;
    private int resType;
    private String url;

    public int getActionType() {
        return actionType;
    }

    public void setActionType(int actionType) {
        this.actionType = actionType;
    }

    public String getResId() {
        return resId;
    }

    public void setResId(String resId) {
        this.resId = resId;
    }

    public int getResType() {
        return resType;
    }

    public void setResType(int resType) {
        this.resType = resType;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public String toString(){
        return "CrashMessageButtonActionBean{" +
                "actionType=" + actionType +
                ", resId=" + resId +
                ", resType=" + resType +
                ", url='" + url + '\'' +
                '}';
    }
}