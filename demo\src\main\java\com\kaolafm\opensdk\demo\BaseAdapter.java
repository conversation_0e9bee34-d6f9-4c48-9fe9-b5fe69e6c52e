package com.kaolafm.opensdk.demo;

import android.content.res.Resources.NotFoundException;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import java.util.ArrayList;
import java.util.List;

/**
 * RecyclerView.Adapter 基类，布局只需要实现getLayoutId()返回布局id就可以。
 * setOnItemClickListener() 可以实现item点击事件
 * setProgramList()添加并覆盖数据；
 * addDataList()添加数据
 *
 * <AUTHOR>
 * @date 2017/12/27
 */

public abstract class BaseAdapter<T> extends RecyclerView.Adapter<BaseHolder<T>> {

    protected BaseHolder<T> mViewHolder;

    protected List<T> mDataList;

    private OnItemClickListener<T> mItemClickListener;

    public BaseAdapter(List<T> dataList) {
        if (dataList == null) {
            mDataList = new ArrayList<>();
        } else {
            mDataList = dataList;
        }
    }

    public BaseAdapter() {
        mDataList = new ArrayList<>();
    }

    @Override
    public BaseHolder<T> onCreateViewHolder(final ViewGroup parent, final int viewType) {
        View view;
        //如果通过布局id获取view失败的话就会尝试通过getItemView(int)方法获取item的view。
        try {
            view = LayoutInflater.from(parent.getContext()).inflate(getLayoutId(viewType), parent, false);
        }catch (NotFoundException e){
            view = getItemView(viewType);
        }
        mViewHolder = getViewHolder(view, viewType);
        mViewHolder.setOnViewHolderClickListener((itemView, position) -> {
            if (mItemClickListener != null) {
                T t = null;
                if (position >= 0 && position < mDataList.size()) {
                    t = mDataList.get(position);
                }
                mItemClickListener.onItemClick(itemView, viewType, t, position);
            }
        });
        return mViewHolder;
    }

    /**
     * 获取item的view
     * @param viewType
     * @return
     */
    protected View getItemView(int viewType) {
        return null;
    }

    /**
     * 获取ViewHolder
     * @param view
     * @param viewType
     * @return
     */
    protected abstract BaseHolder<T> getViewHolder(View view, int viewType);

    /**
     * 获取布局文件id
     * @param viewType
     * @return
     */
    protected abstract int getLayoutId(int viewType);

    @Override
    public int getItemCount() {
        return mDataList == null ? 0 : mDataList.size();
    }

    @Override
    public void onBindViewHolder(final BaseHolder<T> holder, final int position) {
        holder.setupData(mDataList.get(position), position);
    }

    /**
     * 重新加载数据，所有数据会被新数据替换
     */
    public void setDataList(List<T> dataList) {
        mDataList = dataList;
        notifyDataSetChanged();
    }

    /**
     * 加载更多，在尾部加载数据
     * @param dataList
     */
    public void addDataList(List<T> dataList) {
        addDataList(mDataList.size(), dataList);
    }

    /**
     * 从某个位置添加数据
     */
    public void addDataList(int position, List<T> dataList) {
        mDataList.addAll(position, dataList);
        notifyItemRangeChanged(position, dataList.size());
    }

    /**
     * 尾部加载一条数据
     * @param data
     */
    public void addData(T data) {
        mDataList.add(data);
        notifyItemRangeInserted(mDataList.size(), 1);
    }

    /**
     * 在首部加载一条数据
     * @param data
     */
    public void addDataAtFirst(T data) {
        mDataList.add(0, data);
        notifyItemRangeInserted(0, 1);
    }

    /**
     * 获取某个位置item的数据
     * @param position
     * @return
     */
    public T getItemData(int position){
        if (position >=0 && position < getItemCount()){
            return mDataList.get(position);
        }
        return null;
    }
    public List<T> getDataList(){
        return mDataList;
    }

    /**
     * 移除某个位置的一条数据
     * @param position
     */
    public void removeData(int position) {
        mDataList.remove(position);
        notifyItemRemoved(position);
    }

    /**
     * 清空数据
     */
    public void clear() {
        mDataList = new ArrayList<>();
        notifyDataSetChanged();
    }

    /**
     * 遍历所有{@link BaseHolder},释放他们需要释放的资源
     *
     * @param recyclerView
     */
    public static void releaseAllHolder(RecyclerView recyclerView) {
        if (recyclerView == null) {
            return;
        }
        for (int i = recyclerView.getChildCount() - 1; i >= 0; i--) {
            final View view = recyclerView.getChildAt(i);
            RecyclerView.ViewHolder viewHolder = recyclerView.getChildViewHolder(view);
            if (viewHolder != null && viewHolder instanceof BaseHolder) {
                ((BaseHolder) viewHolder).onRelease();
            }
        }
    }


    /**
     * 刷新所有
     * public final void notifyDataSetChanged();
     *
     * position数据发生了改变，那调用这个方法，就会回调对应position的onBindViewHolder()方法了
     * public final void notifyItemChanged(int position);
     *
     * 刷新从positionStart开始itemCount数量的item了（这里的刷新指回调onBindViewHolder()方法）
     * public final void notifyItemRangeChanged(int positionStart, int itemCount);
     *
     * 在第position位置被插入了一条数据的时候可以使用这个方法刷新，注意这个方法调用后会有插入的动画，这个动画可以使用默认的，也可以自己定义
     * public final void notifyItemInserted(int position);
     *
     * 从fromPosition移动到toPosition为止的时候可以使用这个方法刷新
     * public final void notifyItemMoved(int fromPosition, int toPosition);
     *
     * 批量添加
     * public final void notifyItemRangeInserted(int positionStart, int itemCount);
     *
     * 第position个被删除的时候刷新，同样会有动画
     * public final void notifyItemRemoved(int position);
     *
     * 批量删除
     * public final void notifyItemRangeRemoved(int positionStart, int itemCount);
     */

    public void setOnItemClickListener(OnItemClickListener<T> listener) {
        mItemClickListener = listener;
    }

    public interface OnItemClickListener<T> {

        /**
         * 条目点击事件
         *
         * @param view
         * @param viewType
         * @param t
         * @param position
         */
        void onItemClick(View view, int viewType, T t, int position);
    }
}
