/*
 * Copyright (C) 2013-2014 <PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package tv.danmaku.ijk.media.player;

import android.content.Context;
import android.view.Surface;
import android.view.SurfaceHolder;

import java.io.IOException;

public interface IMediaPlayer {

    void setDisplay(SurfaceHolder sh);

    void setDataSource(String path) throws IOException,
            IllegalArgumentException, SecurityException, IllegalStateException;

    String getDataSource();

    void prepare() throws IllegalStateException;

    void prepareAsync() throws IllegalStateException;

    void start() throws IllegalStateException;

    void stop() throws IllegalStateException;

    void pause() throws IllegalStateException;

    void setScreenOnWhilePlaying(boolean screenOn);

    int getVideoWidth();

    int getVideoHeight();

    boolean isPlaying();

    void seekTo(long msec) throws IllegalStateException;

    long getCurrentPosition();

    long getDuration();

    void release();

    void reset();

    void setVolume(float leftVolume, float rightVolume);

    MediaInfo getMediaInfo();

    void setLogEnabled(boolean enable);

    boolean isPlayable();

    void setOnPreparedListener(OnPreparedListener listener);

    void setOnCompletionListener(OnCompletionListener listener);

    void setOnBufferingUpdateListener(
            OnBufferingUpdateListener listener);

    void setOnSeekCompleteListener(
            OnSeekCompleteListener listener);

    void setOnVideoSizeChangedListener(
            OnVideoSizeChangedListener listener);

    void setOnErrorListener(OnErrorListener listener);

    void setOnInfoListener(OnInfoListener listener);

    /*--------------------
     * Listeners
     */
    interface OnPreparedListener {
        public void onPrepared(IMediaPlayer mp);
    }

    interface OnCompletionListener {
        public void onCompletion(IMediaPlayer mp);
    }

    interface OnBufferingUpdateListener {
        void onBufferingUpdate(IMediaPlayer mp, int position, int duration);
    }

    interface OnSeekCompleteListener {
        public void onSeekComplete(IMediaPlayer mp);
    }

    interface OnVideoSizeChangedListener {
        public void onVideoSizeChanged(IMediaPlayer mp, int width, int height,
                                       int sar_num, int sar_den);
    }

    interface OnErrorListener {
        public boolean onError(IMediaPlayer mp, int what, int extra);
    }

    interface OnInfoListener {
        public boolean onInfo(IMediaPlayer mp, int what, int extra);
    }

    /*--------------------
     * Optional
     */
    void setAudioStreamType(int streamtype);

    void setKeepInBackground(boolean keepInBackground);

    int getVideoSarNum();

    int getVideoSarDen();

    @Deprecated
    void setWakeMode(Context context, int mode);

    void setSurface(Surface surface);
}
