package com.kaolafm.base.utils;

import android.Manifest.permission;
import android.content.Context;
import android.os.Environment;
import androidx.annotation.RequiresPermission;
import android.text.TextUtils;

import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.RandomAccessFile;
import java.io.UnsupportedEncodingException;
import java.nio.channels.FileChannel;


/**
 * <AUTHOR>
 * @date 2018/1/9
 */

public class FileUtil {

    private FileUtil() {
    }

    public static String readAssetFile(Context context, String file, String code) {
        int len = 0;
        byte[] buf = null;
        String result = "";
        try {
            InputStream in = context.getAssets().open(file);
            len = in.available();
            buf = new byte[len];
            in.read(buf, 0, len);

            result = new String(buf, code);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    @RequiresPermission(permission.READ_EXTERNAL_STORAGE)
    public static String readFileFromSDCard(File file) {
        try {
            FileInputStream fis = new FileInputStream(file);
            InputStreamReader isr = new InputStreamReader(fis, "UTF-8");
            char[] input = new char[fis.available()];
            isr.read(input);
            isr.close();
            fis.close();
            return String.valueOf(input);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return "";
    }

    @RequiresPermission(permission.READ_EXTERNAL_STORAGE)
    public static String readFileFromSDCard(String filePath) {
        return readFileFromSDCard(new File(filePath));
    }

    @RequiresPermission(permission.WRITE_EXTERNAL_STORAGE)
    public static void writeFileToSDCard(String filePath, String text) {
        writeFileToSDCard(filePath, text, true);
    }

    @RequiresPermission(permission.WRITE_EXTERNAL_STORAGE)
    public static void writeFileToSDCard(String filePath, String text, boolean append) {
        try {
            File file = new File(filePath);
            File parentFile = file.getParentFile();
            if (!parentFile.exists()) {
                parentFile.mkdirs();
            }
            if (!file.exists() || file.isDirectory()) {
                file.createNewFile();
            }
            FileOutputStream fos = new FileOutputStream(file, append);
            OutputStreamWriter osw = new OutputStreamWriter(fos, "UTF-8");
            osw.write(text + "\r\n");
            osw.flush();
            fos.flush();
            osw.close();
            fos.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static boolean deleteFile(String path) {
        if (!TextUtils.isEmpty(path)) {
            return deleteFile(new File(path));
        }
        return false;
    }

    public static boolean deleteFile(File path) {
        boolean result = true;
        if (path.exists()) {
            if (path.isDirectory()) {
                File[] files = path.listFiles();
                if (files == null) {
                    return false;
                }
                for (int i = 0, count = files.length; i < count; i++) {
                    result &= deleteFile(files[i]);
                }
                result &= path.delete(); // Delete empty directory.
            } else {
                result &= path.delete();
            }
            return result;
        } else {
            return false;
        }
    }

    private static final String ENCRYPTED_FLAG_SUFFIX = "_v2";

    @RequiresPermission(permission.WRITE_EXTERNAL_STORAGE)
    public static void writeEncryptedFileToSDCard(String filePath, String text) {
        String encryptedFilePath = getEncryptedFilePath(filePath);
        writeFileToSDCard(encryptedFilePath, SeCoreUtils.encrypt(text), false);
    }

    @RequiresPermission(allOf = {permission.READ_EXTERNAL_STORAGE, permission.WRITE_EXTERNAL_STORAGE})
    public static String readDecryptedFileFromSDCard(String filePath) {
        if (TextUtils.isEmpty(filePath)) {
            return null;
        }
        String encryptedFilePath = getEncryptedFilePath(filePath);
        File file = new File(encryptedFilePath);
        if (file.exists()) {
            String encryptedFile = readFileFromSDCard(file).trim();
            return SeCoreUtils.decrypt(encryptedFile);
        }else {
            String content = readFileFromSDCard(filePath).trim();
            if (!TextUtils.isEmpty(content)) {
                writeEncryptedFileToSDCard(filePath, content);
                new File(filePath).delete();
            }
            return content;
        }
    }

    /**
     * 判断指定路径的文件及其加密文件是否存在。
     * @param filePath
     * @return
     */
    public static boolean isEncryptedFileExiste(String filePath) {
        String path = getEncryptedFilePath(filePath);
        return new File(filePath).exists() || new File(path).exists();
    }

    private static String getEncryptedFilePath(String filePath) {
        int index = filePath.lastIndexOf(".");
        String path = filePath;
        if (index >= 0) {
            String name = filePath.substring(0, index);
            String suffixName = filePath.substring(index);
            path = name + ENCRYPTED_FLAG_SUFFIX + suffixName;
        }
        return path;
    }

    /**
     * 关闭 IO
     *
     * @param closeables closeables
     */
    public static void closeIO(Closeable... closeables) {
        if (closeables == null) return;
        for (Closeable closeable : closeables) {
            if (closeable != null) {
                try {
                    closeable.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 应用程序缓存原理：
     * 1.当SD卡存在或者SD卡不可被移除的时候，就调用getExternalCacheDir()方法来获取缓存路径，否则就调用getCacheDir()方法来获取缓存路径<br>
     * 2.前者是/sdcard/Android/data/<application package>/cache 这个路径<br>
     * 3.后者获取到的是 /data/data/<application package>/cache 这个路径<br>
     *
     * @param uniqueName 缓存目录
     */
    public static File getDiskCacheDir(Context context, String uniqueName) {
        String cachePath;
        if (isSDCardExist() && context.getExternalCacheDir() != null) {
            cachePath = context.getExternalCacheDir().getPath();
        } else {
            cachePath = context.getCacheDir().getPath();
        }
        return new File(cachePath + File.separator + uniqueName);
    }

    private static boolean isSDCardExist() {
        return Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState())
                || !Environment.isExternalStorageRemovable();
    }

    /**
     * 获取磁盘的文件目录
     *
     * @return SD卡不存在: /data/data/com.xxx.xxx/files;<br>
     * 存在: /storage/emulated/0/Android/data/com.xxx.xxx/files;
     */
    private static String getDiskFilesDir(Context context) {
        return isSDCardExist() && context.getExternalFilesDir(null) != null ? context.getExternalFilesDir(null).getPath() : context.getFilesDir().getPath();
    }

    /**
     * 获取磁盘的自定义文件目录
     *
     * @return SD卡不存在: /data/data/com.xxx.xxx/files/fileDir;<br>
     * 存在: /storage/emulated/0/Android/data/com.xxx.xxx/files/fileDir;
     */
    public static String getDiskFilesDir(Context context, String fileDir) {
        return getDiskFilesDir(context) + File.separator + fileDir;
    }

    public static void recreate(File file) {
        recreate(file, 0);
    }

    public static void recreate(File file, long length) {
        if (file == null) {
            return;
        }
        file.delete();
        try {
            boolean created = file.createNewFile();
            if (created) {
                setLength(file, length);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void setLength(File file, long length) {
        try {
            RandomAccessFile accessFile = new RandomAccessFile(file, "rws");
            accessFile.setLength(length);
            closeIO(accessFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static FileChannel getChannel(File file) {
        if (file != null) {
            try {
                return new RandomAccessFile(file, "rws").getChannel();
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
