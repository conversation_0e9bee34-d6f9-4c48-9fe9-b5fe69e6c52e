package com.kaolafm.opensdk.demo.activity;

import android.os.Bundle;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.activity.ActivityRequest;
import com.kaolafm.opensdk.api.activity.model.Activity;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;


import java.util.List;

import butterknife.BindView;

/**
 * 活动专区(Activity)显示页面
 */
public class ActivitiesActivity extends BaseActivity {
    @BindView(R.id.rv_sub)
    RecyclerView rvSub;

    StaggeredGridLayoutManager mLayoutManager;
    ActivityAdapter mActivityAdapter;

    @Override
    public int getLayoutId() {
        return R.layout.activity_activities;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        setTitle("活动专区页面");
        mLayoutManager = new StaggeredGridLayoutManager(1, LinearLayoutManager.VERTICAL);
        rvSub.setLayoutManager(mLayoutManager);
        ((DefaultItemAnimator) rvSub.getItemAnimator()).setSupportsChangeAnimations(false);
        mActivityAdapter = new ActivityAdapter();
        rvSub.setAdapter(mActivityAdapter);
//        rvSub.addItemDecoration(new RvItemDecoration());

        new ActivityRequest().getInfoList(new HttpCallback<BasePageResult<List<Activity>>>() {
            @Override
            public void onSuccess(BasePageResult<List<Activity>> listBasePageResult) {
                mActivityAdapter.setDataList(listBasePageResult.getDataList());
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取活动列表失败", exception);
            }
        });
    }

    @Override
    public void initData() {

    }
}
